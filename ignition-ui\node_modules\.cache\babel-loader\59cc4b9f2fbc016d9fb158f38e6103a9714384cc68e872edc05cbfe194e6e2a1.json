{"ast": null, "code": "import axios from 'axios';\nimport { getHeaders } from \"helpers/functions\";\nimport { APIURL } from \"helpers/constants\";\nexport const fetchPlanInfo = async param => {\n  try {\n    const headers = getHeaders();\n    const response = await axios.get(`${APIURL}/api/plans/${param}`, {\n      headers\n    });\n    return response.data;\n  } catch (error) {\n    throw error;\n  }\n};\nexport const checkUserExistence = async email => {\n  try {\n    const formData = new FormData();\n    formData.append('email', email);\n    const response = await axios.post(`${APIURL}/api/check-user`, formData, {\n      headers: getHeaders()\n    });\n    return response.data;\n  } catch (error) {\n    throw error;\n  }\n};\nexport const sendInvitation = async (plan, email) => {\n  try {\n    const formData = new FormData();\n    formData.append('email', email);\n    const response = await axios.post(`${APIURL}/api/plans/${plan.slug}/invite`, formData, {\n      headers: getHeaders()\n    });\n    return response.data;\n  } catch (error) {\n    throw error;\n  }\n};\nexport const getInvitedUsers = async planSlug => {\n  try {\n    console.log('Calling API to get invited users for plan:', planSlug);\n    const response = await axios.get(`${APIURL}/api/plans/${planSlug}/invited-users`, {\n      headers: getHeaders()\n    });\n    console.log('Full API Response:', response);\n\n    // Kiểm tra cấu trúc response\n    if (response.data && response.data.data) {\n      return response.data;\n    } else if (Array.isArray(response.data)) {\n      return {\n        data: response.data\n      };\n    } else {\n      console.warn('Unexpected API response structure:', response.data);\n      return {\n        data: []\n      };\n    }\n  } catch (error) {\n    console.error('Error fetching invited users:', error);\n    throw new Error('Error fetching invited users: ' + error.message);\n  }\n};\nexport const deletePlan = async planSlug => {\n  try {\n    const response = await axios.delete(`${APIURL}/api/plan/delete/${planSlug}`, {\n      headers: getHeaders()\n    });\n    return response.data;\n  } catch (error) {\n    throw new Error('Error deleting plan of user: ' + error.message);\n  }\n};\nexport const optOutPlan = async planSlug => {\n  try {\n    const response = await axios.delete(`${APIURL}/api/plan/opt-out/${planSlug}`, {\n      headers: getHeaders()\n    });\n    return response.data;\n  } catch (error) {\n    throw new Error('Error opting out of plan: ' + error.message);\n  }\n};\nexport const updateMilestone = async milestone => {\n  console.log('API call - updateMilestone:', milestone);\n  try {\n    const formData = new FormData();\n    formData.append('name', milestone.name);\n    const headers = getHeaders();\n    console.log('Headers:', headers);\n    const url = `${APIURL}/api/milestones/${milestone.id}/update`;\n    console.log('API URL:', url);\n    const response = await axios.put(url, formData, {\n      headers: headers\n    });\n    console.log('API Response:', response);\n    return response.data;\n  } catch (error) {\n    var _error$response, _error$response2, _error$response2$data;\n    console.error('API Error in updateMilestone:', error);\n    console.error('Error details:', ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.data) || error.message);\n    throw new Error('Error updating milestone: ' + (((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || error.message));\n  }\n};\nexport const updateTask = async task => {\n  try {\n    console.log('Updating task:', task);\n    const formData = new FormData();\n\n    // Thêm các trường cần thiết\n    formData.append('name', task.name);\n\n    // Thêm start_date và end_date nếu có\n    if (task.start_date) {\n      formData.append('start_date', task.start_date);\n    }\n    if (task.end_date) {\n      formData.append('end_date', task.end_date);\n    }\n\n    // Thêm các trường khác nếu cần\n    if (task.status) {\n      formData.append('status', task.status);\n    }\n\n    // Đảm bảo progress luôn là số nguyên\n    const progress = task.progress !== null && task.progress !== undefined ? task.progress : 0;\n    formData.append('progress', progress);\n    const response = await axios.put(`${APIURL}/api/tasks/update/${task.slug}`, formData, {\n      headers: getHeaders()\n    });\n    console.log('Update task response:', response);\n    return response.data;\n  } catch (error) {\n    console.error('Error updating task:', error);\n    throw error;\n  }\n};\nexport const updateSubtask = async subtask => {\n  console.log('API call - updateSubtask:', subtask);\n  try {\n    const formData = new FormData();\n    formData.append('name', subtask.name);\n    formData.append('status', subtask.status);\n    if (subtask.progress !== undefined) {\n      formData.append('progress', subtask.progress);\n    }\n    if (subtask.start_date) {\n      formData.append('start_date', subtask.start_date);\n    }\n    if (subtask.end_date) {\n      formData.append('end_date', subtask.end_date);\n    }\n    const headers = getHeaders();\n    console.log('Headers:', headers);\n    const url = `${APIURL}/api/subtasks/update/${subtask.slug}`;\n    console.log('API URL:', url);\n    console.log('FormData being sent:', {\n      name: subtask.name,\n      status: subtask.status,\n      progress: subtask.progress,\n      start_date: subtask.start_date,\n      end_date: subtask.end_date\n    });\n    const response = await axios.put(url, formData, {\n      headers: headers\n    });\n    console.log('API Response:', response);\n    return response.data;\n  } catch (error) {\n    var _error$response3, _error$response4, _error$response4$data;\n    console.error('API Error in updateSubtask:', error);\n    console.error('Error details:', ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.data) || error.message);\n    throw new Error('Error updating subtask: ' + (((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || error.message));\n  }\n};\nexport const addTask = async task => {\n  console.log('API call - addTask:', task);\n  try {\n    const formData = new FormData();\n    formData.append('name', task.name);\n    formData.append('status', task.status || 1);\n    formData.append('progress', task.progress || 0);\n    if (task.description) {\n      formData.append('description', task.description);\n    }\n    if (task.start_date) {\n      formData.append('start_date', task.start_date);\n    }\n    if (task.end_date) {\n      formData.append('end_date', task.end_date);\n    }\n    const headers = getHeaders();\n    console.log('Headers:', headers);\n    const url = `${APIURL}/api/milestones/${task.milestone}/tasks/add`;\n    console.log('API URL:', url);\n    console.log('FormData being sent:', {\n      name: task.name,\n      status: task.status || 1,\n      progress: task.progress || 0,\n      description: task.description,\n      start_date: task.start_date,\n      end_date: task.end_date\n    });\n    const response = await axios.post(url, formData, {\n      headers: headers\n    });\n    console.log('API Response:', response);\n    return response.data;\n  } catch (error) {\n    var _error$response5, _error$response6, _error$response6$data;\n    console.error('API Error in addTask:', error);\n    console.error('Error details:', ((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : _error$response5.data) || error.message);\n    throw new Error('Error adding task: ' + (((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.message) || error.message));\n  }\n};\nexport const addSubtask = async subtask => {\n  console.log('API call - addSubtask:', subtask);\n  try {\n    const formData = new FormData();\n    formData.append('name', subtask.name);\n    formData.append('status', subtask.status || 1);\n    formData.append('progress', subtask.progress || 0);\n    if (subtask.start_date) {\n      formData.append('start_date', subtask.start_date);\n    }\n    if (subtask.end_date) {\n      formData.append('end_date', subtask.end_date);\n    }\n    const headers = getHeaders();\n    console.log('Headers:', headers);\n    const url = `${APIURL}/api/tasks/${subtask.task}/add-subtask`;\n    console.log('API URL:', url);\n    console.log('FormData being sent:', {\n      name: subtask.name,\n      status: subtask.status || 1,\n      progress: subtask.progress || 0,\n      start_date: subtask.start_date,\n      end_date: subtask.end_date\n    });\n    const response = await axios.post(url, formData, {\n      headers: headers\n    });\n    console.log('API Response:', response);\n    return response.data;\n  } catch (error) {\n    var _error$response7, _error$response8, _error$response8$data;\n    console.error('API Error in addSubtask:', error);\n    console.error('Error details:', ((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : _error$response7.data) || error.message);\n    throw new Error('Error adding subtask: ' + (((_error$response8 = error.response) === null || _error$response8 === void 0 ? void 0 : (_error$response8$data = _error$response8.data) === null || _error$response8$data === void 0 ? void 0 : _error$response8$data.message) || error.message));\n  }\n};\nexport const deleteTask = async taskSlug => {\n  console.log('API call - deleteTask:', taskSlug);\n  try {\n    const headers = getHeaders();\n    console.log('Headers:', headers);\n    const url = `${APIURL}/api/tasks/${taskSlug}/delete`;\n    console.log('API URL:', url);\n    const response = await axios.delete(url, {\n      headers: headers\n    });\n    console.log('API Response:', response);\n    return response.data;\n  } catch (error) {\n    var _error$response9, _error$response10, _error$response10$dat;\n    console.error('API Error in deleteTask:', error);\n    console.error('Error details:', ((_error$response9 = error.response) === null || _error$response9 === void 0 ? void 0 : _error$response9.data) || error.message);\n    throw new Error('Error deleting task: ' + (((_error$response10 = error.response) === null || _error$response10 === void 0 ? void 0 : (_error$response10$dat = _error$response10.data) === null || _error$response10$dat === void 0 ? void 0 : _error$response10$dat.message) || error.message));\n  }\n};\nexport const deleteSubtask = async subtaskSlug => {\n  console.log('API call - deleteSubtask:', subtaskSlug);\n  try {\n    const headers = getHeaders();\n    console.log('Headers:', headers);\n    const url = `${APIURL}/api/subtasks/${subtaskSlug}/delete`;\n    console.log('API URL:', url);\n    const response = await axios.delete(url, {\n      headers: headers\n    });\n    console.log('API Response:', response);\n    return response.data;\n  } catch (error) {\n    var _error$response11, _error$response12, _error$response12$dat;\n    console.error('API Error in deleteSubtask:', error);\n    console.error('Error details:', ((_error$response11 = error.response) === null || _error$response11 === void 0 ? void 0 : _error$response11.data) || error.message);\n    throw new Error('Error deleting subtask: ' + (((_error$response12 = error.response) === null || _error$response12 === void 0 ? void 0 : (_error$response12$dat = _error$response12.data) === null || _error$response12$dat === void 0 ? void 0 : _error$response12$dat.message) || error.message));\n  }\n};\n\n// Comment APIs\nexport const getComments = async taskId => {\n  try {\n    const response = await axios.get(`${APIURL}/api/comments/target/${taskId}/type/1`, {\n      headers: getHeaders()\n    });\n    console.log('Get comments response:', response);\n    return response;\n  } catch (error) {\n    console.error('Error fetching comments:', error);\n    throw error;\n  }\n};\nexport const addComment = async (taskId, content) => {\n  try {\n    const formData = new FormData();\n    formData.append('content', content);\n    formData.append('target_id', taskId);\n    formData.append('type', 1);\n    const response = await axios.post(`${APIURL}/api/comments/add`, formData, {\n      headers: getHeaders()\n    });\n    console.log('Add comment response:', response);\n    return response;\n  } catch (error) {\n    console.error('Error adding comment:', error);\n    throw error;\n  }\n};\nexport const updateComment = async (commentId, content) => {\n  try {\n    const formData = new FormData();\n    formData.append('content', content);\n    formData.append('type', 1);\n    const response = await axios.put(`${APIURL}/api/comments/edit/${commentId}`, formData, {\n      headers: getHeaders()\n    });\n    console.log('Update comment response:', response);\n    return response;\n  } catch (error) {\n    console.error('Error updating comment:', error);\n    throw error;\n  }\n};\nexport const deleteComment = async commentId => {\n  try {\n    const response = await axios.delete(`${APIURL}/api/comments/delete/${commentId}`, {\n      headers: getHeaders()\n    });\n    console.log('Delete comment response:', response);\n    return response;\n  } catch (error) {\n    console.error('Error deleting comment:', error);\n    throw error;\n  }\n};\nexport const assignMembersToTask = async (taskSlug, memberIds) => {\n  try {\n    const response = await axios.put(`${APIURL}/api/tasks/${taskSlug}/assign`, {\n      assignees: memberIds\n    }, {\n      headers: getHeaders()\n    });\n    console.log('Assign members response:', response);\n    return response.data;\n  } catch (error) {\n    var _error$response13, _error$response13$dat;\n    console.error('Error assigning members to task:', error);\n    throw new Error('Error assigning members to task: ' + (((_error$response13 = error.response) === null || _error$response13 === void 0 ? void 0 : (_error$response13$dat = _error$response13.data) === null || _error$response13$dat === void 0 ? void 0 : _error$response13$dat.message) || error.message));\n  }\n};", "map": {"version": 3, "names": ["axios", "getHeaders", "APIURL", "fetchPlanInfo", "param", "headers", "response", "get", "data", "error", "checkUserExistence", "email", "formData", "FormData", "append", "post", "sendInvitation", "plan", "slug", "getInvitedUsers", "planSlug", "console", "log", "Array", "isArray", "warn", "Error", "message", "deletePlan", "delete", "optOutPlan", "updateMilestone", "milestone", "name", "url", "id", "put", "_error$response", "_error$response2", "_error$response2$data", "updateTask", "task", "start_date", "end_date", "status", "progress", "undefined", "updateSubtask", "subtask", "_error$response3", "_error$response4", "_error$response4$data", "addTask", "description", "_error$response5", "_error$response6", "_error$response6$data", "addSubtask", "_error$response7", "_error$response8", "_error$response8$data", "deleteTask", "taskSlug", "_error$response9", "_error$response10", "_error$response10$dat", "deleteSubtask", "subtaskSlug", "_error$response11", "_error$response12", "_error$response12$dat", "getComments", "taskId", "addComment", "content", "updateComment", "commentId", "deleteComment", "assignMembersToTask", "memberIds", "assignees", "_error$response13", "_error$response13$dat"], "sources": ["C:/ignition/ignition-ui/src/views/plan/services.js"], "sourcesContent": ["import axios from 'axios';\r\nimport { getHeaders } from \"helpers/functions\";\r\nimport { APIURL } from \"helpers/constants\";\r\n\r\nexport const fetchPlanInfo = async (param) => {\r\n  try {\r\n    const headers = getHeaders();\r\n    const response = await axios.get(`${APIURL}/api/plans/${param}`, { headers });\r\n    return response.data;\r\n  } catch (error) {\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const checkUserExistence = async (email) => {\r\n  try {\r\n    const formData = new FormData();\r\n    formData.append('email', email);\r\n    const response = await axios.post(`${APIURL}/api/check-user`, formData, {\r\n      headers: getHeaders(),\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const sendInvitation = async (plan, email) => {\r\n  try {\r\n    const formData = new FormData();\r\n    formData.append('email', email);\r\n    const response = await axios.post(`${APIURL}/api/plans/${plan.slug}/invite`, formData, {\r\n      headers: getHeaders(),\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const getInvitedUsers = async (planSlug) => {\r\n  try {\r\n    console.log('Calling API to get invited users for plan:', planSlug);\r\n    const response = await axios.get(`${APIURL}/api/plans/${planSlug}/invited-users`, {\r\n      headers: getHeaders(),\r\n    });\r\n    console.log('Full API Response:', response);\r\n    \r\n    // Kiểm tra cấu trúc response\r\n    if (response.data && response.data.data) {\r\n      return response.data;\r\n    } else if (Array.isArray(response.data)) {\r\n      return { data: response.data };\r\n    } else {\r\n      console.warn('Unexpected API response structure:', response.data);\r\n      return { data: [] };\r\n    }\r\n  } catch (error) {\r\n    console.error('Error fetching invited users:', error);\r\n    throw new Error('Error fetching invited users: ' + error.message);\r\n  }\r\n};\r\n\r\nexport const deletePlan = async (planSlug) => {\r\n  try {\r\n    const response = await axios.delete(`${APIURL}/api/plan/delete/${planSlug}`, {\r\n      headers: getHeaders(),\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    throw new Error('Error deleting plan of user: ' + error.message);\r\n  }\r\n}\r\n\r\nexport const optOutPlan = async (planSlug) => {\r\n  try {\r\n    const response = await axios.delete(`${APIURL}/api/plan/opt-out/${planSlug}`, {\r\n      headers: getHeaders(),\r\n    });\r\n    return response.data;\r\n  } catch (error) {\r\n    throw new Error('Error opting out of plan: ' + error.message);\r\n  }\r\n};\r\n\r\nexport const updateMilestone = async (milestone) => {\r\n  console.log('API call - updateMilestone:', milestone);\r\n  try {\r\n    const formData = new FormData();\r\n    formData.append('name', milestone.name);\r\n    \r\n    const headers = getHeaders();\r\n    console.log('Headers:', headers);\r\n    const url = `${APIURL}/api/milestones/${milestone.id}/update`;\r\n    console.log('API URL:', url);\r\n    \r\n    const response = await axios.put(url, formData, {\r\n      headers: headers,\r\n    });\r\n    console.log('API Response:', response);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('API Error in updateMilestone:', error);\r\n    console.error('Error details:', error.response?.data || error.message);\r\n    throw new Error('Error updating milestone: ' + (error.response?.data?.message || error.message));\r\n  }\r\n};\r\n\r\nexport const updateTask = async (task) => {\r\n  try {\r\n    console.log('Updating task:', task);\r\n    const formData = new FormData();\r\n    \r\n    // Thêm các trường cần thiết\r\n    formData.append('name', task.name);\r\n    \r\n    // Thêm start_date và end_date nếu có\r\n    if (task.start_date) {\r\n      formData.append('start_date', task.start_date);\r\n    }\r\n    \r\n    if (task.end_date) {\r\n      formData.append('end_date', task.end_date);\r\n    }\r\n    \r\n    // Thêm các trường khác nếu cần\r\n    if (task.status) {\r\n      formData.append('status', task.status);\r\n    }\r\n    \r\n    // Đảm bảo progress luôn là số nguyên\r\n    const progress = task.progress !== null && task.progress !== undefined ? task.progress : 0;\r\n    formData.append('progress', progress);\r\n    \r\n    const response = await axios.put(\r\n      `${APIURL}/api/tasks/update/${task.slug}`,\r\n      formData,\r\n      { headers: getHeaders() }\r\n    );\r\n    \r\n    console.log('Update task response:', response);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error updating task:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const updateSubtask = async (subtask) => {\r\n  console.log('API call - updateSubtask:', subtask);\r\n  try {\r\n    const formData = new FormData();\r\n    formData.append('name', subtask.name);\r\n    \r\n    formData.append('status', subtask.status);\r\n    if (subtask.progress !== undefined) {\r\n      formData.append('progress', subtask.progress);\r\n    }\r\n    \r\n    if (subtask.start_date) {\r\n      formData.append('start_date', subtask.start_date);\r\n    }\r\n    if (subtask.end_date) {\r\n      formData.append('end_date', subtask.end_date);\r\n    }\r\n    \r\n    const headers = getHeaders();\r\n    console.log('Headers:', headers);\r\n    const url = `${APIURL}/api/subtasks/update/${subtask.slug}`;\r\n    console.log('API URL:', url);\r\n    console.log('FormData being sent:', {\r\n      name: subtask.name,\r\n      status: subtask.status,\r\n      progress: subtask.progress,\r\n      start_date: subtask.start_date,\r\n      end_date: subtask.end_date\r\n    });\r\n    \r\n    const response = await axios.put(url, formData, {\r\n      headers: headers,\r\n    });\r\n    console.log('API Response:', response);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('API Error in updateSubtask:', error);\r\n    console.error('Error details:', error.response?.data || error.message);\r\n    throw new Error('Error updating subtask: ' + (error.response?.data?.message || error.message));\r\n  }\r\n};\r\n\r\nexport const addTask = async (task) => {\r\n  console.log('API call - addTask:', task);\r\n  try {\r\n    const formData = new FormData();\r\n    formData.append('name', task.name);\r\n    \r\n    formData.append('status', task.status || 1);\r\n    formData.append('progress', task.progress || 0);\r\n    \r\n    if (task.description) {\r\n      formData.append('description', task.description);\r\n    }\r\n    if (task.start_date) {\r\n      formData.append('start_date', task.start_date);\r\n    }\r\n    if (task.end_date) {\r\n      formData.append('end_date', task.end_date);\r\n    }\r\n    \r\n    const headers = getHeaders();\r\n    console.log('Headers:', headers);\r\n    const url = `${APIURL}/api/milestones/${task.milestone}/tasks/add`;\r\n    console.log('API URL:', url);\r\n    console.log('FormData being sent:', {\r\n      name: task.name,\r\n      status: task.status || 1,\r\n      progress: task.progress || 0,\r\n      description: task.description,\r\n      start_date: task.start_date,\r\n      end_date: task.end_date\r\n    });\r\n    \r\n    const response = await axios.post(url, formData, {\r\n      headers: headers,\r\n    });\r\n    console.log('API Response:', response);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('API Error in addTask:', error);\r\n    console.error('Error details:', error.response?.data || error.message);\r\n    throw new Error('Error adding task: ' + (error.response?.data?.message || error.message));\r\n  }\r\n};\r\n\r\nexport const addSubtask = async (subtask) => {\r\n  console.log('API call - addSubtask:', subtask);\r\n  try {\r\n    const formData = new FormData();\r\n    formData.append('name', subtask.name);\r\n    \r\n    formData.append('status', subtask.status || 1);\r\n    formData.append('progress', subtask.progress || 0);\r\n    \r\n    if (subtask.start_date) {\r\n      formData.append('start_date', subtask.start_date);\r\n    }\r\n    if (subtask.end_date) {\r\n      formData.append('end_date', subtask.end_date);\r\n    }\r\n    \r\n    const headers = getHeaders();\r\n    console.log('Headers:', headers);\r\n    const url = `${APIURL}/api/tasks/${subtask.task}/add-subtask`;\r\n    console.log('API URL:', url);\r\n    console.log('FormData being sent:', {\r\n      name: subtask.name,\r\n      status: subtask.status || 1,\r\n      progress: subtask.progress || 0,\r\n      start_date: subtask.start_date,\r\n      end_date: subtask.end_date\r\n    });\r\n    \r\n    const response = await axios.post(url, formData, {\r\n      headers: headers,\r\n    });\r\n    console.log('API Response:', response);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('API Error in addSubtask:', error);\r\n    console.error('Error details:', error.response?.data || error.message);\r\n    throw new Error('Error adding subtask: ' + (error.response?.data?.message || error.message));\r\n  }\r\n};\r\n\r\nexport const deleteTask = async (taskSlug) => {\r\n  console.log('API call - deleteTask:', taskSlug);\r\n  try {\r\n    const headers = getHeaders();\r\n    console.log('Headers:', headers);\r\n    const url = `${APIURL}/api/tasks/${taskSlug}/delete`;\r\n    console.log('API URL:', url);\r\n    \r\n    const response = await axios.delete(url, {\r\n      headers: headers,\r\n    });\r\n    console.log('API Response:', response);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('API Error in deleteTask:', error);\r\n    console.error('Error details:', error.response?.data || error.message);\r\n    throw new Error('Error deleting task: ' + (error.response?.data?.message || error.message));\r\n  }\r\n};\r\n\r\nexport const deleteSubtask = async (subtaskSlug) => {\r\n  console.log('API call - deleteSubtask:', subtaskSlug);\r\n  try {\r\n    const headers = getHeaders();\r\n    console.log('Headers:', headers);\r\n    const url = `${APIURL}/api/subtasks/${subtaskSlug}/delete`;\r\n    console.log('API URL:', url);\r\n    \r\n    const response = await axios.delete(url, {\r\n      headers: headers,\r\n    });\r\n    console.log('API Response:', response);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('API Error in deleteSubtask:', error);\r\n    console.error('Error details:', error.response?.data || error.message);\r\n    throw new Error('Error deleting subtask: ' + (error.response?.data?.message || error.message));\r\n  }\r\n};\r\n\r\n// Comment APIs\r\nexport const getComments = async (taskId) => {\r\n  try {\r\n    const response = await axios.get(`${APIURL}/api/comments/target/${taskId}/type/1`, {\r\n      headers: getHeaders()\r\n    });\r\n    console.log('Get comments response:', response);\r\n    return response;\r\n  } catch (error) {\r\n    console.error('Error fetching comments:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const addComment = async (taskId, content) => {\r\n  try {\r\n    const formData = new FormData();\r\n    formData.append('content', content);\r\n    formData.append('target_id', taskId);\r\n    formData.append('type', 1);\r\n\r\n    const response = await axios.post(`${APIURL}/api/comments/add`, \r\n      formData,\r\n      { headers: getHeaders() }\r\n    );\r\n    console.log('Add comment response:', response);\r\n    return response;\r\n  } catch (error) {\r\n    console.error('Error adding comment:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const updateComment = async (commentId, content) => {\r\n  try {\r\n    const formData = new FormData();\r\n    formData.append('content', content);\r\n    formData.append('type', 1);\r\n\r\n    const response = await axios.put(`${APIURL}/api/comments/edit/${commentId}`, \r\n      formData,\r\n      { headers: getHeaders() }\r\n    );\r\n    console.log('Update comment response:', response);\r\n    return response;\r\n  } catch (error) {\r\n    console.error('Error updating comment:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const deleteComment = async (commentId) => {\r\n  try {\r\n    const response = await axios.delete(`${APIURL}/api/comments/delete/${commentId}`, {\r\n      headers: getHeaders()\r\n    });\r\n    console.log('Delete comment response:', response);\r\n    return response;\r\n  } catch (error) {\r\n    console.error('Error deleting comment:', error);\r\n    throw error;\r\n  }\r\n};\r\n\r\nexport const assignMembersToTask = async (taskSlug, memberIds) => {\r\n  try {\r\n    const response = await axios.put(\r\n      `${APIURL}/api/tasks/${taskSlug}/assign`,\r\n      { assignees: memberIds },\r\n      { headers: getHeaders() }\r\n    );\r\n    console.log('Assign members response:', response);\r\n    return response.data;\r\n  } catch (error) {\r\n    console.error('Error assigning members to task:', error);\r\n    throw new Error('Error assigning members to task: ' + (error.response?.data?.message || error.message));\r\n  }\r\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,SAASC,MAAM,QAAQ,mBAAmB;AAE1C,OAAO,MAAMC,aAAa,GAAG,MAAOC,KAAK,IAAK;EAC5C,IAAI;IACF,MAAMC,OAAO,GAAGJ,UAAU,CAAC,CAAC;IAC5B,MAAMK,QAAQ,GAAG,MAAMN,KAAK,CAACO,GAAG,CAAC,GAAGL,MAAM,cAAcE,KAAK,EAAE,EAAE;MAAEC;IAAQ,CAAC,CAAC;IAC7E,OAAOC,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMC,kBAAkB,GAAG,MAAOC,KAAK,IAAK;EACjD,IAAI;IACF,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEH,KAAK,CAAC;IAC/B,MAAML,QAAQ,GAAG,MAAMN,KAAK,CAACe,IAAI,CAAC,GAAGb,MAAM,iBAAiB,EAAEU,QAAQ,EAAE;MACtEP,OAAO,EAAEJ,UAAU,CAAC;IACtB,CAAC,CAAC;IACF,OAAOK,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMO,cAAc,GAAG,MAAAA,CAAOC,IAAI,EAAEN,KAAK,KAAK;EACnD,IAAI;IACF,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEH,KAAK,CAAC;IAC/B,MAAML,QAAQ,GAAG,MAAMN,KAAK,CAACe,IAAI,CAAC,GAAGb,MAAM,cAAce,IAAI,CAACC,IAAI,SAAS,EAAEN,QAAQ,EAAE;MACrFP,OAAO,EAAEJ,UAAU,CAAC;IACtB,CAAC,CAAC;IACF,OAAOK,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMU,eAAe,GAAG,MAAOC,QAAQ,IAAK;EACjD,IAAI;IACFC,OAAO,CAACC,GAAG,CAAC,4CAA4C,EAAEF,QAAQ,CAAC;IACnE,MAAMd,QAAQ,GAAG,MAAMN,KAAK,CAACO,GAAG,CAAC,GAAGL,MAAM,cAAckB,QAAQ,gBAAgB,EAAE;MAChFf,OAAO,EAAEJ,UAAU,CAAC;IACtB,CAAC,CAAC;IACFoB,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEhB,QAAQ,CAAC;;IAE3C;IACA,IAAIA,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACA,IAAI,EAAE;MACvC,OAAOF,QAAQ,CAACE,IAAI;IACtB,CAAC,MAAM,IAAIe,KAAK,CAACC,OAAO,CAAClB,QAAQ,CAACE,IAAI,CAAC,EAAE;MACvC,OAAO;QAAEA,IAAI,EAAEF,QAAQ,CAACE;MAAK,CAAC;IAChC,CAAC,MAAM;MACLa,OAAO,CAACI,IAAI,CAAC,oCAAoC,EAAEnB,QAAQ,CAACE,IAAI,CAAC;MACjE,OAAO;QAAEA,IAAI,EAAE;MAAG,CAAC;IACrB;EACF,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdY,OAAO,CAACZ,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACrD,MAAM,IAAIiB,KAAK,CAAC,gCAAgC,GAAGjB,KAAK,CAACkB,OAAO,CAAC;EACnE;AACF,CAAC;AAED,OAAO,MAAMC,UAAU,GAAG,MAAOR,QAAQ,IAAK;EAC5C,IAAI;IACF,MAAMd,QAAQ,GAAG,MAAMN,KAAK,CAAC6B,MAAM,CAAC,GAAG3B,MAAM,oBAAoBkB,QAAQ,EAAE,EAAE;MAC3Ef,OAAO,EAAEJ,UAAU,CAAC;IACtB,CAAC,CAAC;IACF,OAAOK,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,MAAM,IAAIiB,KAAK,CAAC,+BAA+B,GAAGjB,KAAK,CAACkB,OAAO,CAAC;EAClE;AACF,CAAC;AAED,OAAO,MAAMG,UAAU,GAAG,MAAOV,QAAQ,IAAK;EAC5C,IAAI;IACF,MAAMd,QAAQ,GAAG,MAAMN,KAAK,CAAC6B,MAAM,CAAC,GAAG3B,MAAM,qBAAqBkB,QAAQ,EAAE,EAAE;MAC5Ef,OAAO,EAAEJ,UAAU,CAAC;IACtB,CAAC,CAAC;IACF,OAAOK,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd,MAAM,IAAIiB,KAAK,CAAC,4BAA4B,GAAGjB,KAAK,CAACkB,OAAO,CAAC;EAC/D;AACF,CAAC;AAED,OAAO,MAAMI,eAAe,GAAG,MAAOC,SAAS,IAAK;EAClDX,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEU,SAAS,CAAC;EACrD,IAAI;IACF,MAAMpB,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEkB,SAAS,CAACC,IAAI,CAAC;IAEvC,MAAM5B,OAAO,GAAGJ,UAAU,CAAC,CAAC;IAC5BoB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEjB,OAAO,CAAC;IAChC,MAAM6B,GAAG,GAAG,GAAGhC,MAAM,mBAAmB8B,SAAS,CAACG,EAAE,SAAS;IAC7Dd,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEY,GAAG,CAAC;IAE5B,MAAM5B,QAAQ,GAAG,MAAMN,KAAK,CAACoC,GAAG,CAACF,GAAG,EAAEtB,QAAQ,EAAE;MAC9CP,OAAO,EAAEA;IACX,CAAC,CAAC;IACFgB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEhB,QAAQ,CAAC;IACtC,OAAOA,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA,IAAA4B,eAAA,EAAAC,gBAAA,EAAAC,qBAAA;IACdlB,OAAO,CAACZ,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;IACrDY,OAAO,CAACZ,KAAK,CAAC,gBAAgB,EAAE,EAAA4B,eAAA,GAAA5B,KAAK,CAACH,QAAQ,cAAA+B,eAAA,uBAAdA,eAAA,CAAgB7B,IAAI,KAAIC,KAAK,CAACkB,OAAO,CAAC;IACtE,MAAM,IAAID,KAAK,CAAC,4BAA4B,IAAI,EAAAY,gBAAA,GAAA7B,KAAK,CAACH,QAAQ,cAAAgC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB9B,IAAI,cAAA+B,qBAAA,uBAApBA,qBAAA,CAAsBZ,OAAO,KAAIlB,KAAK,CAACkB,OAAO,CAAC,CAAC;EAClG;AACF,CAAC;AAED,OAAO,MAAMa,UAAU,GAAG,MAAOC,IAAI,IAAK;EACxC,IAAI;IACFpB,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEmB,IAAI,CAAC;IACnC,MAAM7B,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;;IAE/B;IACAD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE2B,IAAI,CAACR,IAAI,CAAC;;IAElC;IACA,IAAIQ,IAAI,CAACC,UAAU,EAAE;MACnB9B,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAE2B,IAAI,CAACC,UAAU,CAAC;IAChD;IAEA,IAAID,IAAI,CAACE,QAAQ,EAAE;MACjB/B,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAE2B,IAAI,CAACE,QAAQ,CAAC;IAC5C;;IAEA;IACA,IAAIF,IAAI,CAACG,MAAM,EAAE;MACfhC,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE2B,IAAI,CAACG,MAAM,CAAC;IACxC;;IAEA;IACA,MAAMC,QAAQ,GAAGJ,IAAI,CAACI,QAAQ,KAAK,IAAI,IAAIJ,IAAI,CAACI,QAAQ,KAAKC,SAAS,GAAGL,IAAI,CAACI,QAAQ,GAAG,CAAC;IAC1FjC,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAE+B,QAAQ,CAAC;IAErC,MAAMvC,QAAQ,GAAG,MAAMN,KAAK,CAACoC,GAAG,CAC9B,GAAGlC,MAAM,qBAAqBuC,IAAI,CAACvB,IAAI,EAAE,EACzCN,QAAQ,EACR;MAAEP,OAAO,EAAEJ,UAAU,CAAC;IAAE,CAC1B,CAAC;IAEDoB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEhB,QAAQ,CAAC;IAC9C,OAAOA,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdY,OAAO,CAACZ,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;IAC5C,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMsC,aAAa,GAAG,MAAOC,OAAO,IAAK;EAC9C3B,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE0B,OAAO,CAAC;EACjD,IAAI;IACF,MAAMpC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEkC,OAAO,CAACf,IAAI,CAAC;IAErCrB,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEkC,OAAO,CAACJ,MAAM,CAAC;IACzC,IAAII,OAAO,CAACH,QAAQ,KAAKC,SAAS,EAAE;MAClClC,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEkC,OAAO,CAACH,QAAQ,CAAC;IAC/C;IAEA,IAAIG,OAAO,CAACN,UAAU,EAAE;MACtB9B,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEkC,OAAO,CAACN,UAAU,CAAC;IACnD;IACA,IAAIM,OAAO,CAACL,QAAQ,EAAE;MACpB/B,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEkC,OAAO,CAACL,QAAQ,CAAC;IAC/C;IAEA,MAAMtC,OAAO,GAAGJ,UAAU,CAAC,CAAC;IAC5BoB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEjB,OAAO,CAAC;IAChC,MAAM6B,GAAG,GAAG,GAAGhC,MAAM,wBAAwB8C,OAAO,CAAC9B,IAAI,EAAE;IAC3DG,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEY,GAAG,CAAC;IAC5Bb,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;MAClCW,IAAI,EAAEe,OAAO,CAACf,IAAI;MAClBW,MAAM,EAAEI,OAAO,CAACJ,MAAM;MACtBC,QAAQ,EAAEG,OAAO,CAACH,QAAQ;MAC1BH,UAAU,EAAEM,OAAO,CAACN,UAAU;MAC9BC,QAAQ,EAAEK,OAAO,CAACL;IACpB,CAAC,CAAC;IAEF,MAAMrC,QAAQ,GAAG,MAAMN,KAAK,CAACoC,GAAG,CAACF,GAAG,EAAEtB,QAAQ,EAAE;MAC9CP,OAAO,EAAEA;IACX,CAAC,CAAC;IACFgB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEhB,QAAQ,CAAC;IACtC,OAAOA,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA,IAAAwC,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA;IACd9B,OAAO,CAACZ,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACnDY,OAAO,CAACZ,KAAK,CAAC,gBAAgB,EAAE,EAAAwC,gBAAA,GAAAxC,KAAK,CAACH,QAAQ,cAAA2C,gBAAA,uBAAdA,gBAAA,CAAgBzC,IAAI,KAAIC,KAAK,CAACkB,OAAO,CAAC;IACtE,MAAM,IAAID,KAAK,CAAC,0BAA0B,IAAI,EAAAwB,gBAAA,GAAAzC,KAAK,CAACH,QAAQ,cAAA4C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1C,IAAI,cAAA2C,qBAAA,uBAApBA,qBAAA,CAAsBxB,OAAO,KAAIlB,KAAK,CAACkB,OAAO,CAAC,CAAC;EAChG;AACF,CAAC;AAED,OAAO,MAAMyB,OAAO,GAAG,MAAOX,IAAI,IAAK;EACrCpB,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEmB,IAAI,CAAC;EACxC,IAAI;IACF,MAAM7B,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE2B,IAAI,CAACR,IAAI,CAAC;IAElCrB,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE2B,IAAI,CAACG,MAAM,IAAI,CAAC,CAAC;IAC3ChC,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAE2B,IAAI,CAACI,QAAQ,IAAI,CAAC,CAAC;IAE/C,IAAIJ,IAAI,CAACY,WAAW,EAAE;MACpBzC,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAE2B,IAAI,CAACY,WAAW,CAAC;IAClD;IACA,IAAIZ,IAAI,CAACC,UAAU,EAAE;MACnB9B,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAE2B,IAAI,CAACC,UAAU,CAAC;IAChD;IACA,IAAID,IAAI,CAACE,QAAQ,EAAE;MACjB/B,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAE2B,IAAI,CAACE,QAAQ,CAAC;IAC5C;IAEA,MAAMtC,OAAO,GAAGJ,UAAU,CAAC,CAAC;IAC5BoB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEjB,OAAO,CAAC;IAChC,MAAM6B,GAAG,GAAG,GAAGhC,MAAM,mBAAmBuC,IAAI,CAACT,SAAS,YAAY;IAClEX,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEY,GAAG,CAAC;IAC5Bb,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;MAClCW,IAAI,EAAEQ,IAAI,CAACR,IAAI;MACfW,MAAM,EAAEH,IAAI,CAACG,MAAM,IAAI,CAAC;MACxBC,QAAQ,EAAEJ,IAAI,CAACI,QAAQ,IAAI,CAAC;MAC5BQ,WAAW,EAAEZ,IAAI,CAACY,WAAW;MAC7BX,UAAU,EAAED,IAAI,CAACC,UAAU;MAC3BC,QAAQ,EAAEF,IAAI,CAACE;IACjB,CAAC,CAAC;IAEF,MAAMrC,QAAQ,GAAG,MAAMN,KAAK,CAACe,IAAI,CAACmB,GAAG,EAAEtB,QAAQ,EAAE;MAC/CP,OAAO,EAAEA;IACX,CAAC,CAAC;IACFgB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEhB,QAAQ,CAAC;IACtC,OAAOA,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA,IAAA6C,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA;IACdnC,OAAO,CAACZ,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC7CY,OAAO,CAACZ,KAAK,CAAC,gBAAgB,EAAE,EAAA6C,gBAAA,GAAA7C,KAAK,CAACH,QAAQ,cAAAgD,gBAAA,uBAAdA,gBAAA,CAAgB9C,IAAI,KAAIC,KAAK,CAACkB,OAAO,CAAC;IACtE,MAAM,IAAID,KAAK,CAAC,qBAAqB,IAAI,EAAA6B,gBAAA,GAAA9C,KAAK,CAACH,QAAQ,cAAAiD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB/C,IAAI,cAAAgD,qBAAA,uBAApBA,qBAAA,CAAsB7B,OAAO,KAAIlB,KAAK,CAACkB,OAAO,CAAC,CAAC;EAC3F;AACF,CAAC;AAED,OAAO,MAAM8B,UAAU,GAAG,MAAOT,OAAO,IAAK;EAC3C3B,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE0B,OAAO,CAAC;EAC9C,IAAI;IACF,MAAMpC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEkC,OAAO,CAACf,IAAI,CAAC;IAErCrB,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAEkC,OAAO,CAACJ,MAAM,IAAI,CAAC,CAAC;IAC9ChC,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEkC,OAAO,CAACH,QAAQ,IAAI,CAAC,CAAC;IAElD,IAAIG,OAAO,CAACN,UAAU,EAAE;MACtB9B,QAAQ,CAACE,MAAM,CAAC,YAAY,EAAEkC,OAAO,CAACN,UAAU,CAAC;IACnD;IACA,IAAIM,OAAO,CAACL,QAAQ,EAAE;MACpB/B,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEkC,OAAO,CAACL,QAAQ,CAAC;IAC/C;IAEA,MAAMtC,OAAO,GAAGJ,UAAU,CAAC,CAAC;IAC5BoB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEjB,OAAO,CAAC;IAChC,MAAM6B,GAAG,GAAG,GAAGhC,MAAM,cAAc8C,OAAO,CAACP,IAAI,cAAc;IAC7DpB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEY,GAAG,CAAC;IAC5Bb,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE;MAClCW,IAAI,EAAEe,OAAO,CAACf,IAAI;MAClBW,MAAM,EAAEI,OAAO,CAACJ,MAAM,IAAI,CAAC;MAC3BC,QAAQ,EAAEG,OAAO,CAACH,QAAQ,IAAI,CAAC;MAC/BH,UAAU,EAAEM,OAAO,CAACN,UAAU;MAC9BC,QAAQ,EAAEK,OAAO,CAACL;IACpB,CAAC,CAAC;IAEF,MAAMrC,QAAQ,GAAG,MAAMN,KAAK,CAACe,IAAI,CAACmB,GAAG,EAAEtB,QAAQ,EAAE;MAC/CP,OAAO,EAAEA;IACX,CAAC,CAAC;IACFgB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEhB,QAAQ,CAAC;IACtC,OAAOA,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA,IAAAiD,gBAAA,EAAAC,gBAAA,EAAAC,qBAAA;IACdvC,OAAO,CAACZ,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAChDY,OAAO,CAACZ,KAAK,CAAC,gBAAgB,EAAE,EAAAiD,gBAAA,GAAAjD,KAAK,CAACH,QAAQ,cAAAoD,gBAAA,uBAAdA,gBAAA,CAAgBlD,IAAI,KAAIC,KAAK,CAACkB,OAAO,CAAC;IACtE,MAAM,IAAID,KAAK,CAAC,wBAAwB,IAAI,EAAAiC,gBAAA,GAAAlD,KAAK,CAACH,QAAQ,cAAAqD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnD,IAAI,cAAAoD,qBAAA,uBAApBA,qBAAA,CAAsBjC,OAAO,KAAIlB,KAAK,CAACkB,OAAO,CAAC,CAAC;EAC9F;AACF,CAAC;AAED,OAAO,MAAMkC,UAAU,GAAG,MAAOC,QAAQ,IAAK;EAC5CzC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEwC,QAAQ,CAAC;EAC/C,IAAI;IACF,MAAMzD,OAAO,GAAGJ,UAAU,CAAC,CAAC;IAC5BoB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEjB,OAAO,CAAC;IAChC,MAAM6B,GAAG,GAAG,GAAGhC,MAAM,cAAc4D,QAAQ,SAAS;IACpDzC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEY,GAAG,CAAC;IAE5B,MAAM5B,QAAQ,GAAG,MAAMN,KAAK,CAAC6B,MAAM,CAACK,GAAG,EAAE;MACvC7B,OAAO,EAAEA;IACX,CAAC,CAAC;IACFgB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEhB,QAAQ,CAAC;IACtC,OAAOA,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA,IAAAsD,gBAAA,EAAAC,iBAAA,EAAAC,qBAAA;IACd5C,OAAO,CAACZ,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAChDY,OAAO,CAACZ,KAAK,CAAC,gBAAgB,EAAE,EAAAsD,gBAAA,GAAAtD,KAAK,CAACH,QAAQ,cAAAyD,gBAAA,uBAAdA,gBAAA,CAAgBvD,IAAI,KAAIC,KAAK,CAACkB,OAAO,CAAC;IACtE,MAAM,IAAID,KAAK,CAAC,uBAAuB,IAAI,EAAAsC,iBAAA,GAAAvD,KAAK,CAACH,QAAQ,cAAA0D,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgBxD,IAAI,cAAAyD,qBAAA,uBAApBA,qBAAA,CAAsBtC,OAAO,KAAIlB,KAAK,CAACkB,OAAO,CAAC,CAAC;EAC7F;AACF,CAAC;AAED,OAAO,MAAMuC,aAAa,GAAG,MAAOC,WAAW,IAAK;EAClD9C,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE6C,WAAW,CAAC;EACrD,IAAI;IACF,MAAM9D,OAAO,GAAGJ,UAAU,CAAC,CAAC;IAC5BoB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEjB,OAAO,CAAC;IAChC,MAAM6B,GAAG,GAAG,GAAGhC,MAAM,iBAAiBiE,WAAW,SAAS;IAC1D9C,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEY,GAAG,CAAC;IAE5B,MAAM5B,QAAQ,GAAG,MAAMN,KAAK,CAAC6B,MAAM,CAACK,GAAG,EAAE;MACvC7B,OAAO,EAAEA;IACX,CAAC,CAAC;IACFgB,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEhB,QAAQ,CAAC;IACtC,OAAOA,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA,IAAA2D,iBAAA,EAAAC,iBAAA,EAAAC,qBAAA;IACdjD,OAAO,CAACZ,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACnDY,OAAO,CAACZ,KAAK,CAAC,gBAAgB,EAAE,EAAA2D,iBAAA,GAAA3D,KAAK,CAACH,QAAQ,cAAA8D,iBAAA,uBAAdA,iBAAA,CAAgB5D,IAAI,KAAIC,KAAK,CAACkB,OAAO,CAAC;IACtE,MAAM,IAAID,KAAK,CAAC,0BAA0B,IAAI,EAAA2C,iBAAA,GAAA5D,KAAK,CAACH,QAAQ,cAAA+D,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgB7D,IAAI,cAAA8D,qBAAA,uBAApBA,qBAAA,CAAsB3C,OAAO,KAAIlB,KAAK,CAACkB,OAAO,CAAC,CAAC;EAChG;AACF,CAAC;;AAED;AACA,OAAO,MAAM4C,WAAW,GAAG,MAAOC,MAAM,IAAK;EAC3C,IAAI;IACF,MAAMlE,QAAQ,GAAG,MAAMN,KAAK,CAACO,GAAG,CAAC,GAAGL,MAAM,wBAAwBsE,MAAM,SAAS,EAAE;MACjFnE,OAAO,EAAEJ,UAAU,CAAC;IACtB,CAAC,CAAC;IACFoB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEhB,QAAQ,CAAC;IAC/C,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdY,OAAO,CAACZ,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAChD,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMgE,UAAU,GAAG,MAAAA,CAAOD,MAAM,EAAEE,OAAO,KAAK;EACnD,IAAI;IACF,MAAM9D,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAE4D,OAAO,CAAC;IACnC9D,QAAQ,CAACE,MAAM,CAAC,WAAW,EAAE0D,MAAM,CAAC;IACpC5D,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;IAE1B,MAAMR,QAAQ,GAAG,MAAMN,KAAK,CAACe,IAAI,CAAC,GAAGb,MAAM,mBAAmB,EAC5DU,QAAQ,EACR;MAAEP,OAAO,EAAEJ,UAAU,CAAC;IAAE,CAC1B,CAAC;IACDoB,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEhB,QAAQ,CAAC;IAC9C,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdY,OAAO,CAACZ,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC7C,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMkE,aAAa,GAAG,MAAAA,CAAOC,SAAS,EAAEF,OAAO,KAAK;EACzD,IAAI;IACF,MAAM9D,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,SAAS,EAAE4D,OAAO,CAAC;IACnC9D,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;IAE1B,MAAMR,QAAQ,GAAG,MAAMN,KAAK,CAACoC,GAAG,CAAC,GAAGlC,MAAM,sBAAsB0E,SAAS,EAAE,EACzEhE,QAAQ,EACR;MAAEP,OAAO,EAAEJ,UAAU,CAAC;IAAE,CAC1B,CAAC;IACDoB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEhB,QAAQ,CAAC;IACjD,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdY,OAAO,CAACZ,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/C,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMoE,aAAa,GAAG,MAAOD,SAAS,IAAK;EAChD,IAAI;IACF,MAAMtE,QAAQ,GAAG,MAAMN,KAAK,CAAC6B,MAAM,CAAC,GAAG3B,MAAM,wBAAwB0E,SAAS,EAAE,EAAE;MAChFvE,OAAO,EAAEJ,UAAU,CAAC;IACtB,CAAC,CAAC;IACFoB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEhB,QAAQ,CAAC;IACjD,OAAOA,QAAQ;EACjB,CAAC,CAAC,OAAOG,KAAK,EAAE;IACdY,OAAO,CAACZ,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;IAC/C,MAAMA,KAAK;EACb;AACF,CAAC;AAED,OAAO,MAAMqE,mBAAmB,GAAG,MAAAA,CAAOhB,QAAQ,EAAEiB,SAAS,KAAK;EAChE,IAAI;IACF,MAAMzE,QAAQ,GAAG,MAAMN,KAAK,CAACoC,GAAG,CAC9B,GAAGlC,MAAM,cAAc4D,QAAQ,SAAS,EACxC;MAAEkB,SAAS,EAAED;IAAU,CAAC,EACxB;MAAE1E,OAAO,EAAEJ,UAAU,CAAC;IAAE,CAC1B,CAAC;IACDoB,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEhB,QAAQ,CAAC;IACjD,OAAOA,QAAQ,CAACE,IAAI;EACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;IAAA,IAAAwE,iBAAA,EAAAC,qBAAA;IACd7D,OAAO,CAACZ,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;IACxD,MAAM,IAAIiB,KAAK,CAAC,mCAAmC,IAAI,EAAAuD,iBAAA,GAAAxE,KAAK,CAACH,QAAQ,cAAA2E,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgBzE,IAAI,cAAA0E,qBAAA,uBAApBA,qBAAA,CAAsBvD,OAAO,KAAIlB,KAAK,CAACkB,OAAO,CAAC,CAAC;EACzG;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}