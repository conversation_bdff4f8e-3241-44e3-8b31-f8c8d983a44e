/* eslint-disable react-hooks/exhaustive-deps */
import React from 'react';
import { Box, Typography, Container } from '@mui/material';
import { useSelector } from 'react-redux';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import 'dayjs/locale/en'; // Import English locale
import styles from "./styles.module.scss";

// Custom hooks
import usePlanFetching from './hooks/usePlanFetching';
import useFilters from './hooks/useFilters';
import useGrouping from './hooks/useGrouping';

// Components
import FilterPopper from './popper';
import EmptyState from './components/EmptyState';
import ViewControls from './components/ViewControls';
import FilterBar from './components/FilterBar';
import CreatePlanButton from './components/CreatePlanButton';
import PlanList from './components/PlanList';
import LoadingPlans from './components/LoadingPlans';

// Configure dayjs
dayjs.extend(relativeTime);
dayjs.locale('en'); // Use English locale

//--------------------------------------------------------------------------------------------------

const Home = () => {
  const INIT_FILTER_DATA = { text: '', inviteUser: '', createdDate: '' };
  const currentUser = useSelector((state) => state.user);

  // Custom hooks
  const {
    filters,
    tempFilters,
    filterAnchor,
    open,
    id,
    handleFilterClick,
    handleTempFilterChange,
    handleApplyFilters,
    handleClosePopper,
    handleRemoveFilter,
    handleClearAllFilters,
    setTempFilters
  } = useFilters(INIT_FILTER_DATA);

  const {
    plans,
    loading,
    loadingMore,
    observerRef
  } = usePlanFetching(filters);

  const {
    viewMode,
    groupBy,
    handleViewModeChange,
    handleGroupByChange,
    getGroupedPlans
  } = useGrouping(plans);

  const groupedPlans = getGroupedPlans();

  return (
    <Container
      maxWidth="lg"
      className={styles.mainContainer}
      sx={{
        padding: '24px',
        minHeight: 'calc(100vh - 65px)',
        fontFamily: '"Recursive Variable", sans-serif'
      }}
    >
      <Box className={styles.header}>
        <Typography
          variant="h4"
          className={styles.pageTitle}
          sx={{
            fontFamily: '"Recursive Variable", sans-serif',
            fontWeight: 700,
            color: '#333',
            margin: 0
          }}
        >
          My Plans
        </Typography>

        <Box className={styles.actionButtons}>
          <ViewControls
            viewMode={viewMode}
            groupBy={groupBy}
            onViewModeChange={handleViewModeChange}
            onGroupByChange={handleGroupByChange}
          />

          <FilterBar
            filters={filters}
            open={open}
            id={id}
            filterAnchor={filterAnchor}
            onFilterClick={handleFilterClick}
            onRemoveFilter={handleRemoveFilter}
            onClearAllFilters={handleClearAllFilters}
          />

          <CreatePlanButton />
        </Box>
      </Box>

      <FilterPopper
        id={id}
        open={open}
        anchorEl={filterAnchor}
        filters={tempFilters}
        onFilterChange={handleTempFilterChange}
        onApplyFilters={handleApplyFilters}
        onClose={handleClosePopper}
        setFilters={setTempFilters}
      />

      {loading ? (
        <LoadingPlans viewMode={viewMode} />
      ) : plans.length > 0 ? (
        <PlanList
          groupedPlans={groupedPlans}
          viewMode={viewMode}
          currentUser={currentUser}
          observerRef={observerRef}
          loadingMore={loadingMore}
        />
      ) : (
        <EmptyState />
      )}
    </Container>
  );
};

export default Home;
