{"ast": null, "code": "'use client';\n\nimport { createBox } from '@mui/system';\nimport PropTypes from 'prop-types';\nimport { unstable_ClassNameGenerator as ClassNameGenerator } from '../className';\nimport defaultTheme from '../styles/defaultTheme';\nimport THEME_ID from '../styles/identifier';\nimport boxClasses from './boxClasses';\n/**\n *\n * Demos:\n *\n * - [Box](https://mui.com/joy-ui/react-box/)\n *\n * API:\n *\n * - [Box API](https://mui.com/joy-ui/api/box/)\n */\nconst Box = createBox({\n  themeId: THEME_ID,\n  defaultTheme,\n  defaultClassName: boxClasses.root,\n  generateClassName: ClassNameGenerator.generate\n});\nprocess.env.NODE_ENV !== \"production\" ? Box.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Box;", "map": {"version": 3, "names": ["createBox", "PropTypes", "unstable_ClassNameGenerator", "ClassNameGenerator", "defaultTheme", "THEME_ID", "boxClasses", "Box", "themeId", "defaultClassName", "root", "generateClassName", "generate", "process", "env", "NODE_ENV", "propTypes", "children", "node", "component", "elementType", "sx", "oneOfType", "arrayOf", "func", "object", "bool"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Box/Box.js"], "sourcesContent": ["'use client';\n\nimport { createBox } from '@mui/system';\nimport PropTypes from 'prop-types';\nimport { unstable_ClassNameGenerator as ClassNameGenerator } from '../className';\nimport defaultTheme from '../styles/defaultTheme';\nimport THEME_ID from '../styles/identifier';\nimport boxClasses from './boxClasses';\n/**\n *\n * Demos:\n *\n * - [Box](https://mui.com/joy-ui/react-box/)\n *\n * API:\n *\n * - [Box API](https://mui.com/joy-ui/api/box/)\n */\nconst Box = createBox({\n  themeId: THEME_ID,\n  defaultTheme,\n  defaultClassName: boxClasses.root,\n  generateClassName: ClassNameGenerator.generate\n});\nprocess.env.NODE_ENV !== \"production\" ? Box.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Box;"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,SAAS,QAAQ,aAAa;AACvC,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,2BAA2B,IAAIC,kBAAkB,QAAQ,cAAc;AAChF,OAAOC,YAAY,MAAM,wBAAwB;AACjD,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,UAAU,MAAM,cAAc;AACrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,GAAG,GAAGP,SAAS,CAAC;EACpBQ,OAAO,EAAEH,QAAQ;EACjBD,YAAY;EACZK,gBAAgB,EAAEH,UAAU,CAACI,IAAI;EACjCC,iBAAiB,EAAER,kBAAkB,CAACS;AACxC,CAAC,CAAC;AACFC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGR,GAAG,CAACS,SAAS,CAAC,yBAAyB;EAC7E;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAEhB,SAAS,CAACiB,IAAI;EACxB;AACF;AACA;AACA;EACEC,SAAS,EAAElB,SAAS,CAACmB,WAAW;EAChC;AACF;AACA;EACEC,EAAE,EAAEpB,SAAS,CAACqB,SAAS,CAAC,CAACrB,SAAS,CAACsB,OAAO,CAACtB,SAAS,CAACqB,SAAS,CAAC,CAACrB,SAAS,CAACuB,IAAI,EAAEvB,SAAS,CAACwB,MAAM,EAAExB,SAAS,CAACyB,IAAI,CAAC,CAAC,CAAC,EAAEzB,SAAS,CAACuB,IAAI,EAAEvB,SAAS,CAACwB,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAelB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}