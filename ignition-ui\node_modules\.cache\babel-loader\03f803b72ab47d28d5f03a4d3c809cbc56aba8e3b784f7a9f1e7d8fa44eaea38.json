{"ast": null, "code": "import React,{useState}from'react';import{Box,Typography,Paper,Button,Dialog,DialogTitle,DialogContent,DialogActions,TextField,Select,MenuItem,FormControl,InputLabel,List,ListItem,ListItemText,ListItemSecondaryAction,IconButton,Chip,Avatar,Tooltip}from'@mui/material';import Iconify from'components/Iconify/index';import{mainYellowColor}from\"helpers/constants\";import styles from'../styles.module.scss';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AccessManagement=_ref=>{let{planInfo,userAccessLevel,onAddAccess,onUpdateAccess,onRemoveAccess}=_ref;const[addDialogOpen,setAddDialogOpen]=useState(false);const[newUserEmail,setNewUserEmail]=useState('');const[newAccessLevel,setNewAccessLevel]=useState('viewer');const[loading,setLoading]=useState(false);const accessLevels=(planInfo===null||planInfo===void 0?void 0:planInfo.access_levels)||[];const isOwner=(userAccessLevel===null||userAccessLevel===void 0?void 0:userAccessLevel.access_level)==='owner';const isHeadOwner=userAccessLevel===null||userAccessLevel===void 0?void 0:userAccessLevel.is_head_owner;const getAccessLevelColor=level=>{switch(level){case'owner':return'#ef4444';case'editor':return'#f97316';case'viewer':return'#10b981';default:return'#6b7280';}};const getAccessLevelIcon=level=>{switch(level){case'owner':return'material-symbols:admin-panel-settings';case'editor':return'material-symbols:edit';case'viewer':return'material-symbols:visibility';default:return'material-symbols:person';}};const handleAddAccess=async()=>{if(!newUserEmail.trim())return;setLoading(true);try{await onAddAccess(newUserEmail,newAccessLevel);setNewUserEmail('');setNewAccessLevel('viewer');setAddDialogOpen(false);}catch(error){console.error('Error adding access:',error);}finally{setLoading(false);}};const handleUpdateAccess=async function(accessId,newLevel){let makeHeadOwner=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;try{await onUpdateAccess(accessId,newLevel,makeHeadOwner);}catch(error){console.error('Error updating access:',error);}};const handleRemoveAccess=async accessId=>{try{await onRemoveAccess(accessId);}catch(error){console.error('Error removing access:',error);}};const canManageUser=access=>{if(access.is_head_owner)return false;// Cannot manage head owner\nif(access.access_level==='owner'&&!isHeadOwner)return false;// Only head owner can manage owners\nreturn isOwner;// Owners can manage editors and viewers\n};return/*#__PURE__*/_jsxs(Paper,{elevation:0,sx:{p:3,borderRadius:'12px',border:'1px solid #f0f0f0',backgroundColor:'#fff'},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center',mb:3},children:[/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",sx:{fontWeight:600,color:'#333',fontFamily:'\"Recursive Variable\", sans-serif',display:'flex',alignItems:'center',gap:1},children:[/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:security\",width:24,height:24,color:mainYellowColor}),\"Access Management\"]}),isOwner&&/*#__PURE__*/_jsx(Button,{variant:\"contained\",startIcon:/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:person-add\"}),onClick:()=>setAddDialogOpen(true),sx:{backgroundColor:mainYellowColor,color:'#fff',textTransform:'none',fontFamily:'\"Recursive Variable\", sans-serif','&:hover':{backgroundColor:'#e6940a'}},children:\"Add Member\"})]}),/*#__PURE__*/_jsx(List,{children:accessLevels.map(access=>/*#__PURE__*/_jsxs(ListItem,{sx:{border:'1px solid #f0f0f0',borderRadius:'8px',mb:1,backgroundColor:'#fafafa'},children:[/*#__PURE__*/_jsx(Avatar,{src:access.user.avatar,alt:access.user.first_name||access.user.email,sx:{mr:2,bgcolor:mainYellowColor},children:access.user.first_name?access.user.first_name.charAt(0).toUpperCase():access.user.email.charAt(0).toUpperCase()}),/*#__PURE__*/_jsx(ListItemText,{primary:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:1},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",sx:{fontWeight:600,fontFamily:'\"Recursive Variable\", sans-serif'},children:access.user.first_name&&access.user.last_name?`${access.user.first_name} ${access.user.last_name}`:access.user.email}),access.is_head_owner&&/*#__PURE__*/_jsx(Chip,{label:\"Head Owner\",size:\"small\",sx:{backgroundColor:'#fef3c7',color:'#92400e',fontWeight:600,fontSize:'0.7rem'}})]}),secondary:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:1,mt:0.5},children:[/*#__PURE__*/_jsx(Chip,{icon:/*#__PURE__*/_jsx(Iconify,{icon:getAccessLevelIcon(access.access_level),width:16,height:16}),label:access.access_level.charAt(0).toUpperCase()+access.access_level.slice(1),size:\"small\",sx:{backgroundColor:`${getAccessLevelColor(access.access_level)}20`,color:getAccessLevelColor(access.access_level),fontWeight:600,fontFamily:'\"Recursive Variable\", sans-serif'}}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{color:'#666',fontFamily:'\"Recursive Variable\", sans-serif'},children:access.user.email})]})}),/*#__PURE__*/_jsx(ListItemSecondaryAction,{children:canManageUser(access)&&/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',gap:1},children:[access.access_level==='owner'&&isHeadOwner&&/*#__PURE__*/_jsx(Tooltip,{title:\"Transfer Head Owner\",children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>handleUpdateAccess(access.id,'owner',true),sx:{color:'#f59e0b'},children:/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:crown\",width:20,height:20})})}),/*#__PURE__*/_jsx(Tooltip,{title:\"Remove Access\",children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>handleRemoveAccess(access.id),sx:{color:'#ef4444'},children:/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:person-remove\",width:20,height:20})})})]})})]},access.id))}),/*#__PURE__*/_jsxs(Dialog,{open:addDialogOpen,onClose:()=>setAddDialogOpen(false),maxWidth:\"sm\",fullWidth:true,children:[/*#__PURE__*/_jsx(DialogTitle,{sx:{fontFamily:'\"Recursive Variable\", sans-serif'},children:\"Add Team Member\"}),/*#__PURE__*/_jsxs(DialogContent,{children:[/*#__PURE__*/_jsx(TextField,{autoFocus:true,margin:\"dense\",label:\"Email Address\",type:\"email\",fullWidth:true,variant:\"outlined\",value:newUserEmail,onChange:e=>setNewUserEmail(e.target.value),sx:{mb:2}}),/*#__PURE__*/_jsxs(FormControl,{fullWidth:true,children:[/*#__PURE__*/_jsx(InputLabel,{children:\"Access Level\"}),/*#__PURE__*/_jsxs(Select,{value:newAccessLevel,label:\"Access Level\",onChange:e=>setNewAccessLevel(e.target.value),children:[/*#__PURE__*/_jsx(MenuItem,{value:\"viewer\",children:\"Viewer - Can view plan content\"}),/*#__PURE__*/_jsx(MenuItem,{value:\"editor\",children:\"Editor - Can edit plan content\"}),isHeadOwner&&/*#__PURE__*/_jsx(MenuItem,{value:\"owner\",children:\"Owner - Can manage team members\"})]})]})]}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(Button,{onClick:()=>setAddDialogOpen(false),sx:{fontFamily:'\"Recursive Variable\", sans-serif'},children:\"Cancel\"}),/*#__PURE__*/_jsx(Button,{onClick:handleAddAccess,variant:\"contained\",disabled:loading||!newUserEmail.trim(),sx:{backgroundColor:mainYellowColor,fontFamily:'\"Recursive Variable\", sans-serif','&:hover':{backgroundColor:'#e6940a'}},children:loading?'Adding...':'Add Member'})]})]})]});};export default AccessManagement;", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Paper", "<PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "TextField", "Select", "MenuItem", "FormControl", "InputLabel", "List", "ListItem", "ListItemText", "ListItemSecondaryAction", "IconButton", "Chip", "Avatar", "<PERSON><PERSON><PERSON>", "Iconify", "mainYellowColor", "styles", "jsx", "_jsx", "jsxs", "_jsxs", "AccessManagement", "_ref", "planInfo", "userAccessLevel", "onAddAccess", "onUpdateAccess", "onRemoveAccess", "addDialogOpen", "setAddDialogOpen", "newUserEmail", "setNewUserEmail", "newAccessLevel", "setNewAccessLevel", "loading", "setLoading", "accessLevels", "access_levels", "isOwner", "access_level", "isHeadOwner", "is_head_owner", "getAccessLevelColor", "level", "getAccessLevelIcon", "handleAddAccess", "trim", "error", "console", "handleUpdateAccess", "accessId", "newLevel", "makeHeadOwner", "arguments", "length", "undefined", "handleRemoveAccess", "canManageUser", "access", "elevation", "sx", "p", "borderRadius", "border", "backgroundColor", "children", "display", "justifyContent", "alignItems", "mb", "variant", "fontWeight", "color", "fontFamily", "gap", "icon", "width", "height", "startIcon", "onClick", "textTransform", "map", "src", "user", "avatar", "alt", "first_name", "email", "mr", "bgcolor", "char<PERSON>t", "toUpperCase", "primary", "last_name", "label", "size", "fontSize", "secondary", "mt", "slice", "title", "id", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "autoFocus", "margin", "type", "value", "onChange", "e", "target", "disabled"], "sources": ["C:/ignition/ignition-ui/src/views/plan/detail/components/AccessManagement.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  Button,\n  Dialog,\n  DialogTitle,\n  DialogContent,\n  DialogActions,\n  TextField,\n  Select,\n  MenuItem,\n  FormControl,\n  InputLabel,\n  List,\n  ListItem,\n  ListItemText,\n  ListItemSecondaryAction,\n  IconButton,\n  Chip,\n  Avatar,\n  Tooltip\n} from '@mui/material';\nimport Iconify from 'components/Iconify/index';\nimport { mainYellowColor } from \"helpers/constants\";\nimport styles from '../styles.module.scss';\n\nconst AccessManagement = ({ \n  planInfo, \n  userAccessLevel,\n  onAddAccess,\n  onUpdateAccess,\n  onRemoveAccess \n}) => {\n  const [addDialogOpen, setAddDialogOpen] = useState(false);\n  const [newUserEmail, setNewUserEmail] = useState('');\n  const [newAccessLevel, setNewAccessLevel] = useState('viewer');\n  const [loading, setLoading] = useState(false);\n\n  const accessLevels = planInfo?.access_levels || [];\n  const isOwner = userAccessLevel?.access_level === 'owner';\n  const isHeadOwner = userAccessLevel?.is_head_owner;\n\n  const getAccessLevelColor = (level) => {\n    switch (level) {\n      case 'owner': return '#ef4444';\n      case 'editor': return '#f97316';\n      case 'viewer': return '#10b981';\n      default: return '#6b7280';\n    }\n  };\n\n  const getAccessLevelIcon = (level) => {\n    switch (level) {\n      case 'owner': return 'material-symbols:admin-panel-settings';\n      case 'editor': return 'material-symbols:edit';\n      case 'viewer': return 'material-symbols:visibility';\n      default: return 'material-symbols:person';\n    }\n  };\n\n  const handleAddAccess = async () => {\n    if (!newUserEmail.trim()) return;\n    \n    setLoading(true);\n    try {\n      await onAddAccess(newUserEmail, newAccessLevel);\n      setNewUserEmail('');\n      setNewAccessLevel('viewer');\n      setAddDialogOpen(false);\n    } catch (error) {\n      console.error('Error adding access:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleUpdateAccess = async (accessId, newLevel, makeHeadOwner = false) => {\n    try {\n      await onUpdateAccess(accessId, newLevel, makeHeadOwner);\n    } catch (error) {\n      console.error('Error updating access:', error);\n    }\n  };\n\n  const handleRemoveAccess = async (accessId) => {\n    try {\n      await onRemoveAccess(accessId);\n    } catch (error) {\n      console.error('Error removing access:', error);\n    }\n  };\n\n  const canManageUser = (access) => {\n    if (access.is_head_owner) return false; // Cannot manage head owner\n    if (access.access_level === 'owner' && !isHeadOwner) return false; // Only head owner can manage owners\n    return isOwner; // Owners can manage editors and viewers\n  };\n\n  return (\n    <Paper \n      elevation={0} \n      sx={{ \n        p: 3, \n        borderRadius: '12px',\n        border: '1px solid #f0f0f0',\n        backgroundColor: '#fff'\n      }}\n    >\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>\n        <Typography \n          variant=\"h6\" \n          sx={{ \n            fontWeight: 600, \n            color: '#333',\n            fontFamily: '\"Recursive Variable\", sans-serif',\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          }}\n        >\n          <Iconify icon=\"material-symbols:security\" width={24} height={24} color={mainYellowColor} />\n          Access Management\n        </Typography>\n        \n        {isOwner && (\n          <Button\n            variant=\"contained\"\n            startIcon={<Iconify icon=\"material-symbols:person-add\" />}\n            onClick={() => setAddDialogOpen(true)}\n            sx={{\n              backgroundColor: mainYellowColor,\n              color: '#fff',\n              textTransform: 'none',\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              '&:hover': {\n                backgroundColor: '#e6940a'\n              }\n            }}\n          >\n            Add Member\n          </Button>\n        )}\n      </Box>\n\n      <List>\n        {accessLevels.map((access) => (\n          <ListItem \n            key={access.id}\n            sx={{ \n              border: '1px solid #f0f0f0',\n              borderRadius: '8px',\n              mb: 1,\n              backgroundColor: '#fafafa'\n            }}\n          >\n            <Avatar\n              src={access.user.avatar}\n              alt={access.user.first_name || access.user.email}\n              sx={{ mr: 2, bgcolor: mainYellowColor }}\n            >\n              {access.user.first_name \n                ? access.user.first_name.charAt(0).toUpperCase()\n                : access.user.email.charAt(0).toUpperCase()\n              }\n            </Avatar>\n            \n            <ListItemText\n              primary={\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n                  <Typography \n                    variant=\"subtitle1\" \n                    sx={{ \n                      fontWeight: 600,\n                      fontFamily: '\"Recursive Variable\", sans-serif'\n                    }}\n                  >\n                    {access.user.first_name && access.user.last_name \n                      ? `${access.user.first_name} ${access.user.last_name}`\n                      : access.user.email\n                    }\n                  </Typography>\n                  {access.is_head_owner && (\n                    <Chip\n                      label=\"Head Owner\"\n                      size=\"small\"\n                      sx={{\n                        backgroundColor: '#fef3c7',\n                        color: '#92400e',\n                        fontWeight: 600,\n                        fontSize: '0.7rem'\n                      }}\n                    />\n                  )}\n                </Box>\n              }\n              secondary={\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>\n                  <Chip\n                    icon={<Iconify icon={getAccessLevelIcon(access.access_level)} width={16} height={16} />}\n                    label={access.access_level.charAt(0).toUpperCase() + access.access_level.slice(1)}\n                    size=\"small\"\n                    sx={{\n                      backgroundColor: `${getAccessLevelColor(access.access_level)}20`,\n                      color: getAccessLevelColor(access.access_level),\n                      fontWeight: 600,\n                      fontFamily: '\"Recursive Variable\", sans-serif'\n                    }}\n                  />\n                  <Typography \n                    variant=\"caption\" \n                    sx={{ \n                      color: '#666',\n                      fontFamily: '\"Recursive Variable\", sans-serif'\n                    }}\n                  >\n                    {access.user.email}\n                  </Typography>\n                </Box>\n              }\n            />\n            \n            <ListItemSecondaryAction>\n              {canManageUser(access) && (\n                <Box sx={{ display: 'flex', gap: 1 }}>\n                  {access.access_level === 'owner' && isHeadOwner && (\n                    <Tooltip title=\"Transfer Head Owner\">\n                      <IconButton\n                        size=\"small\"\n                        onClick={() => handleUpdateAccess(access.id, 'owner', true)}\n                        sx={{ color: '#f59e0b' }}\n                      >\n                        <Iconify icon=\"material-symbols:crown\" width={20} height={20} />\n                      </IconButton>\n                    </Tooltip>\n                  )}\n                  \n                  <Tooltip title=\"Remove Access\">\n                    <IconButton\n                      size=\"small\"\n                      onClick={() => handleRemoveAccess(access.id)}\n                      sx={{ color: '#ef4444' }}\n                    >\n                      <Iconify icon=\"material-symbols:person-remove\" width={20} height={20} />\n                    </IconButton>\n                  </Tooltip>\n                </Box>\n              )}\n            </ListItemSecondaryAction>\n          </ListItem>\n        ))}\n      </List>\n\n      {/* Add Access Dialog */}\n      <Dialog \n        open={addDialogOpen} \n        onClose={() => setAddDialogOpen(false)}\n        maxWidth=\"sm\"\n        fullWidth\n      >\n        <DialogTitle sx={{ fontFamily: '\"Recursive Variable\", sans-serif' }}>\n          Add Team Member\n        </DialogTitle>\n        <DialogContent>\n          <TextField\n            autoFocus\n            margin=\"dense\"\n            label=\"Email Address\"\n            type=\"email\"\n            fullWidth\n            variant=\"outlined\"\n            value={newUserEmail}\n            onChange={(e) => setNewUserEmail(e.target.value)}\n            sx={{ mb: 2 }}\n          />\n          \n          <FormControl fullWidth>\n            <InputLabel>Access Level</InputLabel>\n            <Select\n              value={newAccessLevel}\n              label=\"Access Level\"\n              onChange={(e) => setNewAccessLevel(e.target.value)}\n            >\n              <MenuItem value=\"viewer\">Viewer - Can view plan content</MenuItem>\n              <MenuItem value=\"editor\">Editor - Can edit plan content</MenuItem>\n              {isHeadOwner && (\n                <MenuItem value=\"owner\">Owner - Can manage team members</MenuItem>\n              )}\n            </Select>\n          </FormControl>\n        </DialogContent>\n        <DialogActions>\n          <Button \n            onClick={() => setAddDialogOpen(false)}\n            sx={{ fontFamily: '\"Recursive Variable\", sans-serif' }}\n          >\n            Cancel\n          </Button>\n          <Button \n            onClick={handleAddAccess}\n            variant=\"contained\"\n            disabled={loading || !newUserEmail.trim()}\n            sx={{\n              backgroundColor: mainYellowColor,\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              '&:hover': { backgroundColor: '#e6940a' }\n            }}\n          >\n            {loading ? 'Adding...' : 'Add Member'}\n          </Button>\n        </DialogActions>\n      </Dialog>\n    </Paper>\n  );\n};\n\nexport default AccessManagement;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OACEC,GAAG,CACHC,UAAU,CACVC,KAAK,CACLC,MAAM,CACNC,MAAM,CACNC,WAAW,CACXC,aAAa,CACbC,aAAa,CACbC,SAAS,CACTC,MAAM,CACNC,QAAQ,CACRC,WAAW,CACXC,UAAU,CACVC,IAAI,CACJC,QAAQ,CACRC,YAAY,CACZC,uBAAuB,CACvBC,UAAU,CACVC,IAAI,CACJC,MAAM,CACNC,OAAO,KACF,eAAe,CACtB,MAAO,CAAAC,OAAO,KAAM,0BAA0B,CAC9C,OAASC,eAAe,KAAQ,mBAAmB,CACnD,MAAO,CAAAC,MAAM,KAAM,uBAAuB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE3C,KAAM,CAAAC,gBAAgB,CAAGC,IAAA,EAMnB,IANoB,CACxBC,QAAQ,CACRC,eAAe,CACfC,WAAW,CACXC,cAAc,CACdC,cACF,CAAC,CAAAL,IAAA,CACC,KAAM,CAACM,aAAa,CAAEC,gBAAgB,CAAC,CAAGrC,QAAQ,CAAC,KAAK,CAAC,CACzD,KAAM,CAACsC,YAAY,CAAEC,eAAe,CAAC,CAAGvC,QAAQ,CAAC,EAAE,CAAC,CACpD,KAAM,CAACwC,cAAc,CAAEC,iBAAiB,CAAC,CAAGzC,QAAQ,CAAC,QAAQ,CAAC,CAC9D,KAAM,CAAC0C,OAAO,CAAEC,UAAU,CAAC,CAAG3C,QAAQ,CAAC,KAAK,CAAC,CAE7C,KAAM,CAAA4C,YAAY,CAAG,CAAAb,QAAQ,SAARA,QAAQ,iBAARA,QAAQ,CAAEc,aAAa,GAAI,EAAE,CAClD,KAAM,CAAAC,OAAO,CAAG,CAAAd,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEe,YAAY,IAAK,OAAO,CACzD,KAAM,CAAAC,WAAW,CAAGhB,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEiB,aAAa,CAElD,KAAM,CAAAC,mBAAmB,CAAIC,KAAK,EAAK,CACrC,OAAQA,KAAK,EACX,IAAK,OAAO,CAAE,MAAO,SAAS,CAC9B,IAAK,QAAQ,CAAE,MAAO,SAAS,CAC/B,IAAK,QAAQ,CAAE,MAAO,SAAS,CAC/B,QAAS,MAAO,SAAS,CAC3B,CACF,CAAC,CAED,KAAM,CAAAC,kBAAkB,CAAID,KAAK,EAAK,CACpC,OAAQA,KAAK,EACX,IAAK,OAAO,CAAE,MAAO,uCAAuC,CAC5D,IAAK,QAAQ,CAAE,MAAO,uBAAuB,CAC7C,IAAK,QAAQ,CAAE,MAAO,6BAA6B,CACnD,QAAS,MAAO,yBAAyB,CAC3C,CACF,CAAC,CAED,KAAM,CAAAE,eAAe,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CAACf,YAAY,CAACgB,IAAI,CAAC,CAAC,CAAE,OAE1BX,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAV,WAAW,CAACK,YAAY,CAAEE,cAAc,CAAC,CAC/CD,eAAe,CAAC,EAAE,CAAC,CACnBE,iBAAiB,CAAC,QAAQ,CAAC,CAC3BJ,gBAAgB,CAAC,KAAK,CAAC,CACzB,CAAE,MAAOkB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,CAAEA,KAAK,CAAC,CAC9C,CAAC,OAAS,CACRZ,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAc,kBAAkB,CAAG,cAAAA,CAAOC,QAAQ,CAAEC,QAAQ,CAA4B,IAA1B,CAAAC,aAAa,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,CACzE,GAAI,CACF,KAAM,CAAA3B,cAAc,CAACwB,QAAQ,CAAEC,QAAQ,CAAEC,aAAa,CAAC,CACzD,CAAE,MAAOL,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAChD,CACF,CAAC,CAED,KAAM,CAAAS,kBAAkB,CAAG,KAAO,CAAAN,QAAQ,EAAK,CAC7C,GAAI,CACF,KAAM,CAAAvB,cAAc,CAACuB,QAAQ,CAAC,CAChC,CAAE,MAAOH,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAChD,CACF,CAAC,CAED,KAAM,CAAAU,aAAa,CAAIC,MAAM,EAAK,CAChC,GAAIA,MAAM,CAACjB,aAAa,CAAE,MAAO,MAAK,CAAE;AACxC,GAAIiB,MAAM,CAACnB,YAAY,GAAK,OAAO,EAAI,CAACC,WAAW,CAAE,MAAO,MAAK,CAAE;AACnE,MAAO,CAAAF,OAAO,CAAE;AAClB,CAAC,CAED,mBACElB,KAAA,CAACzB,KAAK,EACJgE,SAAS,CAAE,CAAE,CACbC,EAAE,CAAE,CACFC,CAAC,CAAE,CAAC,CACJC,YAAY,CAAE,MAAM,CACpBC,MAAM,CAAE,mBAAmB,CAC3BC,eAAe,CAAE,MACnB,CAAE,CAAAC,QAAA,eAEF7C,KAAA,CAAC3B,GAAG,EAACmE,EAAE,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,QAAQ,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,eACzF7C,KAAA,CAAC1B,UAAU,EACT4E,OAAO,CAAC,IAAI,CACZV,EAAE,CAAE,CACFW,UAAU,CAAE,GAAG,CACfC,KAAK,CAAE,MAAM,CACbC,UAAU,CAAE,kCAAkC,CAC9CP,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBM,GAAG,CAAE,CACP,CAAE,CAAAT,QAAA,eAEF/C,IAAA,CAACJ,OAAO,EAAC6D,IAAI,CAAC,2BAA2B,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAACL,KAAK,CAAEzD,eAAgB,CAAE,CAAC,oBAE7F,EAAY,CAAC,CAEZuB,OAAO,eACNpB,IAAA,CAACtB,MAAM,EACL0E,OAAO,CAAC,WAAW,CACnBQ,SAAS,cAAE5D,IAAA,CAACJ,OAAO,EAAC6D,IAAI,CAAC,6BAA6B,CAAE,CAAE,CAC1DI,OAAO,CAAEA,CAAA,GAAMlD,gBAAgB,CAAC,IAAI,CAAE,CACtC+B,EAAE,CAAE,CACFI,eAAe,CAAEjD,eAAe,CAChCyD,KAAK,CAAE,MAAM,CACbQ,aAAa,CAAE,MAAM,CACrBP,UAAU,CAAE,kCAAkC,CAC9C,SAAS,CAAE,CACTT,eAAe,CAAE,SACnB,CACF,CAAE,CAAAC,QAAA,CACH,YAED,CAAQ,CACT,EACE,CAAC,cAEN/C,IAAA,CAACZ,IAAI,EAAA2D,QAAA,CACF7B,YAAY,CAAC6C,GAAG,CAAEvB,MAAM,eACvBtC,KAAA,CAACb,QAAQ,EAEPqD,EAAE,CAAE,CACFG,MAAM,CAAE,mBAAmB,CAC3BD,YAAY,CAAE,KAAK,CACnBO,EAAE,CAAE,CAAC,CACLL,eAAe,CAAE,SACnB,CAAE,CAAAC,QAAA,eAEF/C,IAAA,CAACN,MAAM,EACLsE,GAAG,CAAExB,MAAM,CAACyB,IAAI,CAACC,MAAO,CACxBC,GAAG,CAAE3B,MAAM,CAACyB,IAAI,CAACG,UAAU,EAAI5B,MAAM,CAACyB,IAAI,CAACI,KAAM,CACjD3B,EAAE,CAAE,CAAE4B,EAAE,CAAE,CAAC,CAAEC,OAAO,CAAE1E,eAAgB,CAAE,CAAAkD,QAAA,CAEvCP,MAAM,CAACyB,IAAI,CAACG,UAAU,CACnB5B,MAAM,CAACyB,IAAI,CAACG,UAAU,CAACI,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAC9CjC,MAAM,CAACyB,IAAI,CAACI,KAAK,CAACG,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAEvC,CAAC,cAETzE,IAAA,CAACV,YAAY,EACXoF,OAAO,cACLxE,KAAA,CAAC3B,GAAG,EAACmE,EAAE,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEM,GAAG,CAAE,CAAE,CAAE,CAAAT,QAAA,eACzD/C,IAAA,CAACxB,UAAU,EACT4E,OAAO,CAAC,WAAW,CACnBV,EAAE,CAAE,CACFW,UAAU,CAAE,GAAG,CACfE,UAAU,CAAE,kCACd,CAAE,CAAAR,QAAA,CAEDP,MAAM,CAACyB,IAAI,CAACG,UAAU,EAAI5B,MAAM,CAACyB,IAAI,CAACU,SAAS,CAC5C,GAAGnC,MAAM,CAACyB,IAAI,CAACG,UAAU,IAAI5B,MAAM,CAACyB,IAAI,CAACU,SAAS,EAAE,CACpDnC,MAAM,CAACyB,IAAI,CAACI,KAAK,CAEX,CAAC,CACZ7B,MAAM,CAACjB,aAAa,eACnBvB,IAAA,CAACP,IAAI,EACHmF,KAAK,CAAC,YAAY,CAClBC,IAAI,CAAC,OAAO,CACZnC,EAAE,CAAE,CACFI,eAAe,CAAE,SAAS,CAC1BQ,KAAK,CAAE,SAAS,CAChBD,UAAU,CAAE,GAAG,CACfyB,QAAQ,CAAE,QACZ,CAAE,CACH,CACF,EACE,CACN,CACDC,SAAS,cACP7E,KAAA,CAAC3B,GAAG,EAACmE,EAAE,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEM,GAAG,CAAE,CAAC,CAAEwB,EAAE,CAAE,GAAI,CAAE,CAAAjC,QAAA,eAClE/C,IAAA,CAACP,IAAI,EACHgE,IAAI,cAAEzD,IAAA,CAACJ,OAAO,EAAC6D,IAAI,CAAE/B,kBAAkB,CAACc,MAAM,CAACnB,YAAY,CAAE,CAACqC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAAE,CAAE,CACxFiB,KAAK,CAAEpC,MAAM,CAACnB,YAAY,CAACmD,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAGjC,MAAM,CAACnB,YAAY,CAAC4D,KAAK,CAAC,CAAC,CAAE,CAClFJ,IAAI,CAAC,OAAO,CACZnC,EAAE,CAAE,CACFI,eAAe,CAAE,GAAGtB,mBAAmB,CAACgB,MAAM,CAACnB,YAAY,CAAC,IAAI,CAChEiC,KAAK,CAAE9B,mBAAmB,CAACgB,MAAM,CAACnB,YAAY,CAAC,CAC/CgC,UAAU,CAAE,GAAG,CACfE,UAAU,CAAE,kCACd,CAAE,CACH,CAAC,cACFvD,IAAA,CAACxB,UAAU,EACT4E,OAAO,CAAC,SAAS,CACjBV,EAAE,CAAE,CACFY,KAAK,CAAE,MAAM,CACbC,UAAU,CAAE,kCACd,CAAE,CAAAR,QAAA,CAEDP,MAAM,CAACyB,IAAI,CAACI,KAAK,CACR,CAAC,EACV,CACN,CACF,CAAC,cAEFrE,IAAA,CAACT,uBAAuB,EAAAwD,QAAA,CACrBR,aAAa,CAACC,MAAM,CAAC,eACpBtC,KAAA,CAAC3B,GAAG,EAACmE,EAAE,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEQ,GAAG,CAAE,CAAE,CAAE,CAAAT,QAAA,EAClCP,MAAM,CAACnB,YAAY,GAAK,OAAO,EAAIC,WAAW,eAC7CtB,IAAA,CAACL,OAAO,EAACuF,KAAK,CAAC,qBAAqB,CAAAnC,QAAA,cAClC/C,IAAA,CAACR,UAAU,EACTqF,IAAI,CAAC,OAAO,CACZhB,OAAO,CAAEA,CAAA,GAAM9B,kBAAkB,CAACS,MAAM,CAAC2C,EAAE,CAAE,OAAO,CAAE,IAAI,CAAE,CAC5DzC,EAAE,CAAE,CAAEY,KAAK,CAAE,SAAU,CAAE,CAAAP,QAAA,cAEzB/C,IAAA,CAACJ,OAAO,EAAC6D,IAAI,CAAC,wBAAwB,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAAE,CAAC,CACtD,CAAC,CACN,CACV,cAED3D,IAAA,CAACL,OAAO,EAACuF,KAAK,CAAC,eAAe,CAAAnC,QAAA,cAC5B/C,IAAA,CAACR,UAAU,EACTqF,IAAI,CAAC,OAAO,CACZhB,OAAO,CAAEA,CAAA,GAAMvB,kBAAkB,CAACE,MAAM,CAAC2C,EAAE,CAAE,CAC7CzC,EAAE,CAAE,CAAEY,KAAK,CAAE,SAAU,CAAE,CAAAP,QAAA,cAEzB/C,IAAA,CAACJ,OAAO,EAAC6D,IAAI,CAAC,gCAAgC,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAAE,CAAC,CAC9D,CAAC,CACN,CAAC,EACP,CACN,CACsB,CAAC,GApGrBnB,MAAM,CAAC2C,EAqGJ,CACX,CAAC,CACE,CAAC,cAGPjF,KAAA,CAACvB,MAAM,EACLyG,IAAI,CAAE1E,aAAc,CACpB2E,OAAO,CAAEA,CAAA,GAAM1E,gBAAgB,CAAC,KAAK,CAAE,CACvC2E,QAAQ,CAAC,IAAI,CACbC,SAAS,MAAAxC,QAAA,eAET/C,IAAA,CAACpB,WAAW,EAAC8D,EAAE,CAAE,CAAEa,UAAU,CAAE,kCAAmC,CAAE,CAAAR,QAAA,CAAC,iBAErE,CAAa,CAAC,cACd7C,KAAA,CAACrB,aAAa,EAAAkE,QAAA,eACZ/C,IAAA,CAACjB,SAAS,EACRyG,SAAS,MACTC,MAAM,CAAC,OAAO,CACdb,KAAK,CAAC,eAAe,CACrBc,IAAI,CAAC,OAAO,CACZH,SAAS,MACTnC,OAAO,CAAC,UAAU,CAClBuC,KAAK,CAAE/E,YAAa,CACpBgF,QAAQ,CAAGC,CAAC,EAAKhF,eAAe,CAACgF,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CACjDjD,EAAE,CAAE,CAAES,EAAE,CAAE,CAAE,CAAE,CACf,CAAC,cAEFjD,KAAA,CAAChB,WAAW,EAACqG,SAAS,MAAAxC,QAAA,eACpB/C,IAAA,CAACb,UAAU,EAAA4D,QAAA,CAAC,cAAY,CAAY,CAAC,cACrC7C,KAAA,CAAClB,MAAM,EACL2G,KAAK,CAAE7E,cAAe,CACtB8D,KAAK,CAAC,cAAc,CACpBgB,QAAQ,CAAGC,CAAC,EAAK9E,iBAAiB,CAAC8E,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE,CAAA5C,QAAA,eAEnD/C,IAAA,CAACf,QAAQ,EAAC0G,KAAK,CAAC,QAAQ,CAAA5C,QAAA,CAAC,gCAA8B,CAAU,CAAC,cAClE/C,IAAA,CAACf,QAAQ,EAAC0G,KAAK,CAAC,QAAQ,CAAA5C,QAAA,CAAC,gCAA8B,CAAU,CAAC,CACjEzB,WAAW,eACVtB,IAAA,CAACf,QAAQ,EAAC0G,KAAK,CAAC,OAAO,CAAA5C,QAAA,CAAC,iCAA+B,CAAU,CAClE,EACK,CAAC,EACE,CAAC,EACD,CAAC,cAChB7C,KAAA,CAACpB,aAAa,EAAAiE,QAAA,eACZ/C,IAAA,CAACtB,MAAM,EACLmF,OAAO,CAAEA,CAAA,GAAMlD,gBAAgB,CAAC,KAAK,CAAE,CACvC+B,EAAE,CAAE,CAAEa,UAAU,CAAE,kCAAmC,CAAE,CAAAR,QAAA,CACxD,QAED,CAAQ,CAAC,cACT/C,IAAA,CAACtB,MAAM,EACLmF,OAAO,CAAElC,eAAgB,CACzByB,OAAO,CAAC,WAAW,CACnB2C,QAAQ,CAAE/E,OAAO,EAAI,CAACJ,YAAY,CAACgB,IAAI,CAAC,CAAE,CAC1Cc,EAAE,CAAE,CACFI,eAAe,CAAEjD,eAAe,CAChC0D,UAAU,CAAE,kCAAkC,CAC9C,SAAS,CAAE,CAAET,eAAe,CAAE,SAAU,CAC1C,CAAE,CAAAC,QAAA,CAED/B,OAAO,CAAG,WAAW,CAAG,YAAY,CAC/B,CAAC,EACI,CAAC,EACV,CAAC,EACJ,CAAC,CAEZ,CAAC,CAED,cAAe,CAAAb,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}