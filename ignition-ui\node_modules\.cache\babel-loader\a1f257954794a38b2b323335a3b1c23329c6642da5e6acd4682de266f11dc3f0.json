{"ast": null, "code": "var _jsxFileName = \"C:\\\\ignition\\\\ignition-ui\\\\src\\\\views\\\\plan\\\\detail\\\\components\\\\MilestoneOverview.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Box, Typography, Card, CardContent, Chip, LinearProgress, Collapse, Tooltip, Divider } from '@mui/material';\nimport Iconify from 'components/Iconify/index';\nimport { mainYellowColor } from \"helpers/constants\";\nimport dayjs from 'dayjs';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst MilestoneOverview = _ref => {\n  _s();\n  let {\n    milestones,\n    calculateMilestoneProgress,\n    getMilestoneStatus,\n    calculateTaskProgress,\n    getTaskStatus,\n    calculateSubtaskProgress,\n    getSubtaskStatus\n  } = _ref;\n  const [expandedMilestones, setExpandedMilestones] = useState({});\n  const toggleMilestone = milestoneId => {\n    setExpandedMilestones(prev => ({\n      ...prev,\n      [milestoneId]: !prev[milestoneId]\n    }));\n  };\n  if (!milestones || milestones.length === 0) {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        textAlign: 'center',\n        py: 4,\n        backgroundColor: '#f9f9f9',\n        borderRadius: '12px',\n        border: '1px dashed #ddd'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Iconify, {\n        icon: \"material-symbols:flag\",\n        width: 48,\n        height: 48,\n        sx: {\n          color: '#999',\n          mb: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        sx: {\n          color: '#666',\n          fontFamily: '\"Recursive Variable\", sans-serif',\n          fontSize: '0.9rem'\n        },\n        children: \"No milestones found\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex',\n      flexDirection: 'column',\n      gap: 2\n    },\n    children: milestones.map((milestone, index) => {\n      var _milestone$tasks, _milestone$tasks2;\n      const milestoneId = milestone.id || `milestone-${index}`;\n      const isExpanded = expandedMilestones[milestoneId] !== false; // Default to expanded\n      const progress = calculateMilestoneProgress(milestone);\n      const status = getMilestoneStatus(milestone);\n      const completedTasks = ((_milestone$tasks = milestone.tasks) === null || _milestone$tasks === void 0 ? void 0 : _milestone$tasks.filter(task => getTaskStatus(task).label === 'Completed').length) || 0;\n      const totalTasks = ((_milestone$tasks2 = milestone.tasks) === null || _milestone$tasks2 === void 0 ? void 0 : _milestone$tasks2.length) || 0;\n      const daysLeft = milestone.end_date ? dayjs(milestone.end_date).diff(dayjs(), 'day') : null;\n      const isOverdue = daysLeft !== null && daysLeft < 0;\n      return /*#__PURE__*/_jsxDEV(Card, {\n        elevation: 0,\n        sx: {\n          borderRadius: '12px',\n          border: '1px solid #f0f0f0',\n          backgroundColor: '#fff',\n          transition: 'all 0.2s ease',\n          overflow: 'visible',\n          '&:hover': {\n            borderColor: '#e0e0e0',\n            boxShadow: '0 2px 8px rgba(0,0,0,0.05)'\n          }\n        },\n        children: /*#__PURE__*/_jsxDEV(CardContent, {\n          sx: {\n            p: '0 !important'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              p: 2.5,\n              cursor: 'pointer',\n              borderBottom: isExpanded ? '1px solid #f0f0f0' : 'none',\n              borderTopLeftRadius: '12px',\n              borderTopRightRadius: '12px',\n              backgroundColor: isExpanded ? '#fff' : '#fafafa',\n              transition: 'background-color 0.2s ease'\n            },\n            onClick: () => toggleMilestone(milestoneId),\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                width: 16,\n                height: 16,\n                borderRadius: '50%',\n                backgroundColor: status.color,\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                mr: 2,\n                flexShrink: 0\n              },\n              children: /*#__PURE__*/_jsxDEV(Iconify, {\n                icon: isExpanded ? \"material-symbols:keyboard-arrow-down\" : \"material-symbols:keyboard-arrow-right\",\n                width: 14,\n                height: 14,\n                sx: {\n                  color: '#fff'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 106,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 93,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                flexGrow: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"subtitle1\",\n                sx: {\n                  fontWeight: 600,\n                  color: '#333',\n                  fontFamily: '\"Recursive Variable\", sans-serif',\n                  fontSize: '1rem',\n                  lineHeight: 1.3\n                },\n                children: milestone.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 115,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 2,\n                flexShrink: 0\n              },\n              children: [daysLeft !== null && /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: isOverdue ? \"Overdue\" : \"Days remaining\",\n                children: /*#__PURE__*/_jsxDEV(Chip, {\n                  icon: /*#__PURE__*/_jsxDEV(Iconify, {\n                    icon: \"material-symbols:calendar-today\",\n                    width: 14,\n                    height: 14\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 133,\n                    columnNumber: 31\n                  }, this),\n                  label: isOverdue ? `${Math.abs(daysLeft)} days overdue` : `${daysLeft} days left`,\n                  size: \"small\",\n                  sx: {\n                    height: '22px',\n                    fontSize: '0.75rem',\n                    backgroundColor: isOverdue ? '#ffebee' : '#e8f5e9',\n                    color: isOverdue ? '#d32f2f' : '#2e7d32',\n                    fontWeight: 500,\n                    fontFamily: '\"Recursive Variable\", sans-serif'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 132,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 131,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                label: `${progress}%`,\n                size: \"small\",\n                sx: {\n                  height: '22px',\n                  fontSize: '0.75rem',\n                  backgroundColor: progress === 100 ? '#e8f5e9' : `${status.color}15`,\n                  color: progress === 100 ? '#2e7d32' : status.color,\n                  fontWeight: 600,\n                  fontFamily: '\"Recursive Variable\", sans-serif',\n                  minWidth: 45\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 148,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n            in: isExpanded,\n            children: /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                p: 2.5,\n                pt: 2\n              },\n              children: [/*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  flexWrap: 'wrap',\n                  gap: 2,\n                  mb: 2,\n                  alignItems: 'center'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Chip, {\n                  icon: /*#__PURE__*/_jsxDEV(Iconify, {\n                    icon: \"material-symbols:flag\",\n                    width: 14,\n                    height: 14\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 171,\n                    columnNumber: 29\n                  }, this),\n                  label: status.label,\n                  size: \"small\",\n                  sx: {\n                    height: '22px',\n                    fontSize: '0.75rem',\n                    backgroundColor: `${status.color}15`,\n                    color: status.color,\n                    fontWeight: 600,\n                    fontFamily: '\"Recursive Variable\", sans-serif'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 21\n                }, this), milestone.start_date && milestone.end_date && /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    color: '#666',\n                    fontSize: '0.8rem',\n                    fontFamily: '\"Recursive Variable\", sans-serif',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 0.5\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Iconify, {\n                    icon: \"material-symbols:calendar-month\",\n                    width: 16,\n                    height: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 196,\n                    columnNumber: 25\n                  }, this), dayjs(milestone.start_date).format('MMM D'), \" - \", dayjs(milestone.end_date).format('MMM D, YYYY')]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 23\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    color: '#666',\n                    fontSize: '0.8rem',\n                    fontFamily: '\"Recursive Variable\", sans-serif',\n                    display: 'flex',\n                    alignItems: 'center',\n                    gap: 0.5\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Iconify, {\n                    icon: \"material-symbols:task\",\n                    width: 16,\n                    height: 16\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 212,\n                    columnNumber: 23\n                  }, this), completedTasks, \"/\", totalTasks, \" tasks completed\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  mb: 3\n                },\n                children: /*#__PURE__*/_jsxDEV(LinearProgress, {\n                  variant: \"determinate\",\n                  value: progress,\n                  sx: {\n                    height: 6,\n                    borderRadius: 3,\n                    backgroundColor: '#f0f0f0',\n                    '& .MuiLinearProgress-bar': {\n                      backgroundColor: progress === 100 ? '#4CAF50' : mainYellowColor\n                    }\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 19\n              }, this), milestone.tasks && milestone.tasks.length > 0 ? /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  flexDirection: 'column',\n                  gap: 1.5\n                },\n                children: milestone.tasks.map((task, taskIndex) => {\n                  var _task$subtasks;\n                  const taskProgress = calculateTaskProgress(task);\n                  const taskStatus = getTaskStatus(task);\n                  const hasSubtasks = task.subtasks && task.subtasks.length > 0;\n                  const completedSubtasks = ((_task$subtasks = task.subtasks) === null || _task$subtasks === void 0 ? void 0 : _task$subtasks.filter(s => getSubtaskStatus(s).label === 'Completed').length) || 0;\n                  const isTaskCompleted = taskStatus.label === 'Completed';\n                  return /*#__PURE__*/_jsxDEV(Box, {\n                    sx: {\n                      display: 'flex',\n                      flexDirection: 'column',\n                      gap: 1,\n                      backgroundColor: isTaskCompleted ? '#f8fff8' : '#fafafa',\n                      borderRadius: '8px',\n                      p: 1.5,\n                      border: `1px solid ${isTaskCompleted ? '#e6f7e6' : '#f0f0f0'}`,\n                      transition: 'all 0.2s ease',\n                      '&:hover': {\n                        borderColor: isTaskCompleted ? '#c8e6c9' : '#e0e0e0',\n                        backgroundColor: isTaskCompleted ? '#f0fff0' : '#f5f5f5',\n                        boxShadow: '0 1px 4px rgba(0,0,0,0.03)'\n                      }\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Box, {\n                      sx: {\n                        display: 'flex',\n                        alignItems: 'center',\n                        gap: 1.5\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          width: 10,\n                          height: 10,\n                          borderRadius: '50%',\n                          backgroundColor: taskStatus.color,\n                          flexShrink: 0\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 270,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                        variant: \"body2\",\n                        sx: {\n                          flexGrow: 1,\n                          color: isTaskCompleted ? '#4CAF50' : '#333',\n                          fontFamily: '\"Recursive Variable\", sans-serif',\n                          fontSize: '0.875rem',\n                          fontWeight: 500,\n                          lineHeight: 1.3\n                        },\n                        children: task.name\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 279,\n                        columnNumber: 31\n                      }, this), /*#__PURE__*/_jsxDEV(Box, {\n                        sx: {\n                          display: 'flex',\n                          alignItems: 'center',\n                          gap: 1.5\n                        },\n                        children: [hasSubtasks && /*#__PURE__*/_jsxDEV(Tooltip, {\n                          title: `${completedSubtasks} of ${task.subtasks.length} subtasks completed`,\n                          children: /*#__PURE__*/_jsxDEV(Typography, {\n                            variant: \"caption\",\n                            sx: {\n                              fontSize: '0.75rem',\n                              color: completedSubtasks === task.subtasks.length ? '#2e7d32' : '#666',\n                              fontWeight: 500,\n                              fontFamily: '\"Recursive Variable\", sans-serif',\n                              backgroundColor: completedSubtasks === task.subtasks.length ? '#e8f5e9' : '#f5f5f5',\n                              px: 1,\n                              py: 0.5,\n                              borderRadius: '4px',\n                              border: `1px solid ${completedSubtasks === task.subtasks.length ? '#c8e6c9' : '#e0e0e0'}`\n                            },\n                            children: [completedSubtasks, \"/\", task.subtasks.length, \" subtasks completed\"]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 295,\n                            columnNumber: 37\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 294,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                          title: `${taskProgress}% completed`,\n                          children: /*#__PURE__*/_jsxDEV(Box, {\n                            sx: {\n                              position: 'relative',\n                              width: 28,\n                              height: 28\n                            },\n                            children: [/*#__PURE__*/_jsxDEV(Box, {\n                              sx: {\n                                position: 'absolute',\n                                top: 0,\n                                left: 0,\n                                width: 28,\n                                height: 28,\n                                borderRadius: '50%',\n                                backgroundColor: '#f0f0f0'\n                              }\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 315,\n                              columnNumber: 37\n                            }, this), /*#__PURE__*/_jsxDEV(Box, {\n                              sx: {\n                                position: 'absolute',\n                                top: 0,\n                                left: 0,\n                                width: 28,\n                                height: 28,\n                                borderRadius: '50%',\n                                background: `conic-gradient(${taskProgress === 100 ? '#4CAF50' : taskStatus.color} ${taskProgress}%, transparent 0)`,\n                                display: 'flex',\n                                alignItems: 'center',\n                                justifyContent: 'center'\n                              },\n                              children: /*#__PURE__*/_jsxDEV(Box, {\n                                sx: {\n                                  width: 20,\n                                  height: 20,\n                                  borderRadius: '50%',\n                                  backgroundColor: '#fff',\n                                  display: 'flex',\n                                  alignItems: 'center',\n                                  justifyContent: 'center'\n                                },\n                                children: taskProgress === 100 ? /*#__PURE__*/_jsxDEV(Iconify, {\n                                  icon: \"material-symbols:check-small\",\n                                  width: 14,\n                                  height: 14,\n                                  sx: {\n                                    color: '#4CAF50'\n                                  }\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 352,\n                                  columnNumber: 43\n                                }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n                                  variant: \"caption\",\n                                  sx: {\n                                    fontSize: '0.65rem',\n                                    fontWeight: 600,\n                                    color: taskProgress > 0 ? taskProgress >= 50 ? '#4CAF50' : mainYellowColor : '#999',\n                                    lineHeight: 1\n                                  },\n                                  children: [taskProgress, \"%\"]\n                                }, void 0, true, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 354,\n                                  columnNumber: 43\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 340,\n                                columnNumber: 39\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 326,\n                              columnNumber: 37\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 314,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 313,\n                          columnNumber: 33\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 292,\n                        columnNumber: 31\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 269,\n                      columnNumber: 29\n                    }, this)\n                  }, task.id || taskIndex, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 250,\n                    columnNumber: 27\n                  }, this);\n                })\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 235,\n                columnNumber: 21\n              }, this) : /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  textAlign: 'center',\n                  py: 2,\n                  color: '#999'\n                },\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  children: \"No tasks in this milestone\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 377,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 13\n        }, this)\n      }, milestoneId, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 11\n      }, this);\n    })\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 5\n  }, this);\n};\n_s(MilestoneOverview, \"Ee2gFwRpHMLeeRisHZZLFFd81Pg=\");\n_c = MilestoneOverview;\nexport default MilestoneOverview;\nvar _c;\n$RefreshReg$(_c, \"MilestoneOverview\");", "map": {"version": 3, "names": ["React", "useState", "Box", "Typography", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Chip", "LinearProgress", "Collapse", "<PERSON><PERSON><PERSON>", "Divider", "Iconify", "mainYellowColor", "dayjs", "jsxDEV", "_jsxDEV", "MilestoneOverview", "_ref", "_s", "milestones", "calculateMilestoneProgress", "getMilestoneStatus", "calculateTaskProgress", "getTaskStatus", "calculateSubtaskProgress", "getSubtaskStatus", "expandedMilestones", "setExpandedMilestones", "toggleMilestone", "milestoneId", "prev", "length", "sx", "textAlign", "py", "backgroundColor", "borderRadius", "border", "children", "icon", "width", "height", "color", "mb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontFamily", "fontSize", "display", "flexDirection", "gap", "map", "milestone", "index", "_milestone$tasks", "_milestone$tasks2", "id", "isExpanded", "progress", "status", "completedTasks", "tasks", "filter", "task", "label", "totalTasks", "daysLeft", "end_date", "diff", "isOverdue", "elevation", "transition", "overflow", "borderColor", "boxShadow", "p", "alignItems", "cursor", "borderBottom", "borderTopLeftRadius", "borderTopRightRadius", "onClick", "justifyContent", "mr", "flexShrink", "flexGrow", "fontWeight", "lineHeight", "name", "title", "Math", "abs", "size", "min<PERSON><PERSON><PERSON>", "in", "pt", "flexWrap", "start_date", "format", "value", "taskIndex", "_task$subtasks", "taskProgress", "taskStatus", "hasSubtasks", "subtasks", "completedSubtasks", "s", "isTaskCompleted", "px", "position", "top", "left", "background", "_c", "$RefreshReg$"], "sources": ["C:/ignition/ignition-ui/src/views/plan/detail/components/MilestoneOverview.js"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { Box, Typo<PERSON>, Card, CardContent, Chip, LinearProgress, Collapse, Tooltip, Divider } from '@mui/material';\r\nimport Iconify from 'components/Iconify/index';\r\nimport { mainYellowColor } from \"helpers/constants\";\r\nimport dayjs from 'dayjs';\r\n\r\nconst MilestoneOverview = ({ \r\n  milestones,\r\n  calculateMilestoneProgress,\r\n  getMilestoneStatus,\r\n  calculateTaskProgress,\r\n  getTaskStatus,\r\n  calculateSubtaskProgress,\r\n  getSubtaskStatus\r\n}) => {\r\n  const [expandedMilestones, setExpandedMilestones] = useState({});\r\n\r\n  const toggleMilestone = (milestoneId) => {\r\n    setExpandedMilestones(prev => ({\r\n      ...prev,\r\n      [milestoneId]: !prev[milestoneId]\r\n    }));\r\n  };\r\n\r\n  if (!milestones || milestones.length === 0) {\r\n    return (\r\n      <Box \r\n        sx={{ \r\n          textAlign: 'center', \r\n          py: 4, \r\n          backgroundColor: '#f9f9f9',\r\n          borderRadius: '12px',\r\n          border: '1px dashed #ddd'\r\n        }}\r\n      >\r\n        <Iconify \r\n          icon=\"material-symbols:flag\" \r\n          width={48} \r\n          height={48} \r\n          sx={{ color: '#999', mb: 2 }} \r\n        />\r\n        <Typography variant=\"body1\" sx={{ color: '#666', fontFamily: '\"Recursive Variable\", sans-serif', fontSize: '0.9rem' }}>\r\n          No milestones found\r\n        </Typography>\r\n      </Box>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>\r\n      {milestones.map((milestone, index) => {\r\n        const milestoneId = milestone.id || `milestone-${index}`;\r\n        const isExpanded = expandedMilestones[milestoneId] !== false; // Default to expanded\r\n        const progress = calculateMilestoneProgress(milestone);\r\n        const status = getMilestoneStatus(milestone);\r\n        const completedTasks = milestone.tasks?.filter(task => getTaskStatus(task).label === 'Completed').length || 0;\r\n        const totalTasks = milestone.tasks?.length || 0;\r\n        const daysLeft = milestone.end_date ? dayjs(milestone.end_date).diff(dayjs(), 'day') : null;\r\n        const isOverdue = daysLeft !== null && daysLeft < 0;\r\n\r\n        return (\r\n          <Card \r\n            key={milestoneId}\r\n            elevation={0}\r\n            sx={{ \r\n              borderRadius: '12px',\r\n              border: '1px solid #f0f0f0',\r\n              backgroundColor: '#fff',\r\n              transition: 'all 0.2s ease',\r\n              overflow: 'visible',\r\n              '&:hover': {\r\n                borderColor: '#e0e0e0',\r\n                boxShadow: '0 2px 8px rgba(0,0,0,0.05)'\r\n              }\r\n            }}\r\n          >\r\n            <CardContent sx={{ p: '0 !important' }}>\r\n              {/* Milestone Header - Always visible */}\r\n              <Box \r\n                sx={{ \r\n                  display: 'flex', \r\n                  alignItems: 'center', \r\n                  p: 2.5,\r\n                  cursor: 'pointer',\r\n                  borderBottom: isExpanded ? '1px solid #f0f0f0' : 'none',\r\n                  borderTopLeftRadius: '12px',\r\n                  borderTopRightRadius: '12px',\r\n                  backgroundColor: isExpanded ? '#fff' : '#fafafa',\r\n                  transition: 'background-color 0.2s ease'\r\n                }}\r\n                onClick={() => toggleMilestone(milestoneId)}\r\n              >\r\n                <Box \r\n                  sx={{ \r\n                    width: 16, \r\n                    height: 16, \r\n                    borderRadius: '50%', \r\n                    backgroundColor: status.color,\r\n                    display: 'flex',\r\n                    alignItems: 'center',\r\n                    justifyContent: 'center',\r\n                    mr: 2,\r\n                    flexShrink: 0\r\n                  }}\r\n                >\r\n                  <Iconify \r\n                    icon={isExpanded ? \"material-symbols:keyboard-arrow-down\" : \"material-symbols:keyboard-arrow-right\"} \r\n                    width={14} \r\n                    height={14} \r\n                    sx={{ color: '#fff' }} \r\n                  />\r\n                </Box>\r\n                \r\n                <Box sx={{ flexGrow: 1 }}>\r\n                  <Typography \r\n                    variant=\"subtitle1\" \r\n                    sx={{ \r\n                      fontWeight: 600,\r\n                      color: '#333',\r\n                      fontFamily: '\"Recursive Variable\", sans-serif',\r\n                      fontSize: '1rem',\r\n                      lineHeight: 1.3\r\n                    }}\r\n                  >\r\n                    {milestone.name}\r\n                  </Typography>\r\n                </Box>\r\n                \r\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexShrink: 0 }}>\r\n                  {daysLeft !== null && (\r\n                    <Tooltip title={isOverdue ? \"Overdue\" : \"Days remaining\"}>\r\n                      <Chip\r\n                        icon={<Iconify icon=\"material-symbols:calendar-today\" width={14} height={14} />}\r\n                        label={isOverdue ? `${Math.abs(daysLeft)} days overdue` : `${daysLeft} days left`}\r\n                        size=\"small\"\r\n                        sx={{\r\n                          height: '22px',\r\n                          fontSize: '0.75rem',\r\n                          backgroundColor: isOverdue ? '#ffebee' : '#e8f5e9',\r\n                          color: isOverdue ? '#d32f2f' : '#2e7d32',\r\n                          fontWeight: 500,\r\n                          fontFamily: '\"Recursive Variable\", sans-serif'\r\n                        }}\r\n                      />\r\n                    </Tooltip>\r\n                  )}\r\n                  \r\n                  <Chip \r\n                    label={`${progress}%`}\r\n                    size=\"small\"\r\n                    sx={{ \r\n                      height: '22px',\r\n                      fontSize: '0.75rem',\r\n                      backgroundColor: progress === 100 ? '#e8f5e9' : `${status.color}15`,\r\n                      color: progress === 100 ? '#2e7d32' : status.color,\r\n                      fontWeight: 600,\r\n                      fontFamily: '\"Recursive Variable\", sans-serif',\r\n                      minWidth: 45\r\n                    }}\r\n                  />\r\n                </Box>\r\n              </Box>\r\n              \r\n              {/* Milestone Content - Expandable */}\r\n              <Collapse in={isExpanded}>\r\n                <Box sx={{ p: 2.5, pt: 2 }}>\r\n                  \r\n                  {/* Milestone Meta Info */}\r\n                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2, mb: 2, alignItems: 'center' }}>\r\n                    <Chip \r\n                      icon={<Iconify icon=\"material-symbols:flag\" width={14} height={14} />}\r\n                      label={status.label}\r\n                      size=\"small\"\r\n                      sx={{ \r\n                        height: '22px',\r\n                        fontSize: '0.75rem',\r\n                        backgroundColor: `${status.color}15`,\r\n                        color: status.color,\r\n                        fontWeight: 600,\r\n                        fontFamily: '\"Recursive Variable\", sans-serif'\r\n                      }}\r\n                    />\r\n                    \r\n                    {milestone.start_date && milestone.end_date && (\r\n                      <Typography \r\n                        variant=\"caption\" \r\n                        sx={{ \r\n                          color: '#666',\r\n                          fontSize: '0.8rem',\r\n                          fontFamily: '\"Recursive Variable\", sans-serif',\r\n                          display: 'flex',\r\n                          alignItems: 'center',\r\n                          gap: 0.5\r\n                        }}\r\n                      >\r\n                        <Iconify icon=\"material-symbols:calendar-month\" width={16} height={16} />\r\n                        {dayjs(milestone.start_date).format('MMM D')} - {dayjs(milestone.end_date).format('MMM D, YYYY')}\r\n                      </Typography>\r\n                    )}\r\n                    \r\n                    <Typography \r\n                      variant=\"caption\" \r\n                      sx={{ \r\n                        color: '#666',\r\n                        fontSize: '0.8rem',\r\n                        fontFamily: '\"Recursive Variable\", sans-serif',\r\n                        display: 'flex',\r\n                        alignItems: 'center',\r\n                        gap: 0.5\r\n                      }}\r\n                    >\r\n                      <Iconify icon=\"material-symbols:task\" width={16} height={16} />\r\n                      {completedTasks}/{totalTasks} tasks completed\r\n                    </Typography>\r\n                  </Box>\r\n\r\n                  {/* Progress Bar */}\r\n                  <Box sx={{ mb: 3 }}>\r\n                    <LinearProgress \r\n                      variant=\"determinate\" \r\n                      value={progress} \r\n                      sx={{ \r\n                        height: 6,\r\n                        borderRadius: 3,\r\n                        backgroundColor: '#f0f0f0',\r\n                        '& .MuiLinearProgress-bar': {\r\n                          backgroundColor: progress === 100 ? '#4CAF50' : mainYellowColor\r\n                        }\r\n                      }}\r\n                    />\r\n                  </Box>\r\n\r\n                  {/* Tasks List */}\r\n                  {milestone.tasks && milestone.tasks.length > 0 ? (\r\n                    <Box \r\n                      sx={{ \r\n                        display: 'flex', \r\n                        flexDirection: 'column',\r\n                        gap: 1.5\r\n                      }}\r\n                    >\r\n                      {milestone.tasks.map((task, taskIndex) => {\r\n                        const taskProgress = calculateTaskProgress(task);\r\n                        const taskStatus = getTaskStatus(task);\r\n                        const hasSubtasks = task.subtasks && task.subtasks.length > 0;\r\n                        const completedSubtasks = task.subtasks?.filter(s => getSubtaskStatus(s).label === 'Completed').length || 0;\r\n                        const isTaskCompleted = taskStatus.label === 'Completed';\r\n\r\n                        return (\r\n                          <Box \r\n                            key={task.id || taskIndex}\r\n                            sx={{ \r\n                              display: 'flex',\r\n                              flexDirection: 'column',\r\n                              gap: 1,\r\n                              backgroundColor: isTaskCompleted ? '#f8fff8' : '#fafafa',\r\n                              borderRadius: '8px',\r\n                              p: 1.5,\r\n                              border: `1px solid ${isTaskCompleted ? '#e6f7e6' : '#f0f0f0'}`,\r\n                              transition: 'all 0.2s ease',\r\n                              '&:hover': {\r\n                                borderColor: isTaskCompleted ? '#c8e6c9' : '#e0e0e0',\r\n                                backgroundColor: isTaskCompleted ? '#f0fff0' : '#f5f5f5',\r\n                                boxShadow: '0 1px 4px rgba(0,0,0,0.03)'\r\n                              }\r\n                            }}\r\n                          >\r\n                            {/* Task Header */}\r\n                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>\r\n                              <Box \r\n                                sx={{ \r\n                                  width: 10,\r\n                                  height: 10,\r\n                                  borderRadius: '50%',\r\n                                  backgroundColor: taskStatus.color,\r\n                                  flexShrink: 0\r\n                                }}\r\n                              />\r\n                              <Typography \r\n                                variant=\"body2\"\r\n                                sx={{ \r\n                                  flexGrow: 1,\r\n                                  color: isTaskCompleted ? '#4CAF50' : '#333',\r\n                                  fontFamily: '\"Recursive Variable\", sans-serif',\r\n                                  fontSize: '0.875rem',\r\n                                  fontWeight: 500,\r\n                                  lineHeight: 1.3\r\n                                }}\r\n                              >\r\n                                {task.name}\r\n                              </Typography>\r\n                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>\r\n                                {hasSubtasks && (\r\n                                  <Tooltip title={`${completedSubtasks} of ${task.subtasks.length} subtasks completed`}>\r\n                                    <Typography\r\n                                      variant=\"caption\"\r\n                                      sx={{\r\n                                        fontSize: '0.75rem',\r\n                                        color: completedSubtasks === task.subtasks.length ? '#2e7d32' : '#666',\r\n                                        fontWeight: 500,\r\n                                        fontFamily: '\"Recursive Variable\", sans-serif',\r\n                                        backgroundColor: completedSubtasks === task.subtasks.length ? '#e8f5e9' : '#f5f5f5',\r\n                                        px: 1,\r\n                                        py: 0.5,\r\n                                        borderRadius: '4px',\r\n                                        border: `1px solid ${completedSubtasks === task.subtasks.length ? '#c8e6c9' : '#e0e0e0'}`\r\n                                      }}\r\n                                    >\r\n                                      {completedSubtasks}/{task.subtasks.length} subtasks completed\r\n                                    </Typography>\r\n                                  </Tooltip>\r\n                                )}\r\n                                <Tooltip title={`${taskProgress}% completed`}>\r\n                                  <Box sx={{ position: 'relative', width: 28, height: 28 }}>\r\n                                    <Box\r\n                                      sx={{\r\n                                        position: 'absolute',\r\n                                        top: 0,\r\n                                        left: 0,\r\n                                        width: 28,\r\n                                        height: 28,\r\n                                        borderRadius: '50%',\r\n                                        backgroundColor: '#f0f0f0'\r\n                                      }}\r\n                                    />\r\n                                    <Box\r\n                                      sx={{\r\n                                        position: 'absolute',\r\n                                        top: 0,\r\n                                        left: 0,\r\n                                        width: 28,\r\n                                        height: 28,\r\n                                        borderRadius: '50%',\r\n                                        background: `conic-gradient(${taskProgress === 100 ? '#4CAF50' : taskStatus.color} ${taskProgress}%, transparent 0)`,\r\n                                        display: 'flex',\r\n                                        alignItems: 'center',\r\n                                        justifyContent: 'center'\r\n                                      }}\r\n                                    >\r\n                                      <Box\r\n                                        sx={{\r\n                                          width: 20,\r\n                                          height: 20,\r\n                                          borderRadius: '50%',\r\n                                          backgroundColor: '#fff',\r\n                                          display: 'flex',\r\n                                          alignItems: 'center',\r\n                                          justifyContent: 'center'\r\n                                        }}\r\n                                      >\r\n                                        {taskProgress === 100 ? (\r\n                                          <Iconify icon=\"material-symbols:check-small\" width={14} height={14} sx={{ color: '#4CAF50' }} />\r\n                                        ) : (\r\n                                          <Typography\r\n                                            variant=\"caption\"\r\n                                            sx={{\r\n                                              fontSize: '0.65rem',\r\n                                              fontWeight: 600,\r\n                                              color: taskProgress > 0 ? (taskProgress >= 50 ? '#4CAF50' : mainYellowColor) : '#999',\r\n                                              lineHeight: 1\r\n                                            }}\r\n                                          >\r\n                                            {taskProgress}%\r\n                                          </Typography>\r\n                                        )}\r\n                                      </Box>\r\n                                    </Box>\r\n                                  </Box>\r\n                                </Tooltip>\r\n                              </Box>\r\n                            </Box>\r\n                          </Box>\r\n                        );\r\n                      })}\r\n                    </Box>\r\n                  ) : (\r\n                    <Box sx={{ textAlign: 'center', py: 2, color: '#999' }}>\r\n                      <Typography variant=\"body2\">No tasks in this milestone</Typography>\r\n                    </Box>\r\n                  )}\r\n                </Box>\r\n              </Collapse>\r\n            </CardContent>\r\n          </Card>\r\n        );\r\n      })}\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default MilestoneOverview; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,WAAW,EAAEC,IAAI,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,OAAO,QAAQ,eAAe;AACpH,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,iBAAiB,GAAGC,IAAA,IAQpB;EAAAC,EAAA;EAAA,IARqB;IACzBC,UAAU;IACVC,0BAA0B;IAC1BC,kBAAkB;IAClBC,qBAAqB;IACrBC,aAAa;IACbC,wBAAwB;IACxBC;EACF,CAAC,GAAAR,IAAA;EACC,MAAM,CAACS,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG1B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAEhE,MAAM2B,eAAe,GAAIC,WAAW,IAAK;IACvCF,qBAAqB,CAACG,IAAI,KAAK;MAC7B,GAAGA,IAAI;MACP,CAACD,WAAW,GAAG,CAACC,IAAI,CAACD,WAAW;IAClC,CAAC,CAAC,CAAC;EACL,CAAC;EAED,IAAI,CAACV,UAAU,IAAIA,UAAU,CAACY,MAAM,KAAK,CAAC,EAAE;IAC1C,oBACEhB,OAAA,CAACb,GAAG;MACF8B,EAAE,EAAE;QACFC,SAAS,EAAE,QAAQ;QACnBC,EAAE,EAAE,CAAC;QACLC,eAAe,EAAE,SAAS;QAC1BC,YAAY,EAAE,MAAM;QACpBC,MAAM,EAAE;MACV,CAAE;MAAAC,QAAA,gBAEFvB,OAAA,CAACJ,OAAO;QACN4B,IAAI,EAAC,uBAAuB;QAC5BC,KAAK,EAAE,EAAG;QACVC,MAAM,EAAE,EAAG;QACXT,EAAE,EAAE;UAAEU,KAAK,EAAE,MAAM;UAAEC,EAAE,EAAE;QAAE;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9B,CAAC,eACFhC,OAAA,CAACZ,UAAU;QAAC6C,OAAO,EAAC,OAAO;QAAChB,EAAE,EAAE;UAAEU,KAAK,EAAE,MAAM;UAAEO,UAAU,EAAE,kCAAkC;UAAEC,QAAQ,EAAE;QAAS,CAAE;QAAAZ,QAAA,EAAC;MAEvH;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC;EAEV;EAEA,oBACEhC,OAAA,CAACb,GAAG;IAAC8B,EAAE,EAAE;MAAEmB,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE,QAAQ;MAAEC,GAAG,EAAE;IAAE,CAAE;IAAAf,QAAA,EAC3DnB,UAAU,CAACmC,GAAG,CAAC,CAACC,SAAS,EAAEC,KAAK,KAAK;MAAA,IAAAC,gBAAA,EAAAC,iBAAA;MACpC,MAAM7B,WAAW,GAAG0B,SAAS,CAACI,EAAE,IAAI,aAAaH,KAAK,EAAE;MACxD,MAAMI,UAAU,GAAGlC,kBAAkB,CAACG,WAAW,CAAC,KAAK,KAAK,CAAC,CAAC;MAC9D,MAAMgC,QAAQ,GAAGzC,0BAA0B,CAACmC,SAAS,CAAC;MACtD,MAAMO,MAAM,GAAGzC,kBAAkB,CAACkC,SAAS,CAAC;MAC5C,MAAMQ,cAAc,GAAG,EAAAN,gBAAA,GAAAF,SAAS,CAACS,KAAK,cAAAP,gBAAA,uBAAfA,gBAAA,CAAiBQ,MAAM,CAACC,IAAI,IAAI3C,aAAa,CAAC2C,IAAI,CAAC,CAACC,KAAK,KAAK,WAAW,CAAC,CAACpC,MAAM,KAAI,CAAC;MAC7G,MAAMqC,UAAU,GAAG,EAAAV,iBAAA,GAAAH,SAAS,CAACS,KAAK,cAAAN,iBAAA,uBAAfA,iBAAA,CAAiB3B,MAAM,KAAI,CAAC;MAC/C,MAAMsC,QAAQ,GAAGd,SAAS,CAACe,QAAQ,GAAGzD,KAAK,CAAC0C,SAAS,CAACe,QAAQ,CAAC,CAACC,IAAI,CAAC1D,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,GAAG,IAAI;MAC3F,MAAM2D,SAAS,GAAGH,QAAQ,KAAK,IAAI,IAAIA,QAAQ,GAAG,CAAC;MAEnD,oBACEtD,OAAA,CAACX,IAAI;QAEHqE,SAAS,EAAE,CAAE;QACbzC,EAAE,EAAE;UACFI,YAAY,EAAE,MAAM;UACpBC,MAAM,EAAE,mBAAmB;UAC3BF,eAAe,EAAE,MAAM;UACvBuC,UAAU,EAAE,eAAe;UAC3BC,QAAQ,EAAE,SAAS;UACnB,SAAS,EAAE;YACTC,WAAW,EAAE,SAAS;YACtBC,SAAS,EAAE;UACb;QACF,CAAE;QAAAvC,QAAA,eAEFvB,OAAA,CAACV,WAAW;UAAC2B,EAAE,EAAE;YAAE8C,CAAC,EAAE;UAAe,CAAE;UAAAxC,QAAA,gBAErCvB,OAAA,CAACb,GAAG;YACF8B,EAAE,EAAE;cACFmB,OAAO,EAAE,MAAM;cACf4B,UAAU,EAAE,QAAQ;cACpBD,CAAC,EAAE,GAAG;cACNE,MAAM,EAAE,SAAS;cACjBC,YAAY,EAAErB,UAAU,GAAG,mBAAmB,GAAG,MAAM;cACvDsB,mBAAmB,EAAE,MAAM;cAC3BC,oBAAoB,EAAE,MAAM;cAC5BhD,eAAe,EAAEyB,UAAU,GAAG,MAAM,GAAG,SAAS;cAChDc,UAAU,EAAE;YACd,CAAE;YACFU,OAAO,EAAEA,CAAA,KAAMxD,eAAe,CAACC,WAAW,CAAE;YAAAS,QAAA,gBAE5CvB,OAAA,CAACb,GAAG;cACF8B,EAAE,EAAE;gBACFQ,KAAK,EAAE,EAAE;gBACTC,MAAM,EAAE,EAAE;gBACVL,YAAY,EAAE,KAAK;gBACnBD,eAAe,EAAE2B,MAAM,CAACpB,KAAK;gBAC7BS,OAAO,EAAE,MAAM;gBACf4B,UAAU,EAAE,QAAQ;gBACpBM,cAAc,EAAE,QAAQ;gBACxBC,EAAE,EAAE,CAAC;gBACLC,UAAU,EAAE;cACd,CAAE;cAAAjD,QAAA,eAEFvB,OAAA,CAACJ,OAAO;gBACN4B,IAAI,EAAEqB,UAAU,GAAG,sCAAsC,GAAG,uCAAwC;gBACpGpB,KAAK,EAAE,EAAG;gBACVC,MAAM,EAAE,EAAG;gBACXT,EAAE,EAAE;kBAAEU,KAAK,EAAE;gBAAO;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eAENhC,OAAA,CAACb,GAAG;cAAC8B,EAAE,EAAE;gBAAEwD,QAAQ,EAAE;cAAE,CAAE;cAAAlD,QAAA,eACvBvB,OAAA,CAACZ,UAAU;gBACT6C,OAAO,EAAC,WAAW;gBACnBhB,EAAE,EAAE;kBACFyD,UAAU,EAAE,GAAG;kBACf/C,KAAK,EAAE,MAAM;kBACbO,UAAU,EAAE,kCAAkC;kBAC9CC,QAAQ,EAAE,MAAM;kBAChBwC,UAAU,EAAE;gBACd,CAAE;gBAAApD,QAAA,EAEDiB,SAAS,CAACoC;cAAI;gBAAA/C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAENhC,OAAA,CAACb,GAAG;cAAC8B,EAAE,EAAE;gBAAEmB,OAAO,EAAE,MAAM;gBAAE4B,UAAU,EAAE,QAAQ;gBAAE1B,GAAG,EAAE,CAAC;gBAAEkC,UAAU,EAAE;cAAE,CAAE;cAAAjD,QAAA,GACvE+B,QAAQ,KAAK,IAAI,iBAChBtD,OAAA,CAACN,OAAO;gBAACmF,KAAK,EAAEpB,SAAS,GAAG,SAAS,GAAG,gBAAiB;gBAAAlC,QAAA,eACvDvB,OAAA,CAACT,IAAI;kBACHiC,IAAI,eAAExB,OAAA,CAACJ,OAAO;oBAAC4B,IAAI,EAAC,iCAAiC;oBAACC,KAAK,EAAE,EAAG;oBAACC,MAAM,EAAE;kBAAG;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAChFoB,KAAK,EAAEK,SAAS,GAAG,GAAGqB,IAAI,CAACC,GAAG,CAACzB,QAAQ,CAAC,eAAe,GAAG,GAAGA,QAAQ,YAAa;kBAClF0B,IAAI,EAAC,OAAO;kBACZ/D,EAAE,EAAE;oBACFS,MAAM,EAAE,MAAM;oBACdS,QAAQ,EAAE,SAAS;oBACnBf,eAAe,EAAEqC,SAAS,GAAG,SAAS,GAAG,SAAS;oBAClD9B,KAAK,EAAE8B,SAAS,GAAG,SAAS,GAAG,SAAS;oBACxCiB,UAAU,EAAE,GAAG;oBACfxC,UAAU,EAAE;kBACd;gBAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK,CACV,eAEDhC,OAAA,CAACT,IAAI;gBACH6D,KAAK,EAAE,GAAGN,QAAQ,GAAI;gBACtBkC,IAAI,EAAC,OAAO;gBACZ/D,EAAE,EAAE;kBACFS,MAAM,EAAE,MAAM;kBACdS,QAAQ,EAAE,SAAS;kBACnBf,eAAe,EAAE0B,QAAQ,KAAK,GAAG,GAAG,SAAS,GAAG,GAAGC,MAAM,CAACpB,KAAK,IAAI;kBACnEA,KAAK,EAAEmB,QAAQ,KAAK,GAAG,GAAG,SAAS,GAAGC,MAAM,CAACpB,KAAK;kBAClD+C,UAAU,EAAE,GAAG;kBACfxC,UAAU,EAAE,kCAAkC;kBAC9C+C,QAAQ,EAAE;gBACZ;cAAE;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNhC,OAAA,CAACP,QAAQ;YAACyF,EAAE,EAAErC,UAAW;YAAAtB,QAAA,eACvBvB,OAAA,CAACb,GAAG;cAAC8B,EAAE,EAAE;gBAAE8C,CAAC,EAAE,GAAG;gBAAEoB,EAAE,EAAE;cAAE,CAAE;cAAA5D,QAAA,gBAGzBvB,OAAA,CAACb,GAAG;gBAAC8B,EAAE,EAAE;kBAAEmB,OAAO,EAAE,MAAM;kBAAEgD,QAAQ,EAAE,MAAM;kBAAE9C,GAAG,EAAE,CAAC;kBAAEV,EAAE,EAAE,CAAC;kBAAEoC,UAAU,EAAE;gBAAS,CAAE;gBAAAzC,QAAA,gBAClFvB,OAAA,CAACT,IAAI;kBACHiC,IAAI,eAAExB,OAAA,CAACJ,OAAO;oBAAC4B,IAAI,EAAC,uBAAuB;oBAACC,KAAK,EAAE,EAAG;oBAACC,MAAM,EAAE;kBAAG;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACtEoB,KAAK,EAAEL,MAAM,CAACK,KAAM;kBACpB4B,IAAI,EAAC,OAAO;kBACZ/D,EAAE,EAAE;oBACFS,MAAM,EAAE,MAAM;oBACdS,QAAQ,EAAE,SAAS;oBACnBf,eAAe,EAAE,GAAG2B,MAAM,CAACpB,KAAK,IAAI;oBACpCA,KAAK,EAAEoB,MAAM,CAACpB,KAAK;oBACnB+C,UAAU,EAAE,GAAG;oBACfxC,UAAU,EAAE;kBACd;gBAAE;kBAAAL,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,EAEDQ,SAAS,CAAC6C,UAAU,IAAI7C,SAAS,CAACe,QAAQ,iBACzCvD,OAAA,CAACZ,UAAU;kBACT6C,OAAO,EAAC,SAAS;kBACjBhB,EAAE,EAAE;oBACFU,KAAK,EAAE,MAAM;oBACbQ,QAAQ,EAAE,QAAQ;oBAClBD,UAAU,EAAE,kCAAkC;oBAC9CE,OAAO,EAAE,MAAM;oBACf4B,UAAU,EAAE,QAAQ;oBACpB1B,GAAG,EAAE;kBACP,CAAE;kBAAAf,QAAA,gBAEFvB,OAAA,CAACJ,OAAO;oBAAC4B,IAAI,EAAC,iCAAiC;oBAACC,KAAK,EAAE,EAAG;oBAACC,MAAM,EAAE;kBAAG;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EACxElC,KAAK,CAAC0C,SAAS,CAAC6C,UAAU,CAAC,CAACC,MAAM,CAAC,OAAO,CAAC,EAAC,KAAG,EAACxF,KAAK,CAAC0C,SAAS,CAACe,QAAQ,CAAC,CAAC+B,MAAM,CAAC,aAAa,CAAC;gBAAA;kBAAAzD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtF,CACb,eAEDhC,OAAA,CAACZ,UAAU;kBACT6C,OAAO,EAAC,SAAS;kBACjBhB,EAAE,EAAE;oBACFU,KAAK,EAAE,MAAM;oBACbQ,QAAQ,EAAE,QAAQ;oBAClBD,UAAU,EAAE,kCAAkC;oBAC9CE,OAAO,EAAE,MAAM;oBACf4B,UAAU,EAAE,QAAQ;oBACpB1B,GAAG,EAAE;kBACP,CAAE;kBAAAf,QAAA,gBAEFvB,OAAA,CAACJ,OAAO;oBAAC4B,IAAI,EAAC,uBAAuB;oBAACC,KAAK,EAAE,EAAG;oBAACC,MAAM,EAAE;kBAAG;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,EAC9DgB,cAAc,EAAC,GAAC,EAACK,UAAU,EAAC,kBAC/B;gBAAA;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eAGNhC,OAAA,CAACb,GAAG;gBAAC8B,EAAE,EAAE;kBAAEW,EAAE,EAAE;gBAAE,CAAE;gBAAAL,QAAA,eACjBvB,OAAA,CAACR,cAAc;kBACbyC,OAAO,EAAC,aAAa;kBACrBsD,KAAK,EAAEzC,QAAS;kBAChB7B,EAAE,EAAE;oBACFS,MAAM,EAAE,CAAC;oBACTL,YAAY,EAAE,CAAC;oBACfD,eAAe,EAAE,SAAS;oBAC1B,0BAA0B,EAAE;sBAC1BA,eAAe,EAAE0B,QAAQ,KAAK,GAAG,GAAG,SAAS,GAAGjD;oBAClD;kBACF;gBAAE;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,EAGLQ,SAAS,CAACS,KAAK,IAAIT,SAAS,CAACS,KAAK,CAACjC,MAAM,GAAG,CAAC,gBAC5ChB,OAAA,CAACb,GAAG;gBACF8B,EAAE,EAAE;kBACFmB,OAAO,EAAE,MAAM;kBACfC,aAAa,EAAE,QAAQ;kBACvBC,GAAG,EAAE;gBACP,CAAE;gBAAAf,QAAA,EAEDiB,SAAS,CAACS,KAAK,CAACV,GAAG,CAAC,CAACY,IAAI,EAAEqC,SAAS,KAAK;kBAAA,IAAAC,cAAA;kBACxC,MAAMC,YAAY,GAAGnF,qBAAqB,CAAC4C,IAAI,CAAC;kBAChD,MAAMwC,UAAU,GAAGnF,aAAa,CAAC2C,IAAI,CAAC;kBACtC,MAAMyC,WAAW,GAAGzC,IAAI,CAAC0C,QAAQ,IAAI1C,IAAI,CAAC0C,QAAQ,CAAC7E,MAAM,GAAG,CAAC;kBAC7D,MAAM8E,iBAAiB,GAAG,EAAAL,cAAA,GAAAtC,IAAI,CAAC0C,QAAQ,cAAAJ,cAAA,uBAAbA,cAAA,CAAevC,MAAM,CAAC6C,CAAC,IAAIrF,gBAAgB,CAACqF,CAAC,CAAC,CAAC3C,KAAK,KAAK,WAAW,CAAC,CAACpC,MAAM,KAAI,CAAC;kBAC3G,MAAMgF,eAAe,GAAGL,UAAU,CAACvC,KAAK,KAAK,WAAW;kBAExD,oBACEpD,OAAA,CAACb,GAAG;oBAEF8B,EAAE,EAAE;sBACFmB,OAAO,EAAE,MAAM;sBACfC,aAAa,EAAE,QAAQ;sBACvBC,GAAG,EAAE,CAAC;sBACNlB,eAAe,EAAE4E,eAAe,GAAG,SAAS,GAAG,SAAS;sBACxD3E,YAAY,EAAE,KAAK;sBACnB0C,CAAC,EAAE,GAAG;sBACNzC,MAAM,EAAE,aAAa0E,eAAe,GAAG,SAAS,GAAG,SAAS,EAAE;sBAC9DrC,UAAU,EAAE,eAAe;sBAC3B,SAAS,EAAE;wBACTE,WAAW,EAAEmC,eAAe,GAAG,SAAS,GAAG,SAAS;wBACpD5E,eAAe,EAAE4E,eAAe,GAAG,SAAS,GAAG,SAAS;wBACxDlC,SAAS,EAAE;sBACb;oBACF,CAAE;oBAAAvC,QAAA,eAGFvB,OAAA,CAACb,GAAG;sBAAC8B,EAAE,EAAE;wBAAEmB,OAAO,EAAE,MAAM;wBAAE4B,UAAU,EAAE,QAAQ;wBAAE1B,GAAG,EAAE;sBAAI,CAAE;sBAAAf,QAAA,gBAC3DvB,OAAA,CAACb,GAAG;wBACF8B,EAAE,EAAE;0BACFQ,KAAK,EAAE,EAAE;0BACTC,MAAM,EAAE,EAAE;0BACVL,YAAY,EAAE,KAAK;0BACnBD,eAAe,EAAEuE,UAAU,CAAChE,KAAK;0BACjC6C,UAAU,EAAE;wBACd;sBAAE;wBAAA3C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACFhC,OAAA,CAACZ,UAAU;wBACT6C,OAAO,EAAC,OAAO;wBACfhB,EAAE,EAAE;0BACFwD,QAAQ,EAAE,CAAC;0BACX9C,KAAK,EAAEqE,eAAe,GAAG,SAAS,GAAG,MAAM;0BAC3C9D,UAAU,EAAE,kCAAkC;0BAC9CC,QAAQ,EAAE,UAAU;0BACpBuC,UAAU,EAAE,GAAG;0BACfC,UAAU,EAAE;wBACd,CAAE;wBAAApD,QAAA,EAED4B,IAAI,CAACyB;sBAAI;wBAAA/C,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACA,CAAC,eACbhC,OAAA,CAACb,GAAG;wBAAC8B,EAAE,EAAE;0BAAEmB,OAAO,EAAE,MAAM;0BAAE4B,UAAU,EAAE,QAAQ;0BAAE1B,GAAG,EAAE;wBAAI,CAAE;wBAAAf,QAAA,GAC1DqE,WAAW,iBACV5F,OAAA,CAACN,OAAO;0BAACmF,KAAK,EAAE,GAAGiB,iBAAiB,OAAO3C,IAAI,CAAC0C,QAAQ,CAAC7E,MAAM,qBAAsB;0BAAAO,QAAA,eACnFvB,OAAA,CAACZ,UAAU;4BACT6C,OAAO,EAAC,SAAS;4BACjBhB,EAAE,EAAE;8BACFkB,QAAQ,EAAE,SAAS;8BACnBR,KAAK,EAAEmE,iBAAiB,KAAK3C,IAAI,CAAC0C,QAAQ,CAAC7E,MAAM,GAAG,SAAS,GAAG,MAAM;8BACtE0D,UAAU,EAAE,GAAG;8BACfxC,UAAU,EAAE,kCAAkC;8BAC9Cd,eAAe,EAAE0E,iBAAiB,KAAK3C,IAAI,CAAC0C,QAAQ,CAAC7E,MAAM,GAAG,SAAS,GAAG,SAAS;8BACnFiF,EAAE,EAAE,CAAC;8BACL9E,EAAE,EAAE,GAAG;8BACPE,YAAY,EAAE,KAAK;8BACnBC,MAAM,EAAE,aAAawE,iBAAiB,KAAK3C,IAAI,CAAC0C,QAAQ,CAAC7E,MAAM,GAAG,SAAS,GAAG,SAAS;4BACzF,CAAE;4BAAAO,QAAA,GAEDuE,iBAAiB,EAAC,GAAC,EAAC3C,IAAI,CAAC0C,QAAQ,CAAC7E,MAAM,EAAC,qBAC5C;0BAAA;4BAAAa,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAY;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACN,CACV,eACDhC,OAAA,CAACN,OAAO;0BAACmF,KAAK,EAAE,GAAGa,YAAY,aAAc;0BAAAnE,QAAA,eAC3CvB,OAAA,CAACb,GAAG;4BAAC8B,EAAE,EAAE;8BAAEiF,QAAQ,EAAE,UAAU;8BAAEzE,KAAK,EAAE,EAAE;8BAAEC,MAAM,EAAE;4BAAG,CAAE;4BAAAH,QAAA,gBACvDvB,OAAA,CAACb,GAAG;8BACF8B,EAAE,EAAE;gCACFiF,QAAQ,EAAE,UAAU;gCACpBC,GAAG,EAAE,CAAC;gCACNC,IAAI,EAAE,CAAC;gCACP3E,KAAK,EAAE,EAAE;gCACTC,MAAM,EAAE,EAAE;gCACVL,YAAY,EAAE,KAAK;gCACnBD,eAAe,EAAE;8BACnB;4BAAE;8BAAAS,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC,eACFhC,OAAA,CAACb,GAAG;8BACF8B,EAAE,EAAE;gCACFiF,QAAQ,EAAE,UAAU;gCACpBC,GAAG,EAAE,CAAC;gCACNC,IAAI,EAAE,CAAC;gCACP3E,KAAK,EAAE,EAAE;gCACTC,MAAM,EAAE,EAAE;gCACVL,YAAY,EAAE,KAAK;gCACnBgF,UAAU,EAAE,kBAAkBX,YAAY,KAAK,GAAG,GAAG,SAAS,GAAGC,UAAU,CAAChE,KAAK,IAAI+D,YAAY,mBAAmB;gCACpHtD,OAAO,EAAE,MAAM;gCACf4B,UAAU,EAAE,QAAQ;gCACpBM,cAAc,EAAE;8BAClB,CAAE;8BAAA/C,QAAA,eAEFvB,OAAA,CAACb,GAAG;gCACF8B,EAAE,EAAE;kCACFQ,KAAK,EAAE,EAAE;kCACTC,MAAM,EAAE,EAAE;kCACVL,YAAY,EAAE,KAAK;kCACnBD,eAAe,EAAE,MAAM;kCACvBgB,OAAO,EAAE,MAAM;kCACf4B,UAAU,EAAE,QAAQ;kCACpBM,cAAc,EAAE;gCAClB,CAAE;gCAAA/C,QAAA,EAEDmE,YAAY,KAAK,GAAG,gBACnB1F,OAAA,CAACJ,OAAO;kCAAC4B,IAAI,EAAC,8BAA8B;kCAACC,KAAK,EAAE,EAAG;kCAACC,MAAM,EAAE,EAAG;kCAACT,EAAE,EAAE;oCAAEU,KAAK,EAAE;kCAAU;gCAAE;kCAAAE,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE,CAAC,gBAEhGhC,OAAA,CAACZ,UAAU;kCACT6C,OAAO,EAAC,SAAS;kCACjBhB,EAAE,EAAE;oCACFkB,QAAQ,EAAE,SAAS;oCACnBuC,UAAU,EAAE,GAAG;oCACf/C,KAAK,EAAE+D,YAAY,GAAG,CAAC,GAAIA,YAAY,IAAI,EAAE,GAAG,SAAS,GAAG7F,eAAe,GAAI,MAAM;oCACrF8E,UAAU,EAAE;kCACd,CAAE;kCAAApD,QAAA,GAEDmE,YAAY,EAAC,GAChB;gCAAA;kCAAA7D,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAY;8BACb;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OACE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACH,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACH;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACC,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACP,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC,GAxHDmB,IAAI,CAACP,EAAE,IAAI4C,SAAS;oBAAA3D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAyHtB,CAAC;gBAEV,CAAC;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC,gBAENhC,OAAA,CAACb,GAAG;gBAAC8B,EAAE,EAAE;kBAAEC,SAAS,EAAE,QAAQ;kBAAEC,EAAE,EAAE,CAAC;kBAAEQ,KAAK,EAAE;gBAAO,CAAE;gBAAAJ,QAAA,eACrDvB,OAAA,CAACZ,UAAU;kBAAC6C,OAAO,EAAC,OAAO;kBAAAV,QAAA,EAAC;gBAA0B;kBAAAM,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC,GAhUTlB,WAAW;QAAAe,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAiUZ,CAAC;IAEX,CAAC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC7B,EAAA,CA9XIF,iBAAiB;AAAAqG,EAAA,GAAjBrG,iBAAiB;AAgYvB,eAAeA,iBAAiB;AAAC,IAAAqG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}