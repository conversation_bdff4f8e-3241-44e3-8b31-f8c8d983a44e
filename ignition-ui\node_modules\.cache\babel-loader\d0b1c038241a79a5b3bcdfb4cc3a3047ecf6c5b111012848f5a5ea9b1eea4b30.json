{"ast": null, "code": "import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nconst COMPONENT_NAME = 'OptionGroup';\nexport function getOptionGroupUtilityClass(slot) {\n  return generateUtilityClass(COMPONENT_NAME, slot);\n}\nexport const optionGroupClasses = generateUtilityClasses(COMPONENT_NAME, ['root', 'disabled', 'label', 'list']);", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "COMPONENT_NAME", "getOptionGroupUtilityClass", "slot", "optionGroupClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/OptionGroup/optionGroupClasses.js"], "sourcesContent": ["import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nconst COMPONENT_NAME = 'OptionGroup';\nexport function getOptionGroupUtilityClass(slot) {\n  return generateUtilityClass(COMPONENT_NAME, slot);\n}\nexport const optionGroupClasses = generateUtilityClasses(COMPONENT_NAME, ['root', 'disabled', 'label', 'list']);"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,MAAMC,cAAc,GAAG,aAAa;AACpC,OAAO,SAASC,0BAA0BA,CAACC,IAAI,EAAE;EAC/C,OAAOJ,oBAAoB,CAACE,cAAc,EAAEE,IAAI,CAAC;AACnD;AACA,OAAO,MAAMC,kBAAkB,GAAGJ,sBAAsB,CAACC,cAAc,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}