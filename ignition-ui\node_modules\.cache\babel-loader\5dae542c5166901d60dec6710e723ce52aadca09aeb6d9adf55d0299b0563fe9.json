{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport GlobalStyles from '../GlobalStyles';\nimport defaultTheme from '../styles/defaultTheme';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\n/**\n * Kickstart an elegant, consistent, and simple baseline to build upon.\n *\n * Demos:\n *\n * - [CSS Baseline](https://mui.com/joy-ui/react-css-baseline/)\n *\n * API:\n *\n * - [CssBaseline API](https://mui.com/joy-ui/api/css-baseline/)\n */\nfunction CssBaseline(props) {\n  const {\n    children,\n    disableColorScheme = false\n  } = props;\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(GlobalStyles, {\n      styles: theme => {\n        var _components$JoyTypogr, _components;\n        const colorSchemeStyles = {};\n        if (!disableColorScheme) {\n          // The CssBaseline is wrapped inside a CssVarsProvider\n          Object.entries(theme.colorSchemes).forEach(_ref => {\n            let [key, scheme] = _ref;\n            var _scheme$palette;\n            colorSchemeStyles[theme.getColorSchemeSelector(key).replace(/\\s*&/, '')] = {\n              colorScheme: (_scheme$palette = scheme.palette) == null ? void 0 : _scheme$palette.mode\n            };\n          });\n        }\n        const defaultTypographyLevel = (_components$JoyTypogr = (_components = theme.components) == null || (_components = _components.JoyTypography) == null || (_components = _components.defaultProps) == null ? void 0 : _components.level) != null ? _components$JoyTypogr : 'body-md';\n        return _extends({\n          html: {\n            WebkitFontSmoothing: 'antialiased',\n            MozOsxFontSmoothing: 'grayscale',\n            // Change from `box-sizing: content-box` so that `width`\n            // is not affected by `padding` or `border`.\n            boxSizing: 'border-box',\n            // Fix font resize problem in iOS\n            WebkitTextSizeAdjust: '100%'\n          },\n          '*, *::before, *::after': {\n            boxSizing: 'inherit'\n          },\n          'strong, b': {\n            fontWeight: theme.vars.fontWeight.lg\n          },\n          body: _extends({\n            margin: 0,\n            // Remove the margin in all browsers.\n            color: theme.vars.palette.text.primary,\n            fontFamily: theme.vars.fontFamily.body\n          }, theme.typography[defaultTypographyLevel], {\n            backgroundColor: theme.vars.palette.background.body,\n            '@media print': {\n              // Save printer ink.\n              backgroundColor: theme.vars.palette.common.white\n            },\n            // Add support for document.body.requestFullScreen().\n            // Other elements, if background transparent, are not supported.\n            '&::backdrop': {\n              backgroundColor: theme.vars.palette.background.backdrop\n            }\n          })\n        }, colorSchemeStyles);\n      },\n      defaultTheme: defaultTheme\n    }), children]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? CssBaseline.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * You can wrap a node.\n   */\n  children: PropTypes.node,\n  /**\n   * Disable `color-scheme` CSS property.\n   *\n   * For more details, check out https://developer.mozilla.org/en-US/docs/Web/CSS/color-scheme\n   * For browser support, check out https://caniuse.com/?search=color-scheme\n   * @default false\n   */\n  disableColorScheme: PropTypes.bool\n} : void 0;\nexport default CssBaseline;", "map": {"version": 3, "names": ["_extends", "React", "PropTypes", "GlobalStyles", "defaultTheme", "jsx", "_jsx", "jsxs", "_jsxs", "CssBaseline", "props", "children", "disableColorScheme", "Fragment", "styles", "theme", "_components$JoyTypogr", "_components", "colorSchemeStyles", "Object", "entries", "colorSchemes", "for<PERSON>ach", "_ref", "key", "scheme", "_scheme$palette", "getColorSchemeSelector", "replace", "colorScheme", "palette", "mode", "defaultTypographyLevel", "components", "JoyTypography", "defaultProps", "level", "html", "WebkitFontSmoothing", "MozOsxFontSmoothing", "boxSizing", "WebkitTextSizeAdjust", "fontWeight", "vars", "lg", "body", "margin", "color", "text", "primary", "fontFamily", "typography", "backgroundColor", "background", "common", "white", "backdrop", "process", "env", "NODE_ENV", "propTypes", "node", "bool"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/CssBaseline/CssBaseline.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport GlobalStyles from '../GlobalStyles';\nimport defaultTheme from '../styles/defaultTheme';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\n/**\n * Kickstart an elegant, consistent, and simple baseline to build upon.\n *\n * Demos:\n *\n * - [CSS Baseline](https://mui.com/joy-ui/react-css-baseline/)\n *\n * API:\n *\n * - [CssBaseline API](https://mui.com/joy-ui/api/css-baseline/)\n */\nfunction CssBaseline(props) {\n  const {\n    children,\n    disableColorScheme = false\n  } = props;\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(GlobalStyles, {\n      styles: theme => {\n        var _components$JoyTypogr, _components;\n        const colorSchemeStyles = {};\n        if (!disableColorScheme) {\n          // The CssBaseline is wrapped inside a CssVarsProvider\n          Object.entries(theme.colorSchemes).forEach(([key, scheme]) => {\n            var _scheme$palette;\n            colorSchemeStyles[theme.getColorSchemeSelector(key).replace(/\\s*&/, '')] = {\n              colorScheme: (_scheme$palette = scheme.palette) == null ? void 0 : _scheme$palette.mode\n            };\n          });\n        }\n        const defaultTypographyLevel = (_components$JoyTypogr = (_components = theme.components) == null || (_components = _components.JoyTypography) == null || (_components = _components.defaultProps) == null ? void 0 : _components.level) != null ? _components$JoyTypogr : 'body-md';\n        return _extends({\n          html: {\n            WebkitFontSmoothing: 'antialiased',\n            MozOsxFontSmoothing: 'grayscale',\n            // Change from `box-sizing: content-box` so that `width`\n            // is not affected by `padding` or `border`.\n            boxSizing: 'border-box',\n            // Fix font resize problem in iOS\n            WebkitTextSizeAdjust: '100%'\n          },\n          '*, *::before, *::after': {\n            boxSizing: 'inherit'\n          },\n          'strong, b': {\n            fontWeight: theme.vars.fontWeight.lg\n          },\n          body: _extends({\n            margin: 0,\n            // Remove the margin in all browsers.\n            color: theme.vars.palette.text.primary,\n            fontFamily: theme.vars.fontFamily.body\n          }, theme.typography[defaultTypographyLevel], {\n            backgroundColor: theme.vars.palette.background.body,\n            '@media print': {\n              // Save printer ink.\n              backgroundColor: theme.vars.palette.common.white\n            },\n            // Add support for document.body.requestFullScreen().\n            // Other elements, if background transparent, are not supported.\n            '&::backdrop': {\n              backgroundColor: theme.vars.palette.background.backdrop\n            }\n          })\n        }, colorSchemeStyles);\n      },\n      defaultTheme: defaultTheme\n    }), children]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? CssBaseline.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * You can wrap a node.\n   */\n  children: PropTypes.node,\n  /**\n   * Disable `color-scheme` CSS property.\n   *\n   * For more details, check out https://developer.mozilla.org/en-US/docs/Web/CSS/color-scheme\n   * For browser support, check out https://caniuse.com/?search=color-scheme\n   * @default false\n   */\n  disableColorScheme: PropTypes.bool\n} : void 0;\nexport default CssBaseline;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,YAAY,MAAM,iBAAiB;AAC1C,OAAOC,YAAY,MAAM,wBAAwB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,KAAK,EAAE;EAC1B,MAAM;IACJC,QAAQ;IACRC,kBAAkB,GAAG;EACvB,CAAC,GAAGF,KAAK;EACT,OAAO,aAAaF,KAAK,CAACP,KAAK,CAACY,QAAQ,EAAE;IACxCF,QAAQ,EAAE,CAAC,aAAaL,IAAI,CAACH,YAAY,EAAE;MACzCW,MAAM,EAAEC,KAAK,IAAI;QACf,IAAIC,qBAAqB,EAAEC,WAAW;QACtC,MAAMC,iBAAiB,GAAG,CAAC,CAAC;QAC5B,IAAI,CAACN,kBAAkB,EAAE;UACvB;UACAO,MAAM,CAACC,OAAO,CAACL,KAAK,CAACM,YAAY,CAAC,CAACC,OAAO,CAACC,IAAA,IAAmB;YAAA,IAAlB,CAACC,GAAG,EAAEC,MAAM,CAAC,GAAAF,IAAA;YACvD,IAAIG,eAAe;YACnBR,iBAAiB,CAACH,KAAK,CAACY,sBAAsB,CAACH,GAAG,CAAC,CAACI,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,GAAG;cACzEC,WAAW,EAAE,CAACH,eAAe,GAAGD,MAAM,CAACK,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGJ,eAAe,CAACK;YACrF,CAAC;UACH,CAAC,CAAC;QACJ;QACA,MAAMC,sBAAsB,GAAG,CAAChB,qBAAqB,GAAG,CAACC,WAAW,GAAGF,KAAK,CAACkB,UAAU,KAAK,IAAI,IAAI,CAAChB,WAAW,GAAGA,WAAW,CAACiB,aAAa,KAAK,IAAI,IAAI,CAACjB,WAAW,GAAGA,WAAW,CAACkB,YAAY,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGlB,WAAW,CAACmB,KAAK,KAAK,IAAI,GAAGpB,qBAAqB,GAAG,SAAS;QACnR,OAAOhB,QAAQ,CAAC;UACdqC,IAAI,EAAE;YACJC,mBAAmB,EAAE,aAAa;YAClCC,mBAAmB,EAAE,WAAW;YAChC;YACA;YACAC,SAAS,EAAE,YAAY;YACvB;YACAC,oBAAoB,EAAE;UACxB,CAAC;UACD,wBAAwB,EAAE;YACxBD,SAAS,EAAE;UACb,CAAC;UACD,WAAW,EAAE;YACXE,UAAU,EAAE3B,KAAK,CAAC4B,IAAI,CAACD,UAAU,CAACE;UACpC,CAAC;UACDC,IAAI,EAAE7C,QAAQ,CAAC;YACb8C,MAAM,EAAE,CAAC;YACT;YACAC,KAAK,EAAEhC,KAAK,CAAC4B,IAAI,CAACb,OAAO,CAACkB,IAAI,CAACC,OAAO;YACtCC,UAAU,EAAEnC,KAAK,CAAC4B,IAAI,CAACO,UAAU,CAACL;UACpC,CAAC,EAAE9B,KAAK,CAACoC,UAAU,CAACnB,sBAAsB,CAAC,EAAE;YAC3CoB,eAAe,EAAErC,KAAK,CAAC4B,IAAI,CAACb,OAAO,CAACuB,UAAU,CAACR,IAAI;YACnD,cAAc,EAAE;cACd;cACAO,eAAe,EAAErC,KAAK,CAAC4B,IAAI,CAACb,OAAO,CAACwB,MAAM,CAACC;YAC7C,CAAC;YACD;YACA;YACA,aAAa,EAAE;cACbH,eAAe,EAAErC,KAAK,CAAC4B,IAAI,CAACb,OAAO,CAACuB,UAAU,CAACG;YACjD;UACF,CAAC;QACH,CAAC,EAAEtC,iBAAiB,CAAC;MACvB,CAAC;MACDd,YAAY,EAAEA;IAChB,CAAC,CAAC,EAAEO,QAAQ;EACd,CAAC,CAAC;AACJ;AACA8C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGlD,WAAW,CAACmD,SAAS,CAAC,yBAAyB;EACrF;EACA;EACA;EACA;EACA;AACF;AACA;EACEjD,QAAQ,EAAET,SAAS,CAAC2D,IAAI;EACxB;AACF;AACA;AACA;AACA;AACA;AACA;EACEjD,kBAAkB,EAAEV,SAAS,CAAC4D;AAChC,CAAC,GAAG,KAAK,CAAC;AACV,eAAerD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}