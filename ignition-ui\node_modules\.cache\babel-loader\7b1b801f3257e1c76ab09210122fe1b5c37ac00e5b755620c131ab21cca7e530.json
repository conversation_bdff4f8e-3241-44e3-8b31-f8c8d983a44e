{"ast": null, "code": "import getBasePlacement from \"./getBasePlacement.js\";\nimport getVariation from \"./getVariation.js\";\nimport getMainAxisFromPlacement from \"./getMainAxisFromPlacement.js\";\nimport { top, right, bottom, left, start, end } from \"../enums.js\";\nexport default function computeOffsets(_ref) {\n  var reference = _ref.reference,\n    element = _ref.element,\n    placement = _ref.placement;\n  var basePlacement = placement ? getBasePlacement(placement) : null;\n  var variation = placement ? getVariation(placement) : null;\n  var commonX = reference.x + reference.width / 2 - element.width / 2;\n  var commonY = reference.y + reference.height / 2 - element.height / 2;\n  var offsets;\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height\n      };\n      break;\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY\n      };\n      break;\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;\n  if (mainAxis != null) {\n    var len = mainAxis === 'y' ? 'height' : 'width';\n    switch (variation) {\n      case start:\n        offsets[mainAxis] = offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n      case end:\n        offsets[mainAxis] = offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n      default:\n    }\n  }\n  return offsets;\n}", "map": {"version": 3, "names": ["getBasePlacement", "getVariation", "getMainAxisFromPlacement", "top", "right", "bottom", "left", "start", "end", "computeOffsets", "_ref", "reference", "element", "placement", "basePlacement", "variation", "commonX", "x", "width", "commonY", "y", "height", "offsets", "mainAxis", "len"], "sources": ["C:/ignition/ignition-ui/node_modules/@popperjs/core/lib/utils/computeOffsets.js"], "sourcesContent": ["import getBasePlacement from \"./getBasePlacement.js\";\nimport getVariation from \"./getVariation.js\";\nimport getMainAxisFromPlacement from \"./getMainAxisFromPlacement.js\";\nimport { top, right, bottom, left, start, end } from \"../enums.js\";\nexport default function computeOffsets(_ref) {\n  var reference = _ref.reference,\n      element = _ref.element,\n      placement = _ref.placement;\n  var basePlacement = placement ? getBasePlacement(placement) : null;\n  var variation = placement ? getVariation(placement) : null;\n  var commonX = reference.x + reference.width / 2 - element.width / 2;\n  var commonY = reference.y + reference.height / 2 - element.height / 2;\n  var offsets;\n\n  switch (basePlacement) {\n    case top:\n      offsets = {\n        x: commonX,\n        y: reference.y - element.height\n      };\n      break;\n\n    case bottom:\n      offsets = {\n        x: commonX,\n        y: reference.y + reference.height\n      };\n      break;\n\n    case right:\n      offsets = {\n        x: reference.x + reference.width,\n        y: commonY\n      };\n      break;\n\n    case left:\n      offsets = {\n        x: reference.x - element.width,\n        y: commonY\n      };\n      break;\n\n    default:\n      offsets = {\n        x: reference.x,\n        y: reference.y\n      };\n  }\n\n  var mainAxis = basePlacement ? getMainAxisFromPlacement(basePlacement) : null;\n\n  if (mainAxis != null) {\n    var len = mainAxis === 'y' ? 'height' : 'width';\n\n    switch (variation) {\n      case start:\n        offsets[mainAxis] = offsets[mainAxis] - (reference[len] / 2 - element[len] / 2);\n        break;\n\n      case end:\n        offsets[mainAxis] = offsets[mainAxis] + (reference[len] / 2 - element[len] / 2);\n        break;\n\n      default:\n    }\n  }\n\n  return offsets;\n}"], "mappings": "AAAA,OAAOA,gBAAgB,MAAM,uBAAuB;AACpD,OAAOC,YAAY,MAAM,mBAAmB;AAC5C,OAAOC,wBAAwB,MAAM,+BAA+B;AACpE,SAASC,GAAG,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,GAAG,QAAQ,aAAa;AAClE,eAAe,SAASC,cAAcA,CAACC,IAAI,EAAE;EAC3C,IAAIC,SAAS,GAAGD,IAAI,CAACC,SAAS;IAC1BC,OAAO,GAAGF,IAAI,CAACE,OAAO;IACtBC,SAAS,GAAGH,IAAI,CAACG,SAAS;EAC9B,IAAIC,aAAa,GAAGD,SAAS,GAAGb,gBAAgB,CAACa,SAAS,CAAC,GAAG,IAAI;EAClE,IAAIE,SAAS,GAAGF,SAAS,GAAGZ,YAAY,CAACY,SAAS,CAAC,GAAG,IAAI;EAC1D,IAAIG,OAAO,GAAGL,SAAS,CAACM,CAAC,GAAGN,SAAS,CAACO,KAAK,GAAG,CAAC,GAAGN,OAAO,CAACM,KAAK,GAAG,CAAC;EACnE,IAAIC,OAAO,GAAGR,SAAS,CAACS,CAAC,GAAGT,SAAS,CAACU,MAAM,GAAG,CAAC,GAAGT,OAAO,CAACS,MAAM,GAAG,CAAC;EACrE,IAAIC,OAAO;EAEX,QAAQR,aAAa;IACnB,KAAKX,GAAG;MACNmB,OAAO,GAAG;QACRL,CAAC,EAAED,OAAO;QACVI,CAAC,EAAET,SAAS,CAACS,CAAC,GAAGR,OAAO,CAACS;MAC3B,CAAC;MACD;IAEF,KAAKhB,MAAM;MACTiB,OAAO,GAAG;QACRL,CAAC,EAAED,OAAO;QACVI,CAAC,EAAET,SAAS,CAACS,CAAC,GAAGT,SAAS,CAACU;MAC7B,CAAC;MACD;IAEF,KAAKjB,KAAK;MACRkB,OAAO,GAAG;QACRL,CAAC,EAAEN,SAAS,CAACM,CAAC,GAAGN,SAAS,CAACO,KAAK;QAChCE,CAAC,EAAED;MACL,CAAC;MACD;IAEF,KAAKb,IAAI;MACPgB,OAAO,GAAG;QACRL,CAAC,EAAEN,SAAS,CAACM,CAAC,GAAGL,OAAO,CAACM,KAAK;QAC9BE,CAAC,EAAED;MACL,CAAC;MACD;IAEF;MACEG,OAAO,GAAG;QACRL,CAAC,EAAEN,SAAS,CAACM,CAAC;QACdG,CAAC,EAAET,SAAS,CAACS;MACf,CAAC;EACL;EAEA,IAAIG,QAAQ,GAAGT,aAAa,GAAGZ,wBAAwB,CAACY,aAAa,CAAC,GAAG,IAAI;EAE7E,IAAIS,QAAQ,IAAI,IAAI,EAAE;IACpB,IAAIC,GAAG,GAAGD,QAAQ,KAAK,GAAG,GAAG,QAAQ,GAAG,OAAO;IAE/C,QAAQR,SAAS;MACf,KAAKR,KAAK;QACRe,OAAO,CAACC,QAAQ,CAAC,GAAGD,OAAO,CAACC,QAAQ,CAAC,IAAIZ,SAAS,CAACa,GAAG,CAAC,GAAG,CAAC,GAAGZ,OAAO,CAACY,GAAG,CAAC,GAAG,CAAC,CAAC;QAC/E;MAEF,KAAKhB,GAAG;QACNc,OAAO,CAACC,QAAQ,CAAC,GAAGD,OAAO,CAACC,QAAQ,CAAC,IAAIZ,SAAS,CAACa,GAAG,CAAC,GAAG,CAAC,GAAGZ,OAAO,CAACY,GAAG,CAAC,GAAG,CAAC,CAAC;QAC/E;MAEF;IACF;EACF;EAEA,OAAOF,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}