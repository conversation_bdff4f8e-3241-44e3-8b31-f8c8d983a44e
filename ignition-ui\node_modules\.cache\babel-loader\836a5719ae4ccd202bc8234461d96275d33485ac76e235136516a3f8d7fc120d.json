{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useForkRef as useForkRef, unstable_useIsFocusVisible as useIsFocusVisible } from '@mui/utils';\nimport { extractEventHandlers } from '../utils/extractEventHandlers';\nimport { useRootElementName } from '../utils/useRootElementName';\n/**\n *\n * Demos:\n *\n * - [Button](https://mui.com/base-ui/react-button/#hook)\n *\n * API:\n *\n * - [useButton API](https://mui.com/base-ui/react-button/hooks-api/#use-button)\n */\nexport function useButton() {\n  let parameters = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  const {\n    disabled = false,\n    focusableWhenDisabled,\n    href,\n    rootRef: externalRef,\n    tabIndex,\n    to,\n    type,\n    rootElementName: rootElementNameProp\n  } = parameters;\n  const buttonRef = React.useRef(null);\n  const [active, setActive] = React.useState(false);\n  const {\n    isFocusVisibleRef,\n    onFocus: handleFocusVisible,\n    onBlur: handleBlurVisible,\n    ref: focusVisibleRef\n  } = useIsFocusVisible();\n  const [focusVisible, setFocusVisible] = React.useState(false);\n  if (disabled && !focusableWhenDisabled && focusVisible) {\n    setFocusVisible(false);\n  }\n  React.useEffect(() => {\n    isFocusVisibleRef.current = focusVisible;\n  }, [focusVisible, isFocusVisibleRef]);\n  const [rootElementName, updateRootElementName] = useRootElementName({\n    rootElementName: rootElementNameProp != null ? rootElementNameProp : href || to ? 'a' : undefined,\n    componentName: 'Button'\n  });\n  const createHandleMouseLeave = otherHandlers => event => {\n    var _otherHandlers$onMous;\n    if (focusVisible) {\n      event.preventDefault();\n    }\n    (_otherHandlers$onMous = otherHandlers.onMouseLeave) == null || _otherHandlers$onMous.call(otherHandlers, event);\n  };\n  const createHandleBlur = otherHandlers => event => {\n    var _otherHandlers$onBlur;\n    handleBlurVisible(event);\n    if (isFocusVisibleRef.current === false) {\n      setFocusVisible(false);\n    }\n    (_otherHandlers$onBlur = otherHandlers.onBlur) == null || _otherHandlers$onBlur.call(otherHandlers, event);\n  };\n  const createHandleFocus = otherHandlers => event => {\n    var _otherHandlers$onFocu2;\n    // Fix for https://github.com/facebook/react/issues/7769\n    if (!buttonRef.current) {\n      buttonRef.current = event.currentTarget;\n    }\n    handleFocusVisible(event);\n    if (isFocusVisibleRef.current === true) {\n      var _otherHandlers$onFocu;\n      setFocusVisible(true);\n      (_otherHandlers$onFocu = otherHandlers.onFocusVisible) == null || _otherHandlers$onFocu.call(otherHandlers, event);\n    }\n    (_otherHandlers$onFocu2 = otherHandlers.onFocus) == null || _otherHandlers$onFocu2.call(otherHandlers, event);\n  };\n  const isNativeButton = () => {\n    const button = buttonRef.current;\n    return rootElementName === 'BUTTON' || rootElementName === 'INPUT' && ['button', 'submit', 'reset'].includes(button == null ? void 0 : button.type) || rootElementName === 'A' && (button == null ? void 0 : button.href);\n  };\n  const createHandleClick = otherHandlers => event => {\n    if (!disabled) {\n      var _otherHandlers$onClic;\n      (_otherHandlers$onClic = otherHandlers.onClick) == null || _otherHandlers$onClic.call(otherHandlers, event);\n    }\n  };\n  const createHandleMouseDown = otherHandlers => event => {\n    var _otherHandlers$onMous2;\n    if (!disabled) {\n      setActive(true);\n      document.addEventListener('mouseup', () => {\n        setActive(false);\n      }, {\n        once: true\n      });\n    }\n    (_otherHandlers$onMous2 = otherHandlers.onMouseDown) == null || _otherHandlers$onMous2.call(otherHandlers, event);\n  };\n  const createHandleKeyDown = otherHandlers => event => {\n    var _otherHandlers$onKeyD;\n    (_otherHandlers$onKeyD = otherHandlers.onKeyDown) == null || _otherHandlers$onKeyD.call(otherHandlers, event);\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    if (event.target === event.currentTarget && !isNativeButton() && event.key === ' ') {\n      event.preventDefault();\n    }\n    if (event.target === event.currentTarget && event.key === ' ' && !disabled) {\n      setActive(true);\n    }\n\n    // Keyboard accessibility for non interactive elements\n    if (event.target === event.currentTarget && !isNativeButton() && event.key === 'Enter' && !disabled) {\n      var _otherHandlers$onClic2;\n      (_otherHandlers$onClic2 = otherHandlers.onClick) == null || _otherHandlers$onClic2.call(otherHandlers, event);\n      event.preventDefault();\n    }\n  };\n  const createHandleKeyUp = otherHandlers => event => {\n    var _otherHandlers$onKeyU;\n    // calling preventDefault in keyUp on a <button> will not dispatch a click event if Space is pressed\n    // https://codesandbox.io/p/sandbox/button-keyup-preventdefault-dn7f0\n\n    if (event.target === event.currentTarget) {\n      setActive(false);\n    }\n    (_otherHandlers$onKeyU = otherHandlers.onKeyUp) == null || _otherHandlers$onKeyU.call(otherHandlers, event);\n\n    // Keyboard accessibility for non interactive elements\n    if (event.target === event.currentTarget && !isNativeButton() && !disabled && event.key === ' ' && !event.defaultMuiPrevented) {\n      var _otherHandlers$onClic3;\n      (_otherHandlers$onClic3 = otherHandlers.onClick) == null || _otherHandlers$onClic3.call(otherHandlers, event);\n    }\n  };\n  const handleRef = useForkRef(updateRootElementName, externalRef, focusVisibleRef, buttonRef);\n  const buttonProps = {};\n  if (tabIndex !== undefined) {\n    buttonProps.tabIndex = tabIndex;\n  }\n  if (rootElementName === 'BUTTON') {\n    buttonProps.type = type != null ? type : 'button';\n    if (focusableWhenDisabled) {\n      buttonProps['aria-disabled'] = disabled;\n    } else {\n      buttonProps.disabled = disabled;\n    }\n  } else if (rootElementName === 'INPUT') {\n    if (type && ['button', 'submit', 'reset'].includes(type)) {\n      if (focusableWhenDisabled) {\n        buttonProps['aria-disabled'] = disabled;\n      } else {\n        buttonProps.disabled = disabled;\n      }\n    }\n  } else if (rootElementName !== '') {\n    if (!href && !to) {\n      buttonProps.role = 'button';\n      buttonProps.tabIndex = tabIndex != null ? tabIndex : 0;\n    }\n    if (disabled) {\n      buttonProps['aria-disabled'] = disabled;\n      buttonProps.tabIndex = focusableWhenDisabled ? tabIndex != null ? tabIndex : 0 : -1;\n    }\n  }\n  const getRootProps = function () {\n    let externalProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    const externalEventHandlers = _extends({}, extractEventHandlers(parameters), extractEventHandlers(externalProps));\n    const props = _extends({\n      type\n    }, externalEventHandlers, buttonProps, externalProps, {\n      onBlur: createHandleBlur(externalEventHandlers),\n      onClick: createHandleClick(externalEventHandlers),\n      onFocus: createHandleFocus(externalEventHandlers),\n      onKeyDown: createHandleKeyDown(externalEventHandlers),\n      onKeyUp: createHandleKeyUp(externalEventHandlers),\n      onMouseDown: createHandleMouseDown(externalEventHandlers),\n      onMouseLeave: createHandleMouseLeave(externalEventHandlers),\n      ref: handleRef\n    });\n\n    // onFocusVisible can be present on the props or parameters,\n    // but it's not a valid React event handler so it must not be forwarded to the inner component.\n    // If present, it will be handled by the focus handler.\n    delete props.onFocusVisible;\n    return props;\n  };\n  return {\n    getRootProps,\n    focusVisible,\n    setFocusVisible,\n    active,\n    rootRef: handleRef\n  };\n}", "map": {"version": 3, "names": ["_extends", "React", "unstable_useForkRef", "useForkRef", "unstable_useIsFocusVisible", "useIsFocusVisible", "extractEventHandlers", "useRootElementName", "useButton", "parameters", "arguments", "length", "undefined", "disabled", "focusableWhenDisabled", "href", "rootRef", "externalRef", "tabIndex", "to", "type", "rootElementName", "rootElementNameProp", "buttonRef", "useRef", "active", "setActive", "useState", "isFocusVisibleRef", "onFocus", "handleFocusVisible", "onBlur", "handleBlurVisible", "ref", "focusVisibleRef", "focusVisible", "setFocusVisible", "useEffect", "current", "updateRootElementName", "componentName", "createHandleMouseLeave", "otherHandlers", "event", "_otherHandlers$onMous", "preventDefault", "onMouseLeave", "call", "createHandleBlur", "_otherHandlers$onBlur", "createHandleFocus", "_otherHandlers$onFocu2", "currentTarget", "_otherHandlers$onFocu", "onFocusVisible", "isNativeButton", "button", "includes", "createHandleClick", "_otherHandlers$onClic", "onClick", "createHandleMouseDown", "_otherHandlers$onMous2", "document", "addEventListener", "once", "onMouseDown", "createHandleKeyDown", "_otherHandlers$onKeyD", "onKeyDown", "defaultMuiPrevented", "target", "key", "_otherHandlers$onClic2", "createHandleKeyUp", "_otherHandlers$onKeyU", "onKeyUp", "_otherHandlers$onClic3", "handleRef", "buttonProps", "role", "getRootProps", "externalProps", "externalEventHandlers", "props"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/useButton/useButton.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useForkRef as useForkRef, unstable_useIsFocusVisible as useIsFocusVisible } from '@mui/utils';\nimport { extractEventHandlers } from '../utils/extractEventHandlers';\nimport { useRootElementName } from '../utils/useRootElementName';\n/**\n *\n * Demos:\n *\n * - [Button](https://mui.com/base-ui/react-button/#hook)\n *\n * API:\n *\n * - [useButton API](https://mui.com/base-ui/react-button/hooks-api/#use-button)\n */\nexport function useButton(parameters = {}) {\n  const {\n    disabled = false,\n    focusableWhenDisabled,\n    href,\n    rootRef: externalRef,\n    tabIndex,\n    to,\n    type,\n    rootElementName: rootElementNameProp\n  } = parameters;\n  const buttonRef = React.useRef(null);\n  const [active, setActive] = React.useState(false);\n  const {\n    isFocusVisibleRef,\n    onFocus: handleFocusVisible,\n    onBlur: handleBlurVisible,\n    ref: focusVisibleRef\n  } = useIsFocusVisible();\n  const [focusVisible, setFocusVisible] = React.useState(false);\n  if (disabled && !focusableWhenDisabled && focusVisible) {\n    setFocusVisible(false);\n  }\n  React.useEffect(() => {\n    isFocusVisibleRef.current = focusVisible;\n  }, [focusVisible, isFocusVisibleRef]);\n  const [rootElementName, updateRootElementName] = useRootElementName({\n    rootElementName: rootElementNameProp != null ? rootElementNameProp : href || to ? 'a' : undefined,\n    componentName: 'Button'\n  });\n  const createHandleMouseLeave = otherHandlers => event => {\n    var _otherHandlers$onMous;\n    if (focusVisible) {\n      event.preventDefault();\n    }\n    (_otherHandlers$onMous = otherHandlers.onMouseLeave) == null || _otherHandlers$onMous.call(otherHandlers, event);\n  };\n  const createHandleBlur = otherHandlers => event => {\n    var _otherHandlers$onBlur;\n    handleBlurVisible(event);\n    if (isFocusVisibleRef.current === false) {\n      setFocusVisible(false);\n    }\n    (_otherHandlers$onBlur = otherHandlers.onBlur) == null || _otherHandlers$onBlur.call(otherHandlers, event);\n  };\n  const createHandleFocus = otherHandlers => event => {\n    var _otherHandlers$onFocu2;\n    // Fix for https://github.com/facebook/react/issues/7769\n    if (!buttonRef.current) {\n      buttonRef.current = event.currentTarget;\n    }\n    handleFocusVisible(event);\n    if (isFocusVisibleRef.current === true) {\n      var _otherHandlers$onFocu;\n      setFocusVisible(true);\n      (_otherHandlers$onFocu = otherHandlers.onFocusVisible) == null || _otherHandlers$onFocu.call(otherHandlers, event);\n    }\n    (_otherHandlers$onFocu2 = otherHandlers.onFocus) == null || _otherHandlers$onFocu2.call(otherHandlers, event);\n  };\n  const isNativeButton = () => {\n    const button = buttonRef.current;\n    return rootElementName === 'BUTTON' || rootElementName === 'INPUT' && ['button', 'submit', 'reset'].includes(button == null ? void 0 : button.type) || rootElementName === 'A' && (button == null ? void 0 : button.href);\n  };\n  const createHandleClick = otherHandlers => event => {\n    if (!disabled) {\n      var _otherHandlers$onClic;\n      (_otherHandlers$onClic = otherHandlers.onClick) == null || _otherHandlers$onClic.call(otherHandlers, event);\n    }\n  };\n  const createHandleMouseDown = otherHandlers => event => {\n    var _otherHandlers$onMous2;\n    if (!disabled) {\n      setActive(true);\n      document.addEventListener('mouseup', () => {\n        setActive(false);\n      }, {\n        once: true\n      });\n    }\n    (_otherHandlers$onMous2 = otherHandlers.onMouseDown) == null || _otherHandlers$onMous2.call(otherHandlers, event);\n  };\n  const createHandleKeyDown = otherHandlers => event => {\n    var _otherHandlers$onKeyD;\n    (_otherHandlers$onKeyD = otherHandlers.onKeyDown) == null || _otherHandlers$onKeyD.call(otherHandlers, event);\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    if (event.target === event.currentTarget && !isNativeButton() && event.key === ' ') {\n      event.preventDefault();\n    }\n    if (event.target === event.currentTarget && event.key === ' ' && !disabled) {\n      setActive(true);\n    }\n\n    // Keyboard accessibility for non interactive elements\n    if (event.target === event.currentTarget && !isNativeButton() && event.key === 'Enter' && !disabled) {\n      var _otherHandlers$onClic2;\n      (_otherHandlers$onClic2 = otherHandlers.onClick) == null || _otherHandlers$onClic2.call(otherHandlers, event);\n      event.preventDefault();\n    }\n  };\n  const createHandleKeyUp = otherHandlers => event => {\n    var _otherHandlers$onKeyU;\n    // calling preventDefault in keyUp on a <button> will not dispatch a click event if Space is pressed\n    // https://codesandbox.io/p/sandbox/button-keyup-preventdefault-dn7f0\n\n    if (event.target === event.currentTarget) {\n      setActive(false);\n    }\n    (_otherHandlers$onKeyU = otherHandlers.onKeyUp) == null || _otherHandlers$onKeyU.call(otherHandlers, event);\n\n    // Keyboard accessibility for non interactive elements\n    if (event.target === event.currentTarget && !isNativeButton() && !disabled && event.key === ' ' && !event.defaultMuiPrevented) {\n      var _otherHandlers$onClic3;\n      (_otherHandlers$onClic3 = otherHandlers.onClick) == null || _otherHandlers$onClic3.call(otherHandlers, event);\n    }\n  };\n  const handleRef = useForkRef(updateRootElementName, externalRef, focusVisibleRef, buttonRef);\n  const buttonProps = {};\n  if (tabIndex !== undefined) {\n    buttonProps.tabIndex = tabIndex;\n  }\n  if (rootElementName === 'BUTTON') {\n    buttonProps.type = type != null ? type : 'button';\n    if (focusableWhenDisabled) {\n      buttonProps['aria-disabled'] = disabled;\n    } else {\n      buttonProps.disabled = disabled;\n    }\n  } else if (rootElementName === 'INPUT') {\n    if (type && ['button', 'submit', 'reset'].includes(type)) {\n      if (focusableWhenDisabled) {\n        buttonProps['aria-disabled'] = disabled;\n      } else {\n        buttonProps.disabled = disabled;\n      }\n    }\n  } else if (rootElementName !== '') {\n    if (!href && !to) {\n      buttonProps.role = 'button';\n      buttonProps.tabIndex = tabIndex != null ? tabIndex : 0;\n    }\n    if (disabled) {\n      buttonProps['aria-disabled'] = disabled;\n      buttonProps.tabIndex = focusableWhenDisabled ? tabIndex != null ? tabIndex : 0 : -1;\n    }\n  }\n  const getRootProps = (externalProps = {}) => {\n    const externalEventHandlers = _extends({}, extractEventHandlers(parameters), extractEventHandlers(externalProps));\n    const props = _extends({\n      type\n    }, externalEventHandlers, buttonProps, externalProps, {\n      onBlur: createHandleBlur(externalEventHandlers),\n      onClick: createHandleClick(externalEventHandlers),\n      onFocus: createHandleFocus(externalEventHandlers),\n      onKeyDown: createHandleKeyDown(externalEventHandlers),\n      onKeyUp: createHandleKeyUp(externalEventHandlers),\n      onMouseDown: createHandleMouseDown(externalEventHandlers),\n      onMouseLeave: createHandleMouseLeave(externalEventHandlers),\n      ref: handleRef\n    });\n\n    // onFocusVisible can be present on the props or parameters,\n    // but it's not a valid React event handler so it must not be forwarded to the inner component.\n    // If present, it will be handled by the focus handler.\n    delete props.onFocusVisible;\n    return props;\n  };\n  return {\n    getRootProps,\n    focusVisible,\n    setFocusVisible,\n    active,\n    rootRef: handleRef\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,mBAAmB,IAAIC,UAAU,EAAEC,0BAA0B,IAAIC,iBAAiB,QAAQ,YAAY;AAC/G,SAASC,oBAAoB,QAAQ,+BAA+B;AACpE,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAAA,EAAkB;EAAA,IAAjBC,UAAU,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACvC,MAAM;IACJG,QAAQ,GAAG,KAAK;IAChBC,qBAAqB;IACrBC,IAAI;IACJC,OAAO,EAAEC,WAAW;IACpBC,QAAQ;IACRC,EAAE;IACFC,IAAI;IACJC,eAAe,EAAEC;EACnB,CAAC,GAAGb,UAAU;EACd,MAAMc,SAAS,GAAGtB,KAAK,CAACuB,MAAM,CAAC,IAAI,CAAC;EACpC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGzB,KAAK,CAAC0B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM;IACJC,iBAAiB;IACjBC,OAAO,EAAEC,kBAAkB;IAC3BC,MAAM,EAAEC,iBAAiB;IACzBC,GAAG,EAAEC;EACP,CAAC,GAAG7B,iBAAiB,CAAC,CAAC;EACvB,MAAM,CAAC8B,YAAY,EAAEC,eAAe,CAAC,GAAGnC,KAAK,CAAC0B,QAAQ,CAAC,KAAK,CAAC;EAC7D,IAAId,QAAQ,IAAI,CAACC,qBAAqB,IAAIqB,YAAY,EAAE;IACtDC,eAAe,CAAC,KAAK,CAAC;EACxB;EACAnC,KAAK,CAACoC,SAAS,CAAC,MAAM;IACpBT,iBAAiB,CAACU,OAAO,GAAGH,YAAY;EAC1C,CAAC,EAAE,CAACA,YAAY,EAAEP,iBAAiB,CAAC,CAAC;EACrC,MAAM,CAACP,eAAe,EAAEkB,qBAAqB,CAAC,GAAGhC,kBAAkB,CAAC;IAClEc,eAAe,EAAEC,mBAAmB,IAAI,IAAI,GAAGA,mBAAmB,GAAGP,IAAI,IAAII,EAAE,GAAG,GAAG,GAAGP,SAAS;IACjG4B,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,MAAMC,sBAAsB,GAAGC,aAAa,IAAIC,KAAK,IAAI;IACvD,IAAIC,qBAAqB;IACzB,IAAIT,YAAY,EAAE;MAChBQ,KAAK,CAACE,cAAc,CAAC,CAAC;IACxB;IACA,CAACD,qBAAqB,GAAGF,aAAa,CAACI,YAAY,KAAK,IAAI,IAAIF,qBAAqB,CAACG,IAAI,CAACL,aAAa,EAAEC,KAAK,CAAC;EAClH,CAAC;EACD,MAAMK,gBAAgB,GAAGN,aAAa,IAAIC,KAAK,IAAI;IACjD,IAAIM,qBAAqB;IACzBjB,iBAAiB,CAACW,KAAK,CAAC;IACxB,IAAIf,iBAAiB,CAACU,OAAO,KAAK,KAAK,EAAE;MACvCF,eAAe,CAAC,KAAK,CAAC;IACxB;IACA,CAACa,qBAAqB,GAAGP,aAAa,CAACX,MAAM,KAAK,IAAI,IAAIkB,qBAAqB,CAACF,IAAI,CAACL,aAAa,EAAEC,KAAK,CAAC;EAC5G,CAAC;EACD,MAAMO,iBAAiB,GAAGR,aAAa,IAAIC,KAAK,IAAI;IAClD,IAAIQ,sBAAsB;IAC1B;IACA,IAAI,CAAC5B,SAAS,CAACe,OAAO,EAAE;MACtBf,SAAS,CAACe,OAAO,GAAGK,KAAK,CAACS,aAAa;IACzC;IACAtB,kBAAkB,CAACa,KAAK,CAAC;IACzB,IAAIf,iBAAiB,CAACU,OAAO,KAAK,IAAI,EAAE;MACtC,IAAIe,qBAAqB;MACzBjB,eAAe,CAAC,IAAI,CAAC;MACrB,CAACiB,qBAAqB,GAAGX,aAAa,CAACY,cAAc,KAAK,IAAI,IAAID,qBAAqB,CAACN,IAAI,CAACL,aAAa,EAAEC,KAAK,CAAC;IACpH;IACA,CAACQ,sBAAsB,GAAGT,aAAa,CAACb,OAAO,KAAK,IAAI,IAAIsB,sBAAsB,CAACJ,IAAI,CAACL,aAAa,EAAEC,KAAK,CAAC;EAC/G,CAAC;EACD,MAAMY,cAAc,GAAGA,CAAA,KAAM;IAC3B,MAAMC,MAAM,GAAGjC,SAAS,CAACe,OAAO;IAChC,OAAOjB,eAAe,KAAK,QAAQ,IAAIA,eAAe,KAAK,OAAO,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC,CAACoC,QAAQ,CAACD,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACpC,IAAI,CAAC,IAAIC,eAAe,KAAK,GAAG,KAAKmC,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACzC,IAAI,CAAC;EAC3N,CAAC;EACD,MAAM2C,iBAAiB,GAAGhB,aAAa,IAAIC,KAAK,IAAI;IAClD,IAAI,CAAC9B,QAAQ,EAAE;MACb,IAAI8C,qBAAqB;MACzB,CAACA,qBAAqB,GAAGjB,aAAa,CAACkB,OAAO,KAAK,IAAI,IAAID,qBAAqB,CAACZ,IAAI,CAACL,aAAa,EAAEC,KAAK,CAAC;IAC7G;EACF,CAAC;EACD,MAAMkB,qBAAqB,GAAGnB,aAAa,IAAIC,KAAK,IAAI;IACtD,IAAImB,sBAAsB;IAC1B,IAAI,CAACjD,QAAQ,EAAE;MACba,SAAS,CAAC,IAAI,CAAC;MACfqC,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAE,MAAM;QACzCtC,SAAS,CAAC,KAAK,CAAC;MAClB,CAAC,EAAE;QACDuC,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;IACA,CAACH,sBAAsB,GAAGpB,aAAa,CAACwB,WAAW,KAAK,IAAI,IAAIJ,sBAAsB,CAACf,IAAI,CAACL,aAAa,EAAEC,KAAK,CAAC;EACnH,CAAC;EACD,MAAMwB,mBAAmB,GAAGzB,aAAa,IAAIC,KAAK,IAAI;IACpD,IAAIyB,qBAAqB;IACzB,CAACA,qBAAqB,GAAG1B,aAAa,CAAC2B,SAAS,KAAK,IAAI,IAAID,qBAAqB,CAACrB,IAAI,CAACL,aAAa,EAAEC,KAAK,CAAC;IAC7G,IAAIA,KAAK,CAAC2B,mBAAmB,EAAE;MAC7B;IACF;IACA,IAAI3B,KAAK,CAAC4B,MAAM,KAAK5B,KAAK,CAACS,aAAa,IAAI,CAACG,cAAc,CAAC,CAAC,IAAIZ,KAAK,CAAC6B,GAAG,KAAK,GAAG,EAAE;MAClF7B,KAAK,CAACE,cAAc,CAAC,CAAC;IACxB;IACA,IAAIF,KAAK,CAAC4B,MAAM,KAAK5B,KAAK,CAACS,aAAa,IAAIT,KAAK,CAAC6B,GAAG,KAAK,GAAG,IAAI,CAAC3D,QAAQ,EAAE;MAC1Ea,SAAS,CAAC,IAAI,CAAC;IACjB;;IAEA;IACA,IAAIiB,KAAK,CAAC4B,MAAM,KAAK5B,KAAK,CAACS,aAAa,IAAI,CAACG,cAAc,CAAC,CAAC,IAAIZ,KAAK,CAAC6B,GAAG,KAAK,OAAO,IAAI,CAAC3D,QAAQ,EAAE;MACnG,IAAI4D,sBAAsB;MAC1B,CAACA,sBAAsB,GAAG/B,aAAa,CAACkB,OAAO,KAAK,IAAI,IAAIa,sBAAsB,CAAC1B,IAAI,CAACL,aAAa,EAAEC,KAAK,CAAC;MAC7GA,KAAK,CAACE,cAAc,CAAC,CAAC;IACxB;EACF,CAAC;EACD,MAAM6B,iBAAiB,GAAGhC,aAAa,IAAIC,KAAK,IAAI;IAClD,IAAIgC,qBAAqB;IACzB;IACA;;IAEA,IAAIhC,KAAK,CAAC4B,MAAM,KAAK5B,KAAK,CAACS,aAAa,EAAE;MACxC1B,SAAS,CAAC,KAAK,CAAC;IAClB;IACA,CAACiD,qBAAqB,GAAGjC,aAAa,CAACkC,OAAO,KAAK,IAAI,IAAID,qBAAqB,CAAC5B,IAAI,CAACL,aAAa,EAAEC,KAAK,CAAC;;IAE3G;IACA,IAAIA,KAAK,CAAC4B,MAAM,KAAK5B,KAAK,CAACS,aAAa,IAAI,CAACG,cAAc,CAAC,CAAC,IAAI,CAAC1C,QAAQ,IAAI8B,KAAK,CAAC6B,GAAG,KAAK,GAAG,IAAI,CAAC7B,KAAK,CAAC2B,mBAAmB,EAAE;MAC7H,IAAIO,sBAAsB;MAC1B,CAACA,sBAAsB,GAAGnC,aAAa,CAACkB,OAAO,KAAK,IAAI,IAAIiB,sBAAsB,CAAC9B,IAAI,CAACL,aAAa,EAAEC,KAAK,CAAC;IAC/G;EACF,CAAC;EACD,MAAMmC,SAAS,GAAG3E,UAAU,CAACoC,qBAAqB,EAAEtB,WAAW,EAAEiB,eAAe,EAAEX,SAAS,CAAC;EAC5F,MAAMwD,WAAW,GAAG,CAAC,CAAC;EACtB,IAAI7D,QAAQ,KAAKN,SAAS,EAAE;IAC1BmE,WAAW,CAAC7D,QAAQ,GAAGA,QAAQ;EACjC;EACA,IAAIG,eAAe,KAAK,QAAQ,EAAE;IAChC0D,WAAW,CAAC3D,IAAI,GAAGA,IAAI,IAAI,IAAI,GAAGA,IAAI,GAAG,QAAQ;IACjD,IAAIN,qBAAqB,EAAE;MACzBiE,WAAW,CAAC,eAAe,CAAC,GAAGlE,QAAQ;IACzC,CAAC,MAAM;MACLkE,WAAW,CAAClE,QAAQ,GAAGA,QAAQ;IACjC;EACF,CAAC,MAAM,IAAIQ,eAAe,KAAK,OAAO,EAAE;IACtC,IAAID,IAAI,IAAI,CAAC,QAAQ,EAAE,QAAQ,EAAE,OAAO,CAAC,CAACqC,QAAQ,CAACrC,IAAI,CAAC,EAAE;MACxD,IAAIN,qBAAqB,EAAE;QACzBiE,WAAW,CAAC,eAAe,CAAC,GAAGlE,QAAQ;MACzC,CAAC,MAAM;QACLkE,WAAW,CAAClE,QAAQ,GAAGA,QAAQ;MACjC;IACF;EACF,CAAC,MAAM,IAAIQ,eAAe,KAAK,EAAE,EAAE;IACjC,IAAI,CAACN,IAAI,IAAI,CAACI,EAAE,EAAE;MAChB4D,WAAW,CAACC,IAAI,GAAG,QAAQ;MAC3BD,WAAW,CAAC7D,QAAQ,GAAGA,QAAQ,IAAI,IAAI,GAAGA,QAAQ,GAAG,CAAC;IACxD;IACA,IAAIL,QAAQ,EAAE;MACZkE,WAAW,CAAC,eAAe,CAAC,GAAGlE,QAAQ;MACvCkE,WAAW,CAAC7D,QAAQ,GAAGJ,qBAAqB,GAAGI,QAAQ,IAAI,IAAI,GAAGA,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;IACrF;EACF;EACA,MAAM+D,YAAY,GAAG,SAAAA,CAAA,EAAwB;IAAA,IAAvBC,aAAa,GAAAxE,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IACtC,MAAMyE,qBAAqB,GAAGnF,QAAQ,CAAC,CAAC,CAAC,EAAEM,oBAAoB,CAACG,UAAU,CAAC,EAAEH,oBAAoB,CAAC4E,aAAa,CAAC,CAAC;IACjH,MAAME,KAAK,GAAGpF,QAAQ,CAAC;MACrBoB;IACF,CAAC,EAAE+D,qBAAqB,EAAEJ,WAAW,EAAEG,aAAa,EAAE;MACpDnD,MAAM,EAAEiB,gBAAgB,CAACmC,qBAAqB,CAAC;MAC/CvB,OAAO,EAAEF,iBAAiB,CAACyB,qBAAqB,CAAC;MACjDtD,OAAO,EAAEqB,iBAAiB,CAACiC,qBAAqB,CAAC;MACjDd,SAAS,EAAEF,mBAAmB,CAACgB,qBAAqB,CAAC;MACrDP,OAAO,EAAEF,iBAAiB,CAACS,qBAAqB,CAAC;MACjDjB,WAAW,EAAEL,qBAAqB,CAACsB,qBAAqB,CAAC;MACzDrC,YAAY,EAAEL,sBAAsB,CAAC0C,qBAAqB,CAAC;MAC3DlD,GAAG,EAAE6C;IACP,CAAC,CAAC;;IAEF;IACA;IACA;IACA,OAAOM,KAAK,CAAC9B,cAAc;IAC3B,OAAO8B,KAAK;EACd,CAAC;EACD,OAAO;IACLH,YAAY;IACZ9C,YAAY;IACZC,eAAe;IACfX,MAAM;IACNT,OAAO,EAAE8D;EACX,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}