{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getModalOverflowUtilityClass(slot) {\n  return generateUtilityClass('MuiModalOverflow', slot);\n}\nconst modalOverflowClasses = generateUtilityClasses('MuiModalOverflow', ['root']);\nexport default modalOverflowClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getModalOverflowUtilityClass", "slot", "modalOverflowClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/ModalOverflow/modalOverflowClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getModalOverflowUtilityClass(slot) {\n  return generateUtilityClass('MuiModalOverflow', slot);\n}\nconst modalOverflowClasses = generateUtilityClasses('MuiModalOverflow', ['root']);\nexport default modalOverflowClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,4BAA4BA,CAACC,IAAI,EAAE;EACjD,OAAOH,oBAAoB,CAAC,kBAAkB,EAAEG,IAAI,CAAC;AACvD;AACA,MAAMC,oBAAoB,GAAGH,sBAAsB,CAAC,kBAAkB,EAAE,CAAC,MAAM,CAAC,CAAC;AACjF,eAAeG,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}