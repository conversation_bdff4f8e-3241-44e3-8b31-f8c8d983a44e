{"ast": null, "code": "var _jsxFileName = \"C:\\\\ignition\\\\ignition-ui\\\\src\\\\components\\\\Loading\\\\HexagonBallLoading.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { Box } from '@mui/material';\nimport Matter from 'matter-js';\nimport styles from './styles.module.scss';\n\n/**\r\n * Component displays loading screen with a bouncing ball inside a rotating hexagon\r\n * @param {Object} props - Component props\r\n * @param {boolean} props.fromCreatePlan - Flag to determine if component is called from CreatePlan page\r\n * @returns {JSX.Element} - HexagonBallLoading component\r\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HexagonBallLoading = _ref => {\n  _s();\n  let {\n    fromCreatePlan\n  } = _ref;\n  const canvasRef = useRef(null);\n  const engineRef = useRef(null);\n  const requestRef = useRef(null);\n  const ballRef = useRef(null);\n  const hexagonEdgesRef = useRef([]);\n  useEffect(() => {\n    // Realistic physics parameters\n    const rotationSpeed = 0.008; // Slower, more natural rotation\n    const gravity = 0.0008; // More realistic gravity strength\n    const ballRestitution = 0.85; // Realistic bounce with energy loss\n    const wallRestitution = 0.75; // Walls absorb some energy\n\n    // Initialize Matter.js modules\n    const Engine = Matter.Engine;\n    const Render = Matter.Render;\n    const World = Matter.World;\n    const Bodies = Matter.Bodies;\n    const Body = Matter.Body;\n\n    // Create engine with realistic physics\n    engineRef.current = Engine.create({\n      gravity: {\n        x: 0,\n        y: gravity,\n        scale: 1\n      }\n    });\n\n    // Set realistic timing for physics simulation\n    engineRef.current.timing.timeScale = 1;\n\n    // Create renderer\n    const render = Render.create({\n      canvas: canvasRef.current,\n      engine: engineRef.current,\n      options: {\n        width: 352,\n        height: 352,\n        wireframes: false,\n        background: '#000000',\n        showAngleIndicator: false,\n        showCollisions: false,\n        showVelocity: false\n      }\n    });\n\n    // Create hexagon\n    const hexagonRadius = 132;\n    const hexagonSides = 6;\n    const centerX = render.options.width / 2;\n    const centerY = render.options.height / 2;\n\n    // Create hexagon vertices\n    const hexagonVertices = [];\n    for (let i = 0; i < hexagonSides; i++) {\n      const angle = Math.PI * 2 * i / hexagonSides;\n      const x = hexagonRadius * Math.cos(angle);\n      const y = hexagonRadius * Math.sin(angle);\n      hexagonVertices.push({\n        x,\n        y\n      });\n    }\n\n    // Create hexagon edges with realistic physics\n    hexagonEdgesRef.current = [];\n    for (let i = 0; i < hexagonSides; i++) {\n      const j = (i + 1) % hexagonSides;\n      const edgeOptions = {\n        restitution: wallRestitution,\n        // Use realistic wall bounce\n        friction: 0.05,\n        // Reduced friction for smoother movement\n        frictionStatic: 0.1,\n        isStatic: true,\n        render: {\n          fillStyle: 'transparent',\n          strokeStyle: '#FFFFFF',\n          lineWidth: 2 // Slightly thicker for better visibility\n        }\n      };\n\n      // Calculate edge position and angle\n      const vertex1 = hexagonVertices[i];\n      const vertex2 = hexagonVertices[j];\n      const edgeLength = Math.sqrt(Math.pow(vertex2.x - vertex1.x, 2) + Math.pow(vertex2.y - vertex1.y, 2));\n      const edgeAngle = Math.atan2(vertex2.y - vertex1.y, vertex2.x - vertex1.x);\n      const edgeCenterX = centerX + (vertex1.x + vertex2.x) / 2;\n      const edgeCenterY = centerY + (vertex1.y + vertex2.y) / 2;\n\n      // Create edge\n      const edge = Bodies.rectangle(edgeCenterX, edgeCenterY, edgeLength, 1, edgeOptions);\n\n      // Rotate edge to angle\n      Body.rotate(edge, edgeAngle);\n      hexagonEdgesRef.current.push(edge);\n    }\n\n    // Create ball\n    const originalBallRadius = 15;\n    // Reduce by additional 10% from current size (72% * 0.9 = 64.8%)\n    const ballRadius = originalBallRadius * 0.648; // 72% * 0.9 = 64.8% of original size\n\n    // Place ball inside hexagon, closer to center\n    ballRef.current = Bodies.circle(centerX, centerY, ballRadius, {\n      restitution: restitution,\n      friction: 0.05,\n      frictionAir: 0.001,\n      density: 0.001,\n      render: {\n        fillStyle: '#F0A500'\n      }\n    });\n\n    // Add initial force to start ball movement\n    Body.applyForce(ballRef.current, ballRef.current.position, {\n      x: 0.0002,\n      y: -0.0002\n    });\n\n    // Add bodies to world\n    World.add(engineRef.current.world, [...hexagonEdgesRef.current, ballRef.current]);\n\n    // Run renderer\n    Render.run(render);\n\n    // Animation loop function\n    const animate = () => {\n      // Update engine\n      Engine.update(engineRef.current, 16.667);\n\n      // Rotate hexagon edges\n      if (hexagonEdgesRef.current.length > 0) {\n        hexagonEdgesRef.current.forEach(edge => {\n          // Rotate edge around hexagon center\n          Body.setPosition(edge, {\n            x: centerX + (edge.position.x - centerX) * Math.cos(rotationSpeed) - (edge.position.y - centerY) * Math.sin(rotationSpeed),\n            y: centerY + (edge.position.x - centerX) * Math.sin(rotationSpeed) + (edge.position.y - centerY) * Math.cos(rotationSpeed)\n          });\n          Body.rotate(edge, rotationSpeed);\n        });\n      }\n\n      // Check ball position\n      if (ballRef.current) {\n        const ballPos = ballRef.current.position;\n\n        // Calculate distance from ball to hexagon center\n        const distanceFromCenter = Math.sqrt(Math.pow(ballPos.x - centerX, 2) + Math.pow(ballPos.y - centerY, 2));\n\n        // If ball goes beyond hexagon safe boundary\n        const safeHexagonRadius = hexagonRadius * 0.85; // Safe boundary inside hexagon\n\n        if (distanceFromCenter > safeHexagonRadius) {\n          // Calculate vector from hexagon center to ball\n          const directionX = ballPos.x - centerX;\n          const directionY = ballPos.y - centerY;\n          const magnitude = Math.sqrt(directionX * directionX + directionY * directionY);\n\n          // Normalize vector\n          const normalizedX = directionX / magnitude;\n          const normalizedY = directionY / magnitude;\n\n          // Reset ball position to within safe boundary\n          const newPosX = centerX + normalizedX * safeHexagonRadius;\n          const newPosY = centerY + normalizedY * safeHexagonRadius;\n\n          // Set new ball position\n          Matter.Body.setPosition(ballRef.current, {\n            x: newPosX,\n            y: newPosY\n          });\n\n          // Adjust velocity for ball to bounce inward\n          const currentVelocity = ballRef.current.velocity;\n          const dotProduct = currentVelocity.x * normalizedX + currentVelocity.y * normalizedY;\n\n          // If ball is moving outward, reverse direction\n          if (dotProduct > 0) {\n            const newVelocityX = currentVelocity.x - 2 * dotProduct * normalizedX;\n            const newVelocityY = currentVelocity.y - 2 * dotProduct * normalizedY;\n            Matter.Body.setVelocity(ballRef.current, {\n              x: newVelocityX * restitution,\n              y: newVelocityY * restitution\n            });\n          }\n        }\n\n        // Ensure ball keeps moving\n        const velocity = ballRef.current.velocity;\n        const speed = Math.sqrt(velocity.x * velocity.x + velocity.y * velocity.y);\n\n        // If ball moves too slowly, add small force\n        if (speed < 0.5) {\n          const randomAngle = Math.random() * Math.PI * 2;\n          const forceMagnitude = 0.0001;\n          Body.applyForce(ballRef.current, ballRef.current.position, {\n            x: forceMagnitude * Math.cos(randomAngle),\n            y: forceMagnitude * Math.sin(randomAngle)\n          });\n        }\n      }\n\n      // Continue animation loop\n      requestRef.current = requestAnimationFrame(animate);\n    };\n\n    // Start animation loop\n    requestRef.current = requestAnimationFrame(animate);\n\n    // Cleanup when component unmounts\n    return () => {\n      // Cancel animation loop\n      if (requestRef.current) {\n        cancelAnimationFrame(requestRef.current);\n      }\n\n      // Cleanup renderer and engine\n      Render.stop(render);\n      World.clear(engineRef.current.world);\n      Engine.clear(engineRef.current);\n      render.canvas = null;\n      render.context = null;\n      render.textures = {};\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: styles.loadingContainer,\n    sx: {\n      ...(!fromCreatePlan && {\n        minHeight: '90vh'\n      })\n    },\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      className: styles.gameWrapper,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        className: styles.hexagonLoadingContainer,\n        children: /*#__PURE__*/_jsxDEV(\"canvas\", {\n          ref: canvasRef,\n          className: styles.hexagonCanvas\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 249,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 247,\n    columnNumber: 5\n  }, this);\n};\n_s(HexagonBallLoading, \"uDBlP5n4TAfWzOEW2LE7YQXblas=\");\n_c = HexagonBallLoading;\nexport default HexagonBallLoading;\nvar _c;\n$RefreshReg$(_c, \"HexagonBallLoading\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "Box", "Matter", "styles", "jsxDEV", "_jsxDEV", "HexagonBallLoading", "_ref", "_s", "fromCreatePlan", "canvasRef", "engineRef", "requestRef", "ballRef", "hexagonEdgesRef", "rotationSpeed", "gravity", "ballRestitution", "wallRestitution", "Engine", "Render", "World", "Bodies", "Body", "current", "create", "x", "y", "scale", "timing", "timeScale", "render", "canvas", "engine", "options", "width", "height", "wireframes", "background", "showAngleIndicator", "showCollisions", "showVelocity", "hexagonRadius", "hexagonSides", "centerX", "centerY", "hexagonVertices", "i", "angle", "Math", "PI", "cos", "sin", "push", "j", "edgeOptions", "restitution", "friction", "frictionStatic", "isStatic", "fillStyle", "strokeStyle", "lineWidth", "vertex1", "vertex2", "edge<PERSON><PERSON><PERSON>", "sqrt", "pow", "edgeAngle", "atan2", "edgeCenterX", "edgeCenterY", "edge", "rectangle", "rotate", "originalBallRadius", "ballRadius", "circle", "frictionAir", "density", "applyForce", "position", "add", "world", "run", "animate", "update", "length", "for<PERSON>ach", "setPosition", "ballPos", "distanceFromCenter", "safeHexagonRadius", "directionX", "directionY", "magnitude", "normalizedX", "normalizedY", "newPosX", "newPosY", "currentVelocity", "velocity", "dotProduct", "newVelocityX", "newVelocityY", "setVelocity", "speed", "randomAngle", "random", "forceMagnitude", "requestAnimationFrame", "cancelAnimationFrame", "stop", "clear", "context", "textures", "className", "loadingContainer", "sx", "minHeight", "children", "gameWrapper", "hexagonLoadingContainer", "ref", "hexagonCanvas", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/ignition/ignition-ui/src/components/Loading/HexagonBallLoading.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\r\nimport { Box } from '@mui/material';\r\nimport Matter from 'matter-js';\r\nimport styles from './styles.module.scss';\r\n\r\n/**\r\n * Component displays loading screen with a bouncing ball inside a rotating hexagon\r\n * @param {Object} props - Component props\r\n * @param {boolean} props.fromCreatePlan - Flag to determine if component is called from CreatePlan page\r\n * @returns {JSX.Element} - HexagonBallLoading component\r\n */\r\nconst HexagonBallLoading = ({ fromCreatePlan }) => {\r\n  const canvasRef = useRef(null);\r\n  const engineRef = useRef(null);\r\n  const requestRef = useRef(null);\r\n  const ballRef = useRef(null);\r\n  const hexagonEdgesRef = useRef([]);\r\n\r\n  useEffect(() => {\r\n    // Realistic physics parameters\r\n    const rotationSpeed = 0.008; // Slower, more natural rotation\r\n    const gravity = 0.0008; // More realistic gravity strength\r\n    const ballRestitution = 0.85; // Realistic bounce with energy loss\r\n    const wallRestitution = 0.75; // Walls absorb some energy\r\n\r\n    // Initialize Matter.js modules\r\n    const Engine = Matter.Engine;\r\n    const Render = Matter.Render;\r\n    const World = Matter.World;\r\n    const Bodies = Matter.Bodies;\r\n    const Body = Matter.Body;\r\n\r\n    // Create engine with realistic physics\r\n    engineRef.current = Engine.create({\r\n      gravity: { x: 0, y: gravity, scale: 1 },\r\n    });\r\n\r\n    // Set realistic timing for physics simulation\r\n    engineRef.current.timing.timeScale = 1;\r\n\r\n    // Create renderer\r\n    const render = Render.create({\r\n      canvas: canvasRef.current,\r\n      engine: engineRef.current,\r\n      options: {\r\n        width: 352,\r\n        height: 352,\r\n        wireframes: false,\r\n        background: '#000000',\r\n        showAngleIndicator: false,\r\n        showCollisions: false,\r\n        showVelocity: false,\r\n      },\r\n    });\r\n\r\n    // Create hexagon\r\n    const hexagonRadius = 132;\r\n    const hexagonSides = 6;\r\n    const centerX = render.options.width / 2;\r\n    const centerY = render.options.height / 2;\r\n\r\n    // Create hexagon vertices\r\n    const hexagonVertices = [];\r\n    for (let i = 0; i < hexagonSides; i++) {\r\n      const angle = (Math.PI * 2 * i) / hexagonSides;\r\n      const x = hexagonRadius * Math.cos(angle);\r\n      const y = hexagonRadius * Math.sin(angle);\r\n      hexagonVertices.push({ x, y });\r\n    }\r\n\r\n    // Create hexagon edges with realistic physics\r\n    hexagonEdgesRef.current = [];\r\n    for (let i = 0; i < hexagonSides; i++) {\r\n      const j = (i + 1) % hexagonSides;\r\n      const edgeOptions = {\r\n        restitution: wallRestitution, // Use realistic wall bounce\r\n        friction: 0.05, // Reduced friction for smoother movement\r\n        frictionStatic: 0.1,\r\n        isStatic: true,\r\n        render: {\r\n          fillStyle: 'transparent',\r\n          strokeStyle: '#FFFFFF',\r\n          lineWidth: 2, // Slightly thicker for better visibility\r\n        },\r\n      };\r\n\r\n      // Calculate edge position and angle\r\n      const vertex1 = hexagonVertices[i];\r\n      const vertex2 = hexagonVertices[j];\r\n      const edgeLength = Math.sqrt(\r\n        Math.pow(vertex2.x - vertex1.x, 2) + Math.pow(vertex2.y - vertex1.y, 2)\r\n      );\r\n      const edgeAngle = Math.atan2(vertex2.y - vertex1.y, vertex2.x - vertex1.x);\r\n      const edgeCenterX = centerX + (vertex1.x + vertex2.x) / 2;\r\n      const edgeCenterY = centerY + (vertex1.y + vertex2.y) / 2;\r\n\r\n      // Create edge\r\n      const edge = Bodies.rectangle(\r\n        edgeCenterX,\r\n        edgeCenterY,\r\n        edgeLength,\r\n        1,\r\n        edgeOptions\r\n      );\r\n\r\n      // Rotate edge to angle\r\n      Body.rotate(edge, edgeAngle);\r\n\r\n      hexagonEdgesRef.current.push(edge);\r\n    }\r\n\r\n    // Create ball\r\n    const originalBallRadius = 15;\r\n    // Reduce by additional 10% from current size (72% * 0.9 = 64.8%)\r\n    const ballRadius = originalBallRadius * 0.648; // 72% * 0.9 = 64.8% of original size\r\n\r\n    // Place ball inside hexagon, closer to center\r\n    ballRef.current = Bodies.circle(centerX, centerY, ballRadius, {\r\n      restitution: restitution,\r\n      friction: 0.05,\r\n      frictionAir: 0.001,\r\n      density: 0.001,\r\n      render: {\r\n        fillStyle: '#F0A500',\r\n      },\r\n    });\r\n\r\n    // Add initial force to start ball movement\r\n    Body.applyForce(ballRef.current, ballRef.current.position, {\r\n      x: 0.0002,\r\n      y: -0.0002\r\n    });\r\n\r\n    // Add bodies to world\r\n    World.add(engineRef.current.world, [...hexagonEdgesRef.current, ballRef.current]);\r\n\r\n    // Run renderer\r\n    Render.run(render);\r\n\r\n    // Animation loop function\r\n    const animate = () => {\r\n      // Update engine\r\n      Engine.update(engineRef.current, 16.667);\r\n\r\n      // Rotate hexagon edges\r\n      if (hexagonEdgesRef.current.length > 0) {\r\n        hexagonEdgesRef.current.forEach(edge => {\r\n          // Rotate edge around hexagon center\r\n          Body.setPosition(edge, {\r\n            x: centerX + (edge.position.x - centerX) * Math.cos(rotationSpeed) - (edge.position.y - centerY) * Math.sin(rotationSpeed),\r\n            y: centerY + (edge.position.x - centerX) * Math.sin(rotationSpeed) + (edge.position.y - centerY) * Math.cos(rotationSpeed)\r\n          });\r\n          Body.rotate(edge, rotationSpeed);\r\n        });\r\n      }\r\n\r\n      // Check ball position\r\n      if (ballRef.current) {\r\n        const ballPos = ballRef.current.position;\r\n\r\n        // Calculate distance from ball to hexagon center\r\n        const distanceFromCenter = Math.sqrt(\r\n          Math.pow(ballPos.x - centerX, 2) + Math.pow(ballPos.y - centerY, 2)\r\n        );\r\n\r\n        // If ball goes beyond hexagon safe boundary\r\n        const safeHexagonRadius = hexagonRadius * 0.85; // Safe boundary inside hexagon\r\n\r\n        if (distanceFromCenter > safeHexagonRadius) {\r\n          // Calculate vector from hexagon center to ball\r\n          const directionX = ballPos.x - centerX;\r\n          const directionY = ballPos.y - centerY;\r\n          const magnitude = Math.sqrt(directionX * directionX + directionY * directionY);\r\n\r\n          // Normalize vector\r\n          const normalizedX = directionX / magnitude;\r\n          const normalizedY = directionY / magnitude;\r\n\r\n          // Reset ball position to within safe boundary\r\n          const newPosX = centerX + normalizedX * safeHexagonRadius;\r\n          const newPosY = centerY + normalizedY * safeHexagonRadius;\r\n\r\n          // Set new ball position\r\n          Matter.Body.setPosition(ballRef.current, {\r\n            x: newPosX,\r\n            y: newPosY\r\n          });\r\n\r\n          // Adjust velocity for ball to bounce inward\r\n          const currentVelocity = ballRef.current.velocity;\r\n          const dotProduct =\r\n            currentVelocity.x * normalizedX +\r\n            currentVelocity.y * normalizedY;\r\n\r\n          // If ball is moving outward, reverse direction\r\n          if (dotProduct > 0) {\r\n            const newVelocityX = currentVelocity.x - 2 * dotProduct * normalizedX;\r\n            const newVelocityY = currentVelocity.y - 2 * dotProduct * normalizedY;\r\n\r\n            Matter.Body.setVelocity(ballRef.current, {\r\n              x: newVelocityX * restitution,\r\n              y: newVelocityY * restitution\r\n            });\r\n          }\r\n        }\r\n\r\n        // Ensure ball keeps moving\r\n        const velocity = ballRef.current.velocity;\r\n        const speed = Math.sqrt(velocity.x * velocity.x + velocity.y * velocity.y);\r\n\r\n        // If ball moves too slowly, add small force\r\n        if (speed < 0.5) {\r\n          const randomAngle = Math.random() * Math.PI * 2;\r\n          const forceMagnitude = 0.0001;\r\n          Body.applyForce(ballRef.current, ballRef.current.position, {\r\n            x: forceMagnitude * Math.cos(randomAngle),\r\n            y: forceMagnitude * Math.sin(randomAngle)\r\n          });\r\n        }\r\n      }\r\n\r\n      // Continue animation loop\r\n      requestRef.current = requestAnimationFrame(animate);\r\n    };\r\n\r\n    // Start animation loop\r\n    requestRef.current = requestAnimationFrame(animate);\r\n\r\n    // Cleanup when component unmounts\r\n    return () => {\r\n      // Cancel animation loop\r\n      if (requestRef.current) {\r\n        cancelAnimationFrame(requestRef.current);\r\n      }\r\n\r\n      // Cleanup renderer and engine\r\n      Render.stop(render);\r\n      World.clear(engineRef.current.world);\r\n      Engine.clear(engineRef.current);\r\n      render.canvas = null;\r\n      render.context = null;\r\n      render.textures = {};\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <Box className={styles.loadingContainer}\r\n      sx={{ ...(!fromCreatePlan && { minHeight: '90vh' }) }}>\r\n      <Box className={styles.gameWrapper}>\r\n        <Box className={styles.hexagonLoadingContainer}>\r\n          <canvas ref={canvasRef} className={styles.hexagonCanvas} />\r\n        </Box>\r\n      </Box>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default HexagonBallLoading;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,SAASC,GAAG,QAAQ,eAAe;AACnC,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,MAAM,MAAM,sBAAsB;;AAEzC;AACA;AACA;AACA;AACA;AACA;AALA,SAAAC,MAAA,IAAAC,OAAA;AAMA,MAAMC,kBAAkB,GAAGC,IAAA,IAAwB;EAAAC,EAAA;EAAA,IAAvB;IAAEC;EAAe,CAAC,GAAAF,IAAA;EAC5C,MAAMG,SAAS,GAAGV,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMW,SAAS,GAAGX,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMY,UAAU,GAAGZ,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMa,OAAO,GAAGb,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAMc,eAAe,GAAGd,MAAM,CAAC,EAAE,CAAC;EAElCD,SAAS,CAAC,MAAM;IACd;IACA,MAAMgB,aAAa,GAAG,KAAK,CAAC,CAAC;IAC7B,MAAMC,OAAO,GAAG,MAAM,CAAC,CAAC;IACxB,MAAMC,eAAe,GAAG,IAAI,CAAC,CAAC;IAC9B,MAAMC,eAAe,GAAG,IAAI,CAAC,CAAC;;IAE9B;IACA,MAAMC,MAAM,GAAGjB,MAAM,CAACiB,MAAM;IAC5B,MAAMC,MAAM,GAAGlB,MAAM,CAACkB,MAAM;IAC5B,MAAMC,KAAK,GAAGnB,MAAM,CAACmB,KAAK;IAC1B,MAAMC,MAAM,GAAGpB,MAAM,CAACoB,MAAM;IAC5B,MAAMC,IAAI,GAAGrB,MAAM,CAACqB,IAAI;;IAExB;IACAZ,SAAS,CAACa,OAAO,GAAGL,MAAM,CAACM,MAAM,CAAC;MAChCT,OAAO,EAAE;QAAEU,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAEX,OAAO;QAAEY,KAAK,EAAE;MAAE;IACxC,CAAC,CAAC;;IAEF;IACAjB,SAAS,CAACa,OAAO,CAACK,MAAM,CAACC,SAAS,GAAG,CAAC;;IAEtC;IACA,MAAMC,MAAM,GAAGX,MAAM,CAACK,MAAM,CAAC;MAC3BO,MAAM,EAAEtB,SAAS,CAACc,OAAO;MACzBS,MAAM,EAAEtB,SAAS,CAACa,OAAO;MACzBU,OAAO,EAAE;QACPC,KAAK,EAAE,GAAG;QACVC,MAAM,EAAE,GAAG;QACXC,UAAU,EAAE,KAAK;QACjBC,UAAU,EAAE,SAAS;QACrBC,kBAAkB,EAAE,KAAK;QACzBC,cAAc,EAAE,KAAK;QACrBC,YAAY,EAAE;MAChB;IACF,CAAC,CAAC;;IAEF;IACA,MAAMC,aAAa,GAAG,GAAG;IACzB,MAAMC,YAAY,GAAG,CAAC;IACtB,MAAMC,OAAO,GAAGb,MAAM,CAACG,OAAO,CAACC,KAAK,GAAG,CAAC;IACxC,MAAMU,OAAO,GAAGd,MAAM,CAACG,OAAO,CAACE,MAAM,GAAG,CAAC;;IAEzC;IACA,MAAMU,eAAe,GAAG,EAAE;IAC1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,YAAY,EAAEI,CAAC,EAAE,EAAE;MACrC,MAAMC,KAAK,GAAIC,IAAI,CAACC,EAAE,GAAG,CAAC,GAAGH,CAAC,GAAIJ,YAAY;MAC9C,MAAMjB,CAAC,GAAGgB,aAAa,GAAGO,IAAI,CAACE,GAAG,CAACH,KAAK,CAAC;MACzC,MAAMrB,CAAC,GAAGe,aAAa,GAAGO,IAAI,CAACG,GAAG,CAACJ,KAAK,CAAC;MACzCF,eAAe,CAACO,IAAI,CAAC;QAAE3B,CAAC;QAAEC;MAAE,CAAC,CAAC;IAChC;;IAEA;IACAb,eAAe,CAACU,OAAO,GAAG,EAAE;IAC5B,KAAK,IAAIuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,YAAY,EAAEI,CAAC,EAAE,EAAE;MACrC,MAAMO,CAAC,GAAG,CAACP,CAAC,GAAG,CAAC,IAAIJ,YAAY;MAChC,MAAMY,WAAW,GAAG;QAClBC,WAAW,EAAEtC,eAAe;QAAE;QAC9BuC,QAAQ,EAAE,IAAI;QAAE;QAChBC,cAAc,EAAE,GAAG;QACnBC,QAAQ,EAAE,IAAI;QACd5B,MAAM,EAAE;UACN6B,SAAS,EAAE,aAAa;UACxBC,WAAW,EAAE,SAAS;UACtBC,SAAS,EAAE,CAAC,CAAE;QAChB;MACF,CAAC;;MAED;MACA,MAAMC,OAAO,GAAGjB,eAAe,CAACC,CAAC,CAAC;MAClC,MAAMiB,OAAO,GAAGlB,eAAe,CAACQ,CAAC,CAAC;MAClC,MAAMW,UAAU,GAAGhB,IAAI,CAACiB,IAAI,CAC1BjB,IAAI,CAACkB,GAAG,CAACH,OAAO,CAACtC,CAAC,GAAGqC,OAAO,CAACrC,CAAC,EAAE,CAAC,CAAC,GAAGuB,IAAI,CAACkB,GAAG,CAACH,OAAO,CAACrC,CAAC,GAAGoC,OAAO,CAACpC,CAAC,EAAE,CAAC,CACxE,CAAC;MACD,MAAMyC,SAAS,GAAGnB,IAAI,CAACoB,KAAK,CAACL,OAAO,CAACrC,CAAC,GAAGoC,OAAO,CAACpC,CAAC,EAAEqC,OAAO,CAACtC,CAAC,GAAGqC,OAAO,CAACrC,CAAC,CAAC;MAC1E,MAAM4C,WAAW,GAAG1B,OAAO,GAAG,CAACmB,OAAO,CAACrC,CAAC,GAAGsC,OAAO,CAACtC,CAAC,IAAI,CAAC;MACzD,MAAM6C,WAAW,GAAG1B,OAAO,GAAG,CAACkB,OAAO,CAACpC,CAAC,GAAGqC,OAAO,CAACrC,CAAC,IAAI,CAAC;;MAEzD;MACA,MAAM6C,IAAI,GAAGlD,MAAM,CAACmD,SAAS,CAC3BH,WAAW,EACXC,WAAW,EACXN,UAAU,EACV,CAAC,EACDV,WACF,CAAC;;MAED;MACAhC,IAAI,CAACmD,MAAM,CAACF,IAAI,EAAEJ,SAAS,CAAC;MAE5BtD,eAAe,CAACU,OAAO,CAAC6B,IAAI,CAACmB,IAAI,CAAC;IACpC;;IAEA;IACA,MAAMG,kBAAkB,GAAG,EAAE;IAC7B;IACA,MAAMC,UAAU,GAAGD,kBAAkB,GAAG,KAAK,CAAC,CAAC;;IAE/C;IACA9D,OAAO,CAACW,OAAO,GAAGF,MAAM,CAACuD,MAAM,CAACjC,OAAO,EAAEC,OAAO,EAAE+B,UAAU,EAAE;MAC5DpB,WAAW,EAAEA,WAAW;MACxBC,QAAQ,EAAE,IAAI;MACdqB,WAAW,EAAE,KAAK;MAClBC,OAAO,EAAE,KAAK;MACdhD,MAAM,EAAE;QACN6B,SAAS,EAAE;MACb;IACF,CAAC,CAAC;;IAEF;IACArC,IAAI,CAACyD,UAAU,CAACnE,OAAO,CAACW,OAAO,EAAEX,OAAO,CAACW,OAAO,CAACyD,QAAQ,EAAE;MACzDvD,CAAC,EAAE,MAAM;MACTC,CAAC,EAAE,CAAC;IACN,CAAC,CAAC;;IAEF;IACAN,KAAK,CAAC6D,GAAG,CAACvE,SAAS,CAACa,OAAO,CAAC2D,KAAK,EAAE,CAAC,GAAGrE,eAAe,CAACU,OAAO,EAAEX,OAAO,CAACW,OAAO,CAAC,CAAC;;IAEjF;IACAJ,MAAM,CAACgE,GAAG,CAACrD,MAAM,CAAC;;IAElB;IACA,MAAMsD,OAAO,GAAGA,CAAA,KAAM;MACpB;MACAlE,MAAM,CAACmE,MAAM,CAAC3E,SAAS,CAACa,OAAO,EAAE,MAAM,CAAC;;MAExC;MACA,IAAIV,eAAe,CAACU,OAAO,CAAC+D,MAAM,GAAG,CAAC,EAAE;QACtCzE,eAAe,CAACU,OAAO,CAACgE,OAAO,CAAChB,IAAI,IAAI;UACtC;UACAjD,IAAI,CAACkE,WAAW,CAACjB,IAAI,EAAE;YACrB9C,CAAC,EAAEkB,OAAO,GAAG,CAAC4B,IAAI,CAACS,QAAQ,CAACvD,CAAC,GAAGkB,OAAO,IAAIK,IAAI,CAACE,GAAG,CAACpC,aAAa,CAAC,GAAG,CAACyD,IAAI,CAACS,QAAQ,CAACtD,CAAC,GAAGkB,OAAO,IAAII,IAAI,CAACG,GAAG,CAACrC,aAAa,CAAC;YAC1HY,CAAC,EAAEkB,OAAO,GAAG,CAAC2B,IAAI,CAACS,QAAQ,CAACvD,CAAC,GAAGkB,OAAO,IAAIK,IAAI,CAACG,GAAG,CAACrC,aAAa,CAAC,GAAG,CAACyD,IAAI,CAACS,QAAQ,CAACtD,CAAC,GAAGkB,OAAO,IAAII,IAAI,CAACE,GAAG,CAACpC,aAAa;UAC3H,CAAC,CAAC;UACFQ,IAAI,CAACmD,MAAM,CAACF,IAAI,EAAEzD,aAAa,CAAC;QAClC,CAAC,CAAC;MACJ;;MAEA;MACA,IAAIF,OAAO,CAACW,OAAO,EAAE;QACnB,MAAMkE,OAAO,GAAG7E,OAAO,CAACW,OAAO,CAACyD,QAAQ;;QAExC;QACA,MAAMU,kBAAkB,GAAG1C,IAAI,CAACiB,IAAI,CAClCjB,IAAI,CAACkB,GAAG,CAACuB,OAAO,CAAChE,CAAC,GAAGkB,OAAO,EAAE,CAAC,CAAC,GAAGK,IAAI,CAACkB,GAAG,CAACuB,OAAO,CAAC/D,CAAC,GAAGkB,OAAO,EAAE,CAAC,CACpE,CAAC;;QAED;QACA,MAAM+C,iBAAiB,GAAGlD,aAAa,GAAG,IAAI,CAAC,CAAC;;QAEhD,IAAIiD,kBAAkB,GAAGC,iBAAiB,EAAE;UAC1C;UACA,MAAMC,UAAU,GAAGH,OAAO,CAAChE,CAAC,GAAGkB,OAAO;UACtC,MAAMkD,UAAU,GAAGJ,OAAO,CAAC/D,CAAC,GAAGkB,OAAO;UACtC,MAAMkD,SAAS,GAAG9C,IAAI,CAACiB,IAAI,CAAC2B,UAAU,GAAGA,UAAU,GAAGC,UAAU,GAAGA,UAAU,CAAC;;UAE9E;UACA,MAAME,WAAW,GAAGH,UAAU,GAAGE,SAAS;UAC1C,MAAME,WAAW,GAAGH,UAAU,GAAGC,SAAS;;UAE1C;UACA,MAAMG,OAAO,GAAGtD,OAAO,GAAGoD,WAAW,GAAGJ,iBAAiB;UACzD,MAAMO,OAAO,GAAGtD,OAAO,GAAGoD,WAAW,GAAGL,iBAAiB;;UAEzD;UACA1F,MAAM,CAACqB,IAAI,CAACkE,WAAW,CAAC5E,OAAO,CAACW,OAAO,EAAE;YACvCE,CAAC,EAAEwE,OAAO;YACVvE,CAAC,EAAEwE;UACL,CAAC,CAAC;;UAEF;UACA,MAAMC,eAAe,GAAGvF,OAAO,CAACW,OAAO,CAAC6E,QAAQ;UAChD,MAAMC,UAAU,GACdF,eAAe,CAAC1E,CAAC,GAAGsE,WAAW,GAC/BI,eAAe,CAACzE,CAAC,GAAGsE,WAAW;;UAEjC;UACA,IAAIK,UAAU,GAAG,CAAC,EAAE;YAClB,MAAMC,YAAY,GAAGH,eAAe,CAAC1E,CAAC,GAAG,CAAC,GAAG4E,UAAU,GAAGN,WAAW;YACrE,MAAMQ,YAAY,GAAGJ,eAAe,CAACzE,CAAC,GAAG,CAAC,GAAG2E,UAAU,GAAGL,WAAW;YAErE/F,MAAM,CAACqB,IAAI,CAACkF,WAAW,CAAC5F,OAAO,CAACW,OAAO,EAAE;cACvCE,CAAC,EAAE6E,YAAY,GAAG/C,WAAW;cAC7B7B,CAAC,EAAE6E,YAAY,GAAGhD;YACpB,CAAC,CAAC;UACJ;QACF;;QAEA;QACA,MAAM6C,QAAQ,GAAGxF,OAAO,CAACW,OAAO,CAAC6E,QAAQ;QACzC,MAAMK,KAAK,GAAGzD,IAAI,CAACiB,IAAI,CAACmC,QAAQ,CAAC3E,CAAC,GAAG2E,QAAQ,CAAC3E,CAAC,GAAG2E,QAAQ,CAAC1E,CAAC,GAAG0E,QAAQ,CAAC1E,CAAC,CAAC;;QAE1E;QACA,IAAI+E,KAAK,GAAG,GAAG,EAAE;UACf,MAAMC,WAAW,GAAG1D,IAAI,CAAC2D,MAAM,CAAC,CAAC,GAAG3D,IAAI,CAACC,EAAE,GAAG,CAAC;UAC/C,MAAM2D,cAAc,GAAG,MAAM;UAC7BtF,IAAI,CAACyD,UAAU,CAACnE,OAAO,CAACW,OAAO,EAAEX,OAAO,CAACW,OAAO,CAACyD,QAAQ,EAAE;YACzDvD,CAAC,EAAEmF,cAAc,GAAG5D,IAAI,CAACE,GAAG,CAACwD,WAAW,CAAC;YACzChF,CAAC,EAAEkF,cAAc,GAAG5D,IAAI,CAACG,GAAG,CAACuD,WAAW;UAC1C,CAAC,CAAC;QACJ;MACF;;MAEA;MACA/F,UAAU,CAACY,OAAO,GAAGsF,qBAAqB,CAACzB,OAAO,CAAC;IACrD,CAAC;;IAED;IACAzE,UAAU,CAACY,OAAO,GAAGsF,qBAAqB,CAACzB,OAAO,CAAC;;IAEnD;IACA,OAAO,MAAM;MACX;MACA,IAAIzE,UAAU,CAACY,OAAO,EAAE;QACtBuF,oBAAoB,CAACnG,UAAU,CAACY,OAAO,CAAC;MAC1C;;MAEA;MACAJ,MAAM,CAAC4F,IAAI,CAACjF,MAAM,CAAC;MACnBV,KAAK,CAAC4F,KAAK,CAACtG,SAAS,CAACa,OAAO,CAAC2D,KAAK,CAAC;MACpChE,MAAM,CAAC8F,KAAK,CAACtG,SAAS,CAACa,OAAO,CAAC;MAC/BO,MAAM,CAACC,MAAM,GAAG,IAAI;MACpBD,MAAM,CAACmF,OAAO,GAAG,IAAI;MACrBnF,MAAM,CAACoF,QAAQ,GAAG,CAAC,CAAC;IACtB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE9G,OAAA,CAACJ,GAAG;IAACmH,SAAS,EAAEjH,MAAM,CAACkH,gBAAiB;IACtCC,EAAE,EAAE;MAAE,IAAI,CAAC7G,cAAc,IAAI;QAAE8G,SAAS,EAAE;MAAO,CAAC;IAAE,CAAE;IAAAC,QAAA,eACtDnH,OAAA,CAACJ,GAAG;MAACmH,SAAS,EAAEjH,MAAM,CAACsH,WAAY;MAAAD,QAAA,eACjCnH,OAAA,CAACJ,GAAG;QAACmH,SAAS,EAAEjH,MAAM,CAACuH,uBAAwB;QAAAF,QAAA,eAC7CnH,OAAA;UAAQsH,GAAG,EAAEjH,SAAU;UAAC0G,SAAS,EAAEjH,MAAM,CAACyH;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACxH,EAAA,CApPIF,kBAAkB;AAAA2H,EAAA,GAAlB3H,kBAAkB;AAsPxB,eAAeA,kBAAkB;AAAC,IAAA2H,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}