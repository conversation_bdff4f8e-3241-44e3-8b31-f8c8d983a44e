{"ast": null, "code": "var _jsxFileName = \"C:\\\\ignition\\\\ignition-ui\\\\src\\\\components\\\\Loading\\\\ModernSpinner.js\";\nimport React from 'react';\nimport { Box } from '@mui/material';\nimport styles from './ModernSpinner.module.scss';\n\n/**\n * Modern animated spinner component with multiple animation layers\n * @param {Object} props - Component props\n * @param {boolean} props.fromCreatePlan - Flag to determine if component is called from CreatePlan page\n * @param {string} props.size - Size of the spinner ('small', 'medium', 'large')\n * @param {string} props.variant - Spinner variant ('orbital', 'pulse', 'ripple', 'dots', 'galaxy')\n * @param {string} props.status - Current status for dynamic animations ('creating', 'processing', 'finalizing')\n * @returns {JSX.Element} - ModernSpinner component\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ModernSpinner = _ref => {\n  let {\n    fromCreatePlan = false,\n    size = 'large',\n    variant = 'orbital',\n    status = 'creating'\n  } = _ref;\n  const renderOrbitalSpinner = () => /*#__PURE__*/_jsxDEV(Box, {\n    className: `${styles.spinnerContainer} ${styles[size]}`,\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      className: styles.outerRing,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        className: styles.outerOrb\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      className: styles.middleRing,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        className: styles.middleOrb\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      className: styles.innerRing,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        className: styles.innerOrb\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      className: styles.centerCore\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n  const renderPulseSpinner = () => /*#__PURE__*/_jsxDEV(Box, {\n    className: `${styles.pulseContainer} ${styles[size]}`,\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      className: styles.pulseRing1\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      className: styles.pulseRing2\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      className: styles.pulseRing3\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      className: styles.pulseCenter\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this);\n  const renderRippleSpinner = () => /*#__PURE__*/_jsxDEV(Box, {\n    className: `${styles.rippleContainer} ${styles[size]}`,\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      className: styles.ripple\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      className: styles.ripple\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      className: styles.ripple\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 5\n  }, this);\n  const renderDotsSpinner = () => /*#__PURE__*/_jsxDEV(Box, {\n    className: `${styles.dotsContainer} ${styles[size]}`,\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      className: styles.dot1\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      className: styles.dot2\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      className: styles.dot3\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      className: styles.dot4\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 65,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      className: styles.dot5\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 5\n  }, this);\n  const renderSpinner = () => {\n    switch (variant) {\n      case 'pulse':\n        return renderPulseSpinner();\n      case 'ripple':\n        return renderRippleSpinner();\n      case 'dots':\n        return renderDotsSpinner();\n      case 'orbital':\n      default:\n        return renderOrbitalSpinner();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: styles.loadingWrapper,\n    sx: {\n      ...(!fromCreatePlan && {\n        minHeight: '90vh'\n      }),\n      display: 'flex',\n      alignItems: 'center',\n      justifyContent: 'center',\n      flexDirection: 'column'\n    },\n    children: renderSpinner()\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 85,\n    columnNumber: 5\n  }, this);\n};\n_c = ModernSpinner;\nexport default ModernSpinner;\nvar _c;\n$RefreshReg$(_c, \"ModernSpinner\");", "map": {"version": 3, "names": ["React", "Box", "styles", "jsxDEV", "_jsxDEV", "ModernSpinner", "_ref", "fromCreatePlan", "size", "variant", "status", "renderOrbitalSpinner", "className", "spinnerContainer", "children", "outerRing", "outerOrb", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "middleRing", "middleOrb", "innerRing", "innerOrb", "centerCore", "renderPulseSpinner", "pulseContainer", "pulseRing1", "pulseRing2", "pulseRing3", "pulseCenter", "renderRippleSpinner", "ripple<PERSON><PERSON>r", "ripple", "renderDotsSpinner", "dotsContainer", "dot1", "dot2", "dot3", "dot4", "dot5", "renderSpinner", "loadingWrapper", "sx", "minHeight", "display", "alignItems", "justifyContent", "flexDirection", "_c", "$RefreshReg$"], "sources": ["C:/ignition/ignition-ui/src/components/Loading/ModernSpinner.js"], "sourcesContent": ["import React from 'react';\nimport { Box } from '@mui/material';\nimport styles from './ModernSpinner.module.scss';\n\n/**\n * Modern animated spinner component with multiple animation layers\n * @param {Object} props - Component props\n * @param {boolean} props.fromCreatePlan - Flag to determine if component is called from CreatePlan page\n * @param {string} props.size - Size of the spinner ('small', 'medium', 'large')\n * @param {string} props.variant - Spinner variant ('orbital', 'pulse', 'ripple', 'dots', 'galaxy')\n * @param {string} props.status - Current status for dynamic animations ('creating', 'processing', 'finalizing')\n * @returns {JSX.Element} - ModernSpinner component\n */\nconst ModernSpinner = ({\n  fromCreatePlan = false,\n  size = 'large',\n  variant = 'orbital',\n  status = 'creating'\n}) => {\n  \n  const renderOrbitalSpinner = () => (\n    <Box className={`${styles.spinnerContainer} ${styles[size]}`}>\n      {/* Outer rotating ring */}\n      <Box className={styles.outerRing}>\n        <Box className={styles.outerOrb}></Box>\n      </Box>\n      \n      {/* Middle rotating ring */}\n      <Box className={styles.middleRing}>\n        <Box className={styles.middleOrb}></Box>\n      </Box>\n      \n      {/* Inner rotating ring */}\n      <Box className={styles.innerRing}>\n        <Box className={styles.innerOrb}></Box>\n      </Box>\n      \n      {/* Center pulsing core */}\n      <Box className={styles.centerCore}></Box>\n    </Box>\n  );\n\n  const renderPulseSpinner = () => (\n    <Box className={`${styles.pulseContainer} ${styles[size]}`}>\n      <Box className={styles.pulseRing1}></Box>\n      <Box className={styles.pulseRing2}></Box>\n      <Box className={styles.pulseRing3}></Box>\n      <Box className={styles.pulseCenter}></Box>\n    </Box>\n  );\n\n  const renderRippleSpinner = () => (\n    <Box className={`${styles.rippleContainer} ${styles[size]}`}>\n      <Box className={styles.ripple}></Box>\n      <Box className={styles.ripple}></Box>\n      <Box className={styles.ripple}></Box>\n    </Box>\n  );\n\n  const renderDotsSpinner = () => (\n    <Box className={`${styles.dotsContainer} ${styles[size]}`}>\n      <Box className={styles.dot1}></Box>\n      <Box className={styles.dot2}></Box>\n      <Box className={styles.dot3}></Box>\n      <Box className={styles.dot4}></Box>\n      <Box className={styles.dot5}></Box>\n    </Box>\n  );\n\n  const renderSpinner = () => {\n    switch (variant) {\n      case 'pulse':\n        return renderPulseSpinner();\n      case 'ripple':\n        return renderRippleSpinner();\n      case 'dots':\n        return renderDotsSpinner();\n      case 'orbital':\n      default:\n        return renderOrbitalSpinner();\n    }\n  };\n\n  return (\n    <Box \n      className={styles.loadingWrapper}\n      sx={{ \n        ...(!fromCreatePlan && { minHeight: '90vh' }),\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        flexDirection: 'column'\n      }}\n    >\n      {renderSpinner()}\n    </Box>\n  );\n};\n\nexport default ModernSpinner;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,QAAQ,eAAe;AACnC,OAAOC,MAAM,MAAM,6BAA6B;;AAEhD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SAAAC,MAAA,IAAAC,OAAA;AASA,MAAMC,aAAa,GAAGC,IAAA,IAKhB;EAAA,IALiB;IACrBC,cAAc,GAAG,KAAK;IACtBC,IAAI,GAAG,OAAO;IACdC,OAAO,GAAG,SAAS;IACnBC,MAAM,GAAG;EACX,CAAC,GAAAJ,IAAA;EAEC,MAAMK,oBAAoB,GAAGA,CAAA,kBAC3BP,OAAA,CAACH,GAAG;IAACW,SAAS,EAAE,GAAGV,MAAM,CAACW,gBAAgB,IAAIX,MAAM,CAACM,IAAI,CAAC,EAAG;IAAAM,QAAA,gBAE3DV,OAAA,CAACH,GAAG;MAACW,SAAS,EAAEV,MAAM,CAACa,SAAU;MAAAD,QAAA,eAC/BV,OAAA,CAACH,GAAG;QAACW,SAAS,EAAEV,MAAM,CAACc;MAAS;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC,eAGNhB,OAAA,CAACH,GAAG;MAACW,SAAS,EAAEV,MAAM,CAACmB,UAAW;MAAAP,QAAA,eAChCV,OAAA,CAACH,GAAG;QAACW,SAAS,EAAEV,MAAM,CAACoB;MAAU;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC,CAAC,eAGNhB,OAAA,CAACH,GAAG;MAACW,SAAS,EAAEV,MAAM,CAACqB,SAAU;MAAAT,QAAA,eAC/BV,OAAA,CAACH,GAAG;QAACW,SAAS,EAAEV,MAAM,CAACsB;MAAS;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpC,CAAC,eAGNhB,OAAA,CAACH,GAAG;MAACW,SAAS,EAAEV,MAAM,CAACuB;IAAW;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACtC,CACN;EAED,MAAMM,kBAAkB,GAAGA,CAAA,kBACzBtB,OAAA,CAACH,GAAG;IAACW,SAAS,EAAE,GAAGV,MAAM,CAACyB,cAAc,IAAIzB,MAAM,CAACM,IAAI,CAAC,EAAG;IAAAM,QAAA,gBACzDV,OAAA,CAACH,GAAG;MAACW,SAAS,EAAEV,MAAM,CAAC0B;IAAW;MAAAX,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACzChB,OAAA,CAACH,GAAG;MAACW,SAAS,EAAEV,MAAM,CAAC2B;IAAW;MAAAZ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACzChB,OAAA,CAACH,GAAG;MAACW,SAAS,EAAEV,MAAM,CAAC4B;IAAW;MAAAb,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACzChB,OAAA,CAACH,GAAG;MAACW,SAAS,EAAEV,MAAM,CAAC6B;IAAY;MAAAd,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACvC,CACN;EAED,MAAMY,mBAAmB,GAAGA,CAAA,kBAC1B5B,OAAA,CAACH,GAAG;IAACW,SAAS,EAAE,GAAGV,MAAM,CAAC+B,eAAe,IAAI/B,MAAM,CAACM,IAAI,CAAC,EAAG;IAAAM,QAAA,gBAC1DV,OAAA,CAACH,GAAG;MAACW,SAAS,EAAEV,MAAM,CAACgC;IAAO;MAAAjB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACrChB,OAAA,CAACH,GAAG;MAACW,SAAS,EAAEV,MAAM,CAACgC;IAAO;MAAAjB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACrChB,OAAA,CAACH,GAAG;MAACW,SAAS,EAAEV,MAAM,CAACgC;IAAO;MAAAjB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAClC,CACN;EAED,MAAMe,iBAAiB,GAAGA,CAAA,kBACxB/B,OAAA,CAACH,GAAG;IAACW,SAAS,EAAE,GAAGV,MAAM,CAACkC,aAAa,IAAIlC,MAAM,CAACM,IAAI,CAAC,EAAG;IAAAM,QAAA,gBACxDV,OAAA,CAACH,GAAG;MAACW,SAAS,EAAEV,MAAM,CAACmC;IAAK;MAAApB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACnChB,OAAA,CAACH,GAAG;MAACW,SAAS,EAAEV,MAAM,CAACoC;IAAK;MAAArB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACnChB,OAAA,CAACH,GAAG;MAACW,SAAS,EAAEV,MAAM,CAACqC;IAAK;MAAAtB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACnChB,OAAA,CAACH,GAAG;MAACW,SAAS,EAAEV,MAAM,CAACsC;IAAK;MAAAvB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACnChB,OAAA,CAACH,GAAG;MAACW,SAAS,EAAEV,MAAM,CAACuC;IAAK;MAAAxB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAChC,CACN;EAED,MAAMsB,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQjC,OAAO;MACb,KAAK,OAAO;QACV,OAAOiB,kBAAkB,CAAC,CAAC;MAC7B,KAAK,QAAQ;QACX,OAAOM,mBAAmB,CAAC,CAAC;MAC9B,KAAK,MAAM;QACT,OAAOG,iBAAiB,CAAC,CAAC;MAC5B,KAAK,SAAS;MACd;QACE,OAAOxB,oBAAoB,CAAC,CAAC;IACjC;EACF,CAAC;EAED,oBACEP,OAAA,CAACH,GAAG;IACFW,SAAS,EAAEV,MAAM,CAACyC,cAAe;IACjCC,EAAE,EAAE;MACF,IAAI,CAACrC,cAAc,IAAI;QAAEsC,SAAS,EAAE;MAAO,CAAC,CAAC;MAC7CC,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,aAAa,EAAE;IACjB,CAAE;IAAAnC,QAAA,EAED4B,aAAa,CAAC;EAAC;IAAAzB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACb,CAAC;AAEV,CAAC;AAAC8B,EAAA,GApFI7C,aAAa;AAsFnB,eAAeA,aAAa;AAAC,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}