from django.db.models.signals import post_save
from django.dispatch import receiver
from .models import Plan, PlanAccess


@receiver(post_save, sender=Plan)
def create_head_owner_access(sender, instance, created, **kwargs):
    """
    Create head owner access level when a new plan is created
    """
    if created and instance.user:
        PlanAccess.objects.create(
            user=instance.user,
            plan=instance,
            access_level=PlanAccess.OWNER,
            is_head_owner=True
        )
