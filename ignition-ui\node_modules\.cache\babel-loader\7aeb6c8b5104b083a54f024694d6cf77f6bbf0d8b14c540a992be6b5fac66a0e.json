{"ast": null, "code": "import { createStyled } from '@mui/system';\nimport defaultTheme from './defaultTheme';\nimport THEME_ID from './identifier';\nconst styled = createStyled({\n  defaultTheme,\n  themeId: THEME_ID\n});\nexport default styled;", "map": {"version": 3, "names": ["createStyled", "defaultTheme", "THEME_ID", "styled", "themeId"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/styles/styled.js"], "sourcesContent": ["import { createStyled } from '@mui/system';\nimport defaultTheme from './defaultTheme';\nimport THEME_ID from './identifier';\nconst styled = createStyled({\n  defaultTheme,\n  themeId: THEME_ID\n});\nexport default styled;"], "mappings": "AAAA,SAASA,YAAY,QAAQ,aAAa;AAC1C,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,QAAQ,MAAM,cAAc;AACnC,MAAMC,MAAM,GAAGH,YAAY,CAAC;EAC1BC,YAAY;EACZG,OAAO,EAAEF;AACX,CAAC,CAAC;AACF,eAAeC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}