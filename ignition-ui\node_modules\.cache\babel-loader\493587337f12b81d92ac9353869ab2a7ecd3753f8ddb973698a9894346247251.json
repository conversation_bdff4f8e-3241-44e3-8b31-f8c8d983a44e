{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"component\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_useForkRef as useForkRef } from '@mui/utils';\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport { styled, useThemeProps } from '../styles';\nimport { getFormHelperTextUtilityClass } from './formHelperTextClasses';\nimport FormControlContext from '../FormControl/FormControlContext';\nimport formControlClasses from '../FormControl/formControlClasses';\nimport formLabelClasses from '../FormLabel/formLabelClasses';\nimport useSlot from '../utils/useSlot';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = () => {\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getFormHelperTextUtilityClass, {});\n};\nconst FormHelperTextRoot = styled('div', {\n  name: 'JoyFormHelperText',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    '--Icon-fontSize': 'calc(var(--FormHelperText-lineHeight) * 1em)',\n    display: 'flex',\n    alignItems: 'center',\n    gap: '2px',\n    fontFamily: theme.vars.fontFamily.body,\n    fontSize: `var(--FormHelperText-fontSize, ${theme.vars.fontSize.sm})`,\n    lineHeight: `var(--FormHelperText-lineHeight, ${theme.vars.lineHeight.sm})`,\n    color: `var(--FormHelperText-color, ${theme.vars.palette.text.tertiary})`,\n    margin: 'var(--FormHelperText-margin, 0px)',\n    [`.${formLabelClasses.root} + &`]: {\n      '--FormHelperText-margin': '0px' // remove the margin if the helper text is next to the form label.\n    },\n    [`.${formControlClasses.error} &`]: {\n      '--Icon-color': 'currentColor'\n    }\n  };\n});\n/**\n *\n * Demos:\n *\n * - [Input](https://mui.com/joy-ui/react-input/)\n *\n * API:\n *\n * - [FormHelperText API](https://mui.com/joy-ui/api/form-helper-text/)\n */\nconst FormHelperText = /*#__PURE__*/React.forwardRef(function FormHelperText(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyFormHelperText'\n  });\n  const {\n      children,\n      component,\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const rootRef = React.useRef(null);\n  const handleRef = useForkRef(rootRef, ref);\n  const formControl = React.useContext(FormControlContext);\n  const setHelperText = formControl == null ? void 0 : formControl.setHelperText;\n  React.useEffect(() => {\n    setHelperText == null || setHelperText(rootRef.current);\n    return () => {\n      setHelperText == null || setHelperText(null);\n    };\n  }, [setHelperText]);\n  const classes = useUtilityClasses();\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref: handleRef,\n    elementType: FormHelperTextRoot,\n    externalForwardedProps,\n    ownerState: props,\n    additionalProps: {\n      as: component,\n      id: formControl == null ? void 0 : formControl['aria-describedby']\n    },\n    className: classes.root\n  });\n  return /*#__PURE__*/_jsx(SlotRoot, _extends({}, rootProps, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? FormHelperText.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default FormHelperText;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "unstable_useForkRef", "useForkRef", "unstable_composeClasses", "composeClasses", "styled", "useThemeProps", "getFormHelperTextUtilityClass", "FormControlContext", "formControlClasses", "formLabelClasses", "useSlot", "jsx", "_jsx", "useUtilityClasses", "slots", "root", "FormHelperTextRoot", "name", "slot", "overridesResolver", "props", "styles", "_ref", "theme", "display", "alignItems", "gap", "fontFamily", "vars", "body", "fontSize", "sm", "lineHeight", "color", "palette", "text", "tertiary", "margin", "error", "FormHelperText", "forwardRef", "inProps", "ref", "children", "component", "slotProps", "other", "rootRef", "useRef", "handleRef", "formControl", "useContext", "setHelperText", "useEffect", "current", "classes", "externalForwardedProps", "SlotRoot", "rootProps", "elementType", "ownerState", "additionalProps", "as", "id", "className", "process", "env", "NODE_ENV", "propTypes", "node", "shape", "oneOfType", "func", "object", "sx", "arrayOf", "bool"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/FormHelperText/FormHelperText.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"component\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_useForkRef as useForkRef } from '@mui/utils';\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport { styled, useThemeProps } from '../styles';\nimport { getFormHelperTextUtilityClass } from './formHelperTextClasses';\nimport FormControlContext from '../FormControl/FormControlContext';\nimport formControlClasses from '../FormControl/formControlClasses';\nimport formLabelClasses from '../FormLabel/formLabelClasses';\nimport useSlot from '../utils/useSlot';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = () => {\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getFormHelperTextUtilityClass, {});\n};\nconst FormHelperTextRoot = styled('div', {\n  name: 'JoyFormHelperText',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(({\n  theme\n}) => ({\n  '--Icon-fontSize': 'calc(var(--FormHelperText-lineHeight) * 1em)',\n  display: 'flex',\n  alignItems: 'center',\n  gap: '2px',\n  fontFamily: theme.vars.fontFamily.body,\n  fontSize: `var(--FormHelperText-fontSize, ${theme.vars.fontSize.sm})`,\n  lineHeight: `var(--FormHelperText-lineHeight, ${theme.vars.lineHeight.sm})`,\n  color: `var(--FormHelperText-color, ${theme.vars.palette.text.tertiary})`,\n  margin: 'var(--FormHelperText-margin, 0px)',\n  [`.${formLabelClasses.root} + &`]: {\n    '--FormHelperText-margin': '0px' // remove the margin if the helper text is next to the form label.\n  },\n  [`.${formControlClasses.error} &`]: {\n    '--Icon-color': 'currentColor'\n  }\n}));\n/**\n *\n * Demos:\n *\n * - [Input](https://mui.com/joy-ui/react-input/)\n *\n * API:\n *\n * - [FormHelperText API](https://mui.com/joy-ui/api/form-helper-text/)\n */\nconst FormHelperText = /*#__PURE__*/React.forwardRef(function FormHelperText(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyFormHelperText'\n  });\n  const {\n      children,\n      component,\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const rootRef = React.useRef(null);\n  const handleRef = useForkRef(rootRef, ref);\n  const formControl = React.useContext(FormControlContext);\n  const setHelperText = formControl == null ? void 0 : formControl.setHelperText;\n  React.useEffect(() => {\n    setHelperText == null || setHelperText(rootRef.current);\n    return () => {\n      setHelperText == null || setHelperText(null);\n    };\n  }, [setHelperText]);\n  const classes = useUtilityClasses();\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref: handleRef,\n    elementType: FormHelperTextRoot,\n    externalForwardedProps,\n    ownerState: props,\n    additionalProps: {\n      as: component,\n      id: formControl == null ? void 0 : formControl['aria-describedby']\n    },\n    className: classes.root\n  });\n  return /*#__PURE__*/_jsx(SlotRoot, _extends({}, rootProps, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? FormHelperText.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default FormHelperText;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,CAAC;AACjE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,mBAAmB,IAAIC,UAAU,QAAQ,YAAY;AAC9D,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,0BAA0B;AACpF,SAASC,MAAM,EAAEC,aAAa,QAAQ,WAAW;AACjD,SAASC,6BAA6B,QAAQ,yBAAyB;AACvE,OAAOC,kBAAkB,MAAM,mCAAmC;AAClE,OAAOC,kBAAkB,MAAM,mCAAmC;AAClE,OAAOC,gBAAgB,MAAM,+BAA+B;AAC5D,OAAOC,OAAO,MAAM,kBAAkB;AACtC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAC9B,MAAMC,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,OAAOZ,cAAc,CAACW,KAAK,EAAER,6BAA6B,EAAE,CAAC,CAAC,CAAC;AACjE,CAAC;AACD,MAAMU,kBAAkB,GAAGZ,MAAM,CAAC,KAAK,EAAE;EACvCa,IAAI,EAAE,mBAAmB;EACzBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC/C,CAAC,CAAC,CAACO,IAAA;EAAA,IAAC;IACFC;EACF,CAAC,GAAAD,IAAA;EAAA,OAAM;IACL,iBAAiB,EAAE,8CAA8C;IACjEE,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,GAAG,EAAE,KAAK;IACVC,UAAU,EAAEJ,KAAK,CAACK,IAAI,CAACD,UAAU,CAACE,IAAI;IACtCC,QAAQ,EAAE,kCAAkCP,KAAK,CAACK,IAAI,CAACE,QAAQ,CAACC,EAAE,GAAG;IACrEC,UAAU,EAAE,oCAAoCT,KAAK,CAACK,IAAI,CAACI,UAAU,CAACD,EAAE,GAAG;IAC3EE,KAAK,EAAE,+BAA+BV,KAAK,CAACK,IAAI,CAACM,OAAO,CAACC,IAAI,CAACC,QAAQ,GAAG;IACzEC,MAAM,EAAE,mCAAmC;IAC3C,CAAC,IAAI5B,gBAAgB,CAACM,IAAI,MAAM,GAAG;MACjC,yBAAyB,EAAE,KAAK,CAAC;IACnC,CAAC;IACD,CAAC,IAAIP,kBAAkB,CAAC8B,KAAK,IAAI,GAAG;MAClC,cAAc,EAAE;IAClB;EACF,CAAC;AAAA,CAAC,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,GAAG,aAAazC,KAAK,CAAC0C,UAAU,CAAC,SAASD,cAAcA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACzF,MAAMtB,KAAK,GAAGf,aAAa,CAAC;IAC1Be,KAAK,EAAEqB,OAAO;IACdxB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACF0B,QAAQ;MACRC,SAAS;MACT9B,KAAK,GAAG,CAAC,CAAC;MACV+B,SAAS,GAAG,CAAC;IACf,CAAC,GAAGzB,KAAK;IACT0B,KAAK,GAAGlD,6BAA6B,CAACwB,KAAK,EAAEvB,SAAS,CAAC;EACzD,MAAMkD,OAAO,GAAGjD,KAAK,CAACkD,MAAM,CAAC,IAAI,CAAC;EAClC,MAAMC,SAAS,GAAGhD,UAAU,CAAC8C,OAAO,EAAEL,GAAG,CAAC;EAC1C,MAAMQ,WAAW,GAAGpD,KAAK,CAACqD,UAAU,CAAC5C,kBAAkB,CAAC;EACxD,MAAM6C,aAAa,GAAGF,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACE,aAAa;EAC9EtD,KAAK,CAACuD,SAAS,CAAC,MAAM;IACpBD,aAAa,IAAI,IAAI,IAAIA,aAAa,CAACL,OAAO,CAACO,OAAO,CAAC;IACvD,OAAO,MAAM;MACXF,aAAa,IAAI,IAAI,IAAIA,aAAa,CAAC,IAAI,CAAC;IAC9C,CAAC;EACH,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EACnB,MAAMG,OAAO,GAAG1C,iBAAiB,CAAC,CAAC;EACnC,MAAM2C,sBAAsB,GAAG7D,QAAQ,CAAC,CAAC,CAAC,EAAEmD,KAAK,EAAE;IACjDF,SAAS;IACT9B,KAAK;IACL+B;EACF,CAAC,CAAC;EACF,MAAM,CAACY,QAAQ,EAAEC,SAAS,CAAC,GAAGhD,OAAO,CAAC,MAAM,EAAE;IAC5CgC,GAAG,EAAEO,SAAS;IACdU,WAAW,EAAE3C,kBAAkB;IAC/BwC,sBAAsB;IACtBI,UAAU,EAAExC,KAAK;IACjByC,eAAe,EAAE;MACfC,EAAE,EAAElB,SAAS;MACbmB,EAAE,EAAEb,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC,kBAAkB;IACnE,CAAC;IACDc,SAAS,EAAET,OAAO,CAACxC;EACrB,CAAC,CAAC;EACF,OAAO,aAAaH,IAAI,CAAC6C,QAAQ,EAAE9D,QAAQ,CAAC,CAAC,CAAC,EAAE+D,SAAS,EAAE;IACzDf,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFsB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG5B,cAAc,CAAC6B,SAAS,CAAC,yBAAyB;EACxF;EACA;EACA;EACA;EACA;AACF;AACA;EACEzB,QAAQ,EAAE5C,SAAS,CAACsE,IAAI;EACxB;AACF;AACA;AACA;EACEzB,SAAS,EAAE7C,SAAS,CAAC4D,WAAW;EAChC;AACF;AACA;AACA;EACEd,SAAS,EAAE9C,SAAS,CAACuE,KAAK,CAAC;IACzBvD,IAAI,EAAEhB,SAAS,CAACwE,SAAS,CAAC,CAACxE,SAAS,CAACyE,IAAI,EAAEzE,SAAS,CAAC0E,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;EACE3D,KAAK,EAAEf,SAAS,CAACuE,KAAK,CAAC;IACrBvD,IAAI,EAAEhB,SAAS,CAAC4D;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACEe,EAAE,EAAE3E,SAAS,CAACwE,SAAS,CAAC,CAACxE,SAAS,CAAC4E,OAAO,CAAC5E,SAAS,CAACwE,SAAS,CAAC,CAACxE,SAAS,CAACyE,IAAI,EAAEzE,SAAS,CAAC0E,MAAM,EAAE1E,SAAS,CAAC6E,IAAI,CAAC,CAAC,CAAC,EAAE7E,SAAS,CAACyE,IAAI,EAAEzE,SAAS,CAAC0E,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAelC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}