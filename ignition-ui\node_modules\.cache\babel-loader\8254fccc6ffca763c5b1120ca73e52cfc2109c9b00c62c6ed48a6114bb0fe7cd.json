{"ast": null, "code": "var _jsxFileName = \"C:\\\\ignition\\\\ignition-ui\\\\src\\\\views\\\\plan\\\\detail\\\\components\\\\AgentTab.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Box, Typography, Paper, TextField, IconButton, Avatar, Divider, CircularProgress, Chip, Tooltip } from '@mui/material';\nimport Iconify from 'components/Iconify/index';\nimport { mainYellowColor } from \"helpers/constants\";\nimport axios from 'axios';\nimport { APIURL, getHeaders } from 'helpers/constants';\nimport { useLocation } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AgentTab = _ref => {\n  _s();\n  let {\n    planInfo,\n    onPlanUpdate\n  } = _ref;\n  const [conversations, setConversations] = useState([]);\n  const [currentMessage, setCurrentMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef(null);\n  const inputRef = useRef(null);\n  const [error, setError] = useState(null);\n  const location = useLocation();\n\n  // Load conversations from localStorage\n  useEffect(() => {\n    var _location$state;\n    const savedConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\n    const planConversations = savedConversations.filter(conv => conv.planId === (planInfo === null || planInfo === void 0 ? void 0 : planInfo.id));\n    setConversations(planConversations);\n\n    // Check for pending message from ChatbotBar\n    const pendingMessage = localStorage.getItem('pending_agent_message');\n    if (pendingMessage) {\n      try {\n        var _messageData$planInfo;\n        const messageData = JSON.parse(pendingMessage);\n        if (((_messageData$planInfo = messageData.planInfo) === null || _messageData$planInfo === void 0 ? void 0 : _messageData$planInfo.id) === (planInfo === null || planInfo === void 0 ? void 0 : planInfo.id)) {\n          handleSendMessage(messageData.message);\n        }\n        // Clear the pending message\n        localStorage.removeItem('pending_agent_message');\n      } catch (error) {\n        console.error('Error processing pending message:', error);\n        localStorage.removeItem('pending_agent_message');\n      }\n    }\n\n    // If coming from chatbot bar via navigation state, add the initial message\n    if ((_location$state = location.state) !== null && _location$state !== void 0 && _location$state.message) {\n      handleSendMessage(location.state.message);\n    }\n  }, [planInfo === null || planInfo === void 0 ? void 0 : planInfo.id, location.state]);\n\n  // Auto scroll to bottom\n  useEffect(() => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  }, [conversations]);\n  const executeAction = async (action, originalMessage) => {\n    try {\n      const {\n        type,\n        entities\n      } = action;\n      if (type === 'add_milestone') {\n        // Extract milestone name from the message\n        let milestoneName = 'New Milestone';\n\n        // Try to extract from quoted text first\n        if (entities.taskNames && entities.taskNames.length > 0) {\n          milestoneName = entities.taskNames[0];\n        } else {\n          // Try to extract from common patterns\n          const patterns = [/add.*milestone.*[\"']([^\"']+)[\"']/i, /create.*milestone.*[\"']([^\"']+)[\"']/i, /new milestone.*[\"']([^\"']+)[\"']/i, /milestone.*[\"']([^\"']+)[\"']/i, /add.*milestone.*(?:for|about|called)\\s+([^.!?]+)/i, /create.*milestone.*(?:for|about|called)\\s+([^.!?]+)/i];\n          for (const pattern of patterns) {\n            const match = originalMessage.match(pattern);\n            if (match && match[1]) {\n              milestoneName = match[1].trim();\n              break;\n            }\n          }\n        }\n        const response = await axios.post(`${APIURL}/api/assistant/plan-action`, {\n          action: 'add_milestone',\n          plan_slug: planInfo.slug,\n          data: {\n            name: milestoneName,\n            description: `Milestone created by AI agent based on: \"${originalMessage}\"`\n          },\n          message: originalMessage\n        }, {\n          headers: getHeaders()\n        });\n        return {\n          success: true,\n          message: `✅ Successfully created milestone \"${milestoneName}\"!\n\nThe new milestone has been added to your project. You can now:\n• Add tasks to this milestone\n• Set specific goals and deadlines\n• Track progress as you work\n\nWould you like me to add some initial tasks to this milestone?`,\n          data: response.data\n        };\n      }\n\n      // Handle other action types here...\n      return {\n        success: false,\n        message: `I understand you want to ${action.description.toLowerCase()}, but I'm still learning how to do that. For now, I can help you add milestones to your project. What else can I help you with?`\n      };\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Error executing action:', error);\n      return {\n        success: false,\n        message: `Sorry, I encountered an error while trying to ${action.description.toLowerCase()}: ${((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || error.message}. Please try again or rephrase your request.`\n      };\n    }\n  };\n  const handleSendMessage = async function () {\n    let messageText = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : currentMessage;\n    if (!messageText.trim() || isLoading) return;\n    setIsLoading(true);\n    const userMessage = {\n      id: Date.now(),\n      type: 'user',\n      content: messageText.trim(),\n      timestamp: new Date().toISOString()\n    };\n    const newConversations = [...conversations, userMessage];\n    setConversations(newConversations);\n    setCurrentMessage('');\n    try {\n      // Extract actions from the message\n      const actions = extractActions(messageText);\n      let aiResponse;\n      if (actions.length > 0) {\n        // Execute the first action (for now, we'll handle one action at a time)\n        const action = actions[0];\n        const result = await executeAction(action, messageText);\n        aiResponse = {\n          id: Date.now() + 1,\n          type: 'assistant',\n          content: result.message,\n          timestamp: new Date().toISOString(),\n          actions: actions,\n          actionResult: result\n        };\n\n        // If the action was successful and modified the plan, trigger a refresh\n        if (result.success && onPlanUpdate) {\n          onPlanUpdate();\n        }\n      } else {\n        // Generate a contextual response without actions\n        aiResponse = {\n          id: Date.now() + 1,\n          type: 'assistant',\n          content: generateAIResponse(messageText, planInfo),\n          timestamp: new Date().toISOString(),\n          actions: []\n        };\n      }\n      const updatedConversations = [...newConversations, aiResponse];\n      setConversations(updatedConversations);\n\n      // Save to localStorage\n      const allConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\n      const otherPlanConversations = allConversations.filter(conv => conv.planId !== (planInfo === null || planInfo === void 0 ? void 0 : planInfo.id));\n      const planConversations = updatedConversations.map(conv => ({\n        ...conv,\n        planId: planInfo === null || planInfo === void 0 ? void 0 : planInfo.id,\n        planName: planInfo === null || planInfo === void 0 ? void 0 : planInfo.name\n      }));\n      localStorage.setItem('agent_conversations', JSON.stringify([...otherPlanConversations, ...planConversations]));\n    } catch (error) {\n      console.error('Error processing message:', error);\n      const errorResponse = {\n        id: Date.now() + 1,\n        type: 'assistant',\n        content: \"I'm sorry, I encountered an error processing your request. Please try again.\",\n        timestamp: new Date().toISOString(),\n        isError: true\n      };\n      setConversations(prev => [...prev, errorResponse]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const generateAIResponse = (message, planInfo) => {\n    const lowerMessage = message.toLowerCase();\n    const milestones = (planInfo === null || planInfo === void 0 ? void 0 : planInfo.milestones) || [];\n    const totalTasks = milestones.reduce((acc, milestone) => {\n      var _milestone$tasks;\n      return acc + (((_milestone$tasks = milestone.tasks) === null || _milestone$tasks === void 0 ? void 0 : _milestone$tasks.length) || 0);\n    }, 0);\n    const totalSubtasks = milestones.reduce((acc, milestone) => {\n      var _milestone$tasks2;\n      return acc + ((_milestone$tasks2 = milestone.tasks) === null || _milestone$tasks2 === void 0 ? void 0 : _milestone$tasks2.reduce((taskAcc, task) => {\n        var _task$subtasks;\n        return taskAcc + (((_task$subtasks = task.subtasks) === null || _task$subtasks === void 0 ? void 0 : _task$subtasks.length) || 0);\n      }, 0)) || 0;\n    }, 0);\n\n    // Analyze project context\n    const projectContext = {\n      name: (planInfo === null || planInfo === void 0 ? void 0 : planInfo.name) || 'your project',\n      milestoneCount: milestones.length,\n      taskCount: totalTasks,\n      subtaskCount: totalSubtasks,\n      milestoneNames: milestones.map(m => m.name).slice(0, 3) // First 3 milestone names\n    };\n\n    // Advanced intent recognition and response generation\n    if (lowerMessage.includes('milestone') && (lowerMessage.includes('add') || lowerMessage.includes('create') || lowerMessage.includes('new'))) {\n      return `I can help you add a new milestone to \"${projectContext.name}\". Currently, you have ${projectContext.milestoneCount} milestones: ${projectContext.milestoneNames.join(', ')}${projectContext.milestoneCount > 3 ? '...' : ''}.\n\nTo add a new milestone, I'll need:\n1. **Milestone name** - What should we call it?\n2. **Description** - What's the main objective?\n3. **Position** - Should it come before/after a specific milestone?\n4. **Tasks** - Any initial tasks to include?\n\nPlease provide these details, or just tell me the milestone name and I'll help structure the rest!`;\n    }\n    if (lowerMessage.includes('progress') || lowerMessage.includes('status') || lowerMessage.includes('overview')) {\n      const progressDetails = milestones.map(milestone => {\n        var _milestone$tasks3, _milestone$tasks4, _milestone$tasks4$fil;\n        const taskCount = ((_milestone$tasks3 = milestone.tasks) === null || _milestone$tasks3 === void 0 ? void 0 : _milestone$tasks3.length) || 0;\n        const completedTasks = ((_milestone$tasks4 = milestone.tasks) === null || _milestone$tasks4 === void 0 ? void 0 : (_milestone$tasks4$fil = _milestone$tasks4.filter(task => task.status === 'completed')) === null || _milestone$tasks4$fil === void 0 ? void 0 : _milestone$tasks4$fil.length) || 0;\n        return `• ${milestone.name}: ${completedTasks}/${taskCount} tasks completed`;\n      }).join('\\n');\n      return `Here's your project progress for \"${projectContext.name}\":\n\n📊 **Overall Statistics:**\n• ${projectContext.milestoneCount} milestones\n• ${projectContext.taskCount} total tasks\n• ${projectContext.subtaskCount} total subtasks\n\n📋 **Milestone Progress:**\n${progressDetails}\n\nWould you like me to:\n• Show detailed progress for a specific milestone?\n• Identify overdue or at-risk tasks?\n• Suggest next actions to move the project forward?`;\n    }\n    if (lowerMessage.includes('complete') || lowerMessage.includes('done') || lowerMessage.includes('finish')) {\n      const availableTasks = milestones.flatMap(milestone => {\n        var _milestone$tasks5;\n        return ((_milestone$tasks5 = milestone.tasks) === null || _milestone$tasks5 === void 0 ? void 0 : _milestone$tasks5.filter(task => task.status !== 'completed').map(task => `\"${task.name}\" in ${milestone.name}`)) || [];\n      }).slice(0, 5);\n      return `I can mark tasks as completed for you! Here are some pending tasks I found:\n\n${availableTasks.map(task => `• ${task}`).join('\\n')}\n\nTo mark a task as complete, just tell me:\n• \"Mark [task name] as completed\"\n• \"Complete the [task name] task\"\n• Or simply \"Done with [task name]\"\n\nWhich task would you like to mark as completed?`;\n    }\n    if (lowerMessage.includes('add') || lowerMessage.includes('create') || lowerMessage.includes('new')) {\n      if (lowerMessage.includes('task')) {\n        return `I can add new tasks to any of your milestones. You currently have ${projectContext.milestoneCount} milestones:\n\n${projectContext.milestoneNames.map((name, i) => `${i + 1}. ${name}`).join('\\n')}\n\nTo add a task, tell me:\n• **Which milestone** to add it to\n• **Task name** and description\n• **Any subtasks** to include\n\nExample: \"Add task 'Set up development environment' to the first milestone with subtasks for installing tools and configuring settings\"\n\nWhat task would you like to add?`;\n      }\n      return `I can help you add new content to \"${projectContext.name}\". I can create:\n\n🎯 **Milestones** - Major project phases\n📋 **Tasks** - Specific work items within milestones\n✅ **Subtasks** - Detailed steps for tasks\n📝 **Descriptions** - Enhanced details for any item\n\nWhat would you like to add? Just describe it naturally, like:\n• \"Add a milestone for user testing\"\n• \"Create a task for database setup in the development milestone\"\n• \"Add subtasks for the API integration task\"`;\n    }\n    if (lowerMessage.includes('delete') || lowerMessage.includes('remove')) {\n      return `I can help you remove items from your project. For safety, I'll always confirm before deleting anything.\n\nI can remove:\n• **Tasks** that are no longer needed\n• **Subtasks** that are redundant\n• **Completed items** to clean up the project\n• **Duplicate entries**\n\n⚠️ **Note:** I cannot delete milestones as they're core to your project structure.\n\nWhat would you like to remove? Please be specific about the item name and location.`;\n    }\n    if (lowerMessage.includes('update') || lowerMessage.includes('change') || lowerMessage.includes('edit') || lowerMessage.includes('modify')) {\n      return `I can update various aspects of your project \"${projectContext.name}\":\n\n📝 **Content Updates:**\n• Task and subtask descriptions\n• Milestone objectives\n• Due dates and priorities\n• Task assignments\n\n🔄 **Status Changes:**\n• Mark items as in-progress, completed, or blocked\n• Update milestone phases\n• Change task priorities\n\n📊 **Structural Changes:**\n• Move tasks between milestones\n• Reorder items\n• Split large tasks into smaller ones\n\nWhat would you like to update? Describe the change you want to make.`;\n    }\n    if (lowerMessage.includes('help') || lowerMessage.includes('what can you do') || lowerMessage.includes('capabilities')) {\n      return `I'm your AI Project Assistant for \"${projectContext.name}\"! Here's what I can do:\n\n🎯 **Project Management:**\n• Add/remove tasks, subtasks, and milestones\n• Update descriptions, statuses, and priorities\n• Mark items as completed or in-progress\n• Move tasks between milestones\n\n📊 **Project Analysis:**\n• Show progress reports and statistics\n• Identify bottlenecks and overdue items\n• Suggest next actions and optimizations\n• Generate project summaries\n\n🔍 **Smart Search:**\n• Find specific tasks or milestones\n• Filter by status, assignee, or due date\n• Locate related items across the project\n\n💡 **Recommendations:**\n• Suggest task breakdowns\n• Recommend milestone structures\n• Identify missing dependencies\n• Propose timeline optimizations\n\nJust tell me what you want to do in natural language - I'll understand and help you get it done!`;\n    }\n\n    // Default intelligent response\n    return `I understand you want to \"${message}\".\n\nBased on your project \"${projectContext.name}\" with ${projectContext.milestoneCount} milestones and ${projectContext.taskCount} tasks, I can help you:\n\n🎯 **Quick Actions:**\n• \"Show me project progress\"\n• \"Add a new task to [milestone name]\"\n• \"Mark [task name] as completed\"\n• \"Update the description for [item name]\"\n\n💡 **Smart Suggestions:**\n• \"What should I work on next?\"\n• \"Show me overdue items\"\n• \"Help me organize this milestone\"\n• \"Create a timeline for this project\"\n\nWhat specific action would you like me to take? I'm here to make managing your project easier!`;\n  };\n  const extractActions = message => {\n    const actions = [];\n    const lowerMessage = message.toLowerCase();\n\n    // Enhanced action extraction with context\n    const actionPatterns = [{\n      pattern: /(complete|done|finish|mark.*complete)/,\n      type: 'complete_task',\n      confidence: 0.9,\n      description: 'Mark task as completed'\n    }, {\n      pattern: /(add|create|new).*milestone/,\n      type: 'add_milestone',\n      confidence: 0.9,\n      description: 'Add new milestone'\n    }, {\n      pattern: /(add|create|new).*task/,\n      type: 'add_task',\n      confidence: 0.9,\n      description: 'Add new task'\n    }, {\n      pattern: /(add|create|new).*subtask/,\n      type: 'add_subtask',\n      confidence: 0.9,\n      description: 'Add new subtask'\n    }, {\n      pattern: /(delete|remove).*task/,\n      type: 'delete_task',\n      confidence: 0.8,\n      description: 'Delete task'\n    }, {\n      pattern: /(update|change|edit|modify)/,\n      type: 'update_item',\n      confidence: 0.8,\n      description: 'Update item details'\n    }, {\n      pattern: /(progress|status|overview)/,\n      type: 'show_progress',\n      confidence: 0.9,\n      description: 'Show project progress'\n    }, {\n      pattern: /(move|transfer).*task/,\n      type: 'move_task',\n      confidence: 0.8,\n      description: 'Move task between milestones'\n    }, {\n      pattern: /(assign|delegate)/,\n      type: 'assign_task',\n      confidence: 0.8,\n      description: 'Assign task to team member'\n    }, {\n      pattern: /(due date|deadline|schedule)/,\n      type: 'set_deadline',\n      confidence: 0.8,\n      description: 'Set or update due date'\n    }];\n\n    // Extract entities (task names, milestone names, etc.)\n    const entities = {\n      taskNames: [],\n      milestoneNames: [],\n      dates: [],\n      priorities: []\n    };\n\n    // Look for quoted text (likely task/milestone names)\n    const quotedText = message.match(/\"([^\"]+)\"/g);\n    if (quotedText) {\n      entities.taskNames = quotedText.map(q => q.replace(/\"/g, ''));\n    }\n\n    // Look for date patterns\n    const datePatterns = message.match(/\\b\\d{1,2}\\/\\d{1,2}\\/\\d{4}\\b|\\b\\d{1,2}-\\d{1,2}-\\d{4}\\b|tomorrow|today|next week|next month/gi);\n    if (datePatterns) {\n      entities.dates = datePatterns;\n    }\n\n    // Look for priority keywords\n    const priorityPatterns = message.match(/\\b(high|low|medium|urgent|critical|normal)\\s*priority\\b/gi);\n    if (priorityPatterns) {\n      entities.priorities = priorityPatterns;\n    }\n\n    // Match actions against patterns\n    actionPatterns.forEach(_ref2 => {\n      let {\n        pattern,\n        type,\n        confidence,\n        description\n      } = _ref2;\n      if (pattern.test(lowerMessage)) {\n        actions.push({\n          type,\n          confidence,\n          description,\n          entities: entities,\n          originalMessage: message\n        });\n      }\n    });\n    return actions;\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n  const formatTimestamp = timestamp => {\n    return new Date(timestamp).toLocaleTimeString('en-US', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '70vh',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 0,\n      sx: {\n        p: 2,\n        borderRadius: '12px 12px 0 0',\n        border: '1px solid #f0f0f0',\n        borderBottom: 'none',\n        backgroundColor: '#fafafa'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          sx: {\n            backgroundColor: mainYellowColor,\n            width: 40,\n            height: 40\n          },\n          children: /*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"mdi:robot\",\n            width: 24,\n            height: 24,\n            color: \"#fff\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 540,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              fontWeight: 600,\n              color: '#333'\n            },\n            children: \"AI Project Agent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 550,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            sx: {\n              color: '#666',\n              fontFamily: '\"Recursive Variable\", sans-serif'\n            },\n            children: [\"Managing: \", planInfo === null || planInfo === void 0 ? void 0 : planInfo.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 549,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n          label: \"Beta\",\n          size: \"small\",\n          sx: {\n            backgroundColor: `${mainYellowColor}20`,\n            color: mainYellowColor,\n            fontWeight: 600,\n            ml: 'auto'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 570,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 539,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 529,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 0,\n      sx: {\n        flex: 1,\n        border: '1px solid #f0f0f0',\n        borderTop: 'none',\n        borderBottom: 'none',\n        overflow: 'auto',\n        p: 2,\n        backgroundColor: '#fff'\n      },\n      children: conversations.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          justifyContent: 'center',\n          height: '100%',\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          sx: {\n            backgroundColor: `${mainYellowColor}20`,\n            width: 60,\n            height: 60,\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"mdi:robot\",\n            width: 32,\n            height: 32,\n            color: mainYellowColor\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 615,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 607,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontFamily: '\"Recursive Variable\", sans-serif',\n            fontWeight: 600,\n            color: '#333',\n            mb: 1\n          },\n          children: \"Welcome to AI Project Agent\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 617,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            color: '#666',\n            fontFamily: '\"Recursive Variable\", sans-serif',\n            maxWidth: 400\n          },\n          children: \"I'm here to help you manage your project. Ask me questions or request changes to tasks, milestones, and more!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 628,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 597,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Box, {\n        children: [conversations.map(message => /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start',\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              maxWidth: '70%',\n              display: 'flex',\n              flexDirection: message.type === 'user' ? 'row-reverse' : 'row',\n              alignItems: 'flex-start',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                width: 32,\n                height: 32,\n                backgroundColor: message.type === 'user' ? '#e0e0e0' : mainYellowColor\n              },\n              children: /*#__PURE__*/_jsxDEV(Iconify, {\n                icon: message.type === 'user' ? \"material-symbols:person\" : \"mdi:robot\",\n                width: 18,\n                height: 18,\n                color: message.type === 'user' ? '#666' : '#fff'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 666,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 659,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Paper, {\n                elevation: 0,\n                sx: {\n                  p: 1.5,\n                  borderRadius: '12px',\n                  backgroundColor: message.type === 'user' ? mainYellowColor : '#f5f5f5',\n                  color: message.type === 'user' ? '#fff' : '#333',\n                  border: message.isError ? '1px solid #f44336' : 'none'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontFamily: '\"Recursive Variable\", sans-serif',\n                    lineHeight: 1.5,\n                    whiteSpace: 'pre-line'\n                  },\n                  children: message.content\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 684,\n                  columnNumber: 23\n                }, this), message.type === 'assistant' && message.actions && message.actions.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n                  sx: {\n                    mt: 1.5,\n                    display: 'flex',\n                    flexWrap: 'wrap',\n                    gap: 1\n                  },\n                  children: message.actions.slice(0, 3).map((action, actionIndex) => /*#__PURE__*/_jsxDEV(Chip, {\n                    label: action.description,\n                    size: \"small\",\n                    onClick: () => {\n                      // Handle quick action\n                      setCurrentMessage(action.originalMessage || `Please ${action.description.toLowerCase()}`);\n                      if (inputRef.current) {\n                        inputRef.current.focus();\n                      }\n                    },\n                    sx: {\n                      backgroundColor: '#fff',\n                      border: `1px solid ${mainYellowColor}`,\n                      color: mainYellowColor,\n                      fontSize: '0.7rem',\n                      height: '24px',\n                      cursor: 'pointer',\n                      '&:hover': {\n                        backgroundColor: `${mainYellowColor}10`\n                      }\n                    }\n                  }, actionIndex, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 699,\n                    columnNumber: 29\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 697,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 674,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  color: '#999',\n                  fontFamily: '\"Recursive Variable\", sans-serif',\n                  fontSize: '0.7rem',\n                  mt: 0.5,\n                  display: 'block',\n                  textAlign: message.type === 'user' ? 'right' : 'left'\n                },\n                children: formatTimestamp(message.timestamp)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 726,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 673,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 650,\n            columnNumber: 17\n          }, this)\n        }, message.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 642,\n          columnNumber: 15\n        }, this)), isLoading && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'flex-start',\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'flex-start',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                width: 32,\n                height: 32,\n                backgroundColor: mainYellowColor\n              },\n              children: /*#__PURE__*/_jsxDEV(Iconify, {\n                icon: \"mdi:robot\",\n                width: 18,\n                height: 18,\n                color: \"#fff\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 753,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 746,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Paper, {\n              elevation: 0,\n              sx: {\n                p: 1.5,\n                borderRadius: '12px',\n                backgroundColor: '#f5f5f5',\n                display: 'flex',\n                alignItems: 'center',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 16,\n                sx: {\n                  color: mainYellowColor\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 766,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontFamily: '\"Recursive Variable\", sans-serif',\n                  color: '#666'\n                },\n                children: \"Thinking...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 767,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 755,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 745,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 744,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: messagesEndRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 780,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 640,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 584,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 0,\n      sx: {\n        p: 2,\n        borderRadius: '0 0 12px 12px',\n        border: '1px solid #f0f0f0',\n        borderTop: 'none'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 1,\n          alignItems: 'flex-end'\n        },\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          inputRef: inputRef,\n          value: currentMessage,\n          onChange: e => setCurrentMessage(e.target.value),\n          onKeyPress: handleKeyPress,\n          placeholder: \"Ask me anything about your project...\",\n          multiline: true,\n          maxRows: 3,\n          fullWidth: true,\n          variant: \"outlined\",\n          disabled: isLoading,\n          sx: {\n            '& .MuiOutlinedInput-root': {\n              borderRadius: '8px',\n              fontFamily: '\"Recursive Variable\", sans-serif'\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 796,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"Send message\",\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: () => handleSendMessage(),\n            disabled: !currentMessage.trim() || isLoading,\n            sx: {\n              backgroundColor: currentMessage.trim() && !isLoading ? mainYellowColor : '#f0f0f0',\n              color: currentMessage.trim() && !isLoading ? '#fff' : '#999',\n              '&:hover': {\n                backgroundColor: currentMessage.trim() && !isLoading ? '#E69500' : '#f0f0f0'\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"material-symbols:send\",\n              width: 20,\n              height: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 826,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 815,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 814,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 795,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 786,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 527,\n    columnNumber: 5\n  }, this);\n};\n_s(AgentTab, \"B+54LH24BzlpTZtaSD6TTRLr7Gg=\", false, function () {\n  return [useLocation];\n});\n_c = AgentTab;\nexport default AgentTab;\nvar _c;\n$RefreshReg$(_c, \"AgentTab\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Box", "Typography", "Paper", "TextField", "IconButton", "Avatar", "Divider", "CircularProgress", "Chip", "<PERSON><PERSON><PERSON>", "Iconify", "mainYellowColor", "axios", "APIURL", "getHeaders", "useLocation", "jsxDEV", "_jsxDEV", "AgentTab", "_ref", "_s", "planInfo", "onPlanUpdate", "conversations", "setConversations", "currentMessage", "setCurrentMessage", "isLoading", "setIsLoading", "messagesEndRef", "inputRef", "error", "setError", "location", "_location$state", "savedConversations", "JSON", "parse", "localStorage", "getItem", "planConversations", "filter", "conv", "planId", "id", "pendingMessage", "_messageData$planInfo", "messageData", "handleSendMessage", "message", "removeItem", "console", "state", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "executeAction", "action", "originalMessage", "type", "entities", "milestoneName", "taskNames", "length", "patterns", "pattern", "match", "trim", "response", "post", "plan_slug", "slug", "data", "name", "description", "headers", "success", "toLowerCase", "_error$response", "_error$response$data", "messageText", "arguments", "undefined", "userMessage", "Date", "now", "content", "timestamp", "toISOString", "newConversations", "actions", "extractActions", "aiResponse", "result", "actionResult", "generateAIResponse", "updatedConversations", "allConversations", "otherPlanConversations", "map", "planName", "setItem", "stringify", "errorResponse", "isError", "prev", "lowerMessage", "milestones", "totalTasks", "reduce", "acc", "milestone", "_milestone$tasks", "tasks", "totalSubtasks", "_milestone$tasks2", "taskAcc", "task", "_task$subtasks", "subtasks", "projectContext", "milestoneCount", "taskCount", "subtaskCount", "milestoneNames", "m", "slice", "includes", "join", "progressDetails", "_milestone$tasks3", "_milestone$tasks4", "_milestone$tasks4$fil", "completedTasks", "status", "availableTasks", "flatMap", "_milestone$tasks5", "i", "actionPatterns", "confidence", "dates", "priorities", "quotedText", "q", "replace", "datePatterns", "priorityPatterns", "for<PERSON>ach", "_ref2", "test", "push", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "formatTimestamp", "toLocaleTimeString", "hour", "minute", "sx", "height", "display", "flexDirection", "children", "elevation", "p", "borderRadius", "border", "borderBottom", "backgroundColor", "alignItems", "gap", "width", "icon", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontFamily", "fontWeight", "label", "size", "ml", "flex", "borderTop", "overflow", "justifyContent", "textAlign", "mb", "max<PERSON><PERSON><PERSON>", "lineHeight", "whiteSpace", "mt", "flexWrap", "actionIndex", "onClick", "focus", "fontSize", "cursor", "ref", "value", "onChange", "target", "onKeyPress", "placeholder", "multiline", "maxRows", "fullWidth", "disabled", "title", "_c", "$RefreshReg$"], "sources": ["C:/ignition/ignition-ui/src/views/plan/detail/components/AgentTab.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  TextField,\n  IconButton,\n  Avatar,\n  Divider,\n  CircularProgress,\n  Chip,\n  Tooltip\n} from '@mui/material';\nimport Iconify from 'components/Iconify/index';\nimport { mainYellowColor } from \"helpers/constants\";\nimport axios from 'axios';\nimport { APIURL, getHeaders } from 'helpers/constants';\nimport { useLocation } from 'react-router-dom';\n\nconst AgentTab = ({ planInfo, onPlanUpdate }) => {\n  const [conversations, setConversations] = useState([]);\n  const [currentMessage, setCurrentMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef(null);\n  const inputRef = useRef(null);\n  const [error, setError] = useState(null);\n  const location = useLocation();\n\n  // Load conversations from localStorage\n  useEffect(() => {\n    const savedConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\n    const planConversations = savedConversations.filter(conv => conv.planId === planInfo?.id);\n    setConversations(planConversations);\n\n    // Check for pending message from ChatbotBar\n    const pendingMessage = localStorage.getItem('pending_agent_message');\n    if (pendingMessage) {\n      try {\n        const messageData = JSON.parse(pendingMessage);\n        if (messageData.planInfo?.id === planInfo?.id) {\n          handleSendMessage(messageData.message);\n        }\n        // Clear the pending message\n        localStorage.removeItem('pending_agent_message');\n      } catch (error) {\n        console.error('Error processing pending message:', error);\n        localStorage.removeItem('pending_agent_message');\n      }\n    }\n\n    // If coming from chatbot bar via navigation state, add the initial message\n    if (location.state?.message) {\n      handleSendMessage(location.state.message);\n    }\n  }, [planInfo?.id, location.state]);\n\n  // Auto scroll to bottom\n  useEffect(() => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  }, [conversations]);\n\n  const executeAction = async (action, originalMessage) => {\n    try {\n      const { type, entities } = action;\n\n      if (type === 'add_milestone') {\n        // Extract milestone name from the message\n        let milestoneName = 'New Milestone';\n\n        // Try to extract from quoted text first\n        if (entities.taskNames && entities.taskNames.length > 0) {\n          milestoneName = entities.taskNames[0];\n        } else {\n          // Try to extract from common patterns\n          const patterns = [\n            /add.*milestone.*[\"']([^\"']+)[\"']/i,\n            /create.*milestone.*[\"']([^\"']+)[\"']/i,\n            /new milestone.*[\"']([^\"']+)[\"']/i,\n            /milestone.*[\"']([^\"']+)[\"']/i,\n            /add.*milestone.*(?:for|about|called)\\s+([^.!?]+)/i,\n            /create.*milestone.*(?:for|about|called)\\s+([^.!?]+)/i\n          ];\n\n          for (const pattern of patterns) {\n            const match = originalMessage.match(pattern);\n            if (match && match[1]) {\n              milestoneName = match[1].trim();\n              break;\n            }\n          }\n        }\n\n        const response = await axios.post(\n          `${APIURL}/api/assistant/plan-action`,\n          {\n            action: 'add_milestone',\n            plan_slug: planInfo.slug,\n            data: {\n              name: milestoneName,\n              description: `Milestone created by AI agent based on: \"${originalMessage}\"`\n            },\n            message: originalMessage\n          },\n          { headers: getHeaders() }\n        );\n\n        return {\n          success: true,\n          message: `✅ Successfully created milestone \"${milestoneName}\"!\n\nThe new milestone has been added to your project. You can now:\n• Add tasks to this milestone\n• Set specific goals and deadlines\n• Track progress as you work\n\nWould you like me to add some initial tasks to this milestone?`,\n          data: response.data\n        };\n      }\n\n      // Handle other action types here...\n      return {\n        success: false,\n        message: `I understand you want to ${action.description.toLowerCase()}, but I'm still learning how to do that. For now, I can help you add milestones to your project. What else can I help you with?`\n      };\n\n    } catch (error) {\n      console.error('Error executing action:', error);\n      return {\n        success: false,\n        message: `Sorry, I encountered an error while trying to ${action.description.toLowerCase()}: ${error.response?.data?.error || error.message}. Please try again or rephrase your request.`\n      };\n    }\n  };\n\n  const handleSendMessage = async (messageText = currentMessage) => {\n    if (!messageText.trim() || isLoading) return;\n\n    setIsLoading(true);\n    const userMessage = {\n      id: Date.now(),\n      type: 'user',\n      content: messageText.trim(),\n      timestamp: new Date().toISOString()\n    };\n\n    const newConversations = [...conversations, userMessage];\n    setConversations(newConversations);\n    setCurrentMessage('');\n\n    try {\n      // Extract actions from the message\n      const actions = extractActions(messageText);\n      let aiResponse;\n\n      if (actions.length > 0) {\n        // Execute the first action (for now, we'll handle one action at a time)\n        const action = actions[0];\n        const result = await executeAction(action, messageText);\n\n        aiResponse = {\n          id: Date.now() + 1,\n          type: 'assistant',\n          content: result.message,\n          timestamp: new Date().toISOString(),\n          actions: actions,\n          actionResult: result\n        };\n\n        // If the action was successful and modified the plan, trigger a refresh\n        if (result.success && onPlanUpdate) {\n          onPlanUpdate();\n        }\n      } else {\n        // Generate a contextual response without actions\n        aiResponse = {\n          id: Date.now() + 1,\n          type: 'assistant',\n          content: generateAIResponse(messageText, planInfo),\n          timestamp: new Date().toISOString(),\n          actions: []\n        };\n      }\n\n      const updatedConversations = [...newConversations, aiResponse];\n      setConversations(updatedConversations);\n\n      // Save to localStorage\n      const allConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\n      const otherPlanConversations = allConversations.filter(conv => conv.planId !== planInfo?.id);\n      const planConversations = updatedConversations.map(conv => ({\n        ...conv,\n        planId: planInfo?.id,\n        planName: planInfo?.name\n      }));\n      \n      localStorage.setItem('agent_conversations', JSON.stringify([\n        ...otherPlanConversations,\n        ...planConversations\n      ]));\n\n    } catch (error) {\n      console.error('Error processing message:', error);\n      const errorResponse = {\n        id: Date.now() + 1,\n        type: 'assistant',\n        content: \"I'm sorry, I encountered an error processing your request. Please try again.\",\n        timestamp: new Date().toISOString(),\n        isError: true\n      };\n      setConversations(prev => [...prev, errorResponse]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const generateAIResponse = (message, planInfo) => {\n    const lowerMessage = message.toLowerCase();\n    const milestones = planInfo?.milestones || [];\n    const totalTasks = milestones.reduce((acc, milestone) => acc + (milestone.tasks?.length || 0), 0);\n    const totalSubtasks = milestones.reduce((acc, milestone) =>\n      acc + milestone.tasks?.reduce((taskAcc, task) => taskAcc + (task.subtasks?.length || 0), 0) || 0, 0);\n\n    // Analyze project context\n    const projectContext = {\n      name: planInfo?.name || 'your project',\n      milestoneCount: milestones.length,\n      taskCount: totalTasks,\n      subtaskCount: totalSubtasks,\n      milestoneNames: milestones.map(m => m.name).slice(0, 3), // First 3 milestone names\n    };\n\n    // Advanced intent recognition and response generation\n    if (lowerMessage.includes('milestone') && (lowerMessage.includes('add') || lowerMessage.includes('create') || lowerMessage.includes('new'))) {\n      return `I can help you add a new milestone to \"${projectContext.name}\". Currently, you have ${projectContext.milestoneCount} milestones: ${projectContext.milestoneNames.join(', ')}${projectContext.milestoneCount > 3 ? '...' : ''}.\n\nTo add a new milestone, I'll need:\n1. **Milestone name** - What should we call it?\n2. **Description** - What's the main objective?\n3. **Position** - Should it come before/after a specific milestone?\n4. **Tasks** - Any initial tasks to include?\n\nPlease provide these details, or just tell me the milestone name and I'll help structure the rest!`;\n    }\n\n    if (lowerMessage.includes('progress') || lowerMessage.includes('status') || lowerMessage.includes('overview')) {\n      const progressDetails = milestones.map(milestone => {\n        const taskCount = milestone.tasks?.length || 0;\n        const completedTasks = milestone.tasks?.filter(task => task.status === 'completed')?.length || 0;\n        return `• ${milestone.name}: ${completedTasks}/${taskCount} tasks completed`;\n      }).join('\\n');\n\n      return `Here's your project progress for \"${projectContext.name}\":\n\n📊 **Overall Statistics:**\n• ${projectContext.milestoneCount} milestones\n• ${projectContext.taskCount} total tasks\n• ${projectContext.subtaskCount} total subtasks\n\n📋 **Milestone Progress:**\n${progressDetails}\n\nWould you like me to:\n• Show detailed progress for a specific milestone?\n• Identify overdue or at-risk tasks?\n• Suggest next actions to move the project forward?`;\n    }\n\n    if (lowerMessage.includes('complete') || lowerMessage.includes('done') || lowerMessage.includes('finish')) {\n      const availableTasks = milestones.flatMap(milestone =>\n        milestone.tasks?.filter(task => task.status !== 'completed').map(task =>\n          `\"${task.name}\" in ${milestone.name}`\n        ) || []\n      ).slice(0, 5);\n\n      return `I can mark tasks as completed for you! Here are some pending tasks I found:\n\n${availableTasks.map(task => `• ${task}`).join('\\n')}\n\nTo mark a task as complete, just tell me:\n• \"Mark [task name] as completed\"\n• \"Complete the [task name] task\"\n• Or simply \"Done with [task name]\"\n\nWhich task would you like to mark as completed?`;\n    }\n\n    if (lowerMessage.includes('add') || lowerMessage.includes('create') || lowerMessage.includes('new')) {\n      if (lowerMessage.includes('task')) {\n        return `I can add new tasks to any of your milestones. You currently have ${projectContext.milestoneCount} milestones:\n\n${projectContext.milestoneNames.map((name, i) => `${i + 1}. ${name}`).join('\\n')}\n\nTo add a task, tell me:\n• **Which milestone** to add it to\n• **Task name** and description\n• **Any subtasks** to include\n\nExample: \"Add task 'Set up development environment' to the first milestone with subtasks for installing tools and configuring settings\"\n\nWhat task would you like to add?`;\n      }\n\n      return `I can help you add new content to \"${projectContext.name}\". I can create:\n\n🎯 **Milestones** - Major project phases\n📋 **Tasks** - Specific work items within milestones\n✅ **Subtasks** - Detailed steps for tasks\n📝 **Descriptions** - Enhanced details for any item\n\nWhat would you like to add? Just describe it naturally, like:\n• \"Add a milestone for user testing\"\n• \"Create a task for database setup in the development milestone\"\n• \"Add subtasks for the API integration task\"`;\n    }\n\n    if (lowerMessage.includes('delete') || lowerMessage.includes('remove')) {\n      return `I can help you remove items from your project. For safety, I'll always confirm before deleting anything.\n\nI can remove:\n• **Tasks** that are no longer needed\n• **Subtasks** that are redundant\n• **Completed items** to clean up the project\n• **Duplicate entries**\n\n⚠️ **Note:** I cannot delete milestones as they're core to your project structure.\n\nWhat would you like to remove? Please be specific about the item name and location.`;\n    }\n\n    if (lowerMessage.includes('update') || lowerMessage.includes('change') || lowerMessage.includes('edit') || lowerMessage.includes('modify')) {\n      return `I can update various aspects of your project \"${projectContext.name}\":\n\n📝 **Content Updates:**\n• Task and subtask descriptions\n• Milestone objectives\n• Due dates and priorities\n• Task assignments\n\n🔄 **Status Changes:**\n• Mark items as in-progress, completed, or blocked\n• Update milestone phases\n• Change task priorities\n\n📊 **Structural Changes:**\n• Move tasks between milestones\n• Reorder items\n• Split large tasks into smaller ones\n\nWhat would you like to update? Describe the change you want to make.`;\n    }\n\n    if (lowerMessage.includes('help') || lowerMessage.includes('what can you do') || lowerMessage.includes('capabilities')) {\n      return `I'm your AI Project Assistant for \"${projectContext.name}\"! Here's what I can do:\n\n🎯 **Project Management:**\n• Add/remove tasks, subtasks, and milestones\n• Update descriptions, statuses, and priorities\n• Mark items as completed or in-progress\n• Move tasks between milestones\n\n📊 **Project Analysis:**\n• Show progress reports and statistics\n• Identify bottlenecks and overdue items\n• Suggest next actions and optimizations\n• Generate project summaries\n\n🔍 **Smart Search:**\n• Find specific tasks or milestones\n• Filter by status, assignee, or due date\n• Locate related items across the project\n\n💡 **Recommendations:**\n• Suggest task breakdowns\n• Recommend milestone structures\n• Identify missing dependencies\n• Propose timeline optimizations\n\nJust tell me what you want to do in natural language - I'll understand and help you get it done!`;\n    }\n\n    // Default intelligent response\n    return `I understand you want to \"${message}\".\n\nBased on your project \"${projectContext.name}\" with ${projectContext.milestoneCount} milestones and ${projectContext.taskCount} tasks, I can help you:\n\n🎯 **Quick Actions:**\n• \"Show me project progress\"\n• \"Add a new task to [milestone name]\"\n• \"Mark [task name] as completed\"\n• \"Update the description for [item name]\"\n\n💡 **Smart Suggestions:**\n• \"What should I work on next?\"\n• \"Show me overdue items\"\n• \"Help me organize this milestone\"\n• \"Create a timeline for this project\"\n\nWhat specific action would you like me to take? I'm here to make managing your project easier!`;\n  };\n\n  const extractActions = (message) => {\n    const actions = [];\n    const lowerMessage = message.toLowerCase();\n\n    // Enhanced action extraction with context\n    const actionPatterns = [\n      {\n        pattern: /(complete|done|finish|mark.*complete)/,\n        type: 'complete_task',\n        confidence: 0.9,\n        description: 'Mark task as completed'\n      },\n      {\n        pattern: /(add|create|new).*milestone/,\n        type: 'add_milestone',\n        confidence: 0.9,\n        description: 'Add new milestone'\n      },\n      {\n        pattern: /(add|create|new).*task/,\n        type: 'add_task',\n        confidence: 0.9,\n        description: 'Add new task'\n      },\n      {\n        pattern: /(add|create|new).*subtask/,\n        type: 'add_subtask',\n        confidence: 0.9,\n        description: 'Add new subtask'\n      },\n      {\n        pattern: /(delete|remove).*task/,\n        type: 'delete_task',\n        confidence: 0.8,\n        description: 'Delete task'\n      },\n      {\n        pattern: /(update|change|edit|modify)/,\n        type: 'update_item',\n        confidence: 0.8,\n        description: 'Update item details'\n      },\n      {\n        pattern: /(progress|status|overview)/,\n        type: 'show_progress',\n        confidence: 0.9,\n        description: 'Show project progress'\n      },\n      {\n        pattern: /(move|transfer).*task/,\n        type: 'move_task',\n        confidence: 0.8,\n        description: 'Move task between milestones'\n      },\n      {\n        pattern: /(assign|delegate)/,\n        type: 'assign_task',\n        confidence: 0.8,\n        description: 'Assign task to team member'\n      },\n      {\n        pattern: /(due date|deadline|schedule)/,\n        type: 'set_deadline',\n        confidence: 0.8,\n        description: 'Set or update due date'\n      }\n    ];\n\n    // Extract entities (task names, milestone names, etc.)\n    const entities = {\n      taskNames: [],\n      milestoneNames: [],\n      dates: [],\n      priorities: []\n    };\n\n    // Look for quoted text (likely task/milestone names)\n    const quotedText = message.match(/\"([^\"]+)\"/g);\n    if (quotedText) {\n      entities.taskNames = quotedText.map(q => q.replace(/\"/g, ''));\n    }\n\n    // Look for date patterns\n    const datePatterns = message.match(/\\b\\d{1,2}\\/\\d{1,2}\\/\\d{4}\\b|\\b\\d{1,2}-\\d{1,2}-\\d{4}\\b|tomorrow|today|next week|next month/gi);\n    if (datePatterns) {\n      entities.dates = datePatterns;\n    }\n\n    // Look for priority keywords\n    const priorityPatterns = message.match(/\\b(high|low|medium|urgent|critical|normal)\\s*priority\\b/gi);\n    if (priorityPatterns) {\n      entities.priorities = priorityPatterns;\n    }\n\n    // Match actions against patterns\n    actionPatterns.forEach(({ pattern, type, confidence, description }) => {\n      if (pattern.test(lowerMessage)) {\n        actions.push({\n          type,\n          confidence,\n          description,\n          entities: entities,\n          originalMessage: message\n        });\n      }\n    });\n\n    return actions;\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  const formatTimestamp = (timestamp) => {\n    return new Date(timestamp).toLocaleTimeString('en-US', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  return (\n    <Box sx={{ height: '70vh', display: 'flex', flexDirection: 'column' }}>\n      {/* Header */}\n      <Paper\n        elevation={0}\n        sx={{\n          p: 2,\n          borderRadius: '12px 12px 0 0',\n          border: '1px solid #f0f0f0',\n          borderBottom: 'none',\n          backgroundColor: '#fafafa'\n        }}\n      >\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n          <Avatar\n            sx={{\n              backgroundColor: mainYellowColor,\n              width: 40,\n              height: 40\n            }}\n          >\n            <Iconify icon=\"mdi:robot\" width={24} height={24} color=\"#fff\" />\n          </Avatar>\n          <Box>\n            <Typography\n              variant=\"h6\"\n              sx={{\n                fontFamily: '\"Recursive Variable\", sans-serif',\n                fontWeight: 600,\n                color: '#333'\n              }}\n            >\n              AI Project Agent\n            </Typography>\n            <Typography\n              variant=\"caption\"\n              sx={{\n                color: '#666',\n                fontFamily: '\"Recursive Variable\", sans-serif'\n              }}\n            >\n              Managing: {planInfo?.name}\n            </Typography>\n          </Box>\n          <Chip\n            label=\"Beta\"\n            size=\"small\"\n            sx={{\n              backgroundColor: `${mainYellowColor}20`,\n              color: mainYellowColor,\n              fontWeight: 600,\n              ml: 'auto'\n            }}\n          />\n        </Box>\n      </Paper>\n\n      {/* Messages Area */}\n      <Paper\n        elevation={0}\n        sx={{\n          flex: 1,\n          border: '1px solid #f0f0f0',\n          borderTop: 'none',\n          borderBottom: 'none',\n          overflow: 'auto',\n          p: 2,\n          backgroundColor: '#fff'\n        }}\n      >\n        {conversations.length === 0 ? (\n          <Box\n            sx={{\n              display: 'flex',\n              flexDirection: 'column',\n              alignItems: 'center',\n              justifyContent: 'center',\n              height: '100%',\n              textAlign: 'center'\n            }}\n          >\n            <Avatar\n              sx={{\n                backgroundColor: `${mainYellowColor}20`,\n                width: 60,\n                height: 60,\n                mb: 2\n              }}\n            >\n              <Iconify icon=\"mdi:robot\" width={32} height={32} color={mainYellowColor} />\n            </Avatar>\n            <Typography\n              variant=\"h6\"\n              sx={{\n                fontFamily: '\"Recursive Variable\", sans-serif',\n                fontWeight: 600,\n                color: '#333',\n                mb: 1\n              }}\n            >\n              Welcome to AI Project Agent\n            </Typography>\n            <Typography\n              variant=\"body2\"\n              sx={{\n                color: '#666',\n                fontFamily: '\"Recursive Variable\", sans-serif',\n                maxWidth: 400\n              }}\n            >\n              I'm here to help you manage your project. Ask me questions or request changes to tasks, milestones, and more!\n            </Typography>\n          </Box>\n        ) : (\n          <Box>\n            {conversations.map((message) => (\n              <Box\n                key={message.id}\n                sx={{\n                  display: 'flex',\n                  justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start',\n                  mb: 2\n                }}\n              >\n                <Box\n                  sx={{\n                    maxWidth: '70%',\n                    display: 'flex',\n                    flexDirection: message.type === 'user' ? 'row-reverse' : 'row',\n                    alignItems: 'flex-start',\n                    gap: 1\n                  }}\n                >\n                  <Avatar\n                    sx={{\n                      width: 32,\n                      height: 32,\n                      backgroundColor: message.type === 'user' ? '#e0e0e0' : mainYellowColor\n                    }}\n                  >\n                    <Iconify\n                      icon={message.type === 'user' ? \"material-symbols:person\" : \"mdi:robot\"}\n                      width={18}\n                      height={18}\n                      color={message.type === 'user' ? '#666' : '#fff'}\n                    />\n                  </Avatar>\n                  <Box>\n                    <Paper\n                      elevation={0}\n                      sx={{\n                        p: 1.5,\n                        borderRadius: '12px',\n                        backgroundColor: message.type === 'user' ? mainYellowColor : '#f5f5f5',\n                        color: message.type === 'user' ? '#fff' : '#333',\n                        border: message.isError ? '1px solid #f44336' : 'none'\n                      }}\n                    >\n                      <Typography\n                        variant=\"body2\"\n                        sx={{\n                          fontFamily: '\"Recursive Variable\", sans-serif',\n                          lineHeight: 1.5,\n                          whiteSpace: 'pre-line'\n                        }}\n                      >\n                        {message.content}\n                      </Typography>\n\n                      {/* Quick Action Buttons for AI responses */}\n                      {message.type === 'assistant' && message.actions && message.actions.length > 0 && (\n                        <Box sx={{ mt: 1.5, display: 'flex', flexWrap: 'wrap', gap: 1 }}>\n                          {message.actions.slice(0, 3).map((action, actionIndex) => (\n                            <Chip\n                              key={actionIndex}\n                              label={action.description}\n                              size=\"small\"\n                              onClick={() => {\n                                // Handle quick action\n                                setCurrentMessage(action.originalMessage || `Please ${action.description.toLowerCase()}`);\n                                if (inputRef.current) {\n                                  inputRef.current.focus();\n                                }\n                              }}\n                              sx={{\n                                backgroundColor: '#fff',\n                                border: `1px solid ${mainYellowColor}`,\n                                color: mainYellowColor,\n                                fontSize: '0.7rem',\n                                height: '24px',\n                                cursor: 'pointer',\n                                '&:hover': {\n                                  backgroundColor: `${mainYellowColor}10`\n                                }\n                              }}\n                            />\n                          ))}\n                        </Box>\n                      )}\n                    </Paper>\n                    <Typography\n                      variant=\"caption\"\n                      sx={{\n                        color: '#999',\n                        fontFamily: '\"Recursive Variable\", sans-serif',\n                        fontSize: '0.7rem',\n                        mt: 0.5,\n                        display: 'block',\n                        textAlign: message.type === 'user' ? 'right' : 'left'\n                      }}\n                    >\n                      {formatTimestamp(message.timestamp)}\n                    </Typography>\n                  </Box>\n                </Box>\n              </Box>\n            ))}\n            {isLoading && (\n              <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 2 }}>\n                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>\n                  <Avatar\n                    sx={{\n                      width: 32,\n                      height: 32,\n                      backgroundColor: mainYellowColor\n                    }}\n                  >\n                    <Iconify icon=\"mdi:robot\" width={18} height={18} color=\"#fff\" />\n                  </Avatar>\n                  <Paper\n                    elevation={0}\n                    sx={{\n                      p: 1.5,\n                      borderRadius: '12px',\n                      backgroundColor: '#f5f5f5',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 1\n                    }}\n                  >\n                    <CircularProgress size={16} sx={{ color: mainYellowColor }} />\n                    <Typography\n                      variant=\"body2\"\n                      sx={{\n                        fontFamily: '\"Recursive Variable\", sans-serif',\n                        color: '#666'\n                      }}\n                    >\n                      Thinking...\n                    </Typography>\n                  </Paper>\n                </Box>\n              </Box>\n            )}\n            <div ref={messagesEndRef} />\n          </Box>\n        )}\n      </Paper>\n\n      {/* Input Area */}\n      <Paper\n        elevation={0}\n        sx={{\n          p: 2,\n          borderRadius: '0 0 12px 12px',\n          border: '1px solid #f0f0f0',\n          borderTop: 'none'\n        }}\n      >\n        <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>\n          <TextField\n            inputRef={inputRef}\n            value={currentMessage}\n            onChange={(e) => setCurrentMessage(e.target.value)}\n            onKeyPress={handleKeyPress}\n            placeholder=\"Ask me anything about your project...\"\n            multiline\n            maxRows={3}\n            fullWidth\n            variant=\"outlined\"\n            disabled={isLoading}\n            sx={{\n              '& .MuiOutlinedInput-root': {\n                borderRadius: '8px',\n                fontFamily: '\"Recursive Variable\", sans-serif'\n              }\n            }}\n          />\n          <Tooltip title=\"Send message\">\n            <IconButton\n              onClick={() => handleSendMessage()}\n              disabled={!currentMessage.trim() || isLoading}\n              sx={{\n                backgroundColor: currentMessage.trim() && !isLoading ? mainYellowColor : '#f0f0f0',\n                color: currentMessage.trim() && !isLoading ? '#fff' : '#999',\n                '&:hover': {\n                  backgroundColor: currentMessage.trim() && !isLoading ? '#E69500' : '#f0f0f0'\n                }\n              }}\n            >\n              <Iconify icon=\"material-symbols:send\" width={20} height={20} />\n            </IconButton>\n          </Tooltip>\n        </Box>\n      </Paper>\n    </Box>\n  );\n};\n\nexport default AgentTab;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,UAAU,EACVC,MAAM,EACNC,OAAO,EACPC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,QACF,eAAe;AACtB,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,UAAU,QAAQ,mBAAmB;AACtD,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,QAAQ,GAAGC,IAAA,IAAgC;EAAAC,EAAA;EAAA,IAA/B;IAAEC,QAAQ;IAAEC;EAAa,CAAC,GAAAH,IAAA;EAC1C,MAAM,CAACI,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC4B,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC8B,SAAS,EAAEC,YAAY,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAMgC,cAAc,GAAG9B,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM+B,QAAQ,GAAG/B,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAM,CAACgC,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAMoC,QAAQ,GAAGlB,WAAW,CAAC,CAAC;;EAE9B;EACAjB,SAAS,CAAC,MAAM;IAAA,IAAAoC,eAAA;IACd,MAAMC,kBAAkB,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC,IAAI,IAAI,CAAC;IAC1F,MAAMC,iBAAiB,GAAGL,kBAAkB,CAACM,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,MAAM,MAAKtB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuB,EAAE,EAAC;IACzFpB,gBAAgB,CAACgB,iBAAiB,CAAC;;IAEnC;IACA,MAAMK,cAAc,GAAGP,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC;IACpE,IAAIM,cAAc,EAAE;MAClB,IAAI;QAAA,IAAAC,qBAAA;QACF,MAAMC,WAAW,GAAGX,IAAI,CAACC,KAAK,CAACQ,cAAc,CAAC;QAC9C,IAAI,EAAAC,qBAAA,GAAAC,WAAW,CAAC1B,QAAQ,cAAAyB,qBAAA,uBAApBA,qBAAA,CAAsBF,EAAE,OAAKvB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuB,EAAE,GAAE;UAC7CI,iBAAiB,CAACD,WAAW,CAACE,OAAO,CAAC;QACxC;QACA;QACAX,YAAY,CAACY,UAAU,CAAC,uBAAuB,CAAC;MAClD,CAAC,CAAC,OAAOnB,KAAK,EAAE;QACdoB,OAAO,CAACpB,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzDO,YAAY,CAACY,UAAU,CAAC,uBAAuB,CAAC;MAClD;IACF;;IAEA;IACA,KAAAhB,eAAA,GAAID,QAAQ,CAACmB,KAAK,cAAAlB,eAAA,eAAdA,eAAA,CAAgBe,OAAO,EAAE;MAC3BD,iBAAiB,CAACf,QAAQ,CAACmB,KAAK,CAACH,OAAO,CAAC;IAC3C;EACF,CAAC,EAAE,CAAC5B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuB,EAAE,EAAEX,QAAQ,CAACmB,KAAK,CAAC,CAAC;;EAElC;EACAtD,SAAS,CAAC,MAAM;IAAA,IAAAuD,qBAAA;IACd,CAAAA,qBAAA,GAAAxB,cAAc,CAACyB,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC,EAAE,CAACjC,aAAa,CAAC,CAAC;EAEnB,MAAMkC,aAAa,GAAG,MAAAA,CAAOC,MAAM,EAAEC,eAAe,KAAK;IACvD,IAAI;MACF,MAAM;QAAEC,IAAI;QAAEC;MAAS,CAAC,GAAGH,MAAM;MAEjC,IAAIE,IAAI,KAAK,eAAe,EAAE;QAC5B;QACA,IAAIE,aAAa,GAAG,eAAe;;QAEnC;QACA,IAAID,QAAQ,CAACE,SAAS,IAAIF,QAAQ,CAACE,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;UACvDF,aAAa,GAAGD,QAAQ,CAACE,SAAS,CAAC,CAAC,CAAC;QACvC,CAAC,MAAM;UACL;UACA,MAAME,QAAQ,GAAG,CACf,mCAAmC,EACnC,sCAAsC,EACtC,kCAAkC,EAClC,8BAA8B,EAC9B,mDAAmD,EACnD,sDAAsD,CACvD;UAED,KAAK,MAAMC,OAAO,IAAID,QAAQ,EAAE;YAC9B,MAAME,KAAK,GAAGR,eAAe,CAACQ,KAAK,CAACD,OAAO,CAAC;YAC5C,IAAIC,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,EAAE;cACrBL,aAAa,GAAGK,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;cAC/B;YACF;UACF;QACF;QAEA,MAAMC,QAAQ,GAAG,MAAMzD,KAAK,CAAC0D,IAAI,CAC/B,GAAGzD,MAAM,4BAA4B,EACrC;UACE6C,MAAM,EAAE,eAAe;UACvBa,SAAS,EAAElD,QAAQ,CAACmD,IAAI;UACxBC,IAAI,EAAE;YACJC,IAAI,EAAEZ,aAAa;YACnBa,WAAW,EAAE,4CAA4ChB,eAAe;UAC1E,CAAC;UACDV,OAAO,EAAEU;QACX,CAAC,EACD;UAAEiB,OAAO,EAAE9D,UAAU,CAAC;QAAE,CAC1B,CAAC;QAED,OAAO;UACL+D,OAAO,EAAE,IAAI;UACb5B,OAAO,EAAE,qCAAqCa,aAAa;AACrE;AACA;AACA;AACA;AACA;AACA;AACA,+DAA+D;UACrDW,IAAI,EAAEJ,QAAQ,CAACI;QACjB,CAAC;MACH;;MAEA;MACA,OAAO;QACLI,OAAO,EAAE,KAAK;QACd5B,OAAO,EAAE,4BAA4BS,MAAM,CAACiB,WAAW,CAACG,WAAW,CAAC,CAAC;MACvE,CAAC;IAEH,CAAC,CAAC,OAAO/C,KAAK,EAAE;MAAA,IAAAgD,eAAA,EAAAC,oBAAA;MACd7B,OAAO,CAACpB,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,OAAO;QACL8C,OAAO,EAAE,KAAK;QACd5B,OAAO,EAAE,iDAAiDS,MAAM,CAACiB,WAAW,CAACG,WAAW,CAAC,CAAC,KAAK,EAAAC,eAAA,GAAAhD,KAAK,CAACsC,QAAQ,cAAAU,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBN,IAAI,cAAAO,oBAAA,uBAApBA,oBAAA,CAAsBjD,KAAK,KAAIA,KAAK,CAACkB,OAAO;MAC7I,CAAC;IACH;EACF,CAAC;EAED,MAAMD,iBAAiB,GAAG,eAAAA,CAAA,EAAwC;IAAA,IAAjCiC,WAAW,GAAAC,SAAA,CAAAlB,MAAA,QAAAkB,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAGzD,cAAc;IAC3D,IAAI,CAACwD,WAAW,CAACb,IAAI,CAAC,CAAC,IAAIzC,SAAS,EAAE;IAEtCC,YAAY,CAAC,IAAI,CAAC;IAClB,MAAMwD,WAAW,GAAG;MAClBxC,EAAE,EAAEyC,IAAI,CAACC,GAAG,CAAC,CAAC;MACd1B,IAAI,EAAE,MAAM;MACZ2B,OAAO,EAAEN,WAAW,CAACb,IAAI,CAAC,CAAC;MAC3BoB,SAAS,EAAE,IAAIH,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC;IACpC,CAAC;IAED,MAAMC,gBAAgB,GAAG,CAAC,GAAGnE,aAAa,EAAE6D,WAAW,CAAC;IACxD5D,gBAAgB,CAACkE,gBAAgB,CAAC;IAClChE,iBAAiB,CAAC,EAAE,CAAC;IAErB,IAAI;MACF;MACA,MAAMiE,OAAO,GAAGC,cAAc,CAACX,WAAW,CAAC;MAC3C,IAAIY,UAAU;MAEd,IAAIF,OAAO,CAAC3B,MAAM,GAAG,CAAC,EAAE;QACtB;QACA,MAAMN,MAAM,GAAGiC,OAAO,CAAC,CAAC,CAAC;QACzB,MAAMG,MAAM,GAAG,MAAMrC,aAAa,CAACC,MAAM,EAAEuB,WAAW,CAAC;QAEvDY,UAAU,GAAG;UACXjD,EAAE,EAAEyC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;UAClB1B,IAAI,EAAE,WAAW;UACjB2B,OAAO,EAAEO,MAAM,CAAC7C,OAAO;UACvBuC,SAAS,EAAE,IAAIH,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC;UACnCE,OAAO,EAAEA,OAAO;UAChBI,YAAY,EAAED;QAChB,CAAC;;QAED;QACA,IAAIA,MAAM,CAACjB,OAAO,IAAIvD,YAAY,EAAE;UAClCA,YAAY,CAAC,CAAC;QAChB;MACF,CAAC,MAAM;QACL;QACAuE,UAAU,GAAG;UACXjD,EAAE,EAAEyC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;UAClB1B,IAAI,EAAE,WAAW;UACjB2B,OAAO,EAAES,kBAAkB,CAACf,WAAW,EAAE5D,QAAQ,CAAC;UAClDmE,SAAS,EAAE,IAAIH,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC;UACnCE,OAAO,EAAE;QACX,CAAC;MACH;MAEA,MAAMM,oBAAoB,GAAG,CAAC,GAAGP,gBAAgB,EAAEG,UAAU,CAAC;MAC9DrE,gBAAgB,CAACyE,oBAAoB,CAAC;;MAEtC;MACA,MAAMC,gBAAgB,GAAG9D,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC,IAAI,IAAI,CAAC;MACxF,MAAM4D,sBAAsB,GAAGD,gBAAgB,CAACzD,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,MAAM,MAAKtB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuB,EAAE,EAAC;MAC5F,MAAMJ,iBAAiB,GAAGyD,oBAAoB,CAACG,GAAG,CAAC1D,IAAI,KAAK;QAC1D,GAAGA,IAAI;QACPC,MAAM,EAAEtB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuB,EAAE;QACpByD,QAAQ,EAAEhF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqD;MACtB,CAAC,CAAC,CAAC;MAEHpC,YAAY,CAACgE,OAAO,CAAC,qBAAqB,EAAElE,IAAI,CAACmE,SAAS,CAAC,CACzD,GAAGJ,sBAAsB,EACzB,GAAG3D,iBAAiB,CACrB,CAAC,CAAC;IAEL,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdoB,OAAO,CAACpB,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMyE,aAAa,GAAG;QACpB5D,EAAE,EAAEyC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;QAClB1B,IAAI,EAAE,WAAW;QACjB2B,OAAO,EAAE,8EAA8E;QACvFC,SAAS,EAAE,IAAIH,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC;QACnCgB,OAAO,EAAE;MACX,CAAC;MACDjF,gBAAgB,CAACkF,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEF,aAAa,CAAC,CAAC;IACpD,CAAC,SAAS;MACR5E,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMoE,kBAAkB,GAAGA,CAAC/C,OAAO,EAAE5B,QAAQ,KAAK;IAChD,MAAMsF,YAAY,GAAG1D,OAAO,CAAC6B,WAAW,CAAC,CAAC;IAC1C,MAAM8B,UAAU,GAAG,CAAAvF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuF,UAAU,KAAI,EAAE;IAC7C,MAAMC,UAAU,GAAGD,UAAU,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,SAAS;MAAA,IAAAC,gBAAA;MAAA,OAAKF,GAAG,IAAI,EAAAE,gBAAA,GAAAD,SAAS,CAACE,KAAK,cAAAD,gBAAA,uBAAfA,gBAAA,CAAiBjD,MAAM,KAAI,CAAC,CAAC;IAAA,GAAE,CAAC,CAAC;IACjG,MAAMmD,aAAa,GAAGP,UAAU,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,SAAS;MAAA,IAAAI,iBAAA;MAAA,OACrDL,GAAG,KAAAK,iBAAA,GAAGJ,SAAS,CAACE,KAAK,cAAAE,iBAAA,uBAAfA,iBAAA,CAAiBN,MAAM,CAAC,CAACO,OAAO,EAAEC,IAAI;QAAA,IAAAC,cAAA;QAAA,OAAKF,OAAO,IAAI,EAAAE,cAAA,GAAAD,IAAI,CAACE,QAAQ,cAAAD,cAAA,uBAAbA,cAAA,CAAevD,MAAM,KAAI,CAAC,CAAC;MAAA,GAAE,CAAC,CAAC,KAAI,CAAC;IAAA,GAAE,CAAC,CAAC;;IAEtG;IACA,MAAMyD,cAAc,GAAG;MACrB/C,IAAI,EAAE,CAAArD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqD,IAAI,KAAI,cAAc;MACtCgD,cAAc,EAAEd,UAAU,CAAC5C,MAAM;MACjC2D,SAAS,EAAEd,UAAU;MACrBe,YAAY,EAAET,aAAa;MAC3BU,cAAc,EAAEjB,UAAU,CAACR,GAAG,CAAC0B,CAAC,IAAIA,CAAC,CAACpD,IAAI,CAAC,CAACqD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAE;IAC3D,CAAC;;IAED;IACA,IAAIpB,YAAY,CAACqB,QAAQ,CAAC,WAAW,CAAC,KAAKrB,YAAY,CAACqB,QAAQ,CAAC,KAAK,CAAC,IAAIrB,YAAY,CAACqB,QAAQ,CAAC,QAAQ,CAAC,IAAIrB,YAAY,CAACqB,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MAC3I,OAAO,0CAA0CP,cAAc,CAAC/C,IAAI,0BAA0B+C,cAAc,CAACC,cAAc,gBAAgBD,cAAc,CAACI,cAAc,CAACI,IAAI,CAAC,IAAI,CAAC,GAAGR,cAAc,CAACC,cAAc,GAAG,CAAC,GAAG,KAAK,GAAG,EAAE;AAC1O;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mGAAmG;IAC/F;IAEA,IAAIf,YAAY,CAACqB,QAAQ,CAAC,UAAU,CAAC,IAAIrB,YAAY,CAACqB,QAAQ,CAAC,QAAQ,CAAC,IAAIrB,YAAY,CAACqB,QAAQ,CAAC,UAAU,CAAC,EAAE;MAC7G,MAAME,eAAe,GAAGtB,UAAU,CAACR,GAAG,CAACY,SAAS,IAAI;QAAA,IAAAmB,iBAAA,EAAAC,iBAAA,EAAAC,qBAAA;QAClD,MAAMV,SAAS,GAAG,EAAAQ,iBAAA,GAAAnB,SAAS,CAACE,KAAK,cAAAiB,iBAAA,uBAAfA,iBAAA,CAAiBnE,MAAM,KAAI,CAAC;QAC9C,MAAMsE,cAAc,GAAG,EAAAF,iBAAA,GAAApB,SAAS,CAACE,KAAK,cAAAkB,iBAAA,wBAAAC,qBAAA,GAAfD,iBAAA,CAAiB3F,MAAM,CAAC6E,IAAI,IAAIA,IAAI,CAACiB,MAAM,KAAK,WAAW,CAAC,cAAAF,qBAAA,uBAA5DA,qBAAA,CAA8DrE,MAAM,KAAI,CAAC;QAChG,OAAO,KAAKgD,SAAS,CAACtC,IAAI,KAAK4D,cAAc,IAAIX,SAAS,kBAAkB;MAC9E,CAAC,CAAC,CAACM,IAAI,CAAC,IAAI,CAAC;MAEb,OAAO,qCAAqCR,cAAc,CAAC/C,IAAI;AACrE;AACA;AACA,IAAI+C,cAAc,CAACC,cAAc;AACjC,IAAID,cAAc,CAACE,SAAS;AAC5B,IAAIF,cAAc,CAACG,YAAY;AAC/B;AACA;AACA,EAAEM,eAAe;AACjB;AACA;AACA;AACA;AACA,oDAAoD;IAChD;IAEA,IAAIvB,YAAY,CAACqB,QAAQ,CAAC,UAAU,CAAC,IAAIrB,YAAY,CAACqB,QAAQ,CAAC,MAAM,CAAC,IAAIrB,YAAY,CAACqB,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACzG,MAAMQ,cAAc,GAAG5B,UAAU,CAAC6B,OAAO,CAACzB,SAAS;QAAA,IAAA0B,iBAAA;QAAA,OACjD,EAAAA,iBAAA,GAAA1B,SAAS,CAACE,KAAK,cAAAwB,iBAAA,uBAAfA,iBAAA,CAAiBjG,MAAM,CAAC6E,IAAI,IAAIA,IAAI,CAACiB,MAAM,KAAK,WAAW,CAAC,CAACnC,GAAG,CAACkB,IAAI,IACnE,IAAIA,IAAI,CAAC5C,IAAI,QAAQsC,SAAS,CAACtC,IAAI,EACrC,CAAC,KAAI,EAAE;MAAA,CACT,CAAC,CAACqD,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MAEb,OAAO;AACb;AACA,EAAES,cAAc,CAACpC,GAAG,CAACkB,IAAI,IAAI,KAAKA,IAAI,EAAE,CAAC,CAACW,IAAI,CAAC,IAAI,CAAC;AACpD;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD;IAC5C;IAEA,IAAItB,YAAY,CAACqB,QAAQ,CAAC,KAAK,CAAC,IAAIrB,YAAY,CAACqB,QAAQ,CAAC,QAAQ,CAAC,IAAIrB,YAAY,CAACqB,QAAQ,CAAC,KAAK,CAAC,EAAE;MACnG,IAAIrB,YAAY,CAACqB,QAAQ,CAAC,MAAM,CAAC,EAAE;QACjC,OAAO,qEAAqEP,cAAc,CAACC,cAAc;AACjH;AACA,EAAED,cAAc,CAACI,cAAc,CAACzB,GAAG,CAAC,CAAC1B,IAAI,EAAEiE,CAAC,KAAK,GAAGA,CAAC,GAAG,CAAC,KAAKjE,IAAI,EAAE,CAAC,CAACuD,IAAI,CAAC,IAAI,CAAC;AAChF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;MAC3B;MAEA,OAAO,sCAAsCR,cAAc,CAAC/C,IAAI;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C;IAC1C;IAEA,IAAIiC,YAAY,CAACqB,QAAQ,CAAC,QAAQ,CAAC,IAAIrB,YAAY,CAACqB,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACtE,OAAO;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oFAAoF;IAChF;IAEA,IAAIrB,YAAY,CAACqB,QAAQ,CAAC,QAAQ,CAAC,IAAIrB,YAAY,CAACqB,QAAQ,CAAC,QAAQ,CAAC,IAAIrB,YAAY,CAACqB,QAAQ,CAAC,MAAM,CAAC,IAAIrB,YAAY,CAACqB,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAC1I,OAAO,iDAAiDP,cAAc,CAAC/C,IAAI;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE;IACjE;IAEA,IAAIiC,YAAY,CAACqB,QAAQ,CAAC,MAAM,CAAC,IAAIrB,YAAY,CAACqB,QAAQ,CAAC,iBAAiB,CAAC,IAAIrB,YAAY,CAACqB,QAAQ,CAAC,cAAc,CAAC,EAAE;MACtH,OAAO,sCAAsCP,cAAc,CAAC/C,IAAI;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iGAAiG;IAC7F;;IAEA;IACA,OAAO,6BAA6BzB,OAAO;AAC/C;AACA,yBAAyBwE,cAAc,CAAC/C,IAAI,UAAU+C,cAAc,CAACC,cAAc,mBAAmBD,cAAc,CAACE,SAAS;AAC9H;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+FAA+F;EAC7F,CAAC;EAED,MAAM/B,cAAc,GAAI3C,OAAO,IAAK;IAClC,MAAM0C,OAAO,GAAG,EAAE;IAClB,MAAMgB,YAAY,GAAG1D,OAAO,CAAC6B,WAAW,CAAC,CAAC;;IAE1C;IACA,MAAM8D,cAAc,GAAG,CACrB;MACE1E,OAAO,EAAE,uCAAuC;MAChDN,IAAI,EAAE,eAAe;MACrBiF,UAAU,EAAE,GAAG;MACflE,WAAW,EAAE;IACf,CAAC,EACD;MACET,OAAO,EAAE,6BAA6B;MACtCN,IAAI,EAAE,eAAe;MACrBiF,UAAU,EAAE,GAAG;MACflE,WAAW,EAAE;IACf,CAAC,EACD;MACET,OAAO,EAAE,wBAAwB;MACjCN,IAAI,EAAE,UAAU;MAChBiF,UAAU,EAAE,GAAG;MACflE,WAAW,EAAE;IACf,CAAC,EACD;MACET,OAAO,EAAE,2BAA2B;MACpCN,IAAI,EAAE,aAAa;MACnBiF,UAAU,EAAE,GAAG;MACflE,WAAW,EAAE;IACf,CAAC,EACD;MACET,OAAO,EAAE,uBAAuB;MAChCN,IAAI,EAAE,aAAa;MACnBiF,UAAU,EAAE,GAAG;MACflE,WAAW,EAAE;IACf,CAAC,EACD;MACET,OAAO,EAAE,6BAA6B;MACtCN,IAAI,EAAE,aAAa;MACnBiF,UAAU,EAAE,GAAG;MACflE,WAAW,EAAE;IACf,CAAC,EACD;MACET,OAAO,EAAE,4BAA4B;MACrCN,IAAI,EAAE,eAAe;MACrBiF,UAAU,EAAE,GAAG;MACflE,WAAW,EAAE;IACf,CAAC,EACD;MACET,OAAO,EAAE,uBAAuB;MAChCN,IAAI,EAAE,WAAW;MACjBiF,UAAU,EAAE,GAAG;MACflE,WAAW,EAAE;IACf,CAAC,EACD;MACET,OAAO,EAAE,mBAAmB;MAC5BN,IAAI,EAAE,aAAa;MACnBiF,UAAU,EAAE,GAAG;MACflE,WAAW,EAAE;IACf,CAAC,EACD;MACET,OAAO,EAAE,8BAA8B;MACvCN,IAAI,EAAE,cAAc;MACpBiF,UAAU,EAAE,GAAG;MACflE,WAAW,EAAE;IACf,CAAC,CACF;;IAED;IACA,MAAMd,QAAQ,GAAG;MACfE,SAAS,EAAE,EAAE;MACb8D,cAAc,EAAE,EAAE;MAClBiB,KAAK,EAAE,EAAE;MACTC,UAAU,EAAE;IACd,CAAC;;IAED;IACA,MAAMC,UAAU,GAAG/F,OAAO,CAACkB,KAAK,CAAC,YAAY,CAAC;IAC9C,IAAI6E,UAAU,EAAE;MACdnF,QAAQ,CAACE,SAAS,GAAGiF,UAAU,CAAC5C,GAAG,CAAC6C,CAAC,IAAIA,CAAC,CAACC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IAC/D;;IAEA;IACA,MAAMC,YAAY,GAAGlG,OAAO,CAACkB,KAAK,CAAC,6FAA6F,CAAC;IACjI,IAAIgF,YAAY,EAAE;MAChBtF,QAAQ,CAACiF,KAAK,GAAGK,YAAY;IAC/B;;IAEA;IACA,MAAMC,gBAAgB,GAAGnG,OAAO,CAACkB,KAAK,CAAC,2DAA2D,CAAC;IACnG,IAAIiF,gBAAgB,EAAE;MACpBvF,QAAQ,CAACkF,UAAU,GAAGK,gBAAgB;IACxC;;IAEA;IACAR,cAAc,CAACS,OAAO,CAACC,KAAA,IAAgD;MAAA,IAA/C;QAAEpF,OAAO;QAAEN,IAAI;QAAEiF,UAAU;QAAElE;MAAY,CAAC,GAAA2E,KAAA;MAChE,IAAIpF,OAAO,CAACqF,IAAI,CAAC5C,YAAY,CAAC,EAAE;QAC9BhB,OAAO,CAAC6D,IAAI,CAAC;UACX5F,IAAI;UACJiF,UAAU;UACVlE,WAAW;UACXd,QAAQ,EAAEA,QAAQ;UAClBF,eAAe,EAAEV;QACnB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,OAAO0C,OAAO;EAChB,CAAC;EAED,MAAM8D,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClB7G,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;EAED,MAAM8G,eAAe,GAAItE,SAAS,IAAK;IACrC,OAAO,IAAIH,IAAI,CAACG,SAAS,CAAC,CAACuE,kBAAkB,CAAC,OAAO,EAAE;MACrDC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,oBACEhJ,OAAA,CAACjB,GAAG;IAACkK,EAAE,EAAE;MAAEC,MAAM,EAAE,MAAM;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE;IAAS,CAAE;IAAAC,QAAA,gBAEpErJ,OAAA,CAACf,KAAK;MACJqK,SAAS,EAAE,CAAE;MACbL,EAAE,EAAE;QACFM,CAAC,EAAE,CAAC;QACJC,YAAY,EAAE,eAAe;QAC7BC,MAAM,EAAE,mBAAmB;QAC3BC,YAAY,EAAE,MAAM;QACpBC,eAAe,EAAE;MACnB,CAAE;MAAAN,QAAA,eAEFrJ,OAAA,CAACjB,GAAG;QAACkK,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAES,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAR,QAAA,gBACzDrJ,OAAA,CAACZ,MAAM;UACL6J,EAAE,EAAE;YACFU,eAAe,EAAEjK,eAAe;YAChCoK,KAAK,EAAE,EAAE;YACTZ,MAAM,EAAE;UACV,CAAE;UAAAG,QAAA,eAEFrJ,OAAA,CAACP,OAAO;YAACsK,IAAI,EAAC,WAAW;YAACD,KAAK,EAAE,EAAG;YAACZ,MAAM,EAAE,EAAG;YAACc,KAAK,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eACTpK,OAAA,CAACjB,GAAG;UAAAsK,QAAA,gBACFrJ,OAAA,CAAChB,UAAU;YACTqL,OAAO,EAAC,IAAI;YACZpB,EAAE,EAAE;cACFqB,UAAU,EAAE,kCAAkC;cAC9CC,UAAU,EAAE,GAAG;cACfP,KAAK,EAAE;YACT,CAAE;YAAAX,QAAA,EACH;UAED;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbpK,OAAA,CAAChB,UAAU;YACTqL,OAAO,EAAC,SAAS;YACjBpB,EAAE,EAAE;cACFe,KAAK,EAAE,MAAM;cACbM,UAAU,EAAE;YACd,CAAE;YAAAjB,QAAA,GACH,YACW,EAACjJ,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqD,IAAI;UAAA;YAAAwG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNpK,OAAA,CAACT,IAAI;UACHiL,KAAK,EAAC,MAAM;UACZC,IAAI,EAAC,OAAO;UACZxB,EAAE,EAAE;YACFU,eAAe,EAAE,GAAGjK,eAAe,IAAI;YACvCsK,KAAK,EAAEtK,eAAe;YACtB6K,UAAU,EAAE,GAAG;YACfG,EAAE,EAAE;UACN;QAAE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGRpK,OAAA,CAACf,KAAK;MACJqK,SAAS,EAAE,CAAE;MACbL,EAAE,EAAE;QACF0B,IAAI,EAAE,CAAC;QACPlB,MAAM,EAAE,mBAAmB;QAC3BmB,SAAS,EAAE,MAAM;QACjBlB,YAAY,EAAE,MAAM;QACpBmB,QAAQ,EAAE,MAAM;QAChBtB,CAAC,EAAE,CAAC;QACJI,eAAe,EAAE;MACnB,CAAE;MAAAN,QAAA,EAED/I,aAAa,CAACyC,MAAM,KAAK,CAAC,gBACzB/C,OAAA,CAACjB,GAAG;QACFkK,EAAE,EAAE;UACFE,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBQ,UAAU,EAAE,QAAQ;UACpBkB,cAAc,EAAE,QAAQ;UACxB5B,MAAM,EAAE,MAAM;UACd6B,SAAS,EAAE;QACb,CAAE;QAAA1B,QAAA,gBAEFrJ,OAAA,CAACZ,MAAM;UACL6J,EAAE,EAAE;YACFU,eAAe,EAAE,GAAGjK,eAAe,IAAI;YACvCoK,KAAK,EAAE,EAAE;YACTZ,MAAM,EAAE,EAAE;YACV8B,EAAE,EAAE;UACN,CAAE;UAAA3B,QAAA,eAEFrJ,OAAA,CAACP,OAAO;YAACsK,IAAI,EAAC,WAAW;YAACD,KAAK,EAAE,EAAG;YAACZ,MAAM,EAAE,EAAG;YAACc,KAAK,EAAEtK;UAAgB;YAAAuK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC,eACTpK,OAAA,CAAChB,UAAU;UACTqL,OAAO,EAAC,IAAI;UACZpB,EAAE,EAAE;YACFqB,UAAU,EAAE,kCAAkC;YAC9CC,UAAU,EAAE,GAAG;YACfP,KAAK,EAAE,MAAM;YACbgB,EAAE,EAAE;UACN,CAAE;UAAA3B,QAAA,EACH;QAED;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbpK,OAAA,CAAChB,UAAU;UACTqL,OAAO,EAAC,OAAO;UACfpB,EAAE,EAAE;YACFe,KAAK,EAAE,MAAM;YACbM,UAAU,EAAE,kCAAkC;YAC9CW,QAAQ,EAAE;UACZ,CAAE;UAAA5B,QAAA,EACH;QAED;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,gBAENpK,OAAA,CAACjB,GAAG;QAAAsK,QAAA,GACD/I,aAAa,CAAC6E,GAAG,CAAEnD,OAAO,iBACzBhC,OAAA,CAACjB,GAAG;UAEFkK,EAAE,EAAE;YACFE,OAAO,EAAE,MAAM;YACf2B,cAAc,EAAE9I,OAAO,CAACW,IAAI,KAAK,MAAM,GAAG,UAAU,GAAG,YAAY;YACnEqI,EAAE,EAAE;UACN,CAAE;UAAA3B,QAAA,eAEFrJ,OAAA,CAACjB,GAAG;YACFkK,EAAE,EAAE;cACFgC,QAAQ,EAAE,KAAK;cACf9B,OAAO,EAAE,MAAM;cACfC,aAAa,EAAEpH,OAAO,CAACW,IAAI,KAAK,MAAM,GAAG,aAAa,GAAG,KAAK;cAC9DiH,UAAU,EAAE,YAAY;cACxBC,GAAG,EAAE;YACP,CAAE;YAAAR,QAAA,gBAEFrJ,OAAA,CAACZ,MAAM;cACL6J,EAAE,EAAE;gBACFa,KAAK,EAAE,EAAE;gBACTZ,MAAM,EAAE,EAAE;gBACVS,eAAe,EAAE3H,OAAO,CAACW,IAAI,KAAK,MAAM,GAAG,SAAS,GAAGjD;cACzD,CAAE;cAAA2J,QAAA,eAEFrJ,OAAA,CAACP,OAAO;gBACNsK,IAAI,EAAE/H,OAAO,CAACW,IAAI,KAAK,MAAM,GAAG,yBAAyB,GAAG,WAAY;gBACxEmH,KAAK,EAAE,EAAG;gBACVZ,MAAM,EAAE,EAAG;gBACXc,KAAK,EAAEhI,OAAO,CAACW,IAAI,KAAK,MAAM,GAAG,MAAM,GAAG;cAAO;gBAAAsH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACTpK,OAAA,CAACjB,GAAG;cAAAsK,QAAA,gBACFrJ,OAAA,CAACf,KAAK;gBACJqK,SAAS,EAAE,CAAE;gBACbL,EAAE,EAAE;kBACFM,CAAC,EAAE,GAAG;kBACNC,YAAY,EAAE,MAAM;kBACpBG,eAAe,EAAE3H,OAAO,CAACW,IAAI,KAAK,MAAM,GAAGjD,eAAe,GAAG,SAAS;kBACtEsK,KAAK,EAAEhI,OAAO,CAACW,IAAI,KAAK,MAAM,GAAG,MAAM,GAAG,MAAM;kBAChD8G,MAAM,EAAEzH,OAAO,CAACwD,OAAO,GAAG,mBAAmB,GAAG;gBAClD,CAAE;gBAAA6D,QAAA,gBAEFrJ,OAAA,CAAChB,UAAU;kBACTqL,OAAO,EAAC,OAAO;kBACfpB,EAAE,EAAE;oBACFqB,UAAU,EAAE,kCAAkC;oBAC9CY,UAAU,EAAE,GAAG;oBACfC,UAAU,EAAE;kBACd,CAAE;kBAAA9B,QAAA,EAEDrH,OAAO,CAACsC;gBAAO;kBAAA2F,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,EAGZpI,OAAO,CAACW,IAAI,KAAK,WAAW,IAAIX,OAAO,CAAC0C,OAAO,IAAI1C,OAAO,CAAC0C,OAAO,CAAC3B,MAAM,GAAG,CAAC,iBAC5E/C,OAAA,CAACjB,GAAG;kBAACkK,EAAE,EAAE;oBAAEmC,EAAE,EAAE,GAAG;oBAAEjC,OAAO,EAAE,MAAM;oBAAEkC,QAAQ,EAAE,MAAM;oBAAExB,GAAG,EAAE;kBAAE,CAAE;kBAAAR,QAAA,EAC7DrH,OAAO,CAAC0C,OAAO,CAACoC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC3B,GAAG,CAAC,CAAC1C,MAAM,EAAE6I,WAAW,kBACnDtL,OAAA,CAACT,IAAI;oBAEHiL,KAAK,EAAE/H,MAAM,CAACiB,WAAY;oBAC1B+G,IAAI,EAAC,OAAO;oBACZc,OAAO,EAAEA,CAAA,KAAM;sBACb;sBACA9K,iBAAiB,CAACgC,MAAM,CAACC,eAAe,IAAI,UAAUD,MAAM,CAACiB,WAAW,CAACG,WAAW,CAAC,CAAC,EAAE,CAAC;sBACzF,IAAIhD,QAAQ,CAACwB,OAAO,EAAE;wBACpBxB,QAAQ,CAACwB,OAAO,CAACmJ,KAAK,CAAC,CAAC;sBAC1B;oBACF,CAAE;oBACFvC,EAAE,EAAE;sBACFU,eAAe,EAAE,MAAM;sBACvBF,MAAM,EAAE,aAAa/J,eAAe,EAAE;sBACtCsK,KAAK,EAAEtK,eAAe;sBACtB+L,QAAQ,EAAE,QAAQ;sBAClBvC,MAAM,EAAE,MAAM;sBACdwC,MAAM,EAAE,SAAS;sBACjB,SAAS,EAAE;wBACT/B,eAAe,EAAE,GAAGjK,eAAe;sBACrC;oBACF;kBAAE,GApBG4L,WAAW;oBAAArB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAqBjB,CACF;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CACN;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI,CAAC,eACRpK,OAAA,CAAChB,UAAU;gBACTqL,OAAO,EAAC,SAAS;gBACjBpB,EAAE,EAAE;kBACFe,KAAK,EAAE,MAAM;kBACbM,UAAU,EAAE,kCAAkC;kBAC9CmB,QAAQ,EAAE,QAAQ;kBAClBL,EAAE,EAAE,GAAG;kBACPjC,OAAO,EAAE,OAAO;kBAChB4B,SAAS,EAAE/I,OAAO,CAACW,IAAI,KAAK,MAAM,GAAG,OAAO,GAAG;gBACjD,CAAE;gBAAA0G,QAAA,EAEDR,eAAe,CAAC7G,OAAO,CAACuC,SAAS;cAAC;gBAAA0F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAjGDpI,OAAO,CAACL,EAAE;UAAAsI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkGZ,CACN,CAAC,EACD1J,SAAS,iBACRV,OAAA,CAACjB,GAAG;UAACkK,EAAE,EAAE;YAAEE,OAAO,EAAE,MAAM;YAAE2B,cAAc,EAAE,YAAY;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAA3B,QAAA,eAChErJ,OAAA,CAACjB,GAAG;YAACkK,EAAE,EAAE;cAAEE,OAAO,EAAE,MAAM;cAAES,UAAU,EAAE,YAAY;cAAEC,GAAG,EAAE;YAAE,CAAE;YAAAR,QAAA,gBAC7DrJ,OAAA,CAACZ,MAAM;cACL6J,EAAE,EAAE;gBACFa,KAAK,EAAE,EAAE;gBACTZ,MAAM,EAAE,EAAE;gBACVS,eAAe,EAAEjK;cACnB,CAAE;cAAA2J,QAAA,eAEFrJ,OAAA,CAACP,OAAO;gBAACsK,IAAI,EAAC,WAAW;gBAACD,KAAK,EAAE,EAAG;gBAACZ,MAAM,EAAE,EAAG;gBAACc,KAAK,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACTpK,OAAA,CAACf,KAAK;cACJqK,SAAS,EAAE,CAAE;cACbL,EAAE,EAAE;gBACFM,CAAC,EAAE,GAAG;gBACNC,YAAY,EAAE,MAAM;gBACpBG,eAAe,EAAE,SAAS;gBAC1BR,OAAO,EAAE,MAAM;gBACfS,UAAU,EAAE,QAAQ;gBACpBC,GAAG,EAAE;cACP,CAAE;cAAAR,QAAA,gBAEFrJ,OAAA,CAACV,gBAAgB;gBAACmL,IAAI,EAAE,EAAG;gBAACxB,EAAE,EAAE;kBAAEe,KAAK,EAAEtK;gBAAgB;cAAE;gBAAAuK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9DpK,OAAA,CAAChB,UAAU;gBACTqL,OAAO,EAAC,OAAO;gBACfpB,EAAE,EAAE;kBACFqB,UAAU,EAAE,kCAAkC;kBAC9CN,KAAK,EAAE;gBACT,CAAE;gBAAAX,QAAA,EACH;cAED;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eACDpK,OAAA;UAAK2L,GAAG,EAAE/K;QAAe;UAAAqJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGRpK,OAAA,CAACf,KAAK;MACJqK,SAAS,EAAE,CAAE;MACbL,EAAE,EAAE;QACFM,CAAC,EAAE,CAAC;QACJC,YAAY,EAAE,eAAe;QAC7BC,MAAM,EAAE,mBAAmB;QAC3BmB,SAAS,EAAE;MACb,CAAE;MAAAvB,QAAA,eAEFrJ,OAAA,CAACjB,GAAG;QAACkK,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEU,GAAG,EAAE,CAAC;UAAED,UAAU,EAAE;QAAW,CAAE;QAAAP,QAAA,gBAC3DrJ,OAAA,CAACd,SAAS;UACR2B,QAAQ,EAAEA,QAAS;UACnB+K,KAAK,EAAEpL,cAAe;UACtBqL,QAAQ,EAAGpD,CAAC,IAAKhI,iBAAiB,CAACgI,CAAC,CAACqD,MAAM,CAACF,KAAK,CAAE;UACnDG,UAAU,EAAEvD,cAAe;UAC3BwD,WAAW,EAAC,uCAAuC;UACnDC,SAAS;UACTC,OAAO,EAAE,CAAE;UACXC,SAAS;UACT9B,OAAO,EAAC,UAAU;UAClB+B,QAAQ,EAAE1L,SAAU;UACpBuI,EAAE,EAAE;YACF,0BAA0B,EAAE;cAC1BO,YAAY,EAAE,KAAK;cACnBc,UAAU,EAAE;YACd;UACF;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFpK,OAAA,CAACR,OAAO;UAAC6M,KAAK,EAAC,cAAc;UAAAhD,QAAA,eAC3BrJ,OAAA,CAACb,UAAU;YACToM,OAAO,EAAEA,CAAA,KAAMxJ,iBAAiB,CAAC,CAAE;YACnCqK,QAAQ,EAAE,CAAC5L,cAAc,CAAC2C,IAAI,CAAC,CAAC,IAAIzC,SAAU;YAC9CuI,EAAE,EAAE;cACFU,eAAe,EAAEnJ,cAAc,CAAC2C,IAAI,CAAC,CAAC,IAAI,CAACzC,SAAS,GAAGhB,eAAe,GAAG,SAAS;cAClFsK,KAAK,EAAExJ,cAAc,CAAC2C,IAAI,CAAC,CAAC,IAAI,CAACzC,SAAS,GAAG,MAAM,GAAG,MAAM;cAC5D,SAAS,EAAE;gBACTiJ,eAAe,EAAEnJ,cAAc,CAAC2C,IAAI,CAAC,CAAC,IAAI,CAACzC,SAAS,GAAG,SAAS,GAAG;cACrE;YACF,CAAE;YAAA2I,QAAA,eAEFrJ,OAAA,CAACP,OAAO;cAACsK,IAAI,EAAC,uBAAuB;cAACD,KAAK,EAAE,EAAG;cAACZ,MAAM,EAAE;YAAG;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACjK,EAAA,CA7yBIF,QAAQ;EAAA,QAOKH,WAAW;AAAA;AAAAwM,EAAA,GAPxBrM,QAAQ;AA+yBd,eAAeA,QAAQ;AAAC,IAAAqM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}