{"ast": null, "code": "'use strict';\n\nvar callBind = require('call-bind-apply-helpers');\nvar gOPD = require('gopd');\nvar hasProtoAccessor;\ntry {\n  // eslint-disable-next-line no-extra-parens, no-proto\n  hasProtoAccessor = /** @type {{ __proto__?: typeof Array.prototype }} */[].__proto__ === Array.prototype;\n} catch (e) {\n  if (!e || typeof e !== 'object' || !('code' in e) || e.code !== 'ERR_PROTO_ACCESS') {\n    throw e;\n  }\n}\n\n// eslint-disable-next-line no-extra-parens\nvar desc = !!hasProtoAccessor && gOPD && gOPD(Object.prototype, /** @type {keyof typeof Object.prototype} */'__proto__');\nvar $Object = Object;\nvar $getPrototypeOf = $Object.getPrototypeOf;\n\n/** @type {import('./get')} */\nmodule.exports = desc && typeof desc.get === 'function' ? callBind([desc.get]) : typeof $getPrototypeOf === 'function' ? /** @type {import('./get')} */function getDunder(value) {\n  // eslint-disable-next-line eqeqeq\n  return $getPrototypeOf(value == null ? value : $Object(value));\n} : false;", "map": {"version": 3, "names": ["callBind", "require", "gOPD", "hasProtoAccessor", "__proto__", "Array", "prototype", "e", "code", "desc", "Object", "$Object", "$getPrototypeOf", "getPrototypeOf", "module", "exports", "get", "getDunder", "value"], "sources": ["C:/ignition/ignition-ui/node_modules/dunder-proto/get.js"], "sourcesContent": ["'use strict';\n\nvar callBind = require('call-bind-apply-helpers');\nvar gOPD = require('gopd');\n\nvar hasProtoAccessor;\ntry {\n\t// eslint-disable-next-line no-extra-parens, no-proto\n\thasProtoAccessor = /** @type {{ __proto__?: typeof Array.prototype }} */ ([]).__proto__ === Array.prototype;\n} catch (e) {\n\tif (!e || typeof e !== 'object' || !('code' in e) || e.code !== 'ERR_PROTO_ACCESS') {\n\t\tthrow e;\n\t}\n}\n\n// eslint-disable-next-line no-extra-parens\nvar desc = !!hasProtoAccessor && gOPD && gOPD(Object.prototype, /** @type {keyof typeof Object.prototype} */ ('__proto__'));\n\nvar $Object = Object;\nvar $getPrototypeOf = $Object.getPrototypeOf;\n\n/** @type {import('./get')} */\nmodule.exports = desc && typeof desc.get === 'function'\n\t? callBind([desc.get])\n\t: typeof $getPrototypeOf === 'function'\n\t\t? /** @type {import('./get')} */ function getDunder(value) {\n\t\t\t// eslint-disable-next-line eqeqeq\n\t\t\treturn $getPrototypeOf(value == null ? value : $Object(value));\n\t\t}\n\t\t: false;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,QAAQ,GAAGC,OAAO,CAAC,yBAAyB,CAAC;AACjD,IAAIC,IAAI,GAAGD,OAAO,CAAC,MAAM,CAAC;AAE1B,IAAIE,gBAAgB;AACpB,IAAI;EACH;EACAA,gBAAgB,GAAG,qDAAuD,EAAE,CAAEC,SAAS,KAAKC,KAAK,CAACC,SAAS;AAC5G,CAAC,CAAC,OAAOC,CAAC,EAAE;EACX,IAAI,CAACA,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,IAAI,EAAE,MAAM,IAAIA,CAAC,CAAC,IAAIA,CAAC,CAACC,IAAI,KAAK,kBAAkB,EAAE;IACnF,MAAMD,CAAC;EACR;AACD;;AAEA;AACA,IAAIE,IAAI,GAAG,CAAC,CAACN,gBAAgB,IAAID,IAAI,IAAIA,IAAI,CAACQ,MAAM,CAACJ,SAAS,EAAE,4CAA8C,WAAY,CAAC;AAE3H,IAAIK,OAAO,GAAGD,MAAM;AACpB,IAAIE,eAAe,GAAGD,OAAO,CAACE,cAAc;;AAE5C;AACAC,MAAM,CAACC,OAAO,GAAGN,IAAI,IAAI,OAAOA,IAAI,CAACO,GAAG,KAAK,UAAU,GACpDhB,QAAQ,CAAC,CAACS,IAAI,CAACO,GAAG,CAAC,CAAC,GACpB,OAAOJ,eAAe,KAAK,UAAU,GACpC,8BAA+B,SAASK,SAASA,CAACC,KAAK,EAAE;EAC1D;EACA,OAAON,eAAe,CAACM,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAGP,OAAO,CAACO,KAAK,CAAC,CAAC;AAC/D,CAAC,GACC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}