{"ast": null, "code": "var _jsxFileName = \"C:\\\\ignition\\\\ignition-ui\\\\src\\\\views\\\\home\\\\components\\\\PlanCard.js\";\nimport React from 'react';\nimport { Box, Typography, Card, Avatar, LinearProgress } from '@mui/material';\nimport { Link } from \"react-router-dom\";\nimport dayjs from 'dayjs';\nimport Iconify from 'components/Iconify/index';\nimport { mainYellowColor } from \"helpers/constants\";\nimport styles from \"../styles.module.scss\";\n\n// Status constants\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst STATUS = {\n  NOT_STARTED: 1,\n  IN_PROGRESS: 2,\n  COMPLETED: 3\n};\nconst PlanCard = _ref => {\n  var _plan$milestones, _plan$milestones2, _plan$milestones3, _plan$milestones4, _plan$milestones5;\n  let {\n    plan,\n    currentUser,\n    index\n  } = _ref;\n  // Calculate subtask progress\n  const calculateSubtaskProgress = subtask => {\n    // Kiểm tra subtask tồn tại\n    if (!subtask) {\n      return 0;\n    }\n\n    // If progress is explicitly set, use it\n    if (subtask.progress !== undefined && subtask.progress !== null) {\n      return subtask.progress;\n    }\n\n    // Otherwise, determine based on status\n    switch (subtask.status) {\n      case STATUS.COMPLETED:\n        return 100;\n      case STATUS.IN_PROGRESS:\n        return 50;\n      default:\n        return 0;\n    }\n  };\n\n  // Calculate task progress based on subtasks\n  const calculateTaskProgress = task => {\n    // Kiểm tra task tồn tại\n    if (!task) {\n      return 0;\n    }\n\n    // Đảm bảo subtasks là một mảng\n    const subtasks = task.subtasks || [];\n\n    // If no subtasks, base on task status\n    if (subtasks.length === 0) {\n      switch (task.status) {\n        case STATUS.COMPLETED:\n          return 100;\n        case STATUS.IN_PROGRESS:\n          return 50;\n        default:\n          return 0;\n      }\n    }\n\n    // Calculate progress based on subtasks\n    let totalProgress = 0;\n    subtasks.forEach(subtask => {\n      totalProgress += calculateSubtaskProgress(subtask);\n    });\n    return Math.floor(totalProgress / subtasks.length);\n  };\n\n  // Calculate milestone progress based on tasks\n  const calculateMilestoneProgress = milestone => {\n    // Kiểm tra milestone tồn tại\n    if (!milestone) {\n      return 0;\n    }\n\n    // Đảm bảo tasks là một mảng\n    const tasks = milestone.tasks || [];\n\n    // If no tasks, return 0\n    if (tasks.length === 0) {\n      return 0;\n    }\n\n    // Calculate progress based on tasks\n    let totalProgress = 0;\n    tasks.forEach(task => {\n      // Đảm bảo task tồn tại và có thuộc tính cần thiết\n      if (task) {\n        totalProgress += calculateTaskProgress(task);\n      }\n    });\n    return Math.floor(totalProgress / tasks.length);\n  };\n\n  // Calculate overall plan progress\n  const calculatePlanProgress = () => {\n    if (!plan || !plan.milestones || plan.milestones.length === 0) {\n      return 0;\n    }\n    let totalProgress = 0;\n    plan.milestones.forEach(milestone => {\n      totalProgress += calculateMilestoneProgress(milestone);\n    });\n    return Math.floor(totalProgress / plan.milestones.length);\n  };\n\n  // Calculate overview information from plan data\n  const totalMilestones = ((_plan$milestones = plan.milestones) === null || _plan$milestones === void 0 ? void 0 : _plan$milestones.length) || 0;\n\n  // Calculate total tasks from all milestones\n  const totalTasks = ((_plan$milestones2 = plan.milestones) === null || _plan$milestones2 === void 0 ? void 0 : _plan$milestones2.reduce((sum, milestone) => {\n    var _milestone$tasks;\n    return sum + (((_milestone$tasks = milestone.tasks) === null || _milestone$tasks === void 0 ? void 0 : _milestone$tasks.length) || 0);\n  }, 0)) || 0;\n\n  // Calculate total subtasks from all tasks\n  const totalSubtasks = ((_plan$milestones3 = plan.milestones) === null || _plan$milestones3 === void 0 ? void 0 : _plan$milestones3.reduce((sum, milestone) => {\n    var _milestone$tasks2;\n    return sum + ((_milestone$tasks2 = milestone.tasks) === null || _milestone$tasks2 === void 0 ? void 0 : _milestone$tasks2.reduce((taskSum, task) => {\n      var _task$subtasks;\n      return taskSum + (((_task$subtasks = task.subtasks) === null || _task$subtasks === void 0 ? void 0 : _task$subtasks.length) || 0);\n    }, 0));\n  }, 0)) || 0;\n\n  // Calculate progress and task status counts\n  const progress = calculatePlanProgress();\n\n  // Calculate completed, in progress, and not started tasks\n  let completedTasks = 0;\n  let inProgressTasks = 0;\n  let notStartedTasks = 0;\n  (_plan$milestones4 = plan.milestones) === null || _plan$milestones4 === void 0 ? void 0 : _plan$milestones4.forEach(milestone => {\n    var _milestone$tasks3;\n    (_milestone$tasks3 = milestone.tasks) === null || _milestone$tasks3 === void 0 ? void 0 : _milestone$tasks3.forEach(task => {\n      const taskProgress = calculateTaskProgress(task);\n      if (taskProgress === 100) {\n        completedTasks++;\n      } else if (taskProgress > 0) {\n        inProgressTasks++;\n      } else {\n        notStartedTasks++;\n      }\n    });\n  });\n\n  // Determine plan status\n  let status = {\n    color: '#4CAF50',\n    label: 'Active'\n  };\n  if (progress === 100) {\n    status = {\n      color: '#2196F3',\n      label: 'Completed'\n    };\n  } else if (progress === 0) {\n    status = {\n      color: '#FFC107',\n      label: 'Not Started'\n    };\n  } else if (progress > 0) {\n    status = {\n      color: '#FF9800',\n      label: 'In Progress'\n    };\n  }\n\n  // Create tags from plan information\n  const tags = [];\n  if (plan.start_date && plan.end_date) {\n    const duration = dayjs(plan.end_date).diff(dayjs(plan.start_date), 'day');\n    tags.push(`${duration} days`);\n  }\n  if (totalMilestones > 0) {\n    tags.push(`${totalMilestones} milestones`);\n  }\n\n  // Check if there are assignees\n  const hasAssignees = (_plan$milestones5 = plan.milestones) === null || _plan$milestones5 === void 0 ? void 0 : _plan$milestones5.some(milestone => {\n    var _milestone$tasks4;\n    return (_milestone$tasks4 = milestone.tasks) === null || _milestone$tasks4 === void 0 ? void 0 : _milestone$tasks4.some(task => {\n      var _task$assignees;\n      return ((_task$assignees = task.assignees) === null || _task$assignees === void 0 ? void 0 : _task$assignees.length) > 0;\n    });\n  });\n  if (hasAssignees) {\n    tags.push('Has assignees');\n  }\n  return /*#__PURE__*/_jsxDEV(Card, {\n    className: styles.planCard,\n    sx: {\n      position: 'relative'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      className: styles.statusIndicator,\n      sx: {\n        backgroundColor: status.color\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        sx: {\n          fontFamily: '\"Recursive Variable\", sans-serif'\n        },\n        children: status.label\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Link, {\n      to: `/d/plan/${plan.slug}`,\n      className: styles.planCardLink,\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        className: styles.cardHeader,\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          className: styles.cardIcon,\n          children: /*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"codicon:project\",\n            width: 24,\n            height: 24,\n            color: mainYellowColor\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 195,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          className: styles.planName,\n          noWrap: true,\n          sx: {\n            fontFamily: '\"Recursive Variable\", sans-serif',\n            color: '#333',\n            fontSize: '1.2rem',\n            fontWeight: 700,\n            margin: 0,\n            flex: 1,\n            overflow: 'hidden',\n            textOverflow: 'ellipsis'\n          },\n          children: plan.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        className: styles.cardMeta,\n        children: (plan.start_date || plan.end_date) && /*#__PURE__*/_jsxDEV(Box, {\n          className: styles.metaItem,\n          children: [/*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"carbon:calendar\",\n            width: 16,\n            height: 16,\n            color: mainYellowColor\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 222,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: styles.metaLabel,\n              children: \"Timeline: \"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 17\n            }, this), plan.start_date ? dayjs(plan.start_date).format('MM/DD/YYYY') : 'Not started', plan.start_date && plan.end_date && \" ~ \", plan.end_date ? dayjs(plan.end_date).format('MM/DD/YYYY') : 'No end date']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 221,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 219,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        className: styles.planStructure,\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          className: styles.structureItem,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            className: styles.structureIcon,\n            sx: {\n              backgroundColor: '#E3F2FD'\n            },\n            children: /*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"mdi:flag-variant\",\n              width: 16,\n              height: 16,\n              color: \"#2196F3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: styles.structureInfo,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              className: styles.structureLabel,\n              sx: {\n                fontSize: '0.7rem',\n                color: '#666',\n                fontFamily: '\"Recursive Variable\", sans-serif'\n              },\n              children: \"Milestones\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              className: styles.structureValue,\n              sx: {\n                fontWeight: 700,\n                fontSize: '1.2rem',\n                color: '#333',\n                fontFamily: '\"Recursive Variable\", sans-serif',\n                margin: 0,\n                lineHeight: 1.2\n              },\n              children: totalMilestones\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          className: styles.structureItem,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            className: styles.structureIcon,\n            sx: {\n              backgroundColor: '#FFF8E1'\n            },\n            children: /*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"mdi:checkbox-marked-outline\",\n              width: 16,\n              height: 16,\n              color: \"#FFA000\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 270,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 269,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: styles.structureInfo,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              className: styles.structureLabel,\n              sx: {\n                fontSize: '0.7rem',\n                color: '#666',\n                fontFamily: '\"Recursive Variable\", sans-serif'\n              },\n              children: \"Tasks\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              className: styles.structureValue,\n              sx: {\n                fontWeight: 700,\n                fontSize: '1.2rem',\n                color: '#333',\n                fontFamily: '\"Recursive Variable\", sans-serif',\n                margin: 0,\n                lineHeight: 1.2\n              },\n              children: totalTasks\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 284,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          className: styles.structureItem,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            className: styles.structureIcon,\n            sx: {\n              backgroundColor: '#E8F5E9'\n            },\n            children: /*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"mdi:format-list-checks\",\n              width: 16,\n              height: 16,\n              color: \"#4CAF50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 303,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 302,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: styles.structureInfo,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              className: styles.structureLabel,\n              sx: {\n                fontSize: '0.7rem',\n                color: '#666',\n                fontFamily: '\"Recursive Variable\", sans-serif'\n              },\n              children: \"Subtasks\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 306,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h6\",\n              className: styles.structureValue,\n              sx: {\n                fontWeight: 700,\n                fontSize: '1.2rem',\n                color: '#333',\n                fontFamily: '\"Recursive Variable\", sans-serif',\n                margin: 0,\n                lineHeight: 1.2\n              },\n              children: totalSubtasks\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 305,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 301,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        className: styles.progressSection,\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          className: styles.progressHeader,\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            color: \"textSecondary\",\n            sx: {\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              fontSize: '0.7rem',\n              color: '#666'\n            },\n            children: \"Progress\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            className: styles.progressValue,\n            sx: {\n              fontWeight: 700,\n              fontSize: '1.2rem',\n              color: '#333',\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              margin: 0,\n              lineHeight: 1.2\n            },\n            children: [progress, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 337,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n          variant: \"determinate\",\n          value: progress,\n          sx: {\n            height: 8,\n            borderRadius: 4,\n            backgroundColor: 'rgba(0, 0, 0, 0.05)',\n            '& .MuiLinearProgress-bar': {\n              borderRadius: 4,\n              backgroundColor: progress === 100 ? '#4CAF50' : mainYellowColor\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 364,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          className: styles.taskStatusLegend,\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            className: styles.legendItem,\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              className: styles.legendColor,\n              sx: {\n                backgroundColor: '#4CAF50'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 381,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              className: styles.legendText,\n              sx: {\n                fontFamily: '\"Recursive Variable\", sans-serif',\n                fontSize: '0.75rem',\n                color: '#666'\n              },\n              children: [completedTasks, \" completed\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 380,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: styles.legendItem,\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              className: styles.legendColor,\n              sx: {\n                backgroundColor: '#FF9800'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 395,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              className: styles.legendText,\n              sx: {\n                fontFamily: '\"Recursive Variable\", sans-serif',\n                fontSize: '0.75rem',\n                color: '#666'\n              },\n              children: [inProgressTasks, \" in progress\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 396,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 394,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            className: styles.legendItem,\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              className: styles.legendColor,\n              sx: {\n                backgroundColor: '#E0E0E0'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 409,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              className: styles.legendText,\n              sx: {\n                fontFamily: '\"Recursive Variable\", sans-serif',\n                fontSize: '0.75rem',\n                color: '#666'\n              },\n              children: [notStartedTasks, \" not started\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 408,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 336,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        className: styles.planCreator,\n        sx: {\n          borderTop: 'none',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '8px',\n          marginTop: '8px',\n          paddingTop: '8px',\n          justifyContent: 'space-between',\n          fontFamily: '\"Recursive Variable\", sans-serif',\n          marginBottom: '8px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            src: plan.user.avatar || '',\n            alt: `${plan.user.first_name} ${plan.user.last_name}`,\n            className: styles.creatorAvatar,\n            sx: {\n              width: '28px',\n              height: '28px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 441,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              className: styles.creatorLabel,\n              sx: {\n                color: '#888',\n                fontSize: '0.7rem',\n                display: 'block',\n                fontFamily: '\"Recursive Variable\", sans-serif'\n              },\n              children: \"Created by\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 448,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              className: styles.creatorName,\n              sx: {\n                fontWeight: 600,\n                fontSize: '0.8rem',\n                color: '#333',\n                fontFamily: '\"Recursive Variable\", sans-serif'\n              },\n              children: [plan.user.first_name, \" \", plan.user.last_name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [plan.user.id !== currentUser.id && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: '4px',\n              backgroundColor: 'rgba(255, 99, 71, 0.9)',\n              color: 'white',\n              padding: '4px 10px',\n              borderRadius: '16px',\n              fontSize: '0.75rem',\n              fontWeight: 500,\n              boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',\n              marginRight: '8px'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"ph:share-network\",\n              width: 14,\n              height: 14\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 492,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                fontFamily: '\"Recursive Variable\", sans-serif',\n                color: 'white'\n              },\n              children: \"Shared\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 493,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 477,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            className: styles.lastUpdated,\n            sx: {\n              color: '#888',\n              fontSize: '0.7rem',\n              display: 'flex',\n              alignItems: 'center',\n              gap: '4px',\n              fontFamily: '\"Recursive Variable\", sans-serif'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"mdi:clock-outline\",\n              width: 14,\n              height: 14\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 516,\n              columnNumber: 15\n            }, this), dayjs(plan.updated_at).fromNow()]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 504,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 475,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 7\n    }, this)]\n  }, index, true, {\n    fileName: _jsxFileName,\n    lineNumber: 175,\n    columnNumber: 5\n  }, this);\n};\n_c = PlanCard;\nexport default PlanCard;\nvar _c;\n$RefreshReg$(_c, \"PlanCard\");", "map": {"version": 3, "names": ["React", "Box", "Typography", "Card", "Avatar", "LinearProgress", "Link", "dayjs", "Iconify", "mainYellowColor", "styles", "jsxDEV", "_jsxDEV", "STATUS", "NOT_STARTED", "IN_PROGRESS", "COMPLETED", "PlanCard", "_ref", "_plan$milestones", "_plan$milestones2", "_plan$milestones3", "_plan$milestones4", "_plan$milestones5", "plan", "currentUser", "index", "calculateSubtaskProgress", "subtask", "progress", "undefined", "status", "calculateTaskProgress", "task", "subtasks", "length", "totalProgress", "for<PERSON>ach", "Math", "floor", "calculateMilestoneProgress", "milestone", "tasks", "calculatePlanProgress", "milestones", "totalMilestones", "totalTasks", "reduce", "sum", "_milestone$tasks", "totalSubtasks", "_milestone$tasks2", "taskSum", "_task$subtasks", "completedTasks", "inProgressTasks", "notStartedTasks", "_milestone$tasks3", "taskProgress", "color", "label", "tags", "start_date", "end_date", "duration", "diff", "push", "hasAssignees", "some", "_milestone$tasks4", "_task$assignees", "assignees", "className", "planCard", "sx", "position", "children", "statusIndicator", "backgroundColor", "variant", "fontFamily", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "to", "slug", "planCardLink", "<PERSON><PERSON><PERSON><PERSON>", "cardIcon", "icon", "width", "height", "planName", "noWrap", "fontSize", "fontWeight", "margin", "flex", "overflow", "textOverflow", "name", "cardMeta", "metaItem", "metaLabel", "format", "planStructure", "structureItem", "structureIcon", "structureInfo", "structureLabel", "structureValue", "lineHeight", "progressSection", "progressHeader", "progressValue", "value", "borderRadius", "taskStatusLegend", "legendItem", "legendColor", "legendText", "planCreator", "borderTop", "display", "alignItems", "gap", "marginTop", "paddingTop", "justifyContent", "marginBottom", "src", "user", "avatar", "alt", "first_name", "last_name", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "id", "padding", "boxShadow", "marginRight", "lastUpdated", "updated_at", "fromNow", "_c", "$RefreshReg$"], "sources": ["C:/ignition/ignition-ui/src/views/home/<USER>/PlanCard.js"], "sourcesContent": ["import React from 'react';\r\nimport { Box, Typography, Card, Avatar, LinearProgress } from '@mui/material';\r\nimport { Link } from \"react-router-dom\";\r\nimport dayjs from 'dayjs';\r\nimport Iconify from 'components/Iconify/index';\r\nimport { mainYellowColor } from \"helpers/constants\";\r\nimport styles from \"../styles.module.scss\";\r\n\r\n// Status constants\r\nconst STATUS = {\r\n  NOT_STARTED: 1,\r\n  IN_PROGRESS: 2,\r\n  COMPLETED: 3\r\n};\r\n\r\nconst PlanCard = ({ plan, currentUser, index }) => {\r\n  // Calculate subtask progress\r\n  const calculateSubtaskProgress = (subtask) => {\r\n    // Kiểm tra subtask tồn tại\r\n    if (!subtask) {\r\n      return 0;\r\n    }\r\n    \r\n    // If progress is explicitly set, use it\r\n    if (subtask.progress !== undefined && subtask.progress !== null) {\r\n      return subtask.progress;\r\n    }\r\n    \r\n    // Otherwise, determine based on status\r\n    switch (subtask.status) {\r\n      case STATUS.COMPLETED:\r\n        return 100;\r\n      case STATUS.IN_PROGRESS:\r\n        return 50;\r\n      default:\r\n        return 0;\r\n    }\r\n  };\r\n\r\n  // Calculate task progress based on subtasks\r\n  const calculateTaskProgress = (task) => {\r\n    // Ki<PERSON>m tra task tồn tại\r\n    if (!task) {\r\n      return 0;\r\n    }\r\n    \r\n    // Đ<PERSON>m bảo subtasks là một mảng\r\n    const subtasks = task.subtasks || [];\r\n    \r\n    // If no subtasks, base on task status\r\n    if (subtasks.length === 0) {\r\n      switch (task.status) {\r\n        case STATUS.COMPLETED:\r\n          return 100;\r\n        case STATUS.IN_PROGRESS:\r\n          return 50;\r\n        default:\r\n          return 0;\r\n      }\r\n    }\r\n    \r\n    // Calculate progress based on subtasks\r\n    let totalProgress = 0;\r\n    subtasks.forEach(subtask => {\r\n      totalProgress += calculateSubtaskProgress(subtask);\r\n    });\r\n    \r\n    return Math.floor(totalProgress / subtasks.length);\r\n  };\r\n\r\n  // Calculate milestone progress based on tasks\r\n  const calculateMilestoneProgress = (milestone) => {\r\n    // Kiểm tra milestone tồn tại\r\n    if (!milestone) {\r\n      return 0;\r\n    }\r\n    \r\n    // Đảm bảo tasks là một mảng\r\n    const tasks = milestone.tasks || [];\r\n    \r\n    // If no tasks, return 0\r\n    if (tasks.length === 0) {\r\n      return 0;\r\n    }\r\n    \r\n    // Calculate progress based on tasks\r\n    let totalProgress = 0;\r\n    tasks.forEach(task => {\r\n      // Đảm bảo task tồn tại và có thuộc tính cần thiết\r\n      if (task) {\r\n        totalProgress += calculateTaskProgress(task);\r\n      }\r\n    });\r\n    \r\n    return Math.floor(totalProgress / tasks.length);\r\n  };\r\n\r\n  // Calculate overall plan progress\r\n  const calculatePlanProgress = () => {\r\n    if (!plan || !plan.milestones || plan.milestones.length === 0) {\r\n      return 0;\r\n    }\r\n    \r\n    let totalProgress = 0;\r\n    plan.milestones.forEach(milestone => {\r\n      totalProgress += calculateMilestoneProgress(milestone);\r\n    });\r\n    \r\n    return Math.floor(totalProgress / plan.milestones.length);\r\n  };\r\n\r\n  // Calculate overview information from plan data\r\n  const totalMilestones = plan.milestones?.length || 0;\r\n\r\n  // Calculate total tasks from all milestones\r\n  const totalTasks = plan.milestones?.reduce((sum, milestone) =>\r\n    sum + (milestone.tasks?.length || 0), 0) || 0;\r\n\r\n  // Calculate total subtasks from all tasks\r\n  const totalSubtasks = plan.milestones?.reduce((sum, milestone) =>\r\n    sum + milestone.tasks?.reduce((taskSum, task) =>\r\n      taskSum + (task.subtasks?.length || 0), 0), 0) || 0;\r\n\r\n  // Calculate progress and task status counts\r\n  const progress = calculatePlanProgress();\r\n  \r\n  // Calculate completed, in progress, and not started tasks\r\n  let completedTasks = 0;\r\n  let inProgressTasks = 0;\r\n  let notStartedTasks = 0;\r\n\r\n  plan.milestones?.forEach(milestone => {\r\n    milestone.tasks?.forEach(task => {\r\n      const taskProgress = calculateTaskProgress(task);\r\n      if (taskProgress === 100) {\r\n        completedTasks++;\r\n      } else if (taskProgress > 0) {\r\n        inProgressTasks++;\r\n      } else {\r\n        notStartedTasks++;\r\n      }\r\n    });\r\n  });\r\n\r\n  // Determine plan status\r\n  let status = { color: '#4CAF50', label: 'Active' };\r\n  if (progress === 100) {\r\n    status = { color: '#2196F3', label: 'Completed' };\r\n  } else if (progress === 0) {\r\n    status = { color: '#FFC107', label: 'Not Started' };\r\n  } else if (progress > 0) {\r\n    status = { color: '#FF9800', label: 'In Progress' };\r\n  }\r\n\r\n  // Create tags from plan information\r\n  const tags = [];\r\n  if (plan.start_date && plan.end_date) {\r\n    const duration = dayjs(plan.end_date).diff(dayjs(plan.start_date), 'day');\r\n    tags.push(`${duration} days`);\r\n  }\r\n\r\n  if (totalMilestones > 0) {\r\n    tags.push(`${totalMilestones} milestones`);\r\n  }\r\n\r\n  // Check if there are assignees\r\n  const hasAssignees = plan.milestones?.some(milestone =>\r\n    milestone.tasks?.some(task => task.assignees?.length > 0));\r\n\r\n  if (hasAssignees) {\r\n    tags.push('Has assignees');\r\n  }\r\n\r\n  return (\r\n    <Card className={styles.planCard} key={index} sx={{ position: 'relative' }}>\r\n      {/* Always show status indicator at top right */}\r\n      <Box \r\n        className={styles.statusIndicator} \r\n        sx={{ \r\n          backgroundColor: status.color\r\n        }}\r\n      >\r\n        <Typography \r\n          variant=\"caption\"\r\n          sx={{\r\n            fontFamily: '\"Recursive Variable\", sans-serif'\r\n          }}\r\n        >\r\n          {status.label}\r\n        </Typography>\r\n      </Box>\r\n      \r\n      <Link to={`/d/plan/${plan.slug}`} className={styles.planCardLink}>\r\n        <Box className={styles.cardHeader}>\r\n          <Box className={styles.cardIcon}>\r\n            <Iconify icon=\"codicon:project\" width={24} height={24} color={mainYellowColor} />\r\n          </Box>\r\n          <Typography\r\n            variant=\"h6\"\r\n            className={styles.planName}\r\n            noWrap\r\n            sx={{\r\n              fontFamily: '\"Recursive Variable\", sans-serif',\r\n              color: '#333',\r\n              fontSize: '1.2rem',\r\n              fontWeight: 700,\r\n              margin: 0,\r\n              flex: 1,\r\n              overflow: 'hidden',\r\n              textOverflow: 'ellipsis'\r\n            }}\r\n          >\r\n            {plan.name}\r\n          </Typography>\r\n        </Box>\r\n\r\n\r\n\r\n        <Box className={styles.cardMeta}>\r\n          {(plan.start_date || plan.end_date) && (\r\n            <Box className={styles.metaItem}>\r\n              <Iconify icon=\"carbon:calendar\" width={16} height={16} color={mainYellowColor} />\r\n              <Typography variant=\"caption\">\r\n                <span className={styles.metaLabel}>Timeline: </span>\r\n                {plan.start_date ? dayjs(plan.start_date).format('MM/DD/YYYY') : 'Not started'}\r\n                {plan.start_date && plan.end_date && \" ~ \"}\r\n                {plan.end_date ? dayjs(plan.end_date).format('MM/DD/YYYY') : 'No end date'}\r\n              </Typography>\r\n            </Box>\r\n          )}\r\n        </Box>\r\n\r\n        {/* Display plan structure overview */}\r\n        <Box className={styles.planStructure}>\r\n          <Box className={styles.structureItem}>\r\n            <Box className={styles.structureIcon} sx={{ backgroundColor: '#E3F2FD' }}>\r\n              <Iconify icon=\"mdi:flag-variant\" width={16} height={16} color=\"#2196F3\" />\r\n            </Box>\r\n            <Box className={styles.structureInfo}>\r\n              <Typography\r\n                variant=\"caption\"\r\n                className={styles.structureLabel}\r\n                sx={{\r\n                  fontSize: '0.7rem',\r\n                  color: '#666',\r\n                  fontFamily: '\"Recursive Variable\", sans-serif'\r\n                }}\r\n              >\r\n                Milestones\r\n              </Typography>\r\n              <Typography\r\n                variant=\"h6\"\r\n                className={styles.structureValue}\r\n                sx={{\r\n                  fontWeight: 700,\r\n                  fontSize: '1.2rem',\r\n                  color: '#333',\r\n                  fontFamily: '\"Recursive Variable\", sans-serif',\r\n                  margin: 0,\r\n                  lineHeight: 1.2\r\n                }}\r\n              >\r\n                {totalMilestones}\r\n              </Typography>\r\n            </Box>\r\n          </Box>\r\n\r\n          <Box className={styles.structureItem}>\r\n            <Box className={styles.structureIcon} sx={{ backgroundColor: '#FFF8E1' }}>\r\n              <Iconify icon=\"mdi:checkbox-marked-outline\" width={16} height={16} color=\"#FFA000\" />\r\n            </Box>\r\n            <Box className={styles.structureInfo}>\r\n              <Typography\r\n                variant=\"caption\"\r\n                className={styles.structureLabel}\r\n                sx={{\r\n                  fontSize: '0.7rem',\r\n                  color: '#666',\r\n                  fontFamily: '\"Recursive Variable\", sans-serif'\r\n                }}\r\n              >\r\n                Tasks\r\n              </Typography>\r\n              <Typography\r\n                variant=\"h6\"\r\n                className={styles.structureValue}\r\n                sx={{\r\n                  fontWeight: 700,\r\n                  fontSize: '1.2rem',\r\n                  color: '#333',\r\n                  fontFamily: '\"Recursive Variable\", sans-serif',\r\n                  margin: 0,\r\n                  lineHeight: 1.2\r\n                }}\r\n              >\r\n                {totalTasks}\r\n              </Typography>\r\n            </Box>\r\n          </Box>\r\n\r\n          <Box className={styles.structureItem}>\r\n            <Box className={styles.structureIcon} sx={{ backgroundColor: '#E8F5E9' }}>\r\n              <Iconify icon=\"mdi:format-list-checks\" width={16} height={16} color=\"#4CAF50\" />\r\n            </Box>\r\n            <Box className={styles.structureInfo}>\r\n              <Typography\r\n                variant=\"caption\"\r\n                className={styles.structureLabel}\r\n                sx={{\r\n                  fontSize: '0.7rem',\r\n                  color: '#666',\r\n                  fontFamily: '\"Recursive Variable\", sans-serif'\r\n                }}\r\n              >\r\n                Subtasks\r\n              </Typography>\r\n              <Typography\r\n                variant=\"h6\"\r\n                className={styles.structureValue}\r\n                sx={{\r\n                  fontWeight: 700,\r\n                  fontSize: '1.2rem',\r\n                  color: '#333',\r\n                  fontFamily: '\"Recursive Variable\", sans-serif',\r\n                  margin: 0,\r\n                  lineHeight: 1.2\r\n                }}\r\n              >\r\n                {totalSubtasks}\r\n              </Typography>\r\n            </Box>\r\n          </Box>\r\n        </Box>\r\n\r\n        {/* Display progress */}\r\n        <Box className={styles.progressSection}>\r\n          <Box className={styles.progressHeader}>\r\n            <Typography\r\n              variant=\"caption\"\r\n              color=\"textSecondary\"\r\n              sx={{\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                fontSize: '0.7rem',\r\n                color: '#666'\r\n              }}\r\n            >\r\n              Progress\r\n            </Typography>\r\n            <Typography\r\n              variant=\"h6\"\r\n              className={styles.progressValue}\r\n              sx={{\r\n                fontWeight: 700,\r\n                fontSize: '1.2rem',\r\n                color: '#333',\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                margin: 0,\r\n                lineHeight: 1.2\r\n              }}\r\n            >\r\n              {progress}%\r\n            </Typography>\r\n          </Box>\r\n          <LinearProgress \r\n            variant=\"determinate\" \r\n            value={progress} \r\n            sx={{\r\n              height: 8,\r\n              borderRadius: 4,\r\n              backgroundColor: 'rgba(0, 0, 0, 0.05)',\r\n              '& .MuiLinearProgress-bar': {\r\n                borderRadius: 4,\r\n                backgroundColor: progress === 100 ? '#4CAF50' : mainYellowColor,\r\n              }\r\n            }}\r\n          />\r\n\r\n          {/* Display task status distribution */}\r\n          <Box className={styles.taskStatusLegend}>\r\n            <Box className={styles.legendItem}>\r\n              <Box className={styles.legendColor} sx={{ backgroundColor: '#4CAF50' }} />\r\n              <Typography\r\n                variant=\"caption\"\r\n                className={styles.legendText}\r\n                sx={{\r\n                  fontFamily: '\"Recursive Variable\", sans-serif',\r\n                  fontSize: '0.75rem',\r\n                  color: '#666'\r\n                }}\r\n              >\r\n                {completedTasks} completed\r\n              </Typography>\r\n            </Box>\r\n            <Box className={styles.legendItem}>\r\n              <Box className={styles.legendColor} sx={{ backgroundColor: '#FF9800' }} />\r\n              <Typography\r\n                variant=\"caption\"\r\n                className={styles.legendText}\r\n                sx={{\r\n                  fontFamily: '\"Recursive Variable\", sans-serif',\r\n                  fontSize: '0.75rem',\r\n                  color: '#666'\r\n                }}\r\n              >\r\n                {inProgressTasks} in progress\r\n              </Typography>\r\n            </Box>\r\n            <Box className={styles.legendItem}>\r\n              <Box className={styles.legendColor} sx={{ backgroundColor: '#E0E0E0' }} />\r\n              <Typography\r\n                variant=\"caption\"\r\n                className={styles.legendText}\r\n                sx={{\r\n                  fontFamily: '\"Recursive Variable\", sans-serif',\r\n                  fontSize: '0.75rem',\r\n                  color: '#666'\r\n                }}\r\n              >\r\n                {notStartedTasks} not started\r\n              </Typography>\r\n            </Box>\r\n          </Box>\r\n        </Box>\r\n\r\n        {/* Display plan creator and creation time */}\r\n        <Box \r\n          className={styles.planCreator} \r\n          sx={{ \r\n            borderTop: 'none',\r\n            display: 'flex',\r\n            alignItems: 'center',\r\n            gap: '8px',\r\n            marginTop: '8px',\r\n            paddingTop: '8px',\r\n            justifyContent: 'space-between',\r\n            fontFamily: '\"Recursive Variable\", sans-serif',\r\n            marginBottom: '8px'\r\n          }}\r\n        >\r\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n            <Avatar\r\n              src={plan.user.avatar || ''}\r\n              alt={`${plan.user.first_name} ${plan.user.last_name}`}\r\n              className={styles.creatorAvatar}\r\n              sx={{ width: '28px', height: '28px' }}\r\n            />\r\n            <Box>\r\n              <Typography\r\n                variant=\"caption\"\r\n                className={styles.creatorLabel}\r\n                sx={{\r\n                  color: '#888',\r\n                  fontSize: '0.7rem',\r\n                  display: 'block',\r\n                  fontFamily: '\"Recursive Variable\", sans-serif'\r\n                }}\r\n              >\r\n                Created by\r\n              </Typography>\r\n              <Typography\r\n                variant=\"caption\"\r\n                className={styles.creatorName}\r\n                sx={{\r\n                  fontWeight: 600,\r\n                  fontSize: '0.8rem',\r\n                  color: '#333',\r\n                  fontFamily: '\"Recursive Variable\", sans-serif'\r\n                }}\r\n              >\r\n                {plan.user.first_name} {plan.user.last_name}\r\n              </Typography>\r\n            </Box>\r\n          </Box>\r\n\r\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n            {plan.user.id !== currentUser.id && (\r\n              <Box \r\n                sx={{ \r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  gap: '4px',\r\n                  backgroundColor: 'rgba(255, 99, 71, 0.9)',\r\n                  color: 'white',\r\n                  padding: '4px 10px',\r\n                  borderRadius: '16px',\r\n                  fontSize: '0.75rem',\r\n                  fontWeight: 500,\r\n                  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',\r\n                  marginRight: '8px'\r\n                }}\r\n              >\r\n                <Iconify icon=\"ph:share-network\" width={14} height={14} />\r\n                <Typography \r\n                  variant=\"caption\"\r\n                  sx={{\r\n                    fontFamily: '\"Recursive Variable\", sans-serif',\r\n                    color: 'white'\r\n                  }}\r\n                >\r\n                  Shared\r\n                </Typography>\r\n              </Box>\r\n            )}\r\n            <Typography\r\n              variant=\"caption\"\r\n              className={styles.lastUpdated}\r\n              sx={{\r\n                color: '#888',\r\n                fontSize: '0.7rem',\r\n                display: 'flex',\r\n                alignItems: 'center',\r\n                gap: '4px',\r\n                fontFamily: '\"Recursive Variable\", sans-serif'\r\n              }}\r\n            >\r\n              <Iconify icon=\"mdi:clock-outline\" width={14} height={14} />\r\n              {dayjs(plan.updated_at).fromNow()}\r\n            </Typography>\r\n          </Box>\r\n        </Box>\r\n      </Link>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default PlanCard;\r\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,EAAEC,UAAU,EAAEC,IAAI,EAAEC,MAAM,EAAEC,cAAc,QAAQ,eAAe;AAC7E,SAASC,IAAI,QAAQ,kBAAkB;AACvC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,OAAOC,MAAM,MAAM,uBAAuB;;AAE1C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,MAAM,GAAG;EACbC,WAAW,EAAE,CAAC;EACdC,WAAW,EAAE,CAAC;EACdC,SAAS,EAAE;AACb,CAAC;AAED,MAAMC,QAAQ,GAAGC,IAAA,IAAkC;EAAA,IAAAC,gBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA;EAAA,IAAjC;IAAEC,IAAI;IAAEC,WAAW;IAAEC;EAAM,CAAC,GAAAR,IAAA;EAC5C;EACA,MAAMS,wBAAwB,GAAIC,OAAO,IAAK;IAC5C;IACA,IAAI,CAACA,OAAO,EAAE;MACZ,OAAO,CAAC;IACV;;IAEA;IACA,IAAIA,OAAO,CAACC,QAAQ,KAAKC,SAAS,IAAIF,OAAO,CAACC,QAAQ,KAAK,IAAI,EAAE;MAC/D,OAAOD,OAAO,CAACC,QAAQ;IACzB;;IAEA;IACA,QAAQD,OAAO,CAACG,MAAM;MACpB,KAAKlB,MAAM,CAACG,SAAS;QACnB,OAAO,GAAG;MACZ,KAAKH,MAAM,CAACE,WAAW;QACrB,OAAO,EAAE;MACX;QACE,OAAO,CAAC;IACZ;EACF,CAAC;;EAED;EACA,MAAMiB,qBAAqB,GAAIC,IAAI,IAAK;IACtC;IACA,IAAI,CAACA,IAAI,EAAE;MACT,OAAO,CAAC;IACV;;IAEA;IACA,MAAMC,QAAQ,GAAGD,IAAI,CAACC,QAAQ,IAAI,EAAE;;IAEpC;IACA,IAAIA,QAAQ,CAACC,MAAM,KAAK,CAAC,EAAE;MACzB,QAAQF,IAAI,CAACF,MAAM;QACjB,KAAKlB,MAAM,CAACG,SAAS;UACnB,OAAO,GAAG;QACZ,KAAKH,MAAM,CAACE,WAAW;UACrB,OAAO,EAAE;QACX;UACE,OAAO,CAAC;MACZ;IACF;;IAEA;IACA,IAAIqB,aAAa,GAAG,CAAC;IACrBF,QAAQ,CAACG,OAAO,CAACT,OAAO,IAAI;MAC1BQ,aAAa,IAAIT,wBAAwB,CAACC,OAAO,CAAC;IACpD,CAAC,CAAC;IAEF,OAAOU,IAAI,CAACC,KAAK,CAACH,aAAa,GAAGF,QAAQ,CAACC,MAAM,CAAC;EACpD,CAAC;;EAED;EACA,MAAMK,0BAA0B,GAAIC,SAAS,IAAK;IAChD;IACA,IAAI,CAACA,SAAS,EAAE;MACd,OAAO,CAAC;IACV;;IAEA;IACA,MAAMC,KAAK,GAAGD,SAAS,CAACC,KAAK,IAAI,EAAE;;IAEnC;IACA,IAAIA,KAAK,CAACP,MAAM,KAAK,CAAC,EAAE;MACtB,OAAO,CAAC;IACV;;IAEA;IACA,IAAIC,aAAa,GAAG,CAAC;IACrBM,KAAK,CAACL,OAAO,CAACJ,IAAI,IAAI;MACpB;MACA,IAAIA,IAAI,EAAE;QACRG,aAAa,IAAIJ,qBAAqB,CAACC,IAAI,CAAC;MAC9C;IACF,CAAC,CAAC;IAEF,OAAOK,IAAI,CAACC,KAAK,CAACH,aAAa,GAAGM,KAAK,CAACP,MAAM,CAAC;EACjD,CAAC;;EAED;EACA,MAAMQ,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,CAACnB,IAAI,IAAI,CAACA,IAAI,CAACoB,UAAU,IAAIpB,IAAI,CAACoB,UAAU,CAACT,MAAM,KAAK,CAAC,EAAE;MAC7D,OAAO,CAAC;IACV;IAEA,IAAIC,aAAa,GAAG,CAAC;IACrBZ,IAAI,CAACoB,UAAU,CAACP,OAAO,CAACI,SAAS,IAAI;MACnCL,aAAa,IAAII,0BAA0B,CAACC,SAAS,CAAC;IACxD,CAAC,CAAC;IAEF,OAAOH,IAAI,CAACC,KAAK,CAACH,aAAa,GAAGZ,IAAI,CAACoB,UAAU,CAACT,MAAM,CAAC;EAC3D,CAAC;;EAED;EACA,MAAMU,eAAe,GAAG,EAAA1B,gBAAA,GAAAK,IAAI,CAACoB,UAAU,cAAAzB,gBAAA,uBAAfA,gBAAA,CAAiBgB,MAAM,KAAI,CAAC;;EAEpD;EACA,MAAMW,UAAU,GAAG,EAAA1B,iBAAA,GAAAI,IAAI,CAACoB,UAAU,cAAAxB,iBAAA,uBAAfA,iBAAA,CAAiB2B,MAAM,CAAC,CAACC,GAAG,EAAEP,SAAS;IAAA,IAAAQ,gBAAA;IAAA,OACxDD,GAAG,IAAI,EAAAC,gBAAA,GAAAR,SAAS,CAACC,KAAK,cAAAO,gBAAA,uBAAfA,gBAAA,CAAiBd,MAAM,KAAI,CAAC,CAAC;EAAA,GAAE,CAAC,CAAC,KAAI,CAAC;;EAE/C;EACA,MAAMe,aAAa,GAAG,EAAA7B,iBAAA,GAAAG,IAAI,CAACoB,UAAU,cAAAvB,iBAAA,uBAAfA,iBAAA,CAAiB0B,MAAM,CAAC,CAACC,GAAG,EAAEP,SAAS;IAAA,IAAAU,iBAAA;IAAA,OAC3DH,GAAG,KAAAG,iBAAA,GAAGV,SAAS,CAACC,KAAK,cAAAS,iBAAA,uBAAfA,iBAAA,CAAiBJ,MAAM,CAAC,CAACK,OAAO,EAAEnB,IAAI;MAAA,IAAAoB,cAAA;MAAA,OAC1CD,OAAO,IAAI,EAAAC,cAAA,GAAApB,IAAI,CAACC,QAAQ,cAAAmB,cAAA,uBAAbA,cAAA,CAAelB,MAAM,KAAI,CAAC,CAAC;IAAA,GAAE,CAAC,CAAC;EAAA,GAAE,CAAC,CAAC,KAAI,CAAC;;EAEvD;EACA,MAAMN,QAAQ,GAAGc,qBAAqB,CAAC,CAAC;;EAExC;EACA,IAAIW,cAAc,GAAG,CAAC;EACtB,IAAIC,eAAe,GAAG,CAAC;EACvB,IAAIC,eAAe,GAAG,CAAC;EAEvB,CAAAlC,iBAAA,GAAAE,IAAI,CAACoB,UAAU,cAAAtB,iBAAA,uBAAfA,iBAAA,CAAiBe,OAAO,CAACI,SAAS,IAAI;IAAA,IAAAgB,iBAAA;IACpC,CAAAA,iBAAA,GAAAhB,SAAS,CAACC,KAAK,cAAAe,iBAAA,uBAAfA,iBAAA,CAAiBpB,OAAO,CAACJ,IAAI,IAAI;MAC/B,MAAMyB,YAAY,GAAG1B,qBAAqB,CAACC,IAAI,CAAC;MAChD,IAAIyB,YAAY,KAAK,GAAG,EAAE;QACxBJ,cAAc,EAAE;MAClB,CAAC,MAAM,IAAII,YAAY,GAAG,CAAC,EAAE;QAC3BH,eAAe,EAAE;MACnB,CAAC,MAAM;QACLC,eAAe,EAAE;MACnB;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;;EAEF;EACA,IAAIzB,MAAM,GAAG;IAAE4B,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAS,CAAC;EAClD,IAAI/B,QAAQ,KAAK,GAAG,EAAE;IACpBE,MAAM,GAAG;MAAE4B,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAY,CAAC;EACnD,CAAC,MAAM,IAAI/B,QAAQ,KAAK,CAAC,EAAE;IACzBE,MAAM,GAAG;MAAE4B,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAc,CAAC;EACrD,CAAC,MAAM,IAAI/B,QAAQ,GAAG,CAAC,EAAE;IACvBE,MAAM,GAAG;MAAE4B,KAAK,EAAE,SAAS;MAAEC,KAAK,EAAE;IAAc,CAAC;EACrD;;EAEA;EACA,MAAMC,IAAI,GAAG,EAAE;EACf,IAAIrC,IAAI,CAACsC,UAAU,IAAItC,IAAI,CAACuC,QAAQ,EAAE;IACpC,MAAMC,QAAQ,GAAGzD,KAAK,CAACiB,IAAI,CAACuC,QAAQ,CAAC,CAACE,IAAI,CAAC1D,KAAK,CAACiB,IAAI,CAACsC,UAAU,CAAC,EAAE,KAAK,CAAC;IACzED,IAAI,CAACK,IAAI,CAAC,GAAGF,QAAQ,OAAO,CAAC;EAC/B;EAEA,IAAInB,eAAe,GAAG,CAAC,EAAE;IACvBgB,IAAI,CAACK,IAAI,CAAC,GAAGrB,eAAe,aAAa,CAAC;EAC5C;;EAEA;EACA,MAAMsB,YAAY,IAAA5C,iBAAA,GAAGC,IAAI,CAACoB,UAAU,cAAArB,iBAAA,uBAAfA,iBAAA,CAAiB6C,IAAI,CAAC3B,SAAS;IAAA,IAAA4B,iBAAA;IAAA,QAAAA,iBAAA,GAClD5B,SAAS,CAACC,KAAK,cAAA2B,iBAAA,uBAAfA,iBAAA,CAAiBD,IAAI,CAACnC,IAAI;MAAA,IAAAqC,eAAA;MAAA,OAAI,EAAAA,eAAA,GAAArC,IAAI,CAACsC,SAAS,cAAAD,eAAA,uBAAdA,eAAA,CAAgBnC,MAAM,IAAG,CAAC;IAAA,EAAC;EAAA,EAAC;EAE5D,IAAIgC,YAAY,EAAE;IAChBN,IAAI,CAACK,IAAI,CAAC,eAAe,CAAC;EAC5B;EAEA,oBACEtD,OAAA,CAACT,IAAI;IAACqE,SAAS,EAAE9D,MAAM,CAAC+D,QAAS;IAAaC,EAAE,EAAE;MAAEC,QAAQ,EAAE;IAAW,CAAE;IAAAC,QAAA,gBAEzEhE,OAAA,CAACX,GAAG;MACFuE,SAAS,EAAE9D,MAAM,CAACmE,eAAgB;MAClCH,EAAE,EAAE;QACFI,eAAe,EAAE/C,MAAM,CAAC4B;MAC1B,CAAE;MAAAiB,QAAA,eAEFhE,OAAA,CAACV,UAAU;QACT6E,OAAO,EAAC,SAAS;QACjBL,EAAE,EAAE;UACFM,UAAU,EAAE;QACd,CAAE;QAAAJ,QAAA,EAED7C,MAAM,CAAC6B;MAAK;QAAAqB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,eAENxE,OAAA,CAACN,IAAI;MAAC+E,EAAE,EAAE,WAAW7D,IAAI,CAAC8D,IAAI,EAAG;MAACd,SAAS,EAAE9D,MAAM,CAAC6E,YAAa;MAAAX,QAAA,gBAC/DhE,OAAA,CAACX,GAAG;QAACuE,SAAS,EAAE9D,MAAM,CAAC8E,UAAW;QAAAZ,QAAA,gBAChChE,OAAA,CAACX,GAAG;UAACuE,SAAS,EAAE9D,MAAM,CAAC+E,QAAS;UAAAb,QAAA,eAC9BhE,OAAA,CAACJ,OAAO;YAACkF,IAAI,EAAC,iBAAiB;YAACC,KAAK,EAAE,EAAG;YAACC,MAAM,EAAE,EAAG;YAACjC,KAAK,EAAElD;UAAgB;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9E,CAAC,eACNxE,OAAA,CAACV,UAAU;UACT6E,OAAO,EAAC,IAAI;UACZP,SAAS,EAAE9D,MAAM,CAACmF,QAAS;UAC3BC,MAAM;UACNpB,EAAE,EAAE;YACFM,UAAU,EAAE,kCAAkC;YAC9CrB,KAAK,EAAE,MAAM;YACboC,QAAQ,EAAE,QAAQ;YAClBC,UAAU,EAAE,GAAG;YACfC,MAAM,EAAE,CAAC;YACTC,IAAI,EAAE,CAAC;YACPC,QAAQ,EAAE,QAAQ;YAClBC,YAAY,EAAE;UAChB,CAAE;UAAAxB,QAAA,EAEDpD,IAAI,CAAC6E;QAAI;UAAApB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eAINxE,OAAA,CAACX,GAAG;QAACuE,SAAS,EAAE9D,MAAM,CAAC4F,QAAS;QAAA1B,QAAA,EAC7B,CAACpD,IAAI,CAACsC,UAAU,IAAItC,IAAI,CAACuC,QAAQ,kBAChCnD,OAAA,CAACX,GAAG;UAACuE,SAAS,EAAE9D,MAAM,CAAC6F,QAAS;UAAA3B,QAAA,gBAC9BhE,OAAA,CAACJ,OAAO;YAACkF,IAAI,EAAC,iBAAiB;YAACC,KAAK,EAAE,EAAG;YAACC,MAAM,EAAE,EAAG;YAACjC,KAAK,EAAElD;UAAgB;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACjFxE,OAAA,CAACV,UAAU;YAAC6E,OAAO,EAAC,SAAS;YAAAH,QAAA,gBAC3BhE,OAAA;cAAM4D,SAAS,EAAE9D,MAAM,CAAC8F,SAAU;cAAA5B,QAAA,EAAC;YAAU;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EACnD5D,IAAI,CAACsC,UAAU,GAAGvD,KAAK,CAACiB,IAAI,CAACsC,UAAU,CAAC,CAAC2C,MAAM,CAAC,YAAY,CAAC,GAAG,aAAa,EAC7EjF,IAAI,CAACsC,UAAU,IAAItC,IAAI,CAACuC,QAAQ,IAAI,KAAK,EACzCvC,IAAI,CAACuC,QAAQ,GAAGxD,KAAK,CAACiB,IAAI,CAACuC,QAAQ,CAAC,CAAC0C,MAAM,CAAC,YAAY,CAAC,GAAG,aAAa;UAAA;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGNxE,OAAA,CAACX,GAAG;QAACuE,SAAS,EAAE9D,MAAM,CAACgG,aAAc;QAAA9B,QAAA,gBACnChE,OAAA,CAACX,GAAG;UAACuE,SAAS,EAAE9D,MAAM,CAACiG,aAAc;UAAA/B,QAAA,gBACnChE,OAAA,CAACX,GAAG;YAACuE,SAAS,EAAE9D,MAAM,CAACkG,aAAc;YAAClC,EAAE,EAAE;cAAEI,eAAe,EAAE;YAAU,CAAE;YAAAF,QAAA,eACvEhE,OAAA,CAACJ,OAAO;cAACkF,IAAI,EAAC,kBAAkB;cAACC,KAAK,EAAE,EAAG;cAACC,MAAM,EAAE,EAAG;cAACjC,KAAK,EAAC;YAAS;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvE,CAAC,eACNxE,OAAA,CAACX,GAAG;YAACuE,SAAS,EAAE9D,MAAM,CAACmG,aAAc;YAAAjC,QAAA,gBACnChE,OAAA,CAACV,UAAU;cACT6E,OAAO,EAAC,SAAS;cACjBP,SAAS,EAAE9D,MAAM,CAACoG,cAAe;cACjCpC,EAAE,EAAE;gBACFqB,QAAQ,EAAE,QAAQ;gBAClBpC,KAAK,EAAE,MAAM;gBACbqB,UAAU,EAAE;cACd,CAAE;cAAAJ,QAAA,EACH;YAED;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbxE,OAAA,CAACV,UAAU;cACT6E,OAAO,EAAC,IAAI;cACZP,SAAS,EAAE9D,MAAM,CAACqG,cAAe;cACjCrC,EAAE,EAAE;gBACFsB,UAAU,EAAE,GAAG;gBACfD,QAAQ,EAAE,QAAQ;gBAClBpC,KAAK,EAAE,MAAM;gBACbqB,UAAU,EAAE,kCAAkC;gBAC9CiB,MAAM,EAAE,CAAC;gBACTe,UAAU,EAAE;cACd,CAAE;cAAApC,QAAA,EAED/B;YAAe;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxE,OAAA,CAACX,GAAG;UAACuE,SAAS,EAAE9D,MAAM,CAACiG,aAAc;UAAA/B,QAAA,gBACnChE,OAAA,CAACX,GAAG;YAACuE,SAAS,EAAE9D,MAAM,CAACkG,aAAc;YAAClC,EAAE,EAAE;cAAEI,eAAe,EAAE;YAAU,CAAE;YAAAF,QAAA,eACvEhE,OAAA,CAACJ,OAAO;cAACkF,IAAI,EAAC,6BAA6B;cAACC,KAAK,EAAE,EAAG;cAACC,MAAM,EAAE,EAAG;cAACjC,KAAK,EAAC;YAAS;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClF,CAAC,eACNxE,OAAA,CAACX,GAAG;YAACuE,SAAS,EAAE9D,MAAM,CAACmG,aAAc;YAAAjC,QAAA,gBACnChE,OAAA,CAACV,UAAU;cACT6E,OAAO,EAAC,SAAS;cACjBP,SAAS,EAAE9D,MAAM,CAACoG,cAAe;cACjCpC,EAAE,EAAE;gBACFqB,QAAQ,EAAE,QAAQ;gBAClBpC,KAAK,EAAE,MAAM;gBACbqB,UAAU,EAAE;cACd,CAAE;cAAAJ,QAAA,EACH;YAED;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbxE,OAAA,CAACV,UAAU;cACT6E,OAAO,EAAC,IAAI;cACZP,SAAS,EAAE9D,MAAM,CAACqG,cAAe;cACjCrC,EAAE,EAAE;gBACFsB,UAAU,EAAE,GAAG;gBACfD,QAAQ,EAAE,QAAQ;gBAClBpC,KAAK,EAAE,MAAM;gBACbqB,UAAU,EAAE,kCAAkC;gBAC9CiB,MAAM,EAAE,CAAC;gBACTe,UAAU,EAAE;cACd,CAAE;cAAApC,QAAA,EAED9B;YAAU;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxE,OAAA,CAACX,GAAG;UAACuE,SAAS,EAAE9D,MAAM,CAACiG,aAAc;UAAA/B,QAAA,gBACnChE,OAAA,CAACX,GAAG;YAACuE,SAAS,EAAE9D,MAAM,CAACkG,aAAc;YAAClC,EAAE,EAAE;cAAEI,eAAe,EAAE;YAAU,CAAE;YAAAF,QAAA,eACvEhE,OAAA,CAACJ,OAAO;cAACkF,IAAI,EAAC,wBAAwB;cAACC,KAAK,EAAE,EAAG;cAACC,MAAM,EAAE,EAAG;cAACjC,KAAK,EAAC;YAAS;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7E,CAAC,eACNxE,OAAA,CAACX,GAAG;YAACuE,SAAS,EAAE9D,MAAM,CAACmG,aAAc;YAAAjC,QAAA,gBACnChE,OAAA,CAACV,UAAU;cACT6E,OAAO,EAAC,SAAS;cACjBP,SAAS,EAAE9D,MAAM,CAACoG,cAAe;cACjCpC,EAAE,EAAE;gBACFqB,QAAQ,EAAE,QAAQ;gBAClBpC,KAAK,EAAE,MAAM;gBACbqB,UAAU,EAAE;cACd,CAAE;cAAAJ,QAAA,EACH;YAED;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbxE,OAAA,CAACV,UAAU;cACT6E,OAAO,EAAC,IAAI;cACZP,SAAS,EAAE9D,MAAM,CAACqG,cAAe;cACjCrC,EAAE,EAAE;gBACFsB,UAAU,EAAE,GAAG;gBACfD,QAAQ,EAAE,QAAQ;gBAClBpC,KAAK,EAAE,MAAM;gBACbqB,UAAU,EAAE,kCAAkC;gBAC9CiB,MAAM,EAAE,CAAC;gBACTe,UAAU,EAAE;cACd,CAAE;cAAApC,QAAA,EAED1B;YAAa;cAAA+B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNxE,OAAA,CAACX,GAAG;QAACuE,SAAS,EAAE9D,MAAM,CAACuG,eAAgB;QAAArC,QAAA,gBACrChE,OAAA,CAACX,GAAG;UAACuE,SAAS,EAAE9D,MAAM,CAACwG,cAAe;UAAAtC,QAAA,gBACpChE,OAAA,CAACV,UAAU;YACT6E,OAAO,EAAC,SAAS;YACjBpB,KAAK,EAAC,eAAe;YACrBe,EAAE,EAAE;cACFM,UAAU,EAAE,kCAAkC;cAC9Ce,QAAQ,EAAE,QAAQ;cAClBpC,KAAK,EAAE;YACT,CAAE;YAAAiB,QAAA,EACH;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxE,OAAA,CAACV,UAAU;YACT6E,OAAO,EAAC,IAAI;YACZP,SAAS,EAAE9D,MAAM,CAACyG,aAAc;YAChCzC,EAAE,EAAE;cACFsB,UAAU,EAAE,GAAG;cACfD,QAAQ,EAAE,QAAQ;cAClBpC,KAAK,EAAE,MAAM;cACbqB,UAAU,EAAE,kCAAkC;cAC9CiB,MAAM,EAAE,CAAC;cACTe,UAAU,EAAE;YACd,CAAE;YAAApC,QAAA,GAED/C,QAAQ,EAAC,GACZ;UAAA;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNxE,OAAA,CAACP,cAAc;UACb0E,OAAO,EAAC,aAAa;UACrBqC,KAAK,EAAEvF,QAAS;UAChB6C,EAAE,EAAE;YACFkB,MAAM,EAAE,CAAC;YACTyB,YAAY,EAAE,CAAC;YACfvC,eAAe,EAAE,qBAAqB;YACtC,0BAA0B,EAAE;cAC1BuC,YAAY,EAAE,CAAC;cACfvC,eAAe,EAAEjD,QAAQ,KAAK,GAAG,GAAG,SAAS,GAAGpB;YAClD;UACF;QAAE;UAAAwE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGFxE,OAAA,CAACX,GAAG;UAACuE,SAAS,EAAE9D,MAAM,CAAC4G,gBAAiB;UAAA1C,QAAA,gBACtChE,OAAA,CAACX,GAAG;YAACuE,SAAS,EAAE9D,MAAM,CAAC6G,UAAW;YAAA3C,QAAA,gBAChChE,OAAA,CAACX,GAAG;cAACuE,SAAS,EAAE9D,MAAM,CAAC8G,WAAY;cAAC9C,EAAE,EAAE;gBAAEI,eAAe,EAAE;cAAU;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1ExE,OAAA,CAACV,UAAU;cACT6E,OAAO,EAAC,SAAS;cACjBP,SAAS,EAAE9D,MAAM,CAAC+G,UAAW;cAC7B/C,EAAE,EAAE;gBACFM,UAAU,EAAE,kCAAkC;gBAC9Ce,QAAQ,EAAE,SAAS;gBACnBpC,KAAK,EAAE;cACT,CAAE;cAAAiB,QAAA,GAEDtB,cAAc,EAAC,YAClB;YAAA;cAAA2B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNxE,OAAA,CAACX,GAAG;YAACuE,SAAS,EAAE9D,MAAM,CAAC6G,UAAW;YAAA3C,QAAA,gBAChChE,OAAA,CAACX,GAAG;cAACuE,SAAS,EAAE9D,MAAM,CAAC8G,WAAY;cAAC9C,EAAE,EAAE;gBAAEI,eAAe,EAAE;cAAU;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1ExE,OAAA,CAACV,UAAU;cACT6E,OAAO,EAAC,SAAS;cACjBP,SAAS,EAAE9D,MAAM,CAAC+G,UAAW;cAC7B/C,EAAE,EAAE;gBACFM,UAAU,EAAE,kCAAkC;gBAC9Ce,QAAQ,EAAE,SAAS;gBACnBpC,KAAK,EAAE;cACT,CAAE;cAAAiB,QAAA,GAEDrB,eAAe,EAAC,cACnB;YAAA;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNxE,OAAA,CAACX,GAAG;YAACuE,SAAS,EAAE9D,MAAM,CAAC6G,UAAW;YAAA3C,QAAA,gBAChChE,OAAA,CAACX,GAAG;cAACuE,SAAS,EAAE9D,MAAM,CAAC8G,WAAY;cAAC9C,EAAE,EAAE;gBAAEI,eAAe,EAAE;cAAU;YAAE;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1ExE,OAAA,CAACV,UAAU;cACT6E,OAAO,EAAC,SAAS;cACjBP,SAAS,EAAE9D,MAAM,CAAC+G,UAAW;cAC7B/C,EAAE,EAAE;gBACFM,UAAU,EAAE,kCAAkC;gBAC9Ce,QAAQ,EAAE,SAAS;gBACnBpC,KAAK,EAAE;cACT,CAAE;cAAAiB,QAAA,GAEDpB,eAAe,EAAC,cACnB;YAAA;cAAAyB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNxE,OAAA,CAACX,GAAG;QACFuE,SAAS,EAAE9D,MAAM,CAACgH,WAAY;QAC9BhD,EAAE,EAAE;UACFiD,SAAS,EAAE,MAAM;UACjBC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,GAAG,EAAE,KAAK;UACVC,SAAS,EAAE,KAAK;UAChBC,UAAU,EAAE,KAAK;UACjBC,cAAc,EAAE,eAAe;UAC/BjD,UAAU,EAAE,kCAAkC;UAC9CkD,YAAY,EAAE;QAChB,CAAE;QAAAtD,QAAA,gBAEFhE,OAAA,CAACX,GAAG;UAACyE,EAAE,EAAE;YAAEkD,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAE,CAAE;UAAAlD,QAAA,gBACzDhE,OAAA,CAACR,MAAM;YACL+H,GAAG,EAAE3G,IAAI,CAAC4G,IAAI,CAACC,MAAM,IAAI,EAAG;YAC5BC,GAAG,EAAE,GAAG9G,IAAI,CAAC4G,IAAI,CAACG,UAAU,IAAI/G,IAAI,CAAC4G,IAAI,CAACI,SAAS,EAAG;YACtDhE,SAAS,EAAE9D,MAAM,CAAC+H,aAAc;YAChC/D,EAAE,EAAE;cAAEiB,KAAK,EAAE,MAAM;cAAEC,MAAM,EAAE;YAAO;UAAE;YAAAX,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvC,CAAC,eACFxE,OAAA,CAACX,GAAG;YAAA2E,QAAA,gBACFhE,OAAA,CAACV,UAAU;cACT6E,OAAO,EAAC,SAAS;cACjBP,SAAS,EAAE9D,MAAM,CAACgI,YAAa;cAC/BhE,EAAE,EAAE;gBACFf,KAAK,EAAE,MAAM;gBACboC,QAAQ,EAAE,QAAQ;gBAClB6B,OAAO,EAAE,OAAO;gBAChB5C,UAAU,EAAE;cACd,CAAE;cAAAJ,QAAA,EACH;YAED;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbxE,OAAA,CAACV,UAAU;cACT6E,OAAO,EAAC,SAAS;cACjBP,SAAS,EAAE9D,MAAM,CAACiI,WAAY;cAC9BjE,EAAE,EAAE;gBACFsB,UAAU,EAAE,GAAG;gBACfD,QAAQ,EAAE,QAAQ;gBAClBpC,KAAK,EAAE,MAAM;gBACbqB,UAAU,EAAE;cACd,CAAE;cAAAJ,QAAA,GAEDpD,IAAI,CAAC4G,IAAI,CAACG,UAAU,EAAC,GAAC,EAAC/G,IAAI,CAAC4G,IAAI,CAACI,SAAS;YAAA;cAAAvD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENxE,OAAA,CAACX,GAAG;UAACyE,EAAE,EAAE;YAAEkD,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAE,CAAE;UAAAlD,QAAA,GACxDpD,IAAI,CAAC4G,IAAI,CAACQ,EAAE,KAAKnH,WAAW,CAACmH,EAAE,iBAC9BhI,OAAA,CAACX,GAAG;YACFyE,EAAE,EAAE;cACFkD,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,GAAG,EAAE,KAAK;cACVhD,eAAe,EAAE,wBAAwB;cACzCnB,KAAK,EAAE,OAAO;cACdkF,OAAO,EAAE,UAAU;cACnBxB,YAAY,EAAE,MAAM;cACpBtB,QAAQ,EAAE,SAAS;cACnBC,UAAU,EAAE,GAAG;cACf8C,SAAS,EAAE,8BAA8B;cACzCC,WAAW,EAAE;YACf,CAAE;YAAAnE,QAAA,gBAEFhE,OAAA,CAACJ,OAAO;cAACkF,IAAI,EAAC,kBAAkB;cAACC,KAAK,EAAE,EAAG;cAACC,MAAM,EAAE;YAAG;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1DxE,OAAA,CAACV,UAAU;cACT6E,OAAO,EAAC,SAAS;cACjBL,EAAE,EAAE;gBACFM,UAAU,EAAE,kCAAkC;gBAC9CrB,KAAK,EAAE;cACT,CAAE;cAAAiB,QAAA,EACH;YAED;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CACN,eACDxE,OAAA,CAACV,UAAU;YACT6E,OAAO,EAAC,SAAS;YACjBP,SAAS,EAAE9D,MAAM,CAACsI,WAAY;YAC9BtE,EAAE,EAAE;cACFf,KAAK,EAAE,MAAM;cACboC,QAAQ,EAAE,QAAQ;cAClB6B,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,GAAG,EAAE,KAAK;cACV9C,UAAU,EAAE;YACd,CAAE;YAAAJ,QAAA,gBAEFhE,OAAA,CAACJ,OAAO;cAACkF,IAAI,EAAC,mBAAmB;cAACC,KAAK,EAAE,EAAG;cAACC,MAAM,EAAE;YAAG;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,EAC1D7E,KAAK,CAACiB,IAAI,CAACyH,UAAU,CAAC,CAACC,OAAO,CAAC,CAAC;UAAA;YAAAjE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA,GA1V8B1D,KAAK;IAAAuD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OA2VtC,CAAC;AAEX,CAAC;AAAC+D,EAAA,GA5fIlI,QAAQ;AA8fd,eAAeA,QAAQ;AAAC,IAAAkI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}