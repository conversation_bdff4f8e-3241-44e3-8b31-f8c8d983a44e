{"ast": null, "code": "'use strict';\n\nvar hasToStringTag = require('has-tostringtag/shams')();\nvar callBound = require('call-bound');\nvar $toString = callBound('Object.prototype.toString');\n\n/** @type {import('.')} */\nvar isStandardArguments = function isArguments(value) {\n  if (hasToStringTag && value && typeof value === 'object' && Symbol.toStringTag in value) {\n    return false;\n  }\n  return $toString(value) === '[object Arguments]';\n};\n\n/** @type {import('.')} */\nvar isLegacyArguments = function isArguments(value) {\n  if (isStandardArguments(value)) {\n    return true;\n  }\n  return value !== null && typeof value === 'object' && 'length' in value && typeof value.length === 'number' && value.length >= 0 && $toString(value) !== '[object Array]' && 'callee' in value && $toString(value.callee) === '[object Function]';\n};\nvar supportsStandardArguments = function () {\n  return isStandardArguments(arguments);\n}();\n\n// @ts-expect-error TODO make this not error\nisStandardArguments.isLegacyArguments = isLegacyArguments; // for tests\n\n/** @type {import('.')} */\nmodule.exports = supportsStandardArguments ? isStandardArguments : isLegacyArguments;", "map": {"version": 3, "names": ["hasToStringTag", "require", "callBound", "$toString", "isStandardArguments", "isArguments", "value", "Symbol", "toStringTag", "isLegacyArguments", "length", "callee", "supportsStandardArguments", "arguments", "module", "exports"], "sources": ["C:/ignition/ignition-ui/node_modules/is-arguments/index.js"], "sourcesContent": ["'use strict';\n\nvar hasToStringTag = require('has-tostringtag/shams')();\nvar callBound = require('call-bound');\n\nvar $toString = callBound('Object.prototype.toString');\n\n/** @type {import('.')} */\nvar isStandardArguments = function isArguments(value) {\n\tif (\n\t\thasToStringTag\n\t\t&& value\n\t\t&& typeof value === 'object'\n\t\t&& Symbol.toStringTag in value\n\t) {\n\t\treturn false;\n\t}\n\treturn $toString(value) === '[object Arguments]';\n};\n\n/** @type {import('.')} */\nvar isLegacyArguments = function isArguments(value) {\n\tif (isStandardArguments(value)) {\n\t\treturn true;\n\t}\n\treturn value !== null\n\t\t&& typeof value === 'object'\n\t\t&& 'length' in value\n\t\t&& typeof value.length === 'number'\n\t\t&& value.length >= 0\n\t\t&& $toString(value) !== '[object Array]'\n\t\t&& 'callee' in value\n\t\t&& $toString(value.callee) === '[object Function]';\n};\n\nvar supportsStandardArguments = (function () {\n\treturn isStandardArguments(arguments);\n}());\n\n// @ts-expect-error TODO make this not error\nisStandardArguments.isLegacyArguments = isLegacyArguments; // for tests\n\n/** @type {import('.')} */\nmodule.exports = supportsStandardArguments ? isStandardArguments : isLegacyArguments;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,cAAc,GAAGC,OAAO,CAAC,uBAAuB,CAAC,CAAC,CAAC;AACvD,IAAIC,SAAS,GAAGD,OAAO,CAAC,YAAY,CAAC;AAErC,IAAIE,SAAS,GAAGD,SAAS,CAAC,2BAA2B,CAAC;;AAEtD;AACA,IAAIE,mBAAmB,GAAG,SAASC,WAAWA,CAACC,KAAK,EAAE;EACrD,IACCN,cAAc,IACXM,KAAK,IACL,OAAOA,KAAK,KAAK,QAAQ,IACzBC,MAAM,CAACC,WAAW,IAAIF,KAAK,EAC7B;IACD,OAAO,KAAK;EACb;EACA,OAAOH,SAAS,CAACG,KAAK,CAAC,KAAK,oBAAoB;AACjD,CAAC;;AAED;AACA,IAAIG,iBAAiB,GAAG,SAASJ,WAAWA,CAACC,KAAK,EAAE;EACnD,IAAIF,mBAAmB,CAACE,KAAK,CAAC,EAAE;IAC/B,OAAO,IAAI;EACZ;EACA,OAAOA,KAAK,KAAK,IAAI,IACjB,OAAOA,KAAK,KAAK,QAAQ,IACzB,QAAQ,IAAIA,KAAK,IACjB,OAAOA,KAAK,CAACI,MAAM,KAAK,QAAQ,IAChCJ,KAAK,CAACI,MAAM,IAAI,CAAC,IACjBP,SAAS,CAACG,KAAK,CAAC,KAAK,gBAAgB,IACrC,QAAQ,IAAIA,KAAK,IACjBH,SAAS,CAACG,KAAK,CAACK,MAAM,CAAC,KAAK,mBAAmB;AACpD,CAAC;AAED,IAAIC,yBAAyB,GAAI,YAAY;EAC5C,OAAOR,mBAAmB,CAACS,SAAS,CAAC;AACtC,CAAC,CAAC,CAAE;;AAEJ;AACAT,mBAAmB,CAACK,iBAAiB,GAAGA,iBAAiB,CAAC,CAAC;;AAE3D;AACAK,MAAM,CAACC,OAAO,GAAGH,yBAAyB,GAAGR,mBAAmB,GAAGK,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}