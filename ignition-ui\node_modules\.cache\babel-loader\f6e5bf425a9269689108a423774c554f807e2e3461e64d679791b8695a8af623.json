{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nvar _ClearIcon, _ArrowDropDownIcon;\nconst _excluded = [\"aria-describedby\", \"aria-label\", \"aria-labelledby\", \"autoComplete\", \"autoHighlight\", \"autoSelect\", \"autoFocus\", \"blurOnSelect\", \"clearIcon\", \"clearOnBlur\", \"clearOnEscape\", \"clearText\", \"closeText\", \"defaultValue\", \"disableCloseOnSelect\", \"disabledItemsFocusable\", \"disableListWrap\", \"disableClearable\", \"disabled\", \"endDecorator\", \"error\", \"filterOptions\", \"filterSelectedOptions\", \"forcePopupIcon\", \"freeSolo\", \"getLimitTagsText\", \"getOptionDisabled\", \"getOptionKey\", \"getOptionLabel\", \"handleHomeEndKeys\", \"includeInputInList\", \"isOptionEqualToValue\", \"groupBy\", \"id\", \"inputValue\", \"limitTags\", \"loading\", \"loadingText\", \"multiple\", \"name\", \"noOptionsText\", \"onChange\", \"onClose\", \"onHighlightChange\", \"onInputChange\", \"onOpen\", \"open\", \"openOnFocus\", \"openText\", \"options\", \"placeholder\", \"popupIcon\", \"readOnly\", \"renderGroup\", \"renderOption\", \"renderTags\", \"required\", \"type\", \"startDecorator\", \"size\", \"color\", \"variant\", \"value\", \"component\", \"selectOnFocus\", \"slots\", \"slotProps\"],\n  _excluded2 = [\"onDelete\"],\n  _excluded3 = [\"key\"],\n  _excluded4 = [\"onBlur\", \"onFocus\", \"onMouseDown\"],\n  _excluded5 = [\"key\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { chainPropTypes, integerPropType, unstable_useForkRef as useForkRef, unstable_capitalize as capitalize } from '@mui/utils';\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport { useAutocomplete } from '@mui/base/useAutocomplete';\nimport { Popper } from '@mui/base/Popper';\nimport { useThemeProps } from '../styles';\nimport ClearIcon from '../internal/svg-icons/Close';\nimport ArrowDropDownIcon from '../internal/svg-icons/ArrowDropDown';\nimport styled from '../styles/styled';\nimport { VariantColorProvider, getChildVariantAndColor } from '../styles/variantColorInheritance';\n// slot components\nimport { StyledIconButton } from '../IconButton/IconButton';\n// default render components\nimport Chip from '../Chip';\nimport ChipDelete from '../ChipDelete';\nimport { StyledInputRoot, StyledInputHtml, StyledInputStartDecorator, StyledInputEndDecorator } from '../Input/Input';\nimport List from '../List';\nimport ListProvider from '../List/ListProvider';\nimport ListSubheader from '../ListSubheader';\nimport ListItem from '../ListItem';\nimport autocompleteClasses, { getAutocompleteUtilityClass } from './autocompleteClasses';\nimport FormControlContext from '../FormControl/FormControlContext';\nimport { StyledAutocompleteListbox } from '../AutocompleteListbox/AutocompleteListbox';\nimport { StyledAutocompleteOption } from '../AutocompleteOption/AutocompleteOption';\nimport useSlot from '../utils/useSlot';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst defaultIsActiveElementInListbox = listboxRef => listboxRef.current !== null && listboxRef.current.contains(document.activeElement);\n// @ts-ignore\nconst defaultGetOptionLabel = option => {\n  var _option$label;\n  return (_option$label = option.label) != null ? _option$label : option;\n};\nconst defaultLimitTagsText = more => `+${more}`;\nconst defaultRenderGroup = params => /*#__PURE__*/_jsxs(ListItem, {\n  nested: true,\n  children: [/*#__PURE__*/_jsx(ListSubheader, {\n    sticky: true,\n    children: params.group\n  }), /*#__PURE__*/_jsx(List, {\n    children: params.children\n  })]\n}, params.key);\nconst useUtilityClasses = ownerState => {\n  const {\n    disabled,\n    focused,\n    hasClearIcon,\n    hasPopupIcon,\n    popupOpen,\n    variant,\n    color,\n    size,\n    multiple\n  } = ownerState;\n  const slots = {\n    root: ['root', focused && 'focused', hasClearIcon && 'hasClearIcon', hasPopupIcon && 'hasPopupIcon', variant && `variant${capitalize(variant)}`, color && `color${capitalize(color)}`, size && `size${capitalize(size)}`],\n    wrapper: ['wrapper', multiple && 'multiple'],\n    input: ['input'],\n    startDecorator: ['startDecorator'],\n    endDecorator: ['endDecorator'],\n    clearIndicator: ['clearIndicator'],\n    popupIndicator: ['popupIndicator', popupOpen && 'popupIndicatorOpen', disabled && 'disabled'],\n    listbox: ['listbox'],\n    option: ['option'],\n    loading: ['loading'],\n    noOptions: ['noOptions'],\n    limitTag: ['limitTag']\n  };\n  return composeClasses(slots, getAutocompleteUtilityClass, {});\n};\nconst AutocompleteRoot = styled(StyledInputRoot, {\n  name: 'JoyAutocomplete',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(_ref4 => {\n  let {\n    ownerState\n  } = _ref4;\n  return _extends({}, ownerState.size === 'sm' && {\n    '--Autocomplete-wrapperGap': '3px'\n  }, ownerState.size === 'md' && {\n    '--Autocomplete-wrapperGap': '3px'\n  }, ownerState.size === 'lg' && {\n    '--Autocomplete-wrapperGap': '4px'\n  }, {\n    /* Avoid double tap issue on iOS */\n    '@media (pointer: fine)': {\n      [`&:hover .${autocompleteClasses.clearIndicator}`]: {\n        visibility: 'visible'\n      }\n    }\n  }, ownerState.multiple && !ownerState.startDecorator && {\n    paddingInlineStart: 0\n  });\n});\n\n/**\n * Wrapper groups the chips (multi selection) and the input\n * so that start/end decorators can stay in the normal flow.\n */\nconst AutocompleteWrapper = styled('div', {\n  name: 'JoyAutocomplete',\n  slot: 'Wrapper',\n  overridesResolver: (props, styles) => styles.wrapper\n})(_ref5 => {\n  let {\n    ownerState\n  } = _ref5;\n  return {\n    flex: 1,\n    // stretch to fill the root slot\n    minWidth: 0,\n    // won't push end decorator out of the autocomplete\n    display: 'flex',\n    alignItems: 'center',\n    flexWrap: 'wrap',\n    gap: 'var(--Autocomplete-wrapperGap)',\n    [`&.${autocompleteClasses.multiple}`]: _extends({\n      paddingBlock: 'var(--Autocomplete-wrapperGap)'\n    }, !ownerState.startDecorator && {\n      paddingInlineStart: 'var(--Autocomplete-wrapperGap)'\n    }, !ownerState.endDecorator && {\n      paddingInlineEnd: 'var(--Autocomplete-wrapperGap)'\n    })\n  };\n});\nconst AutocompleteInput = styled(StyledInputHtml, {\n  name: 'JoyAutocomplete',\n  slot: 'Input',\n  overridesResolver: (props, styles) => styles.input\n})(_ref6 => {\n  let {\n    ownerState\n  } = _ref6;\n  return _extends({\n    minWidth: 30,\n    minHeight: 'var(--Chip-minHeight)'\n  }, ownerState.multiple && {\n    marginInlineStart: 'calc(var(--Autocomplete-wrapperGap) * 2.5)'\n  });\n});\nconst AutocompleteStartDecorator = styled(StyledInputStartDecorator, {\n  name: 'JoyAutocomplete',\n  slot: 'StartDecorator',\n  overridesResolver: (props, styles) => styles.startDecorator\n})({});\nconst AutocompleteEndDecorator = styled(StyledInputEndDecorator, {\n  name: 'JoyAutocomplete',\n  slot: 'EndDecorator',\n  overridesResolver: (props, styles) => styles.endDecorator\n})(_ref7 => {\n  let {\n    ownerState\n  } = _ref7;\n  return _extends({}, (ownerState.hasClearIcon || ownerState.hasPopupIcon) && {\n    '--Button-margin': '0px',\n    '--IconButton-margin': '0px',\n    '--Icon-margin': '0px'\n  });\n});\nconst AutocompleteClearIndicator = styled(StyledIconButton, {\n  name: 'JoyAutocomplete',\n  slot: 'ClearIndicator',\n  overridesResolver: (props, styles) => styles.clearIndicator\n})(_ref8 => {\n  let {\n    ownerState\n  } = _ref8;\n  return _extends({\n    alignSelf: 'center'\n  }, !ownerState.hasPopupIcon && {\n    marginInlineEnd: 'calc(var(--Input-decoratorChildOffset) * -1)'\n  }, {\n    marginInlineStart: 'calc(var(--_Input-paddingBlock) / 2)',\n    visibility: ownerState.focused ? 'visible' : 'hidden'\n  });\n});\nconst AutocompletePopupIndicator = styled(StyledIconButton, {\n  name: 'JoyAutocomplete',\n  slot: 'PopupIndicator',\n  overridesResolver: (props, styles) => styles.popupIndicator\n})({\n  alignSelf: 'center',\n  marginInlineStart: 'calc(var(--_Input-paddingBlock) / 2)',\n  marginInlineEnd: 'calc(var(--Input-decoratorChildOffset) * -1)',\n  [`&.${autocompleteClasses.popupIndicatorOpen}`]: {\n    transform: 'rotate(180deg)',\n    '--Icon-color': 'currentColor'\n  }\n});\nconst AutocompleteListbox = styled(StyledAutocompleteListbox, {\n  name: 'JoyAutocomplete',\n  slot: 'Listbox',\n  overridesResolver: (props, styles) => styles.listbox\n})(_ref9 => {\n  let {\n    theme\n  } = _ref9;\n  return {\n    // `unstable_popup-zIndex` is a private variable that lets other component, for example Modal, to override the z-index so that the listbox can be displayed above the Modal.\n    zIndex: `var(--unstable_popup-zIndex, ${theme.vars.zIndex.popup})`\n  };\n});\nconst AutocompleteOption = styled(StyledAutocompleteOption, {\n  name: 'JoyAutocomplete',\n  slot: 'Option',\n  overridesResolver: (props, styles) => styles.option\n})({});\nconst AutocompleteLoading = styled(ListItem, {\n  name: 'JoyAutocomplete',\n  slot: 'Loading',\n  overridesResolver: (props, styles) => styles.loading\n})(_ref10 => {\n  let {\n    theme\n  } = _ref10;\n  return {\n    color: (theme.vars || theme).palette.text.secondary\n  };\n});\nconst AutocompleteNoOptions = styled(ListItem, {\n  name: 'JoyAutocomplete',\n  slot: 'NoOptions',\n  overridesResolver: (props, styles) => styles.noOptions\n})(_ref11 => {\n  let {\n    theme\n  } = _ref11;\n  return {\n    color: (theme.vars || theme).palette.text.secondary\n  };\n});\nconst AutocompleteLimitTag = styled('div', {\n  name: 'JoyAutocomplete',\n  slot: 'NoOptions',\n  overridesResolver: (props, styles) => styles.noOptions\n})({\n  marginInlineStart: 'calc(var(--Input-paddingInline) / 2)',\n  marginBlockStart: 'var(--_Input-paddingBlock)'\n});\n/**\n *\n * Demos:\n *\n * - [Autocomplete](https://mui.com/joy-ui/react-autocomplete/)\n *\n * API:\n *\n * - [Autocomplete API](https://mui.com/joy-ui/api/autocomplete/)\n */\nconst Autocomplete = /*#__PURE__*/React.forwardRef(function Autocomplete(inProps, ref) {\n  var _ref, _inProps$error, _ref2, _inProps$size, _inProps$color, _formControl$color, _ref3;\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyAutocomplete'\n  });\n  const {\n      'aria-describedby': ariaDescribedby,\n      'aria-label': ariaLabel,\n      'aria-labelledby': ariaLabelledby,\n      autoFocus,\n      clearIcon = _ClearIcon || (_ClearIcon = /*#__PURE__*/_jsx(ClearIcon, {\n        fontSize: \"md\"\n      })),\n      clearText = 'Clear',\n      closeText = 'Close',\n      disableClearable = false,\n      disabled: disabledProp,\n      endDecorator,\n      error: errorProp = false,\n      forcePopupIcon = 'auto',\n      freeSolo = false,\n      getLimitTagsText = defaultLimitTagsText,\n      getOptionLabel = defaultGetOptionLabel,\n      groupBy,\n      id,\n      limitTags = -1,\n      loading = false,\n      loadingText = 'Loading…',\n      multiple = false,\n      name,\n      noOptionsText = 'No options',\n      openText = 'Open',\n      placeholder,\n      popupIcon = _ArrowDropDownIcon || (_ArrowDropDownIcon = /*#__PURE__*/_jsx(ArrowDropDownIcon, {})),\n      readOnly = false,\n      renderGroup = defaultRenderGroup,\n      renderOption: renderOptionProp,\n      renderTags,\n      required,\n      type,\n      startDecorator,\n      size: sizeProp = 'md',\n      color: colorProp = 'neutral',\n      variant = 'outlined',\n      component,\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const formControl = React.useContext(FormControlContext);\n  const error = (_ref = (_inProps$error = inProps.error) != null ? _inProps$error : formControl == null ? void 0 : formControl.error) != null ? _ref : errorProp;\n  const size = (_ref2 = (_inProps$size = inProps.size) != null ? _inProps$size : formControl == null ? void 0 : formControl.size) != null ? _ref2 : sizeProp;\n  const color = (_inProps$color = inProps.color) != null ? _inProps$color : error ? 'danger' : (_formControl$color = formControl == null ? void 0 : formControl.color) != null ? _formControl$color : colorProp;\n  const disabled = (_ref3 = disabledProp != null ? disabledProp : formControl == null ? void 0 : formControl.disabled) != null ? _ref3 : false;\n  const {\n    getRootProps,\n    getInputProps,\n    getPopupIndicatorProps,\n    getClearProps,\n    getTagProps,\n    getListboxProps,\n    getOptionProps,\n    value,\n    dirty,\n    popupOpen,\n    focused,\n    focusedTag,\n    anchorEl,\n    setAnchorEl,\n    inputValue,\n    groupedOptions\n  } = useAutocomplete(_extends({}, props, {\n    id: id != null ? id : formControl == null ? void 0 : formControl.htmlFor,\n    componentName: 'Autocomplete',\n    unstable_classNamePrefix: 'Mui',\n    unstable_isActiveElementInListbox: defaultIsActiveElementInListbox\n  }));\n  const {\n    onMouseDown: handleInputMouseDown\n  } = getInputProps();\n  const {\n    onClick: handleRootOnClick\n  } = getRootProps();\n  const hasClearIcon = !disableClearable && !disabled && dirty && !readOnly;\n  const hasPopupIcon = (!freeSolo || forcePopupIcon === true) && forcePopupIcon !== false;\n\n  // If you modify this, make sure to keep the `AutocompleteOwnerState` type in sync.\n  const ownerState = _extends({\n    instanceColor: inProps.color\n  }, props, {\n    value,\n    disabled,\n    focused,\n    getOptionLabel,\n    hasOptions: !!groupedOptions.length,\n    hasClearIcon,\n    hasPopupIcon,\n    inputFocused: focusedTag === -1,\n    popupOpen,\n    size,\n    color,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  let selectedOptions;\n  if (multiple && value.length > 0) {\n    const getCustomizedTagProps = params => {\n      const _getTagProps = getTagProps(params),\n        {\n          onDelete\n        } = _getTagProps,\n        tagProps = _objectWithoutPropertiesLoose(_getTagProps, _excluded2);\n      return _extends({\n        disabled,\n        size,\n        onClick: onDelete\n      }, tagProps);\n    };\n    if (renderTags) {\n      selectedOptions = renderTags(value, getCustomizedTagProps, ownerState);\n    } else {\n      selectedOptions = value.map((option, index) => {\n        const _getCustomizedTagProp = getCustomizedTagProps({\n            index\n          }),\n          {\n            key: endDecoratorKey\n          } = _getCustomizedTagProp,\n          endDecoratorProps = _objectWithoutPropertiesLoose(_getCustomizedTagProp, _excluded3);\n        return /*#__PURE__*/_jsx(Chip, {\n          size: size,\n          variant: \"soft\",\n          color: \"neutral\",\n          endDecorator: /*#__PURE__*/_jsx(ChipDelete, _extends({}, endDecoratorProps), endDecoratorKey),\n          sx: {\n            minWidth: 0\n          },\n          children: getOptionLabel(option)\n        }, index);\n      });\n    }\n  }\n  const rootRef = useForkRef(ref, setAnchorEl);\n  const rootStateClasses = {\n    [autocompleteClasses.disabled]: disabled,\n    [autocompleteClasses.error]: error,\n    [autocompleteClasses.focused]: focused,\n    [autocompleteClasses.formControl]: Boolean(formControl)\n  };\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref: rootRef,\n    className: [classes.root, rootStateClasses],\n    elementType: AutocompleteRoot,\n    externalForwardedProps,\n    ownerState,\n    getSlotProps: getRootProps,\n    additionalProps: {\n      onClick: event => {\n        if (handleRootOnClick) {\n          handleRootOnClick(event);\n        }\n        if (event.currentTarget === event.target && handleInputMouseDown) {\n          handleInputMouseDown(event);\n        }\n      }\n    }\n  });\n  const [SlotWrapper, wrapperProps] = useSlot('wrapper', {\n    className: classes.wrapper,\n    elementType: AutocompleteWrapper,\n    externalForwardedProps,\n    ownerState\n  });\n  const inputStateClasses = {\n    [autocompleteClasses.disabled]: disabled\n  };\n  const [SlotInput, inputProps] = useSlot('input', {\n    className: [classes.input, inputStateClasses],\n    elementType: AutocompleteInput,\n    getSlotProps: handlers => {\n      const _getInputProps = getInputProps(),\n        {\n          onBlur,\n          onFocus,\n          onMouseDown\n        } = _getInputProps,\n        inputSlotProps = _objectWithoutPropertiesLoose(_getInputProps, _excluded4);\n      return _extends({}, inputSlotProps, {\n        onBlur: event => {\n          var _handlers$onBlur;\n          onBlur == null || onBlur(event);\n          (_handlers$onBlur = handlers.onBlur) == null || _handlers$onBlur.call(handlers, event);\n        },\n        onFocus: event => {\n          var _handlers$onFocus;\n          onFocus == null || onFocus(event);\n          (_handlers$onFocus = handlers.onFocus) == null || _handlers$onFocus.call(handlers, event);\n        },\n        onMouseDown: event => {\n          var _handlers$onMouseDown;\n          onMouseDown == null || onMouseDown(event);\n          (_handlers$onMouseDown = handlers.onMouseDown) == null || _handlers$onMouseDown.call(handlers, event);\n        }\n      });\n    },\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      autoFocus,\n      placeholder,\n      name,\n      readOnly,\n      disabled,\n      required: required != null ? required : formControl == null ? void 0 : formControl.required,\n      type,\n      'aria-invalid': error || undefined,\n      'aria-label': ariaLabel,\n      'aria-labelledby': ariaLabelledby,\n      'aria-describedby': ariaDescribedby != null ? ariaDescribedby : formControl == null ? void 0 : formControl['aria-describedby']\n    }\n  });\n  const [SlotStartDecorator, startDecoratorProps] = useSlot('startDecorator', {\n    className: classes.startDecorator,\n    elementType: AutocompleteStartDecorator,\n    externalForwardedProps,\n    ownerState\n  });\n  const [SlotEndDecorator, endDecoratorProps] = useSlot('endDecorator', {\n    className: classes.endDecorator,\n    elementType: AutocompleteEndDecorator,\n    externalForwardedProps,\n    ownerState\n  });\n  const [SlotClearIndicator, clearIndicatorProps] = useSlot('clearIndicator', {\n    className: classes.clearIndicator,\n    elementType: AutocompleteClearIndicator,\n    getSlotProps: getClearProps,\n    externalForwardedProps,\n    ownerState,\n    getSlotOwnerState: mergedProps => ({\n      size: mergedProps.size || size,\n      variant: mergedProps.variant || getChildVariantAndColor(variant, color).variant || 'plain',\n      color: mergedProps.color || getChildVariantAndColor(variant, color).color || 'neutral',\n      disableColorInversion: !!inProps.color\n    }),\n    additionalProps: {\n      'aria-label': clearText,\n      title: clearText\n    }\n  });\n  const [SlotPopupIndicator, popupIndicatorProps] = useSlot('popupIndicator', {\n    className: classes.popupIndicator,\n    elementType: AutocompletePopupIndicator,\n    getSlotProps: getPopupIndicatorProps,\n    externalForwardedProps,\n    ownerState,\n    getSlotOwnerState: mergedProps => ({\n      size: mergedProps.size || size,\n      variant: mergedProps.variant || getChildVariantAndColor(variant, color).variant || 'plain',\n      color: mergedProps.color || getChildVariantAndColor(variant, color).color || 'neutral',\n      disableColorInversion: !!inProps.color\n    }),\n    additionalProps: {\n      disabled,\n      'aria-label': popupOpen ? closeText : openText,\n      title: popupOpen ? closeText : openText,\n      type: 'button'\n    }\n  });\n  const [SlotListbox, listboxProps] = useSlot('listbox', {\n    className: classes.listbox,\n    elementType: AutocompleteListbox,\n    getSlotProps: getListboxProps,\n    externalForwardedProps,\n    ownerState,\n    getSlotOwnerState: mergedProps => ({\n      size: mergedProps.size || size,\n      variant: mergedProps.variant || variant,\n      color: mergedProps.color || color,\n      disableColorInversion: !mergedProps.disablePortal\n    }),\n    additionalProps: {\n      anchorEl,\n      open: popupOpen,\n      style: anchorEl ? {\n        width: anchorEl.clientWidth\n      } : {}\n    }\n  });\n  const [SlotLoading, loadingProps] = useSlot('loading', {\n    className: classes.loading,\n    elementType: AutocompleteLoading,\n    externalForwardedProps,\n    ownerState\n  });\n  const [SlotNoOptions, noOptionsProps] = useSlot('noOptions', {\n    className: classes.noOptions,\n    elementType: AutocompleteNoOptions,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      role: 'presentation',\n      onMouseDown: event => {\n        // Prevent input blur when interacting with the \"no options\" content\n        event.preventDefault();\n      }\n    }\n  });\n  const [SlotLimitTag, limitTagProps] = useSlot('limitTag', {\n    className: classes.limitTag,\n    elementType: AutocompleteLimitTag,\n    externalForwardedProps,\n    ownerState\n  });\n  if (limitTags > -1 && Array.isArray(selectedOptions)) {\n    const more = selectedOptions.length - limitTags;\n    if (!focused && more > 0) {\n      selectedOptions = selectedOptions.splice(0, limitTags);\n      selectedOptions.push(/*#__PURE__*/_jsx(SlotLimitTag, _extends({}, limitTagProps, {\n        children: getLimitTagsText(more)\n      }), selectedOptions.length));\n    }\n  }\n  const [SlotOption, baseOptionProps] = useSlot('option', {\n    className: classes.option,\n    elementType: AutocompleteOption,\n    externalForwardedProps,\n    ownerState,\n    getSlotOwnerState: mergedProps => ({\n      variant: mergedProps.variant || getChildVariantAndColor(variant, color).variant || 'plain',\n      color: mergedProps.color || getChildVariantAndColor(variant, color).color || 'neutral',\n      disableColorInversion: !listboxProps.disablePortal\n    }),\n    additionalProps: {\n      as: 'li'\n    }\n  });\n  const defaultRenderOption = (optionProps, option) => {\n    const {\n        key\n      } = optionProps,\n      rest = _objectWithoutPropertiesLoose(optionProps, _excluded5);\n    return /*#__PURE__*/_jsx(SlotOption, _extends({}, rest, {\n      children: getOptionLabel(option)\n    }), key);\n  };\n  const renderOption = renderOptionProp || defaultRenderOption;\n  const renderListOption = (option, index) => {\n    const optionProps = getOptionProps({\n      option,\n      index\n    });\n    return renderOption(_extends({}, baseOptionProps, optionProps), option, {\n      // `aria-selected` prop will always by boolean, see useAutocomplete hook.\n      selected: !!optionProps['aria-selected'],\n      inputValue,\n      ownerState\n    });\n  };\n\n  // Wait for `listboxProps` because `slotProps.listbox` could be a function.\n  const modifiers = React.useMemo(() => [{\n    name: 'offset',\n    options: {\n      offset: [0, 4]\n    }\n  }, ...(listboxProps.modifiers || [])], [listboxProps.modifiers]);\n  let popup = null;\n  if (anchorEl) {\n    var _props$slots;\n    popup = /*#__PURE__*/_jsx(VariantColorProvider, {\n      variant: variant,\n      color: color,\n      children: /*#__PURE__*/_jsx(ListProvider, {\n        nested: true,\n        children: /*#__PURE__*/_jsxs(SlotListbox, _extends({}, listboxProps, {\n          className: clsx(listboxProps.className)\n          // @ts-ignore internal logic (too complex to typed PopperOwnProps to SlotListbox but this should be removed when we have `usePopper`)\n          ,\n\n          modifiers: modifiers\n        }, !((_props$slots = props.slots) != null && _props$slots.listbox) && {\n          as: Popper,\n          slots: {\n            root: listboxProps.as || 'ul'\n          }\n        }, {\n          children: [groupedOptions.map((option, index) => {\n            if (groupBy) {\n              const typedOption = option;\n              return renderGroup({\n                key: String(typedOption.key),\n                group: typedOption.group,\n                children: typedOption.options.map((option2, index2) => renderListOption(option2, typedOption.index + index2))\n              });\n            }\n            return renderListOption(option, index);\n          }), loading && groupedOptions.length === 0 ? /*#__PURE__*/_jsx(SlotLoading, _extends({}, loadingProps, {\n            children: loadingText\n          })) : null, groupedOptions.length === 0 && !freeSolo && !loading ? /*#__PURE__*/_jsx(SlotNoOptions, _extends({}, noOptionsProps, {\n            children: noOptionsText\n          })) : null]\n        }))\n      })\n    });\n  }\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsxs(SlotRoot, _extends({}, rootProps, {\n      children: [startDecorator && /*#__PURE__*/_jsx(SlotStartDecorator, _extends({}, startDecoratorProps, {\n        children: startDecorator\n      })), /*#__PURE__*/_jsxs(SlotWrapper, _extends({}, wrapperProps, {\n        children: [selectedOptions, /*#__PURE__*/_jsx(SlotInput, _extends({}, inputProps))]\n      })), endDecorator && /*#__PURE__*/_jsx(SlotEndDecorator, _extends({}, endDecoratorProps, {\n        children: endDecorator\n      })), hasClearIcon ? /*#__PURE__*/_jsx(SlotClearIndicator, _extends({}, clearIndicatorProps, {\n        children: clearIcon\n      })) : null, hasPopupIcon ? /*#__PURE__*/_jsx(SlotPopupIndicator, _extends({}, popupIndicatorProps, {\n        children: popupIcon\n      })) : null]\n    })), popup]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Autocomplete.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Identifies the element (or elements) that describes the object.\n   * @see aria-labelledby\n   */\n  'aria-describedby': PropTypes.string,\n  /**\n   * Defines a string value that labels the current element.\n   * @see aria-labelledby.\n   */\n  'aria-label': PropTypes.string,\n  /**\n   * Identifies the element (or elements) that labels the current element.\n   * @see aria-describedby.\n   */\n  'aria-labelledby': PropTypes.string,\n  /**\n   * If `true`, the portion of the selected suggestion that the user hasn't typed,\n   * known as the completion string, appears inline after the input cursor in the textbox.\n   * The inline completion string is visually highlighted and has a selected state.\n   * @default false\n   */\n  autoComplete: PropTypes.bool,\n  /**\n   * If `true`, the `input` element is focused during the first mount.\n   */\n  autoFocus: PropTypes.bool,\n  /**\n   * If `true`, the first option is automatically highlighted.\n   * @default false\n   */\n  autoHighlight: PropTypes.bool,\n  /**\n   * If `true`, the selected option becomes the value of the input\n   * when the Autocomplete loses focus unless the user chooses\n   * a different option or changes the character string in the input.\n   *\n   * When using the `freeSolo` mode, the typed value will be the input value\n   * if the Autocomplete loses focus without highlighting an option.\n   * @default false\n   */\n  autoSelect: PropTypes.bool,\n  /**\n   * Control if the input should be blurred when an option is selected:\n   *\n   * - `false` the input is not blurred.\n   * - `true` the input is always blurred.\n   * - `touch` the input is blurred after a touch event.\n   * - `mouse` the input is blurred after a mouse event.\n   * @default false\n   */\n  blurOnSelect: PropTypes.oneOfType([PropTypes.oneOf(['mouse', 'touch']), PropTypes.bool]),\n  /**\n   * The icon to display in place of the default clear icon.\n   * @default <ClearIcon fontSize=\"md\" />\n   */\n  clearIcon: PropTypes.node,\n  /**\n   * If `true`, the input's text is cleared on blur if no value is selected.\n   *\n   * Set it to `true` if you want to help the user enter a new value.\n   * Set it to `false` if you want to help the user resume their search.\n   * @default !props.freeSolo\n   */\n  clearOnBlur: PropTypes.bool,\n  /**\n   * If `true`, clear all values when the user presses escape and the popup is closed.\n   * @default false\n   */\n  clearOnEscape: PropTypes.bool,\n  /**\n   * Override the default text for the *clear* icon button.\n   *\n   * For localization purposes, you can use the provided [translations](/material-ui/guides/localization/).\n   * @default 'Clear'\n   */\n  clearText: PropTypes.string,\n  /**\n   * Override the default text for the *close popup* icon button.\n   *\n   * For localization purposes, you can use the provided [translations](/material-ui/guides/localization/).\n   * @default 'Close'\n   */\n  closeText: PropTypes.string,\n  /**\n   * The color of the component. It supports those theme colors that make sense for this component.\n   * @default 'neutral'\n   */\n  color: PropTypes.oneOf(['danger', 'neutral', 'primary', 'success', 'warning']),\n  /**\n   * The default value. Use when the component is not controlled.\n   * @default props.multiple ? [] : null\n   */\n  defaultValue: chainPropTypes(PropTypes.any, props => {\n    if (props.multiple && props.defaultValue !== undefined && !Array.isArray(props.defaultValue)) {\n      return new Error(['MUI: The Autocomplete expects the `defaultValue` prop to be an array when `multiple={true}` or undefined.', `However, ${props.defaultValue} was provided.`].join('\\n'));\n    }\n    return null;\n  }),\n  /**\n   * If `true`, the input can't be cleared.\n   * @default false\n   */\n  disableClearable: PropTypes.bool,\n  /**\n   * If `true`, the popup won't close when a value is selected.\n   * @default false\n   */\n  disableCloseOnSelect: PropTypes.bool,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, will allow focus on disabled items.\n   * @default false\n   */\n  disabledItemsFocusable: PropTypes.bool,\n  /**\n   * If `true`, the list box in the popup will not wrap focus.\n   * @default false\n   */\n  disableListWrap: PropTypes.bool,\n  /**\n   * Trailing adornment for this input.\n   */\n  endDecorator: PropTypes.node,\n  /**\n   * If `true`, the `input` will indicate an error.\n   * The prop defaults to the value (`false`) inherited from the parent FormControl component.\n   * @default false\n   */\n  error: PropTypes.bool,\n  /**\n   * A function that determines the filtered options to be rendered on search.\n   *\n   * @default createFilterOptions()\n   * @param {Value[]} options The options to render.\n   * @param {object} state The state of the component.\n   * @returns {Value[]}\n   */\n  filterOptions: PropTypes.func,\n  /**\n   * If `true`, hide the selected options from the list box.\n   * @default false\n   */\n  filterSelectedOptions: PropTypes.bool,\n  /**\n   * Force the visibility display of the popup icon.\n   * @default 'auto'\n   */\n  forcePopupIcon: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.bool]),\n  /**\n   * If `true`, the Autocomplete is free solo, meaning that the user input is not bound to provided options.\n   * @default false\n   */\n  freeSolo: PropTypes.bool,\n  /**\n   * The label to display when the tags are truncated (`limitTags`).\n   *\n   * @param {string | number} more The number of truncated tags.\n   * @returns {ReactNode}\n   * @default (more: string | number) => `+${more}`\n   */\n  getLimitTagsText: PropTypes.func,\n  /**\n   * Used to determine the disabled state for a given option.\n   *\n   * @param {Value} option The option to test.\n   * @returns {boolean}\n   */\n  getOptionDisabled: PropTypes.func,\n  /**\n   * Used to determine the key for a given option.\n   * This can be useful when the labels of options are not unique (since labels are used as keys by default).\n   *\n   * @param {Value} option The option to get the key for.\n   * @returns {string | number}\n   */\n  getOptionKey: PropTypes.func,\n  /**\n   * Used to determine the string value for a given option.\n   * It's used to fill the input (and the list box options if `renderOption` is not provided).\n   *\n   * If used in free solo mode, it must accept both the type of the options and a string.\n   *\n   * @param {Value} option\n   * @returns {string}\n   * @default (option) => option.label ?? option\n   */\n  getOptionLabel: PropTypes.func,\n  /**\n   * If provided, the options will be grouped under the returned string.\n   * The groupBy value is also used as the text for group headings when `renderGroup` is not provided.\n   *\n   * @param {Value} options The options to group.\n   * @returns {string}\n   */\n  groupBy: PropTypes.func,\n  /**\n   * If `true`, the component handles the \"Home\" and \"End\" keys when the popup is open.\n   * It should move focus to the first option and last option, respectively.\n   * @default !props.freeSolo\n   */\n  handleHomeEndKeys: PropTypes.bool,\n  /**\n   * This prop is used to help implement the accessibility logic.\n   * If you don't provide an id it will fall back to a randomly generated one.\n   */\n  id: PropTypes.string,\n  /**\n   * If `true`, the highlight can move to the input.\n   * @default false\n   */\n  includeInputInList: PropTypes.bool,\n  /**\n   * The input value.\n   */\n  inputValue: PropTypes.string,\n  /**\n   * Used to determine if the option represents the given value.\n   * Uses strict equality by default.\n   * ⚠️ Both arguments need to be handled, an option can only match with one value.\n   *\n   * @param {Value} option The option to test.\n   * @param {Value} value The value to test against.\n   * @returns {boolean}\n   */\n  isOptionEqualToValue: PropTypes.func,\n  /**\n   * The maximum number of tags that will be visible when not focused.\n   * Set `-1` to disable the limit.\n   * @default -1\n   */\n  limitTags: integerPropType,\n  /**\n   * If `true`, the component is in a loading state.\n   * This shows the `loadingText` in place of suggestions (only if there are no suggestions to show, for example `options` are empty).\n   * @default false\n   */\n  loading: PropTypes.bool,\n  /**\n   * Text to display when in a loading state.\n   *\n   * For localization purposes, you can use the provided [translations](/material-ui/guides/localization/).\n   * @default 'Loading…'\n   */\n  loadingText: PropTypes.node,\n  /**\n   * If `true`, `value` must be an array and the menu will support multiple selections.\n   * @default false\n   */\n  multiple: PropTypes.bool,\n  /**\n   * Name attribute of the `input` element.\n   */\n  name: PropTypes.string,\n  /**\n   * Text to display when there are no options.\n   *\n   * For localization purposes, you can use the provided [translations](/material-ui/guides/localization/).\n   * @default 'No options'\n   */\n  noOptionsText: PropTypes.node,\n  /**\n   * Callback fired when the value changes.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {Value|Value[]} value The new value of the component.\n   * @param {string} reason One of \"createOption\", \"selectOption\", \"removeOption\", \"blur\" or \"clear\".\n   * @param {string} [details]\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback fired when the popup requests to be closed.\n   * Use in controlled mode (see open).\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {string} reason Can be: `\"toggleInput\"`, `\"escape\"`, `\"selectOption\"`, `\"removeOption\"`, `\"blur\"`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * Callback fired when the highlight option changes.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {Value} option The highlighted option.\n   * @param {string} reason Can be: `\"keyboard\"`, `\"auto\"`, `\"mouse\"`, `\"touch\"`.\n   */\n  onHighlightChange: PropTypes.func,\n  /**\n   * Callback fired when the input value changes.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {string} value The new value of the text input.\n   * @param {string} reason Can be: `\"input\"` (user input), `\"reset\"` (programmatic change), `\"clear\"`.\n   */\n  onInputChange: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyDown: PropTypes.func,\n  /**\n   * Callback fired when the popup requests to be opened.\n   * Use in controlled mode (see open).\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   */\n  onOpen: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool,\n  /**\n   * If `true`, the popup will open on input focus.\n   * @default false\n   */\n  openOnFocus: PropTypes.bool,\n  /**\n   * Override the default text for the *open popup* icon button.\n   *\n   * For localization purposes, you can use the provided [translations](/material-ui/guides/localization/).\n   * @default 'Open'\n   */\n  openText: PropTypes.string,\n  /**\n   * Array of options.\n   */\n  options: PropTypes.array.isRequired,\n  /**\n   * The input placeholder\n   */\n  placeholder: PropTypes.string,\n  /**\n   * The icon to display in place of the default popup icon.\n   * @default <ArrowDropDownIcon />\n   */\n  popupIcon: PropTypes.node,\n  /**\n   * If `true`, the component becomes readonly. It is also supported for multiple tags where the tag cannot be deleted.\n   * @default false\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * Render the group.\n   *\n   * @param {AutocompleteRenderGroupParams} params The group to render.\n   * @returns {ReactNode}\n   */\n  renderGroup: PropTypes.func,\n  /**\n   * Render the option, use `getOptionLabel` by default.\n   *\n   * @param {object} props The props to apply on the li element.\n   * @param {T} option The option to render.\n   * @param {object} state The state of the component.\n   * @returns {ReactNode}\n   */\n  renderOption: PropTypes.func,\n  /**\n   * Render the selected value.\n   *\n   * @param {T[]} value The `value` provided to the component.\n   * @param {function} getTagProps A tag props getter.\n   * @param {object} ownerState The state of the Autocomplete component.\n   * @returns {ReactNode}\n   */\n  renderTags: PropTypes.func,\n  /**\n   * If `true`, the `input` element is required.\n   * The prop defaults to the value (`false`) inherited from the parent FormControl component.\n   */\n  required: PropTypes.bool,\n  /**\n   * If `true`, the input's text is selected on focus.\n   * It helps the user clear the selected value.\n   * @default !props.freeSolo\n   */\n  selectOnFocus: PropTypes.bool,\n  /**\n   * The size of the component.\n   * @default 'md'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['sm', 'md', 'lg']), PropTypes.string]),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    clearIndicator: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    endDecorator: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    input: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    limitTag: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    listbox: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    loading: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    noOptions: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    option: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    popupIndicator: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    startDecorator: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    wrapper: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    clearIndicator: PropTypes.elementType,\n    endDecorator: PropTypes.elementType,\n    input: PropTypes.elementType,\n    limitTag: PropTypes.elementType,\n    listbox: PropTypes.elementType,\n    loading: PropTypes.elementType,\n    noOptions: PropTypes.elementType,\n    option: PropTypes.elementType,\n    popupIndicator: PropTypes.elementType,\n    root: PropTypes.elementType,\n    startDecorator: PropTypes.elementType,\n    wrapper: PropTypes.elementType\n  }),\n  /**\n   * Leading adornment for this input.\n   */\n  startDecorator: PropTypes.node,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Type of the `input` element. It should be [a valid HTML5 input type](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#Form_%3Cinput%3E_types).\n   */\n  type: PropTypes.string,\n  /**\n   * The value of the autocomplete.\n   *\n   * The value must have reference equality with the option in order to be selected.\n   * You can customize the equality behavior with the `isOptionEqualToValue` prop.\n   */\n  value: chainPropTypes(PropTypes.any, props => {\n    if (props.multiple && props.value !== undefined && !Array.isArray(props.value)) {\n      return new Error(['MUI: The Autocomplete expects the `value` prop to be an array when `multiple={true}` or undefined.', `However, ${props.value} was provided.`].join('\\n'));\n    }\n    return null;\n  }),\n  /**\n   * The [global variant](https://mui.com/joy-ui/main-features/global-variants/) to use.\n   * @default 'outlined'\n   */\n  variant: PropTypes.oneOf(['outlined', 'plain', 'soft', 'solid'])\n} : void 0;\nexport default Autocomplete;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_ClearIcon", "_ArrowDropDownIcon", "_excluded", "_excluded2", "_excluded3", "_excluded4", "_excluded5", "React", "PropTypes", "clsx", "chainPropTypes", "integerPropType", "unstable_useForkRef", "useForkRef", "unstable_capitalize", "capitalize", "unstable_composeClasses", "composeClasses", "useAutocomplete", "<PERSON><PERSON>", "useThemeProps", "ClearIcon", "ArrowDropDownIcon", "styled", "VariantColorProvider", "getChildVariantAndColor", "StyledIconButton", "Chip", "ChipDelete", "StyledInputRoot", "StyledInputHtml", "StyledInputStartDecorator", "StyledInputEndDecorator", "List", "ListProvider", "ListSubheader", "ListItem", "autocompleteClasses", "getAutocompleteUtilityClass", "FormControlContext", "StyledAutocompleteListbox", "StyledAutocompleteOption", "useSlot", "jsx", "_jsx", "jsxs", "_jsxs", "defaultIsActiveElementInListbox", "listboxRef", "current", "contains", "document", "activeElement", "defaultGetOptionLabel", "option", "_option$label", "label", "defaultLimitTagsText", "more", "defaultRenderGroup", "params", "nested", "children", "sticky", "group", "key", "useUtilityClasses", "ownerState", "disabled", "focused", "hasClearIcon", "hasPopupIcon", "popupOpen", "variant", "color", "size", "multiple", "slots", "root", "wrapper", "input", "startDecorator", "endDecorator", "clearIndicator", "popupIndicator", "listbox", "loading", "noOptions", "limitTag", "AutocompleteRoot", "name", "slot", "overridesResolver", "props", "styles", "_ref4", "visibility", "paddingInlineStart", "AutocompleteWrapper", "_ref5", "flex", "min<PERSON><PERSON><PERSON>", "display", "alignItems", "flexWrap", "gap", "paddingBlock", "paddingInlineEnd", "AutocompleteInput", "_ref6", "minHeight", "marginInlineStart", "AutocompleteStartDecorator", "AutocompleteEndDecorator", "_ref7", "AutocompleteClearIndicator", "_ref8", "alignSelf", "marginInlineEnd", "AutocompletePopupIndicator", "popupIndicatorOpen", "transform", "AutocompleteListbox", "_ref9", "theme", "zIndex", "vars", "popup", "AutocompleteOption", "AutocompleteLoading", "_ref10", "palette", "text", "secondary", "AutocompleteNoOptions", "_ref11", "AutocompleteLimitTag", "marginBlockStart", "Autocomplete", "forwardRef", "inProps", "ref", "_ref", "_inProps$error", "_ref2", "_inProps$size", "_inProps$color", "_formControl$color", "_ref3", "aria<PERSON><PERSON><PERSON><PERSON>", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "autoFocus", "clearIcon", "fontSize", "clearText", "closeText", "disableClearable", "disabledProp", "error", "errorProp", "forcePopupIcon", "freeSolo", "getLimitTagsText", "getOptionLabel", "groupBy", "id", "limitTags", "loadingText", "noOptionsText", "openText", "placeholder", "popupIcon", "readOnly", "renderGroup", "renderOption", "renderOptionProp", "renderTags", "required", "type", "sizeProp", "colorProp", "component", "slotProps", "other", "formControl", "useContext", "getRootProps", "getInputProps", "getPopupIndicatorProps", "getClearProps", "getTagProps", "getListboxProps", "getOptionProps", "value", "dirty", "focusedTag", "anchorEl", "setAnchorEl", "inputValue", "groupedOptions", "htmlFor", "componentName", "unstable_classNamePrefix", "unstable_isActiveElementInListbox", "onMouseDown", "handleInputMouseDown", "onClick", "handleRootOnClick", "instanceColor", "hasOptions", "length", "inputFocused", "classes", "externalForwardedProps", "selectedOptions", "getCustomizedTagProps", "_getTagProps", "onDelete", "tagProps", "map", "index", "_getCustomizedTagProp", "endDecoratorKey", "endDecoratorProps", "sx", "rootRef", "rootStateClasses", "Boolean", "SlotRoot", "rootProps", "className", "elementType", "getSlotProps", "additionalProps", "event", "currentTarget", "target", "SlotWrapper", "wrapperProps", "inputStateClasses", "SlotInput", "inputProps", "handlers", "_getInputProps", "onBlur", "onFocus", "inputSlotProps", "_handlers$onBlur", "call", "_handlers$onFocus", "_handlers$onMouseDown", "undefined", "SlotStartDecorator", "startDecoratorProps", "SlotEndDecorator", "SlotClearIndicator", "clearIndicatorProps", "getSlotOwnerState", "mergedProps", "disableColorInversion", "title", "SlotPopupIndicator", "popupIndicatorProps", "SlotListbox", "listboxProps", "disable<PERSON><PERSON><PERSON>", "open", "style", "width", "clientWidth", "SlotLoading", "loadingProps", "SlotNoOptions", "noOptionsProps", "role", "preventDefault", "SlotLimitTag", "limitTagProps", "Array", "isArray", "splice", "push", "SlotOption", "baseOptionProps", "as", "defaultRenderOption", "optionProps", "rest", "renderListOption", "selected", "modifiers", "useMemo", "options", "offset", "_props$slots", "typedOption", "String", "option2", "index2", "Fragment", "process", "env", "NODE_ENV", "propTypes", "string", "autoComplete", "bool", "autoHighlight", "autoSelect", "blurOnSelect", "oneOfType", "oneOf", "node", "clearOnBlur", "clearOnEscape", "defaultValue", "any", "Error", "join", "disableCloseOnSelect", "disabledItemsFocusable", "disableListWrap", "filterOptions", "func", "filterSelectedOptions", "getOptionDisabled", "getOption<PERSON>ey", "handleHomeEndKeys", "includeInputInList", "isOptionEqualToValue", "onChange", "onClose", "onHighlightChange", "onInputChange", "onKeyDown", "onOpen", "openOnFocus", "array", "isRequired", "selectOnFocus", "shape", "object", "arrayOf"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Autocomplete/Autocomplete.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nvar _ClearIcon, _ArrowDropDownIcon;\nconst _excluded = [\"aria-describedby\", \"aria-label\", \"aria-labelledby\", \"autoComplete\", \"autoHighlight\", \"autoSelect\", \"autoFocus\", \"blurOnSelect\", \"clearIcon\", \"clearOnBlur\", \"clearOnEscape\", \"clearText\", \"closeText\", \"defaultValue\", \"disableCloseOnSelect\", \"disabledItemsFocusable\", \"disableListWrap\", \"disableClearable\", \"disabled\", \"endDecorator\", \"error\", \"filterOptions\", \"filterSelectedOptions\", \"forcePopupIcon\", \"freeSolo\", \"getLimitTagsText\", \"getOptionDisabled\", \"getOptionKey\", \"getOptionLabel\", \"handleHomeEndKeys\", \"includeInputInList\", \"isOptionEqualToValue\", \"groupBy\", \"id\", \"inputValue\", \"limitTags\", \"loading\", \"loadingText\", \"multiple\", \"name\", \"noOptionsText\", \"onChange\", \"onClose\", \"onHighlightChange\", \"onInputChange\", \"onOpen\", \"open\", \"openOnFocus\", \"openText\", \"options\", \"placeholder\", \"popupIcon\", \"readOnly\", \"renderGroup\", \"renderOption\", \"renderTags\", \"required\", \"type\", \"startDecorator\", \"size\", \"color\", \"variant\", \"value\", \"component\", \"selectOnFocus\", \"slots\", \"slotProps\"],\n  _excluded2 = [\"onDelete\"],\n  _excluded3 = [\"key\"],\n  _excluded4 = [\"onBlur\", \"onFocus\", \"onMouseDown\"],\n  _excluded5 = [\"key\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { chainPropTypes, integerPropType, unstable_useForkRef as useForkRef, unstable_capitalize as capitalize } from '@mui/utils';\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport { useAutocomplete } from '@mui/base/useAutocomplete';\nimport { Popper } from '@mui/base/Popper';\nimport { useThemeProps } from '../styles';\nimport ClearIcon from '../internal/svg-icons/Close';\nimport ArrowDropDownIcon from '../internal/svg-icons/ArrowDropDown';\nimport styled from '../styles/styled';\nimport { VariantColorProvider, getChildVariantAndColor } from '../styles/variantColorInheritance';\n// slot components\nimport { StyledIconButton } from '../IconButton/IconButton';\n// default render components\nimport Chip from '../Chip';\nimport ChipDelete from '../ChipDelete';\nimport { StyledInputRoot, StyledInputHtml, StyledInputStartDecorator, StyledInputEndDecorator } from '../Input/Input';\nimport List from '../List';\nimport ListProvider from '../List/ListProvider';\nimport ListSubheader from '../ListSubheader';\nimport ListItem from '../ListItem';\nimport autocompleteClasses, { getAutocompleteUtilityClass } from './autocompleteClasses';\nimport FormControlContext from '../FormControl/FormControlContext';\nimport { StyledAutocompleteListbox } from '../AutocompleteListbox/AutocompleteListbox';\nimport { StyledAutocompleteOption } from '../AutocompleteOption/AutocompleteOption';\nimport useSlot from '../utils/useSlot';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst defaultIsActiveElementInListbox = listboxRef => listboxRef.current !== null && listboxRef.current.contains(document.activeElement);\n// @ts-ignore\nconst defaultGetOptionLabel = option => {\n  var _option$label;\n  return (_option$label = option.label) != null ? _option$label : option;\n};\nconst defaultLimitTagsText = more => `+${more}`;\nconst defaultRenderGroup = params => /*#__PURE__*/_jsxs(ListItem, {\n  nested: true,\n  children: [/*#__PURE__*/_jsx(ListSubheader, {\n    sticky: true,\n    children: params.group\n  }), /*#__PURE__*/_jsx(List, {\n    children: params.children\n  })]\n}, params.key);\nconst useUtilityClasses = ownerState => {\n  const {\n    disabled,\n    focused,\n    hasClearIcon,\n    hasPopupIcon,\n    popupOpen,\n    variant,\n    color,\n    size,\n    multiple\n  } = ownerState;\n  const slots = {\n    root: ['root', focused && 'focused', hasClearIcon && 'hasClearIcon', hasPopupIcon && 'hasPopupIcon', variant && `variant${capitalize(variant)}`, color && `color${capitalize(color)}`, size && `size${capitalize(size)}`],\n    wrapper: ['wrapper', multiple && 'multiple'],\n    input: ['input'],\n    startDecorator: ['startDecorator'],\n    endDecorator: ['endDecorator'],\n    clearIndicator: ['clearIndicator'],\n    popupIndicator: ['popupIndicator', popupOpen && 'popupIndicatorOpen', disabled && 'disabled'],\n    listbox: ['listbox'],\n    option: ['option'],\n    loading: ['loading'],\n    noOptions: ['noOptions'],\n    limitTag: ['limitTag']\n  };\n  return composeClasses(slots, getAutocompleteUtilityClass, {});\n};\nconst AutocompleteRoot = styled(StyledInputRoot, {\n  name: 'JoyAutocomplete',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(({\n  ownerState\n}) => _extends({}, ownerState.size === 'sm' && {\n  '--Autocomplete-wrapperGap': '3px'\n}, ownerState.size === 'md' && {\n  '--Autocomplete-wrapperGap': '3px'\n}, ownerState.size === 'lg' && {\n  '--Autocomplete-wrapperGap': '4px'\n}, {\n  /* Avoid double tap issue on iOS */\n  '@media (pointer: fine)': {\n    [`&:hover .${autocompleteClasses.clearIndicator}`]: {\n      visibility: 'visible'\n    }\n  }\n}, ownerState.multiple && !ownerState.startDecorator && {\n  paddingInlineStart: 0\n}));\n\n/**\n * Wrapper groups the chips (multi selection) and the input\n * so that start/end decorators can stay in the normal flow.\n */\nconst AutocompleteWrapper = styled('div', {\n  name: 'JoyAutocomplete',\n  slot: 'Wrapper',\n  overridesResolver: (props, styles) => styles.wrapper\n})(({\n  ownerState\n}) => ({\n  flex: 1,\n  // stretch to fill the root slot\n  minWidth: 0,\n  // won't push end decorator out of the autocomplete\n  display: 'flex',\n  alignItems: 'center',\n  flexWrap: 'wrap',\n  gap: 'var(--Autocomplete-wrapperGap)',\n  [`&.${autocompleteClasses.multiple}`]: _extends({\n    paddingBlock: 'var(--Autocomplete-wrapperGap)'\n  }, !ownerState.startDecorator && {\n    paddingInlineStart: 'var(--Autocomplete-wrapperGap)'\n  }, !ownerState.endDecorator && {\n    paddingInlineEnd: 'var(--Autocomplete-wrapperGap)'\n  })\n}));\nconst AutocompleteInput = styled(StyledInputHtml, {\n  name: 'JoyAutocomplete',\n  slot: 'Input',\n  overridesResolver: (props, styles) => styles.input\n})(({\n  ownerState\n}) => _extends({\n  minWidth: 30,\n  minHeight: 'var(--Chip-minHeight)'\n}, ownerState.multiple && {\n  marginInlineStart: 'calc(var(--Autocomplete-wrapperGap) * 2.5)'\n}));\nconst AutocompleteStartDecorator = styled(StyledInputStartDecorator, {\n  name: 'JoyAutocomplete',\n  slot: 'StartDecorator',\n  overridesResolver: (props, styles) => styles.startDecorator\n})({});\nconst AutocompleteEndDecorator = styled(StyledInputEndDecorator, {\n  name: 'JoyAutocomplete',\n  slot: 'EndDecorator',\n  overridesResolver: (props, styles) => styles.endDecorator\n})(({\n  ownerState\n}) => _extends({}, (ownerState.hasClearIcon || ownerState.hasPopupIcon) && {\n  '--Button-margin': '0px',\n  '--IconButton-margin': '0px',\n  '--Icon-margin': '0px'\n}));\nconst AutocompleteClearIndicator = styled(StyledIconButton, {\n  name: 'JoyAutocomplete',\n  slot: 'ClearIndicator',\n  overridesResolver: (props, styles) => styles.clearIndicator\n})(({\n  ownerState\n}) => _extends({\n  alignSelf: 'center'\n}, !ownerState.hasPopupIcon && {\n  marginInlineEnd: 'calc(var(--Input-decoratorChildOffset) * -1)'\n}, {\n  marginInlineStart: 'calc(var(--_Input-paddingBlock) / 2)',\n  visibility: ownerState.focused ? 'visible' : 'hidden'\n}));\nconst AutocompletePopupIndicator = styled(StyledIconButton, {\n  name: 'JoyAutocomplete',\n  slot: 'PopupIndicator',\n  overridesResolver: (props, styles) => styles.popupIndicator\n})({\n  alignSelf: 'center',\n  marginInlineStart: 'calc(var(--_Input-paddingBlock) / 2)',\n  marginInlineEnd: 'calc(var(--Input-decoratorChildOffset) * -1)',\n  [`&.${autocompleteClasses.popupIndicatorOpen}`]: {\n    transform: 'rotate(180deg)',\n    '--Icon-color': 'currentColor'\n  }\n});\nconst AutocompleteListbox = styled(StyledAutocompleteListbox, {\n  name: 'JoyAutocomplete',\n  slot: 'Listbox',\n  overridesResolver: (props, styles) => styles.listbox\n})(({\n  theme\n}) => ({\n  // `unstable_popup-zIndex` is a private variable that lets other component, for example Modal, to override the z-index so that the listbox can be displayed above the Modal.\n  zIndex: `var(--unstable_popup-zIndex, ${theme.vars.zIndex.popup})`\n}));\nconst AutocompleteOption = styled(StyledAutocompleteOption, {\n  name: 'JoyAutocomplete',\n  slot: 'Option',\n  overridesResolver: (props, styles) => styles.option\n})({});\nconst AutocompleteLoading = styled(ListItem, {\n  name: 'JoyAutocomplete',\n  slot: 'Loading',\n  overridesResolver: (props, styles) => styles.loading\n})(({\n  theme\n}) => ({\n  color: (theme.vars || theme).palette.text.secondary\n}));\nconst AutocompleteNoOptions = styled(ListItem, {\n  name: 'JoyAutocomplete',\n  slot: 'NoOptions',\n  overridesResolver: (props, styles) => styles.noOptions\n})(({\n  theme\n}) => ({\n  color: (theme.vars || theme).palette.text.secondary\n}));\nconst AutocompleteLimitTag = styled('div', {\n  name: 'JoyAutocomplete',\n  slot: 'NoOptions',\n  overridesResolver: (props, styles) => styles.noOptions\n})({\n  marginInlineStart: 'calc(var(--Input-paddingInline) / 2)',\n  marginBlockStart: 'var(--_Input-paddingBlock)'\n});\n/**\n *\n * Demos:\n *\n * - [Autocomplete](https://mui.com/joy-ui/react-autocomplete/)\n *\n * API:\n *\n * - [Autocomplete API](https://mui.com/joy-ui/api/autocomplete/)\n */\nconst Autocomplete = /*#__PURE__*/React.forwardRef(function Autocomplete(inProps, ref) {\n  var _ref, _inProps$error, _ref2, _inProps$size, _inProps$color, _formControl$color, _ref3;\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyAutocomplete'\n  });\n  const {\n      'aria-describedby': ariaDescribedby,\n      'aria-label': ariaLabel,\n      'aria-labelledby': ariaLabelledby,\n      autoFocus,\n      clearIcon = _ClearIcon || (_ClearIcon = /*#__PURE__*/_jsx(ClearIcon, {\n        fontSize: \"md\"\n      })),\n      clearText = 'Clear',\n      closeText = 'Close',\n      disableClearable = false,\n      disabled: disabledProp,\n      endDecorator,\n      error: errorProp = false,\n      forcePopupIcon = 'auto',\n      freeSolo = false,\n      getLimitTagsText = defaultLimitTagsText,\n      getOptionLabel = defaultGetOptionLabel,\n      groupBy,\n      id,\n      limitTags = -1,\n      loading = false,\n      loadingText = 'Loading…',\n      multiple = false,\n      name,\n      noOptionsText = 'No options',\n      openText = 'Open',\n      placeholder,\n      popupIcon = _ArrowDropDownIcon || (_ArrowDropDownIcon = /*#__PURE__*/_jsx(ArrowDropDownIcon, {})),\n      readOnly = false,\n      renderGroup = defaultRenderGroup,\n      renderOption: renderOptionProp,\n      renderTags,\n      required,\n      type,\n      startDecorator,\n      size: sizeProp = 'md',\n      color: colorProp = 'neutral',\n      variant = 'outlined',\n      component,\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const formControl = React.useContext(FormControlContext);\n  const error = (_ref = (_inProps$error = inProps.error) != null ? _inProps$error : formControl == null ? void 0 : formControl.error) != null ? _ref : errorProp;\n  const size = (_ref2 = (_inProps$size = inProps.size) != null ? _inProps$size : formControl == null ? void 0 : formControl.size) != null ? _ref2 : sizeProp;\n  const color = (_inProps$color = inProps.color) != null ? _inProps$color : error ? 'danger' : (_formControl$color = formControl == null ? void 0 : formControl.color) != null ? _formControl$color : colorProp;\n  const disabled = (_ref3 = disabledProp != null ? disabledProp : formControl == null ? void 0 : formControl.disabled) != null ? _ref3 : false;\n  const {\n    getRootProps,\n    getInputProps,\n    getPopupIndicatorProps,\n    getClearProps,\n    getTagProps,\n    getListboxProps,\n    getOptionProps,\n    value,\n    dirty,\n    popupOpen,\n    focused,\n    focusedTag,\n    anchorEl,\n    setAnchorEl,\n    inputValue,\n    groupedOptions\n  } = useAutocomplete(_extends({}, props, {\n    id: id != null ? id : formControl == null ? void 0 : formControl.htmlFor,\n    componentName: 'Autocomplete',\n    unstable_classNamePrefix: 'Mui',\n    unstable_isActiveElementInListbox: defaultIsActiveElementInListbox\n  }));\n  const {\n    onMouseDown: handleInputMouseDown\n  } = getInputProps();\n  const {\n    onClick: handleRootOnClick\n  } = getRootProps();\n  const hasClearIcon = !disableClearable && !disabled && dirty && !readOnly;\n  const hasPopupIcon = (!freeSolo || forcePopupIcon === true) && forcePopupIcon !== false;\n\n  // If you modify this, make sure to keep the `AutocompleteOwnerState` type in sync.\n  const ownerState = _extends({\n    instanceColor: inProps.color\n  }, props, {\n    value,\n    disabled,\n    focused,\n    getOptionLabel,\n    hasOptions: !!groupedOptions.length,\n    hasClearIcon,\n    hasPopupIcon,\n    inputFocused: focusedTag === -1,\n    popupOpen,\n    size,\n    color,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  let selectedOptions;\n  if (multiple && value.length > 0) {\n    const getCustomizedTagProps = params => {\n      const _getTagProps = getTagProps(params),\n        {\n          onDelete\n        } = _getTagProps,\n        tagProps = _objectWithoutPropertiesLoose(_getTagProps, _excluded2);\n      return _extends({\n        disabled,\n        size,\n        onClick: onDelete\n      }, tagProps);\n    };\n    if (renderTags) {\n      selectedOptions = renderTags(value, getCustomizedTagProps, ownerState);\n    } else {\n      selectedOptions = value.map((option, index) => {\n        const _getCustomizedTagProp = getCustomizedTagProps({\n            index\n          }),\n          {\n            key: endDecoratorKey\n          } = _getCustomizedTagProp,\n          endDecoratorProps = _objectWithoutPropertiesLoose(_getCustomizedTagProp, _excluded3);\n        return /*#__PURE__*/_jsx(Chip, {\n          size: size,\n          variant: \"soft\",\n          color: \"neutral\",\n          endDecorator: /*#__PURE__*/_jsx(ChipDelete, _extends({}, endDecoratorProps), endDecoratorKey),\n          sx: {\n            minWidth: 0\n          },\n          children: getOptionLabel(option)\n        }, index);\n      });\n    }\n  }\n  const rootRef = useForkRef(ref, setAnchorEl);\n  const rootStateClasses = {\n    [autocompleteClasses.disabled]: disabled,\n    [autocompleteClasses.error]: error,\n    [autocompleteClasses.focused]: focused,\n    [autocompleteClasses.formControl]: Boolean(formControl)\n  };\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref: rootRef,\n    className: [classes.root, rootStateClasses],\n    elementType: AutocompleteRoot,\n    externalForwardedProps,\n    ownerState,\n    getSlotProps: getRootProps,\n    additionalProps: {\n      onClick: event => {\n        if (handleRootOnClick) {\n          handleRootOnClick(event);\n        }\n        if (event.currentTarget === event.target && handleInputMouseDown) {\n          handleInputMouseDown(event);\n        }\n      }\n    }\n  });\n  const [SlotWrapper, wrapperProps] = useSlot('wrapper', {\n    className: classes.wrapper,\n    elementType: AutocompleteWrapper,\n    externalForwardedProps,\n    ownerState\n  });\n  const inputStateClasses = {\n    [autocompleteClasses.disabled]: disabled\n  };\n  const [SlotInput, inputProps] = useSlot('input', {\n    className: [classes.input, inputStateClasses],\n    elementType: AutocompleteInput,\n    getSlotProps: handlers => {\n      const _getInputProps = getInputProps(),\n        {\n          onBlur,\n          onFocus,\n          onMouseDown\n        } = _getInputProps,\n        inputSlotProps = _objectWithoutPropertiesLoose(_getInputProps, _excluded4);\n      return _extends({}, inputSlotProps, {\n        onBlur: event => {\n          var _handlers$onBlur;\n          onBlur == null || onBlur(event);\n          (_handlers$onBlur = handlers.onBlur) == null || _handlers$onBlur.call(handlers, event);\n        },\n        onFocus: event => {\n          var _handlers$onFocus;\n          onFocus == null || onFocus(event);\n          (_handlers$onFocus = handlers.onFocus) == null || _handlers$onFocus.call(handlers, event);\n        },\n        onMouseDown: event => {\n          var _handlers$onMouseDown;\n          onMouseDown == null || onMouseDown(event);\n          (_handlers$onMouseDown = handlers.onMouseDown) == null || _handlers$onMouseDown.call(handlers, event);\n        }\n      });\n    },\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      autoFocus,\n      placeholder,\n      name,\n      readOnly,\n      disabled,\n      required: required != null ? required : formControl == null ? void 0 : formControl.required,\n      type,\n      'aria-invalid': error || undefined,\n      'aria-label': ariaLabel,\n      'aria-labelledby': ariaLabelledby,\n      'aria-describedby': ariaDescribedby != null ? ariaDescribedby : formControl == null ? void 0 : formControl['aria-describedby']\n    }\n  });\n  const [SlotStartDecorator, startDecoratorProps] = useSlot('startDecorator', {\n    className: classes.startDecorator,\n    elementType: AutocompleteStartDecorator,\n    externalForwardedProps,\n    ownerState\n  });\n  const [SlotEndDecorator, endDecoratorProps] = useSlot('endDecorator', {\n    className: classes.endDecorator,\n    elementType: AutocompleteEndDecorator,\n    externalForwardedProps,\n    ownerState\n  });\n  const [SlotClearIndicator, clearIndicatorProps] = useSlot('clearIndicator', {\n    className: classes.clearIndicator,\n    elementType: AutocompleteClearIndicator,\n    getSlotProps: getClearProps,\n    externalForwardedProps,\n    ownerState,\n    getSlotOwnerState: mergedProps => ({\n      size: mergedProps.size || size,\n      variant: mergedProps.variant || getChildVariantAndColor(variant, color).variant || 'plain',\n      color: mergedProps.color || getChildVariantAndColor(variant, color).color || 'neutral',\n      disableColorInversion: !!inProps.color\n    }),\n    additionalProps: {\n      'aria-label': clearText,\n      title: clearText\n    }\n  });\n  const [SlotPopupIndicator, popupIndicatorProps] = useSlot('popupIndicator', {\n    className: classes.popupIndicator,\n    elementType: AutocompletePopupIndicator,\n    getSlotProps: getPopupIndicatorProps,\n    externalForwardedProps,\n    ownerState,\n    getSlotOwnerState: mergedProps => ({\n      size: mergedProps.size || size,\n      variant: mergedProps.variant || getChildVariantAndColor(variant, color).variant || 'plain',\n      color: mergedProps.color || getChildVariantAndColor(variant, color).color || 'neutral',\n      disableColorInversion: !!inProps.color\n    }),\n    additionalProps: {\n      disabled,\n      'aria-label': popupOpen ? closeText : openText,\n      title: popupOpen ? closeText : openText,\n      type: 'button'\n    }\n  });\n  const [SlotListbox, listboxProps] = useSlot('listbox', {\n    className: classes.listbox,\n    elementType: AutocompleteListbox,\n    getSlotProps: getListboxProps,\n    externalForwardedProps,\n    ownerState,\n    getSlotOwnerState: mergedProps => ({\n      size: mergedProps.size || size,\n      variant: mergedProps.variant || variant,\n      color: mergedProps.color || color,\n      disableColorInversion: !mergedProps.disablePortal\n    }),\n    additionalProps: {\n      anchorEl,\n      open: popupOpen,\n      style: anchorEl ? {\n        width: anchorEl.clientWidth\n      } : {}\n    }\n  });\n  const [SlotLoading, loadingProps] = useSlot('loading', {\n    className: classes.loading,\n    elementType: AutocompleteLoading,\n    externalForwardedProps,\n    ownerState\n  });\n  const [SlotNoOptions, noOptionsProps] = useSlot('noOptions', {\n    className: classes.noOptions,\n    elementType: AutocompleteNoOptions,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      role: 'presentation',\n      onMouseDown: event => {\n        // Prevent input blur when interacting with the \"no options\" content\n        event.preventDefault();\n      }\n    }\n  });\n  const [SlotLimitTag, limitTagProps] = useSlot('limitTag', {\n    className: classes.limitTag,\n    elementType: AutocompleteLimitTag,\n    externalForwardedProps,\n    ownerState\n  });\n  if (limitTags > -1 && Array.isArray(selectedOptions)) {\n    const more = selectedOptions.length - limitTags;\n    if (!focused && more > 0) {\n      selectedOptions = selectedOptions.splice(0, limitTags);\n      selectedOptions.push( /*#__PURE__*/_jsx(SlotLimitTag, _extends({}, limitTagProps, {\n        children: getLimitTagsText(more)\n      }), selectedOptions.length));\n    }\n  }\n  const [SlotOption, baseOptionProps] = useSlot('option', {\n    className: classes.option,\n    elementType: AutocompleteOption,\n    externalForwardedProps,\n    ownerState,\n    getSlotOwnerState: mergedProps => ({\n      variant: mergedProps.variant || getChildVariantAndColor(variant, color).variant || 'plain',\n      color: mergedProps.color || getChildVariantAndColor(variant, color).color || 'neutral',\n      disableColorInversion: !listboxProps.disablePortal\n    }),\n    additionalProps: {\n      as: 'li'\n    }\n  });\n  const defaultRenderOption = (optionProps, option) => {\n    const {\n        key\n      } = optionProps,\n      rest = _objectWithoutPropertiesLoose(optionProps, _excluded5);\n    return /*#__PURE__*/_jsx(SlotOption, _extends({}, rest, {\n      children: getOptionLabel(option)\n    }), key);\n  };\n  const renderOption = renderOptionProp || defaultRenderOption;\n  const renderListOption = (option, index) => {\n    const optionProps = getOptionProps({\n      option,\n      index\n    });\n    return renderOption(_extends({}, baseOptionProps, optionProps), option, {\n      // `aria-selected` prop will always by boolean, see useAutocomplete hook.\n      selected: !!optionProps['aria-selected'],\n      inputValue,\n      ownerState\n    });\n  };\n\n  // Wait for `listboxProps` because `slotProps.listbox` could be a function.\n  const modifiers = React.useMemo(() => [{\n    name: 'offset',\n    options: {\n      offset: [0, 4]\n    }\n  }, ...(listboxProps.modifiers || [])], [listboxProps.modifiers]);\n  let popup = null;\n  if (anchorEl) {\n    var _props$slots;\n    popup = /*#__PURE__*/_jsx(VariantColorProvider, {\n      variant: variant,\n      color: color,\n      children: /*#__PURE__*/_jsx(ListProvider, {\n        nested: true,\n        children: /*#__PURE__*/_jsxs(SlotListbox, _extends({}, listboxProps, {\n          className: clsx(listboxProps.className)\n          // @ts-ignore internal logic (too complex to typed PopperOwnProps to SlotListbox but this should be removed when we have `usePopper`)\n          ,\n          modifiers: modifiers\n        }, !((_props$slots = props.slots) != null && _props$slots.listbox) && {\n          as: Popper,\n          slots: {\n            root: listboxProps.as || 'ul'\n          }\n        }, {\n          children: [groupedOptions.map((option, index) => {\n            if (groupBy) {\n              const typedOption = option;\n              return renderGroup({\n                key: String(typedOption.key),\n                group: typedOption.group,\n                children: typedOption.options.map((option2, index2) => renderListOption(option2, typedOption.index + index2))\n              });\n            }\n            return renderListOption(option, index);\n          }), loading && groupedOptions.length === 0 ? /*#__PURE__*/_jsx(SlotLoading, _extends({}, loadingProps, {\n            children: loadingText\n          })) : null, groupedOptions.length === 0 && !freeSolo && !loading ? /*#__PURE__*/_jsx(SlotNoOptions, _extends({}, noOptionsProps, {\n            children: noOptionsText\n          })) : null]\n        }))\n      })\n    });\n  }\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsxs(SlotRoot, _extends({}, rootProps, {\n      children: [startDecorator && /*#__PURE__*/_jsx(SlotStartDecorator, _extends({}, startDecoratorProps, {\n        children: startDecorator\n      })), /*#__PURE__*/_jsxs(SlotWrapper, _extends({}, wrapperProps, {\n        children: [selectedOptions, /*#__PURE__*/_jsx(SlotInput, _extends({}, inputProps))]\n      })), endDecorator && /*#__PURE__*/_jsx(SlotEndDecorator, _extends({}, endDecoratorProps, {\n        children: endDecorator\n      })), hasClearIcon ? /*#__PURE__*/_jsx(SlotClearIndicator, _extends({}, clearIndicatorProps, {\n        children: clearIcon\n      })) : null, hasPopupIcon ? /*#__PURE__*/_jsx(SlotPopupIndicator, _extends({}, popupIndicatorProps, {\n        children: popupIcon\n      })) : null]\n    })), popup]\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Autocomplete.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Identifies the element (or elements) that describes the object.\n   * @see aria-labelledby\n   */\n  'aria-describedby': PropTypes.string,\n  /**\n   * Defines a string value that labels the current element.\n   * @see aria-labelledby.\n   */\n  'aria-label': PropTypes.string,\n  /**\n   * Identifies the element (or elements) that labels the current element.\n   * @see aria-describedby.\n   */\n  'aria-labelledby': PropTypes.string,\n  /**\n   * If `true`, the portion of the selected suggestion that the user hasn't typed,\n   * known as the completion string, appears inline after the input cursor in the textbox.\n   * The inline completion string is visually highlighted and has a selected state.\n   * @default false\n   */\n  autoComplete: PropTypes.bool,\n  /**\n   * If `true`, the `input` element is focused during the first mount.\n   */\n  autoFocus: PropTypes.bool,\n  /**\n   * If `true`, the first option is automatically highlighted.\n   * @default false\n   */\n  autoHighlight: PropTypes.bool,\n  /**\n   * If `true`, the selected option becomes the value of the input\n   * when the Autocomplete loses focus unless the user chooses\n   * a different option or changes the character string in the input.\n   *\n   * When using the `freeSolo` mode, the typed value will be the input value\n   * if the Autocomplete loses focus without highlighting an option.\n   * @default false\n   */\n  autoSelect: PropTypes.bool,\n  /**\n   * Control if the input should be blurred when an option is selected:\n   *\n   * - `false` the input is not blurred.\n   * - `true` the input is always blurred.\n   * - `touch` the input is blurred after a touch event.\n   * - `mouse` the input is blurred after a mouse event.\n   * @default false\n   */\n  blurOnSelect: PropTypes.oneOfType([PropTypes.oneOf(['mouse', 'touch']), PropTypes.bool]),\n  /**\n   * The icon to display in place of the default clear icon.\n   * @default <ClearIcon fontSize=\"md\" />\n   */\n  clearIcon: PropTypes.node,\n  /**\n   * If `true`, the input's text is cleared on blur if no value is selected.\n   *\n   * Set it to `true` if you want to help the user enter a new value.\n   * Set it to `false` if you want to help the user resume their search.\n   * @default !props.freeSolo\n   */\n  clearOnBlur: PropTypes.bool,\n  /**\n   * If `true`, clear all values when the user presses escape and the popup is closed.\n   * @default false\n   */\n  clearOnEscape: PropTypes.bool,\n  /**\n   * Override the default text for the *clear* icon button.\n   *\n   * For localization purposes, you can use the provided [translations](/material-ui/guides/localization/).\n   * @default 'Clear'\n   */\n  clearText: PropTypes.string,\n  /**\n   * Override the default text for the *close popup* icon button.\n   *\n   * For localization purposes, you can use the provided [translations](/material-ui/guides/localization/).\n   * @default 'Close'\n   */\n  closeText: PropTypes.string,\n  /**\n   * The color of the component. It supports those theme colors that make sense for this component.\n   * @default 'neutral'\n   */\n  color: PropTypes.oneOf(['danger', 'neutral', 'primary', 'success', 'warning']),\n  /**\n   * The default value. Use when the component is not controlled.\n   * @default props.multiple ? [] : null\n   */\n  defaultValue: chainPropTypes(PropTypes.any, props => {\n    if (props.multiple && props.defaultValue !== undefined && !Array.isArray(props.defaultValue)) {\n      return new Error(['MUI: The Autocomplete expects the `defaultValue` prop to be an array when `multiple={true}` or undefined.', `However, ${props.defaultValue} was provided.`].join('\\n'));\n    }\n    return null;\n  }),\n  /**\n   * If `true`, the input can't be cleared.\n   * @default false\n   */\n  disableClearable: PropTypes.bool,\n  /**\n   * If `true`, the popup won't close when a value is selected.\n   * @default false\n   */\n  disableCloseOnSelect: PropTypes.bool,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, will allow focus on disabled items.\n   * @default false\n   */\n  disabledItemsFocusable: PropTypes.bool,\n  /**\n   * If `true`, the list box in the popup will not wrap focus.\n   * @default false\n   */\n  disableListWrap: PropTypes.bool,\n  /**\n   * Trailing adornment for this input.\n   */\n  endDecorator: PropTypes.node,\n  /**\n   * If `true`, the `input` will indicate an error.\n   * The prop defaults to the value (`false`) inherited from the parent FormControl component.\n   * @default false\n   */\n  error: PropTypes.bool,\n  /**\n   * A function that determines the filtered options to be rendered on search.\n   *\n   * @default createFilterOptions()\n   * @param {Value[]} options The options to render.\n   * @param {object} state The state of the component.\n   * @returns {Value[]}\n   */\n  filterOptions: PropTypes.func,\n  /**\n   * If `true`, hide the selected options from the list box.\n   * @default false\n   */\n  filterSelectedOptions: PropTypes.bool,\n  /**\n   * Force the visibility display of the popup icon.\n   * @default 'auto'\n   */\n  forcePopupIcon: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.bool]),\n  /**\n   * If `true`, the Autocomplete is free solo, meaning that the user input is not bound to provided options.\n   * @default false\n   */\n  freeSolo: PropTypes.bool,\n  /**\n   * The label to display when the tags are truncated (`limitTags`).\n   *\n   * @param {string | number} more The number of truncated tags.\n   * @returns {ReactNode}\n   * @default (more: string | number) => `+${more}`\n   */\n  getLimitTagsText: PropTypes.func,\n  /**\n   * Used to determine the disabled state for a given option.\n   *\n   * @param {Value} option The option to test.\n   * @returns {boolean}\n   */\n  getOptionDisabled: PropTypes.func,\n  /**\n   * Used to determine the key for a given option.\n   * This can be useful when the labels of options are not unique (since labels are used as keys by default).\n   *\n   * @param {Value} option The option to get the key for.\n   * @returns {string | number}\n   */\n  getOptionKey: PropTypes.func,\n  /**\n   * Used to determine the string value for a given option.\n   * It's used to fill the input (and the list box options if `renderOption` is not provided).\n   *\n   * If used in free solo mode, it must accept both the type of the options and a string.\n   *\n   * @param {Value} option\n   * @returns {string}\n   * @default (option) => option.label ?? option\n   */\n  getOptionLabel: PropTypes.func,\n  /**\n   * If provided, the options will be grouped under the returned string.\n   * The groupBy value is also used as the text for group headings when `renderGroup` is not provided.\n   *\n   * @param {Value} options The options to group.\n   * @returns {string}\n   */\n  groupBy: PropTypes.func,\n  /**\n   * If `true`, the component handles the \"Home\" and \"End\" keys when the popup is open.\n   * It should move focus to the first option and last option, respectively.\n   * @default !props.freeSolo\n   */\n  handleHomeEndKeys: PropTypes.bool,\n  /**\n   * This prop is used to help implement the accessibility logic.\n   * If you don't provide an id it will fall back to a randomly generated one.\n   */\n  id: PropTypes.string,\n  /**\n   * If `true`, the highlight can move to the input.\n   * @default false\n   */\n  includeInputInList: PropTypes.bool,\n  /**\n   * The input value.\n   */\n  inputValue: PropTypes.string,\n  /**\n   * Used to determine if the option represents the given value.\n   * Uses strict equality by default.\n   * ⚠️ Both arguments need to be handled, an option can only match with one value.\n   *\n   * @param {Value} option The option to test.\n   * @param {Value} value The value to test against.\n   * @returns {boolean}\n   */\n  isOptionEqualToValue: PropTypes.func,\n  /**\n   * The maximum number of tags that will be visible when not focused.\n   * Set `-1` to disable the limit.\n   * @default -1\n   */\n  limitTags: integerPropType,\n  /**\n   * If `true`, the component is in a loading state.\n   * This shows the `loadingText` in place of suggestions (only if there are no suggestions to show, for example `options` are empty).\n   * @default false\n   */\n  loading: PropTypes.bool,\n  /**\n   * Text to display when in a loading state.\n   *\n   * For localization purposes, you can use the provided [translations](/material-ui/guides/localization/).\n   * @default 'Loading…'\n   */\n  loadingText: PropTypes.node,\n  /**\n   * If `true`, `value` must be an array and the menu will support multiple selections.\n   * @default false\n   */\n  multiple: PropTypes.bool,\n  /**\n   * Name attribute of the `input` element.\n   */\n  name: PropTypes.string,\n  /**\n   * Text to display when there are no options.\n   *\n   * For localization purposes, you can use the provided [translations](/material-ui/guides/localization/).\n   * @default 'No options'\n   */\n  noOptionsText: PropTypes.node,\n  /**\n   * Callback fired when the value changes.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {Value|Value[]} value The new value of the component.\n   * @param {string} reason One of \"createOption\", \"selectOption\", \"removeOption\", \"blur\" or \"clear\".\n   * @param {string} [details]\n   */\n  onChange: PropTypes.func,\n  /**\n   * Callback fired when the popup requests to be closed.\n   * Use in controlled mode (see open).\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {string} reason Can be: `\"toggleInput\"`, `\"escape\"`, `\"selectOption\"`, `\"removeOption\"`, `\"blur\"`.\n   */\n  onClose: PropTypes.func,\n  /**\n   * Callback fired when the highlight option changes.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {Value} option The highlighted option.\n   * @param {string} reason Can be: `\"keyboard\"`, `\"auto\"`, `\"mouse\"`, `\"touch\"`.\n   */\n  onHighlightChange: PropTypes.func,\n  /**\n   * Callback fired when the input value changes.\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   * @param {string} value The new value of the text input.\n   * @param {string} reason Can be: `\"input\"` (user input), `\"reset\"` (programmatic change), `\"clear\"`.\n   */\n  onInputChange: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onKeyDown: PropTypes.func,\n  /**\n   * Callback fired when the popup requests to be opened.\n   * Use in controlled mode (see open).\n   *\n   * @param {React.SyntheticEvent} event The event source of the callback.\n   */\n  onOpen: PropTypes.func,\n  /**\n   * If `true`, the component is shown.\n   */\n  open: PropTypes.bool,\n  /**\n   * If `true`, the popup will open on input focus.\n   * @default false\n   */\n  openOnFocus: PropTypes.bool,\n  /**\n   * Override the default text for the *open popup* icon button.\n   *\n   * For localization purposes, you can use the provided [translations](/material-ui/guides/localization/).\n   * @default 'Open'\n   */\n  openText: PropTypes.string,\n  /**\n   * Array of options.\n   */\n  options: PropTypes.array.isRequired,\n  /**\n   * The input placeholder\n   */\n  placeholder: PropTypes.string,\n  /**\n   * The icon to display in place of the default popup icon.\n   * @default <ArrowDropDownIcon />\n   */\n  popupIcon: PropTypes.node,\n  /**\n   * If `true`, the component becomes readonly. It is also supported for multiple tags where the tag cannot be deleted.\n   * @default false\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * Render the group.\n   *\n   * @param {AutocompleteRenderGroupParams} params The group to render.\n   * @returns {ReactNode}\n   */\n  renderGroup: PropTypes.func,\n  /**\n   * Render the option, use `getOptionLabel` by default.\n   *\n   * @param {object} props The props to apply on the li element.\n   * @param {T} option The option to render.\n   * @param {object} state The state of the component.\n   * @returns {ReactNode}\n   */\n  renderOption: PropTypes.func,\n  /**\n   * Render the selected value.\n   *\n   * @param {T[]} value The `value` provided to the component.\n   * @param {function} getTagProps A tag props getter.\n   * @param {object} ownerState The state of the Autocomplete component.\n   * @returns {ReactNode}\n   */\n  renderTags: PropTypes.func,\n  /**\n   * If `true`, the `input` element is required.\n   * The prop defaults to the value (`false`) inherited from the parent FormControl component.\n   */\n  required: PropTypes.bool,\n  /**\n   * If `true`, the input's text is selected on focus.\n   * It helps the user clear the selected value.\n   * @default !props.freeSolo\n   */\n  selectOnFocus: PropTypes.bool,\n  /**\n   * The size of the component.\n   * @default 'md'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['sm', 'md', 'lg']), PropTypes.string]),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    clearIndicator: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    endDecorator: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    input: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    limitTag: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    listbox: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    loading: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    noOptions: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    option: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    popupIndicator: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    startDecorator: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    wrapper: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    clearIndicator: PropTypes.elementType,\n    endDecorator: PropTypes.elementType,\n    input: PropTypes.elementType,\n    limitTag: PropTypes.elementType,\n    listbox: PropTypes.elementType,\n    loading: PropTypes.elementType,\n    noOptions: PropTypes.elementType,\n    option: PropTypes.elementType,\n    popupIndicator: PropTypes.elementType,\n    root: PropTypes.elementType,\n    startDecorator: PropTypes.elementType,\n    wrapper: PropTypes.elementType\n  }),\n  /**\n   * Leading adornment for this input.\n   */\n  startDecorator: PropTypes.node,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * Type of the `input` element. It should be [a valid HTML5 input type](https://developer.mozilla.org/en-US/docs/Web/HTML/Element/input#Form_%3Cinput%3E_types).\n   */\n  type: PropTypes.string,\n  /**\n   * The value of the autocomplete.\n   *\n   * The value must have reference equality with the option in order to be selected.\n   * You can customize the equality behavior with the `isOptionEqualToValue` prop.\n   */\n  value: chainPropTypes(PropTypes.any, props => {\n    if (props.multiple && props.value !== undefined && !Array.isArray(props.value)) {\n      return new Error(['MUI: The Autocomplete expects the `value` prop to be an array when `multiple={true}` or undefined.', `However, ${props.value} was provided.`].join('\\n'));\n    }\n    return null;\n  }),\n  /**\n   * The [global variant](https://mui.com/joy-ui/main-features/global-variants/) to use.\n   * @default 'outlined'\n   */\n  variant: PropTypes.oneOf(['outlined', 'plain', 'soft', 'solid'])\n} : void 0;\nexport default Autocomplete;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,IAAIC,UAAU,EAAEC,kBAAkB;AAClC,MAAMC,SAAS,GAAG,CAAC,kBAAkB,EAAE,YAAY,EAAE,iBAAiB,EAAE,cAAc,EAAE,eAAe,EAAE,YAAY,EAAE,WAAW,EAAE,cAAc,EAAE,WAAW,EAAE,aAAa,EAAE,eAAe,EAAE,WAAW,EAAE,WAAW,EAAE,cAAc,EAAE,sBAAsB,EAAE,wBAAwB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,UAAU,EAAE,cAAc,EAAE,OAAO,EAAE,eAAe,EAAE,uBAAuB,EAAE,gBAAgB,EAAE,UAAU,EAAE,kBAAkB,EAAE,mBAAmB,EAAE,cAAc,EAAE,gBAAgB,EAAE,mBAAmB,EAAE,oBAAoB,EAAE,sBAAsB,EAAE,SAAS,EAAE,IAAI,EAAE,YAAY,EAAE,WAAW,EAAE,SAAS,EAAE,aAAa,EAAE,UAAU,EAAE,MAAM,EAAE,eAAe,EAAE,UAAU,EAAE,SAAS,EAAE,mBAAmB,EAAE,eAAe,EAAE,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,UAAU,EAAE,SAAS,EAAE,aAAa,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,cAAc,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM,EAAE,gBAAgB,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,EAAE,WAAW,EAAE,eAAe,EAAE,OAAO,EAAE,WAAW,CAAC;EACh/BC,UAAU,GAAG,CAAC,UAAU,CAAC;EACzBC,UAAU,GAAG,CAAC,KAAK,CAAC;EACpBC,UAAU,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,aAAa,CAAC;EACjDC,UAAU,GAAG,CAAC,KAAK,CAAC;AACtB,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,cAAc,EAAEC,eAAe,EAAEC,mBAAmB,IAAIC,UAAU,EAAEC,mBAAmB,IAAIC,UAAU,QAAQ,YAAY;AAClI,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,0BAA0B;AACpF,SAASC,eAAe,QAAQ,2BAA2B;AAC3D,SAASC,MAAM,QAAQ,kBAAkB;AACzC,SAASC,aAAa,QAAQ,WAAW;AACzC,OAAOC,SAAS,MAAM,6BAA6B;AACnD,OAAOC,iBAAiB,MAAM,qCAAqC;AACnE,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,oBAAoB,EAAEC,uBAAuB,QAAQ,mCAAmC;AACjG;AACA,SAASC,gBAAgB,QAAQ,0BAA0B;AAC3D;AACA,OAAOC,IAAI,MAAM,SAAS;AAC1B,OAAOC,UAAU,MAAM,eAAe;AACtC,SAASC,eAAe,EAAEC,eAAe,EAAEC,yBAAyB,EAAEC,uBAAuB,QAAQ,gBAAgB;AACrH,OAAOC,IAAI,MAAM,SAAS;AAC1B,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,QAAQ,MAAM,aAAa;AAClC,OAAOC,mBAAmB,IAAIC,2BAA2B,QAAQ,uBAAuB;AACxF,OAAOC,kBAAkB,MAAM,mCAAmC;AAClE,SAASC,yBAAyB,QAAQ,4CAA4C;AACtF,SAASC,wBAAwB,QAAQ,0CAA0C;AACnF,OAAOC,OAAO,MAAM,kBAAkB;AACtC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,+BAA+B,GAAGC,UAAU,IAAIA,UAAU,CAACC,OAAO,KAAK,IAAI,IAAID,UAAU,CAACC,OAAO,CAACC,QAAQ,CAACC,QAAQ,CAACC,aAAa,CAAC;AACxI;AACA,MAAMC,qBAAqB,GAAGC,MAAM,IAAI;EACtC,IAAIC,aAAa;EACjB,OAAO,CAACA,aAAa,GAAGD,MAAM,CAACE,KAAK,KAAK,IAAI,GAAGD,aAAa,GAAGD,MAAM;AACxE,CAAC;AACD,MAAMG,oBAAoB,GAAGC,IAAI,IAAI,IAAIA,IAAI,EAAE;AAC/C,MAAMC,kBAAkB,GAAGC,MAAM,IAAI,aAAad,KAAK,CAACV,QAAQ,EAAE;EAChEyB,MAAM,EAAE,IAAI;EACZC,QAAQ,EAAE,CAAC,aAAalB,IAAI,CAACT,aAAa,EAAE;IAC1C4B,MAAM,EAAE,IAAI;IACZD,QAAQ,EAAEF,MAAM,CAACI;EACnB,CAAC,CAAC,EAAE,aAAapB,IAAI,CAACX,IAAI,EAAE;IAC1B6B,QAAQ,EAAEF,MAAM,CAACE;EACnB,CAAC,CAAC;AACJ,CAAC,EAAEF,MAAM,CAACK,GAAG,CAAC;AACd,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,QAAQ;IACRC,OAAO;IACPC,YAAY;IACZC,YAAY;IACZC,SAAS;IACTC,OAAO;IACPC,KAAK;IACLC,IAAI;IACJC;EACF,CAAC,GAAGT,UAAU;EACd,MAAMU,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAET,OAAO,IAAI,SAAS,EAAEC,YAAY,IAAI,cAAc,EAAEC,YAAY,IAAI,cAAc,EAAEE,OAAO,IAAI,UAAU1D,UAAU,CAAC0D,OAAO,CAAC,EAAE,EAAEC,KAAK,IAAI,QAAQ3D,UAAU,CAAC2D,KAAK,CAAC,EAAE,EAAEC,IAAI,IAAI,OAAO5D,UAAU,CAAC4D,IAAI,CAAC,EAAE,CAAC;IACzNI,OAAO,EAAE,CAAC,SAAS,EAAEH,QAAQ,IAAI,UAAU,CAAC;IAC5CI,KAAK,EAAE,CAAC,OAAO,CAAC;IAChBC,cAAc,EAAE,CAAC,gBAAgB,CAAC;IAClCC,YAAY,EAAE,CAAC,cAAc,CAAC;IAC9BC,cAAc,EAAE,CAAC,gBAAgB,CAAC;IAClCC,cAAc,EAAE,CAAC,gBAAgB,EAAEZ,SAAS,IAAI,oBAAoB,EAAEJ,QAAQ,IAAI,UAAU,CAAC;IAC7FiB,OAAO,EAAE,CAAC,SAAS,CAAC;IACpB/B,MAAM,EAAE,CAAC,QAAQ,CAAC;IAClBgC,OAAO,EAAE,CAAC,SAAS,CAAC;IACpBC,SAAS,EAAE,CAAC,WAAW,CAAC;IACxBC,QAAQ,EAAE,CAAC,UAAU;EACvB,CAAC;EACD,OAAOvE,cAAc,CAAC4D,KAAK,EAAEvC,2BAA2B,EAAE,CAAC,CAAC,CAAC;AAC/D,CAAC;AACD,MAAMmD,gBAAgB,GAAGlE,MAAM,CAACM,eAAe,EAAE;EAC/C6D,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAAChB;AAC/C,CAAC,CAAC,CAACiB,KAAA;EAAA,IAAC;IACF5B;EACF,CAAC,GAAA4B,KAAA;EAAA,OAAKhG,QAAQ,CAAC,CAAC,CAAC,EAAEoE,UAAU,CAACQ,IAAI,KAAK,IAAI,IAAI;IAC7C,2BAA2B,EAAE;EAC/B,CAAC,EAAER,UAAU,CAACQ,IAAI,KAAK,IAAI,IAAI;IAC7B,2BAA2B,EAAE;EAC/B,CAAC,EAAER,UAAU,CAACQ,IAAI,KAAK,IAAI,IAAI;IAC7B,2BAA2B,EAAE;EAC/B,CAAC,EAAE;IACD;IACA,wBAAwB,EAAE;MACxB,CAAC,YAAYtC,mBAAmB,CAAC8C,cAAc,EAAE,GAAG;QAClDa,UAAU,EAAE;MACd;IACF;EACF,CAAC,EAAE7B,UAAU,CAACS,QAAQ,IAAI,CAACT,UAAU,CAACc,cAAc,IAAI;IACtDgB,kBAAkB,EAAE;EACtB,CAAC,CAAC;AAAA,EAAC;;AAEH;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG3E,MAAM,CAAC,KAAK,EAAE;EACxCmE,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,SAAS;EACfC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACf;AAC/C,CAAC,CAAC,CAACoB,KAAA;EAAA,IAAC;IACFhC;EACF,CAAC,GAAAgC,KAAA;EAAA,OAAM;IACLC,IAAI,EAAE,CAAC;IACP;IACAC,QAAQ,EAAE,CAAC;IACX;IACAC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE,MAAM;IAChBC,GAAG,EAAE,gCAAgC;IACrC,CAAC,KAAKpE,mBAAmB,CAACuC,QAAQ,EAAE,GAAG7E,QAAQ,CAAC;MAC9C2G,YAAY,EAAE;IAChB,CAAC,EAAE,CAACvC,UAAU,CAACc,cAAc,IAAI;MAC/BgB,kBAAkB,EAAE;IACtB,CAAC,EAAE,CAAC9B,UAAU,CAACe,YAAY,IAAI;MAC7ByB,gBAAgB,EAAE;IACpB,CAAC;EACH,CAAC;AAAA,CAAC,CAAC;AACH,MAAMC,iBAAiB,GAAGrF,MAAM,CAACO,eAAe,EAAE;EAChD4D,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,OAAO;EACbC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACd;AAC/C,CAAC,CAAC,CAAC6B,KAAA;EAAA,IAAC;IACF1C;EACF,CAAC,GAAA0C,KAAA;EAAA,OAAK9G,QAAQ,CAAC;IACbsG,QAAQ,EAAE,EAAE;IACZS,SAAS,EAAE;EACb,CAAC,EAAE3C,UAAU,CAACS,QAAQ,IAAI;IACxBmC,iBAAiB,EAAE;EACrB,CAAC,CAAC;AAAA,EAAC;AACH,MAAMC,0BAA0B,GAAGzF,MAAM,CAACQ,yBAAyB,EAAE;EACnE2D,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,gBAAgB;EACtBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACb;AAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACN,MAAMgC,wBAAwB,GAAG1F,MAAM,CAACS,uBAAuB,EAAE;EAC/D0D,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,cAAc;EACpBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACZ;AAC/C,CAAC,CAAC,CAACgC,KAAA;EAAA,IAAC;IACF/C;EACF,CAAC,GAAA+C,KAAA;EAAA,OAAKnH,QAAQ,CAAC,CAAC,CAAC,EAAE,CAACoE,UAAU,CAACG,YAAY,IAAIH,UAAU,CAACI,YAAY,KAAK;IACzE,iBAAiB,EAAE,KAAK;IACxB,qBAAqB,EAAE,KAAK;IAC5B,eAAe,EAAE;EACnB,CAAC,CAAC;AAAA,EAAC;AACH,MAAM4C,0BAA0B,GAAG5F,MAAM,CAACG,gBAAgB,EAAE;EAC1DgE,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,gBAAgB;EACtBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACX;AAC/C,CAAC,CAAC,CAACiC,KAAA;EAAA,IAAC;IACFjD;EACF,CAAC,GAAAiD,KAAA;EAAA,OAAKrH,QAAQ,CAAC;IACbsH,SAAS,EAAE;EACb,CAAC,EAAE,CAAClD,UAAU,CAACI,YAAY,IAAI;IAC7B+C,eAAe,EAAE;EACnB,CAAC,EAAE;IACDP,iBAAiB,EAAE,sCAAsC;IACzDf,UAAU,EAAE7B,UAAU,CAACE,OAAO,GAAG,SAAS,GAAG;EAC/C,CAAC,CAAC;AAAA,EAAC;AACH,MAAMkD,0BAA0B,GAAGhG,MAAM,CAACG,gBAAgB,EAAE;EAC1DgE,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,gBAAgB;EACtBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACV;AAC/C,CAAC,CAAC,CAAC;EACDiC,SAAS,EAAE,QAAQ;EACnBN,iBAAiB,EAAE,sCAAsC;EACzDO,eAAe,EAAE,8CAA8C;EAC/D,CAAC,KAAKjF,mBAAmB,CAACmF,kBAAkB,EAAE,GAAG;IAC/CC,SAAS,EAAE,gBAAgB;IAC3B,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;AACF,MAAMC,mBAAmB,GAAGnG,MAAM,CAACiB,yBAAyB,EAAE;EAC5DkD,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,SAAS;EACfC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACT;AAC/C,CAAC,CAAC,CAACsC,KAAA;EAAA,IAAC;IACFC;EACF,CAAC,GAAAD,KAAA;EAAA,OAAM;IACL;IACAE,MAAM,EAAE,gCAAgCD,KAAK,CAACE,IAAI,CAACD,MAAM,CAACE,KAAK;EACjE,CAAC;AAAA,CAAC,CAAC;AACH,MAAMC,kBAAkB,GAAGzG,MAAM,CAACkB,wBAAwB,EAAE;EAC1DiD,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,QAAQ;EACdC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACxC;AAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACN,MAAM2E,mBAAmB,GAAG1G,MAAM,CAACa,QAAQ,EAAE;EAC3CsD,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,SAAS;EACfC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACR;AAC/C,CAAC,CAAC,CAAC4C,MAAA;EAAA,IAAC;IACFN;EACF,CAAC,GAAAM,MAAA;EAAA,OAAM;IACLxD,KAAK,EAAE,CAACkD,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEO,OAAO,CAACC,IAAI,CAACC;EAC5C,CAAC;AAAA,CAAC,CAAC;AACH,MAAMC,qBAAqB,GAAG/G,MAAM,CAACa,QAAQ,EAAE;EAC7CsD,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,WAAW;EACjBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACP;AAC/C,CAAC,CAAC,CAACgD,MAAA;EAAA,IAAC;IACFX;EACF,CAAC,GAAAW,MAAA;EAAA,OAAM;IACL7D,KAAK,EAAE,CAACkD,KAAK,CAACE,IAAI,IAAIF,KAAK,EAAEO,OAAO,CAACC,IAAI,CAACC;EAC5C,CAAC;AAAA,CAAC,CAAC;AACH,MAAMG,oBAAoB,GAAGjH,MAAM,CAAC,KAAK,EAAE;EACzCmE,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE,WAAW;EACjBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACP;AAC/C,CAAC,CAAC,CAAC;EACDwB,iBAAiB,EAAE,sCAAsC;EACzD0B,gBAAgB,EAAE;AACpB,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAG,aAAanI,KAAK,CAACoI,UAAU,CAAC,SAASD,YAAYA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACrF,IAAIC,IAAI,EAAEC,cAAc,EAAEC,KAAK,EAAEC,aAAa,EAAEC,cAAc,EAAEC,kBAAkB,EAAEC,KAAK;EACzF,MAAMvD,KAAK,GAAGzE,aAAa,CAAC;IAC1ByE,KAAK,EAAE+C,OAAO;IACdlD,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACF,kBAAkB,EAAE2D,eAAe;MACnC,YAAY,EAAEC,SAAS;MACvB,iBAAiB,EAAEC,cAAc;MACjCC,SAAS;MACTC,SAAS,GAAGzJ,UAAU,KAAKA,UAAU,GAAG,aAAa4C,IAAI,CAACvB,SAAS,EAAE;QACnEqI,QAAQ,EAAE;MACZ,CAAC,CAAC,CAAC;MACHC,SAAS,GAAG,OAAO;MACnBC,SAAS,GAAG,OAAO;MACnBC,gBAAgB,GAAG,KAAK;MACxBzF,QAAQ,EAAE0F,YAAY;MACtB5E,YAAY;MACZ6E,KAAK,EAAEC,SAAS,GAAG,KAAK;MACxBC,cAAc,GAAG,MAAM;MACvBC,QAAQ,GAAG,KAAK;MAChBC,gBAAgB,GAAG1G,oBAAoB;MACvC2G,cAAc,GAAG/G,qBAAqB;MACtCgH,OAAO;MACPC,EAAE;MACFC,SAAS,GAAG,CAAC,CAAC;MACdjF,OAAO,GAAG,KAAK;MACfkF,WAAW,GAAG,UAAU;MACxB5F,QAAQ,GAAG,KAAK;MAChBc,IAAI;MACJ+E,aAAa,GAAG,YAAY;MAC5BC,QAAQ,GAAG,MAAM;MACjBC,WAAW;MACXC,SAAS,GAAG3K,kBAAkB,KAAKA,kBAAkB,GAAG,aAAa2C,IAAI,CAACtB,iBAAiB,EAAE,CAAC,CAAC,CAAC,CAAC;MACjGuJ,QAAQ,GAAG,KAAK;MAChBC,WAAW,GAAGnH,kBAAkB;MAChCoH,YAAY,EAAEC,gBAAgB;MAC9BC,UAAU;MACVC,QAAQ;MACRC,IAAI;MACJlG,cAAc;MACdN,IAAI,EAAEyG,QAAQ,GAAG,IAAI;MACrB1G,KAAK,EAAE2G,SAAS,GAAG,SAAS;MAC5B5G,OAAO,GAAG,UAAU;MACpB6G,SAAS;MACTzG,KAAK,GAAG,CAAC,CAAC;MACV0G,SAAS,GAAG,CAAC;IACf,CAAC,GAAG1F,KAAK;IACT2F,KAAK,GAAG1L,6BAA6B,CAAC+F,KAAK,EAAE3F,SAAS,CAAC;EACzD,MAAMuL,WAAW,GAAGlL,KAAK,CAACmL,UAAU,CAACnJ,kBAAkB,CAAC;EACxD,MAAMwH,KAAK,GAAG,CAACjB,IAAI,GAAG,CAACC,cAAc,GAAGH,OAAO,CAACmB,KAAK,KAAK,IAAI,GAAGhB,cAAc,GAAG0C,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC1B,KAAK,KAAK,IAAI,GAAGjB,IAAI,GAAGkB,SAAS;EAC9J,MAAMrF,IAAI,GAAG,CAACqE,KAAK,GAAG,CAACC,aAAa,GAAGL,OAAO,CAACjE,IAAI,KAAK,IAAI,GAAGsE,aAAa,GAAGwC,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC9G,IAAI,KAAK,IAAI,GAAGqE,KAAK,GAAGoC,QAAQ;EAC1J,MAAM1G,KAAK,GAAG,CAACwE,cAAc,GAAGN,OAAO,CAAClE,KAAK,KAAK,IAAI,GAAGwE,cAAc,GAAGa,KAAK,GAAG,QAAQ,GAAG,CAACZ,kBAAkB,GAAGsC,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC/G,KAAK,KAAK,IAAI,GAAGyE,kBAAkB,GAAGkC,SAAS;EAC7M,MAAMjH,QAAQ,GAAG,CAACgF,KAAK,GAAGU,YAAY,IAAI,IAAI,GAAGA,YAAY,GAAG2B,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACrH,QAAQ,KAAK,IAAI,GAAGgF,KAAK,GAAG,KAAK;EAC5I,MAAM;IACJuC,YAAY;IACZC,aAAa;IACbC,sBAAsB;IACtBC,aAAa;IACbC,WAAW;IACXC,eAAe;IACfC,cAAc;IACdC,KAAK;IACLC,KAAK;IACL3H,SAAS;IACTH,OAAO;IACP+H,UAAU;IACVC,QAAQ;IACRC,WAAW;IACXC,UAAU;IACVC;EACF,CAAC,GAAGtL,eAAe,CAACnB,QAAQ,CAAC,CAAC,CAAC,EAAE8F,KAAK,EAAE;IACtCyE,EAAE,EAAEA,EAAE,IAAI,IAAI,GAAGA,EAAE,GAAGmB,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACgB,OAAO;IACxEC,aAAa,EAAE,cAAc;IAC7BC,wBAAwB,EAAE,KAAK;IAC/BC,iCAAiC,EAAE7J;EACrC,CAAC,CAAC,CAAC;EACH,MAAM;IACJ8J,WAAW,EAAEC;EACf,CAAC,GAAGlB,aAAa,CAAC,CAAC;EACnB,MAAM;IACJmB,OAAO,EAAEC;EACX,CAAC,GAAGrB,YAAY,CAAC,CAAC;EAClB,MAAMrH,YAAY,GAAG,CAACuF,gBAAgB,IAAI,CAACzF,QAAQ,IAAI+H,KAAK,IAAI,CAACtB,QAAQ;EACzE,MAAMtG,YAAY,GAAG,CAAC,CAAC2F,QAAQ,IAAID,cAAc,KAAK,IAAI,KAAKA,cAAc,KAAK,KAAK;;EAEvF;EACA,MAAM9F,UAAU,GAAGpE,QAAQ,CAAC;IAC1BkN,aAAa,EAAErE,OAAO,CAAClE;EACzB,CAAC,EAAEmB,KAAK,EAAE;IACRqG,KAAK;IACL9H,QAAQ;IACRC,OAAO;IACP+F,cAAc;IACd8C,UAAU,EAAE,CAAC,CAACV,cAAc,CAACW,MAAM;IACnC7I,YAAY;IACZC,YAAY;IACZ6I,YAAY,EAAEhB,UAAU,KAAK,CAAC,CAAC;IAC/B5H,SAAS;IACTG,IAAI;IACJD,KAAK;IACLD;EACF,CAAC,CAAC;EACF,MAAM4I,OAAO,GAAGnJ,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMmJ,sBAAsB,GAAGvN,QAAQ,CAAC,CAAC,CAAC,EAAEyL,KAAK,EAAE;IACjDF,SAAS;IACTzG,KAAK;IACL0G;EACF,CAAC,CAAC;EACF,IAAIgC,eAAe;EACnB,IAAI3I,QAAQ,IAAIsH,KAAK,CAACiB,MAAM,GAAG,CAAC,EAAE;IAChC,MAAMK,qBAAqB,GAAG5J,MAAM,IAAI;MACtC,MAAM6J,YAAY,GAAG1B,WAAW,CAACnI,MAAM,CAAC;QACtC;UACE8J;QACF,CAAC,GAAGD,YAAY;QAChBE,QAAQ,GAAG7N,6BAA6B,CAAC2N,YAAY,EAAEtN,UAAU,CAAC;MACpE,OAAOJ,QAAQ,CAAC;QACdqE,QAAQ;QACRO,IAAI;QACJoI,OAAO,EAAEW;MACX,CAAC,EAAEC,QAAQ,CAAC;IACd,CAAC;IACD,IAAI1C,UAAU,EAAE;MACdsC,eAAe,GAAGtC,UAAU,CAACiB,KAAK,EAAEsB,qBAAqB,EAAErJ,UAAU,CAAC;IACxE,CAAC,MAAM;MACLoJ,eAAe,GAAGrB,KAAK,CAAC0B,GAAG,CAAC,CAACtK,MAAM,EAAEuK,KAAK,KAAK;QAC7C,MAAMC,qBAAqB,GAAGN,qBAAqB,CAAC;YAChDK;UACF,CAAC,CAAC;UACF;YACE5J,GAAG,EAAE8J;UACP,CAAC,GAAGD,qBAAqB;UACzBE,iBAAiB,GAAGlO,6BAA6B,CAACgO,qBAAqB,EAAE1N,UAAU,CAAC;QACtF,OAAO,aAAawC,IAAI,CAACjB,IAAI,EAAE;UAC7BgD,IAAI,EAAEA,IAAI;UACVF,OAAO,EAAE,MAAM;UACfC,KAAK,EAAE,SAAS;UAChBQ,YAAY,EAAE,aAAatC,IAAI,CAAChB,UAAU,EAAE7B,QAAQ,CAAC,CAAC,CAAC,EAAEiO,iBAAiB,CAAC,EAAED,eAAe,CAAC;UAC7FE,EAAE,EAAE;YACF5H,QAAQ,EAAE;UACZ,CAAC;UACDvC,QAAQ,EAAEsG,cAAc,CAAC9G,MAAM;QACjC,CAAC,EAAEuK,KAAK,CAAC;MACX,CAAC,CAAC;IACJ;EACF;EACA,MAAMK,OAAO,GAAGrN,UAAU,CAACgI,GAAG,EAAEyD,WAAW,CAAC;EAC5C,MAAM6B,gBAAgB,GAAG;IACvB,CAAC9L,mBAAmB,CAAC+B,QAAQ,GAAGA,QAAQ;IACxC,CAAC/B,mBAAmB,CAAC0H,KAAK,GAAGA,KAAK;IAClC,CAAC1H,mBAAmB,CAACgC,OAAO,GAAGA,OAAO;IACtC,CAAChC,mBAAmB,CAACoJ,WAAW,GAAG2C,OAAO,CAAC3C,WAAW;EACxD,CAAC;EACD,MAAM,CAAC4C,QAAQ,EAAEC,SAAS,CAAC,GAAG5L,OAAO,CAAC,MAAM,EAAE;IAC5CmG,GAAG,EAAEqF,OAAO;IACZK,SAAS,EAAE,CAAClB,OAAO,CAACvI,IAAI,EAAEqJ,gBAAgB,CAAC;IAC3CK,WAAW,EAAE/I,gBAAgB;IAC7B6H,sBAAsB;IACtBnJ,UAAU;IACVsK,YAAY,EAAE9C,YAAY;IAC1B+C,eAAe,EAAE;MACf3B,OAAO,EAAE4B,KAAK,IAAI;QAChB,IAAI3B,iBAAiB,EAAE;UACrBA,iBAAiB,CAAC2B,KAAK,CAAC;QAC1B;QACA,IAAIA,KAAK,CAACC,aAAa,KAAKD,KAAK,CAACE,MAAM,IAAI/B,oBAAoB,EAAE;UAChEA,oBAAoB,CAAC6B,KAAK,CAAC;QAC7B;MACF;IACF;EACF,CAAC,CAAC;EACF,MAAM,CAACG,WAAW,EAAEC,YAAY,CAAC,GAAGrM,OAAO,CAAC,SAAS,EAAE;IACrD6L,SAAS,EAAElB,OAAO,CAACtI,OAAO;IAC1ByJ,WAAW,EAAEtI,mBAAmB;IAChCoH,sBAAsB;IACtBnJ;EACF,CAAC,CAAC;EACF,MAAM6K,iBAAiB,GAAG;IACxB,CAAC3M,mBAAmB,CAAC+B,QAAQ,GAAGA;EAClC,CAAC;EACD,MAAM,CAAC6K,SAAS,EAAEC,UAAU,CAAC,GAAGxM,OAAO,CAAC,OAAO,EAAE;IAC/C6L,SAAS,EAAE,CAAClB,OAAO,CAACrI,KAAK,EAAEgK,iBAAiB,CAAC;IAC7CR,WAAW,EAAE5H,iBAAiB;IAC9B6H,YAAY,EAAEU,QAAQ,IAAI;MACxB,MAAMC,cAAc,GAAGxD,aAAa,CAAC,CAAC;QACpC;UACEyD,MAAM;UACNC,OAAO;UACPzC;QACF,CAAC,GAAGuC,cAAc;QAClBG,cAAc,GAAGzP,6BAA6B,CAACsP,cAAc,EAAE/O,UAAU,CAAC;MAC5E,OAAON,QAAQ,CAAC,CAAC,CAAC,EAAEwP,cAAc,EAAE;QAClCF,MAAM,EAAEV,KAAK,IAAI;UACf,IAAIa,gBAAgB;UACpBH,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACV,KAAK,CAAC;UAC/B,CAACa,gBAAgB,GAAGL,QAAQ,CAACE,MAAM,KAAK,IAAI,IAAIG,gBAAgB,CAACC,IAAI,CAACN,QAAQ,EAAER,KAAK,CAAC;QACxF,CAAC;QACDW,OAAO,EAAEX,KAAK,IAAI;UAChB,IAAIe,iBAAiB;UACrBJ,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACX,KAAK,CAAC;UACjC,CAACe,iBAAiB,GAAGP,QAAQ,CAACG,OAAO,KAAK,IAAI,IAAII,iBAAiB,CAACD,IAAI,CAACN,QAAQ,EAAER,KAAK,CAAC;QAC3F,CAAC;QACD9B,WAAW,EAAE8B,KAAK,IAAI;UACpB,IAAIgB,qBAAqB;UACzB9C,WAAW,IAAI,IAAI,IAAIA,WAAW,CAAC8B,KAAK,CAAC;UACzC,CAACgB,qBAAqB,GAAGR,QAAQ,CAACtC,WAAW,KAAK,IAAI,IAAI8C,qBAAqB,CAACF,IAAI,CAACN,QAAQ,EAAER,KAAK,CAAC;QACvG;MACF,CAAC,CAAC;IACJ,CAAC;IACDrB,sBAAsB;IACtBnJ,UAAU;IACVuK,eAAe,EAAE;MACflF,SAAS;MACTmB,WAAW;MACXjF,IAAI;MACJmF,QAAQ;MACRzG,QAAQ;MACR8G,QAAQ,EAAEA,QAAQ,IAAI,IAAI,GAAGA,QAAQ,GAAGO,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACP,QAAQ;MAC3FC,IAAI;MACJ,cAAc,EAAEpB,KAAK,IAAI6F,SAAS;MAClC,YAAY,EAAEtG,SAAS;MACvB,iBAAiB,EAAEC,cAAc;MACjC,kBAAkB,EAAEF,eAAe,IAAI,IAAI,GAAGA,eAAe,GAAGoC,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC,kBAAkB;IAC/H;EACF,CAAC,CAAC;EACF,MAAM,CAACoE,kBAAkB,EAAEC,mBAAmB,CAAC,GAAGpN,OAAO,CAAC,gBAAgB,EAAE;IAC1E6L,SAAS,EAAElB,OAAO,CAACpI,cAAc;IACjCuJ,WAAW,EAAExH,0BAA0B;IACvCsG,sBAAsB;IACtBnJ;EACF,CAAC,CAAC;EACF,MAAM,CAAC4L,gBAAgB,EAAE/B,iBAAiB,CAAC,GAAGtL,OAAO,CAAC,cAAc,EAAE;IACpE6L,SAAS,EAAElB,OAAO,CAACnI,YAAY;IAC/BsJ,WAAW,EAAEvH,wBAAwB;IACrCqG,sBAAsB;IACtBnJ;EACF,CAAC,CAAC;EACF,MAAM,CAAC6L,kBAAkB,EAAEC,mBAAmB,CAAC,GAAGvN,OAAO,CAAC,gBAAgB,EAAE;IAC1E6L,SAAS,EAAElB,OAAO,CAAClI,cAAc;IACjCqJ,WAAW,EAAErH,0BAA0B;IACvCsH,YAAY,EAAE3C,aAAa;IAC3BwB,sBAAsB;IACtBnJ,UAAU;IACV+L,iBAAiB,EAAEC,WAAW,KAAK;MACjCxL,IAAI,EAAEwL,WAAW,CAACxL,IAAI,IAAIA,IAAI;MAC9BF,OAAO,EAAE0L,WAAW,CAAC1L,OAAO,IAAIhD,uBAAuB,CAACgD,OAAO,EAAEC,KAAK,CAAC,CAACD,OAAO,IAAI,OAAO;MAC1FC,KAAK,EAAEyL,WAAW,CAACzL,KAAK,IAAIjD,uBAAuB,CAACgD,OAAO,EAAEC,KAAK,CAAC,CAACA,KAAK,IAAI,SAAS;MACtF0L,qBAAqB,EAAE,CAAC,CAACxH,OAAO,CAAClE;IACnC,CAAC,CAAC;IACFgK,eAAe,EAAE;MACf,YAAY,EAAE/E,SAAS;MACvB0G,KAAK,EAAE1G;IACT;EACF,CAAC,CAAC;EACF,MAAM,CAAC2G,kBAAkB,EAAEC,mBAAmB,CAAC,GAAG7N,OAAO,CAAC,gBAAgB,EAAE;IAC1E6L,SAAS,EAAElB,OAAO,CAACjI,cAAc;IACjCoJ,WAAW,EAAEjH,0BAA0B;IACvCkH,YAAY,EAAE5C,sBAAsB;IACpCyB,sBAAsB;IACtBnJ,UAAU;IACV+L,iBAAiB,EAAEC,WAAW,KAAK;MACjCxL,IAAI,EAAEwL,WAAW,CAACxL,IAAI,IAAIA,IAAI;MAC9BF,OAAO,EAAE0L,WAAW,CAAC1L,OAAO,IAAIhD,uBAAuB,CAACgD,OAAO,EAAEC,KAAK,CAAC,CAACD,OAAO,IAAI,OAAO;MAC1FC,KAAK,EAAEyL,WAAW,CAACzL,KAAK,IAAIjD,uBAAuB,CAACgD,OAAO,EAAEC,KAAK,CAAC,CAACA,KAAK,IAAI,SAAS;MACtF0L,qBAAqB,EAAE,CAAC,CAACxH,OAAO,CAAClE;IACnC,CAAC,CAAC;IACFgK,eAAe,EAAE;MACftK,QAAQ;MACR,YAAY,EAAEI,SAAS,GAAGoF,SAAS,GAAGc,QAAQ;MAC9C2F,KAAK,EAAE7L,SAAS,GAAGoF,SAAS,GAAGc,QAAQ;MACvCS,IAAI,EAAE;IACR;EACF,CAAC,CAAC;EACF,MAAM,CAACqF,WAAW,EAAEC,YAAY,CAAC,GAAG/N,OAAO,CAAC,SAAS,EAAE;IACrD6L,SAAS,EAAElB,OAAO,CAAChI,OAAO;IAC1BmJ,WAAW,EAAE9G,mBAAmB;IAChC+G,YAAY,EAAEzC,eAAe;IAC7BsB,sBAAsB;IACtBnJ,UAAU;IACV+L,iBAAiB,EAAEC,WAAW,KAAK;MACjCxL,IAAI,EAAEwL,WAAW,CAACxL,IAAI,IAAIA,IAAI;MAC9BF,OAAO,EAAE0L,WAAW,CAAC1L,OAAO,IAAIA,OAAO;MACvCC,KAAK,EAAEyL,WAAW,CAACzL,KAAK,IAAIA,KAAK;MACjC0L,qBAAqB,EAAE,CAACD,WAAW,CAACO;IACtC,CAAC,CAAC;IACFhC,eAAe,EAAE;MACfrC,QAAQ;MACRsE,IAAI,EAAEnM,SAAS;MACfoM,KAAK,EAAEvE,QAAQ,GAAG;QAChBwE,KAAK,EAAExE,QAAQ,CAACyE;MAClB,CAAC,GAAG,CAAC;IACP;EACF,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,YAAY,CAAC,GAAGtO,OAAO,CAAC,SAAS,EAAE;IACrD6L,SAAS,EAAElB,OAAO,CAAC/H,OAAO;IAC1BkJ,WAAW,EAAEvG,mBAAmB;IAChCqF,sBAAsB;IACtBnJ;EACF,CAAC,CAAC;EACF,MAAM,CAAC8M,aAAa,EAAEC,cAAc,CAAC,GAAGxO,OAAO,CAAC,WAAW,EAAE;IAC3D6L,SAAS,EAAElB,OAAO,CAAC9H,SAAS;IAC5BiJ,WAAW,EAAElG,qBAAqB;IAClCgF,sBAAsB;IACtBnJ,UAAU;IACVuK,eAAe,EAAE;MACfyC,IAAI,EAAE,cAAc;MACpBtE,WAAW,EAAE8B,KAAK,IAAI;QACpB;QACAA,KAAK,CAACyC,cAAc,CAAC,CAAC;MACxB;IACF;EACF,CAAC,CAAC;EACF,MAAM,CAACC,YAAY,EAAEC,aAAa,CAAC,GAAG5O,OAAO,CAAC,UAAU,EAAE;IACxD6L,SAAS,EAAElB,OAAO,CAAC7H,QAAQ;IAC3BgJ,WAAW,EAAEhG,oBAAoB;IACjC8E,sBAAsB;IACtBnJ;EACF,CAAC,CAAC;EACF,IAAIoG,SAAS,GAAG,CAAC,CAAC,IAAIgH,KAAK,CAACC,OAAO,CAACjE,eAAe,CAAC,EAAE;IACpD,MAAM7J,IAAI,GAAG6J,eAAe,CAACJ,MAAM,GAAG5C,SAAS;IAC/C,IAAI,CAAClG,OAAO,IAAIX,IAAI,GAAG,CAAC,EAAE;MACxB6J,eAAe,GAAGA,eAAe,CAACkE,MAAM,CAAC,CAAC,EAAElH,SAAS,CAAC;MACtDgD,eAAe,CAACmE,IAAI,CAAE,aAAa9O,IAAI,CAACyO,YAAY,EAAEtR,QAAQ,CAAC,CAAC,CAAC,EAAEuR,aAAa,EAAE;QAChFxN,QAAQ,EAAEqG,gBAAgB,CAACzG,IAAI;MACjC,CAAC,CAAC,EAAE6J,eAAe,CAACJ,MAAM,CAAC,CAAC;IAC9B;EACF;EACA,MAAM,CAACwE,UAAU,EAAEC,eAAe,CAAC,GAAGlP,OAAO,CAAC,QAAQ,EAAE;IACtD6L,SAAS,EAAElB,OAAO,CAAC/J,MAAM;IACzBkL,WAAW,EAAExG,kBAAkB;IAC/BsF,sBAAsB;IACtBnJ,UAAU;IACV+L,iBAAiB,EAAEC,WAAW,KAAK;MACjC1L,OAAO,EAAE0L,WAAW,CAAC1L,OAAO,IAAIhD,uBAAuB,CAACgD,OAAO,EAAEC,KAAK,CAAC,CAACD,OAAO,IAAI,OAAO;MAC1FC,KAAK,EAAEyL,WAAW,CAACzL,KAAK,IAAIjD,uBAAuB,CAACgD,OAAO,EAAEC,KAAK,CAAC,CAACA,KAAK,IAAI,SAAS;MACtF0L,qBAAqB,EAAE,CAACK,YAAY,CAACC;IACvC,CAAC,CAAC;IACFhC,eAAe,EAAE;MACfmD,EAAE,EAAE;IACN;EACF,CAAC,CAAC;EACF,MAAMC,mBAAmB,GAAGA,CAACC,WAAW,EAAEzO,MAAM,KAAK;IACnD,MAAM;QACFW;MACF,CAAC,GAAG8N,WAAW;MACfC,IAAI,GAAGlS,6BAA6B,CAACiS,WAAW,EAAEzR,UAAU,CAAC;IAC/D,OAAO,aAAasC,IAAI,CAAC+O,UAAU,EAAE5R,QAAQ,CAAC,CAAC,CAAC,EAAEiS,IAAI,EAAE;MACtDlO,QAAQ,EAAEsG,cAAc,CAAC9G,MAAM;IACjC,CAAC,CAAC,EAAEW,GAAG,CAAC;EACV,CAAC;EACD,MAAM8G,YAAY,GAAGC,gBAAgB,IAAI8G,mBAAmB;EAC5D,MAAMG,gBAAgB,GAAGA,CAAC3O,MAAM,EAAEuK,KAAK,KAAK;IAC1C,MAAMkE,WAAW,GAAG9F,cAAc,CAAC;MACjC3I,MAAM;MACNuK;IACF,CAAC,CAAC;IACF,OAAO9C,YAAY,CAAChL,QAAQ,CAAC,CAAC,CAAC,EAAE6R,eAAe,EAAEG,WAAW,CAAC,EAAEzO,MAAM,EAAE;MACtE;MACA4O,QAAQ,EAAE,CAAC,CAACH,WAAW,CAAC,eAAe,CAAC;MACxCxF,UAAU;MACVpI;IACF,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMgO,SAAS,GAAG5R,KAAK,CAAC6R,OAAO,CAAC,MAAM,CAAC;IACrC1M,IAAI,EAAE,QAAQ;IACd2M,OAAO,EAAE;MACPC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC;IACf;EACF,CAAC,EAAE,IAAI7B,YAAY,CAAC0B,SAAS,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC1B,YAAY,CAAC0B,SAAS,CAAC,CAAC;EAChE,IAAIpK,KAAK,GAAG,IAAI;EAChB,IAAIsE,QAAQ,EAAE;IACZ,IAAIkG,YAAY;IAChBxK,KAAK,GAAG,aAAanF,IAAI,CAACpB,oBAAoB,EAAE;MAC9CiD,OAAO,EAAEA,OAAO;MAChBC,KAAK,EAAEA,KAAK;MACZZ,QAAQ,EAAE,aAAalB,IAAI,CAACV,YAAY,EAAE;QACxC2B,MAAM,EAAE,IAAI;QACZC,QAAQ,EAAE,aAAahB,KAAK,CAAC0N,WAAW,EAAEzQ,QAAQ,CAAC,CAAC,CAAC,EAAE0Q,YAAY,EAAE;UACnElC,SAAS,EAAE9N,IAAI,CAACgQ,YAAY,CAAClC,SAAS;UACtC;UAAA;;UAEA4D,SAAS,EAAEA;QACb,CAAC,EAAE,EAAE,CAACI,YAAY,GAAG1M,KAAK,CAAChB,KAAK,KAAK,IAAI,IAAI0N,YAAY,CAAClN,OAAO,CAAC,IAAI;UACpEwM,EAAE,EAAE1Q,MAAM;UACV0D,KAAK,EAAE;YACLC,IAAI,EAAE2L,YAAY,CAACoB,EAAE,IAAI;UAC3B;QACF,CAAC,EAAE;UACD/N,QAAQ,EAAE,CAAC0I,cAAc,CAACoB,GAAG,CAAC,CAACtK,MAAM,EAAEuK,KAAK,KAAK;YAC/C,IAAIxD,OAAO,EAAE;cACX,MAAMmI,WAAW,GAAGlP,MAAM;cAC1B,OAAOwH,WAAW,CAAC;gBACjB7G,GAAG,EAAEwO,MAAM,CAACD,WAAW,CAACvO,GAAG,CAAC;gBAC5BD,KAAK,EAAEwO,WAAW,CAACxO,KAAK;gBACxBF,QAAQ,EAAE0O,WAAW,CAACH,OAAO,CAACzE,GAAG,CAAC,CAAC8E,OAAO,EAAEC,MAAM,KAAKV,gBAAgB,CAACS,OAAO,EAAEF,WAAW,CAAC3E,KAAK,GAAG8E,MAAM,CAAC;cAC9G,CAAC,CAAC;YACJ;YACA,OAAOV,gBAAgB,CAAC3O,MAAM,EAAEuK,KAAK,CAAC;UACxC,CAAC,CAAC,EAAEvI,OAAO,IAAIkH,cAAc,CAACW,MAAM,KAAK,CAAC,GAAG,aAAavK,IAAI,CAACmO,WAAW,EAAEhR,QAAQ,CAAC,CAAC,CAAC,EAAEiR,YAAY,EAAE;YACrGlN,QAAQ,EAAE0G;UACZ,CAAC,CAAC,CAAC,GAAG,IAAI,EAAEgC,cAAc,CAACW,MAAM,KAAK,CAAC,IAAI,CAACjD,QAAQ,IAAI,CAAC5E,OAAO,GAAG,aAAa1C,IAAI,CAACqO,aAAa,EAAElR,QAAQ,CAAC,CAAC,CAAC,EAAEmR,cAAc,EAAE;YAC/HpN,QAAQ,EAAE2G;UACZ,CAAC,CAAC,CAAC,GAAG,IAAI;QACZ,CAAC,CAAC;MACJ,CAAC;IACH,CAAC,CAAC;EACJ;EACA,OAAO,aAAa3H,KAAK,CAACvC,KAAK,CAACqS,QAAQ,EAAE;IACxC9O,QAAQ,EAAE,CAAC,aAAahB,KAAK,CAACuL,QAAQ,EAAEtO,QAAQ,CAAC,CAAC,CAAC,EAAEuO,SAAS,EAAE;MAC9DxK,QAAQ,EAAE,CAACmB,cAAc,IAAI,aAAarC,IAAI,CAACiN,kBAAkB,EAAE9P,QAAQ,CAAC,CAAC,CAAC,EAAE+P,mBAAmB,EAAE;QACnGhM,QAAQ,EAAEmB;MACZ,CAAC,CAAC,CAAC,EAAE,aAAanC,KAAK,CAACgM,WAAW,EAAE/O,QAAQ,CAAC,CAAC,CAAC,EAAEgP,YAAY,EAAE;QAC9DjL,QAAQ,EAAE,CAACyJ,eAAe,EAAE,aAAa3K,IAAI,CAACqM,SAAS,EAAElP,QAAQ,CAAC,CAAC,CAAC,EAAEmP,UAAU,CAAC,CAAC;MACpF,CAAC,CAAC,CAAC,EAAEhK,YAAY,IAAI,aAAatC,IAAI,CAACmN,gBAAgB,EAAEhQ,QAAQ,CAAC,CAAC,CAAC,EAAEiO,iBAAiB,EAAE;QACvFlK,QAAQ,EAAEoB;MACZ,CAAC,CAAC,CAAC,EAAEZ,YAAY,GAAG,aAAa1B,IAAI,CAACoN,kBAAkB,EAAEjQ,QAAQ,CAAC,CAAC,CAAC,EAAEkQ,mBAAmB,EAAE;QAC1FnM,QAAQ,EAAE2F;MACZ,CAAC,CAAC,CAAC,GAAG,IAAI,EAAElF,YAAY,GAAG,aAAa3B,IAAI,CAAC0N,kBAAkB,EAAEvQ,QAAQ,CAAC,CAAC,CAAC,EAAEwQ,mBAAmB,EAAE;QACjGzM,QAAQ,EAAE8G;MACZ,CAAC,CAAC,CAAC,GAAG,IAAI;IACZ,CAAC,CAAC,CAAC,EAAE7C,KAAK;EACZ,CAAC,CAAC;AACJ,CAAC,CAAC;AACF8K,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGrK,YAAY,CAACsK,SAAS,CAAC,yBAAyB;EACtF;EACA;EACA;EACA;EACA;AACF;AACA;AACA;EACE,kBAAkB,EAAExS,SAAS,CAACyS,MAAM;EACpC;AACF;AACA;AACA;EACE,YAAY,EAAEzS,SAAS,CAACyS,MAAM;EAC9B;AACF;AACA;AACA;EACE,iBAAiB,EAAEzS,SAAS,CAACyS,MAAM;EACnC;AACF;AACA;AACA;AACA;AACA;EACEC,YAAY,EAAE1S,SAAS,CAAC2S,IAAI;EAC5B;AACF;AACA;EACE3J,SAAS,EAAEhJ,SAAS,CAAC2S,IAAI;EACzB;AACF;AACA;AACA;EACEC,aAAa,EAAE5S,SAAS,CAAC2S,IAAI;EAC7B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEE,UAAU,EAAE7S,SAAS,CAAC2S,IAAI;EAC1B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEG,YAAY,EAAE9S,SAAS,CAAC+S,SAAS,CAAC,CAAC/S,SAAS,CAACgT,KAAK,CAAC,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC,EAAEhT,SAAS,CAAC2S,IAAI,CAAC,CAAC;EACxF;AACF;AACA;AACA;EACE1J,SAAS,EAAEjJ,SAAS,CAACiT,IAAI;EACzB;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,WAAW,EAAElT,SAAS,CAAC2S,IAAI;EAC3B;AACF;AACA;AACA;EACEQ,aAAa,EAAEnT,SAAS,CAAC2S,IAAI;EAC7B;AACF;AACA;AACA;AACA;AACA;EACExJ,SAAS,EAAEnJ,SAAS,CAACyS,MAAM;EAC3B;AACF;AACA;AACA;AACA;AACA;EACErJ,SAAS,EAAEpJ,SAAS,CAACyS,MAAM;EAC3B;AACF;AACA;AACA;EACEvO,KAAK,EAAElE,SAAS,CAACgT,KAAK,CAAC,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;EAC9E;AACF;AACA;AACA;EACEI,YAAY,EAAElT,cAAc,CAACF,SAAS,CAACqT,GAAG,EAAEhO,KAAK,IAAI;IACnD,IAAIA,KAAK,CAACjB,QAAQ,IAAIiB,KAAK,CAAC+N,YAAY,KAAKhE,SAAS,IAAI,CAAC2B,KAAK,CAACC,OAAO,CAAC3L,KAAK,CAAC+N,YAAY,CAAC,EAAE;MAC5F,OAAO,IAAIE,KAAK,CAAC,CAAC,2GAA2G,EAAE,YAAYjO,KAAK,CAAC+N,YAAY,gBAAgB,CAAC,CAACG,IAAI,CAAC,IAAI,CAAC,CAAC;IAC5L;IACA,OAAO,IAAI;EACb,CAAC,CAAC;EACF;AACF;AACA;AACA;EACElK,gBAAgB,EAAErJ,SAAS,CAAC2S,IAAI;EAChC;AACF;AACA;AACA;EACEa,oBAAoB,EAAExT,SAAS,CAAC2S,IAAI;EACpC;AACF;AACA;AACA;EACE/O,QAAQ,EAAE5D,SAAS,CAAC2S,IAAI;EACxB;AACF;AACA;AACA;EACEc,sBAAsB,EAAEzT,SAAS,CAAC2S,IAAI;EACtC;AACF;AACA;AACA;EACEe,eAAe,EAAE1T,SAAS,CAAC2S,IAAI;EAC/B;AACF;AACA;EACEjO,YAAY,EAAE1E,SAAS,CAACiT,IAAI;EAC5B;AACF;AACA;AACA;AACA;EACE1J,KAAK,EAAEvJ,SAAS,CAAC2S,IAAI;EACrB;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEgB,aAAa,EAAE3T,SAAS,CAAC4T,IAAI;EAC7B;AACF;AACA;AACA;EACEC,qBAAqB,EAAE7T,SAAS,CAAC2S,IAAI;EACrC;AACF;AACA;AACA;EACElJ,cAAc,EAAEzJ,SAAS,CAAC+S,SAAS,CAAC,CAAC/S,SAAS,CAACgT,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,EAAEhT,SAAS,CAAC2S,IAAI,CAAC,CAAC;EAChF;AACF;AACA;AACA;EACEjJ,QAAQ,EAAE1J,SAAS,CAAC2S,IAAI;EACxB;AACF;AACA;AACA;AACA;AACA;AACA;EACEhJ,gBAAgB,EAAE3J,SAAS,CAAC4T,IAAI;EAChC;AACF;AACA;AACA;AACA;AACA;EACEE,iBAAiB,EAAE9T,SAAS,CAAC4T,IAAI;EACjC;AACF;AACA;AACA;AACA;AACA;AACA;EACEG,YAAY,EAAE/T,SAAS,CAAC4T,IAAI;EAC5B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEhK,cAAc,EAAE5J,SAAS,CAAC4T,IAAI;EAC9B;AACF;AACA;AACA;AACA;AACA;AACA;EACE/J,OAAO,EAAE7J,SAAS,CAAC4T,IAAI;EACvB;AACF;AACA;AACA;AACA;EACEI,iBAAiB,EAAEhU,SAAS,CAAC2S,IAAI;EACjC;AACF;AACA;AACA;EACE7I,EAAE,EAAE9J,SAAS,CAACyS,MAAM;EACpB;AACF;AACA;AACA;EACEwB,kBAAkB,EAAEjU,SAAS,CAAC2S,IAAI;EAClC;AACF;AACA;EACE5G,UAAU,EAAE/L,SAAS,CAACyS,MAAM;EAC5B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEyB,oBAAoB,EAAElU,SAAS,CAAC4T,IAAI;EACpC;AACF;AACA;AACA;AACA;EACE7J,SAAS,EAAE5J,eAAe;EAC1B;AACF;AACA;AACA;AACA;EACE2E,OAAO,EAAE9E,SAAS,CAAC2S,IAAI;EACvB;AACF;AACA;AACA;AACA;AACA;EACE3I,WAAW,EAAEhK,SAAS,CAACiT,IAAI;EAC3B;AACF;AACA;AACA;EACE7O,QAAQ,EAAEpE,SAAS,CAAC2S,IAAI;EACxB;AACF;AACA;EACEzN,IAAI,EAAElF,SAAS,CAACyS,MAAM;EACtB;AACF;AACA;AACA;AACA;AACA;EACExI,aAAa,EAAEjK,SAAS,CAACiT,IAAI;EAC7B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEkB,QAAQ,EAAEnU,SAAS,CAAC4T,IAAI;EACxB;AACF;AACA;AACA;AACA;AACA;AACA;EACEQ,OAAO,EAAEpU,SAAS,CAAC4T,IAAI;EACvB;AACF;AACA;AACA;AACA;AACA;AACA;EACES,iBAAiB,EAAErU,SAAS,CAAC4T,IAAI;EACjC;AACF;AACA;AACA;AACA;AACA;AACA;EACEU,aAAa,EAAEtU,SAAS,CAAC4T,IAAI;EAC7B;AACF;AACA;EACEW,SAAS,EAAEvU,SAAS,CAAC4T,IAAI;EACzB;AACF;AACA;AACA;AACA;AACA;EACEY,MAAM,EAAExU,SAAS,CAAC4T,IAAI;EACtB;AACF;AACA;EACEzD,IAAI,EAAEnQ,SAAS,CAAC2S,IAAI;EACpB;AACF;AACA;AACA;EACE8B,WAAW,EAAEzU,SAAS,CAAC2S,IAAI;EAC3B;AACF;AACA;AACA;AACA;AACA;EACEzI,QAAQ,EAAElK,SAAS,CAACyS,MAAM;EAC1B;AACF;AACA;EACEZ,OAAO,EAAE7R,SAAS,CAAC0U,KAAK,CAACC,UAAU;EACnC;AACF;AACA;EACExK,WAAW,EAAEnK,SAAS,CAACyS,MAAM;EAC7B;AACF;AACA;AACA;EACErI,SAAS,EAAEpK,SAAS,CAACiT,IAAI;EACzB;AACF;AACA;AACA;EACE5I,QAAQ,EAAErK,SAAS,CAAC2S,IAAI;EACxB;AACF;AACA;AACA;AACA;AACA;EACErI,WAAW,EAAEtK,SAAS,CAAC4T,IAAI;EAC3B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACErJ,YAAY,EAAEvK,SAAS,CAAC4T,IAAI;EAC5B;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACEnJ,UAAU,EAAEzK,SAAS,CAAC4T,IAAI;EAC1B;AACF;AACA;AACA;EACElJ,QAAQ,EAAE1K,SAAS,CAAC2S,IAAI;EACxB;AACF;AACA;AACA;AACA;EACEiC,aAAa,EAAE5U,SAAS,CAAC2S,IAAI;EAC7B;AACF;AACA;AACA;EACExO,IAAI,EAAEnE,SAAS,CAAC,sCAAsC+S,SAAS,CAAC,CAAC/S,SAAS,CAACgT,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAEhT,SAAS,CAACyS,MAAM,CAAC,CAAC;EACxH;AACF;AACA;AACA;EACE1H,SAAS,EAAE/K,SAAS,CAAC6U,KAAK,CAAC;IACzBlQ,cAAc,EAAE3E,SAAS,CAAC+S,SAAS,CAAC,CAAC/S,SAAS,CAAC4T,IAAI,EAAE5T,SAAS,CAAC8U,MAAM,CAAC,CAAC;IACvEpQ,YAAY,EAAE1E,SAAS,CAAC+S,SAAS,CAAC,CAAC/S,SAAS,CAAC4T,IAAI,EAAE5T,SAAS,CAAC8U,MAAM,CAAC,CAAC;IACrEtQ,KAAK,EAAExE,SAAS,CAAC+S,SAAS,CAAC,CAAC/S,SAAS,CAAC4T,IAAI,EAAE5T,SAAS,CAAC8U,MAAM,CAAC,CAAC;IAC9D9P,QAAQ,EAAEhF,SAAS,CAAC+S,SAAS,CAAC,CAAC/S,SAAS,CAAC4T,IAAI,EAAE5T,SAAS,CAAC8U,MAAM,CAAC,CAAC;IACjEjQ,OAAO,EAAE7E,SAAS,CAAC+S,SAAS,CAAC,CAAC/S,SAAS,CAAC4T,IAAI,EAAE5T,SAAS,CAAC8U,MAAM,CAAC,CAAC;IAChEhQ,OAAO,EAAE9E,SAAS,CAAC+S,SAAS,CAAC,CAAC/S,SAAS,CAAC4T,IAAI,EAAE5T,SAAS,CAAC8U,MAAM,CAAC,CAAC;IAChE/P,SAAS,EAAE/E,SAAS,CAAC+S,SAAS,CAAC,CAAC/S,SAAS,CAAC4T,IAAI,EAAE5T,SAAS,CAAC8U,MAAM,CAAC,CAAC;IAClEhS,MAAM,EAAE9C,SAAS,CAAC+S,SAAS,CAAC,CAAC/S,SAAS,CAAC4T,IAAI,EAAE5T,SAAS,CAAC8U,MAAM,CAAC,CAAC;IAC/DlQ,cAAc,EAAE5E,SAAS,CAAC+S,SAAS,CAAC,CAAC/S,SAAS,CAAC4T,IAAI,EAAE5T,SAAS,CAAC8U,MAAM,CAAC,CAAC;IACvExQ,IAAI,EAAEtE,SAAS,CAAC+S,SAAS,CAAC,CAAC/S,SAAS,CAAC4T,IAAI,EAAE5T,SAAS,CAAC8U,MAAM,CAAC,CAAC;IAC7DrQ,cAAc,EAAEzE,SAAS,CAAC+S,SAAS,CAAC,CAAC/S,SAAS,CAAC4T,IAAI,EAAE5T,SAAS,CAAC8U,MAAM,CAAC,CAAC;IACvEvQ,OAAO,EAAEvE,SAAS,CAAC+S,SAAS,CAAC,CAAC/S,SAAS,CAAC4T,IAAI,EAAE5T,SAAS,CAAC8U,MAAM,CAAC;EACjE,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEzQ,KAAK,EAAErE,SAAS,CAAC6U,KAAK,CAAC;IACrBlQ,cAAc,EAAE3E,SAAS,CAACgO,WAAW;IACrCtJ,YAAY,EAAE1E,SAAS,CAACgO,WAAW;IACnCxJ,KAAK,EAAExE,SAAS,CAACgO,WAAW;IAC5BhJ,QAAQ,EAAEhF,SAAS,CAACgO,WAAW;IAC/BnJ,OAAO,EAAE7E,SAAS,CAACgO,WAAW;IAC9BlJ,OAAO,EAAE9E,SAAS,CAACgO,WAAW;IAC9BjJ,SAAS,EAAE/E,SAAS,CAACgO,WAAW;IAChClL,MAAM,EAAE9C,SAAS,CAACgO,WAAW;IAC7BpJ,cAAc,EAAE5E,SAAS,CAACgO,WAAW;IACrC1J,IAAI,EAAEtE,SAAS,CAACgO,WAAW;IAC3BvJ,cAAc,EAAEzE,SAAS,CAACgO,WAAW;IACrCzJ,OAAO,EAAEvE,SAAS,CAACgO;EACrB,CAAC,CAAC;EACF;AACF;AACA;EACEvJ,cAAc,EAAEzE,SAAS,CAACiT,IAAI;EAC9B;AACF;AACA;EACExF,EAAE,EAAEzN,SAAS,CAAC+S,SAAS,CAAC,CAAC/S,SAAS,CAAC+U,OAAO,CAAC/U,SAAS,CAAC+S,SAAS,CAAC,CAAC/S,SAAS,CAAC4T,IAAI,EAAE5T,SAAS,CAAC8U,MAAM,EAAE9U,SAAS,CAAC2S,IAAI,CAAC,CAAC,CAAC,EAAE3S,SAAS,CAAC4T,IAAI,EAAE5T,SAAS,CAAC8U,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;EACEnK,IAAI,EAAE3K,SAAS,CAACyS,MAAM;EACtB;AACF;AACA;AACA;AACA;AACA;EACE/G,KAAK,EAAExL,cAAc,CAACF,SAAS,CAACqT,GAAG,EAAEhO,KAAK,IAAI;IAC5C,IAAIA,KAAK,CAACjB,QAAQ,IAAIiB,KAAK,CAACqG,KAAK,KAAK0D,SAAS,IAAI,CAAC2B,KAAK,CAACC,OAAO,CAAC3L,KAAK,CAACqG,KAAK,CAAC,EAAE;MAC9E,OAAO,IAAI4H,KAAK,CAAC,CAAC,oGAAoG,EAAE,YAAYjO,KAAK,CAACqG,KAAK,gBAAgB,CAAC,CAAC6H,IAAI,CAAC,IAAI,CAAC,CAAC;IAC9K;IACA,OAAO,IAAI;EACb,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEtP,OAAO,EAAEjE,SAAS,CAACgT,KAAK,CAAC,CAAC,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;AACjE,CAAC,GAAG,KAAK,CAAC;AACV,eAAe9K,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}