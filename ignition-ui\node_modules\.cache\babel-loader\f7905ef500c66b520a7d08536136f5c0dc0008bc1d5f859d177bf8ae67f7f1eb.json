{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"component\", \"children\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport { useThemeProps } from '../styles';\nimport styled from '../styles/styled';\nimport { getCardCoverUtilityClass } from './cardCoverClasses';\nimport useSlot from '../utils/useSlot';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = () => {\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getCardCoverUtilityClass, {});\n};\nconst CardCoverRoot = styled('div', {\n  name: 'JoyCardCover',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({\n  position: 'absolute',\n  zIndex: 0,\n  top: 0,\n  left: 0,\n  right: 0,\n  bottom: 0,\n  borderRadius: 'var(--CardCover-radius)',\n  // use data-attribute instead of :first-child to support zero config SSR (emotion)\n  // use nested selector for integrating with nextjs image `fill` layout (spans are inserted on top of the img)\n  '& [data-first-child]': {\n    display: 'flex',\n    justifyContent: 'center',\n    alignItems: 'center',\n    width: '100%',\n    height: '100%',\n    objectFit: 'cover',\n    boxSizing: 'border-box',\n    borderRadius: 'var(--CardCover-radius)',\n    margin: 0,\n    padding: 0,\n    '& > img': {\n      // support art-direction that uses <picture><img /></picture>\n      width: '100%',\n      height: '100%',\n      objectFit: 'cover'\n    }\n  }\n});\n/**\n *\n * Demos:\n *\n * - [Card](https://mui.com/joy-ui/react-card/)\n *\n * API:\n *\n * - [CardCover API](https://mui.com/joy-ui/api/card-cover/)\n */\nconst CardCover = /*#__PURE__*/React.forwardRef(function CardCover(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyCardCover'\n  });\n  const {\n      className,\n      component = 'div',\n      children,\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    component\n  });\n  const classes = useUtilityClasses();\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    className: clsx(classes.root, className),\n    elementType: CardCoverRoot,\n    externalForwardedProps,\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(SlotRoot, _extends({}, rootProps, {\n    children: React.Children.map(children, (child, index) => index === 0 && /*#__PURE__*/React.isValidElement(child) ? /*#__PURE__*/React.cloneElement(child, {\n      'data-first-child': ''\n    }) : child)\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? CardCover.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Used to render icon or text elements inside the CardCover if `src` is not set.\n   * This can be an element, or just a string.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default CardCover;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "clsx", "PropTypes", "unstable_composeClasses", "composeClasses", "useThemeProps", "styled", "getCardCoverUtilityClass", "useSlot", "jsx", "_jsx", "useUtilityClasses", "slots", "root", "CardCoverRoot", "name", "slot", "overridesResolver", "props", "styles", "position", "zIndex", "top", "left", "right", "bottom", "borderRadius", "display", "justifyContent", "alignItems", "width", "height", "objectFit", "boxSizing", "margin", "padding", "CardCover", "forwardRef", "inProps", "ref", "className", "component", "children", "slotProps", "other", "ownerState", "classes", "externalForwardedProps", "SlotRoot", "rootProps", "elementType", "Children", "map", "child", "index", "isValidElement", "cloneElement", "process", "env", "NODE_ENV", "propTypes", "node", "string", "shape", "oneOfType", "func", "object", "sx", "arrayOf", "bool"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/CardCover/CardCover.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"component\", \"children\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport { useThemeProps } from '../styles';\nimport styled from '../styles/styled';\nimport { getCardCoverUtilityClass } from './cardCoverClasses';\nimport useSlot from '../utils/useSlot';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = () => {\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getCardCoverUtilityClass, {});\n};\nconst CardCoverRoot = styled('div', {\n  name: 'JoyCardCover',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({\n  position: 'absolute',\n  zIndex: 0,\n  top: 0,\n  left: 0,\n  right: 0,\n  bottom: 0,\n  borderRadius: 'var(--CardCover-radius)',\n  // use data-attribute instead of :first-child to support zero config SSR (emotion)\n  // use nested selector for integrating with nextjs image `fill` layout (spans are inserted on top of the img)\n  '& [data-first-child]': {\n    display: 'flex',\n    justifyContent: 'center',\n    alignItems: 'center',\n    width: '100%',\n    height: '100%',\n    objectFit: 'cover',\n    boxSizing: 'border-box',\n    borderRadius: 'var(--CardCover-radius)',\n    margin: 0,\n    padding: 0,\n    '& > img': {\n      // support art-direction that uses <picture><img /></picture>\n      width: '100%',\n      height: '100%',\n      objectFit: 'cover'\n    }\n  }\n});\n/**\n *\n * Demos:\n *\n * - [Card](https://mui.com/joy-ui/react-card/)\n *\n * API:\n *\n * - [CardCover API](https://mui.com/joy-ui/api/card-cover/)\n */\nconst CardCover = /*#__PURE__*/React.forwardRef(function CardCover(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyCardCover'\n  });\n  const {\n      className,\n      component = 'div',\n      children,\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    component\n  });\n  const classes = useUtilityClasses();\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    className: clsx(classes.root, className),\n    elementType: CardCoverRoot,\n    externalForwardedProps,\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(SlotRoot, _extends({}, rootProps, {\n    children: React.Children.map(children, (child, index) => index === 0 && /*#__PURE__*/React.isValidElement(child) ? /*#__PURE__*/React.cloneElement(child, {\n      'data-first-child': ''\n    }) : child)\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? CardCover.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Used to render icon or text elements inside the CardCover if `src` is not set.\n   * This can be an element, or just a string.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default CardCover;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,CAAC;AAC9E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,WAAW;AACrE,SAASC,aAAa,QAAQ,WAAW;AACzC,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,wBAAwB,QAAQ,oBAAoB;AAC7D,OAAOC,OAAO,MAAM,kBAAkB;AACtC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAC9B,MAAMC,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,OAAOT,cAAc,CAACQ,KAAK,EAAEL,wBAAwB,EAAE,CAAC,CAAC,CAAC;AAC5D,CAAC;AACD,MAAMO,aAAa,GAAGR,MAAM,CAAC,KAAK,EAAE;EAClCS,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC/C,CAAC,CAAC,CAAC;EACDO,QAAQ,EAAE,UAAU;EACpBC,MAAM,EAAE,CAAC;EACTC,GAAG,EAAE,CAAC;EACNC,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,YAAY,EAAE,yBAAyB;EACvC;EACA;EACA,sBAAsB,EAAE;IACtBC,OAAO,EAAE,MAAM;IACfC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,QAAQ;IACpBC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdC,SAAS,EAAE,OAAO;IAClBC,SAAS,EAAE,YAAY;IACvBP,YAAY,EAAE,yBAAyB;IACvCQ,MAAM,EAAE,CAAC;IACTC,OAAO,EAAE,CAAC;IACV,SAAS,EAAE;MACT;MACAL,KAAK,EAAE,MAAM;MACbC,MAAM,EAAE,MAAM;MACdC,SAAS,EAAE;IACb;EACF;AACF,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,SAAS,GAAG,aAAapC,KAAK,CAACqC,UAAU,CAAC,SAASD,SAASA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC/E,MAAMrB,KAAK,GAAGb,aAAa,CAAC;IAC1Ba,KAAK,EAAEoB,OAAO;IACdvB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFyB,SAAS;MACTC,SAAS,GAAG,KAAK;MACjBC,QAAQ;MACR9B,KAAK,GAAG,CAAC,CAAC;MACV+B,SAAS,GAAG,CAAC;IACf,CAAC,GAAGzB,KAAK;IACT0B,KAAK,GAAG9C,6BAA6B,CAACoB,KAAK,EAAEnB,SAAS,CAAC;EACzD,MAAM8C,UAAU,GAAGhD,QAAQ,CAAC,CAAC,CAAC,EAAEqB,KAAK,EAAE;IACrCuB;EACF,CAAC,CAAC;EACF,MAAMK,OAAO,GAAGnC,iBAAiB,CAAC,CAAC;EACnC,MAAMoC,sBAAsB,GAAGlD,QAAQ,CAAC,CAAC,CAAC,EAAE+C,KAAK,EAAE;IACjDH,SAAS;IACT7B,KAAK;IACL+B;EACF,CAAC,CAAC;EACF,MAAM,CAACK,QAAQ,EAAEC,SAAS,CAAC,GAAGzC,OAAO,CAAC,MAAM,EAAE;IAC5C+B,GAAG;IACHC,SAAS,EAAEvC,IAAI,CAAC6C,OAAO,CAACjC,IAAI,EAAE2B,SAAS,CAAC;IACxCU,WAAW,EAAEpC,aAAa;IAC1BiC,sBAAsB;IACtBF;EACF,CAAC,CAAC;EACF,OAAO,aAAanC,IAAI,CAACsC,QAAQ,EAAEnD,QAAQ,CAAC,CAAC,CAAC,EAAEoD,SAAS,EAAE;IACzDP,QAAQ,EAAE1C,KAAK,CAACmD,QAAQ,CAACC,GAAG,CAACV,QAAQ,EAAE,CAACW,KAAK,EAAEC,KAAK,KAAKA,KAAK,KAAK,CAAC,IAAI,aAAatD,KAAK,CAACuD,cAAc,CAACF,KAAK,CAAC,GAAG,aAAarD,KAAK,CAACwD,YAAY,CAACH,KAAK,EAAE;MACxJ,kBAAkB,EAAE;IACtB,CAAC,CAAC,GAAGA,KAAK;EACZ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFI,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGvB,SAAS,CAACwB,SAAS,CAAC,yBAAyB;EACnF;EACA;EACA;EACA;EACA;AACF;AACA;AACA;EACElB,QAAQ,EAAExC,SAAS,CAAC2D,IAAI;EACxB;AACF;AACA;EACErB,SAAS,EAAEtC,SAAS,CAAC4D,MAAM;EAC3B;AACF;AACA;AACA;EACErB,SAAS,EAAEvC,SAAS,CAACgD,WAAW;EAChC;AACF;AACA;AACA;EACEP,SAAS,EAAEzC,SAAS,CAAC6D,KAAK,CAAC;IACzBlD,IAAI,EAAEX,SAAS,CAAC8D,SAAS,CAAC,CAAC9D,SAAS,CAAC+D,IAAI,EAAE/D,SAAS,CAACgE,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEtD,KAAK,EAAEV,SAAS,CAAC6D,KAAK,CAAC;IACrBlD,IAAI,EAAEX,SAAS,CAACgD;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACEiB,EAAE,EAAEjE,SAAS,CAAC8D,SAAS,CAAC,CAAC9D,SAAS,CAACkE,OAAO,CAAClE,SAAS,CAAC8D,SAAS,CAAC,CAAC9D,SAAS,CAAC+D,IAAI,EAAE/D,SAAS,CAACgE,MAAM,EAAEhE,SAAS,CAACmE,IAAI,CAAC,CAAC,CAAC,EAAEnE,SAAS,CAAC+D,IAAI,EAAE/D,SAAS,CAACgE,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAe9B,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}