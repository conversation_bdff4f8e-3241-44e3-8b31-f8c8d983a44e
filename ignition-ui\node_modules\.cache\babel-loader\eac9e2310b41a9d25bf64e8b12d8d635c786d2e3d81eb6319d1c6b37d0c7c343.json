{"ast": null, "code": "export const NumberInputActionTypes = {\n  clamp: 'numberInput:clamp',\n  inputChange: 'numberInput:inputChange',\n  increment: 'numberInput:increment',\n  decrement: 'numberInput:decrement',\n  decrementToMin: 'numberInput:decrementToMin',\n  incrementToMax: 'numberInput:incrementToMax',\n  resetInputValue: 'numberInput:resetInputValue'\n};", "map": {"version": 3, "names": ["NumberInputActionTypes", "clamp", "inputChange", "increment", "decrement", "decrementToMin", "incrementToMax", "resetInputValue"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/unstable_useNumberInput/numberInputAction.types.js"], "sourcesContent": ["export const NumberInputActionTypes = {\n  clamp: 'numberInput:clamp',\n  inputChange: 'numberInput:inputChange',\n  increment: 'numberInput:increment',\n  decrement: 'numberInput:decrement',\n  decrementToMin: 'numberInput:decrementToMin',\n  incrementToMax: 'numberInput:incrementToMax',\n  resetInputValue: 'numberInput:resetInputValue'\n};"], "mappings": "AAAA,OAAO,MAAMA,sBAAsB,GAAG;EACpCC,KAAK,EAAE,mBAAmB;EAC1BC,WAAW,EAAE,yBAAyB;EACtCC,SAAS,EAAE,uBAAuB;EAClCC,SAAS,EAAE,uBAAuB;EAClCC,cAAc,EAAE,4BAA4B;EAC5CC,cAAc,EAAE,4BAA4B;EAC5CC,eAAe,EAAE;AACnB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}