{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getCardOverflowUtilityClass(slot) {\n  return generateUtilityClass('MuiCardOverflow', slot);\n}\nconst aspectRatioClasses = generateUtilityClasses('MuiCardOverflow', ['root', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid']);\nexport default aspectRatioClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getCardOverflowUtilityClass", "slot", "aspectRatioClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/CardOverflow/cardOverflowClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getCardOverflowUtilityClass(slot) {\n  return generateUtilityClass('MuiCardOverflow', slot);\n}\nconst aspectRatioClasses = generateUtilityClasses('MuiCardOverflow', ['root', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid']);\nexport default aspectRatioClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,2BAA2BA,CAACC,IAAI,EAAE;EAChD,OAAOH,oBAAoB,CAAC,iBAAiB,EAAEG,IAAI,CAAC;AACtD;AACA,MAAMC,kBAAkB,GAAGH,sBAAsB,CAAC,iBAAiB,EAAE,CAAC,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,iBAAiB,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;AAC/O,eAAeG,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}