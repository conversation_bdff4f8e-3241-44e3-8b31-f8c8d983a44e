{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { ThemeProvider as SystemThemeProvider, useTheme as useSystemTheme } from '@mui/system';\nimport defaultTheme from './defaultTheme';\nimport extendTheme from './extendTheme';\nimport THEME_ID from './identifier';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const useTheme = () => {\n  const theme = useSystemTheme(defaultTheme);\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useDebugValue(theme);\n  }\n\n  // @ts-ignore internal logic\n  return theme[THEME_ID] || theme;\n};\nexport default function ThemeProvider(_ref) {\n  let {\n    children,\n    theme: themeInput\n  } = _ref;\n  let theme = defaultTheme;\n  if (themeInput) {\n    theme = extendTheme(THEME_ID in themeInput ? themeInput[THEME_ID] : themeInput);\n  }\n  return /*#__PURE__*/_jsx(SystemThemeProvider, {\n    theme: theme,\n    themeId: themeInput && THEME_ID in themeInput ? THEME_ID : undefined,\n    children: children\n  });\n}", "map": {"version": 3, "names": ["React", "ThemeProvider", "SystemThemeProvider", "useTheme", "useSystemTheme", "defaultTheme", "extendTheme", "THEME_ID", "jsx", "_jsx", "theme", "process", "env", "NODE_ENV", "useDebugValue", "_ref", "children", "themeInput", "themeId", "undefined"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/styles/ThemeProvider.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { ThemeProvider as SystemThemeProvider, useTheme as useSystemTheme } from '@mui/system';\nimport defaultTheme from './defaultTheme';\nimport extendTheme from './extendTheme';\nimport THEME_ID from './identifier';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const useTheme = () => {\n  const theme = useSystemTheme(defaultTheme);\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useDebugValue(theme);\n  }\n\n  // @ts-ignore internal logic\n  return theme[THEME_ID] || theme;\n};\nexport default function ThemeProvider({\n  children,\n  theme: themeInput\n}) {\n  let theme = defaultTheme;\n  if (themeInput) {\n    theme = extendTheme(THEME_ID in themeInput ? themeInput[THEME_ID] : themeInput);\n  }\n  return /*#__PURE__*/_jsx(SystemThemeProvider, {\n    theme: theme,\n    themeId: themeInput && THEME_ID in themeInput ? THEME_ID : undefined,\n    children: children\n  });\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,IAAIC,mBAAmB,EAAEC,QAAQ,IAAIC,cAAc,QAAQ,aAAa;AAC9F,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,QAAQ,MAAM,cAAc;AACnC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,MAAMN,QAAQ,GAAGA,CAAA,KAAM;EAC5B,MAAMO,KAAK,GAAGN,cAAc,CAACC,YAAY,CAAC;EAC1C,IAAIM,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACAb,KAAK,CAACc,aAAa,CAACJ,KAAK,CAAC;EAC5B;;EAEA;EACA,OAAOA,KAAK,CAACH,QAAQ,CAAC,IAAIG,KAAK;AACjC,CAAC;AACD,eAAe,SAAST,aAAaA,CAAAc,IAAA,EAGlC;EAAA,IAHmC;IACpCC,QAAQ;IACRN,KAAK,EAAEO;EACT,CAAC,GAAAF,IAAA;EACC,IAAIL,KAAK,GAAGL,YAAY;EACxB,IAAIY,UAAU,EAAE;IACdP,KAAK,GAAGJ,WAAW,CAACC,QAAQ,IAAIU,UAAU,GAAGA,UAAU,CAACV,QAAQ,CAAC,GAAGU,UAAU,CAAC;EACjF;EACA,OAAO,aAAaR,IAAI,CAACP,mBAAmB,EAAE;IAC5CQ,KAAK,EAAEA,KAAK;IACZQ,OAAO,EAAED,UAAU,IAAIV,QAAQ,IAAIU,UAAU,GAAGV,QAAQ,GAAGY,SAAS;IACpEH,QAAQ,EAAEA;EACZ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}