{"ast": null, "code": "'use strict';\n\nvar implementation = require('./implementation');\nmodule.exports = Function.prototype.bind || implementation;", "map": {"version": 3, "names": ["implementation", "require", "module", "exports", "Function", "prototype", "bind"], "sources": ["C:/ignition/ignition-ui/node_modules/function-bind/index.js"], "sourcesContent": ["'use strict';\n\nvar implementation = require('./implementation');\n\nmodule.exports = Function.prototype.bind || implementation;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,cAAc,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAEhDC,MAAM,CAACC,OAAO,GAAGC,QAAQ,CAACC,SAAS,CAACC,IAAI,IAAIN,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}