{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nfunction areEqual(a, b) {\n  return a === b;\n}\nconst EMPTY_OBJECT = {};\nconst NOOP = () => {};\n\n/**\n * Gets the current state augmented with controlled values from the outside.\n * If a state item has a corresponding controlled value, it will be used instead of the internal state.\n */\nfunction getControlledState(internalState, controlledProps) {\n  const augmentedState = _extends({}, internalState);\n  Object.keys(controlledProps).forEach(key => {\n    if (controlledProps[key] !== undefined) {\n      augmentedState[key] = controlledProps[key];\n    }\n  });\n  return augmentedState;\n}\n/**\n * Defines an effect that compares the next state with the previous state and calls\n * the `onStateChange` callback if the state has changed.\n * The comparison is done based on the `stateComparers` parameter.\n */\nfunction useStateChangeDetection(parameters) {\n  const {\n    nextState,\n    initialState,\n    stateComparers,\n    onStateChange,\n    controlledProps,\n    lastActionRef\n  } = parameters;\n  const internalPreviousStateRef = React.useRef(initialState);\n  React.useEffect(() => {\n    if (lastActionRef.current === null) {\n      // Detect changes only if an action has been dispatched.\n      return;\n    }\n    const previousState = getControlledState(internalPreviousStateRef.current, controlledProps);\n    Object.keys(nextState).forEach(key => {\n      var _stateComparers$key;\n      // go through all state keys and compare them with the previous state\n      const stateComparer = (_stateComparers$key = stateComparers[key]) != null ? _stateComparers$key : areEqual;\n      const nextStateItem = nextState[key];\n      const previousStateItem = previousState[key];\n      if (previousStateItem == null && nextStateItem != null || previousStateItem != null && nextStateItem == null || previousStateItem != null && nextStateItem != null && !stateComparer(nextStateItem, previousStateItem)) {\n        var _event, _type;\n        onStateChange == null || onStateChange((_event = lastActionRef.current.event) != null ? _event : null, key, nextStateItem, (_type = lastActionRef.current.type) != null ? _type : '', nextState);\n      }\n    });\n    internalPreviousStateRef.current = nextState;\n    lastActionRef.current = null;\n  }, [internalPreviousStateRef, nextState, lastActionRef, onStateChange, stateComparers, controlledProps]);\n}\n\n/**\n * The alternative to `React.useReducer` that lets you control the state from the outside.\n *\n * It can be used in an uncontrolled mode, similar to `React.useReducer`, or in a controlled mode, when the state is controlled by the props.\n * It also supports partially controlled state, when some state items are controlled and some are not.\n *\n * The controlled state items are provided via the `controlledProps` parameter.\n * When a reducer action is dispatched, the internal state is updated with the new values.\n * A change event (`onStateChange`) is then triggered (for each changed state item) if the new state is different from the previous state.\n * This event can be used to update the controlled values.\n *\n * The comparison of the previous and next states is done using the `stateComparers` parameter.\n * If a state item has a corresponding comparer, it will be used to determine if the state has changed.\n * This is useful when the state item is an object and you want to compare only a subset of its properties or if it's an array and you want to compare its contents.\n *\n * An additional feature is the `actionContext` parameter. It allows you to add additional properties to every action object,\n * similarly to how React context is implicitly available to every component.\n *\n * @template State - The type of the state calculated by the reducer.\n * @template Action - The type of the actions that can be dispatched.\n * @template ActionContext - The type of the additional properties that will be added to every action object.\n *\n * @ignore - internal hook.\n */\nexport function useControllableReducer(parameters) {\n  const lastActionRef = React.useRef(null);\n  const {\n    reducer,\n    initialState,\n    controlledProps = EMPTY_OBJECT,\n    stateComparers = EMPTY_OBJECT,\n    onStateChange = NOOP,\n    actionContext,\n    componentName = ''\n  } = parameters;\n  const controlledPropsRef = React.useRef(controlledProps);\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      Object.keys(controlledProps).forEach(key => {\n        if (controlledPropsRef.current[key] !== undefined && controlledProps[key] === undefined) {\n          console.error(`useControllableReducer: ${componentName ? `The ${componentName} component` : 'A component'} is changing a controlled prop to be uncontrolled: ${key}`);\n        }\n        if (controlledPropsRef.current[key] === undefined && controlledProps[key] !== undefined) {\n          console.error(`useControllableReducer: ${componentName ? `The ${componentName} component` : 'A component'} is changing an uncontrolled prop to be controlled: ${key}`);\n        }\n      });\n    }, [controlledProps, componentName]);\n  }\n\n  // The reducer that is passed to React.useReducer is wrapped with a function that augments the state with controlled values.\n  const reducerWithControlledState = React.useCallback((state, action) => {\n    lastActionRef.current = action;\n    const controlledState = getControlledState(state, controlledProps);\n    const newState = reducer(controlledState, action);\n    return newState;\n  }, [controlledProps, reducer]);\n  const [nextState, dispatch] = React.useReducer(reducerWithControlledState, initialState);\n\n  // The action that is passed to dispatch is augmented with the actionContext.\n  const dispatchWithContext = React.useCallback(action => {\n    dispatch(_extends({}, action, {\n      context: actionContext\n    }));\n  }, [actionContext]);\n  useStateChangeDetection({\n    nextState,\n    initialState,\n    stateComparers: stateComparers != null ? stateComparers : EMPTY_OBJECT,\n    onStateChange: onStateChange != null ? onStateChange : NOOP,\n    controlledProps,\n    lastActionRef\n  });\n  return [getControlledState(nextState, controlledProps), dispatchWithContext];\n}", "map": {"version": 3, "names": ["_extends", "React", "areEqual", "a", "b", "EMPTY_OBJECT", "NOOP", "getControlledState", "internalState", "controlledProps", "augmentedState", "Object", "keys", "for<PERSON>ach", "key", "undefined", "useStateChangeDetection", "parameters", "nextState", "initialState", "stateComparers", "onStateChange", "lastActionRef", "internalPreviousStateRef", "useRef", "useEffect", "current", "previousState", "_stateComparers$key", "stateC<PERSON><PERSON>er", "nextStateItem", "previousStateItem", "_event", "_type", "event", "type", "useControllableReducer", "reducer", "actionContext", "componentName", "controlledPropsRef", "process", "env", "NODE_ENV", "console", "error", "reducerWithControlledState", "useCallback", "state", "action", "controlledState", "newState", "dispatch", "useReducer", "dispatchWithContext", "context"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/utils/useControllableReducer.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nfunction areEqual(a, b) {\n  return a === b;\n}\nconst EMPTY_OBJECT = {};\nconst NOOP = () => {};\n\n/**\n * Gets the current state augmented with controlled values from the outside.\n * If a state item has a corresponding controlled value, it will be used instead of the internal state.\n */\nfunction getControlledState(internalState, controlledProps) {\n  const augmentedState = _extends({}, internalState);\n  Object.keys(controlledProps).forEach(key => {\n    if (controlledProps[key] !== undefined) {\n      augmentedState[key] = controlledProps[key];\n    }\n  });\n  return augmentedState;\n}\n/**\n * Defines an effect that compares the next state with the previous state and calls\n * the `onStateChange` callback if the state has changed.\n * The comparison is done based on the `stateComparers` parameter.\n */\nfunction useStateChangeDetection(parameters) {\n  const {\n    nextState,\n    initialState,\n    stateComparers,\n    onStateChange,\n    controlledProps,\n    lastActionRef\n  } = parameters;\n  const internalPreviousStateRef = React.useRef(initialState);\n  React.useEffect(() => {\n    if (lastActionRef.current === null) {\n      // Detect changes only if an action has been dispatched.\n      return;\n    }\n    const previousState = getControlledState(internalPreviousStateRef.current, controlledProps);\n    Object.keys(nextState).forEach(key => {\n      var _stateComparers$key;\n      // go through all state keys and compare them with the previous state\n      const stateComparer = (_stateComparers$key = stateComparers[key]) != null ? _stateComparers$key : areEqual;\n      const nextStateItem = nextState[key];\n      const previousStateItem = previousState[key];\n      if (previousStateItem == null && nextStateItem != null || previousStateItem != null && nextStateItem == null || previousStateItem != null && nextStateItem != null && !stateComparer(nextStateItem, previousStateItem)) {\n        var _event, _type;\n        onStateChange == null || onStateChange((_event = lastActionRef.current.event) != null ? _event : null, key, nextStateItem, (_type = lastActionRef.current.type) != null ? _type : '', nextState);\n      }\n    });\n    internalPreviousStateRef.current = nextState;\n    lastActionRef.current = null;\n  }, [internalPreviousStateRef, nextState, lastActionRef, onStateChange, stateComparers, controlledProps]);\n}\n\n/**\n * The alternative to `React.useReducer` that lets you control the state from the outside.\n *\n * It can be used in an uncontrolled mode, similar to `React.useReducer`, or in a controlled mode, when the state is controlled by the props.\n * It also supports partially controlled state, when some state items are controlled and some are not.\n *\n * The controlled state items are provided via the `controlledProps` parameter.\n * When a reducer action is dispatched, the internal state is updated with the new values.\n * A change event (`onStateChange`) is then triggered (for each changed state item) if the new state is different from the previous state.\n * This event can be used to update the controlled values.\n *\n * The comparison of the previous and next states is done using the `stateComparers` parameter.\n * If a state item has a corresponding comparer, it will be used to determine if the state has changed.\n * This is useful when the state item is an object and you want to compare only a subset of its properties or if it's an array and you want to compare its contents.\n *\n * An additional feature is the `actionContext` parameter. It allows you to add additional properties to every action object,\n * similarly to how React context is implicitly available to every component.\n *\n * @template State - The type of the state calculated by the reducer.\n * @template Action - The type of the actions that can be dispatched.\n * @template ActionContext - The type of the additional properties that will be added to every action object.\n *\n * @ignore - internal hook.\n */\nexport function useControllableReducer(parameters) {\n  const lastActionRef = React.useRef(null);\n  const {\n    reducer,\n    initialState,\n    controlledProps = EMPTY_OBJECT,\n    stateComparers = EMPTY_OBJECT,\n    onStateChange = NOOP,\n    actionContext,\n    componentName = ''\n  } = parameters;\n  const controlledPropsRef = React.useRef(controlledProps);\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      Object.keys(controlledProps).forEach(key => {\n        if (controlledPropsRef.current[key] !== undefined && controlledProps[key] === undefined) {\n          console.error(`useControllableReducer: ${componentName ? `The ${componentName} component` : 'A component'} is changing a controlled prop to be uncontrolled: ${key}`);\n        }\n        if (controlledPropsRef.current[key] === undefined && controlledProps[key] !== undefined) {\n          console.error(`useControllableReducer: ${componentName ? `The ${componentName} component` : 'A component'} is changing an uncontrolled prop to be controlled: ${key}`);\n        }\n      });\n    }, [controlledProps, componentName]);\n  }\n\n  // The reducer that is passed to React.useReducer is wrapped with a function that augments the state with controlled values.\n  const reducerWithControlledState = React.useCallback((state, action) => {\n    lastActionRef.current = action;\n    const controlledState = getControlledState(state, controlledProps);\n    const newState = reducer(controlledState, action);\n    return newState;\n  }, [controlledProps, reducer]);\n  const [nextState, dispatch] = React.useReducer(reducerWithControlledState, initialState);\n\n  // The action that is passed to dispatch is augmented with the actionContext.\n  const dispatchWithContext = React.useCallback(action => {\n    dispatch(_extends({}, action, {\n      context: actionContext\n    }));\n  }, [actionContext]);\n  useStateChangeDetection({\n    nextState,\n    initialState,\n    stateComparers: stateComparers != null ? stateComparers : EMPTY_OBJECT,\n    onStateChange: onStateChange != null ? onStateChange : NOOP,\n    controlledProps,\n    lastActionRef\n  });\n  return [getControlledState(nextState, controlledProps), dispatchWithContext];\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACtB,OAAOD,CAAC,KAAKC,CAAC;AAChB;AACA,MAAMC,YAAY,GAAG,CAAC,CAAC;AACvB,MAAMC,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;;AAErB;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAACC,aAAa,EAAEC,eAAe,EAAE;EAC1D,MAAMC,cAAc,GAAGV,QAAQ,CAAC,CAAC,CAAC,EAAEQ,aAAa,CAAC;EAClDG,MAAM,CAACC,IAAI,CAACH,eAAe,CAAC,CAACI,OAAO,CAACC,GAAG,IAAI;IAC1C,IAAIL,eAAe,CAACK,GAAG,CAAC,KAAKC,SAAS,EAAE;MACtCL,cAAc,CAACI,GAAG,CAAC,GAAGL,eAAe,CAACK,GAAG,CAAC;IAC5C;EACF,CAAC,CAAC;EACF,OAAOJ,cAAc;AACvB;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,uBAAuBA,CAACC,UAAU,EAAE;EAC3C,MAAM;IACJC,SAAS;IACTC,YAAY;IACZC,cAAc;IACdC,aAAa;IACbZ,eAAe;IACfa;EACF,CAAC,GAAGL,UAAU;EACd,MAAMM,wBAAwB,GAAGtB,KAAK,CAACuB,MAAM,CAACL,YAAY,CAAC;EAC3DlB,KAAK,CAACwB,SAAS,CAAC,MAAM;IACpB,IAAIH,aAAa,CAACI,OAAO,KAAK,IAAI,EAAE;MAClC;MACA;IACF;IACA,MAAMC,aAAa,GAAGpB,kBAAkB,CAACgB,wBAAwB,CAACG,OAAO,EAAEjB,eAAe,CAAC;IAC3FE,MAAM,CAACC,IAAI,CAACM,SAAS,CAAC,CAACL,OAAO,CAACC,GAAG,IAAI;MACpC,IAAIc,mBAAmB;MACvB;MACA,MAAMC,aAAa,GAAG,CAACD,mBAAmB,GAAGR,cAAc,CAACN,GAAG,CAAC,KAAK,IAAI,GAAGc,mBAAmB,GAAG1B,QAAQ;MAC1G,MAAM4B,aAAa,GAAGZ,SAAS,CAACJ,GAAG,CAAC;MACpC,MAAMiB,iBAAiB,GAAGJ,aAAa,CAACb,GAAG,CAAC;MAC5C,IAAIiB,iBAAiB,IAAI,IAAI,IAAID,aAAa,IAAI,IAAI,IAAIC,iBAAiB,IAAI,IAAI,IAAID,aAAa,IAAI,IAAI,IAAIC,iBAAiB,IAAI,IAAI,IAAID,aAAa,IAAI,IAAI,IAAI,CAACD,aAAa,CAACC,aAAa,EAAEC,iBAAiB,CAAC,EAAE;QACtN,IAAIC,MAAM,EAAEC,KAAK;QACjBZ,aAAa,IAAI,IAAI,IAAIA,aAAa,CAAC,CAACW,MAAM,GAAGV,aAAa,CAACI,OAAO,CAACQ,KAAK,KAAK,IAAI,GAAGF,MAAM,GAAG,IAAI,EAAElB,GAAG,EAAEgB,aAAa,EAAE,CAACG,KAAK,GAAGX,aAAa,CAACI,OAAO,CAACS,IAAI,KAAK,IAAI,GAAGF,KAAK,GAAG,EAAE,EAAEf,SAAS,CAAC;MAClM;IACF,CAAC,CAAC;IACFK,wBAAwB,CAACG,OAAO,GAAGR,SAAS;IAC5CI,aAAa,CAACI,OAAO,GAAG,IAAI;EAC9B,CAAC,EAAE,CAACH,wBAAwB,EAAEL,SAAS,EAAEI,aAAa,EAAED,aAAa,EAAED,cAAc,EAAEX,eAAe,CAAC,CAAC;AAC1G;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAAS2B,sBAAsBA,CAACnB,UAAU,EAAE;EACjD,MAAMK,aAAa,GAAGrB,KAAK,CAACuB,MAAM,CAAC,IAAI,CAAC;EACxC,MAAM;IACJa,OAAO;IACPlB,YAAY;IACZV,eAAe,GAAGJ,YAAY;IAC9Be,cAAc,GAAGf,YAAY;IAC7BgB,aAAa,GAAGf,IAAI;IACpBgC,aAAa;IACbC,aAAa,GAAG;EAClB,CAAC,GAAGtB,UAAU;EACd,MAAMuB,kBAAkB,GAAGvC,KAAK,CAACuB,MAAM,CAACf,eAAe,CAAC;EACxD,IAAIgC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACA1C,KAAK,CAACwB,SAAS,CAAC,MAAM;MACpBd,MAAM,CAACC,IAAI,CAACH,eAAe,CAAC,CAACI,OAAO,CAACC,GAAG,IAAI;QAC1C,IAAI0B,kBAAkB,CAACd,OAAO,CAACZ,GAAG,CAAC,KAAKC,SAAS,IAAIN,eAAe,CAACK,GAAG,CAAC,KAAKC,SAAS,EAAE;UACvF6B,OAAO,CAACC,KAAK,CAAC,2BAA2BN,aAAa,GAAG,OAAOA,aAAa,YAAY,GAAG,aAAa,sDAAsDzB,GAAG,EAAE,CAAC;QACvK;QACA,IAAI0B,kBAAkB,CAACd,OAAO,CAACZ,GAAG,CAAC,KAAKC,SAAS,IAAIN,eAAe,CAACK,GAAG,CAAC,KAAKC,SAAS,EAAE;UACvF6B,OAAO,CAACC,KAAK,CAAC,2BAA2BN,aAAa,GAAG,OAAOA,aAAa,YAAY,GAAG,aAAa,uDAAuDzB,GAAG,EAAE,CAAC;QACxK;MACF,CAAC,CAAC;IACJ,CAAC,EAAE,CAACL,eAAe,EAAE8B,aAAa,CAAC,CAAC;EACtC;;EAEA;EACA,MAAMO,0BAA0B,GAAG7C,KAAK,CAAC8C,WAAW,CAAC,CAACC,KAAK,EAAEC,MAAM,KAAK;IACtE3B,aAAa,CAACI,OAAO,GAAGuB,MAAM;IAC9B,MAAMC,eAAe,GAAG3C,kBAAkB,CAACyC,KAAK,EAAEvC,eAAe,CAAC;IAClE,MAAM0C,QAAQ,GAAGd,OAAO,CAACa,eAAe,EAAED,MAAM,CAAC;IACjD,OAAOE,QAAQ;EACjB,CAAC,EAAE,CAAC1C,eAAe,EAAE4B,OAAO,CAAC,CAAC;EAC9B,MAAM,CAACnB,SAAS,EAAEkC,QAAQ,CAAC,GAAGnD,KAAK,CAACoD,UAAU,CAACP,0BAA0B,EAAE3B,YAAY,CAAC;;EAExF;EACA,MAAMmC,mBAAmB,GAAGrD,KAAK,CAAC8C,WAAW,CAACE,MAAM,IAAI;IACtDG,QAAQ,CAACpD,QAAQ,CAAC,CAAC,CAAC,EAAEiD,MAAM,EAAE;MAC5BM,OAAO,EAAEjB;IACX,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAACA,aAAa,CAAC,CAAC;EACnBtB,uBAAuB,CAAC;IACtBE,SAAS;IACTC,YAAY;IACZC,cAAc,EAAEA,cAAc,IAAI,IAAI,GAAGA,cAAc,GAAGf,YAAY;IACtEgB,aAAa,EAAEA,aAAa,IAAI,IAAI,GAAGA,aAAa,GAAGf,IAAI;IAC3DG,eAAe;IACfa;EACF,CAAC,CAAC;EACF,OAAO,CAACf,kBAAkB,CAACW,SAAS,EAAET,eAAe,CAAC,EAAE6C,mBAAmB,CAAC;AAC9E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}