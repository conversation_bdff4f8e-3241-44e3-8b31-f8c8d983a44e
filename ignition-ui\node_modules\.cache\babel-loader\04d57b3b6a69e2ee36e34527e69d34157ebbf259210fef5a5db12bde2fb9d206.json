{"ast": null, "code": "'use client';\n\nimport { createStack } from '@mui/system';\nimport PropTypes from 'prop-types';\nimport styled from '../styles/styled';\nimport { useThemeProps } from '../styles';\n/**\n *\n * Demos:\n *\n * - [Stack](https://mui.com/joy-ui/react-stack/)\n *\n * API:\n *\n * - [Stack API](https://mui.com/joy-ui/api/stack/)\n */\nconst Stack = createStack({\n  createStyledComponent: styled('div', {\n    name: 'JoyStack',\n    slot: 'Root',\n    overridesResolver: (props, styles) => styles.root\n  }),\n  useThemeProps: inProps => useThemeProps({\n    props: inProps,\n    name: 'JoyStack'\n  })\n});\nprocess.env.NODE_ENV !== \"production\" ? Stack.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Defines the `flex-direction` style property.\n   * It is applied for all screen sizes.\n   * @default 'column'\n   */\n  direction: PropTypes.oneOfType([PropTypes.oneOf(['column-reverse', 'column', 'row-reverse', 'row']), PropTypes.arrayOf(PropTypes.oneOf(['column-reverse', 'column', 'row-reverse', 'row'])), PropTypes.object]),\n  /**\n   * Add an element between each child.\n   */\n  divider: PropTypes.node,\n  /**\n   * Defines the space between immediate children.\n   * @default 0\n   */\n  spacing: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.number, PropTypes.string])), PropTypes.number, PropTypes.object, PropTypes.string]),\n  /**\n   * The system prop, which allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * If `true`, the CSS flexbox `gap` is used instead of applying `margin` to children.\n   *\n   * While CSS `gap` removes the [known limitations](https://mui.com/joy-ui/react-stack/#limitations),\n   * it is not fully supported in some browsers. We recommend checking https://caniuse.com/?search=flex%20gap before using this flag.\n   *\n   * To enable this flag globally, follow the [theme's default props](https://mui.com/joy-ui/customization/themed-components/#default-props) configuration.\n   * @default false\n   */\n  useFlexGap: PropTypes.bool\n} : void 0;\nexport default Stack;", "map": {"version": 3, "names": ["createStack", "PropTypes", "styled", "useThemeProps", "<PERSON><PERSON>", "createStyledComponent", "name", "slot", "overridesResolver", "props", "styles", "root", "inProps", "process", "env", "NODE_ENV", "propTypes", "children", "node", "component", "elementType", "direction", "oneOfType", "oneOf", "arrayOf", "object", "divider", "spacing", "number", "string", "sx", "func", "bool", "useFlexGap"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Stack/Stack.js"], "sourcesContent": ["'use client';\n\nimport { createStack } from '@mui/system';\nimport PropTypes from 'prop-types';\nimport styled from '../styles/styled';\nimport { useThemeProps } from '../styles';\n/**\n *\n * Demos:\n *\n * - [Stack](https://mui.com/joy-ui/react-stack/)\n *\n * API:\n *\n * - [Stack API](https://mui.com/joy-ui/api/stack/)\n */\nconst Stack = createStack({\n  createStyledComponent: styled('div', {\n    name: 'JoyStack',\n    slot: 'Root',\n    overridesResolver: (props, styles) => styles.root\n  }),\n  useThemeProps: inProps => useThemeProps({\n    props: inProps,\n    name: 'JoyStack'\n  })\n});\nprocess.env.NODE_ENV !== \"production\" ? Stack.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Defines the `flex-direction` style property.\n   * It is applied for all screen sizes.\n   * @default 'column'\n   */\n  direction: PropTypes.oneOfType([PropTypes.oneOf(['column-reverse', 'column', 'row-reverse', 'row']), PropTypes.arrayOf(PropTypes.oneOf(['column-reverse', 'column', 'row-reverse', 'row'])), PropTypes.object]),\n  /**\n   * Add an element between each child.\n   */\n  divider: PropTypes.node,\n  /**\n   * Defines the space between immediate children.\n   * @default 0\n   */\n  spacing: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.number, PropTypes.string])), PropTypes.number, PropTypes.object, PropTypes.string]),\n  /**\n   * The system prop, which allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * If `true`, the CSS flexbox `gap` is used instead of applying `margin` to children.\n   *\n   * While CSS `gap` removes the [known limitations](https://mui.com/joy-ui/react-stack/#limitations),\n   * it is not fully supported in some browsers. We recommend checking https://caniuse.com/?search=flex%20gap before using this flag.\n   *\n   * To enable this flag globally, follow the [theme's default props](https://mui.com/joy-ui/customization/themed-components/#default-props) configuration.\n   * @default false\n   */\n  useFlexGap: PropTypes.bool\n} : void 0;\nexport default Stack;"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,WAAW,QAAQ,aAAa;AACzC,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,aAAa,QAAQ,WAAW;AACzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,KAAK,GAAGJ,WAAW,CAAC;EACxBK,qBAAqB,EAAEH,MAAM,CAAC,KAAK,EAAE;IACnCI,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,MAAM;IACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACC;EAC/C,CAAC,CAAC;EACFR,aAAa,EAAES,OAAO,IAAIT,aAAa,CAAC;IACtCM,KAAK,EAAEG,OAAO;IACdN,IAAI,EAAE;EACR,CAAC;AACH,CAAC,CAAC;AACFO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGX,KAAK,CAACY,SAAS,CAAC,yBAAyB;EAC/E;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAEhB,SAAS,CAACiB,IAAI;EACxB;AACF;AACA;AACA;EACEC,SAAS,EAAElB,SAAS,CAACmB,WAAW;EAChC;AACF;AACA;AACA;AACA;EACEC,SAAS,EAAEpB,SAAS,CAACqB,SAAS,CAAC,CAACrB,SAAS,CAACsB,KAAK,CAAC,CAAC,gBAAgB,EAAE,QAAQ,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC,EAAEtB,SAAS,CAACuB,OAAO,CAACvB,SAAS,CAACsB,KAAK,CAAC,CAAC,gBAAgB,EAAE,QAAQ,EAAE,aAAa,EAAE,KAAK,CAAC,CAAC,CAAC,EAAEtB,SAAS,CAACwB,MAAM,CAAC,CAAC;EAC/M;AACF;AACA;EACEC,OAAO,EAAEzB,SAAS,CAACiB,IAAI;EACvB;AACF;AACA;AACA;EACES,OAAO,EAAE1B,SAAS,CAACqB,SAAS,CAAC,CAACrB,SAAS,CAACuB,OAAO,CAACvB,SAAS,CAACqB,SAAS,CAAC,CAACrB,SAAS,CAAC2B,MAAM,EAAE3B,SAAS,CAAC4B,MAAM,CAAC,CAAC,CAAC,EAAE5B,SAAS,CAAC2B,MAAM,EAAE3B,SAAS,CAACwB,MAAM,EAAExB,SAAS,CAAC4B,MAAM,CAAC,CAAC;EAClK;AACF;AACA;EACEC,EAAE,EAAE7B,SAAS,CAACqB,SAAS,CAAC,CAACrB,SAAS,CAACuB,OAAO,CAACvB,SAAS,CAACqB,SAAS,CAAC,CAACrB,SAAS,CAAC8B,IAAI,EAAE9B,SAAS,CAACwB,MAAM,EAAExB,SAAS,CAAC+B,IAAI,CAAC,CAAC,CAAC,EAAE/B,SAAS,CAAC8B,IAAI,EAAE9B,SAAS,CAACwB,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEQ,UAAU,EAAEhC,SAAS,CAAC+B;AACxB,CAAC,GAAG,KAAK,CAAC;AACV,eAAe5B,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}