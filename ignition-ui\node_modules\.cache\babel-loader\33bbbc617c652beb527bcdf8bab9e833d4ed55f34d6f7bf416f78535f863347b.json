{"ast": null, "code": "import * as React from 'react';\nconst GroupListContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== 'production') {\n  GroupListContext.displayName = 'GroupListContext';\n}\nexport default GroupListContext;", "map": {"version": 3, "names": ["React", "GroupListContext", "createContext", "undefined", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/List/GroupListContext.js"], "sourcesContent": ["import * as React from 'react';\nconst GroupListContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== 'production') {\n  GroupListContext.displayName = 'GroupListContext';\n}\nexport default GroupListContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,gBAAgB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAACC,SAAS,CAAC;AACpE,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCL,gBAAgB,CAACM,WAAW,GAAG,kBAAkB;AACnD;AACA,eAAeN,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}