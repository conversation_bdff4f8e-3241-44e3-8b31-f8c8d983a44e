{"ast": null, "code": "import { useReducer, useRef, useMemo, useContext, useDebugValue } from 'react';\nimport { useReduxContext as useDefaultReduxContext } from './useReduxContext';\nimport { createSubscription } from '../utils/Subscription';\nimport { useIsomorphicLayoutEffect } from '../utils/useIsomorphicLayoutEffect';\nimport { ReactReduxContext } from '../components/Context';\nvar refEquality = function refEquality(a, b) {\n  return a === b;\n};\nfunction useSelectorWithStoreAndSubscription(selector, equalityFn, store, contextSub) {\n  var _useReducer = useReducer(function (s) {\n      return s + 1;\n    }, 0),\n    forceRender = _useReducer[1];\n  var subscription = useMemo(function () {\n    return createSubscription(store, contextSub);\n  }, [store, contextSub]);\n  var latestSubscriptionCallbackError = useRef();\n  var latestSelector = useRef();\n  var latestStoreState = useRef();\n  var latestSelectedState = useRef();\n  var storeState = store.getState();\n  var selectedState;\n  try {\n    if (selector !== latestSelector.current || storeState !== latestStoreState.current || latestSubscriptionCallbackError.current) {\n      var newSelectedState = selector(storeState); // ensure latest selected state is reused so that a custom equality function can result in identical references\n\n      if (latestSelectedState.current === undefined || !equalityFn(newSelectedState, latestSelectedState.current)) {\n        selectedState = newSelectedState;\n      } else {\n        selectedState = latestSelectedState.current;\n      }\n    } else {\n      selectedState = latestSelectedState.current;\n    }\n  } catch (err) {\n    if (latestSubscriptionCallbackError.current) {\n      err.message += \"\\nThe error may be correlated with this previous error:\\n\" + latestSubscriptionCallbackError.current.stack + \"\\n\\n\";\n    }\n    throw err;\n  }\n  useIsomorphicLayoutEffect(function () {\n    latestSelector.current = selector;\n    latestStoreState.current = storeState;\n    latestSelectedState.current = selectedState;\n    latestSubscriptionCallbackError.current = undefined;\n  });\n  useIsomorphicLayoutEffect(function () {\n    function checkForUpdates() {\n      try {\n        var newStoreState = store.getState(); // Avoid calling selector multiple times if the store's state has not changed\n\n        if (newStoreState === latestStoreState.current) {\n          return;\n        }\n        var _newSelectedState = latestSelector.current(newStoreState);\n        if (equalityFn(_newSelectedState, latestSelectedState.current)) {\n          return;\n        }\n        latestSelectedState.current = _newSelectedState;\n        latestStoreState.current = newStoreState;\n      } catch (err) {\n        // we ignore all errors here, since when the component\n        // is re-rendered, the selectors are called again, and\n        // will throw again, if neither props nor store state\n        // changed\n        latestSubscriptionCallbackError.current = err;\n      }\n      forceRender();\n    }\n    subscription.onStateChange = checkForUpdates;\n    subscription.trySubscribe();\n    checkForUpdates();\n    return function () {\n      return subscription.tryUnsubscribe();\n    };\n  }, [store, subscription]);\n  return selectedState;\n}\n/**\r\n * Hook factory, which creates a `useSelector` hook bound to a given context.\r\n *\r\n * @param {React.Context} [context=ReactReduxContext] Context passed to your `<Provider>`.\r\n * @returns {Function} A `useSelector` hook bound to the specified context.\r\n */\n\nexport function createSelectorHook(context) {\n  if (context === void 0) {\n    context = ReactReduxContext;\n  }\n  var useReduxContext = context === ReactReduxContext ? useDefaultReduxContext : function () {\n    return useContext(context);\n  };\n  return function useSelector(selector, equalityFn) {\n    if (equalityFn === void 0) {\n      equalityFn = refEquality;\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      if (!selector) {\n        throw new Error(\"You must pass a selector to useSelector\");\n      }\n      if (typeof selector !== 'function') {\n        throw new Error(\"You must pass a function as a selector to useSelector\");\n      }\n      if (typeof equalityFn !== 'function') {\n        throw new Error(\"You must pass a function as an equality function to useSelector\");\n      }\n    }\n    var _useReduxContext = useReduxContext(),\n      store = _useReduxContext.store,\n      contextSub = _useReduxContext.subscription;\n    var selectedState = useSelectorWithStoreAndSubscription(selector, equalityFn, store, contextSub);\n    useDebugValue(selectedState);\n    return selectedState;\n  };\n}\n/**\r\n * A hook to access the redux store's state. This hook takes a selector function\r\n * as an argument. The selector is called with the store state.\r\n *\r\n * This hook takes an optional equality comparison function as the second parameter\r\n * that allows you to customize the way the selected state is compared to determine\r\n * whether the component needs to be re-rendered.\r\n *\r\n * @param {Function} selector the selector function\r\n * @param {Function=} equalityFn the function that will be used to determine equality\r\n *\r\n * @returns {any} the selected state\r\n *\r\n * @example\r\n *\r\n * import React from 'react'\r\n * import { useSelector } from 'react-redux'\r\n *\r\n * export const CounterComponent = () => {\r\n *   const counter = useSelector(state => state.counter)\r\n *   return <div>{counter}</div>\r\n * }\r\n */\n\nexport var useSelector = /*#__PURE__*/createSelectorHook();", "map": {"version": 3, "names": ["useReducer", "useRef", "useMemo", "useContext", "useDebugValue", "useReduxContext", "useDefaultReduxContext", "createSubscription", "useIsomorphicLayoutEffect", "ReactReduxContext", "refEquality", "a", "b", "useSelectorWithStoreAndSubscription", "selector", "equalityFn", "store", "contextSub", "_useReducer", "s", "forceRender", "subscription", "latestSubscriptionCallbackError", "latestSelector", "latestStoreState", "latestSelectedState", "storeState", "getState", "selectedState", "current", "newSelectedState", "undefined", "err", "message", "stack", "checkForUpdates", "newStoreState", "_newSelectedState", "onStateChange", "trySubscribe", "tryUnsubscribe", "createSelectorHook", "context", "useSelector", "process", "env", "NODE_ENV", "Error", "_useReduxContext"], "sources": ["C:/ignition/ignition-ui/node_modules/react-beautiful-dnd/node_modules/react-redux/es/hooks/useSelector.js"], "sourcesContent": ["import { useReducer, useRef, useMemo, useContext, useDebugValue } from 'react';\nimport { useReduxContext as useDefaultReduxContext } from './useReduxContext';\nimport { createSubscription } from '../utils/Subscription';\nimport { useIsomorphicLayoutEffect } from '../utils/useIsomorphicLayoutEffect';\nimport { ReactReduxContext } from '../components/Context';\n\nvar refEquality = function refEquality(a, b) {\n  return a === b;\n};\n\nfunction useSelectorWithStoreAndSubscription(selector, equalityFn, store, contextSub) {\n  var _useReducer = useReducer(function (s) {\n    return s + 1;\n  }, 0),\n      forceRender = _useReducer[1];\n\n  var subscription = useMemo(function () {\n    return createSubscription(store, contextSub);\n  }, [store, contextSub]);\n  var latestSubscriptionCallbackError = useRef();\n  var latestSelector = useRef();\n  var latestStoreState = useRef();\n  var latestSelectedState = useRef();\n  var storeState = store.getState();\n  var selectedState;\n\n  try {\n    if (selector !== latestSelector.current || storeState !== latestStoreState.current || latestSubscriptionCallbackError.current) {\n      var newSelectedState = selector(storeState); // ensure latest selected state is reused so that a custom equality function can result in identical references\n\n      if (latestSelectedState.current === undefined || !equalityFn(newSelectedState, latestSelectedState.current)) {\n        selectedState = newSelectedState;\n      } else {\n        selectedState = latestSelectedState.current;\n      }\n    } else {\n      selectedState = latestSelectedState.current;\n    }\n  } catch (err) {\n    if (latestSubscriptionCallbackError.current) {\n      err.message += \"\\nThe error may be correlated with this previous error:\\n\" + latestSubscriptionCallbackError.current.stack + \"\\n\\n\";\n    }\n\n    throw err;\n  }\n\n  useIsomorphicLayoutEffect(function () {\n    latestSelector.current = selector;\n    latestStoreState.current = storeState;\n    latestSelectedState.current = selectedState;\n    latestSubscriptionCallbackError.current = undefined;\n  });\n  useIsomorphicLayoutEffect(function () {\n    function checkForUpdates() {\n      try {\n        var newStoreState = store.getState(); // Avoid calling selector multiple times if the store's state has not changed\n\n        if (newStoreState === latestStoreState.current) {\n          return;\n        }\n\n        var _newSelectedState = latestSelector.current(newStoreState);\n\n        if (equalityFn(_newSelectedState, latestSelectedState.current)) {\n          return;\n        }\n\n        latestSelectedState.current = _newSelectedState;\n        latestStoreState.current = newStoreState;\n      } catch (err) {\n        // we ignore all errors here, since when the component\n        // is re-rendered, the selectors are called again, and\n        // will throw again, if neither props nor store state\n        // changed\n        latestSubscriptionCallbackError.current = err;\n      }\n\n      forceRender();\n    }\n\n    subscription.onStateChange = checkForUpdates;\n    subscription.trySubscribe();\n    checkForUpdates();\n    return function () {\n      return subscription.tryUnsubscribe();\n    };\n  }, [store, subscription]);\n  return selectedState;\n}\n/**\r\n * Hook factory, which creates a `useSelector` hook bound to a given context.\r\n *\r\n * @param {React.Context} [context=ReactReduxContext] Context passed to your `<Provider>`.\r\n * @returns {Function} A `useSelector` hook bound to the specified context.\r\n */\n\n\nexport function createSelectorHook(context) {\n  if (context === void 0) {\n    context = ReactReduxContext;\n  }\n\n  var useReduxContext = context === ReactReduxContext ? useDefaultReduxContext : function () {\n    return useContext(context);\n  };\n  return function useSelector(selector, equalityFn) {\n    if (equalityFn === void 0) {\n      equalityFn = refEquality;\n    }\n\n    if (process.env.NODE_ENV !== 'production') {\n      if (!selector) {\n        throw new Error(\"You must pass a selector to useSelector\");\n      }\n\n      if (typeof selector !== 'function') {\n        throw new Error(\"You must pass a function as a selector to useSelector\");\n      }\n\n      if (typeof equalityFn !== 'function') {\n        throw new Error(\"You must pass a function as an equality function to useSelector\");\n      }\n    }\n\n    var _useReduxContext = useReduxContext(),\n        store = _useReduxContext.store,\n        contextSub = _useReduxContext.subscription;\n\n    var selectedState = useSelectorWithStoreAndSubscription(selector, equalityFn, store, contextSub);\n    useDebugValue(selectedState);\n    return selectedState;\n  };\n}\n/**\r\n * A hook to access the redux store's state. This hook takes a selector function\r\n * as an argument. The selector is called with the store state.\r\n *\r\n * This hook takes an optional equality comparison function as the second parameter\r\n * that allows you to customize the way the selected state is compared to determine\r\n * whether the component needs to be re-rendered.\r\n *\r\n * @param {Function} selector the selector function\r\n * @param {Function=} equalityFn the function that will be used to determine equality\r\n *\r\n * @returns {any} the selected state\r\n *\r\n * @example\r\n *\r\n * import React from 'react'\r\n * import { useSelector } from 'react-redux'\r\n *\r\n * export const CounterComponent = () => {\r\n *   const counter = useSelector(state => state.counter)\r\n *   return <div>{counter}</div>\r\n * }\r\n */\n\nexport var useSelector = /*#__PURE__*/createSelectorHook();"], "mappings": "AAAA,SAASA,UAAU,EAAEC,MAAM,EAAEC,OAAO,EAAEC,UAAU,EAAEC,aAAa,QAAQ,OAAO;AAC9E,SAASC,eAAe,IAAIC,sBAAsB,QAAQ,mBAAmB;AAC7E,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,yBAAyB,QAAQ,oCAAoC;AAC9E,SAASC,iBAAiB,QAAQ,uBAAuB;AAEzD,IAAIC,WAAW,GAAG,SAASA,WAAWA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC3C,OAAOD,CAAC,KAAKC,CAAC;AAChB,CAAC;AAED,SAASC,mCAAmCA,CAACC,QAAQ,EAAEC,UAAU,EAAEC,KAAK,EAAEC,UAAU,EAAE;EACpF,IAAIC,WAAW,GAAGlB,UAAU,CAAC,UAAUmB,CAAC,EAAE;MACxC,OAAOA,CAAC,GAAG,CAAC;IACd,CAAC,EAAE,CAAC,CAAC;IACDC,WAAW,GAAGF,WAAW,CAAC,CAAC,CAAC;EAEhC,IAAIG,YAAY,GAAGnB,OAAO,CAAC,YAAY;IACrC,OAAOK,kBAAkB,CAACS,KAAK,EAAEC,UAAU,CAAC;EAC9C,CAAC,EAAE,CAACD,KAAK,EAAEC,UAAU,CAAC,CAAC;EACvB,IAAIK,+BAA+B,GAAGrB,MAAM,CAAC,CAAC;EAC9C,IAAIsB,cAAc,GAAGtB,MAAM,CAAC,CAAC;EAC7B,IAAIuB,gBAAgB,GAAGvB,MAAM,CAAC,CAAC;EAC/B,IAAIwB,mBAAmB,GAAGxB,MAAM,CAAC,CAAC;EAClC,IAAIyB,UAAU,GAAGV,KAAK,CAACW,QAAQ,CAAC,CAAC;EACjC,IAAIC,aAAa;EAEjB,IAAI;IACF,IAAId,QAAQ,KAAKS,cAAc,CAACM,OAAO,IAAIH,UAAU,KAAKF,gBAAgB,CAACK,OAAO,IAAIP,+BAA+B,CAACO,OAAO,EAAE;MAC7H,IAAIC,gBAAgB,GAAGhB,QAAQ,CAACY,UAAU,CAAC,CAAC,CAAC;;MAE7C,IAAID,mBAAmB,CAACI,OAAO,KAAKE,SAAS,IAAI,CAAChB,UAAU,CAACe,gBAAgB,EAAEL,mBAAmB,CAACI,OAAO,CAAC,EAAE;QAC3GD,aAAa,GAAGE,gBAAgB;MAClC,CAAC,MAAM;QACLF,aAAa,GAAGH,mBAAmB,CAACI,OAAO;MAC7C;IACF,CAAC,MAAM;MACLD,aAAa,GAAGH,mBAAmB,CAACI,OAAO;IAC7C;EACF,CAAC,CAAC,OAAOG,GAAG,EAAE;IACZ,IAAIV,+BAA+B,CAACO,OAAO,EAAE;MAC3CG,GAAG,CAACC,OAAO,IAAI,2DAA2D,GAAGX,+BAA+B,CAACO,OAAO,CAACK,KAAK,GAAG,MAAM;IACrI;IAEA,MAAMF,GAAG;EACX;EAEAxB,yBAAyB,CAAC,YAAY;IACpCe,cAAc,CAACM,OAAO,GAAGf,QAAQ;IACjCU,gBAAgB,CAACK,OAAO,GAAGH,UAAU;IACrCD,mBAAmB,CAACI,OAAO,GAAGD,aAAa;IAC3CN,+BAA+B,CAACO,OAAO,GAAGE,SAAS;EACrD,CAAC,CAAC;EACFvB,yBAAyB,CAAC,YAAY;IACpC,SAAS2B,eAAeA,CAAA,EAAG;MACzB,IAAI;QACF,IAAIC,aAAa,GAAGpB,KAAK,CAACW,QAAQ,CAAC,CAAC,CAAC,CAAC;;QAEtC,IAAIS,aAAa,KAAKZ,gBAAgB,CAACK,OAAO,EAAE;UAC9C;QACF;QAEA,IAAIQ,iBAAiB,GAAGd,cAAc,CAACM,OAAO,CAACO,aAAa,CAAC;QAE7D,IAAIrB,UAAU,CAACsB,iBAAiB,EAAEZ,mBAAmB,CAACI,OAAO,CAAC,EAAE;UAC9D;QACF;QAEAJ,mBAAmB,CAACI,OAAO,GAAGQ,iBAAiB;QAC/Cb,gBAAgB,CAACK,OAAO,GAAGO,aAAa;MAC1C,CAAC,CAAC,OAAOJ,GAAG,EAAE;QACZ;QACA;QACA;QACA;QACAV,+BAA+B,CAACO,OAAO,GAAGG,GAAG;MAC/C;MAEAZ,WAAW,CAAC,CAAC;IACf;IAEAC,YAAY,CAACiB,aAAa,GAAGH,eAAe;IAC5Cd,YAAY,CAACkB,YAAY,CAAC,CAAC;IAC3BJ,eAAe,CAAC,CAAC;IACjB,OAAO,YAAY;MACjB,OAAOd,YAAY,CAACmB,cAAc,CAAC,CAAC;IACtC,CAAC;EACH,CAAC,EAAE,CAACxB,KAAK,EAAEK,YAAY,CAAC,CAAC;EACzB,OAAOO,aAAa;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,OAAO,SAASa,kBAAkBA,CAACC,OAAO,EAAE;EAC1C,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAGjC,iBAAiB;EAC7B;EAEA,IAAIJ,eAAe,GAAGqC,OAAO,KAAKjC,iBAAiB,GAAGH,sBAAsB,GAAG,YAAY;IACzF,OAAOH,UAAU,CAACuC,OAAO,CAAC;EAC5B,CAAC;EACD,OAAO,SAASC,WAAWA,CAAC7B,QAAQ,EAAEC,UAAU,EAAE;IAChD,IAAIA,UAAU,KAAK,KAAK,CAAC,EAAE;MACzBA,UAAU,GAAGL,WAAW;IAC1B;IAEA,IAAIkC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAI,CAAChC,QAAQ,EAAE;QACb,MAAM,IAAIiC,KAAK,CAAC,yCAAyC,CAAC;MAC5D;MAEA,IAAI,OAAOjC,QAAQ,KAAK,UAAU,EAAE;QAClC,MAAM,IAAIiC,KAAK,CAAC,uDAAuD,CAAC;MAC1E;MAEA,IAAI,OAAOhC,UAAU,KAAK,UAAU,EAAE;QACpC,MAAM,IAAIgC,KAAK,CAAC,iEAAiE,CAAC;MACpF;IACF;IAEA,IAAIC,gBAAgB,GAAG3C,eAAe,CAAC,CAAC;MACpCW,KAAK,GAAGgC,gBAAgB,CAAChC,KAAK;MAC9BC,UAAU,GAAG+B,gBAAgB,CAAC3B,YAAY;IAE9C,IAAIO,aAAa,GAAGf,mCAAmC,CAACC,QAAQ,EAAEC,UAAU,EAAEC,KAAK,EAAEC,UAAU,CAAC;IAChGb,aAAa,CAACwB,aAAa,CAAC;IAC5B,OAAOA,aAAa;EACtB,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,IAAIe,WAAW,GAAG,aAAaF,kBAAkB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}