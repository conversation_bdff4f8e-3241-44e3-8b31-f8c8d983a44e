{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n/**\n * internal utility\n *\n * Why? to read `sx` values and attach component's CSS variables\n *      e.g. <Card sx={{ borderRadius: 0 }} /> should attach\n *          `--Card-radius: 0px` so that developers don't have to remember\n *\n * Why not reuse `styleFunctionSx`?\n *     `styleFunctionSx` is more expensive as it iterates over all the keys\n */\n// eslint-disable-next-line import/prefer-default-export\nexport const resolveSxValue = (_ref, keys) => {\n  let {\n    theme,\n    ownerState\n  } = _ref;\n  let sxObject = {};\n  function resolveSx(sxProp) {\n    if (typeof sxProp === 'function') {\n      const result = sxProp(theme);\n      resolveSx(result);\n    } else if (Array.isArray(sxProp)) {\n      sxProp.forEach(sxItem => {\n        if (typeof sxItem !== 'boolean') {\n          resolveSx(sxItem);\n        }\n      });\n    } else if (typeof sxProp === 'object') {\n      sxObject = _extends({}, sxObject, sxProp);\n    }\n  }\n  if (ownerState.sx) {\n    resolveSx(ownerState.sx);\n    keys.forEach(key => {\n      const value = sxObject[key];\n      if (typeof value === 'string' || typeof value === 'number') {\n        if (key === 'borderRadius') {\n          if (typeof value === 'number') {\n            sxObject[key] = `${value}px`;\n          } else {\n            var _theme$vars;\n            sxObject[key] = ((_theme$vars = theme.vars) == null ? void 0 : _theme$vars.radius[value]) || value;\n          }\n        } else if (['p', 'padding', 'm', 'margin'].indexOf(key) !== -1 && typeof value === 'number') {\n          sxObject[key] = theme.spacing(value);\n        } else {\n          sxObject[key] = value;\n        }\n      } else if (typeof value === 'function') {\n        sxObject[key] = value(theme);\n      } else {\n        sxObject[key] = undefined;\n      }\n    });\n  }\n  return sxObject;\n};", "map": {"version": 3, "names": ["_extends", "resolveSxValue", "_ref", "keys", "theme", "ownerState", "sxObject", "resolveSx", "sxProp", "result", "Array", "isArray", "for<PERSON>ach", "sxItem", "sx", "key", "value", "_theme$vars", "vars", "radius", "indexOf", "spacing", "undefined"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/styles/styleUtils.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n/**\n * internal utility\n *\n * Why? to read `sx` values and attach component's CSS variables\n *      e.g. <Card sx={{ borderRadius: 0 }} /> should attach\n *          `--Card-radius: 0px` so that developers don't have to remember\n *\n * Why not reuse `styleFunctionSx`?\n *     `styleFunctionSx` is more expensive as it iterates over all the keys\n */\n// eslint-disable-next-line import/prefer-default-export\nexport const resolveSxValue = ({\n  theme,\n  ownerState\n}, keys) => {\n  let sxObject = {};\n  function resolveSx(sxProp) {\n    if (typeof sxProp === 'function') {\n      const result = sxProp(theme);\n      resolveSx(result);\n    } else if (Array.isArray(sxProp)) {\n      sxProp.forEach(sxItem => {\n        if (typeof sxItem !== 'boolean') {\n          resolveSx(sxItem);\n        }\n      });\n    } else if (typeof sxProp === 'object') {\n      sxObject = _extends({}, sxObject, sxProp);\n    }\n  }\n  if (ownerState.sx) {\n    resolveSx(ownerState.sx);\n    keys.forEach(key => {\n      const value = sxObject[key];\n      if (typeof value === 'string' || typeof value === 'number') {\n        if (key === 'borderRadius') {\n          if (typeof value === 'number') {\n            sxObject[key] = `${value}px`;\n          } else {\n            var _theme$vars;\n            sxObject[key] = ((_theme$vars = theme.vars) == null ? void 0 : _theme$vars.radius[value]) || value;\n          }\n        } else if (['p', 'padding', 'm', 'margin'].indexOf(key) !== -1 && typeof value === 'number') {\n          sxObject[key] = theme.spacing(value);\n        } else {\n          sxObject[key] = value;\n        }\n      } else if (typeof value === 'function') {\n        sxObject[key] = value(theme);\n      } else {\n        sxObject[key] = undefined;\n      }\n    });\n  }\n  return sxObject;\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,cAAc,GAAGA,CAAAC,IAAA,EAG3BC,IAAI,KAAK;EAAA,IAHmB;IAC7BC,KAAK;IACLC;EACF,CAAC,GAAAH,IAAA;EACC,IAAII,QAAQ,GAAG,CAAC,CAAC;EACjB,SAASC,SAASA,CAACC,MAAM,EAAE;IACzB,IAAI,OAAOA,MAAM,KAAK,UAAU,EAAE;MAChC,MAAMC,MAAM,GAAGD,MAAM,CAACJ,KAAK,CAAC;MAC5BG,SAAS,CAACE,MAAM,CAAC;IACnB,CAAC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,EAAE;MAChCA,MAAM,CAACI,OAAO,CAACC,MAAM,IAAI;QACvB,IAAI,OAAOA,MAAM,KAAK,SAAS,EAAE;UAC/BN,SAAS,CAACM,MAAM,CAAC;QACnB;MACF,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,OAAOL,MAAM,KAAK,QAAQ,EAAE;MACrCF,QAAQ,GAAGN,QAAQ,CAAC,CAAC,CAAC,EAAEM,QAAQ,EAAEE,MAAM,CAAC;IAC3C;EACF;EACA,IAAIH,UAAU,CAACS,EAAE,EAAE;IACjBP,SAAS,CAACF,UAAU,CAACS,EAAE,CAAC;IACxBX,IAAI,CAACS,OAAO,CAACG,GAAG,IAAI;MAClB,MAAMC,KAAK,GAAGV,QAAQ,CAACS,GAAG,CAAC;MAC3B,IAAI,OAAOC,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC1D,IAAID,GAAG,KAAK,cAAc,EAAE;UAC1B,IAAI,OAAOC,KAAK,KAAK,QAAQ,EAAE;YAC7BV,QAAQ,CAACS,GAAG,CAAC,GAAG,GAAGC,KAAK,IAAI;UAC9B,CAAC,MAAM;YACL,IAAIC,WAAW;YACfX,QAAQ,CAACS,GAAG,CAAC,GAAG,CAAC,CAACE,WAAW,GAAGb,KAAK,CAACc,IAAI,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,WAAW,CAACE,MAAM,CAACH,KAAK,CAAC,KAAKA,KAAK;UACpG;QACF,CAAC,MAAM,IAAI,CAAC,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,QAAQ,CAAC,CAACI,OAAO,CAACL,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,OAAOC,KAAK,KAAK,QAAQ,EAAE;UAC3FV,QAAQ,CAACS,GAAG,CAAC,GAAGX,KAAK,CAACiB,OAAO,CAACL,KAAK,CAAC;QACtC,CAAC,MAAM;UACLV,QAAQ,CAACS,GAAG,CAAC,GAAGC,KAAK;QACvB;MACF,CAAC,MAAM,IAAI,OAAOA,KAAK,KAAK,UAAU,EAAE;QACtCV,QAAQ,CAACS,GAAG,CAAC,GAAGC,KAAK,CAACZ,KAAK,CAAC;MAC9B,CAAC,MAAM;QACLE,QAAQ,CAACS,GAAG,CAAC,GAAGO,SAAS;MAC3B;IACF,CAAC,CAAC;EACJ;EACA,OAAOhB,QAAQ;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}