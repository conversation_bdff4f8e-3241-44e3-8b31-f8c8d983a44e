import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  TextField,
  IconButton,
  Paper,
  Typography,
  Chip,
  Tooltip,
  Collapse,
  CircularProgress
} from '@mui/material';
import Iconify from 'components/Iconify/index';
import { mainYellowColor } from "helpers/constants";

const ChatbotBar = ({ planInfo, onPlanUpdate, onSwitchToAgent }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [suggestions] = useState([
    "Mark task as completed",
    "Add new task to milestone",
    "Update task description",
    "Delete completed tasks",
    "Show project progress"
  ]);
  
  const inputRef = useRef(null);

  // Focus input when expanded
  useEffect(() => {
    if (isExpanded && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isExpanded]);

  const handleToggleExpand = () => {
    setIsExpanded(!isExpanded);
  };

  const handleSendMessage = async () => {
    if (!message.trim() || isLoading) return;

    console.log('ChatbotBar - planInfo:', planInfo); // Debug log
    setIsLoading(true);

    try {
      // Validate planInfo before proceeding
      if (!planInfo?.id) {
        console.error('ChatbotBar - Missing planInfo or planInfo.id:', planInfo);
        return;
      }

      // Save conversation to localStorage for Agent tab
      const conversationHistory = JSON.parse(localStorage.getItem('agent_conversations') || '[]');
      const newConversation = {
        id: Date.now(),
        planId: planInfo.id,
        planName: planInfo.name,
        message: message.trim(),
        timestamp: new Date().toISOString(),
        response: null // Will be filled by AI response
      };

      conversationHistory.push(newConversation);
      localStorage.setItem('agent_conversations', JSON.stringify(conversationHistory));

      console.log('ChatbotBar - Conversation saved, switching to agent tab'); // Debug log

      // Switch to Agent tab with the conversation
      if (onSwitchToAgent) {
        onSwitchToAgent({
          message: message.trim(),
          planInfo: planInfo,
          conversationId: newConversation.id
        });
      } else {
        console.error('ChatbotBar - onSwitchToAgent callback not provided');
      }

    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setIsLoading(false);
      setMessage('');
      setIsExpanded(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleSuggestionClick = (suggestion) => {
    setMessage(suggestion);
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  return (
    <Paper
      elevation={0}
      sx={{
        mb: 2,
        borderRadius: '12px',
        border: '1px solid #f0f0f0',
        overflow: 'hidden',
        transition: 'all 0.3s ease',
        backgroundColor: '#fafafa'
      }}
    >
      {/* Chatbot Header */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          p: 2,
          cursor: 'pointer',
          '&:hover': {
            backgroundColor: '#f5f5f5'
          }
        }}
        onClick={handleToggleExpand}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>
          <Box
            sx={{
              width: 32,
              height: 32,
              borderRadius: '50%',
              backgroundColor: mainYellowColor,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
          >
            <Iconify icon="mdi:robot" width={18} height={18} color="#fff" />
          </Box>
          <Box>
            <Typography
              variant="h6"
              sx={{
                fontFamily: '"Recursive Variable", sans-serif',
                fontWeight: 600,
                fontSize: '1rem',
                color: '#333',
                mb: 0.5
              }}
            >
              AI Project Assistant
            </Typography>
            <Typography
              variant="caption"
              sx={{
                color: '#666',
                fontFamily: '"Recursive Variable", sans-serif',
                fontSize: '0.8rem'
              }}
            >
              Ask questions or make changes to your project
            </Typography>
          </Box>
        </Box>

        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <Chip
            label="Beta"
            size="small"
            sx={{
              backgroundColor: `${mainYellowColor}20`,
              color: mainYellowColor,
              fontWeight: 600,
              fontSize: '0.7rem',
              height: '20px'
            }}
          />
          <IconButton size="small">
            <Iconify 
              icon={isExpanded ? "material-symbols:expand-less" : "material-symbols:expand-more"} 
              width={20} 
              height={20} 
              color="#666"
            />
          </IconButton>
        </Box>
      </Box>

      {/* Expanded Content */}
      <Collapse in={isExpanded}>
        <Box sx={{ px: 2, pb: 2 }}>
          {/* Quick Suggestions */}
          <Box sx={{ mb: 2 }}>
            <Typography
              variant="caption"
              sx={{
                color: '#666',
                fontFamily: '"Recursive Variable", sans-serif',
                fontSize: '0.8rem',
                mb: 1,
                display: 'block'
              }}
            >
              Quick suggestions:
            </Typography>
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              {suggestions.map((suggestion, index) => (
                <Chip
                  key={index}
                  label={suggestion}
                  size="small"
                  onClick={() => handleSuggestionClick(suggestion)}
                  sx={{
                    backgroundColor: '#fff',
                    border: '1px solid #e0e0e0',
                    cursor: 'pointer',
                    fontSize: '0.75rem',
                    '&:hover': {
                      backgroundColor: `${mainYellowColor}10`,
                      borderColor: mainYellowColor
                    }
                  }}
                />
              ))}
            </Box>
          </Box>

          {/* Input Field */}
          <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>
            <TextField
              inputRef={inputRef}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Ask me anything about your project or request changes..."
              multiline
              maxRows={3}
              fullWidth
              variant="outlined"
              disabled={isLoading}
              sx={{
                '& .MuiOutlinedInput-root': {
                  borderRadius: '8px',
                  backgroundColor: '#fff',
                  fontFamily: '"Recursive Variable", sans-serif',
                  fontSize: '0.9rem'
                }
              }}
            />
            <Tooltip title="Send message">
              <IconButton
                onClick={handleSendMessage}
                disabled={!message.trim() || isLoading}
                sx={{
                  backgroundColor: message.trim() && !isLoading ? mainYellowColor : '#f0f0f0',
                  color: message.trim() && !isLoading ? '#fff' : '#999',
                  '&:hover': {
                    backgroundColor: message.trim() && !isLoading ? '#E69500' : '#f0f0f0'
                  },
                  mb: 0.5
                }}
              >
                {isLoading ? (
                  <CircularProgress size={20} sx={{ color: '#999' }} />
                ) : (
                  <Iconify icon="material-symbols:send" width={20} height={20} />
                )}
              </IconButton>
            </Tooltip>
          </Box>

          {/* Helper Text */}
          <Typography
            variant="caption"
            sx={{
              color: '#999',
              fontFamily: '"Recursive Variable", sans-serif',
              fontSize: '0.75rem',
              mt: 1,
              display: 'block'
            }}
          >
            💡 I can help you mark tasks as complete, add new tasks, update descriptions, and more!
          </Typography>
        </Box>
      </Collapse>
    </Paper>
  );
};

export default ChatbotBar;
