{"ast": null, "code": "import { unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nconst boxClasses = generateUtilityClasses('MuiBox', ['root']);\nexport default boxClasses;", "map": {"version": 3, "names": ["unstable_generateUtilityClasses", "generateUtilityClasses", "boxClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Box/boxClasses.js"], "sourcesContent": ["import { unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nconst boxClasses = generateUtilityClasses('MuiBox', ['root']);\nexport default boxClasses;"], "mappings": "AAAA,SAASA,+BAA+B,IAAIC,sBAAsB,QAAQ,YAAY;AACtF,MAAMC,UAAU,GAAGD,sBAAsB,CAAC,QAAQ,EAAE,CAAC,MAAM,CAAC,CAAC;AAC7D,eAAeC,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}