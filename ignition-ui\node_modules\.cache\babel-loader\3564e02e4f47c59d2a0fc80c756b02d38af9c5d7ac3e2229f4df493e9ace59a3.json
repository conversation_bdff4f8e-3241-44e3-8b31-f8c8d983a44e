{"ast": null, "code": "import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nconst COMPONENT_NAME = 'Popup';\nexport function getPopupUtilityClass(slot) {\n  return generateUtilityClass(COMPONENT_NAME, slot);\n}\nexport const popupClasses = generateUtilityClasses(COMPONENT_NAME, ['root', 'open']);", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "COMPONENT_NAME", "getPopupUtilityClass", "slot", "popupClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/Unstable_Popup/popupClasses.js"], "sourcesContent": ["import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nconst COMPONENT_NAME = 'Popup';\nexport function getPopupUtilityClass(slot) {\n  return generateUtilityClass(COMPONENT_NAME, slot);\n}\nexport const popupClasses = generateUtilityClasses(COMPONENT_NAME, ['root', 'open']);"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,MAAMC,cAAc,GAAG,OAAO;AAC9B,OAAO,SAASC,oBAAoBA,CAACC,IAAI,EAAE;EACzC,OAAOJ,oBAAoB,CAACE,cAAc,EAAEE,IAAI,CAAC;AACnD;AACA,OAAO,MAAMC,YAAY,GAAGJ,sBAAsB,CAACC,cAAc,EAAE,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}