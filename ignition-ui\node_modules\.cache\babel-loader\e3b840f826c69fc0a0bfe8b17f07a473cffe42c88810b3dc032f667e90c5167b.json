{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"defaultValue\", \"children\", \"disabled\", \"error\", \"onChange\", \"required\", \"slotProps\", \"slots\", \"value\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport useControlled from '@mui/utils/useControlled';\nimport { FormControlContext } from './FormControlContext';\nimport { getFormControlUtilityClass } from './formControlClasses';\nimport { useSlotProps } from '../utils';\nimport { unstable_composeClasses as composeClasses } from '../composeClasses';\nimport { useClassNamesOverride } from '../utils/ClassNameConfigurator';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction hasValue(value) {\n  return value != null && !(Array.isArray(value) && value.length === 0) && value !== '';\n}\nfunction useUtilityClasses(ownerState) {\n  const {\n    disabled,\n    error,\n    filled,\n    focused,\n    required\n  } = ownerState;\n  const slots = {\n    root: ['root', disabled && 'disabled', focused && 'focused', error && 'error', filled && 'filled', required && 'required']\n  };\n  return composeClasses(slots, useClassNamesOverride(getFormControlUtilityClass));\n}\n\n/**\n * Provides context such as filled/focused/error/required for form inputs.\n * Relying on the context provides high flexibility and ensures that the state always stays\n * consistent across the children of the `FormControl`.\n * This context is used by the following components:\n *\n * *   FormLabel\n * *   FormHelperText\n * *   Input\n * *   InputLabel\n *\n * You can find one composition example below and more going to [the demos](https://mui.com/material-ui/react-text-field/#components).\n *\n * ```jsx\n * <FormControl>\n *   <InputLabel htmlFor=\"my-input\">Email address</InputLabel>\n *   <Input id=\"my-input\" aria-describedby=\"my-helper-text\" />\n *   <FormHelperText id=\"my-helper-text\">We'll never share your email.</FormHelperText>\n * </FormControl>\n * ```\n *\n * ⚠️ Only one `Input` can be used within a FormControl because it create visual inconsistencies.\n * For instance, only one input can be focused at the same time, the state shouldn't be shared.\n *\n * Demos:\n *\n * - [Form Control](https://mui.com/base-ui/react-form-control/)\n * - [Input](https://mui.com/joy-ui/react-input/)\n * - [Checkbox](https://mui.com/material-ui/react-checkbox/)\n * - [Radio Group](https://mui.com/material-ui/react-radio-button/)\n * - [Switch](https://mui.com/material-ui/react-switch/)\n * - [Text Field](https://mui.com/material-ui/react-text-field/)\n *\n * API:\n *\n * - [FormControl API](https://mui.com/base-ui/react-form-control/components-api/#form-control)\n */\nconst FormControl = /*#__PURE__*/React.forwardRef(function FormControl(props, forwardedRef) {\n  var _slots$root;\n  const {\n      defaultValue,\n      children,\n      disabled = false,\n      error = false,\n      onChange,\n      required = false,\n      slotProps = {},\n      slots = {},\n      value: incomingValue\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const [value, setValue] = useControlled({\n    controlled: incomingValue,\n    default: defaultValue,\n    name: 'FormControl',\n    state: 'value'\n  });\n  const filled = hasValue(value);\n  const [focusedState, setFocused] = React.useState(false);\n  const focused = focusedState && !disabled;\n  React.useEffect(() => setFocused(isFocused => disabled ? false : isFocused), [disabled]);\n  const ownerState = _extends({}, props, {\n    disabled,\n    error,\n    filled,\n    focused,\n    required\n  });\n  const childContext = React.useMemo(() => {\n    return {\n      disabled,\n      error,\n      filled,\n      focused,\n      onBlur: () => {\n        setFocused(false);\n      },\n      onChange: event => {\n        setValue(event.target.value);\n        onChange == null || onChange(event);\n      },\n      onFocus: () => {\n        setFocused(true);\n      },\n      required,\n      value: value != null ? value : ''\n    };\n  }, [disabled, error, filled, focused, onChange, required, setValue, value]);\n  const classes = useUtilityClasses(ownerState);\n  const renderChildren = () => {\n    if (typeof children === 'function') {\n      return children(childContext);\n    }\n    return children;\n  };\n  const Root = (_slots$root = slots.root) != null ? _slots$root : 'div';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    externalSlotProps: slotProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      ref: forwardedRef,\n      children: renderChildren()\n    },\n    ownerState,\n    className: classes.root\n  });\n  return /*#__PURE__*/_jsx(FormControlContext.Provider, {\n    value: childContext,\n    children: /*#__PURE__*/_jsx(Root, _extends({}, rootProps))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? FormControl.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.node, PropTypes.func]),\n  /**\n   * Class name applied to the root element.\n   */\n  className: PropTypes.string,\n  /**\n   * @ignore\n   */\n  defaultValue: PropTypes.any,\n  /**\n   * If `true`, the label, input and helper text should be displayed in a disabled state.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the label is displayed in an error state.\n   * @default false\n   */\n  error: PropTypes.bool,\n  /**\n   * Callback fired when the form element's value is modified.\n   */\n  onChange: PropTypes.func,\n  /**\n   * If `true`, the label will indicate that the `input` is required.\n   * @default false\n   */\n  required: PropTypes.bool,\n  /**\n   * The props used for each slot inside the FormControl.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the FormControl.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The value of the form element.\n   */\n  value: PropTypes.any\n} : void 0;\nexport { FormControl };", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "useControlled", "FormControlContext", "getFormControlUtilityClass", "useSlotProps", "unstable_composeClasses", "composeClasses", "useClassNamesOverride", "jsx", "_jsx", "hasValue", "value", "Array", "isArray", "length", "useUtilityClasses", "ownerState", "disabled", "error", "filled", "focused", "required", "slots", "root", "FormControl", "forwardRef", "props", "forwardedRef", "_slots$root", "defaultValue", "children", "onChange", "slotProps", "incomingValue", "other", "setValue", "controlled", "default", "name", "state", "focusedState", "setFocused", "useState", "useEffect", "isFocused", "childContext", "useMemo", "onBlur", "event", "target", "onFocus", "classes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Root", "rootProps", "elementType", "externalSlotProps", "externalForwardedProps", "additionalProps", "ref", "className", "Provider", "process", "env", "NODE_ENV", "propTypes", "oneOfType", "node", "func", "string", "any", "bool", "shape", "object"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/FormControl/FormControl.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"defaultValue\", \"children\", \"disabled\", \"error\", \"onChange\", \"required\", \"slotProps\", \"slots\", \"value\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport useControlled from '@mui/utils/useControlled';\nimport { FormControlContext } from './FormControlContext';\nimport { getFormControlUtilityClass } from './formControlClasses';\nimport { useSlotProps } from '../utils';\nimport { unstable_composeClasses as composeClasses } from '../composeClasses';\nimport { useClassNamesOverride } from '../utils/ClassNameConfigurator';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction hasValue(value) {\n  return value != null && !(Array.isArray(value) && value.length === 0) && value !== '';\n}\nfunction useUtilityClasses(ownerState) {\n  const {\n    disabled,\n    error,\n    filled,\n    focused,\n    required\n  } = ownerState;\n  const slots = {\n    root: ['root', disabled && 'disabled', focused && 'focused', error && 'error', filled && 'filled', required && 'required']\n  };\n  return composeClasses(slots, useClassNamesOverride(getFormControlUtilityClass));\n}\n\n/**\n * Provides context such as filled/focused/error/required for form inputs.\n * Relying on the context provides high flexibility and ensures that the state always stays\n * consistent across the children of the `FormControl`.\n * This context is used by the following components:\n *\n * *   FormLabel\n * *   FormHelperText\n * *   Input\n * *   InputLabel\n *\n * You can find one composition example below and more going to [the demos](https://mui.com/material-ui/react-text-field/#components).\n *\n * ```jsx\n * <FormControl>\n *   <InputLabel htmlFor=\"my-input\">Email address</InputLabel>\n *   <Input id=\"my-input\" aria-describedby=\"my-helper-text\" />\n *   <FormHelperText id=\"my-helper-text\">We'll never share your email.</FormHelperText>\n * </FormControl>\n * ```\n *\n * ⚠️ Only one `Input` can be used within a FormControl because it create visual inconsistencies.\n * For instance, only one input can be focused at the same time, the state shouldn't be shared.\n *\n * Demos:\n *\n * - [Form Control](https://mui.com/base-ui/react-form-control/)\n * - [Input](https://mui.com/joy-ui/react-input/)\n * - [Checkbox](https://mui.com/material-ui/react-checkbox/)\n * - [Radio Group](https://mui.com/material-ui/react-radio-button/)\n * - [Switch](https://mui.com/material-ui/react-switch/)\n * - [Text Field](https://mui.com/material-ui/react-text-field/)\n *\n * API:\n *\n * - [FormControl API](https://mui.com/base-ui/react-form-control/components-api/#form-control)\n */\nconst FormControl = /*#__PURE__*/React.forwardRef(function FormControl(props, forwardedRef) {\n  var _slots$root;\n  const {\n      defaultValue,\n      children,\n      disabled = false,\n      error = false,\n      onChange,\n      required = false,\n      slotProps = {},\n      slots = {},\n      value: incomingValue\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const [value, setValue] = useControlled({\n    controlled: incomingValue,\n    default: defaultValue,\n    name: 'FormControl',\n    state: 'value'\n  });\n  const filled = hasValue(value);\n  const [focusedState, setFocused] = React.useState(false);\n  const focused = focusedState && !disabled;\n  React.useEffect(() => setFocused(isFocused => disabled ? false : isFocused), [disabled]);\n  const ownerState = _extends({}, props, {\n    disabled,\n    error,\n    filled,\n    focused,\n    required\n  });\n  const childContext = React.useMemo(() => {\n    return {\n      disabled,\n      error,\n      filled,\n      focused,\n      onBlur: () => {\n        setFocused(false);\n      },\n      onChange: event => {\n        setValue(event.target.value);\n        onChange == null || onChange(event);\n      },\n      onFocus: () => {\n        setFocused(true);\n      },\n      required,\n      value: value != null ? value : ''\n    };\n  }, [disabled, error, filled, focused, onChange, required, setValue, value]);\n  const classes = useUtilityClasses(ownerState);\n  const renderChildren = () => {\n    if (typeof children === 'function') {\n      return children(childContext);\n    }\n    return children;\n  };\n  const Root = (_slots$root = slots.root) != null ? _slots$root : 'div';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    externalSlotProps: slotProps.root,\n    externalForwardedProps: other,\n    additionalProps: {\n      ref: forwardedRef,\n      children: renderChildren()\n    },\n    ownerState,\n    className: classes.root\n  });\n  return /*#__PURE__*/_jsx(FormControlContext.Provider, {\n    value: childContext,\n    children: /*#__PURE__*/_jsx(Root, _extends({}, rootProps))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? FormControl.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.node, PropTypes.func]),\n  /**\n   * Class name applied to the root element.\n   */\n  className: PropTypes.string,\n  /**\n   * @ignore\n   */\n  defaultValue: PropTypes.any,\n  /**\n   * If `true`, the label, input and helper text should be displayed in a disabled state.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the label is displayed in an error state.\n   * @default false\n   */\n  error: PropTypes.bool,\n  /**\n   * Callback fired when the form element's value is modified.\n   */\n  onChange: PropTypes.func,\n  /**\n   * If `true`, the label will indicate that the `input` is required.\n   * @default false\n   */\n  required: PropTypes.bool,\n  /**\n   * The props used for each slot inside the FormControl.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the FormControl.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The value of the form element.\n   */\n  value: PropTypes.any\n} : void 0;\nexport { FormControl };"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,cAAc,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,CAAC;AAC1H,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,aAAa,MAAM,0BAA0B;AACpD,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,0BAA0B,QAAQ,sBAAsB;AACjE,SAASC,YAAY,QAAQ,UAAU;AACvC,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,mBAAmB;AAC7E,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,QAAQA,CAACC,KAAK,EAAE;EACvB,OAAOA,KAAK,IAAI,IAAI,IAAI,EAAEC,KAAK,CAACC,OAAO,CAACF,KAAK,CAAC,IAAIA,KAAK,CAACG,MAAM,KAAK,CAAC,CAAC,IAAIH,KAAK,KAAK,EAAE;AACvF;AACA,SAASI,iBAAiBA,CAACC,UAAU,EAAE;EACrC,MAAM;IACJC,QAAQ;IACRC,KAAK;IACLC,MAAM;IACNC,OAAO;IACPC;EACF,CAAC,GAAGL,UAAU;EACd,MAAMM,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEN,QAAQ,IAAI,UAAU,EAAEG,OAAO,IAAI,SAAS,EAAEF,KAAK,IAAI,OAAO,EAAEC,MAAM,IAAI,QAAQ,EAAEE,QAAQ,IAAI,UAAU;EAC3H,CAAC;EACD,OAAOf,cAAc,CAACgB,KAAK,EAAEf,qBAAqB,CAACJ,0BAA0B,CAAC,CAAC;AACjF;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqB,WAAW,GAAG,aAAazB,KAAK,CAAC0B,UAAU,CAAC,SAASD,WAAWA,CAACE,KAAK,EAAEC,YAAY,EAAE;EAC1F,IAAIC,WAAW;EACf,MAAM;MACFC,YAAY;MACZC,QAAQ;MACRb,QAAQ,GAAG,KAAK;MAChBC,KAAK,GAAG,KAAK;MACba,QAAQ;MACRV,QAAQ,GAAG,KAAK;MAChBW,SAAS,GAAG,CAAC,CAAC;MACdV,KAAK,GAAG,CAAC,CAAC;MACVX,KAAK,EAAEsB;IACT,CAAC,GAAGP,KAAK;IACTQ,KAAK,GAAGrC,6BAA6B,CAAC6B,KAAK,EAAE5B,SAAS,CAAC;EACzD,MAAM,CAACa,KAAK,EAAEwB,QAAQ,CAAC,GAAGlC,aAAa,CAAC;IACtCmC,UAAU,EAAEH,aAAa;IACzBI,OAAO,EAAER,YAAY;IACrBS,IAAI,EAAE,aAAa;IACnBC,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAMpB,MAAM,GAAGT,QAAQ,CAACC,KAAK,CAAC;EAC9B,MAAM,CAAC6B,YAAY,EAAEC,UAAU,CAAC,GAAG1C,KAAK,CAAC2C,QAAQ,CAAC,KAAK,CAAC;EACxD,MAAMtB,OAAO,GAAGoB,YAAY,IAAI,CAACvB,QAAQ;EACzClB,KAAK,CAAC4C,SAAS,CAAC,MAAMF,UAAU,CAACG,SAAS,IAAI3B,QAAQ,GAAG,KAAK,GAAG2B,SAAS,CAAC,EAAE,CAAC3B,QAAQ,CAAC,CAAC;EACxF,MAAMD,UAAU,GAAGpB,QAAQ,CAAC,CAAC,CAAC,EAAE8B,KAAK,EAAE;IACrCT,QAAQ;IACRC,KAAK;IACLC,MAAM;IACNC,OAAO;IACPC;EACF,CAAC,CAAC;EACF,MAAMwB,YAAY,GAAG9C,KAAK,CAAC+C,OAAO,CAAC,MAAM;IACvC,OAAO;MACL7B,QAAQ;MACRC,KAAK;MACLC,MAAM;MACNC,OAAO;MACP2B,MAAM,EAAEA,CAAA,KAAM;QACZN,UAAU,CAAC,KAAK,CAAC;MACnB,CAAC;MACDV,QAAQ,EAAEiB,KAAK,IAAI;QACjBb,QAAQ,CAACa,KAAK,CAACC,MAAM,CAACtC,KAAK,CAAC;QAC5BoB,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAACiB,KAAK,CAAC;MACrC,CAAC;MACDE,OAAO,EAAEA,CAAA,KAAM;QACbT,UAAU,CAAC,IAAI,CAAC;MAClB,CAAC;MACDpB,QAAQ;MACRV,KAAK,EAAEA,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAG;IACjC,CAAC;EACH,CAAC,EAAE,CAACM,QAAQ,EAAEC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAEW,QAAQ,EAAEV,QAAQ,EAAEc,QAAQ,EAAExB,KAAK,CAAC,CAAC;EAC3E,MAAMwC,OAAO,GAAGpC,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMoC,cAAc,GAAGA,CAAA,KAAM;IAC3B,IAAI,OAAOtB,QAAQ,KAAK,UAAU,EAAE;MAClC,OAAOA,QAAQ,CAACe,YAAY,CAAC;IAC/B;IACA,OAAOf,QAAQ;EACjB,CAAC;EACD,MAAMuB,IAAI,GAAG,CAACzB,WAAW,GAAGN,KAAK,CAACC,IAAI,KAAK,IAAI,GAAGK,WAAW,GAAG,KAAK;EACrE,MAAM0B,SAAS,GAAGlD,YAAY,CAAC;IAC7BmD,WAAW,EAAEF,IAAI;IACjBG,iBAAiB,EAAExB,SAAS,CAACT,IAAI;IACjCkC,sBAAsB,EAAEvB,KAAK;IAC7BwB,eAAe,EAAE;MACfC,GAAG,EAAEhC,YAAY;MACjBG,QAAQ,EAAEsB,cAAc,CAAC;IAC3B,CAAC;IACDpC,UAAU;IACV4C,SAAS,EAAET,OAAO,CAAC5B;EACrB,CAAC,CAAC;EACF,OAAO,aAAad,IAAI,CAACP,kBAAkB,CAAC2D,QAAQ,EAAE;IACpDlD,KAAK,EAAEkC,YAAY;IACnBf,QAAQ,EAAE,aAAarB,IAAI,CAAC4C,IAAI,EAAEzD,QAAQ,CAAC,CAAC,CAAC,EAAE0D,SAAS,CAAC;EAC3D,CAAC,CAAC;AACJ,CAAC,CAAC;AACFQ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGxC,WAAW,CAACyC,SAAS,CAAC,yBAAyB;EACrF;EACA;EACA;EACA;EACA;AACF;AACA;EACEnC,QAAQ,EAAE9B,SAAS,CAAC,sCAAsCkE,SAAS,CAAC,CAAClE,SAAS,CAACmE,IAAI,EAAEnE,SAAS,CAACoE,IAAI,CAAC,CAAC;EACrG;AACF;AACA;EACER,SAAS,EAAE5D,SAAS,CAACqE,MAAM;EAC3B;AACF;AACA;EACExC,YAAY,EAAE7B,SAAS,CAACsE,GAAG;EAC3B;AACF;AACA;AACA;EACErD,QAAQ,EAAEjB,SAAS,CAACuE,IAAI;EACxB;AACF;AACA;AACA;EACErD,KAAK,EAAElB,SAAS,CAACuE,IAAI;EACrB;AACF;AACA;EACExC,QAAQ,EAAE/B,SAAS,CAACoE,IAAI;EACxB;AACF;AACA;AACA;EACE/C,QAAQ,EAAErB,SAAS,CAACuE,IAAI;EACxB;AACF;AACA;AACA;EACEvC,SAAS,EAAEhC,SAAS,CAACwE,KAAK,CAAC;IACzBjD,IAAI,EAAEvB,SAAS,CAACkE,SAAS,CAAC,CAAClE,SAAS,CAACoE,IAAI,EAAEpE,SAAS,CAACyE,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACEnD,KAAK,EAAEtB,SAAS,CAACwE,KAAK,CAAC;IACrBjD,IAAI,EAAEvB,SAAS,CAACuD;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACE5C,KAAK,EAAEX,SAAS,CAACsE;AACnB,CAAC,GAAG,KAAK,CAAC;AACV,SAAS9C,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}