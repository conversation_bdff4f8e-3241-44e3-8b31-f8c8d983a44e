{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n/**\n * Combines the two get*Props functions from Base UI hooks into one.\n * Useful when a hook uses two other hooks behind the scenes\n * (such as useSelect that depends on useList and useButton for its root slot).\n *\n * The resulting function will return the combined props.\n * They are merged from left to right, similarly to how Object.assign works.\n *\n * The getSecondProps function will receive the result of the getFirstProps function as its argument,\n * so its event handlers can call the previous handlers and act depending on its result.\n *\n * @param getFirstProps - A getter function that returns the props for the first slot. It receives the external event handlers as its argument.\n * @param getSecondProps - A getter function that returns the props for the second slot. It receives the result of the getFirstProps function as its argument.\n */\nexport function combineHooksSlotProps(getFirstProps, getSecondProps) {\n  return function getCombinedProps() {\n    let external = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    const firstResult = _extends({}, external, getFirstProps(external));\n    const result = _extends({}, firstResult, getSecondProps(firstResult));\n    return result;\n  };\n}", "map": {"version": 3, "names": ["_extends", "combineHooksSlotProps", "getFirstProps", "getSecondProps", "getCombinedProps", "external", "arguments", "length", "undefined", "firstResult", "result"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/utils/combineHooksSlotProps.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n/**\n * Combines the two get*Props functions from Base UI hooks into one.\n * Useful when a hook uses two other hooks behind the scenes\n * (such as useSelect that depends on useList and useButton for its root slot).\n *\n * The resulting function will return the combined props.\n * They are merged from left to right, similarly to how Object.assign works.\n *\n * The getSecondProps function will receive the result of the getFirstProps function as its argument,\n * so its event handlers can call the previous handlers and act depending on its result.\n *\n * @param getFirstProps - A getter function that returns the props for the first slot. It receives the external event handlers as its argument.\n * @param getSecondProps - A getter function that returns the props for the second slot. It receives the result of the getFirstProps function as its argument.\n */\nexport function combineHooksSlotProps(getFirstProps, getSecondProps) {\n  return function getCombinedProps(external = {}) {\n    const firstResult = _extends({}, external, getFirstProps(external));\n    const result = _extends({}, firstResult, getSecondProps(firstResult));\n    return result;\n  };\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,qBAAqBA,CAACC,aAAa,EAAEC,cAAc,EAAE;EACnE,OAAO,SAASC,gBAAgBA,CAAA,EAAgB;IAAA,IAAfC,QAAQ,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAC5C,MAAMG,WAAW,GAAGT,QAAQ,CAAC,CAAC,CAAC,EAAEK,QAAQ,EAAEH,aAAa,CAACG,QAAQ,CAAC,CAAC;IACnE,MAAMK,MAAM,GAAGV,QAAQ,CAAC,CAAC,CAAC,EAAES,WAAW,EAAEN,cAAc,CAACM,WAAW,CAAC,CAAC;IACrE,OAAOC,MAAM;EACf,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}