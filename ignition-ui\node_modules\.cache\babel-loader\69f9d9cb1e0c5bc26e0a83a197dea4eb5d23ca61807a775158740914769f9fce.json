{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useEffect, useCallback } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { fetchPlanInfo, getInvitedUsers, sendInvitation, checkUserExistence, deletePlan, optOutPlan } from '../../services';\nimport { successSnackbar, errorSnackbar } from 'components/Snackbar';\n\n// Status constants\nexport const STATUS = {\n  NOT_STARTED: 1,\n  IN_PROGRESS: 2,\n  COMPLETED: 3\n};\n\n// Status configuration constants\nexport const STATUS_CONFIG = {\n  [STATUS.NOT_STARTED]: {\n    color: '#FFC107',\n    label: 'Not Started',\n    icon: 'material-symbols:hourglass-empty'\n  },\n  [STATUS.IN_PROGRESS]: {\n    color: '#FF9800',\n    label: 'In Progress',\n    icon: 'material-symbols:pending'\n  },\n  [STATUS.COMPLETED]: {\n    color: '#4CAF50',\n    label: 'Completed',\n    icon: 'material-symbols:check-circle'\n  }\n};\nconst usePlanData = planSlug => {\n  _s();\n  const [planInfo, setPlanInfo] = useState(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(false);\n  const [invitedUsers, setInvitedUsers] = useState([]);\n  const [email, setEmail] = useState('');\n  const [isSending, setIsSending] = useState(false);\n  const navigate = useNavigate();\n\n  // Fetch plan info\n  useEffect(() => {\n    const fetchData = async () => {\n      try {\n        setLoading(true);\n        const response = await fetchPlanInfo(planSlug);\n        setPlanInfo(response.data);\n        setError(false);\n      } catch (err) {\n        console.error('Error fetching plan info:', err);\n        setError(true);\n      } finally {\n        setLoading(false);\n      }\n    };\n    if (planSlug) {\n      fetchData();\n    }\n  }, [planSlug]);\n\n  // Fetch invited users - using useCallback to avoid creating a new function on each render\n  const fetchInvitedUsers = useCallback(async () => {\n    try {\n      const response = await getInvitedUsers(planSlug);\n      console.log('API Response - Invited Users:', response);\n\n      // Process data from API\n      let usersData = [];\n      if (response && response.data) {\n        usersData = response.data;\n      } else if (Array.isArray(response)) {\n        usersData = response;\n      }\n      console.log('Processed users data:', usersData);\n      setInvitedUsers(usersData);\n    } catch (error) {\n      console.error('Error fetching invited users:', error);\n      setInvitedUsers([]);\n    }\n  }, [planSlug]);\n\n  // Only call fetchInvitedUsers once when planInfo has been loaded\n  useEffect(() => {\n    if (planSlug && !loading) {\n      fetchInvitedUsers();\n    }\n  }, [planSlug, loading, fetchInvitedUsers]);\n\n  // Log invitedUsers whenever it changes\n  useEffect(() => {\n    console.log('Current invitedUsers state:', invitedUsers);\n  }, [invitedUsers]);\n\n  // Handle email change\n  const handleEmailChange = value => {\n    setEmail(value);\n  };\n\n  // Handle invite user\n  const handleInviteUser = async emailToInvite => {\n    if (!emailToInvite) return;\n    setIsSending(true);\n    try {\n      // Check if user exists\n      const result = await checkUserExistence(emailToInvite);\n\n      // Send invitation regardless of user existence\n      await sendInvitation(planInfo, emailToInvite);\n      await fetchInvitedUsers();\n      successSnackbar(result.exists ? `${emailToInvite} has been added to the plan.` : `Invitation sent to ${emailToInvite}`);\n      return true;\n    } catch (err) {\n      console.error('Error inviting user:', err);\n      errorSnackbar(`Could not send invitation to ${emailToInvite}`);\n      return false;\n    } finally {\n      setIsSending(false);\n    }\n  };\n\n  // Handle delete plan\n  const handleDeletePlan = async () => {\n    try {\n      await deletePlan(planInfo.slug);\n      successSnackbar('Plan has been deleted successfully.');\n      navigate('/d/plan');\n      return true;\n    } catch (err) {\n      console.error('Error deleting plan:', err);\n      errorSnackbar('Could not delete the plan.');\n      return false;\n    }\n  };\n\n  // Handle opt out of plan\n  const handleOptOutPlan = async () => {\n    try {\n      await optOutPlan(planInfo.slug);\n      successSnackbar('You have successfully opted out of the plan.');\n      navigate('/d/');\n      return true;\n    } catch (err) {\n      console.error('Error opting out of plan:', err);\n      errorSnackbar('Could not opt out of the plan.');\n      return false;\n    }\n  };\n\n  // Calculate subtask progress\n  const calculateSubtaskProgress = subtask => {\n    // Kiểm tra subtask tồn tại\n    if (!subtask) {\n      return 0;\n    }\n\n    // If progress is explicitly set, use it\n    if (subtask.progress !== undefined && subtask.progress !== null) {\n      return subtask.progress;\n    }\n\n    // Otherwise, determine based on status\n    switch (subtask.status) {\n      case STATUS.COMPLETED:\n        return 100;\n      case STATUS.IN_PROGRESS:\n        return 50;\n      default:\n        return 0;\n    }\n  };\n\n  // Calculate subtask status\n  const getSubtaskStatus = subtask => {\n    const progress = calculateSubtaskProgress(subtask);\n    if (progress === 100 || subtask.status === STATUS.COMPLETED) {\n      return STATUS.COMPLETED;\n    } else if (progress > 0 || subtask.status === STATUS.IN_PROGRESS) {\n      return STATUS.IN_PROGRESS;\n    }\n    return STATUS.NOT_STARTED;\n  };\n\n  // Calculate task progress based on subtasks\n  const calculateTaskProgress = task => {\n    // Kiểm tra task tồn tại\n    if (!task) {\n      return 0;\n    }\n\n    // Đảm bảo subtasks là một mảng\n    const subtasks = task.subtasks || [];\n\n    // If no subtasks, base on task status\n    if (subtasks.length === 0) {\n      switch (task.status) {\n        case STATUS.COMPLETED:\n          return 100;\n        case STATUS.IN_PROGRESS:\n          return 50;\n        default:\n          return 0;\n      }\n    }\n\n    // Calculate progress based on subtasks\n    let totalProgress = 0;\n    subtasks.forEach(subtask => {\n      totalProgress += calculateSubtaskProgress(subtask);\n    });\n    return Math.floor(totalProgress / subtasks.length);\n  };\n\n  // Determine task status\n  const getTaskStatus = task => {\n    const subtasks = task.subtasks || [];\n\n    // If no subtasks, base on task status\n    if (subtasks.length === 0) {\n      return task.status || STATUS.NOT_STARTED;\n    }\n\n    // Check status of subtasks\n    let completedSubtasks = 0;\n    let inProgressSubtasks = 0;\n    subtasks.forEach(subtask => {\n      const status = getSubtaskStatus(subtask);\n      if (status === STATUS.COMPLETED) {\n        completedSubtasks++;\n      } else if (status === STATUS.IN_PROGRESS) {\n        inProgressSubtasks++;\n      }\n    });\n    if (completedSubtasks === subtasks.length) {\n      return STATUS.COMPLETED;\n    } else if (completedSubtasks > 0 || inProgressSubtasks > 0) {\n      return STATUS.IN_PROGRESS;\n    }\n    return STATUS.NOT_STARTED;\n  };\n\n  // Calculate milestone progress based on tasks\n  const calculateMilestoneProgress = milestone => {\n    // Kiểm tra milestone tồn tại\n    if (!milestone) {\n      return 0;\n    }\n\n    // Đảm bảo tasks là một mảng\n    const tasks = milestone.tasks || [];\n\n    // If no tasks, return 0\n    if (tasks.length === 0) {\n      return 0;\n    }\n\n    // Calculate progress based on tasks\n    let totalProgress = 0;\n    tasks.forEach(task => {\n      // Đảm bảo task tồn tại và có thuộc tính cần thiết\n      if (task) {\n        totalProgress += calculateTaskProgress(task);\n      }\n    });\n    return Math.floor(totalProgress / tasks.length);\n  };\n\n  // Determine milestone status\n  const getMilestoneStatus = milestone => {\n    const tasks = milestone.tasks || [];\n    if (tasks.length === 0) return STATUS.NOT_STARTED;\n    let completedTasks = 0;\n    let inProgressTasks = 0;\n    tasks.forEach(task => {\n      const status = getTaskStatus(task);\n      if (status === STATUS.COMPLETED) {\n        completedTasks++;\n      } else if (status === STATUS.IN_PROGRESS) {\n        inProgressTasks++;\n      }\n    });\n    if (completedTasks === tasks.length) {\n      return STATUS.COMPLETED;\n    } else if (completedTasks > 0 || inProgressTasks > 0) {\n      return STATUS.IN_PROGRESS;\n    }\n    return STATUS.NOT_STARTED;\n  };\n\n  // Calculate overall plan progress\n  const calculatePlanProgress = () => {\n    if (!planInfo || !planInfo.milestones || planInfo.milestones.length === 0) {\n      return 0;\n    }\n    let totalProgress = 0;\n    planInfo.milestones.forEach(milestone => {\n      totalProgress += calculateMilestoneProgress(milestone);\n    });\n    return Math.floor(totalProgress / planInfo.milestones.length);\n  };\n\n  // Determine overall plan status\n  const getPlanStatus = () => {\n    if (!planInfo || !planInfo.milestones || planInfo.milestones.length === 0) {\n      return STATUS.NOT_STARTED;\n    }\n    let completedMilestones = 0;\n    let inProgressMilestones = 0;\n    planInfo.milestones.forEach(milestone => {\n      const status = getMilestoneStatus(milestone);\n      if (status === STATUS.COMPLETED) {\n        completedMilestones++;\n      } else if (status === STATUS.IN_PROGRESS) {\n        inProgressMilestones++;\n      }\n    });\n    if (completedMilestones === planInfo.milestones.length) {\n      return STATUS.COMPLETED;\n    } else if (completedMilestones > 0 || inProgressMilestones > 0) {\n      return STATUS.IN_PROGRESS;\n    }\n    return STATUS.NOT_STARTED;\n  };\n\n  // Calculate plan statistics\n  const calculatePlanStats = () => {\n    var _planInfo$milestones, _planInfo$milestones2;\n    if (!planInfo) return {\n      milestones: 0,\n      tasks: 0,\n      subtasks: 0,\n      progress: 0,\n      completedTasks: 0,\n      inProgressTasks: 0,\n      notStartedTasks: 0,\n      comments: 0,\n      status: STATUS.NOT_STARTED\n    };\n    const milestones = ((_planInfo$milestones = planInfo.milestones) === null || _planInfo$milestones === void 0 ? void 0 : _planInfo$milestones.length) || 0;\n    let tasks = 0;\n    let subtasks = 0;\n    let completedTasks = 0;\n    let inProgressTasks = 0;\n    let notStartedTasks = 0;\n    let comments = 0;\n    (_planInfo$milestones2 = planInfo.milestones) === null || _planInfo$milestones2 === void 0 ? void 0 : _planInfo$milestones2.forEach(milestone => {\n      var _milestone$tasks, _milestone$tasks2;\n      const milestoneTasks = ((_milestone$tasks = milestone.tasks) === null || _milestone$tasks === void 0 ? void 0 : _milestone$tasks.length) || 0;\n      tasks += milestoneTasks;\n      (_milestone$tasks2 = milestone.tasks) === null || _milestone$tasks2 === void 0 ? void 0 : _milestone$tasks2.forEach(task => {\n        var _task$subtasks, _task$comments;\n        subtasks += ((_task$subtasks = task.subtasks) === null || _task$subtasks === void 0 ? void 0 : _task$subtasks.length) || 0;\n        comments += ((_task$comments = task.comments) === null || _task$comments === void 0 ? void 0 : _task$comments.length) || 0;\n\n        // Use getTaskStatus to determine accurate status\n        const taskStatus = getTaskStatus(task);\n        if (taskStatus === STATUS.COMPLETED) {\n          completedTasks++;\n        } else if (taskStatus === STATUS.IN_PROGRESS) {\n          inProgressTasks++;\n        } else {\n          notStartedTasks++;\n        }\n      });\n    });\n\n    // Use calculatePlanProgress to calculate accurate progress\n    const progress = calculatePlanProgress();\n    const status = getPlanStatus();\n    return {\n      milestones,\n      tasks,\n      subtasks,\n      progress,\n      completedTasks,\n      inProgressTasks,\n      notStartedTasks,\n      comments,\n      status\n    };\n  };\n  return {\n    planInfo,\n    loading,\n    error,\n    invitedUsers,\n    email,\n    isSending,\n    fetchInvitedUsers,\n    handleEmailChange,\n    handleInviteUser,\n    handleDeletePlan,\n    calculatePlanStats,\n    // Export calculation functions for use in other components\n    calculateSubtaskProgress,\n    getSubtaskStatus,\n    calculateTaskProgress,\n    getTaskStatus,\n    calculateMilestoneProgress,\n    getMilestoneStatus,\n    calculatePlanProgress,\n    getPlanStatus,\n    STATUS,\n    STATUS_CONFIG\n  };\n};\n_s(usePlanData, \"h4o3G8u0dPQOUiIbz5fyVsM/ass=\", false, function () {\n  return [useNavigate];\n});\nexport default usePlanData;", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "useNavigate", "fetchPlanInfo", "getInvitedUsers", "sendInvitation", "checkUserExistence", "deletePlan", "optOutPlan", "successSnackbar", "errorSnackbar", "STATUS", "NOT_STARTED", "IN_PROGRESS", "COMPLETED", "STATUS_CONFIG", "color", "label", "icon", "usePlanData", "planSlug", "_s", "planInfo", "setPlanInfo", "loading", "setLoading", "error", "setError", "invitedUsers", "setInvitedUsers", "email", "setEmail", "isSending", "setIsSending", "navigate", "fetchData", "response", "data", "err", "console", "fetchInvitedUsers", "log", "usersData", "Array", "isArray", "handleEmailChange", "value", "handleInviteUser", "emailToInvite", "result", "exists", "handleDeletePlan", "slug", "handleOptOutPlan", "calculateSubtaskProgress", "subtask", "progress", "undefined", "status", "getSubtaskStatus", "calculateTaskProgress", "task", "subtasks", "length", "totalProgress", "for<PERSON>ach", "Math", "floor", "getTaskStatus", "completedSubtasks", "inProgressSubtasks", "calculateMilestoneProgress", "milestone", "tasks", "getMilestoneStatus", "completedTasks", "inProgressTasks", "calculatePlanProgress", "milestones", "getPlanStatus", "completedMilestones", "inProgressMilestones", "calculatePlanStats", "_planInfo$milestones", "_planInfo$milestones2", "notStartedTasks", "comments", "_milestone$tasks", "_milestone$tasks2", "milestoneTasks", "_task$subtasks", "_task$comments", "taskStatus"], "sources": ["C:/ignition/ignition-ui/src/views/plan/detail/hooks/usePlanData.js"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\r\nimport { useNavigate } from 'react-router-dom';\r\nimport {\r\n  fetchPlanInfo,\r\n  getInvitedUsers,\r\n  sendInvitation,\r\n  checkUserExistence,\r\n  deletePlan,\r\n  optOutPlan\r\n} from '../../services';\r\nimport { successSnackbar, errorSnackbar } from 'components/Snackbar';\r\n\r\n// Status constants\r\nexport const STATUS = {\r\n  NOT_STARTED: 1,\r\n  IN_PROGRESS: 2,\r\n  COMPLETED: 3\r\n};\r\n\r\n// Status configuration constants\r\nexport const STATUS_CONFIG = {\r\n  [STATUS.NOT_STARTED]: { \r\n    color: '#FFC107', \r\n    label: 'Not Started', \r\n    icon: 'material-symbols:hourglass-empty' \r\n  },\r\n  [STATUS.IN_PROGRESS]: { \r\n    color: '#FF9800', \r\n    label: 'In Progress', \r\n    icon: 'material-symbols:pending' \r\n  },\r\n  [STATUS.COMPLETED]: { \r\n    color: '#4CAF50', \r\n    label: 'Completed', \r\n    icon: 'material-symbols:check-circle' \r\n  }\r\n};\r\n\r\nconst usePlanData = (planSlug) => {\r\n  const [planInfo, setPlanInfo] = useState(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState(false);\r\n  const [invitedUsers, setInvitedUsers] = useState([]);\r\n  const [email, setEmail] = useState('');\r\n  const [isSending, setIsSending] = useState(false);\r\n  const navigate = useNavigate();\r\n\r\n  // Fetch plan info\r\n  useEffect(() => {\r\n    const fetchData = async () => {\r\n      try {\r\n        setLoading(true);\r\n        const response = await fetchPlanInfo(planSlug);\r\n        setPlanInfo(response.data);\r\n        setError(false);\r\n      } catch (err) {\r\n        console.error('Error fetching plan info:', err);\r\n        setError(true);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    if (planSlug) {\r\n      fetchData();\r\n    }\r\n  }, [planSlug]);\r\n\r\n  // Fetch invited users - using useCallback to avoid creating a new function on each render\r\n  const fetchInvitedUsers = useCallback(async () => {\r\n    try {\r\n      const response = await getInvitedUsers(planSlug);\r\n      console.log('API Response - Invited Users:', response);\r\n      \r\n      // Process data from API\r\n      let usersData = [];\r\n      if (response && response.data) {\r\n        usersData = response.data;\r\n      } else if (Array.isArray(response)) {\r\n        usersData = response;\r\n      }\r\n      \r\n      console.log('Processed users data:', usersData);\r\n      setInvitedUsers(usersData);\r\n    } catch (error) {\r\n      console.error('Error fetching invited users:', error);\r\n      setInvitedUsers([]);\r\n    }\r\n  }, [planSlug]);\r\n\r\n  // Only call fetchInvitedUsers once when planInfo has been loaded\r\n  useEffect(() => {\r\n    if (planSlug && !loading) {\r\n      fetchInvitedUsers();\r\n    }\r\n  }, [planSlug, loading, fetchInvitedUsers]);\r\n\r\n  // Log invitedUsers whenever it changes\r\n  useEffect(() => {\r\n    console.log('Current invitedUsers state:', invitedUsers);\r\n  }, [invitedUsers]);\r\n\r\n  // Handle email change\r\n  const handleEmailChange = (value) => {\r\n    setEmail(value);\r\n  };\r\n\r\n  // Handle invite user\r\n  const handleInviteUser = async (emailToInvite) => {\r\n    if (!emailToInvite) return;\r\n    \r\n    setIsSending(true);\r\n    try {\r\n      // Check if user exists\r\n      const result = await checkUserExistence(emailToInvite);\r\n      \r\n      // Send invitation regardless of user existence\r\n      await sendInvitation(planInfo, emailToInvite);\r\n      await fetchInvitedUsers();\r\n      \r\n      successSnackbar(\r\n        result.exists \r\n          ? `${emailToInvite} has been added to the plan.` \r\n          : `Invitation sent to ${emailToInvite}`\r\n      );\r\n      \r\n      return true;\r\n    } catch (err) {\r\n      console.error('Error inviting user:', err);\r\n      errorSnackbar(`Could not send invitation to ${emailToInvite}`);\r\n      return false;\r\n    } finally {\r\n      setIsSending(false);\r\n    }\r\n  };\r\n\r\n  // Handle delete plan\r\n  const handleDeletePlan = async () => {\r\n    try {\r\n      await deletePlan(planInfo.slug);\r\n      successSnackbar('Plan has been deleted successfully.');\r\n      navigate('/d/plan');\r\n      return true;\r\n    } catch (err) {\r\n      console.error('Error deleting plan:', err);\r\n      errorSnackbar('Could not delete the plan.');\r\n      return false;\r\n    }\r\n  };\r\n\r\n  // Handle opt out of plan\r\n  const handleOptOutPlan = async () => {\r\n    try {\r\n      await optOutPlan(planInfo.slug);\r\n      successSnackbar('You have successfully opted out of the plan.');\r\n      navigate('/d/');\r\n      return true;\r\n    } catch (err) {\r\n      console.error('Error opting out of plan:', err);\r\n      errorSnackbar('Could not opt out of the plan.');\r\n      return false;\r\n    }\r\n  };\r\n\r\n  // Calculate subtask progress\r\n  const calculateSubtaskProgress = (subtask) => {\r\n    // Kiểm tra subtask tồn tại\r\n    if (!subtask) {\r\n      return 0;\r\n    }\r\n    \r\n    // If progress is explicitly set, use it\r\n    if (subtask.progress !== undefined && subtask.progress !== null) {\r\n      return subtask.progress;\r\n    }\r\n    \r\n    // Otherwise, determine based on status\r\n    switch (subtask.status) {\r\n      case STATUS.COMPLETED:\r\n        return 100;\r\n      case STATUS.IN_PROGRESS:\r\n        return 50;\r\n      default:\r\n        return 0;\r\n    }\r\n  };\r\n\r\n  // Calculate subtask status\r\n  const getSubtaskStatus = (subtask) => {\r\n    const progress = calculateSubtaskProgress(subtask);\r\n    \r\n    if (progress === 100 || subtask.status === STATUS.COMPLETED) {\r\n      return STATUS.COMPLETED;\r\n    } else if (progress > 0 || subtask.status === STATUS.IN_PROGRESS) {\r\n      return STATUS.IN_PROGRESS;\r\n    }\r\n    \r\n    return STATUS.NOT_STARTED;\r\n  };\r\n\r\n  // Calculate task progress based on subtasks\r\n  const calculateTaskProgress = (task) => {\r\n    // Kiểm tra task tồn tại\r\n    if (!task) {\r\n      return 0;\r\n    }\r\n    \r\n    // Đảm bảo subtasks là một mảng\r\n    const subtasks = task.subtasks || [];\r\n    \r\n    // If no subtasks, base on task status\r\n    if (subtasks.length === 0) {\r\n      switch (task.status) {\r\n        case STATUS.COMPLETED:\r\n          return 100;\r\n        case STATUS.IN_PROGRESS:\r\n          return 50;\r\n        default:\r\n          return 0;\r\n      }\r\n    }\r\n    \r\n    // Calculate progress based on subtasks\r\n    let totalProgress = 0;\r\n    subtasks.forEach(subtask => {\r\n      totalProgress += calculateSubtaskProgress(subtask);\r\n    });\r\n    \r\n    return Math.floor(totalProgress / subtasks.length);\r\n  };\r\n\r\n  // Determine task status\r\n  const getTaskStatus = (task) => {\r\n    const subtasks = task.subtasks || [];\r\n    \r\n    // If no subtasks, base on task status\r\n    if (subtasks.length === 0) {\r\n      return task.status || STATUS.NOT_STARTED;\r\n    }\r\n    \r\n    // Check status of subtasks\r\n    let completedSubtasks = 0;\r\n    let inProgressSubtasks = 0;\r\n    \r\n    subtasks.forEach(subtask => {\r\n      const status = getSubtaskStatus(subtask);\r\n      if (status === STATUS.COMPLETED) {\r\n        completedSubtasks++;\r\n      } else if (status === STATUS.IN_PROGRESS) {\r\n        inProgressSubtasks++;\r\n      }\r\n    });\r\n    \r\n    if (completedSubtasks === subtasks.length) {\r\n      return STATUS.COMPLETED;\r\n    } else if (completedSubtasks > 0 || inProgressSubtasks > 0) {\r\n      return STATUS.IN_PROGRESS;\r\n    }\r\n    \r\n    return STATUS.NOT_STARTED;\r\n  };\r\n\r\n  // Calculate milestone progress based on tasks\r\n  const calculateMilestoneProgress = (milestone) => {\r\n    // Kiểm tra milestone tồn tại\r\n    if (!milestone) {\r\n      return 0;\r\n    }\r\n    \r\n    // Đảm bảo tasks là một mảng\r\n    const tasks = milestone.tasks || [];\r\n    \r\n    // If no tasks, return 0\r\n    if (tasks.length === 0) {\r\n      return 0;\r\n    }\r\n    \r\n    // Calculate progress based on tasks\r\n    let totalProgress = 0;\r\n    tasks.forEach(task => {\r\n      // Đảm bảo task tồn tại và có thuộc tính cần thiết\r\n      if (task) {\r\n        totalProgress += calculateTaskProgress(task);\r\n      }\r\n    });\r\n    \r\n    return Math.floor(totalProgress / tasks.length);\r\n  };\r\n\r\n  // Determine milestone status\r\n  const getMilestoneStatus = (milestone) => {\r\n    const tasks = milestone.tasks || [];\r\n    if (tasks.length === 0) return STATUS.NOT_STARTED;\r\n    \r\n    let completedTasks = 0;\r\n    let inProgressTasks = 0;\r\n    \r\n    tasks.forEach(task => {\r\n      const status = getTaskStatus(task);\r\n      if (status === STATUS.COMPLETED) {\r\n        completedTasks++;\r\n      } else if (status === STATUS.IN_PROGRESS) {\r\n        inProgressTasks++;\r\n      }\r\n    });\r\n    \r\n    if (completedTasks === tasks.length) {\r\n      return STATUS.COMPLETED;\r\n    } else if (completedTasks > 0 || inProgressTasks > 0) {\r\n      return STATUS.IN_PROGRESS;\r\n    }\r\n    \r\n    return STATUS.NOT_STARTED;\r\n  };\r\n\r\n  // Calculate overall plan progress\r\n  const calculatePlanProgress = () => {\r\n    if (!planInfo || !planInfo.milestones || planInfo.milestones.length === 0) {\r\n      return 0;\r\n    }\r\n    \r\n    let totalProgress = 0;\r\n    planInfo.milestones.forEach(milestone => {\r\n      totalProgress += calculateMilestoneProgress(milestone);\r\n    });\r\n    \r\n    return Math.floor(totalProgress / planInfo.milestones.length);\r\n  };\r\n\r\n  // Determine overall plan status\r\n  const getPlanStatus = () => {\r\n    if (!planInfo || !planInfo.milestones || planInfo.milestones.length === 0) {\r\n      return STATUS.NOT_STARTED;\r\n    }\r\n    \r\n    let completedMilestones = 0;\r\n    let inProgressMilestones = 0;\r\n    \r\n    planInfo.milestones.forEach(milestone => {\r\n      const status = getMilestoneStatus(milestone);\r\n      if (status === STATUS.COMPLETED) {\r\n        completedMilestones++;\r\n      } else if (status === STATUS.IN_PROGRESS) {\r\n        inProgressMilestones++;\r\n      }\r\n    });\r\n    \r\n    if (completedMilestones === planInfo.milestones.length) {\r\n      return STATUS.COMPLETED;\r\n    } else if (completedMilestones > 0 || inProgressMilestones > 0) {\r\n      return STATUS.IN_PROGRESS;\r\n    }\r\n    \r\n    return STATUS.NOT_STARTED;\r\n  };\r\n\r\n  // Calculate plan statistics\r\n  const calculatePlanStats = () => {\r\n    if (!planInfo) return { \r\n      milestones: 0, \r\n      tasks: 0, \r\n      subtasks: 0, \r\n      progress: 0,\r\n      completedTasks: 0,\r\n      inProgressTasks: 0,\r\n      notStartedTasks: 0,\r\n      comments: 0,\r\n      status: STATUS.NOT_STARTED\r\n    };\r\n    \r\n    const milestones = planInfo.milestones?.length || 0;\r\n    \r\n    let tasks = 0;\r\n    let subtasks = 0;\r\n    let completedTasks = 0;\r\n    let inProgressTasks = 0;\r\n    let notStartedTasks = 0;\r\n    let comments = 0;\r\n    \r\n    planInfo.milestones?.forEach(milestone => {\r\n      const milestoneTasks = milestone.tasks?.length || 0;\r\n      tasks += milestoneTasks;\r\n      \r\n      milestone.tasks?.forEach(task => {\r\n        subtasks += task.subtasks?.length || 0;\r\n        comments += task.comments?.length || 0;\r\n        \r\n        // Use getTaskStatus to determine accurate status\r\n        const taskStatus = getTaskStatus(task);\r\n        if (taskStatus === STATUS.COMPLETED) {\r\n          completedTasks++;\r\n        } else if (taskStatus === STATUS.IN_PROGRESS) {\r\n          inProgressTasks++;\r\n        } else {\r\n          notStartedTasks++;\r\n        }\r\n      });\r\n    });\r\n    \r\n    // Use calculatePlanProgress to calculate accurate progress\r\n    const progress = calculatePlanProgress();\r\n    const status = getPlanStatus();\r\n    \r\n    return {\r\n      milestones,\r\n      tasks,\r\n      subtasks,\r\n      progress,\r\n      completedTasks,\r\n      inProgressTasks,\r\n      notStartedTasks,\r\n      comments,\r\n      status\r\n    };\r\n  };\r\n\r\n  return {\r\n    planInfo,\r\n    loading,\r\n    error,\r\n    invitedUsers,\r\n    email,\r\n    isSending,\r\n    fetchInvitedUsers,\r\n    handleEmailChange,\r\n    handleInviteUser,\r\n    handleDeletePlan,\r\n    calculatePlanStats,\r\n    // Export calculation functions for use in other components\r\n    calculateSubtaskProgress,\r\n    getSubtaskStatus,\r\n    calculateTaskProgress,\r\n    getTaskStatus,\r\n    calculateMilestoneProgress,\r\n    getMilestoneStatus,\r\n    calculatePlanProgress,\r\n    getPlanStatus,\r\n    STATUS,\r\n    STATUS_CONFIG\r\n  };\r\n};\r\n\r\nexport default usePlanData; "], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACxD,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SACEC,aAAa,EACbC,eAAe,EACfC,cAAc,EACdC,kBAAkB,EAClBC,UAAU,EACVC,UAAU,QACL,gBAAgB;AACvB,SAASC,eAAe,EAAEC,aAAa,QAAQ,qBAAqB;;AAEpE;AACA,OAAO,MAAMC,MAAM,GAAG;EACpBC,WAAW,EAAE,CAAC;EACdC,WAAW,EAAE,CAAC;EACdC,SAAS,EAAE;AACb,CAAC;;AAED;AACA,OAAO,MAAMC,aAAa,GAAG;EAC3B,CAACJ,MAAM,CAACC,WAAW,GAAG;IACpBI,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,aAAa;IACpBC,IAAI,EAAE;EACR,CAAC;EACD,CAACP,MAAM,CAACE,WAAW,GAAG;IACpBG,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,aAAa;IACpBC,IAAI,EAAE;EACR,CAAC;EACD,CAACP,MAAM,CAACG,SAAS,GAAG;IAClBE,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,WAAW;IAClBC,IAAI,EAAE;EACR;AACF,CAAC;AAED,MAAMC,WAAW,GAAIC,QAAQ,IAAK;EAAAC,EAAA;EAChC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAC9C,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC2B,KAAK,EAAEC,QAAQ,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACzC,MAAM,CAAC6B,YAAY,EAAEC,eAAe,CAAC,GAAG9B,QAAQ,CAAC,EAAE,CAAC;EACpD,MAAM,CAAC+B,KAAK,EAAEC,QAAQ,CAAC,GAAGhC,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAMmC,QAAQ,GAAGhC,WAAW,CAAC,CAAC;;EAE9B;EACAF,SAAS,CAAC,MAAM;IACd,MAAMmC,SAAS,GAAG,MAAAA,CAAA,KAAY;MAC5B,IAAI;QACFV,UAAU,CAAC,IAAI,CAAC;QAChB,MAAMW,QAAQ,GAAG,MAAMjC,aAAa,CAACiB,QAAQ,CAAC;QAC9CG,WAAW,CAACa,QAAQ,CAACC,IAAI,CAAC;QAC1BV,QAAQ,CAAC,KAAK,CAAC;MACjB,CAAC,CAAC,OAAOW,GAAG,EAAE;QACZC,OAAO,CAACb,KAAK,CAAC,2BAA2B,EAAEY,GAAG,CAAC;QAC/CX,QAAQ,CAAC,IAAI,CAAC;MAChB,CAAC,SAAS;QACRF,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAED,IAAIL,QAAQ,EAAE;MACZe,SAAS,CAAC,CAAC;IACb;EACF,CAAC,EAAE,CAACf,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMoB,iBAAiB,GAAGvC,WAAW,CAAC,YAAY;IAChD,IAAI;MACF,MAAMmC,QAAQ,GAAG,MAAMhC,eAAe,CAACgB,QAAQ,CAAC;MAChDmB,OAAO,CAACE,GAAG,CAAC,+BAA+B,EAAEL,QAAQ,CAAC;;MAEtD;MACA,IAAIM,SAAS,GAAG,EAAE;MAClB,IAAIN,QAAQ,IAAIA,QAAQ,CAACC,IAAI,EAAE;QAC7BK,SAAS,GAAGN,QAAQ,CAACC,IAAI;MAC3B,CAAC,MAAM,IAAIM,KAAK,CAACC,OAAO,CAACR,QAAQ,CAAC,EAAE;QAClCM,SAAS,GAAGN,QAAQ;MACtB;MAEAG,OAAO,CAACE,GAAG,CAAC,uBAAuB,EAAEC,SAAS,CAAC;MAC/Cb,eAAe,CAACa,SAAS,CAAC;IAC5B,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACda,OAAO,CAACb,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrDG,eAAe,CAAC,EAAE,CAAC;IACrB;EACF,CAAC,EAAE,CAACT,QAAQ,CAAC,CAAC;;EAEd;EACApB,SAAS,CAAC,MAAM;IACd,IAAIoB,QAAQ,IAAI,CAACI,OAAO,EAAE;MACxBgB,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC,EAAE,CAACpB,QAAQ,EAAEI,OAAO,EAAEgB,iBAAiB,CAAC,CAAC;;EAE1C;EACAxC,SAAS,CAAC,MAAM;IACduC,OAAO,CAACE,GAAG,CAAC,6BAA6B,EAAEb,YAAY,CAAC;EAC1D,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;;EAElB;EACA,MAAMiB,iBAAiB,GAAIC,KAAK,IAAK;IACnCf,QAAQ,CAACe,KAAK,CAAC;EACjB,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAG,MAAOC,aAAa,IAAK;IAChD,IAAI,CAACA,aAAa,EAAE;IAEpBf,YAAY,CAAC,IAAI,CAAC;IAClB,IAAI;MACF;MACA,MAAMgB,MAAM,GAAG,MAAM3C,kBAAkB,CAAC0C,aAAa,CAAC;;MAEtD;MACA,MAAM3C,cAAc,CAACiB,QAAQ,EAAE0B,aAAa,CAAC;MAC7C,MAAMR,iBAAiB,CAAC,CAAC;MAEzB/B,eAAe,CACbwC,MAAM,CAACC,MAAM,GACT,GAAGF,aAAa,8BAA8B,GAC9C,sBAAsBA,aAAa,EACzC,CAAC;MAED,OAAO,IAAI;IACb,CAAC,CAAC,OAAOV,GAAG,EAAE;MACZC,OAAO,CAACb,KAAK,CAAC,sBAAsB,EAAEY,GAAG,CAAC;MAC1C5B,aAAa,CAAC,gCAAgCsC,aAAa,EAAE,CAAC;MAC9D,OAAO,KAAK;IACd,CAAC,SAAS;MACRf,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMkB,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAM5C,UAAU,CAACe,QAAQ,CAAC8B,IAAI,CAAC;MAC/B3C,eAAe,CAAC,qCAAqC,CAAC;MACtDyB,QAAQ,CAAC,SAAS,CAAC;MACnB,OAAO,IAAI;IACb,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZC,OAAO,CAACb,KAAK,CAAC,sBAAsB,EAAEY,GAAG,CAAC;MAC1C5B,aAAa,CAAC,4BAA4B,CAAC;MAC3C,OAAO,KAAK;IACd;EACF,CAAC;;EAED;EACA,MAAM2C,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAM7C,UAAU,CAACc,QAAQ,CAAC8B,IAAI,CAAC;MAC/B3C,eAAe,CAAC,8CAA8C,CAAC;MAC/DyB,QAAQ,CAAC,KAAK,CAAC;MACf,OAAO,IAAI;IACb,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZC,OAAO,CAACb,KAAK,CAAC,2BAA2B,EAAEY,GAAG,CAAC;MAC/C5B,aAAa,CAAC,gCAAgC,CAAC;MAC/C,OAAO,KAAK;IACd;EACF,CAAC;;EAED;EACA,MAAM4C,wBAAwB,GAAIC,OAAO,IAAK;IAC5C;IACA,IAAI,CAACA,OAAO,EAAE;MACZ,OAAO,CAAC;IACV;;IAEA;IACA,IAAIA,OAAO,CAACC,QAAQ,KAAKC,SAAS,IAAIF,OAAO,CAACC,QAAQ,KAAK,IAAI,EAAE;MAC/D,OAAOD,OAAO,CAACC,QAAQ;IACzB;;IAEA;IACA,QAAQD,OAAO,CAACG,MAAM;MACpB,KAAK/C,MAAM,CAACG,SAAS;QACnB,OAAO,GAAG;MACZ,KAAKH,MAAM,CAACE,WAAW;QACrB,OAAO,EAAE;MACX;QACE,OAAO,CAAC;IACZ;EACF,CAAC;;EAED;EACA,MAAM8C,gBAAgB,GAAIJ,OAAO,IAAK;IACpC,MAAMC,QAAQ,GAAGF,wBAAwB,CAACC,OAAO,CAAC;IAElD,IAAIC,QAAQ,KAAK,GAAG,IAAID,OAAO,CAACG,MAAM,KAAK/C,MAAM,CAACG,SAAS,EAAE;MAC3D,OAAOH,MAAM,CAACG,SAAS;IACzB,CAAC,MAAM,IAAI0C,QAAQ,GAAG,CAAC,IAAID,OAAO,CAACG,MAAM,KAAK/C,MAAM,CAACE,WAAW,EAAE;MAChE,OAAOF,MAAM,CAACE,WAAW;IAC3B;IAEA,OAAOF,MAAM,CAACC,WAAW;EAC3B,CAAC;;EAED;EACA,MAAMgD,qBAAqB,GAAIC,IAAI,IAAK;IACtC;IACA,IAAI,CAACA,IAAI,EAAE;MACT,OAAO,CAAC;IACV;;IAEA;IACA,MAAMC,QAAQ,GAAGD,IAAI,CAACC,QAAQ,IAAI,EAAE;;IAEpC;IACA,IAAIA,QAAQ,CAACC,MAAM,KAAK,CAAC,EAAE;MACzB,QAAQF,IAAI,CAACH,MAAM;QACjB,KAAK/C,MAAM,CAACG,SAAS;UACnB,OAAO,GAAG;QACZ,KAAKH,MAAM,CAACE,WAAW;UACrB,OAAO,EAAE;QACX;UACE,OAAO,CAAC;MACZ;IACF;;IAEA;IACA,IAAImD,aAAa,GAAG,CAAC;IACrBF,QAAQ,CAACG,OAAO,CAACV,OAAO,IAAI;MAC1BS,aAAa,IAAIV,wBAAwB,CAACC,OAAO,CAAC;IACpD,CAAC,CAAC;IAEF,OAAOW,IAAI,CAACC,KAAK,CAACH,aAAa,GAAGF,QAAQ,CAACC,MAAM,CAAC;EACpD,CAAC;;EAED;EACA,MAAMK,aAAa,GAAIP,IAAI,IAAK;IAC9B,MAAMC,QAAQ,GAAGD,IAAI,CAACC,QAAQ,IAAI,EAAE;;IAEpC;IACA,IAAIA,QAAQ,CAACC,MAAM,KAAK,CAAC,EAAE;MACzB,OAAOF,IAAI,CAACH,MAAM,IAAI/C,MAAM,CAACC,WAAW;IAC1C;;IAEA;IACA,IAAIyD,iBAAiB,GAAG,CAAC;IACzB,IAAIC,kBAAkB,GAAG,CAAC;IAE1BR,QAAQ,CAACG,OAAO,CAACV,OAAO,IAAI;MAC1B,MAAMG,MAAM,GAAGC,gBAAgB,CAACJ,OAAO,CAAC;MACxC,IAAIG,MAAM,KAAK/C,MAAM,CAACG,SAAS,EAAE;QAC/BuD,iBAAiB,EAAE;MACrB,CAAC,MAAM,IAAIX,MAAM,KAAK/C,MAAM,CAACE,WAAW,EAAE;QACxCyD,kBAAkB,EAAE;MACtB;IACF,CAAC,CAAC;IAEF,IAAID,iBAAiB,KAAKP,QAAQ,CAACC,MAAM,EAAE;MACzC,OAAOpD,MAAM,CAACG,SAAS;IACzB,CAAC,MAAM,IAAIuD,iBAAiB,GAAG,CAAC,IAAIC,kBAAkB,GAAG,CAAC,EAAE;MAC1D,OAAO3D,MAAM,CAACE,WAAW;IAC3B;IAEA,OAAOF,MAAM,CAACC,WAAW;EAC3B,CAAC;;EAED;EACA,MAAM2D,0BAA0B,GAAIC,SAAS,IAAK;IAChD;IACA,IAAI,CAACA,SAAS,EAAE;MACd,OAAO,CAAC;IACV;;IAEA;IACA,MAAMC,KAAK,GAAGD,SAAS,CAACC,KAAK,IAAI,EAAE;;IAEnC;IACA,IAAIA,KAAK,CAACV,MAAM,KAAK,CAAC,EAAE;MACtB,OAAO,CAAC;IACV;;IAEA;IACA,IAAIC,aAAa,GAAG,CAAC;IACrBS,KAAK,CAACR,OAAO,CAACJ,IAAI,IAAI;MACpB;MACA,IAAIA,IAAI,EAAE;QACRG,aAAa,IAAIJ,qBAAqB,CAACC,IAAI,CAAC;MAC9C;IACF,CAAC,CAAC;IAEF,OAAOK,IAAI,CAACC,KAAK,CAACH,aAAa,GAAGS,KAAK,CAACV,MAAM,CAAC;EACjD,CAAC;;EAED;EACA,MAAMW,kBAAkB,GAAIF,SAAS,IAAK;IACxC,MAAMC,KAAK,GAAGD,SAAS,CAACC,KAAK,IAAI,EAAE;IACnC,IAAIA,KAAK,CAACV,MAAM,KAAK,CAAC,EAAE,OAAOpD,MAAM,CAACC,WAAW;IAEjD,IAAI+D,cAAc,GAAG,CAAC;IACtB,IAAIC,eAAe,GAAG,CAAC;IAEvBH,KAAK,CAACR,OAAO,CAACJ,IAAI,IAAI;MACpB,MAAMH,MAAM,GAAGU,aAAa,CAACP,IAAI,CAAC;MAClC,IAAIH,MAAM,KAAK/C,MAAM,CAACG,SAAS,EAAE;QAC/B6D,cAAc,EAAE;MAClB,CAAC,MAAM,IAAIjB,MAAM,KAAK/C,MAAM,CAACE,WAAW,EAAE;QACxC+D,eAAe,EAAE;MACnB;IACF,CAAC,CAAC;IAEF,IAAID,cAAc,KAAKF,KAAK,CAACV,MAAM,EAAE;MACnC,OAAOpD,MAAM,CAACG,SAAS;IACzB,CAAC,MAAM,IAAI6D,cAAc,GAAG,CAAC,IAAIC,eAAe,GAAG,CAAC,EAAE;MACpD,OAAOjE,MAAM,CAACE,WAAW;IAC3B;IAEA,OAAOF,MAAM,CAACC,WAAW;EAC3B,CAAC;;EAED;EACA,MAAMiE,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,CAACvD,QAAQ,IAAI,CAACA,QAAQ,CAACwD,UAAU,IAAIxD,QAAQ,CAACwD,UAAU,CAACf,MAAM,KAAK,CAAC,EAAE;MACzE,OAAO,CAAC;IACV;IAEA,IAAIC,aAAa,GAAG,CAAC;IACrB1C,QAAQ,CAACwD,UAAU,CAACb,OAAO,CAACO,SAAS,IAAI;MACvCR,aAAa,IAAIO,0BAA0B,CAACC,SAAS,CAAC;IACxD,CAAC,CAAC;IAEF,OAAON,IAAI,CAACC,KAAK,CAACH,aAAa,GAAG1C,QAAQ,CAACwD,UAAU,CAACf,MAAM,CAAC;EAC/D,CAAC;;EAED;EACA,MAAMgB,aAAa,GAAGA,CAAA,KAAM;IAC1B,IAAI,CAACzD,QAAQ,IAAI,CAACA,QAAQ,CAACwD,UAAU,IAAIxD,QAAQ,CAACwD,UAAU,CAACf,MAAM,KAAK,CAAC,EAAE;MACzE,OAAOpD,MAAM,CAACC,WAAW;IAC3B;IAEA,IAAIoE,mBAAmB,GAAG,CAAC;IAC3B,IAAIC,oBAAoB,GAAG,CAAC;IAE5B3D,QAAQ,CAACwD,UAAU,CAACb,OAAO,CAACO,SAAS,IAAI;MACvC,MAAMd,MAAM,GAAGgB,kBAAkB,CAACF,SAAS,CAAC;MAC5C,IAAId,MAAM,KAAK/C,MAAM,CAACG,SAAS,EAAE;QAC/BkE,mBAAmB,EAAE;MACvB,CAAC,MAAM,IAAItB,MAAM,KAAK/C,MAAM,CAACE,WAAW,EAAE;QACxCoE,oBAAoB,EAAE;MACxB;IACF,CAAC,CAAC;IAEF,IAAID,mBAAmB,KAAK1D,QAAQ,CAACwD,UAAU,CAACf,MAAM,EAAE;MACtD,OAAOpD,MAAM,CAACG,SAAS;IACzB,CAAC,MAAM,IAAIkE,mBAAmB,GAAG,CAAC,IAAIC,oBAAoB,GAAG,CAAC,EAAE;MAC9D,OAAOtE,MAAM,CAACE,WAAW;IAC3B;IAEA,OAAOF,MAAM,CAACC,WAAW;EAC3B,CAAC;;EAED;EACA,MAAMsE,kBAAkB,GAAGA,CAAA,KAAM;IAAA,IAAAC,oBAAA,EAAAC,qBAAA;IAC/B,IAAI,CAAC9D,QAAQ,EAAE,OAAO;MACpBwD,UAAU,EAAE,CAAC;MACbL,KAAK,EAAE,CAAC;MACRX,QAAQ,EAAE,CAAC;MACXN,QAAQ,EAAE,CAAC;MACXmB,cAAc,EAAE,CAAC;MACjBC,eAAe,EAAE,CAAC;MAClBS,eAAe,EAAE,CAAC;MAClBC,QAAQ,EAAE,CAAC;MACX5B,MAAM,EAAE/C,MAAM,CAACC;IACjB,CAAC;IAED,MAAMkE,UAAU,GAAG,EAAAK,oBAAA,GAAA7D,QAAQ,CAACwD,UAAU,cAAAK,oBAAA,uBAAnBA,oBAAA,CAAqBpB,MAAM,KAAI,CAAC;IAEnD,IAAIU,KAAK,GAAG,CAAC;IACb,IAAIX,QAAQ,GAAG,CAAC;IAChB,IAAIa,cAAc,GAAG,CAAC;IACtB,IAAIC,eAAe,GAAG,CAAC;IACvB,IAAIS,eAAe,GAAG,CAAC;IACvB,IAAIC,QAAQ,GAAG,CAAC;IAEhB,CAAAF,qBAAA,GAAA9D,QAAQ,CAACwD,UAAU,cAAAM,qBAAA,uBAAnBA,qBAAA,CAAqBnB,OAAO,CAACO,SAAS,IAAI;MAAA,IAAAe,gBAAA,EAAAC,iBAAA;MACxC,MAAMC,cAAc,GAAG,EAAAF,gBAAA,GAAAf,SAAS,CAACC,KAAK,cAAAc,gBAAA,uBAAfA,gBAAA,CAAiBxB,MAAM,KAAI,CAAC;MACnDU,KAAK,IAAIgB,cAAc;MAEvB,CAAAD,iBAAA,GAAAhB,SAAS,CAACC,KAAK,cAAAe,iBAAA,uBAAfA,iBAAA,CAAiBvB,OAAO,CAACJ,IAAI,IAAI;QAAA,IAAA6B,cAAA,EAAAC,cAAA;QAC/B7B,QAAQ,IAAI,EAAA4B,cAAA,GAAA7B,IAAI,CAACC,QAAQ,cAAA4B,cAAA,uBAAbA,cAAA,CAAe3B,MAAM,KAAI,CAAC;QACtCuB,QAAQ,IAAI,EAAAK,cAAA,GAAA9B,IAAI,CAACyB,QAAQ,cAAAK,cAAA,uBAAbA,cAAA,CAAe5B,MAAM,KAAI,CAAC;;QAEtC;QACA,MAAM6B,UAAU,GAAGxB,aAAa,CAACP,IAAI,CAAC;QACtC,IAAI+B,UAAU,KAAKjF,MAAM,CAACG,SAAS,EAAE;UACnC6D,cAAc,EAAE;QAClB,CAAC,MAAM,IAAIiB,UAAU,KAAKjF,MAAM,CAACE,WAAW,EAAE;UAC5C+D,eAAe,EAAE;QACnB,CAAC,MAAM;UACLS,eAAe,EAAE;QACnB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;;IAEF;IACA,MAAM7B,QAAQ,GAAGqB,qBAAqB,CAAC,CAAC;IACxC,MAAMnB,MAAM,GAAGqB,aAAa,CAAC,CAAC;IAE9B,OAAO;MACLD,UAAU;MACVL,KAAK;MACLX,QAAQ;MACRN,QAAQ;MACRmB,cAAc;MACdC,eAAe;MACfS,eAAe;MACfC,QAAQ;MACR5B;IACF,CAAC;EACH,CAAC;EAED,OAAO;IACLpC,QAAQ;IACRE,OAAO;IACPE,KAAK;IACLE,YAAY;IACZE,KAAK;IACLE,SAAS;IACTQ,iBAAiB;IACjBK,iBAAiB;IACjBE,gBAAgB;IAChBI,gBAAgB;IAChB+B,kBAAkB;IAClB;IACA5B,wBAAwB;IACxBK,gBAAgB;IAChBC,qBAAqB;IACrBQ,aAAa;IACbG,0BAA0B;IAC1BG,kBAAkB;IAClBG,qBAAqB;IACrBE,aAAa;IACbpE,MAAM;IACNI;EACF,CAAC;AACH,CAAC;AAACM,EAAA,CAlZIF,WAAW;EAAA,QAOEjB,WAAW;AAAA;AA6Y9B,eAAeiB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}