{"ast": null, "code": "/**\n * Takes an argument and if it's an array, returns the first item in the array,\n * otherwise returns the argument. Used for Preact compatibility.\n */\nexport var unwrapArray = function unwrapArray(arg) {\n  return Array.isArray(arg) ? arg[0] : arg;\n};\n/**\n * Takes a maybe-undefined function and arbitrary args and invokes the function\n * only if it is defined.\n */\n\nexport var safeInvoke = function safeInvoke(fn) {\n  if (typeof fn === \"function\") {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n    return fn.apply(void 0, args);\n  }\n};\n/**\n * Does a shallow equality check of two objects by comparing the reference\n * equality of each value.\n */\n\nexport var shallowEqual = function shallowEqual(objA, objB) {\n  var aKeys = Object.keys(objA);\n  var bKeys = Object.keys(objB);\n  if (bKeys.length !== aKeys.length) {\n    return false;\n  }\n  for (var i = 0; i < bKeys.length; i++) {\n    var key = aKeys[i];\n    if (objA[key] !== objB[key]) {\n      return false;\n    }\n  }\n  return true;\n};\n/**\n * Sets a ref using either a ref callback or a ref object\n */\n\nexport var setRef = function setRef(ref, node) {\n  // if its a function call it\n  if (typeof ref === \"function\") {\n    return safeInvoke(ref, node);\n  } // otherwise we should treat it as a ref object\n  else if (ref != null) {\n    ref.current = node;\n  }\n};", "map": {"version": 3, "names": ["unwrapArray", "arg", "Array", "isArray", "safeInvoke", "fn", "_len", "arguments", "length", "args", "_key", "apply", "shallowEqual", "objA", "objB", "a<PERSON><PERSON><PERSON>", "Object", "keys", "b<PERSON><PERSON><PERSON>", "i", "key", "setRef", "ref", "node", "current"], "sources": ["C:/ignition/ignition-ui/node_modules/react-popper/lib/esm/utils.js"], "sourcesContent": ["/**\n * Takes an argument and if it's an array, returns the first item in the array,\n * otherwise returns the argument. Used for Preact compatibility.\n */\nexport var unwrapArray = function unwrapArray(arg) {\n  return Array.isArray(arg) ? arg[0] : arg;\n};\n/**\n * Takes a maybe-undefined function and arbitrary args and invokes the function\n * only if it is defined.\n */\n\nexport var safeInvoke = function safeInvoke(fn) {\n  if (typeof fn === \"function\") {\n    for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n      args[_key - 1] = arguments[_key];\n    }\n\n    return fn.apply(void 0, args);\n  }\n};\n/**\n * Does a shallow equality check of two objects by comparing the reference\n * equality of each value.\n */\n\nexport var shallowEqual = function shallowEqual(objA, objB) {\n  var aKeys = Object.keys(objA);\n  var bKeys = Object.keys(objB);\n\n  if (bKeys.length !== aKeys.length) {\n    return false;\n  }\n\n  for (var i = 0; i < bKeys.length; i++) {\n    var key = aKeys[i];\n\n    if (objA[key] !== objB[key]) {\n      return false;\n    }\n  }\n\n  return true;\n};\n/**\n * Sets a ref using either a ref callback or a ref object\n */\n\nexport var setRef = function setRef(ref, node) {\n  // if its a function call it\n  if (typeof ref === \"function\") {\n    return safeInvoke(ref, node);\n  } // otherwise we should treat it as a ref object\n  else if (ref != null) {\n      ref.current = node;\n    }\n};"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,IAAIA,WAAW,GAAG,SAASA,WAAWA,CAACC,GAAG,EAAE;EACjD,OAAOC,KAAK,CAACC,OAAO,CAACF,GAAG,CAAC,GAAGA,GAAG,CAAC,CAAC,CAAC,GAAGA,GAAG;AAC1C,CAAC;AACD;AACA;AACA;AACA;;AAEA,OAAO,IAAIG,UAAU,GAAG,SAASA,UAAUA,CAACC,EAAE,EAAE;EAC9C,IAAI,OAAOA,EAAE,KAAK,UAAU,EAAE;IAC5B,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIP,KAAK,CAACI,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEI,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGJ,IAAI,EAAEI,IAAI,EAAE,EAAE;MAC1GD,IAAI,CAACC,IAAI,GAAG,CAAC,CAAC,GAAGH,SAAS,CAACG,IAAI,CAAC;IAClC;IAEA,OAAOL,EAAE,CAACM,KAAK,CAAC,KAAK,CAAC,EAAEF,IAAI,CAAC;EAC/B;AACF,CAAC;AACD;AACA;AACA;AACA;;AAEA,OAAO,IAAIG,YAAY,GAAG,SAASA,YAAYA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAC1D,IAAIC,KAAK,GAAGC,MAAM,CAACC,IAAI,CAACJ,IAAI,CAAC;EAC7B,IAAIK,KAAK,GAAGF,MAAM,CAACC,IAAI,CAACH,IAAI,CAAC;EAE7B,IAAII,KAAK,CAACV,MAAM,KAAKO,KAAK,CAACP,MAAM,EAAE;IACjC,OAAO,KAAK;EACd;EAEA,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACV,MAAM,EAAEW,CAAC,EAAE,EAAE;IACrC,IAAIC,GAAG,GAAGL,KAAK,CAACI,CAAC,CAAC;IAElB,IAAIN,IAAI,CAACO,GAAG,CAAC,KAAKN,IAAI,CAACM,GAAG,CAAC,EAAE;MAC3B,OAAO,KAAK;IACd;EACF;EAEA,OAAO,IAAI;AACb,CAAC;AACD;AACA;AACA;;AAEA,OAAO,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,GAAG,EAAEC,IAAI,EAAE;EAC7C;EACA,IAAI,OAAOD,GAAG,KAAK,UAAU,EAAE;IAC7B,OAAOlB,UAAU,CAACkB,GAAG,EAAEC,IAAI,CAAC;EAC9B,CAAC,CAAC;EAAA,KACG,IAAID,GAAG,IAAI,IAAI,EAAE;IAClBA,GAAG,CAACE,OAAO,GAAGD,IAAI;EACpB;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}