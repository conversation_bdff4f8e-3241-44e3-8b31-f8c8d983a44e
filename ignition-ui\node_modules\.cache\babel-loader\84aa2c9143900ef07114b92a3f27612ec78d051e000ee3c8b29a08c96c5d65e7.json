{"ast": null, "code": "'use client';\n\nexport { useTabPanel } from './useTabPanel';\nexport * from './useTabPanel.types';", "map": {"version": 3, "names": ["useTabPanel"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/useTabPanel/index.js"], "sourcesContent": ["'use client';\n\nexport { useTabPanel } from './useTabPanel';\nexport * from './useTabPanel.types';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,WAAW,QAAQ,eAAe;AAC3C,cAAc,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}