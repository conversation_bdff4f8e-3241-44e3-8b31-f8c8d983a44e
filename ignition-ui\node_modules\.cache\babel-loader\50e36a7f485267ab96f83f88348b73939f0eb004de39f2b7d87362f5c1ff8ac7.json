{"ast": null, "code": "function is(x, y) {\n  if (x === y) {\n    return x !== 0 || y !== 0 || 1 / x === 1 / y;\n  } else {\n    return x !== x && y !== y;\n  }\n}\nexport default function shallowEqual(objA, objB) {\n  if (is(objA, objB)) return true;\n  if (typeof objA !== 'object' || objA === null || typeof objB !== 'object' || objB === null) {\n    return false;\n  }\n  var keysA = Object.keys(objA);\n  var keysB = Object.keys(objB);\n  if (keysA.length !== keysB.length) return false;\n  for (var i = 0; i < keysA.length; i++) {\n    if (!Object.prototype.hasOwnProperty.call(objB, keysA[i]) || !is(objA[keysA[i]], objB[keysA[i]])) {\n      return false;\n    }\n  }\n  return true;\n}", "map": {"version": 3, "names": ["is", "x", "y", "shallowEqual", "objA", "objB", "keysA", "Object", "keys", "keysB", "length", "i", "prototype", "hasOwnProperty", "call"], "sources": ["C:/ignition/ignition-ui/node_modules/react-beautiful-dnd/node_modules/react-redux/es/utils/shallowEqual.js"], "sourcesContent": ["function is(x, y) {\n  if (x === y) {\n    return x !== 0 || y !== 0 || 1 / x === 1 / y;\n  } else {\n    return x !== x && y !== y;\n  }\n}\n\nexport default function shallowEqual(objA, objB) {\n  if (is(objA, objB)) return true;\n\n  if (typeof objA !== 'object' || objA === null || typeof objB !== 'object' || objB === null) {\n    return false;\n  }\n\n  var keysA = Object.keys(objA);\n  var keysB = Object.keys(objB);\n  if (keysA.length !== keysB.length) return false;\n\n  for (var i = 0; i < keysA.length; i++) {\n    if (!Object.prototype.hasOwnProperty.call(objB, keysA[i]) || !is(objA[keysA[i]], objB[keysA[i]])) {\n      return false;\n    }\n  }\n\n  return true;\n}"], "mappings": "AAAA,SAASA,EAAEA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAChB,IAAID,CAAC,KAAKC,CAAC,EAAE;IACX,OAAOD,CAAC,KAAK,CAAC,IAAIC,CAAC,KAAK,CAAC,IAAI,CAAC,GAAGD,CAAC,KAAK,CAAC,GAAGC,CAAC;EAC9C,CAAC,MAAM;IACL,OAAOD,CAAC,KAAKA,CAAC,IAAIC,CAAC,KAAKA,CAAC;EAC3B;AACF;AAEA,eAAe,SAASC,YAAYA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAC/C,IAAIL,EAAE,CAACI,IAAI,EAAEC,IAAI,CAAC,EAAE,OAAO,IAAI;EAE/B,IAAI,OAAOD,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,IAAI,OAAOC,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,EAAE;IAC1F,OAAO,KAAK;EACd;EAEA,IAAIC,KAAK,GAAGC,MAAM,CAACC,IAAI,CAACJ,IAAI,CAAC;EAC7B,IAAIK,KAAK,GAAGF,MAAM,CAACC,IAAI,CAACH,IAAI,CAAC;EAC7B,IAAIC,KAAK,CAACI,MAAM,KAAKD,KAAK,CAACC,MAAM,EAAE,OAAO,KAAK;EAE/C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,KAAK,CAACI,MAAM,EAAEC,CAAC,EAAE,EAAE;IACrC,IAAI,CAACJ,MAAM,CAACK,SAAS,CAACC,cAAc,CAACC,IAAI,CAACT,IAAI,EAAEC,KAAK,CAACK,CAAC,CAAC,CAAC,IAAI,CAACX,EAAE,CAACI,IAAI,CAACE,KAAK,CAACK,CAAC,CAAC,CAAC,EAAEN,IAAI,CAACC,KAAK,CAACK,CAAC,CAAC,CAAC,CAAC,EAAE;MAChG,OAAO,KAAK;IACd;EACF;EAEA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}