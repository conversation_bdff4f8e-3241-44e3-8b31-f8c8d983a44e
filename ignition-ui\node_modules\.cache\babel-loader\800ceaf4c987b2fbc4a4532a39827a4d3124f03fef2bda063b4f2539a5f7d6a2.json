{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"onClick\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport { useThemeProps, styled } from '../styles';\nimport useSlot from '../utils/useSlot';\nimport { getModalOverflowUtilityClass } from './modalOverflowClasses';\nimport CloseModalContext from '../Modal/CloseModalContext';\nimport modalDialogClasses from '../ModalDialog/modalDialogClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = () => {\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getModalOverflowUtilityClass, {});\n};\nexport const ModalOverflowRoot = styled('div', {\n  name: 'JoyModalOverflow',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({\n  '--ModalOverflow-paddingY': '1.5rem',\n  position: 'absolute',\n  top: 0,\n  right: 0,\n  bottom: 0,\n  left: 0,\n  height: '100%',\n  overflow: 'hidden auto',\n  outline: 'none',\n  display: 'flex',\n  flexDirection: 'column',\n  // required for fullscreen ModalDialog, using `row` cannot be achieved.\n  padding: 'var(--ModalOverflow-paddingY) 0',\n  // let's not create `size` prop to only control the `padding`.\n  [`& .${modalDialogClasses.layoutCenter}`]: {\n    position: 'relative',\n    margin: 'auto',\n    // to make the dialog stay at center when content does not overflow the screen.\n    height: 'max-content',\n    // height is based on content, otherwise `margin: auto` will take place.\n    maxHeight: 'unset',\n    transform: 'none',\n    top: 'unset',\n    left: 'unset'\n  },\n  [`& .${modalDialogClasses.layoutFullscreen}`]: {\n    position: 'relative',\n    width: '100%',\n    margin: 'calc(-1 * var(--ModalOverflow-paddingY)) 0',\n    flex: 1\n  }\n});\n/**\n *\n * Demos:\n *\n * - [Modal](https://mui.com/joy-ui/react-modal/)\n *\n * API:\n *\n * - [ModalOverflow API](https://mui.com/joy-ui/api/modal-overflow/)\n */\nconst ModalOverflow = /*#__PURE__*/React.forwardRef(function ModalOverflow(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyModalOverflow'\n  });\n  const {\n      children,\n      onClick\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const onClose = React.useContext(CloseModalContext);\n  const ownerState = props;\n  const classes = useUtilityClasses();\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    className: classes.root,\n    elementType: ModalOverflowRoot,\n    externalForwardedProps: other,\n    ownerState,\n    additionalProps: {\n      role: 'presentation',\n      tabIndex: -1,\n      onClick: event => {\n        if (event.target === event.currentTarget) {\n          onClose == null || onClose(event, 'backdropClick');\n        }\n        onClick == null || onClick(event);\n      }\n    }\n  });\n  return /*#__PURE__*/_jsx(SlotRoot, _extends({}, rootProps, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? ModalOverflow.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  onClick: PropTypes.func,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default ModalOverflow;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "unstable_composeClasses", "composeClasses", "useThemeProps", "styled", "useSlot", "getModalOverflowUtilityClass", "CloseModalContext", "modalDialogClasses", "jsx", "_jsx", "useUtilityClasses", "slots", "root", "ModalOverflowRoot", "name", "slot", "overridesResolver", "props", "styles", "position", "top", "right", "bottom", "left", "height", "overflow", "outline", "display", "flexDirection", "padding", "layoutCenter", "margin", "maxHeight", "transform", "layoutFullscreen", "width", "flex", "ModalOverflow", "forwardRef", "inProps", "ref", "children", "onClick", "other", "onClose", "useContext", "ownerState", "classes", "SlotRoot", "rootProps", "className", "elementType", "externalForwardedProps", "additionalProps", "role", "tabIndex", "event", "target", "currentTarget", "process", "env", "NODE_ENV", "propTypes", "node", "func", "sx", "oneOfType", "arrayOf", "object", "bool"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/ModalOverflow/ModalOverflow.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"onClick\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport { useThemeProps, styled } from '../styles';\nimport useSlot from '../utils/useSlot';\nimport { getModalOverflowUtilityClass } from './modalOverflowClasses';\nimport CloseModalContext from '../Modal/CloseModalContext';\nimport modalDialogClasses from '../ModalDialog/modalDialogClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = () => {\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getModalOverflowUtilityClass, {});\n};\nexport const ModalOverflowRoot = styled('div', {\n  name: 'JoyModalOverflow',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({\n  '--ModalOverflow-paddingY': '1.5rem',\n  position: 'absolute',\n  top: 0,\n  right: 0,\n  bottom: 0,\n  left: 0,\n  height: '100%',\n  overflow: 'hidden auto',\n  outline: 'none',\n  display: 'flex',\n  flexDirection: 'column',\n  // required for fullscreen ModalDialog, using `row` cannot be achieved.\n  padding: 'var(--ModalOverflow-paddingY) 0',\n  // let's not create `size` prop to only control the `padding`.\n  [`& .${modalDialogClasses.layoutCenter}`]: {\n    position: 'relative',\n    margin: 'auto',\n    // to make the dialog stay at center when content does not overflow the screen.\n    height: 'max-content',\n    // height is based on content, otherwise `margin: auto` will take place.\n    maxHeight: 'unset',\n    transform: 'none',\n    top: 'unset',\n    left: 'unset'\n  },\n  [`& .${modalDialogClasses.layoutFullscreen}`]: {\n    position: 'relative',\n    width: '100%',\n    margin: 'calc(-1 * var(--ModalOverflow-paddingY)) 0',\n    flex: 1\n  }\n});\n/**\n *\n * Demos:\n *\n * - [Modal](https://mui.com/joy-ui/react-modal/)\n *\n * API:\n *\n * - [ModalOverflow API](https://mui.com/joy-ui/api/modal-overflow/)\n */\nconst ModalOverflow = /*#__PURE__*/React.forwardRef(function ModalOverflow(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyModalOverflow'\n  });\n  const {\n      children,\n      onClick\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const onClose = React.useContext(CloseModalContext);\n  const ownerState = props;\n  const classes = useUtilityClasses();\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    className: classes.root,\n    elementType: ModalOverflowRoot,\n    externalForwardedProps: other,\n    ownerState,\n    additionalProps: {\n      role: 'presentation',\n      tabIndex: -1,\n      onClick: event => {\n        if (event.target === event.currentTarget) {\n          onClose == null || onClose(event, 'backdropClick');\n        }\n        onClick == null || onClick(event);\n      }\n    }\n  });\n  return /*#__PURE__*/_jsx(SlotRoot, _extends({}, rootProps, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? ModalOverflow.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  onClick: PropTypes.func,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default ModalOverflow;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,SAAS,CAAC;AACzC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,WAAW;AACrE,SAASC,aAAa,EAAEC,MAAM,QAAQ,WAAW;AACjD,OAAOC,OAAO,MAAM,kBAAkB;AACtC,SAASC,4BAA4B,QAAQ,wBAAwB;AACrE,OAAOC,iBAAiB,MAAM,4BAA4B;AAC1D,OAAOC,kBAAkB,MAAM,mCAAmC;AAClE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAC9B,MAAMC,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,OAAOX,cAAc,CAACU,KAAK,EAAEN,4BAA4B,EAAE,CAAC,CAAC,CAAC;AAChE,CAAC;AACD,OAAO,MAAMQ,iBAAiB,GAAGV,MAAM,CAAC,KAAK,EAAE;EAC7CW,IAAI,EAAE,kBAAkB;EACxBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC/C,CAAC,CAAC,CAAC;EACD,0BAA0B,EAAE,QAAQ;EACpCO,QAAQ,EAAE,UAAU;EACpBC,GAAG,EAAE,CAAC;EACNC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,IAAI,EAAE,CAAC;EACPC,MAAM,EAAE,MAAM;EACdC,QAAQ,EAAE,aAAa;EACvBC,OAAO,EAAE,MAAM;EACfC,OAAO,EAAE,MAAM;EACfC,aAAa,EAAE,QAAQ;EACvB;EACAC,OAAO,EAAE,iCAAiC;EAC1C;EACA,CAAC,MAAMtB,kBAAkB,CAACuB,YAAY,EAAE,GAAG;IACzCX,QAAQ,EAAE,UAAU;IACpBY,MAAM,EAAE,MAAM;IACd;IACAP,MAAM,EAAE,aAAa;IACrB;IACAQ,SAAS,EAAE,OAAO;IAClBC,SAAS,EAAE,MAAM;IACjBb,GAAG,EAAE,OAAO;IACZG,IAAI,EAAE;EACR,CAAC;EACD,CAAC,MAAMhB,kBAAkB,CAAC2B,gBAAgB,EAAE,GAAG;IAC7Cf,QAAQ,EAAE,UAAU;IACpBgB,KAAK,EAAE,MAAM;IACbJ,MAAM,EAAE,4CAA4C;IACpDK,IAAI,EAAE;EACR;AACF,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAG,aAAavC,KAAK,CAACwC,UAAU,CAAC,SAASD,aAAaA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACvF,MAAMvB,KAAK,GAAGf,aAAa,CAAC;IAC1Be,KAAK,EAAEsB,OAAO;IACdzB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACF2B,QAAQ;MACRC;IACF,CAAC,GAAGzB,KAAK;IACT0B,KAAK,GAAG/C,6BAA6B,CAACqB,KAAK,EAAEpB,SAAS,CAAC;EACzD,MAAM+C,OAAO,GAAG9C,KAAK,CAAC+C,UAAU,CAACvC,iBAAiB,CAAC;EACnD,MAAMwC,UAAU,GAAG7B,KAAK;EACxB,MAAM8B,OAAO,GAAGrC,iBAAiB,CAAC,CAAC;EACnC,MAAM,CAACsC,QAAQ,EAAEC,SAAS,CAAC,GAAG7C,OAAO,CAAC,MAAM,EAAE;IAC5CoC,GAAG;IACHU,SAAS,EAAEH,OAAO,CAACnC,IAAI;IACvBuC,WAAW,EAAEtC,iBAAiB;IAC9BuC,sBAAsB,EAAET,KAAK;IAC7BG,UAAU;IACVO,eAAe,EAAE;MACfC,IAAI,EAAE,cAAc;MACpBC,QAAQ,EAAE,CAAC,CAAC;MACZb,OAAO,EAAEc,KAAK,IAAI;QAChB,IAAIA,KAAK,CAACC,MAAM,KAAKD,KAAK,CAACE,aAAa,EAAE;UACxCd,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACY,KAAK,EAAE,eAAe,CAAC;QACpD;QACAd,OAAO,IAAI,IAAI,IAAIA,OAAO,CAACc,KAAK,CAAC;MACnC;IACF;EACF,CAAC,CAAC;EACF,OAAO,aAAa/C,IAAI,CAACuC,QAAQ,EAAErD,QAAQ,CAAC,CAAC,CAAC,EAAEsD,SAAS,EAAE;IACzDR,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFkB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGxB,aAAa,CAACyB,SAAS,CAAC,yBAAyB;EACvF;EACA;EACA;EACA;EACA;AACF;AACA;EACErB,QAAQ,EAAE1C,SAAS,CAACgE,IAAI;EACxB;AACF;AACA;EACErB,OAAO,EAAE3C,SAAS,CAACiE,IAAI;EACvB;AACF;AACA;EACEC,EAAE,EAAElE,SAAS,CAACmE,SAAS,CAAC,CAACnE,SAAS,CAACoE,OAAO,CAACpE,SAAS,CAACmE,SAAS,CAAC,CAACnE,SAAS,CAACiE,IAAI,EAAEjE,SAAS,CAACqE,MAAM,EAAErE,SAAS,CAACsE,IAAI,CAAC,CAAC,CAAC,EAAEtE,SAAS,CAACiE,IAAI,EAAEjE,SAAS,CAACqE,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAe/B,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}