{"ast": null, "code": "import * as React from 'react';\nconst CloseModalContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== 'production') {\n  CloseModalContext.displayName = 'CloseModalContext';\n}\nexport default CloseModalContext;", "map": {"version": 3, "names": ["React", "CloseModalContext", "createContext", "undefined", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Modal/CloseModalContext.js"], "sourcesContent": ["import * as React from 'react';\nconst CloseModalContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== 'production') {\n  CloseModalContext.displayName = 'CloseModalContext';\n}\nexport default CloseModalContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,iBAAiB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAACC,SAAS,CAAC;AACrE,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCL,iBAAiB,CAACM,WAAW,GAAG,mBAAmB;AACrD;AACA,eAAeN,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}