{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getLinkUtilityClass(slot) {\n  return generateUtilityClass('MuiLink', slot);\n}\nconst linkClasses = generateUtilityClasses('MuiLink', ['root', 'disabled', 'focusVisible', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'focusVisible', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid', 'underlineNone', 'underlineHover', 'underlineAlways', 'h1', 'h2', 'h3', 'h4', 'title-lg', 'title-md', 'title-sm', 'body-lg', 'body-md', 'body-sm', 'body-xs', 'startDecorator', 'endDecorator']);\nexport default linkClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getLinkUtilityClass", "slot", "linkClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Link/linkClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getLinkUtilityClass(slot) {\n  return generateUtilityClass('MuiLink', slot);\n}\nconst linkClasses = generateUtilityClasses('MuiLink', ['root', 'disabled', 'focusVisible', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'focusVisible', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid', 'underlineNone', 'underlineHover', 'underlineAlways', 'h1', 'h2', 'h3', 'h4', 'title-lg', 'title-md', 'title-sm', 'body-lg', 'body-md', 'body-sm', 'body-xs', 'startDecorator', 'endDecorator']);\nexport default linkClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,mBAAmBA,CAACC,IAAI,EAAE;EACxC,OAAOH,oBAAoB,CAAC,SAAS,EAAEG,IAAI,CAAC;AAC9C;AACA,MAAMC,WAAW,GAAGH,sBAAsB,CAAC,SAAS,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,iBAAiB,EAAE,aAAa,EAAE,cAAc,EAAE,eAAe,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,gBAAgB,EAAE,cAAc,CAAC,CAAC;AAC5c,eAAeG,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}