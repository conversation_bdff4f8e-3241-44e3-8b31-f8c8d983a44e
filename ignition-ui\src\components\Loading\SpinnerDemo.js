import React, { useState } from 'react';
import { Box, Typography, Button, ButtonGroup, Paper, Grid } from '@mui/material';
import ModernSpinner from './ModernSpinner';

/**
 * Demo component to showcase all spinner variants
 */
const SpinnerDemo = () => {
  const [selectedVariant, setSelectedVariant] = useState('orbital');
  const [selectedSize, setSelectedSize] = useState('medium');
  const [selectedStatus, setSelectedStatus] = useState('creating');

  const variants = ['orbital', 'galaxy', 'pulse', 'ripple', 'dots'];
  const sizes = ['small', 'medium', 'large'];
  const statuses = ['creating', 'processing', 'finalizing'];

  return (
    <Box sx={{ p: 4, maxWidth: 1200, mx: 'auto' }}>
      <Typography variant="h3" align="center" sx={{ mb: 4, fontFamily: 'Recursive Variable' }}>
        Modern Spinner Showcase
      </Typography>

      {/* Controls */}
      <Paper elevation={3} sx={{ p: 3, mb: 4 }}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={4}>
            <Typography variant="h6" sx={{ mb: 2 }}>Variant</Typography>
            <ButtonGroup variant="outlined" size="small">
              {variants.map((variant) => (
                <Button
                  key={variant}
                  onClick={() => setSelectedVariant(variant)}
                  variant={selectedVariant === variant ? 'contained' : 'outlined'}
                  sx={{ textTransform: 'capitalize' }}
                >
                  {variant}
                </Button>
              ))}
            </ButtonGroup>
          </Grid>

          <Grid item xs={12} md={4}>
            <Typography variant="h6" sx={{ mb: 2 }}>Size</Typography>
            <ButtonGroup variant="outlined" size="small">
              {sizes.map((size) => (
                <Button
                  key={size}
                  onClick={() => setSelectedSize(size)}
                  variant={selectedSize === size ? 'contained' : 'outlined'}
                  sx={{ textTransform: 'capitalize' }}
                >
                  {size}
                </Button>
              ))}
            </ButtonGroup>
          </Grid>

          <Grid item xs={12} md={4}>
            <Typography variant="h6" sx={{ mb: 2 }}>Status</Typography>
            <ButtonGroup variant="outlined" size="small">
              {statuses.map((status) => (
                <Button
                  key={status}
                  onClick={() => setSelectedStatus(status)}
                  variant={selectedStatus === status ? 'contained' : 'outlined'}
                  sx={{ textTransform: 'capitalize' }}
                >
                  {status}
                </Button>
              ))}
            </ButtonGroup>
          </Grid>
        </Grid>
      </Paper>

      {/* Main Demo */}
      <Paper elevation={3} sx={{ p: 4, mb: 4, minHeight: 400, display: 'flex', alignItems: 'center', justifyContent: 'center', bgcolor: '#f5f5f5' }}>
        <Box sx={{ textAlign: 'center' }}>
          <ModernSpinner 
            variant={selectedVariant}
            size={selectedSize}
            status={selectedStatus}
            fromCreatePlan={false}
          />
          <Typography variant="h6" sx={{ mt: 3, fontFamily: 'Recursive Variable' }}>
            {selectedVariant.charAt(0).toUpperCase() + selectedVariant.slice(1)} Spinner
          </Typography>
          <Typography variant="body2" sx={{ color: '#666' }}>
            Size: {selectedSize} | Status: {selectedStatus}
          </Typography>
        </Box>
      </Paper>

      {/* All Variants Grid */}
      <Typography variant="h4" align="center" sx={{ mb: 3, fontFamily: 'Recursive Variable' }}>
        All Variants
      </Typography>
      <Grid container spacing={3}>
        {variants.map((variant) => (
          <Grid item xs={12} sm={6} md={4} lg={2.4} key={variant}>
            <Paper elevation={2} sx={{ p: 3, textAlign: 'center', minHeight: 250, display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
              <Box sx={{ mb: 2 }}>
                <ModernSpinner 
                  variant={variant}
                  size="medium"
                  status="creating"
                  fromCreatePlan={false}
                />
              </Box>
              <Typography variant="h6" sx={{ textTransform: 'capitalize', fontFamily: 'Recursive Variable' }}>
                {variant}
              </Typography>
            </Paper>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default SpinnerDemo;
