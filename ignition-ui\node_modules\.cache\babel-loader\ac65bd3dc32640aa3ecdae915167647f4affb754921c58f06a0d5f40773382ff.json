{"ast": null, "code": "export const TabsListActionTypes = {\n  valueChange: 'valueChange'\n};", "map": {"version": 3, "names": ["TabsListActionTypes", "valueChange"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/useTabsList/useTabsList.types.js"], "sourcesContent": ["export const TabsListActionTypes = {\n  valueChange: 'valueChange'\n};"], "mappings": "AAAA,OAAO,MAAMA,mBAAmB,GAAG;EACjCC,WAAW,EAAE;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}