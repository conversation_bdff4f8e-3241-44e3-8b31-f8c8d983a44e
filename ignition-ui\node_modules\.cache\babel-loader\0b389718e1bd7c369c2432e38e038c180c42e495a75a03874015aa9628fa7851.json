{"ast": null, "code": "import * as React from 'react';\nconst NestedListContext = /*#__PURE__*/React.createContext(false);\nif (process.env.NODE_ENV !== 'production') {\n  NestedListContext.displayName = 'NestedListContext';\n}\nexport default NestedListContext;", "map": {"version": 3, "names": ["React", "NestedListContext", "createContext", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/List/NestedListContext.js"], "sourcesContent": ["import * as React from 'react';\nconst NestedListContext = /*#__PURE__*/React.createContext(false);\nif (process.env.NODE_ENV !== 'production') {\n  NestedListContext.displayName = 'NestedListContext';\n}\nexport default NestedListContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,iBAAiB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,KAAK,CAAC;AACjE,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,iBAAiB,CAACK,WAAW,GAAG,mBAAmB;AACrD;AACA,eAAeL,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}