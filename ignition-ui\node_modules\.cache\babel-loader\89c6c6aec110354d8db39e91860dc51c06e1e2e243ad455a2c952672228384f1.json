{"ast": null, "code": "'use client';\n\nexport { TextareaAutosize } from './TextareaAutosize';\nexport * from './TextareaAutosize.types';", "map": {"version": 3, "names": ["TextareaAutosize"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/TextareaAutosize/index.js"], "sourcesContent": ["'use client';\n\nexport { TextareaAutosize } from './TextareaAutosize';\nexport * from './TextareaAutosize.types';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,gBAAgB,QAAQ,oBAAoB;AACrD,cAAc,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}