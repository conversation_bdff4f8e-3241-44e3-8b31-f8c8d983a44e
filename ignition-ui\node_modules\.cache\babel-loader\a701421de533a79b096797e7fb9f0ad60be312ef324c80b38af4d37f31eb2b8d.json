{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getModalCloseUtilityClass(slot) {\n  return generateUtilityClass('MuiModalClose', slot);\n}\nconst modalCloseClasses = generateUtilityClasses('MuiModalClose', ['root', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid', 'sizeSm', 'sizeMd', 'sizeLg']);\nexport default modalCloseClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getModalCloseUtilityClass", "slot", "modalCloseClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/ModalClose/modalCloseClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getModalCloseUtilityClass(slot) {\n  return generateUtilityClass('MuiModalClose', slot);\n}\nconst modalCloseClasses = generateUtilityClasses('MuiModalClose', ['root', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid', 'sizeSm', 'sizeMd', 'sizeLg']);\nexport default modalCloseClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,yBAAyBA,CAACC,IAAI,EAAE;EAC9C,OAAOH,oBAAoB,CAAC,eAAe,EAAEG,IAAI,CAAC;AACpD;AACA,MAAMC,iBAAiB,GAAGH,sBAAsB,CAAC,eAAe,EAAE,CAAC,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,iBAAiB,EAAE,aAAa,EAAE,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;AAC1Q,eAAeG,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}