{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { useControllableReducer } from '../utils/useControllableReducer';\nimport { DropdownActionTypes } from './useDropdown.types';\nimport { dropdownReducer } from './dropdownReducer';\n\n/**\n *\n * Demos:\n *\n * - [Menu](https://mui.com/base-ui/react-menu/#hooks)\n *\n * API:\n *\n * - [useDropdown API](https://mui.com/base-ui/react-menu/hooks-api/#use-dropdown)\n */\nexport function useDropdown() {\n  let parameters = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  const {\n    defaultOpen,\n    onOpenChange,\n    open: openProp,\n    componentName = 'useDropdown'\n  } = parameters;\n  const [popupId, setPopupId] = React.useState('');\n  const [triggerElement, setTriggerElement] = React.useState(null);\n  const lastActionType = React.useRef(null);\n  const handleStateChange = React.useCallback((event, field, value, reason) => {\n    if (field === 'open') {\n      onOpenChange == null || onOpenChange(event, value);\n    }\n    lastActionType.current = reason;\n  }, [onOpenChange]);\n  const controlledProps = React.useMemo(() => openProp !== undefined ? {\n    open: openProp\n  } : {}, [openProp]);\n  const [state, dispatch] = useControllableReducer({\n    controlledProps,\n    initialState: defaultOpen ? {\n      open: true,\n      changeReason: null\n    } : {\n      open: false,\n      changeReason: null\n    },\n    onStateChange: handleStateChange,\n    reducer: dropdownReducer,\n    componentName\n  });\n  React.useEffect(() => {\n    if (!state.open && lastActionType.current !== null && lastActionType.current !== DropdownActionTypes.blur) {\n      triggerElement == null || triggerElement.focus();\n    }\n  }, [state.open, triggerElement]);\n  const contextValue = {\n    state,\n    dispatch,\n    popupId,\n    registerPopup: setPopupId,\n    registerTrigger: setTriggerElement,\n    triggerElement\n  };\n  return {\n    contextValue,\n    open: state.open\n  };\n}", "map": {"version": 3, "names": ["React", "useControllableReducer", "DropdownActionTypes", "dropdownReducer", "useDropdown", "parameters", "arguments", "length", "undefined", "defaultOpen", "onOpenChange", "open", "openProp", "componentName", "popupId", "setPopupId", "useState", "triggerElement", "setTriggerElement", "lastActionType", "useRef", "handleStateChange", "useCallback", "event", "field", "value", "reason", "current", "controlledProps", "useMemo", "state", "dispatch", "initialState", "changeReason", "onStateChange", "reducer", "useEffect", "blur", "focus", "contextValue", "registerPopup", "registerTrigger"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/useDropdown/useDropdown.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { useControllableReducer } from '../utils/useControllableReducer';\nimport { DropdownActionTypes } from './useDropdown.types';\nimport { dropdownReducer } from './dropdownReducer';\n\n/**\n *\n * Demos:\n *\n * - [Menu](https://mui.com/base-ui/react-menu/#hooks)\n *\n * API:\n *\n * - [useDropdown API](https://mui.com/base-ui/react-menu/hooks-api/#use-dropdown)\n */\nexport function useDropdown(parameters = {}) {\n  const {\n    defaultOpen,\n    onOpenChange,\n    open: openProp,\n    componentName = 'useDropdown'\n  } = parameters;\n  const [popupId, setPopupId] = React.useState('');\n  const [triggerElement, setTriggerElement] = React.useState(null);\n  const lastActionType = React.useRef(null);\n  const handleStateChange = React.useCallback((event, field, value, reason) => {\n    if (field === 'open') {\n      onOpenChange == null || onOpenChange(event, value);\n    }\n    lastActionType.current = reason;\n  }, [onOpenChange]);\n  const controlledProps = React.useMemo(() => openProp !== undefined ? {\n    open: openProp\n  } : {}, [openProp]);\n  const [state, dispatch] = useControllableReducer({\n    controlledProps,\n    initialState: defaultOpen ? {\n      open: true,\n      changeReason: null\n    } : {\n      open: false,\n      changeReason: null\n    },\n    onStateChange: handleStateChange,\n    reducer: dropdownReducer,\n    componentName\n  });\n  React.useEffect(() => {\n    if (!state.open && lastActionType.current !== null && lastActionType.current !== DropdownActionTypes.blur) {\n      triggerElement == null || triggerElement.focus();\n    }\n  }, [state.open, triggerElement]);\n  const contextValue = {\n    state,\n    dispatch,\n    popupId,\n    registerPopup: setPopupId,\n    registerTrigger: setTriggerElement,\n    triggerElement\n  };\n  return {\n    contextValue,\n    open: state.open\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,sBAAsB,QAAQ,iCAAiC;AACxE,SAASC,mBAAmB,QAAQ,qBAAqB;AACzD,SAASC,eAAe,QAAQ,mBAAmB;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,WAAWA,CAAA,EAAkB;EAAA,IAAjBC,UAAU,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACzC,MAAM;IACJG,WAAW;IACXC,YAAY;IACZC,IAAI,EAAEC,QAAQ;IACdC,aAAa,GAAG;EAClB,CAAC,GAAGR,UAAU;EACd,MAAM,CAACS,OAAO,EAAEC,UAAU,CAAC,GAAGf,KAAK,CAACgB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGlB,KAAK,CAACgB,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAMG,cAAc,GAAGnB,KAAK,CAACoB,MAAM,CAAC,IAAI,CAAC;EACzC,MAAMC,iBAAiB,GAAGrB,KAAK,CAACsB,WAAW,CAAC,CAACC,KAAK,EAAEC,KAAK,EAAEC,KAAK,EAAEC,MAAM,KAAK;IAC3E,IAAIF,KAAK,KAAK,MAAM,EAAE;MACpBd,YAAY,IAAI,IAAI,IAAIA,YAAY,CAACa,KAAK,EAAEE,KAAK,CAAC;IACpD;IACAN,cAAc,CAACQ,OAAO,GAAGD,MAAM;EACjC,CAAC,EAAE,CAAChB,YAAY,CAAC,CAAC;EAClB,MAAMkB,eAAe,GAAG5B,KAAK,CAAC6B,OAAO,CAAC,MAAMjB,QAAQ,KAAKJ,SAAS,GAAG;IACnEG,IAAI,EAAEC;EACR,CAAC,GAAG,CAAC,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EACnB,MAAM,CAACkB,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,sBAAsB,CAAC;IAC/C2B,eAAe;IACfI,YAAY,EAAEvB,WAAW,GAAG;MAC1BE,IAAI,EAAE,IAAI;MACVsB,YAAY,EAAE;IAChB,CAAC,GAAG;MACFtB,IAAI,EAAE,KAAK;MACXsB,YAAY,EAAE;IAChB,CAAC;IACDC,aAAa,EAAEb,iBAAiB;IAChCc,OAAO,EAAEhC,eAAe;IACxBU;EACF,CAAC,CAAC;EACFb,KAAK,CAACoC,SAAS,CAAC,MAAM;IACpB,IAAI,CAACN,KAAK,CAACnB,IAAI,IAAIQ,cAAc,CAACQ,OAAO,KAAK,IAAI,IAAIR,cAAc,CAACQ,OAAO,KAAKzB,mBAAmB,CAACmC,IAAI,EAAE;MACzGpB,cAAc,IAAI,IAAI,IAAIA,cAAc,CAACqB,KAAK,CAAC,CAAC;IAClD;EACF,CAAC,EAAE,CAACR,KAAK,CAACnB,IAAI,EAAEM,cAAc,CAAC,CAAC;EAChC,MAAMsB,YAAY,GAAG;IACnBT,KAAK;IACLC,QAAQ;IACRjB,OAAO;IACP0B,aAAa,EAAEzB,UAAU;IACzB0B,eAAe,EAAEvB,iBAAiB;IAClCD;EACF,CAAC;EACD,OAAO;IACLsB,YAAY;IACZ5B,IAAI,EAAEmB,KAAK,CAACnB;EACd,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}