{"ast": null, "code": "'use strict';\n\nvar slice = Array.prototype.slice;\nvar isArgs = require('./isArguments');\nvar origKeys = Object.keys;\nvar keysShim = origKeys ? function keys(o) {\n  return origKeys(o);\n} : require('./implementation');\nvar originalKeys = Object.keys;\nkeysShim.shim = function shimObjectKeys() {\n  if (Object.keys) {\n    var keysWorksWithArguments = function () {\n      // Safari 5.0 bug\n      var args = Object.keys(arguments);\n      return args && args.length === arguments.length;\n    }(1, 2);\n    if (!keysWorksWithArguments) {\n      Object.keys = function keys(object) {\n        // eslint-disable-line func-name-matching\n        if (isArgs(object)) {\n          return originalKeys(slice.call(object));\n        }\n        return originalKeys(object);\n      };\n    }\n  } else {\n    Object.keys = keysShim;\n  }\n  return Object.keys || keysShim;\n};\nmodule.exports = keysShim;", "map": {"version": 3, "names": ["slice", "Array", "prototype", "is<PERSON><PERSON><PERSON>", "require", "orig<PERSON>eys", "Object", "keys", "<PERSON><PERSON><PERSON>", "o", "originalKeys", "shim", "shimObjectKeys", "keysWorksWithArguments", "args", "arguments", "length", "object", "call", "module", "exports"], "sources": ["C:/ignition/ignition-ui/node_modules/object-keys/index.js"], "sourcesContent": ["'use strict';\n\nvar slice = Array.prototype.slice;\nvar isArgs = require('./isArguments');\n\nvar origKeys = Object.keys;\nvar keysShim = origKeys ? function keys(o) { return origKeys(o); } : require('./implementation');\n\nvar originalKeys = Object.keys;\n\nkeysShim.shim = function shimObjectKeys() {\n\tif (Object.keys) {\n\t\tvar keysWorksWithArguments = (function () {\n\t\t\t// Safari 5.0 bug\n\t\t\tvar args = Object.keys(arguments);\n\t\t\treturn args && args.length === arguments.length;\n\t\t}(1, 2));\n\t\tif (!keysWorksWithArguments) {\n\t\t\tObject.keys = function keys(object) { // eslint-disable-line func-name-matching\n\t\t\t\tif (isArgs(object)) {\n\t\t\t\t\treturn originalKeys(slice.call(object));\n\t\t\t\t}\n\t\t\t\treturn originalKeys(object);\n\t\t\t};\n\t\t}\n\t} else {\n\t\tObject.keys = keysShim;\n\t}\n\treturn Object.keys || keysShim;\n};\n\nmodule.exports = keysShim;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,KAAK,GAAGC,KAAK,CAACC,SAAS,CAACF,KAAK;AACjC,IAAIG,MAAM,GAAGC,OAAO,CAAC,eAAe,CAAC;AAErC,IAAIC,QAAQ,GAAGC,MAAM,CAACC,IAAI;AAC1B,IAAIC,QAAQ,GAAGH,QAAQ,GAAG,SAASE,IAAIA,CAACE,CAAC,EAAE;EAAE,OAAOJ,QAAQ,CAACI,CAAC,CAAC;AAAE,CAAC,GAAGL,OAAO,CAAC,kBAAkB,CAAC;AAEhG,IAAIM,YAAY,GAAGJ,MAAM,CAACC,IAAI;AAE9BC,QAAQ,CAACG,IAAI,GAAG,SAASC,cAAcA,CAAA,EAAG;EACzC,IAAIN,MAAM,CAACC,IAAI,EAAE;IAChB,IAAIM,sBAAsB,GAAI,YAAY;MACzC;MACA,IAAIC,IAAI,GAAGR,MAAM,CAACC,IAAI,CAACQ,SAAS,CAAC;MACjC,OAAOD,IAAI,IAAIA,IAAI,CAACE,MAAM,KAAKD,SAAS,CAACC,MAAM;IAChD,CAAC,CAAC,CAAC,EAAE,CAAC,CAAE;IACR,IAAI,CAACH,sBAAsB,EAAE;MAC5BP,MAAM,CAACC,IAAI,GAAG,SAASA,IAAIA,CAACU,MAAM,EAAE;QAAE;QACrC,IAAId,MAAM,CAACc,MAAM,CAAC,EAAE;UACnB,OAAOP,YAAY,CAACV,KAAK,CAACkB,IAAI,CAACD,MAAM,CAAC,CAAC;QACxC;QACA,OAAOP,YAAY,CAACO,MAAM,CAAC;MAC5B,CAAC;IACF;EACD,CAAC,MAAM;IACNX,MAAM,CAACC,IAAI,GAAGC,QAAQ;EACvB;EACA,OAAOF,MAAM,CAACC,IAAI,IAAIC,QAAQ;AAC/B,CAAC;AAEDW,MAAM,CAACC,OAAO,GAAGZ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}