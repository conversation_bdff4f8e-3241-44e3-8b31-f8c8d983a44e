{"ast": null, "code": "var _jsxFileName = \"C:\\\\ignition\\\\ignition-ui\\\\src\\\\views\\\\plan\\\\detail\\\\components\\\\AgentTab.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Box, Typography, Paper, TextField, IconButton, Avatar, Divider, CircularProgress, Chip, Tooltip } from '@mui/material';\nimport Iconify from 'components/Iconify/index';\nimport { mainYellowColor } from \"helpers/constants\";\nimport { useLocation, useParams } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AgentTab = _ref => {\n  _s();\n  let {\n    planInfo,\n    onPlanUpdate\n  } = _ref;\n  const [conversations, setConversations] = useState([]);\n  const [currentMessage, setCurrentMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef(null);\n  const inputRef = useRef(null);\n  const location = useLocation();\n  const {\n    param\n  } = useParams();\n\n  // Load conversations from localStorage\n  useEffect(() => {\n    var _location$state;\n    const savedConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\n    const planConversations = savedConversations.filter(conv => conv.planId === (planInfo === null || planInfo === void 0 ? void 0 : planInfo.id));\n    setConversations(planConversations);\n\n    // If coming from chatbot bar, add the initial message\n    if ((_location$state = location.state) !== null && _location$state !== void 0 && _location$state.message) {\n      handleSendMessage(location.state.message);\n    }\n  }, [planInfo === null || planInfo === void 0 ? void 0 : planInfo.id, location.state]);\n\n  // Auto scroll to bottom\n  useEffect(() => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  }, [conversations]);\n  const handleSendMessage = async function () {\n    let messageText = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : currentMessage;\n    if (!messageText.trim() || isLoading) return;\n    setIsLoading(true);\n    const userMessage = {\n      id: Date.now(),\n      type: 'user',\n      content: messageText.trim(),\n      timestamp: new Date().toISOString()\n    };\n    const newConversations = [...conversations, userMessage];\n    setConversations(newConversations);\n    setCurrentMessage('');\n    try {\n      // Simulate AI processing (replace with actual AI API call)\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      const aiResponse = {\n        id: Date.now() + 1,\n        type: 'assistant',\n        content: generateAIResponse(messageText, planInfo),\n        timestamp: new Date().toISOString(),\n        actions: extractActions(messageText)\n      };\n      const updatedConversations = [...newConversations, aiResponse];\n      setConversations(updatedConversations);\n\n      // Save to localStorage\n      const allConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\n      const otherPlanConversations = allConversations.filter(conv => conv.planId !== (planInfo === null || planInfo === void 0 ? void 0 : planInfo.id));\n      const planConversations = updatedConversations.map(conv => ({\n        ...conv,\n        planId: planInfo === null || planInfo === void 0 ? void 0 : planInfo.id,\n        planName: planInfo === null || planInfo === void 0 ? void 0 : planInfo.name\n      }));\n      localStorage.setItem('agent_conversations', JSON.stringify([...otherPlanConversations, ...planConversations]));\n    } catch (error) {\n      console.error('Error processing message:', error);\n      const errorResponse = {\n        id: Date.now() + 1,\n        type: 'assistant',\n        content: \"I'm sorry, I encountered an error processing your request. Please try again.\",\n        timestamp: new Date().toISOString(),\n        isError: true\n      };\n      setConversations(prev => [...prev, errorResponse]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const generateAIResponse = (message, planInfo) => {\n    // Simple response generation (replace with actual AI integration)\n    const lowerMessage = message.toLowerCase();\n    if (lowerMessage.includes('progress') || lowerMessage.includes('status')) {\n      var _planInfo$milestones;\n      return `Based on your project \"${planInfo === null || planInfo === void 0 ? void 0 : planInfo.name}\", you currently have ${(planInfo === null || planInfo === void 0 ? void 0 : (_planInfo$milestones = planInfo.milestones) === null || _planInfo$milestones === void 0 ? void 0 : _planInfo$milestones.length) || 0} milestones. I can help you track progress and update task statuses. Would you like me to show you the current completion status?`;\n    }\n    if (lowerMessage.includes('complete') || lowerMessage.includes('done')) {\n      return `I can help you mark tasks as completed. Please specify which task you'd like to mark as done, and I'll update it for you. You can say something like \"Mark task [task name] as completed\".`;\n    }\n    if (lowerMessage.includes('add') || lowerMessage.includes('create')) {\n      return `I can help you add new tasks or subtasks to your project. Please specify which milestone you'd like to add to and what the new task should be. For example: \"Add task [task name] to [milestone name]\".`;\n    }\n    if (lowerMessage.includes('delete') || lowerMessage.includes('remove')) {\n      return `I can help you remove tasks or subtasks. Please specify what you'd like to delete, and I'll handle it for you. For safety, I'll ask for confirmation before making any deletions.`;\n    }\n    return `I understand you want to \"${message}\". I'm here to help you manage your project \"${planInfo === null || planInfo === void 0 ? void 0 : planInfo.name}\". I can help you:\n\n• Mark tasks as completed or update their status\n• Add new tasks and subtasks to milestones  \n• Update task descriptions and details\n• Delete completed or unnecessary tasks\n• Show project progress and statistics\n\nWhat specific action would you like me to take?`;\n  };\n  const extractActions = message => {\n    // Extract potential actions from the message\n    const actions = [];\n    const lowerMessage = message.toLowerCase();\n    if (lowerMessage.includes('complete') || lowerMessage.includes('done')) {\n      actions.push({\n        type: 'complete_task',\n        confidence: 0.8\n      });\n    }\n    if (lowerMessage.includes('add') || lowerMessage.includes('create')) {\n      actions.push({\n        type: 'add_task',\n        confidence: 0.8\n      });\n    }\n    if (lowerMessage.includes('delete') || lowerMessage.includes('remove')) {\n      actions.push({\n        type: 'delete_task',\n        confidence: 0.7\n      });\n    }\n    return actions;\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n  const formatTimestamp = timestamp => {\n    return new Date(timestamp).toLocaleTimeString('en-US', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '70vh',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 0,\n      sx: {\n        p: 2,\n        borderRadius: '12px 12px 0 0',\n        border: '1px solid #f0f0f0',\n        borderBottom: 'none',\n        backgroundColor: '#fafafa'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          sx: {\n            backgroundColor: mainYellowColor,\n            width: 40,\n            height: 40\n          },\n          children: /*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"mdi:robot\",\n            width: 24,\n            height: 24,\n            color: \"#fff\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              fontWeight: 600,\n              color: '#333'\n            },\n            children: \"AI Project Agent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            sx: {\n              color: '#666',\n              fontFamily: '\"Recursive Variable\", sans-serif'\n            },\n            children: [\"Managing: \", planInfo === null || planInfo === void 0 ? void 0 : planInfo.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n          label: \"Beta\",\n          size: \"small\",\n          sx: {\n            backgroundColor: `${mainYellowColor}20`,\n            color: mainYellowColor,\n            fontWeight: 600,\n            ml: 'auto'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 210,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 169,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 0,\n      sx: {\n        flex: 1,\n        border: '1px solid #f0f0f0',\n        borderTop: 'none',\n        borderBottom: 'none',\n        overflow: 'auto',\n        p: 2,\n        backgroundColor: '#fff'\n      },\n      children: conversations.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          justifyContent: 'center',\n          height: '100%',\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          sx: {\n            backgroundColor: `${mainYellowColor}20`,\n            width: 60,\n            height: 60,\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"mdi:robot\",\n            width: 32,\n            height: 32,\n            color: mainYellowColor\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 247,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontFamily: '\"Recursive Variable\", sans-serif',\n            fontWeight: 600,\n            color: '#333',\n            mb: 1\n          },\n          children: \"Welcome to AI Project Agent\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 257,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            color: '#666',\n            fontFamily: '\"Recursive Variable\", sans-serif',\n            maxWidth: 400\n          },\n          children: \"I'm here to help you manage your project. Ask me questions or request changes to tasks, milestones, and more!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Box, {\n        children: [conversations.map(message => /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start',\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              maxWidth: '70%',\n              display: 'flex',\n              flexDirection: message.type === 'user' ? 'row-reverse' : 'row',\n              alignItems: 'flex-start',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                width: 32,\n                height: 32,\n                backgroundColor: message.type === 'user' ? '#e0e0e0' : mainYellowColor\n              },\n              children: /*#__PURE__*/_jsxDEV(Iconify, {\n                icon: message.type === 'user' ? \"material-symbols:person\" : \"mdi:robot\",\n                width: 18,\n                height: 18,\n                color: message.type === 'user' ? '#666' : '#fff'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Paper, {\n                elevation: 0,\n                sx: {\n                  p: 1.5,\n                  borderRadius: '12px',\n                  backgroundColor: message.type === 'user' ? mainYellowColor : '#f5f5f5',\n                  color: message.type === 'user' ? '#fff' : '#333',\n                  border: message.isError ? '1px solid #f44336' : 'none'\n                },\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontFamily: '\"Recursive Variable\", sans-serif',\n                    lineHeight: 1.5,\n                    whiteSpace: 'pre-line'\n                  },\n                  children: message.content\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 324,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 314,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  color: '#999',\n                  fontFamily: '\"Recursive Variable\", sans-serif',\n                  fontSize: '0.7rem',\n                  mt: 0.5,\n                  display: 'block',\n                  textAlign: message.type === 'user' ? 'right' : 'left'\n                },\n                children: formatTimestamp(message.timestamp)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 313,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 290,\n            columnNumber: 17\n          }, this)\n        }, message.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 282,\n          columnNumber: 15\n        }, this)), isLoading && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'flex-start',\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'flex-start',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                width: 32,\n                height: 32,\n                backgroundColor: mainYellowColor\n              },\n              children: /*#__PURE__*/_jsxDEV(Iconify, {\n                icon: \"mdi:robot\",\n                width: 18,\n                height: 18,\n                color: \"#fff\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Paper, {\n              elevation: 0,\n              sx: {\n                p: 1.5,\n                borderRadius: '12px',\n                backgroundColor: '#f5f5f5',\n                display: 'flex',\n                alignItems: 'center',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 16,\n                sx: {\n                  color: mainYellowColor\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontFamily: '\"Recursive Variable\", sans-serif',\n                  color: '#666'\n                },\n                children: \"Thinking...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 376,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 354,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 353,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: messagesEndRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 280,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 224,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 0,\n      sx: {\n        p: 2,\n        borderRadius: '0 0 12px 12px',\n        border: '1px solid #f0f0f0',\n        borderTop: 'none'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 1,\n          alignItems: 'flex-end'\n        },\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          inputRef: inputRef,\n          value: currentMessage,\n          onChange: e => setCurrentMessage(e.target.value),\n          onKeyPress: handleKeyPress,\n          placeholder: \"Ask me anything about your project...\",\n          multiline: true,\n          maxRows: 3,\n          fullWidth: true,\n          variant: \"outlined\",\n          disabled: isLoading,\n          sx: {\n            '& .MuiOutlinedInput-root': {\n              borderRadius: '8px',\n              fontFamily: '\"Recursive Variable\", sans-serif'\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"Send message\",\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: () => handleSendMessage(),\n            disabled: !currentMessage.trim() || isLoading,\n            sx: {\n              backgroundColor: currentMessage.trim() && !isLoading ? mainYellowColor : '#f0f0f0',\n              color: currentMessage.trim() && !isLoading ? '#fff' : '#999',\n              '&:hover': {\n                backgroundColor: currentMessage.trim() && !isLoading ? '#E69500' : '#f0f0f0'\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"material-symbols:send\",\n              width: 20,\n              height: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 424,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 404,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 395,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 167,\n    columnNumber: 5\n  }, this);\n};\n_s(AgentTab, \"nzqEMm/prUbAbARksxhBai1Wkss=\", false, function () {\n  return [useLocation, useParams];\n});\n_c = AgentTab;\nexport default AgentTab;\nvar _c;\n$RefreshReg$(_c, \"AgentTab\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Box", "Typography", "Paper", "TextField", "IconButton", "Avatar", "Divider", "CircularProgress", "Chip", "<PERSON><PERSON><PERSON>", "Iconify", "mainYellowColor", "useLocation", "useParams", "jsxDEV", "_jsxDEV", "AgentTab", "_ref", "_s", "planInfo", "onPlanUpdate", "conversations", "setConversations", "currentMessage", "setCurrentMessage", "isLoading", "setIsLoading", "messagesEndRef", "inputRef", "location", "param", "_location$state", "savedConversations", "JSON", "parse", "localStorage", "getItem", "planConversations", "filter", "conv", "planId", "id", "state", "message", "handleSendMessage", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "messageText", "arguments", "length", "undefined", "trim", "userMessage", "Date", "now", "type", "content", "timestamp", "toISOString", "newConversations", "Promise", "resolve", "setTimeout", "aiResponse", "generateAIResponse", "actions", "extractActions", "updatedConversations", "allConversations", "otherPlanConversations", "map", "planName", "name", "setItem", "stringify", "error", "console", "errorResponse", "isError", "prev", "lowerMessage", "toLowerCase", "includes", "_planInfo$milestones", "milestones", "push", "confidence", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "formatTimestamp", "toLocaleTimeString", "hour", "minute", "sx", "height", "display", "flexDirection", "children", "elevation", "p", "borderRadius", "border", "borderBottom", "backgroundColor", "alignItems", "gap", "width", "icon", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontFamily", "fontWeight", "label", "size", "ml", "flex", "borderTop", "overflow", "justifyContent", "textAlign", "mb", "max<PERSON><PERSON><PERSON>", "lineHeight", "whiteSpace", "fontSize", "mt", "ref", "value", "onChange", "target", "onKeyPress", "placeholder", "multiline", "maxRows", "fullWidth", "disabled", "title", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/ignition/ignition-ui/src/views/plan/detail/components/AgentTab.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  TextField,\n  IconButton,\n  Avatar,\n  Divider,\n  CircularProgress,\n  Chip,\n  Tooltip\n} from '@mui/material';\nimport Iconify from 'components/Iconify/index';\nimport { mainYellowColor } from \"helpers/constants\";\nimport { useLocation, useParams } from 'react-router-dom';\n\nconst AgentTab = ({ planInfo, onPlanUpdate }) => {\n  const [conversations, setConversations] = useState([]);\n  const [currentMessage, setCurrentMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef(null);\n  const inputRef = useRef(null);\n  const location = useLocation();\n  const { param } = useParams();\n\n  // Load conversations from localStorage\n  useEffect(() => {\n    const savedConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\n    const planConversations = savedConversations.filter(conv => conv.planId === planInfo?.id);\n    setConversations(planConversations);\n\n    // If coming from chatbot bar, add the initial message\n    if (location.state?.message) {\n      handleSendMessage(location.state.message);\n    }\n  }, [planInfo?.id, location.state]);\n\n  // Auto scroll to bottom\n  useEffect(() => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  }, [conversations]);\n\n  const handleSendMessage = async (messageText = currentMessage) => {\n    if (!messageText.trim() || isLoading) return;\n\n    setIsLoading(true);\n    const userMessage = {\n      id: Date.now(),\n      type: 'user',\n      content: messageText.trim(),\n      timestamp: new Date().toISOString()\n    };\n\n    const newConversations = [...conversations, userMessage];\n    setConversations(newConversations);\n    setCurrentMessage('');\n\n    try {\n      // Simulate AI processing (replace with actual AI API call)\n      await new Promise(resolve => setTimeout(resolve, 2000));\n\n      const aiResponse = {\n        id: Date.now() + 1,\n        type: 'assistant',\n        content: generateAIResponse(messageText, planInfo),\n        timestamp: new Date().toISOString(),\n        actions: extractActions(messageText)\n      };\n\n      const updatedConversations = [...newConversations, aiResponse];\n      setConversations(updatedConversations);\n\n      // Save to localStorage\n      const allConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\n      const otherPlanConversations = allConversations.filter(conv => conv.planId !== planInfo?.id);\n      const planConversations = updatedConversations.map(conv => ({\n        ...conv,\n        planId: planInfo?.id,\n        planName: planInfo?.name\n      }));\n      \n      localStorage.setItem('agent_conversations', JSON.stringify([\n        ...otherPlanConversations,\n        ...planConversations\n      ]));\n\n    } catch (error) {\n      console.error('Error processing message:', error);\n      const errorResponse = {\n        id: Date.now() + 1,\n        type: 'assistant',\n        content: \"I'm sorry, I encountered an error processing your request. Please try again.\",\n        timestamp: new Date().toISOString(),\n        isError: true\n      };\n      setConversations(prev => [...prev, errorResponse]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const generateAIResponse = (message, planInfo) => {\n    // Simple response generation (replace with actual AI integration)\n    const lowerMessage = message.toLowerCase();\n    \n    if (lowerMessage.includes('progress') || lowerMessage.includes('status')) {\n      return `Based on your project \"${planInfo?.name}\", you currently have ${planInfo?.milestones?.length || 0} milestones. I can help you track progress and update task statuses. Would you like me to show you the current completion status?`;\n    }\n    \n    if (lowerMessage.includes('complete') || lowerMessage.includes('done')) {\n      return `I can help you mark tasks as completed. Please specify which task you'd like to mark as done, and I'll update it for you. You can say something like \"Mark task [task name] as completed\".`;\n    }\n    \n    if (lowerMessage.includes('add') || lowerMessage.includes('create')) {\n      return `I can help you add new tasks or subtasks to your project. Please specify which milestone you'd like to add to and what the new task should be. For example: \"Add task [task name] to [milestone name]\".`;\n    }\n    \n    if (lowerMessage.includes('delete') || lowerMessage.includes('remove')) {\n      return `I can help you remove tasks or subtasks. Please specify what you'd like to delete, and I'll handle it for you. For safety, I'll ask for confirmation before making any deletions.`;\n    }\n    \n    return `I understand you want to \"${message}\". I'm here to help you manage your project \"${planInfo?.name}\". I can help you:\n\n• Mark tasks as completed or update their status\n• Add new tasks and subtasks to milestones  \n• Update task descriptions and details\n• Delete completed or unnecessary tasks\n• Show project progress and statistics\n\nWhat specific action would you like me to take?`;\n  };\n\n  const extractActions = (message) => {\n    // Extract potential actions from the message\n    const actions = [];\n    const lowerMessage = message.toLowerCase();\n    \n    if (lowerMessage.includes('complete') || lowerMessage.includes('done')) {\n      actions.push({ type: 'complete_task', confidence: 0.8 });\n    }\n    if (lowerMessage.includes('add') || lowerMessage.includes('create')) {\n      actions.push({ type: 'add_task', confidence: 0.8 });\n    }\n    if (lowerMessage.includes('delete') || lowerMessage.includes('remove')) {\n      actions.push({ type: 'delete_task', confidence: 0.7 });\n    }\n    \n    return actions;\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  const formatTimestamp = (timestamp) => {\n    return new Date(timestamp).toLocaleTimeString('en-US', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  return (\n    <Box sx={{ height: '70vh', display: 'flex', flexDirection: 'column' }}>\n      {/* Header */}\n      <Paper\n        elevation={0}\n        sx={{\n          p: 2,\n          borderRadius: '12px 12px 0 0',\n          border: '1px solid #f0f0f0',\n          borderBottom: 'none',\n          backgroundColor: '#fafafa'\n        }}\n      >\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n          <Avatar\n            sx={{\n              backgroundColor: mainYellowColor,\n              width: 40,\n              height: 40\n            }}\n          >\n            <Iconify icon=\"mdi:robot\" width={24} height={24} color=\"#fff\" />\n          </Avatar>\n          <Box>\n            <Typography\n              variant=\"h6\"\n              sx={{\n                fontFamily: '\"Recursive Variable\", sans-serif',\n                fontWeight: 600,\n                color: '#333'\n              }}\n            >\n              AI Project Agent\n            </Typography>\n            <Typography\n              variant=\"caption\"\n              sx={{\n                color: '#666',\n                fontFamily: '\"Recursive Variable\", sans-serif'\n              }}\n            >\n              Managing: {planInfo?.name}\n            </Typography>\n          </Box>\n          <Chip\n            label=\"Beta\"\n            size=\"small\"\n            sx={{\n              backgroundColor: `${mainYellowColor}20`,\n              color: mainYellowColor,\n              fontWeight: 600,\n              ml: 'auto'\n            }}\n          />\n        </Box>\n      </Paper>\n\n      {/* Messages Area */}\n      <Paper\n        elevation={0}\n        sx={{\n          flex: 1,\n          border: '1px solid #f0f0f0',\n          borderTop: 'none',\n          borderBottom: 'none',\n          overflow: 'auto',\n          p: 2,\n          backgroundColor: '#fff'\n        }}\n      >\n        {conversations.length === 0 ? (\n          <Box\n            sx={{\n              display: 'flex',\n              flexDirection: 'column',\n              alignItems: 'center',\n              justifyContent: 'center',\n              height: '100%',\n              textAlign: 'center'\n            }}\n          >\n            <Avatar\n              sx={{\n                backgroundColor: `${mainYellowColor}20`,\n                width: 60,\n                height: 60,\n                mb: 2\n              }}\n            >\n              <Iconify icon=\"mdi:robot\" width={32} height={32} color={mainYellowColor} />\n            </Avatar>\n            <Typography\n              variant=\"h6\"\n              sx={{\n                fontFamily: '\"Recursive Variable\", sans-serif',\n                fontWeight: 600,\n                color: '#333',\n                mb: 1\n              }}\n            >\n              Welcome to AI Project Agent\n            </Typography>\n            <Typography\n              variant=\"body2\"\n              sx={{\n                color: '#666',\n                fontFamily: '\"Recursive Variable\", sans-serif',\n                maxWidth: 400\n              }}\n            >\n              I'm here to help you manage your project. Ask me questions or request changes to tasks, milestones, and more!\n            </Typography>\n          </Box>\n        ) : (\n          <Box>\n            {conversations.map((message) => (\n              <Box\n                key={message.id}\n                sx={{\n                  display: 'flex',\n                  justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start',\n                  mb: 2\n                }}\n              >\n                <Box\n                  sx={{\n                    maxWidth: '70%',\n                    display: 'flex',\n                    flexDirection: message.type === 'user' ? 'row-reverse' : 'row',\n                    alignItems: 'flex-start',\n                    gap: 1\n                  }}\n                >\n                  <Avatar\n                    sx={{\n                      width: 32,\n                      height: 32,\n                      backgroundColor: message.type === 'user' ? '#e0e0e0' : mainYellowColor\n                    }}\n                  >\n                    <Iconify\n                      icon={message.type === 'user' ? \"material-symbols:person\" : \"mdi:robot\"}\n                      width={18}\n                      height={18}\n                      color={message.type === 'user' ? '#666' : '#fff'}\n                    />\n                  </Avatar>\n                  <Box>\n                    <Paper\n                      elevation={0}\n                      sx={{\n                        p: 1.5,\n                        borderRadius: '12px',\n                        backgroundColor: message.type === 'user' ? mainYellowColor : '#f5f5f5',\n                        color: message.type === 'user' ? '#fff' : '#333',\n                        border: message.isError ? '1px solid #f44336' : 'none'\n                      }}\n                    >\n                      <Typography\n                        variant=\"body2\"\n                        sx={{\n                          fontFamily: '\"Recursive Variable\", sans-serif',\n                          lineHeight: 1.5,\n                          whiteSpace: 'pre-line'\n                        }}\n                      >\n                        {message.content}\n                      </Typography>\n                    </Paper>\n                    <Typography\n                      variant=\"caption\"\n                      sx={{\n                        color: '#999',\n                        fontFamily: '\"Recursive Variable\", sans-serif',\n                        fontSize: '0.7rem',\n                        mt: 0.5,\n                        display: 'block',\n                        textAlign: message.type === 'user' ? 'right' : 'left'\n                      }}\n                    >\n                      {formatTimestamp(message.timestamp)}\n                    </Typography>\n                  </Box>\n                </Box>\n              </Box>\n            ))}\n            {isLoading && (\n              <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 2 }}>\n                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>\n                  <Avatar\n                    sx={{\n                      width: 32,\n                      height: 32,\n                      backgroundColor: mainYellowColor\n                    }}\n                  >\n                    <Iconify icon=\"mdi:robot\" width={18} height={18} color=\"#fff\" />\n                  </Avatar>\n                  <Paper\n                    elevation={0}\n                    sx={{\n                      p: 1.5,\n                      borderRadius: '12px',\n                      backgroundColor: '#f5f5f5',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 1\n                    }}\n                  >\n                    <CircularProgress size={16} sx={{ color: mainYellowColor }} />\n                    <Typography\n                      variant=\"body2\"\n                      sx={{\n                        fontFamily: '\"Recursive Variable\", sans-serif',\n                        color: '#666'\n                      }}\n                    >\n                      Thinking...\n                    </Typography>\n                  </Paper>\n                </Box>\n              </Box>\n            )}\n            <div ref={messagesEndRef} />\n          </Box>\n        )}\n      </Paper>\n\n      {/* Input Area */}\n      <Paper\n        elevation={0}\n        sx={{\n          p: 2,\n          borderRadius: '0 0 12px 12px',\n          border: '1px solid #f0f0f0',\n          borderTop: 'none'\n        }}\n      >\n        <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>\n          <TextField\n            inputRef={inputRef}\n            value={currentMessage}\n            onChange={(e) => setCurrentMessage(e.target.value)}\n            onKeyPress={handleKeyPress}\n            placeholder=\"Ask me anything about your project...\"\n            multiline\n            maxRows={3}\n            fullWidth\n            variant=\"outlined\"\n            disabled={isLoading}\n            sx={{\n              '& .MuiOutlinedInput-root': {\n                borderRadius: '8px',\n                fontFamily: '\"Recursive Variable\", sans-serif'\n              }\n            }}\n          />\n          <Tooltip title=\"Send message\">\n            <IconButton\n              onClick={() => handleSendMessage()}\n              disabled={!currentMessage.trim() || isLoading}\n              sx={{\n                backgroundColor: currentMessage.trim() && !isLoading ? mainYellowColor : '#f0f0f0',\n                color: currentMessage.trim() && !isLoading ? '#fff' : '#999',\n                '&:hover': {\n                  backgroundColor: currentMessage.trim() && !isLoading ? '#E69500' : '#f0f0f0'\n                }\n              }}\n            >\n              <Iconify icon=\"material-symbols:send\" width={20} height={20} />\n            </IconButton>\n          </Tooltip>\n        </Box>\n      </Paper>\n    </Box>\n  );\n};\n\nexport default AgentTab;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,UAAU,EACVC,MAAM,EACNC,OAAO,EACPC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,QACF,eAAe;AACtB,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1D,MAAMC,QAAQ,GAAGC,IAAA,IAAgC;EAAAC,EAAA;EAAA,IAA/B;IAAEC,QAAQ;IAAEC;EAAa,CAAC,GAAAH,IAAA;EAC1C,MAAM,CAACI,aAAa,EAAEC,gBAAgB,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAAC0B,cAAc,EAAEC,iBAAiB,CAAC,GAAG3B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC4B,SAAS,EAAEC,YAAY,CAAC,GAAG7B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM8B,cAAc,GAAG5B,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM6B,QAAQ,GAAG7B,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAM8B,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEkB;EAAM,CAAC,GAAGjB,SAAS,CAAC,CAAC;;EAE7B;EACAf,SAAS,CAAC,MAAM;IAAA,IAAAiC,eAAA;IACd,MAAMC,kBAAkB,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC,IAAI,IAAI,CAAC;IAC1F,MAAMC,iBAAiB,GAAGL,kBAAkB,CAACM,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,MAAM,MAAKrB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,EAAE,EAAC;IACzFnB,gBAAgB,CAACe,iBAAiB,CAAC;;IAEnC;IACA,KAAAN,eAAA,GAAIF,QAAQ,CAACa,KAAK,cAAAX,eAAA,eAAdA,eAAA,CAAgBY,OAAO,EAAE;MAC3BC,iBAAiB,CAACf,QAAQ,CAACa,KAAK,CAACC,OAAO,CAAC;IAC3C;EACF,CAAC,EAAE,CAACxB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,EAAE,EAAEZ,QAAQ,CAACa,KAAK,CAAC,CAAC;;EAElC;EACA5C,SAAS,CAAC,MAAM;IAAA,IAAA+C,qBAAA;IACd,CAAAA,qBAAA,GAAAlB,cAAc,CAACmB,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC,EAAE,CAAC3B,aAAa,CAAC,CAAC;EAEnB,MAAMuB,iBAAiB,GAAG,eAAAA,CAAA,EAAwC;IAAA,IAAjCK,WAAW,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG3B,cAAc;IAC3D,IAAI,CAAC0B,WAAW,CAACI,IAAI,CAAC,CAAC,IAAI5B,SAAS,EAAE;IAEtCC,YAAY,CAAC,IAAI,CAAC;IAClB,MAAM4B,WAAW,GAAG;MAClBb,EAAE,EAAEc,IAAI,CAACC,GAAG,CAAC,CAAC;MACdC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAET,WAAW,CAACI,IAAI,CAAC,CAAC;MAC3BM,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC;IACpC,CAAC;IAED,MAAMC,gBAAgB,GAAG,CAAC,GAAGxC,aAAa,EAAEiC,WAAW,CAAC;IACxDhC,gBAAgB,CAACuC,gBAAgB,CAAC;IAClCrC,iBAAiB,CAAC,EAAE,CAAC;IAErB,IAAI;MACF;MACA,MAAM,IAAIsC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvD,MAAME,UAAU,GAAG;QACjBxB,EAAE,EAAEc,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;QAClBC,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAEQ,kBAAkB,CAACjB,WAAW,EAAE9B,QAAQ,CAAC;QAClDwC,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC;QACnCO,OAAO,EAAEC,cAAc,CAACnB,WAAW;MACrC,CAAC;MAED,MAAMoB,oBAAoB,GAAG,CAAC,GAAGR,gBAAgB,EAAEI,UAAU,CAAC;MAC9D3C,gBAAgB,CAAC+C,oBAAoB,CAAC;;MAEtC;MACA,MAAMC,gBAAgB,GAAGrC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC,IAAI,IAAI,CAAC;MACxF,MAAMmC,sBAAsB,GAAGD,gBAAgB,CAAChC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,MAAM,MAAKrB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,EAAE,EAAC;MAC5F,MAAMJ,iBAAiB,GAAGgC,oBAAoB,CAACG,GAAG,CAACjC,IAAI,KAAK;QAC1D,GAAGA,IAAI;QACPC,MAAM,EAAErB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEsB,EAAE;QACpBgC,QAAQ,EAAEtD,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuD;MACtB,CAAC,CAAC,CAAC;MAEHvC,YAAY,CAACwC,OAAO,CAAC,qBAAqB,EAAE1C,IAAI,CAAC2C,SAAS,CAAC,CACzD,GAAGL,sBAAsB,EACzB,GAAGlC,iBAAiB,CACrB,CAAC,CAAC;IAEL,CAAC,CAAC,OAAOwC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAME,aAAa,GAAG;QACpBtC,EAAE,EAAEc,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;QAClBC,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAE,8EAA8E;QACvFC,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC;QACnCoB,OAAO,EAAE;MACX,CAAC;MACD1D,gBAAgB,CAAC2D,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEF,aAAa,CAAC,CAAC;IACpD,CAAC,SAAS;MACRrD,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAMwC,kBAAkB,GAAGA,CAACvB,OAAO,EAAExB,QAAQ,KAAK;IAChD;IACA,MAAM+D,YAAY,GAAGvC,OAAO,CAACwC,WAAW,CAAC,CAAC;IAE1C,IAAID,YAAY,CAACE,QAAQ,CAAC,UAAU,CAAC,IAAIF,YAAY,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAAA,IAAAC,oBAAA;MACxE,OAAO,0BAA0BlE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuD,IAAI,yBAAyB,CAAAvD,QAAQ,aAARA,QAAQ,wBAAAkE,oBAAA,GAARlE,QAAQ,CAAEmE,UAAU,cAAAD,oBAAA,uBAApBA,oBAAA,CAAsBlC,MAAM,KAAI,CAAC,mIAAmI;IAC9O;IAEA,IAAI+B,YAAY,CAACE,QAAQ,CAAC,UAAU,CAAC,IAAIF,YAAY,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;MACtE,OAAO,4LAA4L;IACrM;IAEA,IAAIF,YAAY,CAACE,QAAQ,CAAC,KAAK,CAAC,IAAIF,YAAY,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACnE,OAAO,yMAAyM;IAClN;IAEA,IAAIF,YAAY,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAIF,YAAY,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACtE,OAAO,mLAAmL;IAC5L;IAEA,OAAO,6BAA6BzC,OAAO,gDAAgDxB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuD,IAAI;AAC7G;AACA;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD;EAC9C,CAAC;EAED,MAAMN,cAAc,GAAIzB,OAAO,IAAK;IAClC;IACA,MAAMwB,OAAO,GAAG,EAAE;IAClB,MAAMe,YAAY,GAAGvC,OAAO,CAACwC,WAAW,CAAC,CAAC;IAE1C,IAAID,YAAY,CAACE,QAAQ,CAAC,UAAU,CAAC,IAAIF,YAAY,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAE;MACtEjB,OAAO,CAACoB,IAAI,CAAC;QAAE9B,IAAI,EAAE,eAAe;QAAE+B,UAAU,EAAE;MAAI,CAAC,CAAC;IAC1D;IACA,IAAIN,YAAY,CAACE,QAAQ,CAAC,KAAK,CAAC,IAAIF,YAAY,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACnEjB,OAAO,CAACoB,IAAI,CAAC;QAAE9B,IAAI,EAAE,UAAU;QAAE+B,UAAU,EAAE;MAAI,CAAC,CAAC;IACrD;IACA,IAAIN,YAAY,CAACE,QAAQ,CAAC,QAAQ,CAAC,IAAIF,YAAY,CAACE,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACtEjB,OAAO,CAACoB,IAAI,CAAC;QAAE9B,IAAI,EAAE,aAAa;QAAE+B,UAAU,EAAE;MAAI,CAAC,CAAC;IACxD;IAEA,OAAOrB,OAAO;EAChB,CAAC;EAED,MAAMsB,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBjD,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;EAED,MAAMkD,eAAe,GAAInC,SAAS,IAAK;IACrC,OAAO,IAAIJ,IAAI,CAACI,SAAS,CAAC,CAACoC,kBAAkB,CAAC,OAAO,EAAE;MACrDC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,oBACElF,OAAA,CAACf,GAAG;IAACkG,EAAE,EAAE;MAAEC,MAAM,EAAE,MAAM;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE;IAAS,CAAE;IAAAC,QAAA,gBAEpEvF,OAAA,CAACb,KAAK;MACJqG,SAAS,EAAE,CAAE;MACbL,EAAE,EAAE;QACFM,CAAC,EAAE,CAAC;QACJC,YAAY,EAAE,eAAe;QAC7BC,MAAM,EAAE,mBAAmB;QAC3BC,YAAY,EAAE,MAAM;QACpBC,eAAe,EAAE;MACnB,CAAE;MAAAN,QAAA,eAEFvF,OAAA,CAACf,GAAG;QAACkG,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAES,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAR,QAAA,gBACzDvF,OAAA,CAACV,MAAM;UACL6F,EAAE,EAAE;YACFU,eAAe,EAAEjG,eAAe;YAChCoG,KAAK,EAAE,EAAE;YACTZ,MAAM,EAAE;UACV,CAAE;UAAAG,QAAA,eAEFvF,OAAA,CAACL,OAAO;YAACsG,IAAI,EAAC,WAAW;YAACD,KAAK,EAAE,EAAG;YAACZ,MAAM,EAAE,EAAG;YAACc,KAAK,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eACTtG,OAAA,CAACf,GAAG;UAAAsG,QAAA,gBACFvF,OAAA,CAACd,UAAU;YACTqH,OAAO,EAAC,IAAI;YACZpB,EAAE,EAAE;cACFqB,UAAU,EAAE,kCAAkC;cAC9CC,UAAU,EAAE,GAAG;cACfP,KAAK,EAAE;YACT,CAAE;YAAAX,QAAA,EACH;UAED;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbtG,OAAA,CAACd,UAAU;YACTqH,OAAO,EAAC,SAAS;YACjBpB,EAAE,EAAE;cACFe,KAAK,EAAE,MAAM;cACbM,UAAU,EAAE;YACd,CAAE;YAAAjB,QAAA,GACH,YACW,EAACnF,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuD,IAAI;UAAA;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNtG,OAAA,CAACP,IAAI;UACHiH,KAAK,EAAC,MAAM;UACZC,IAAI,EAAC,OAAO;UACZxB,EAAE,EAAE;YACFU,eAAe,EAAE,GAAGjG,eAAe,IAAI;YACvCsG,KAAK,EAAEtG,eAAe;YACtB6G,UAAU,EAAE,GAAG;YACfG,EAAE,EAAE;UACN;QAAE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGRtG,OAAA,CAACb,KAAK;MACJqG,SAAS,EAAE,CAAE;MACbL,EAAE,EAAE;QACF0B,IAAI,EAAE,CAAC;QACPlB,MAAM,EAAE,mBAAmB;QAC3BmB,SAAS,EAAE,MAAM;QACjBlB,YAAY,EAAE,MAAM;QACpBmB,QAAQ,EAAE,MAAM;QAChBtB,CAAC,EAAE,CAAC;QACJI,eAAe,EAAE;MACnB,CAAE;MAAAN,QAAA,EAEDjF,aAAa,CAAC8B,MAAM,KAAK,CAAC,gBACzBpC,OAAA,CAACf,GAAG;QACFkG,EAAE,EAAE;UACFE,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBQ,UAAU,EAAE,QAAQ;UACpBkB,cAAc,EAAE,QAAQ;UACxB5B,MAAM,EAAE,MAAM;UACd6B,SAAS,EAAE;QACb,CAAE;QAAA1B,QAAA,gBAEFvF,OAAA,CAACV,MAAM;UACL6F,EAAE,EAAE;YACFU,eAAe,EAAE,GAAGjG,eAAe,IAAI;YACvCoG,KAAK,EAAE,EAAE;YACTZ,MAAM,EAAE,EAAE;YACV8B,EAAE,EAAE;UACN,CAAE;UAAA3B,QAAA,eAEFvF,OAAA,CAACL,OAAO;YAACsG,IAAI,EAAC,WAAW;YAACD,KAAK,EAAE,EAAG;YAACZ,MAAM,EAAE,EAAG;YAACc,KAAK,EAAEtG;UAAgB;YAAAuG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC,eACTtG,OAAA,CAACd,UAAU;UACTqH,OAAO,EAAC,IAAI;UACZpB,EAAE,EAAE;YACFqB,UAAU,EAAE,kCAAkC;YAC9CC,UAAU,EAAE,GAAG;YACfP,KAAK,EAAE,MAAM;YACbgB,EAAE,EAAE;UACN,CAAE;UAAA3B,QAAA,EACH;QAED;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbtG,OAAA,CAACd,UAAU;UACTqH,OAAO,EAAC,OAAO;UACfpB,EAAE,EAAE;YACFe,KAAK,EAAE,MAAM;YACbM,UAAU,EAAE,kCAAkC;YAC9CW,QAAQ,EAAE;UACZ,CAAE;UAAA5B,QAAA,EACH;QAED;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,gBAENtG,OAAA,CAACf,GAAG;QAAAsG,QAAA,GACDjF,aAAa,CAACmD,GAAG,CAAE7B,OAAO,iBACzB5B,OAAA,CAACf,GAAG;UAEFkG,EAAE,EAAE;YACFE,OAAO,EAAE,MAAM;YACf2B,cAAc,EAAEpF,OAAO,CAACc,IAAI,KAAK,MAAM,GAAG,UAAU,GAAG,YAAY;YACnEwE,EAAE,EAAE;UACN,CAAE;UAAA3B,QAAA,eAEFvF,OAAA,CAACf,GAAG;YACFkG,EAAE,EAAE;cACFgC,QAAQ,EAAE,KAAK;cACf9B,OAAO,EAAE,MAAM;cACfC,aAAa,EAAE1D,OAAO,CAACc,IAAI,KAAK,MAAM,GAAG,aAAa,GAAG,KAAK;cAC9DoD,UAAU,EAAE,YAAY;cACxBC,GAAG,EAAE;YACP,CAAE;YAAAR,QAAA,gBAEFvF,OAAA,CAACV,MAAM;cACL6F,EAAE,EAAE;gBACFa,KAAK,EAAE,EAAE;gBACTZ,MAAM,EAAE,EAAE;gBACVS,eAAe,EAAEjE,OAAO,CAACc,IAAI,KAAK,MAAM,GAAG,SAAS,GAAG9C;cACzD,CAAE;cAAA2F,QAAA,eAEFvF,OAAA,CAACL,OAAO;gBACNsG,IAAI,EAAErE,OAAO,CAACc,IAAI,KAAK,MAAM,GAAG,yBAAyB,GAAG,WAAY;gBACxEsD,KAAK,EAAE,EAAG;gBACVZ,MAAM,EAAE,EAAG;gBACXc,KAAK,EAAEtE,OAAO,CAACc,IAAI,KAAK,MAAM,GAAG,MAAM,GAAG;cAAO;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACTtG,OAAA,CAACf,GAAG;cAAAsG,QAAA,gBACFvF,OAAA,CAACb,KAAK;gBACJqG,SAAS,EAAE,CAAE;gBACbL,EAAE,EAAE;kBACFM,CAAC,EAAE,GAAG;kBACNC,YAAY,EAAE,MAAM;kBACpBG,eAAe,EAAEjE,OAAO,CAACc,IAAI,KAAK,MAAM,GAAG9C,eAAe,GAAG,SAAS;kBACtEsG,KAAK,EAAEtE,OAAO,CAACc,IAAI,KAAK,MAAM,GAAG,MAAM,GAAG,MAAM;kBAChDiD,MAAM,EAAE/D,OAAO,CAACqC,OAAO,GAAG,mBAAmB,GAAG;gBAClD,CAAE;gBAAAsB,QAAA,eAEFvF,OAAA,CAACd,UAAU;kBACTqH,OAAO,EAAC,OAAO;kBACfpB,EAAE,EAAE;oBACFqB,UAAU,EAAE,kCAAkC;oBAC9CY,UAAU,EAAE,GAAG;oBACfC,UAAU,EAAE;kBACd,CAAE;kBAAA9B,QAAA,EAED3D,OAAO,CAACe;gBAAO;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eACRtG,OAAA,CAACd,UAAU;gBACTqH,OAAO,EAAC,SAAS;gBACjBpB,EAAE,EAAE;kBACFe,KAAK,EAAE,MAAM;kBACbM,UAAU,EAAE,kCAAkC;kBAC9Cc,QAAQ,EAAE,QAAQ;kBAClBC,EAAE,EAAE,GAAG;kBACPlC,OAAO,EAAE,OAAO;kBAChB4B,SAAS,EAAErF,OAAO,CAACc,IAAI,KAAK,MAAM,GAAG,OAAO,GAAG;gBACjD,CAAE;gBAAA6C,QAAA,EAEDR,eAAe,CAACnD,OAAO,CAACgB,SAAS;cAAC;gBAAAuD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAlED1E,OAAO,CAACF,EAAE;UAAAyE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmEZ,CACN,CAAC,EACD5F,SAAS,iBACRV,OAAA,CAACf,GAAG;UAACkG,EAAE,EAAE;YAAEE,OAAO,EAAE,MAAM;YAAE2B,cAAc,EAAE,YAAY;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAA3B,QAAA,eAChEvF,OAAA,CAACf,GAAG;YAACkG,EAAE,EAAE;cAAEE,OAAO,EAAE,MAAM;cAAES,UAAU,EAAE,YAAY;cAAEC,GAAG,EAAE;YAAE,CAAE;YAAAR,QAAA,gBAC7DvF,OAAA,CAACV,MAAM;cACL6F,EAAE,EAAE;gBACFa,KAAK,EAAE,EAAE;gBACTZ,MAAM,EAAE,EAAE;gBACVS,eAAe,EAAEjG;cACnB,CAAE;cAAA2F,QAAA,eAEFvF,OAAA,CAACL,OAAO;gBAACsG,IAAI,EAAC,WAAW;gBAACD,KAAK,EAAE,EAAG;gBAACZ,MAAM,EAAE,EAAG;gBAACc,KAAK,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACTtG,OAAA,CAACb,KAAK;cACJqG,SAAS,EAAE,CAAE;cACbL,EAAE,EAAE;gBACFM,CAAC,EAAE,GAAG;gBACNC,YAAY,EAAE,MAAM;gBACpBG,eAAe,EAAE,SAAS;gBAC1BR,OAAO,EAAE,MAAM;gBACfS,UAAU,EAAE,QAAQ;gBACpBC,GAAG,EAAE;cACP,CAAE;cAAAR,QAAA,gBAEFvF,OAAA,CAACR,gBAAgB;gBAACmH,IAAI,EAAE,EAAG;gBAACxB,EAAE,EAAE;kBAAEe,KAAK,EAAEtG;gBAAgB;cAAE;gBAAAuG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9DtG,OAAA,CAACd,UAAU;gBACTqH,OAAO,EAAC,OAAO;gBACfpB,EAAE,EAAE;kBACFqB,UAAU,EAAE,kCAAkC;kBAC9CN,KAAK,EAAE;gBACT,CAAE;gBAAAX,QAAA,EACH;cAED;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eACDtG,OAAA;UAAKwH,GAAG,EAAE5G;QAAe;UAAAuF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGRtG,OAAA,CAACb,KAAK;MACJqG,SAAS,EAAE,CAAE;MACbL,EAAE,EAAE;QACFM,CAAC,EAAE,CAAC;QACJC,YAAY,EAAE,eAAe;QAC7BC,MAAM,EAAE,mBAAmB;QAC3BmB,SAAS,EAAE;MACb,CAAE;MAAAvB,QAAA,eAEFvF,OAAA,CAACf,GAAG;QAACkG,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEU,GAAG,EAAE,CAAC;UAAED,UAAU,EAAE;QAAW,CAAE;QAAAP,QAAA,gBAC3DvF,OAAA,CAACZ,SAAS;UACRyB,QAAQ,EAAEA,QAAS;UACnB4G,KAAK,EAAEjH,cAAe;UACtBkH,QAAQ,EAAG/C,CAAC,IAAKlE,iBAAiB,CAACkE,CAAC,CAACgD,MAAM,CAACF,KAAK,CAAE;UACnDG,UAAU,EAAElD,cAAe;UAC3BmD,WAAW,EAAC,uCAAuC;UACnDC,SAAS;UACTC,OAAO,EAAE,CAAE;UACXC,SAAS;UACTzB,OAAO,EAAC,UAAU;UAClB0B,QAAQ,EAAEvH,SAAU;UACpByE,EAAE,EAAE;YACF,0BAA0B,EAAE;cAC1BO,YAAY,EAAE,KAAK;cACnBc,UAAU,EAAE;YACd;UACF;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFtG,OAAA,CAACN,OAAO;UAACwI,KAAK,EAAC,cAAc;UAAA3C,QAAA,eAC3BvF,OAAA,CAACX,UAAU;YACT8I,OAAO,EAAEA,CAAA,KAAMtG,iBAAiB,CAAC,CAAE;YACnCoG,QAAQ,EAAE,CAACzH,cAAc,CAAC8B,IAAI,CAAC,CAAC,IAAI5B,SAAU;YAC9CyE,EAAE,EAAE;cACFU,eAAe,EAAErF,cAAc,CAAC8B,IAAI,CAAC,CAAC,IAAI,CAAC5B,SAAS,GAAGd,eAAe,GAAG,SAAS;cAClFsG,KAAK,EAAE1F,cAAc,CAAC8B,IAAI,CAAC,CAAC,IAAI,CAAC5B,SAAS,GAAG,MAAM,GAAG,MAAM;cAC5D,SAAS,EAAE;gBACTmF,eAAe,EAAErF,cAAc,CAAC8B,IAAI,CAAC,CAAC,IAAI,CAAC5B,SAAS,GAAG,SAAS,GAAG;cACrE;YACF,CAAE;YAAA6E,QAAA,eAEFvF,OAAA,CAACL,OAAO;cAACsG,IAAI,EAAC,uBAAuB;cAACD,KAAK,EAAE,EAAG;cAACZ,MAAM,EAAE;YAAG;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACnG,EAAA,CAxaIF,QAAQ;EAAA,QAMKJ,WAAW,EACVC,SAAS;AAAA;AAAAsI,EAAA,GAPvBnI,QAAQ;AA0ad,eAAeA,QAAQ;AAAC,IAAAmI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}