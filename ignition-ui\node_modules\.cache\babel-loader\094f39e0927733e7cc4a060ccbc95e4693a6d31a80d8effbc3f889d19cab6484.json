{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getStepIndicatorUtilityClass(slot) {\n  return generateUtilityClass('MuiStepIndicator', slot);\n}\nconst stepIndicatorClasses = generateUtilityClasses('MuiStepIndicator', ['root', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid', 'horizontal', 'vertical']);\nexport default stepIndicatorClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getStepIndicatorUtilityClass", "slot", "stepIndicatorClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/StepIndicator/stepIndicatorClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getStepIndicatorUtilityClass(slot) {\n  return generateUtilityClass('MuiStepIndicator', slot);\n}\nconst stepIndicatorClasses = generateUtilityClasses('MuiStepIndicator', ['root', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid', 'horizontal', 'vertical']);\nexport default stepIndicatorClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,4BAA4BA,CAACC,IAAI,EAAE;EACjD,OAAOH,oBAAoB,CAAC,kBAAkB,EAAEG,IAAI,CAAC;AACvD;AACA,MAAMC,oBAAoB,GAAGH,sBAAsB,CAAC,kBAAkB,EAAE,CAAC,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,iBAAiB,EAAE,aAAa,EAAE,cAAc,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;AAC5Q,eAAeG,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}