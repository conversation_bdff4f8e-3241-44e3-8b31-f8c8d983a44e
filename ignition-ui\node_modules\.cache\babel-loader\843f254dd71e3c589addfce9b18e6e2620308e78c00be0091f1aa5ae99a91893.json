{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getModalDialogUtilityClass(slot) {\n  return generateUtilityClass('MuiModalDialog', slot);\n}\nconst modalDialogClasses = generateUtilityClasses('MuiModalDialog', ['root', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid', 'sizeSm', 'sizeMd', 'sizeLg', 'layoutCenter', 'layoutFullscreen']);\nexport default modalDialogClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getModalDialogUtilityClass", "slot", "modalDialogClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/ModalDialog/modalDialogClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getModalDialogUtilityClass(slot) {\n  return generateUtilityClass('MuiModalDialog', slot);\n}\nconst modalDialogClasses = generateUtilityClasses('MuiModalDialog', ['root', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid', 'sizeSm', 'sizeMd', 'sizeLg', 'layoutCenter', 'layoutFullscreen']);\nexport default modalDialogClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,0BAA0BA,CAACC,IAAI,EAAE;EAC/C,OAAOH,oBAAoB,CAAC,gBAAgB,EAAEG,IAAI,CAAC;AACrD;AACA,MAAMC,kBAAkB,GAAGH,sBAAsB,CAAC,gBAAgB,EAAE,CAAC,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,iBAAiB,EAAE,aAAa,EAAE,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,cAAc,EAAE,kBAAkB,CAAC,CAAC;AAChT,eAAeG,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}