{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"component\", \"color\", \"variant\", \"className\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport { unstable_capitalize as capitalize } from '@mui/utils';\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport { StyledListItemButton } from '../ListItemButton/ListItemButton';\nimport { styled, useThemeProps } from '../styles';\nimport { useVariantColor } from '../styles/variantColorInheritance';\nimport { getAutocompleteOptionUtilityClass } from './autocompleteOptionClasses';\nimport useSlot from '../utils/useSlot';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    color,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', color && `color${capitalize(color)}`, variant && `variant${capitalize(variant)}`]\n  };\n  return composeClasses(slots, getAutocompleteOptionUtilityClass, {});\n};\nexport const StyledAutocompleteOption = styled(StyledListItemButton)(_ref => {\n  let {\n    theme,\n    ownerState\n  } = _ref;\n  var _theme$variants, _theme$variants2;\n  return {\n    '&[aria-disabled=\"true\"]': (_theme$variants = theme.variants[`${ownerState.variant}Disabled`]) == null ? void 0 : _theme$variants[ownerState.color],\n    '&[aria-selected=\"true\"]': _extends({}, (_theme$variants2 = theme.variants[`${ownerState.variant}Active`]) == null ? void 0 : _theme$variants2[ownerState.color], {\n      fontWeight: theme.vars.fontWeight.md\n    })\n  };\n});\nconst AutocompleteOptionRoot = styled(StyledAutocompleteOption, {\n  name: 'JoyAutocompleteOption',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({});\n/**\n *\n * Demos:\n *\n * - [Autocomplete](https://mui.com/joy-ui/react-autocomplete/)\n *\n * API:\n *\n * - [AutocompleteOption API](https://mui.com/joy-ui/api/autocomplete-option/)\n */\nconst AutocompleteOption = /*#__PURE__*/React.forwardRef(function AutocompleteOption(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyAutocompleteOption'\n  });\n  const {\n      children,\n      component = 'li',\n      color: colorProp = 'neutral',\n      variant: variantProp = 'plain',\n      className,\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    variant = variantProp,\n    color = colorProp\n  } = useVariantColor(inProps.variant, inProps.color);\n  const ownerState = _extends({}, props, {\n    component,\n    color,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    className: clsx(classes.root, className),\n    elementType: AutocompleteOptionRoot,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      as: component,\n      role: 'option'\n    }\n  });\n  return /*#__PURE__*/_jsx(SlotRoot, _extends({}, rootProps, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? AutocompleteOption.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component. It supports those theme colors that make sense for this component.\n   * @default 'neutral'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['danger', 'neutral', 'primary', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The [global variant](https://mui.com/joy-ui/main-features/global-variants/) to use.\n   * @default 'plain'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['contained', 'light', 'outlined', 'text']), PropTypes.string])\n} : void 0;\nexport default AutocompleteOption;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "clsx", "PropTypes", "unstable_capitalize", "capitalize", "unstable_composeClasses", "composeClasses", "StyledListItemButton", "styled", "useThemeProps", "useVariantColor", "getAutocompleteOptionUtilityClass", "useSlot", "jsx", "_jsx", "useUtilityClasses", "ownerState", "color", "variant", "slots", "root", "StyledAutocompleteOption", "_ref", "theme", "_theme$variants", "_theme$variants2", "variants", "fontWeight", "vars", "md", "AutocompleteOptionRoot", "name", "slot", "overridesResolver", "props", "styles", "AutocompleteOption", "forwardRef", "inProps", "ref", "children", "component", "colorProp", "variantProp", "className", "slotProps", "other", "classes", "externalForwardedProps", "SlotRoot", "rootProps", "elementType", "additionalProps", "as", "role", "process", "env", "NODE_ENV", "propTypes", "node", "string", "oneOfType", "oneOf", "shape", "func", "object", "sx", "arrayOf", "bool"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/AutocompleteOption/AutocompleteOption.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"component\", \"color\", \"variant\", \"className\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport { unstable_capitalize as capitalize } from '@mui/utils';\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport { StyledListItemButton } from '../ListItemButton/ListItemButton';\nimport { styled, useThemeProps } from '../styles';\nimport { useVariantColor } from '../styles/variantColorInheritance';\nimport { getAutocompleteOptionUtilityClass } from './autocompleteOptionClasses';\nimport useSlot from '../utils/useSlot';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    color,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', color && `color${capitalize(color)}`, variant && `variant${capitalize(variant)}`]\n  };\n  return composeClasses(slots, getAutocompleteOptionUtilityClass, {});\n};\nexport const StyledAutocompleteOption = styled(StyledListItemButton)(({\n  theme,\n  ownerState\n}) => {\n  var _theme$variants, _theme$variants2;\n  return {\n    '&[aria-disabled=\"true\"]': (_theme$variants = theme.variants[`${ownerState.variant}Disabled`]) == null ? void 0 : _theme$variants[ownerState.color],\n    '&[aria-selected=\"true\"]': _extends({}, (_theme$variants2 = theme.variants[`${ownerState.variant}Active`]) == null ? void 0 : _theme$variants2[ownerState.color], {\n      fontWeight: theme.vars.fontWeight.md\n    })\n  };\n});\nconst AutocompleteOptionRoot = styled(StyledAutocompleteOption, {\n  name: 'JoyAutocompleteOption',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({});\n/**\n *\n * Demos:\n *\n * - [Autocomplete](https://mui.com/joy-ui/react-autocomplete/)\n *\n * API:\n *\n * - [AutocompleteOption API](https://mui.com/joy-ui/api/autocomplete-option/)\n */\nconst AutocompleteOption = /*#__PURE__*/React.forwardRef(function AutocompleteOption(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyAutocompleteOption'\n  });\n  const {\n      children,\n      component = 'li',\n      color: colorProp = 'neutral',\n      variant: variantProp = 'plain',\n      className,\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    variant = variantProp,\n    color = colorProp\n  } = useVariantColor(inProps.variant, inProps.color);\n  const ownerState = _extends({}, props, {\n    component,\n    color,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    className: clsx(classes.root, className),\n    elementType: AutocompleteOptionRoot,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      as: component,\n      role: 'option'\n    }\n  });\n  return /*#__PURE__*/_jsx(SlotRoot, _extends({}, rootProps, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? AutocompleteOption.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component. It supports those theme colors that make sense for this component.\n   * @default 'neutral'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['danger', 'neutral', 'primary', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The [global variant](https://mui.com/joy-ui/main-features/global-variants/) to use.\n   * @default 'plain'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['contained', 'light', 'outlined', 'text']), PropTypes.string])\n} : void 0;\nexport default AutocompleteOption;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,CAAC;AAClG,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,mBAAmB,IAAIC,UAAU,QAAQ,YAAY;AAC9D,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,0BAA0B;AACpF,SAASC,oBAAoB,QAAQ,kCAAkC;AACvE,SAASC,MAAM,EAAEC,aAAa,QAAQ,WAAW;AACjD,SAASC,eAAe,QAAQ,mCAAmC;AACnE,SAASC,iCAAiC,QAAQ,6BAA6B;AAC/E,OAAOC,OAAO,MAAM,kBAAkB;AACtC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,KAAK;IACLC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEH,KAAK,IAAI,QAAQb,UAAU,CAACa,KAAK,CAAC,EAAE,EAAEC,OAAO,IAAI,UAAUd,UAAU,CAACc,OAAO,CAAC,EAAE;EACjG,CAAC;EACD,OAAOZ,cAAc,CAACa,KAAK,EAAER,iCAAiC,EAAE,CAAC,CAAC,CAAC;AACrE,CAAC;AACD,OAAO,MAAMU,wBAAwB,GAAGb,MAAM,CAACD,oBAAoB,CAAC,CAACe,IAAA,IAG/D;EAAA,IAHgE;IACpEC,KAAK;IACLP;EACF,CAAC,GAAAM,IAAA;EACC,IAAIE,eAAe,EAAEC,gBAAgB;EACrC,OAAO;IACL,yBAAyB,EAAE,CAACD,eAAe,GAAGD,KAAK,CAACG,QAAQ,CAAC,GAAGV,UAAU,CAACE,OAAO,UAAU,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGM,eAAe,CAACR,UAAU,CAACC,KAAK,CAAC;IACnJ,yBAAyB,EAAEnB,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC2B,gBAAgB,GAAGF,KAAK,CAACG,QAAQ,CAAC,GAAGV,UAAU,CAACE,OAAO,QAAQ,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGO,gBAAgB,CAACT,UAAU,CAACC,KAAK,CAAC,EAAE;MAChKU,UAAU,EAAEJ,KAAK,CAACK,IAAI,CAACD,UAAU,CAACE;IACpC,CAAC;EACH,CAAC;AACH,CAAC,CAAC;AACF,MAAMC,sBAAsB,GAAGtB,MAAM,CAACa,wBAAwB,EAAE;EAC9DU,IAAI,EAAE,uBAAuB;EAC7BC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACf;AAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgB,kBAAkB,GAAG,aAAapC,KAAK,CAACqC,UAAU,CAAC,SAASD,kBAAkBA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACjG,MAAML,KAAK,GAAGzB,aAAa,CAAC;IAC1ByB,KAAK,EAAEI,OAAO;IACdP,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFS,QAAQ;MACRC,SAAS,GAAG,IAAI;MAChBxB,KAAK,EAAEyB,SAAS,GAAG,SAAS;MAC5BxB,OAAO,EAAEyB,WAAW,GAAG,OAAO;MAC9BC,SAAS;MACTzB,KAAK,GAAG,CAAC,CAAC;MACV0B,SAAS,GAAG,CAAC;IACf,CAAC,GAAGX,KAAK;IACTY,KAAK,GAAGjD,6BAA6B,CAACqC,KAAK,EAAEnC,SAAS,CAAC;EACzD,MAAM;IACJmB,OAAO,GAAGyB,WAAW;IACrB1B,KAAK,GAAGyB;EACV,CAAC,GAAGhC,eAAe,CAAC4B,OAAO,CAACpB,OAAO,EAAEoB,OAAO,CAACrB,KAAK,CAAC;EACnD,MAAMD,UAAU,GAAGlB,QAAQ,CAAC,CAAC,CAAC,EAAEoC,KAAK,EAAE;IACrCO,SAAS;IACTxB,KAAK;IACLC;EACF,CAAC,CAAC;EACF,MAAM6B,OAAO,GAAGhC,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMgC,sBAAsB,GAAGlD,QAAQ,CAAC,CAAC,CAAC,EAAEgD,KAAK,EAAE;IACjDL,SAAS;IACTtB,KAAK;IACL0B;EACF,CAAC,CAAC;EACF,MAAM,CAACI,QAAQ,EAAEC,SAAS,CAAC,GAAGtC,OAAO,CAAC,MAAM,EAAE;IAC5C2B,GAAG;IACHK,SAAS,EAAE3C,IAAI,CAAC8C,OAAO,CAAC3B,IAAI,EAAEwB,SAAS,CAAC;IACxCO,WAAW,EAAErB,sBAAsB;IACnCkB,sBAAsB;IACtBhC,UAAU;IACVoC,eAAe,EAAE;MACfC,EAAE,EAAEZ,SAAS;MACba,IAAI,EAAE;IACR;EACF,CAAC,CAAC;EACF,OAAO,aAAaxC,IAAI,CAACmC,QAAQ,EAAEnD,QAAQ,CAAC,CAAC,CAAC,EAAEoD,SAAS,EAAE;IACzDV,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFe,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGrB,kBAAkB,CAACsB,SAAS,CAAC,yBAAyB;EAC5F;EACA;EACA;EACA;EACA;AACF;AACA;EACElB,QAAQ,EAAEtC,SAAS,CAACyD,IAAI;EACxB;AACF;AACA;EACEf,SAAS,EAAE1C,SAAS,CAAC0D,MAAM;EAC3B;AACF;AACA;AACA;EACE3C,KAAK,EAAEf,SAAS,CAAC,sCAAsC2D,SAAS,CAAC,CAAC3D,SAAS,CAAC4D,KAAK,CAAC,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,EAAE5D,SAAS,CAAC0D,MAAM,CAAC,CAAC;EAC7J;AACF;AACA;AACA;EACEnB,SAAS,EAAEvC,SAAS,CAACiD,WAAW;EAChC;AACF;AACA;AACA;EACEN,SAAS,EAAE3C,SAAS,CAAC6D,KAAK,CAAC;IACzB3C,IAAI,EAAElB,SAAS,CAAC2D,SAAS,CAAC,CAAC3D,SAAS,CAAC8D,IAAI,EAAE9D,SAAS,CAAC+D,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;EACE9C,KAAK,EAAEjB,SAAS,CAAC6D,KAAK,CAAC;IACrB3C,IAAI,EAAElB,SAAS,CAACiD;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACEe,EAAE,EAAEhE,SAAS,CAAC2D,SAAS,CAAC,CAAC3D,SAAS,CAACiE,OAAO,CAACjE,SAAS,CAAC2D,SAAS,CAAC,CAAC3D,SAAS,CAAC8D,IAAI,EAAE9D,SAAS,CAAC+D,MAAM,EAAE/D,SAAS,CAACkE,IAAI,CAAC,CAAC,CAAC,EAAElE,SAAS,CAAC8D,IAAI,EAAE9D,SAAS,CAAC+D,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;EACE/C,OAAO,EAAEhB,SAAS,CAAC,sCAAsC2D,SAAS,CAAC,CAAC3D,SAAS,CAAC4D,KAAK,CAAC,CAAC,WAAW,EAAE,OAAO,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC,EAAE5D,SAAS,CAAC0D,MAAM,CAAC;AACpJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAexB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}