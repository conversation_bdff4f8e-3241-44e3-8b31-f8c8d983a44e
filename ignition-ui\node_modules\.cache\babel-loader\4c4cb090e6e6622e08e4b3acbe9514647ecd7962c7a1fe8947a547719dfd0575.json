{"ast": null, "code": "'use strict';\n\nvar keysShim;\nif (!Object.keys) {\n  // modified from https://github.com/es-shims/es5-shim\n  var has = Object.prototype.hasOwnProperty;\n  var toStr = Object.prototype.toString;\n  var isArgs = require('./isArguments'); // eslint-disable-line global-require\n  var isEnumerable = Object.prototype.propertyIsEnumerable;\n  var hasDontEnumBug = !isEnumerable.call({\n    toString: null\n  }, 'toString');\n  var hasProtoEnumBug = isEnumerable.call(function () {}, 'prototype');\n  var dontEnums = ['toString', 'toLocaleString', 'valueOf', 'hasOwnProperty', 'isPrototypeOf', 'propertyIsEnumerable', 'constructor'];\n  var equalsConstructorPrototype = function (o) {\n    var ctor = o.constructor;\n    return ctor && ctor.prototype === o;\n  };\n  var excludedKeys = {\n    $applicationCache: true,\n    $console: true,\n    $external: true,\n    $frame: true,\n    $frameElement: true,\n    $frames: true,\n    $innerHeight: true,\n    $innerWidth: true,\n    $onmozfullscreenchange: true,\n    $onmozfullscreenerror: true,\n    $outerHeight: true,\n    $outerWidth: true,\n    $pageXOffset: true,\n    $pageYOffset: true,\n    $parent: true,\n    $scrollLeft: true,\n    $scrollTop: true,\n    $scrollX: true,\n    $scrollY: true,\n    $self: true,\n    $webkitIndexedDB: true,\n    $webkitStorageInfo: true,\n    $window: true\n  };\n  var hasAutomationEqualityBug = function () {\n    /* global window */\n    if (typeof window === 'undefined') {\n      return false;\n    }\n    for (var k in window) {\n      try {\n        if (!excludedKeys['$' + k] && has.call(window, k) && window[k] !== null && typeof window[k] === 'object') {\n          try {\n            equalsConstructorPrototype(window[k]);\n          } catch (e) {\n            return true;\n          }\n        }\n      } catch (e) {\n        return true;\n      }\n    }\n    return false;\n  }();\n  var equalsConstructorPrototypeIfNotBuggy = function (o) {\n    /* global window */\n    if (typeof window === 'undefined' || !hasAutomationEqualityBug) {\n      return equalsConstructorPrototype(o);\n    }\n    try {\n      return equalsConstructorPrototype(o);\n    } catch (e) {\n      return false;\n    }\n  };\n  keysShim = function keys(object) {\n    var isObject = object !== null && typeof object === 'object';\n    var isFunction = toStr.call(object) === '[object Function]';\n    var isArguments = isArgs(object);\n    var isString = isObject && toStr.call(object) === '[object String]';\n    var theKeys = [];\n    if (!isObject && !isFunction && !isArguments) {\n      throw new TypeError('Object.keys called on a non-object');\n    }\n    var skipProto = hasProtoEnumBug && isFunction;\n    if (isString && object.length > 0 && !has.call(object, 0)) {\n      for (var i = 0; i < object.length; ++i) {\n        theKeys.push(String(i));\n      }\n    }\n    if (isArguments && object.length > 0) {\n      for (var j = 0; j < object.length; ++j) {\n        theKeys.push(String(j));\n      }\n    } else {\n      for (var name in object) {\n        if (!(skipProto && name === 'prototype') && has.call(object, name)) {\n          theKeys.push(String(name));\n        }\n      }\n    }\n    if (hasDontEnumBug) {\n      var skipConstructor = equalsConstructorPrototypeIfNotBuggy(object);\n      for (var k = 0; k < dontEnums.length; ++k) {\n        if (!(skipConstructor && dontEnums[k] === 'constructor') && has.call(object, dontEnums[k])) {\n          theKeys.push(dontEnums[k]);\n        }\n      }\n    }\n    return theKeys;\n  };\n}\nmodule.exports = keysShim;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON>", "Object", "keys", "has", "prototype", "hasOwnProperty", "toStr", "toString", "is<PERSON><PERSON><PERSON>", "require", "isEnumerable", "propertyIsEnumerable", "hasDontEnumBug", "call", "hasProtoEnumBug", "dontEnums", "equalsConstructorPrototype", "o", "ctor", "constructor", "<PERSON><PERSON><PERSON><PERSON>", "$applicationCache", "$console", "$external", "$frame", "$frameElement", "$frames", "$innerHeight", "$innerWidth", "$onmozfullscreenchange", "$onmozfullscreenerror", "$outerHeight", "$outerWidth", "$pageXOffset", "$pageYOffset", "$parent", "$scrollLeft", "$scrollTop", "$scrollX", "$scrollY", "$self", "$webkitIndexedDB", "$webkitStorageInfo", "$window", "hasAutomationEqualityBug", "window", "k", "e", "equalsConstructorPrototypeIfNotBuggy", "object", "isObject", "isFunction", "isArguments", "isString", "theKeys", "TypeError", "<PERSON><PERSON><PERSON><PERSON>", "length", "i", "push", "String", "j", "name", "skipConstructor", "module", "exports"], "sources": ["C:/ignition/ignition-ui/node_modules/object-keys/implementation.js"], "sourcesContent": ["'use strict';\n\nvar keysShim;\nif (!Object.keys) {\n\t// modified from https://github.com/es-shims/es5-shim\n\tvar has = Object.prototype.hasOwnProperty;\n\tvar toStr = Object.prototype.toString;\n\tvar isArgs = require('./isArguments'); // eslint-disable-line global-require\n\tvar isEnumerable = Object.prototype.propertyIsEnumerable;\n\tvar hasDontEnumBug = !isEnumerable.call({ toString: null }, 'toString');\n\tvar hasProtoEnumBug = isEnumerable.call(function () {}, 'prototype');\n\tvar dontEnums = [\n\t\t'toString',\n\t\t'toLocaleString',\n\t\t'valueOf',\n\t\t'hasOwnProperty',\n\t\t'isPrototypeOf',\n\t\t'propertyIsEnumerable',\n\t\t'constructor'\n\t];\n\tvar equalsConstructorPrototype = function (o) {\n\t\tvar ctor = o.constructor;\n\t\treturn ctor && ctor.prototype === o;\n\t};\n\tvar excludedKeys = {\n\t\t$applicationCache: true,\n\t\t$console: true,\n\t\t$external: true,\n\t\t$frame: true,\n\t\t$frameElement: true,\n\t\t$frames: true,\n\t\t$innerHeight: true,\n\t\t$innerWidth: true,\n\t\t$onmozfullscreenchange: true,\n\t\t$onmozfullscreenerror: true,\n\t\t$outerHeight: true,\n\t\t$outerWidth: true,\n\t\t$pageXOffset: true,\n\t\t$pageYOffset: true,\n\t\t$parent: true,\n\t\t$scrollLeft: true,\n\t\t$scrollTop: true,\n\t\t$scrollX: true,\n\t\t$scrollY: true,\n\t\t$self: true,\n\t\t$webkitIndexedDB: true,\n\t\t$webkitStorageInfo: true,\n\t\t$window: true\n\t};\n\tvar hasAutomationEqualityBug = (function () {\n\t\t/* global window */\n\t\tif (typeof window === 'undefined') { return false; }\n\t\tfor (var k in window) {\n\t\t\ttry {\n\t\t\t\tif (!excludedKeys['$' + k] && has.call(window, k) && window[k] !== null && typeof window[k] === 'object') {\n\t\t\t\t\ttry {\n\t\t\t\t\t\tequalsConstructorPrototype(window[k]);\n\t\t\t\t\t} catch (e) {\n\t\t\t\t\t\treturn true;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} catch (e) {\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\t\treturn false;\n\t}());\n\tvar equalsConstructorPrototypeIfNotBuggy = function (o) {\n\t\t/* global window */\n\t\tif (typeof window === 'undefined' || !hasAutomationEqualityBug) {\n\t\t\treturn equalsConstructorPrototype(o);\n\t\t}\n\t\ttry {\n\t\t\treturn equalsConstructorPrototype(o);\n\t\t} catch (e) {\n\t\t\treturn false;\n\t\t}\n\t};\n\n\tkeysShim = function keys(object) {\n\t\tvar isObject = object !== null && typeof object === 'object';\n\t\tvar isFunction = toStr.call(object) === '[object Function]';\n\t\tvar isArguments = isArgs(object);\n\t\tvar isString = isObject && toStr.call(object) === '[object String]';\n\t\tvar theKeys = [];\n\n\t\tif (!isObject && !isFunction && !isArguments) {\n\t\t\tthrow new TypeError('Object.keys called on a non-object');\n\t\t}\n\n\t\tvar skipProto = hasProtoEnumBug && isFunction;\n\t\tif (isString && object.length > 0 && !has.call(object, 0)) {\n\t\t\tfor (var i = 0; i < object.length; ++i) {\n\t\t\t\ttheKeys.push(String(i));\n\t\t\t}\n\t\t}\n\n\t\tif (isArguments && object.length > 0) {\n\t\t\tfor (var j = 0; j < object.length; ++j) {\n\t\t\t\ttheKeys.push(String(j));\n\t\t\t}\n\t\t} else {\n\t\t\tfor (var name in object) {\n\t\t\t\tif (!(skipProto && name === 'prototype') && has.call(object, name)) {\n\t\t\t\t\ttheKeys.push(String(name));\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (hasDontEnumBug) {\n\t\t\tvar skipConstructor = equalsConstructorPrototypeIfNotBuggy(object);\n\n\t\t\tfor (var k = 0; k < dontEnums.length; ++k) {\n\t\t\t\tif (!(skipConstructor && dontEnums[k] === 'constructor') && has.call(object, dontEnums[k])) {\n\t\t\t\t\ttheKeys.push(dontEnums[k]);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn theKeys;\n\t};\n}\nmodule.exports = keysShim;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,QAAQ;AACZ,IAAI,CAACC,MAAM,CAACC,IAAI,EAAE;EACjB;EACA,IAAIC,GAAG,GAAGF,MAAM,CAACG,SAAS,CAACC,cAAc;EACzC,IAAIC,KAAK,GAAGL,MAAM,CAACG,SAAS,CAACG,QAAQ;EACrC,IAAIC,MAAM,GAAGC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC;EACvC,IAAIC,YAAY,GAAGT,MAAM,CAACG,SAAS,CAACO,oBAAoB;EACxD,IAAIC,cAAc,GAAG,CAACF,YAAY,CAACG,IAAI,CAAC;IAAEN,QAAQ,EAAE;EAAK,CAAC,EAAE,UAAU,CAAC;EACvE,IAAIO,eAAe,GAAGJ,YAAY,CAACG,IAAI,CAAC,YAAY,CAAC,CAAC,EAAE,WAAW,CAAC;EACpE,IAAIE,SAAS,GAAG,CACf,UAAU,EACV,gBAAgB,EAChB,SAAS,EACT,gBAAgB,EAChB,eAAe,EACf,sBAAsB,EACtB,aAAa,CACb;EACD,IAAIC,0BAA0B,GAAG,SAAAA,CAAUC,CAAC,EAAE;IAC7C,IAAIC,IAAI,GAAGD,CAAC,CAACE,WAAW;IACxB,OAAOD,IAAI,IAAIA,IAAI,CAACd,SAAS,KAAKa,CAAC;EACpC,CAAC;EACD,IAAIG,YAAY,GAAG;IAClBC,iBAAiB,EAAE,IAAI;IACvBC,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAE,IAAI;IACfC,MAAM,EAAE,IAAI;IACZC,aAAa,EAAE,IAAI;IACnBC,OAAO,EAAE,IAAI;IACbC,YAAY,EAAE,IAAI;IAClBC,WAAW,EAAE,IAAI;IACjBC,sBAAsB,EAAE,IAAI;IAC5BC,qBAAqB,EAAE,IAAI;IAC3BC,YAAY,EAAE,IAAI;IAClBC,WAAW,EAAE,IAAI;IACjBC,YAAY,EAAE,IAAI;IAClBC,YAAY,EAAE,IAAI;IAClBC,OAAO,EAAE,IAAI;IACbC,WAAW,EAAE,IAAI;IACjBC,UAAU,EAAE,IAAI;IAChBC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,IAAI;IACdC,KAAK,EAAE,IAAI;IACXC,gBAAgB,EAAE,IAAI;IACtBC,kBAAkB,EAAE,IAAI;IACxBC,OAAO,EAAE;EACV,CAAC;EACD,IAAIC,wBAAwB,GAAI,YAAY;IAC3C;IACA,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;MAAE,OAAO,KAAK;IAAE;IACnD,KAAK,IAAIC,CAAC,IAAID,MAAM,EAAE;MACrB,IAAI;QACH,IAAI,CAACzB,YAAY,CAAC,GAAG,GAAG0B,CAAC,CAAC,IAAI3C,GAAG,CAACU,IAAI,CAACgC,MAAM,EAAEC,CAAC,CAAC,IAAID,MAAM,CAACC,CAAC,CAAC,KAAK,IAAI,IAAI,OAAOD,MAAM,CAACC,CAAC,CAAC,KAAK,QAAQ,EAAE;UACzG,IAAI;YACH9B,0BAA0B,CAAC6B,MAAM,CAACC,CAAC,CAAC,CAAC;UACtC,CAAC,CAAC,OAAOC,CAAC,EAAE;YACX,OAAO,IAAI;UACZ;QACD;MACD,CAAC,CAAC,OAAOA,CAAC,EAAE;QACX,OAAO,IAAI;MACZ;IACD;IACA,OAAO,KAAK;EACb,CAAC,CAAC,CAAE;EACJ,IAAIC,oCAAoC,GAAG,SAAAA,CAAU/B,CAAC,EAAE;IACvD;IACA,IAAI,OAAO4B,MAAM,KAAK,WAAW,IAAI,CAACD,wBAAwB,EAAE;MAC/D,OAAO5B,0BAA0B,CAACC,CAAC,CAAC;IACrC;IACA,IAAI;MACH,OAAOD,0BAA0B,CAACC,CAAC,CAAC;IACrC,CAAC,CAAC,OAAO8B,CAAC,EAAE;MACX,OAAO,KAAK;IACb;EACD,CAAC;EAED/C,QAAQ,GAAG,SAASE,IAAIA,CAAC+C,MAAM,EAAE;IAChC,IAAIC,QAAQ,GAAGD,MAAM,KAAK,IAAI,IAAI,OAAOA,MAAM,KAAK,QAAQ;IAC5D,IAAIE,UAAU,GAAG7C,KAAK,CAACO,IAAI,CAACoC,MAAM,CAAC,KAAK,mBAAmB;IAC3D,IAAIG,WAAW,GAAG5C,MAAM,CAACyC,MAAM,CAAC;IAChC,IAAII,QAAQ,GAAGH,QAAQ,IAAI5C,KAAK,CAACO,IAAI,CAACoC,MAAM,CAAC,KAAK,iBAAiB;IACnE,IAAIK,OAAO,GAAG,EAAE;IAEhB,IAAI,CAACJ,QAAQ,IAAI,CAACC,UAAU,IAAI,CAACC,WAAW,EAAE;MAC7C,MAAM,IAAIG,SAAS,CAAC,oCAAoC,CAAC;IAC1D;IAEA,IAAIC,SAAS,GAAG1C,eAAe,IAAIqC,UAAU;IAC7C,IAAIE,QAAQ,IAAIJ,MAAM,CAACQ,MAAM,GAAG,CAAC,IAAI,CAACtD,GAAG,CAACU,IAAI,CAACoC,MAAM,EAAE,CAAC,CAAC,EAAE;MAC1D,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,MAAM,CAACQ,MAAM,EAAE,EAAEC,CAAC,EAAE;QACvCJ,OAAO,CAACK,IAAI,CAACC,MAAM,CAACF,CAAC,CAAC,CAAC;MACxB;IACD;IAEA,IAAIN,WAAW,IAAIH,MAAM,CAACQ,MAAM,GAAG,CAAC,EAAE;MACrC,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,MAAM,CAACQ,MAAM,EAAE,EAAEI,CAAC,EAAE;QACvCP,OAAO,CAACK,IAAI,CAACC,MAAM,CAACC,CAAC,CAAC,CAAC;MACxB;IACD,CAAC,MAAM;MACN,KAAK,IAAIC,IAAI,IAAIb,MAAM,EAAE;QACxB,IAAI,EAAEO,SAAS,IAAIM,IAAI,KAAK,WAAW,CAAC,IAAI3D,GAAG,CAACU,IAAI,CAACoC,MAAM,EAAEa,IAAI,CAAC,EAAE;UACnER,OAAO,CAACK,IAAI,CAACC,MAAM,CAACE,IAAI,CAAC,CAAC;QAC3B;MACD;IACD;IAEA,IAAIlD,cAAc,EAAE;MACnB,IAAImD,eAAe,GAAGf,oCAAoC,CAACC,MAAM,CAAC;MAElE,KAAK,IAAIH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/B,SAAS,CAAC0C,MAAM,EAAE,EAAEX,CAAC,EAAE;QAC1C,IAAI,EAAEiB,eAAe,IAAIhD,SAAS,CAAC+B,CAAC,CAAC,KAAK,aAAa,CAAC,IAAI3C,GAAG,CAACU,IAAI,CAACoC,MAAM,EAAElC,SAAS,CAAC+B,CAAC,CAAC,CAAC,EAAE;UAC3FQ,OAAO,CAACK,IAAI,CAAC5C,SAAS,CAAC+B,CAAC,CAAC,CAAC;QAC3B;MACD;IACD;IACA,OAAOQ,OAAO;EACf,CAAC;AACF;AACAU,MAAM,CAACC,OAAO,GAAGjE,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}