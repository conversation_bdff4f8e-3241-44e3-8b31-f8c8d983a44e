{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"component\", \"className\", \"children\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport { styled, useThemeProps } from '../styles';\nimport { getListItemDecoratorUtilityClass } from './listItemDecoratorClasses';\nimport ListItemButtonOrientationContext from '../ListItemButton/ListItemButtonOrientationContext';\nimport useSlot from '../utils/useSlot';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = () => {\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getListItemDecoratorUtilityClass, {});\n};\nconst ListItemDecoratorRoot = styled('span', {\n  name: 'JoyListItemDecorator',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(_ref => {\n  let {\n    ownerState\n  } = _ref;\n  return _extends({\n    boxSizing: 'border-box',\n    display: 'inline-flex',\n    alignItems: `var(--unstable_ListItemDecorator-alignItems, ${ownerState.parentOrientation === 'horizontal' ? 'center' : 'initial'})`\n  }, ownerState.parentOrientation === 'horizontal' ? {\n    minInlineSize: 'var(--ListItemDecorator-size)',\n    marginInlineEnd: 'calc(-1 * var(--ListItem-gap))'\n  } : {\n    minBlockSize: 'var(--ListItemDecorator-size)',\n    justifyContent: 'center',\n    marginBlockEnd: 'calc(-1 * var(--ListItem-gap))'\n  });\n});\n/**\n *\n * Demos:\n *\n * - [Lists](https://mui.com/joy-ui/react-list/)\n *\n * API:\n *\n * - [ListItemDecorator API](https://mui.com/joy-ui/api/list-item-decorator/)\n */\nconst ListItemDecorator = /*#__PURE__*/React.forwardRef(function ListItemDecorator(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyListItemDecorator'\n  });\n  const {\n      component,\n      className,\n      children,\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const parentOrientation = React.useContext(ListItemButtonOrientationContext);\n  const ownerState = _extends({\n    parentOrientation\n  }, props);\n  const classes = useUtilityClasses();\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    className: clsx(classes.root, className),\n    elementType: ListItemDecoratorRoot,\n    externalForwardedProps,\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(SlotRoot, _extends({}, rootProps, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? ListItemDecorator.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default ListItemDecorator;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "unstable_composeClasses", "composeClasses", "styled", "useThemeProps", "getListItemDecoratorUtilityClass", "ListItemButtonOrientationContext", "useSlot", "jsx", "_jsx", "useUtilityClasses", "slots", "root", "ListItemDecoratorRoot", "name", "slot", "overridesResolver", "props", "styles", "_ref", "ownerState", "boxSizing", "display", "alignItems", "parentOrientation", "minInlineSize", "marginInlineEnd", "minBlockSize", "justifyContent", "marginBlockEnd", "ListItemDecorator", "forwardRef", "inProps", "ref", "component", "className", "children", "slotProps", "other", "useContext", "classes", "externalForwardedProps", "SlotRoot", "rootProps", "elementType", "process", "env", "NODE_ENV", "propTypes", "node", "string", "shape", "oneOfType", "func", "object", "sx", "arrayOf", "bool"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/ListItemDecorator/ListItemDecorator.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"component\", \"className\", \"children\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport { styled, useThemeProps } from '../styles';\nimport { getListItemDecoratorUtilityClass } from './listItemDecoratorClasses';\nimport ListItemButtonOrientationContext from '../ListItemButton/ListItemButtonOrientationContext';\nimport useSlot from '../utils/useSlot';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = () => {\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getListItemDecoratorUtilityClass, {});\n};\nconst ListItemDecoratorRoot = styled('span', {\n  name: 'JoyListItemDecorator',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(({\n  ownerState\n}) => _extends({\n  boxSizing: 'border-box',\n  display: 'inline-flex',\n  alignItems: `var(--unstable_ListItemDecorator-alignItems, ${ownerState.parentOrientation === 'horizontal' ? 'center' : 'initial'})`\n}, ownerState.parentOrientation === 'horizontal' ? {\n  minInlineSize: 'var(--ListItemDecorator-size)',\n  marginInlineEnd: 'calc(-1 * var(--ListItem-gap))'\n} : {\n  minBlockSize: 'var(--ListItemDecorator-size)',\n  justifyContent: 'center',\n  marginBlockEnd: 'calc(-1 * var(--ListItem-gap))'\n}));\n/**\n *\n * Demos:\n *\n * - [Lists](https://mui.com/joy-ui/react-list/)\n *\n * API:\n *\n * - [ListItemDecorator API](https://mui.com/joy-ui/api/list-item-decorator/)\n */\nconst ListItemDecorator = /*#__PURE__*/React.forwardRef(function ListItemDecorator(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyListItemDecorator'\n  });\n  const {\n      component,\n      className,\n      children,\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const parentOrientation = React.useContext(ListItemButtonOrientationContext);\n  const ownerState = _extends({\n    parentOrientation\n  }, props);\n  const classes = useUtilityClasses();\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    className: clsx(classes.root, className),\n    elementType: ListItemDecoratorRoot,\n    externalForwardedProps,\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(SlotRoot, _extends({}, rootProps, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? ListItemDecorator.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default ListItemDecorator;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,CAAC;AAC9E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,0BAA0B;AACpF,SAASC,MAAM,EAAEC,aAAa,QAAQ,WAAW;AACjD,SAASC,gCAAgC,QAAQ,4BAA4B;AAC7E,OAAOC,gCAAgC,MAAM,oDAAoD;AACjG,OAAOC,OAAO,MAAM,kBAAkB;AACtC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAC9B,MAAMC,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,OAAOV,cAAc,CAACS,KAAK,EAAEN,gCAAgC,EAAE,CAAC,CAAC,CAAC;AACpE,CAAC;AACD,MAAMQ,qBAAqB,GAAGV,MAAM,CAAC,MAAM,EAAE;EAC3CW,IAAI,EAAE,sBAAsB;EAC5BC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC/C,CAAC,CAAC,CAACO,IAAA;EAAA,IAAC;IACFC;EACF,CAAC,GAAAD,IAAA;EAAA,OAAKvB,QAAQ,CAAC;IACbyB,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,aAAa;IACtBC,UAAU,EAAE,gDAAgDH,UAAU,CAACI,iBAAiB,KAAK,YAAY,GAAG,QAAQ,GAAG,SAAS;EAClI,CAAC,EAAEJ,UAAU,CAACI,iBAAiB,KAAK,YAAY,GAAG;IACjDC,aAAa,EAAE,+BAA+B;IAC9CC,eAAe,EAAE;EACnB,CAAC,GAAG;IACFC,YAAY,EAAE,+BAA+B;IAC7CC,cAAc,EAAE,QAAQ;IACxBC,cAAc,EAAE;EAClB,CAAC,CAAC;AAAA,EAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,GAAG,aAAahC,KAAK,CAACiC,UAAU,CAAC,SAASD,iBAAiBA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC/F,MAAMhB,KAAK,GAAGb,aAAa,CAAC;IAC1Ba,KAAK,EAAEe,OAAO;IACdlB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFoB,SAAS;MACTC,SAAS;MACTC,QAAQ;MACRzB,KAAK,GAAG,CAAC,CAAC;MACV0B,SAAS,GAAG,CAAC;IACf,CAAC,GAAGpB,KAAK;IACTqB,KAAK,GAAG3C,6BAA6B,CAACsB,KAAK,EAAEpB,SAAS,CAAC;EACzD,MAAM2B,iBAAiB,GAAG1B,KAAK,CAACyC,UAAU,CAACjC,gCAAgC,CAAC;EAC5E,MAAMc,UAAU,GAAGxB,QAAQ,CAAC;IAC1B4B;EACF,CAAC,EAAEP,KAAK,CAAC;EACT,MAAMuB,OAAO,GAAG9B,iBAAiB,CAAC,CAAC;EACnC,MAAM+B,sBAAsB,GAAG7C,QAAQ,CAAC,CAAC,CAAC,EAAE0C,KAAK,EAAE;IACjDJ,SAAS;IACTvB,KAAK;IACL0B;EACF,CAAC,CAAC;EACF,MAAM,CAACK,QAAQ,EAAEC,SAAS,CAAC,GAAGpC,OAAO,CAAC,MAAM,EAAE;IAC5C0B,GAAG;IACHE,SAAS,EAAEnC,IAAI,CAACwC,OAAO,CAAC5B,IAAI,EAAEuB,SAAS,CAAC;IACxCS,WAAW,EAAE/B,qBAAqB;IAClC4B,sBAAsB;IACtBrB;EACF,CAAC,CAAC;EACF,OAAO,aAAaX,IAAI,CAACiC,QAAQ,EAAE9C,QAAQ,CAAC,CAAC,CAAC,EAAE+C,SAAS,EAAE;IACzDP,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFS,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGjB,iBAAiB,CAACkB,SAAS,CAAC,yBAAyB;EAC3F;EACA;EACA;EACA;EACA;AACF;AACA;EACEZ,QAAQ,EAAErC,SAAS,CAACkD,IAAI;EACxB;AACF;AACA;EACEd,SAAS,EAAEpC,SAAS,CAACmD,MAAM;EAC3B;AACF;AACA;AACA;EACEhB,SAAS,EAAEnC,SAAS,CAAC6C,WAAW;EAChC;AACF;AACA;AACA;EACEP,SAAS,EAAEtC,SAAS,CAACoD,KAAK,CAAC;IACzBvC,IAAI,EAAEb,SAAS,CAACqD,SAAS,CAAC,CAACrD,SAAS,CAACsD,IAAI,EAAEtD,SAAS,CAACuD,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;EACE3C,KAAK,EAAEZ,SAAS,CAACoD,KAAK,CAAC;IACrBvC,IAAI,EAAEb,SAAS,CAAC6C;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACEW,EAAE,EAAExD,SAAS,CAACqD,SAAS,CAAC,CAACrD,SAAS,CAACyD,OAAO,CAACzD,SAAS,CAACqD,SAAS,CAAC,CAACrD,SAAS,CAACsD,IAAI,EAAEtD,SAAS,CAACuD,MAAM,EAAEvD,SAAS,CAAC0D,IAAI,CAAC,CAAC,CAAC,EAAE1D,SAAS,CAACsD,IAAI,EAAEtD,SAAS,CAACuD,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAexB,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}