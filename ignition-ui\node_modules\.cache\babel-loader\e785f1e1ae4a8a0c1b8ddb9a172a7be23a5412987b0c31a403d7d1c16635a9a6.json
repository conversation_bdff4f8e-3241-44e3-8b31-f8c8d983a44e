{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/extends\";\nimport _inheritsLoose from \"@babel/runtime/helpers/inheritsLoose\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/assertThisInitialized\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport * as React from 'react';\nimport warning from 'warning';\nimport { ManagerReferenceNodeSetterContext } from './Manager';\nimport { safeInvoke, unwrapArray, setRef } from './utils';\nvar InnerReference = /*#__PURE__*/\nfunction (_React$Component) {\n  _inheritsLoose(InnerReference, _React$Component);\n  function InnerReference() {\n    var _this;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"refHandler\", function (node) {\n      setRef(_this.props.innerRef, node);\n      safeInvoke(_this.props.setReferenceNode, node);\n    });\n    return _this;\n  }\n  var _proto = InnerReference.prototype;\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    setRef(this.props.innerRef, null);\n  };\n  _proto.render = function render() {\n    warning(Boolean(this.props.setReferenceNode), '`Reference` should not be used outside of a `Manager` component.');\n    return unwrapArray(this.props.children)({\n      ref: this.refHandler\n    });\n  };\n  return InnerReference;\n}(React.Component);\nexport default function Reference(props) {\n  return React.createElement(ManagerReferenceNodeSetterContext.Consumer, null, function (setReferenceNode) {\n    return React.createElement(InnerReference, _extends({\n      setReferenceNode: setReferenceNode\n    }, props));\n  });\n}", "map": {"version": 3, "names": ["_extends", "_inherits<PERSON><PERSON>e", "_assertThisInitialized", "_defineProperty", "React", "warning", "ManagerReferenceNodeSetterContext", "safeInvoke", "unwrapArray", "setRef", "InnerReference", "_React$Component", "_this", "_len", "arguments", "length", "args", "Array", "_key", "call", "apply", "concat", "node", "props", "innerRef", "setReferenceNode", "_proto", "prototype", "componentWillUnmount", "render", "Boolean", "children", "ref", "ref<PERSON><PERSON><PERSON>", "Component", "Reference", "createElement", "Consumer"], "sources": ["C:/ignition/ignition-ui/node_modules/react-popper/lib/esm/Reference.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/extends\";\nimport _inheritsLoose from \"@babel/runtime/helpers/inheritsLoose\";\nimport _assertThisInitialized from \"@babel/runtime/helpers/assertThisInitialized\";\nimport _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nimport * as React from 'react';\nimport warning from 'warning';\nimport { ManagerReferenceNodeSetterContext } from './Manager';\nimport { safeInvoke, unwrapArray, setRef } from './utils';\n\nvar InnerReference =\n/*#__PURE__*/\nfunction (_React$Component) {\n  _inheritsLoose(InnerReference, _React$Component);\n\n  function InnerReference() {\n    var _this;\n\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n\n    _this = _React$Component.call.apply(_React$Component, [this].concat(args)) || this;\n\n    _defineProperty(_assertThisInitialized(_assertThisInitialized(_this)), \"refHandler\", function (node) {\n      setRef(_this.props.innerRef, node);\n      safeInvoke(_this.props.setReferenceNode, node);\n    });\n\n    return _this;\n  }\n\n  var _proto = InnerReference.prototype;\n\n  _proto.componentWillUnmount = function componentWillUnmount() {\n    setRef(this.props.innerRef, null);\n  };\n\n  _proto.render = function render() {\n    warning(Boolean(this.props.setReferenceNode), '`Reference` should not be used outside of a `Manager` component.');\n    return unwrapArray(this.props.children)({\n      ref: this.refHandler\n    });\n  };\n\n  return InnerReference;\n}(React.Component);\n\nexport default function Reference(props) {\n  return React.createElement(ManagerReferenceNodeSetterContext.Consumer, null, function (setReferenceNode) {\n    return React.createElement(InnerReference, _extends({\n      setReferenceNode: setReferenceNode\n    }, props));\n  });\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,gCAAgC;AACrD,OAAOC,cAAc,MAAM,sCAAsC;AACjE,OAAOC,sBAAsB,MAAM,8CAA8C;AACjF,OAAOC,eAAe,MAAM,uCAAuC;AACnE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,OAAO,MAAM,SAAS;AAC7B,SAASC,iCAAiC,QAAQ,WAAW;AAC7D,SAASC,UAAU,EAAEC,WAAW,EAAEC,MAAM,QAAQ,SAAS;AAEzD,IAAIC,cAAc,GAClB;AACA,UAAUC,gBAAgB,EAAE;EAC1BV,cAAc,CAACS,cAAc,EAAEC,gBAAgB,CAAC;EAEhD,SAASD,cAAcA,CAAA,EAAG;IACxB,IAAIE,KAAK;IAET,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC9B;IAEAN,KAAK,GAAGD,gBAAgB,CAACQ,IAAI,CAACC,KAAK,CAACT,gBAAgB,EAAE,CAAC,IAAI,CAAC,CAACU,MAAM,CAACL,IAAI,CAAC,CAAC,IAAI,IAAI;IAElFb,eAAe,CAACD,sBAAsB,CAACA,sBAAsB,CAACU,KAAK,CAAC,CAAC,EAAE,YAAY,EAAE,UAAUU,IAAI,EAAE;MACnGb,MAAM,CAACG,KAAK,CAACW,KAAK,CAACC,QAAQ,EAAEF,IAAI,CAAC;MAClCf,UAAU,CAACK,KAAK,CAACW,KAAK,CAACE,gBAAgB,EAAEH,IAAI,CAAC;IAChD,CAAC,CAAC;IAEF,OAAOV,KAAK;EACd;EAEA,IAAIc,MAAM,GAAGhB,cAAc,CAACiB,SAAS;EAErCD,MAAM,CAACE,oBAAoB,GAAG,SAASA,oBAAoBA,CAAA,EAAG;IAC5DnB,MAAM,CAAC,IAAI,CAACc,KAAK,CAACC,QAAQ,EAAE,IAAI,CAAC;EACnC,CAAC;EAEDE,MAAM,CAACG,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;IAChCxB,OAAO,CAACyB,OAAO,CAAC,IAAI,CAACP,KAAK,CAACE,gBAAgB,CAAC,EAAE,kEAAkE,CAAC;IACjH,OAAOjB,WAAW,CAAC,IAAI,CAACe,KAAK,CAACQ,QAAQ,CAAC,CAAC;MACtCC,GAAG,EAAE,IAAI,CAACC;IACZ,CAAC,CAAC;EACJ,CAAC;EAED,OAAOvB,cAAc;AACvB,CAAC,CAACN,KAAK,CAAC8B,SAAS,CAAC;AAElB,eAAe,SAASC,SAASA,CAACZ,KAAK,EAAE;EACvC,OAAOnB,KAAK,CAACgC,aAAa,CAAC9B,iCAAiC,CAAC+B,QAAQ,EAAE,IAAI,EAAE,UAAUZ,gBAAgB,EAAE;IACvG,OAAOrB,KAAK,CAACgC,aAAa,CAAC1B,cAAc,EAAEV,QAAQ,CAAC;MAClDyB,gBAAgB,EAAEA;IACpB,CAAC,EAAEF,KAAK,CAAC,CAAC;EACZ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}