{"ast": null, "code": "// @flow\n'use strict';\n\nvar key = '__global_unique_id__';\nmodule.exports = function () {\n  return global[key] = (global[key] || 0) + 1;\n};", "map": {"version": 3, "names": ["key", "module", "exports", "global"], "sources": ["C:/ignition/ignition-ui/node_modules/gud/index.js"], "sourcesContent": ["// @flow\n'use strict';\n\nvar key = '__global_unique_id__';\n\nmodule.exports = function() {\n  return global[key] = (global[key] || 0) + 1;\n};\n"], "mappings": "AAAA;AACA,YAAY;;AAEZ,IAAIA,GAAG,GAAG,sBAAsB;AAEhCC,MAAM,CAACC,OAAO,GAAG,YAAW;EAC1B,OAAOC,MAAM,CAACH,GAAG,CAAC,GAAG,CAACG,MAAM,CAACH,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;AAC7C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}