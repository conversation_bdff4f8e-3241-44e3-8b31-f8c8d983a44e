{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { unstable_defaultSxConfig } from '@mui/system';\nconst sxConfig = _extends({}, unstable_defaultSxConfig, {\n  // The default system themeKey is shape\n  borderRadius: {\n    themeKey: 'radius'\n  },\n  // The default system themeKey is shadows\n  boxShadow: {\n    themeKey: 'shadow'\n  },\n  // The default system themeKey is typography\n  fontFamily: {\n    themeKey: 'fontFamily'\n  },\n  // The default system themeKey is typography\n  fontSize: {\n    themeKey: 'fontSize'\n  },\n  // The default system themeKey is typography\n  fontWeight: {\n    themeKey: 'fontWeight'\n  },\n  // The default system themeKey is typography\n  letterSpacing: {\n    themeKey: 'letterSpacing'\n  },\n  // The default system themeKey is typography\n  lineHeight: {\n    themeKey: 'lineHeight'\n  }\n});\nexport default sxConfig;", "map": {"version": 3, "names": ["_extends", "unstable_defaultSxConfig", "sxConfig", "borderRadius", "<PERSON><PERSON><PERSON>", "boxShadow", "fontFamily", "fontSize", "fontWeight", "letterSpacing", "lineHeight"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/styles/sxConfig.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { unstable_defaultSxConfig } from '@mui/system';\nconst sxConfig = _extends({}, unstable_defaultSxConfig, {\n  // The default system themeKey is shape\n  borderRadius: {\n    themeKey: 'radius'\n  },\n  // The default system themeKey is shadows\n  boxShadow: {\n    themeKey: 'shadow'\n  },\n  // The default system themeKey is typography\n  fontFamily: {\n    themeKey: 'fontFamily'\n  },\n  // The default system themeKey is typography\n  fontSize: {\n    themeKey: 'fontSize'\n  },\n  // The default system themeKey is typography\n  fontWeight: {\n    themeKey: 'fontWeight'\n  },\n  // The default system themeKey is typography\n  letterSpacing: {\n    themeKey: 'letterSpacing'\n  },\n  // The default system themeKey is typography\n  lineHeight: {\n    themeKey: 'lineHeight'\n  }\n});\nexport default sxConfig;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,wBAAwB,QAAQ,aAAa;AACtD,MAAMC,QAAQ,GAAGF,QAAQ,CAAC,CAAC,CAAC,EAAEC,wBAAwB,EAAE;EACtD;EACAE,YAAY,EAAE;IACZC,QAAQ,EAAE;EACZ,CAAC;EACD;EACAC,SAAS,EAAE;IACTD,QAAQ,EAAE;EACZ,CAAC;EACD;EACAE,UAAU,EAAE;IACVF,QAAQ,EAAE;EACZ,CAAC;EACD;EACAG,QAAQ,EAAE;IACRH,QAAQ,EAAE;EACZ,CAAC;EACD;EACAI,UAAU,EAAE;IACVJ,QAAQ,EAAE;EACZ,CAAC;EACD;EACAK,aAAa,EAAE;IACbL,QAAQ,EAAE;EACZ,CAAC;EACD;EACAM,UAAU,EAAE;IACVN,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC;AACF,eAAeF,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}