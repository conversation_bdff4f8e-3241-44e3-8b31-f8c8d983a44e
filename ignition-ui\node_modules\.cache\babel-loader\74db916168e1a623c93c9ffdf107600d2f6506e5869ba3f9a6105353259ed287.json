{"ast": null, "code": "import { ReactReduxContext } from '../components/Context';\nimport { useStore as useDefaultStore, createStoreHook } from './useStore';\n/**\r\n * Hook factory, which creates a `useDispatch` hook bound to a given context.\r\n *\r\n * @param {React.Context} [context=ReactReduxContext] Context passed to your `<Provider>`.\r\n * @returns {Function} A `useDispatch` hook bound to the specified context.\r\n */\n\nexport function createDispatchHook(context) {\n  if (context === void 0) {\n    context = ReactReduxContext;\n  }\n  var useStore = context === ReactReduxContext ? useDefaultStore : createStoreHook(context);\n  return function useDispatch() {\n    var store = useStore();\n    return store.dispatch;\n  };\n}\n/**\r\n * A hook to access the redux `dispatch` function.\r\n *\r\n * @returns {any|function} redux store's `dispatch` function\r\n *\r\n * @example\r\n *\r\n * import React, { useCallback } from 'react'\r\n * import { useDispatch } from 'react-redux'\r\n *\r\n * export const CounterComponent = ({ value }) => {\r\n *   const dispatch = useDispatch()\r\n *   const increaseCounter = useCallback(() => dispatch({ type: 'increase-counter' }), [])\r\n *   return (\r\n *     <div>\r\n *       <span>{value}</span>\r\n *       <button onClick={increaseCounter}>Increase counter</button>\r\n *     </div>\r\n *   )\r\n * }\r\n */\n\nexport var useDispatch = /*#__PURE__*/createDispatchHook();", "map": {"version": 3, "names": ["ReactReduxContext", "useStore", "useDefaultStore", "createStoreHook", "createDispatchHook", "context", "useDispatch", "store", "dispatch"], "sources": ["C:/ignition/ignition-ui/node_modules/react-beautiful-dnd/node_modules/react-redux/es/hooks/useDispatch.js"], "sourcesContent": ["import { ReactReduxContext } from '../components/Context';\nimport { useStore as useDefaultStore, createStoreHook } from './useStore';\n/**\r\n * Hook factory, which creates a `useDispatch` hook bound to a given context.\r\n *\r\n * @param {React.Context} [context=ReactReduxContext] Context passed to your `<Provider>`.\r\n * @returns {Function} A `useDispatch` hook bound to the specified context.\r\n */\n\nexport function createDispatchHook(context) {\n  if (context === void 0) {\n    context = ReactReduxContext;\n  }\n\n  var useStore = context === ReactReduxContext ? useDefaultStore : createStoreHook(context);\n  return function useDispatch() {\n    var store = useStore();\n    return store.dispatch;\n  };\n}\n/**\r\n * A hook to access the redux `dispatch` function.\r\n *\r\n * @returns {any|function} redux store's `dispatch` function\r\n *\r\n * @example\r\n *\r\n * import React, { useCallback } from 'react'\r\n * import { useDispatch } from 'react-redux'\r\n *\r\n * export const CounterComponent = ({ value }) => {\r\n *   const dispatch = useDispatch()\r\n *   const increaseCounter = useCallback(() => dispatch({ type: 'increase-counter' }), [])\r\n *   return (\r\n *     <div>\r\n *       <span>{value}</span>\r\n *       <button onClick={increaseCounter}>Increase counter</button>\r\n *     </div>\r\n *   )\r\n * }\r\n */\n\nexport var useDispatch = /*#__PURE__*/createDispatchHook();"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,QAAQ,IAAIC,eAAe,EAAEC,eAAe,QAAQ,YAAY;AACzE;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,kBAAkBA,CAACC,OAAO,EAAE;EAC1C,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAGL,iBAAiB;EAC7B;EAEA,IAAIC,QAAQ,GAAGI,OAAO,KAAKL,iBAAiB,GAAGE,eAAe,GAAGC,eAAe,CAACE,OAAO,CAAC;EACzF,OAAO,SAASC,WAAWA,CAAA,EAAG;IAC5B,IAAIC,KAAK,GAAGN,QAAQ,CAAC,CAAC;IACtB,OAAOM,KAAK,CAACC,QAAQ;EACvB,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,IAAIF,WAAW,GAAG,aAAaF,kBAAkB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}