{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { moveHighlight, listReducer, ListActionTypes, handleItemSelection } from '../useList';\nimport { SelectActionTypes } from './useSelect.types';\nexport function selectReducer(state, action) {\n  const {\n    open\n  } = state;\n  const {\n    context: {\n      selectionMode\n    }\n  } = action;\n  if (action.type === SelectActionTypes.buttonClick) {\n    var _state$selectedValues;\n    const itemToHighlight = (_state$selectedValues = state.selectedValues[0]) != null ? _state$selectedValues : moveHighlight(null, 'start', action.context);\n    return _extends({}, state, {\n      open: !open,\n      highlightedValue: !open ? itemToHighlight : null\n    });\n  }\n  if (action.type === SelectActionTypes.browserAutoFill) {\n    return handleItemSelection(action.item, state, action.context);\n  }\n  const newState = listReducer(state, action);\n  switch (action.type) {\n    case ListActionTypes.keyDown:\n      if (state.open) {\n        if (action.event.key === 'Escape') {\n          return _extends({}, newState, {\n            open: false\n          });\n        }\n      } else {\n        if (action.event.key === 'ArrowDown') {\n          var _state$selectedValues2;\n          return _extends({}, state, {\n            open: true,\n            highlightedValue: (_state$selectedValues2 = state.selectedValues[0]) != null ? _state$selectedValues2 : moveHighlight(null, 'start', action.context)\n          });\n        }\n        if (action.event.key === 'ArrowUp') {\n          var _state$selectedValues3;\n          return _extends({}, state, {\n            open: true,\n            highlightedValue: (_state$selectedValues3 = state.selectedValues[0]) != null ? _state$selectedValues3 : moveHighlight(null, 'end', action.context)\n          });\n        }\n      }\n      break;\n    case ListActionTypes.itemClick:\n      if (selectionMode === 'single') {\n        return _extends({}, newState, {\n          open: false\n        });\n      }\n      break;\n    case ListActionTypes.blur:\n      return _extends({}, newState, {\n        open: false\n      });\n    default:\n      return newState;\n  }\n  return newState;\n}", "map": {"version": 3, "names": ["_extends", "moveHighlight", "listReducer", "ListActionTypes", "handleItemSelection", "SelectActionTypes", "selectReducer", "state", "action", "open", "context", "selectionMode", "type", "buttonClick", "_state$selectedValues", "itemToHighlight", "<PERSON><PERSON><PERSON><PERSON>", "highlightedValue", "browserAutoFill", "item", "newState", "keyDown", "event", "key", "_state$selectedValues2", "_state$selectedValues3", "itemClick", "blur"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/useSelect/selectReducer.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { moveHighlight, listReducer, ListActionTypes, handleItemSelection } from '../useList';\nimport { SelectActionTypes } from './useSelect.types';\nexport function selectReducer(state, action) {\n  const {\n    open\n  } = state;\n  const {\n    context: {\n      selectionMode\n    }\n  } = action;\n  if (action.type === SelectActionTypes.buttonClick) {\n    var _state$selectedValues;\n    const itemToHighlight = (_state$selectedValues = state.selectedValues[0]) != null ? _state$selectedValues : moveHighlight(null, 'start', action.context);\n    return _extends({}, state, {\n      open: !open,\n      highlightedValue: !open ? itemToHighlight : null\n    });\n  }\n  if (action.type === SelectActionTypes.browserAutoFill) {\n    return handleItemSelection(action.item, state, action.context);\n  }\n  const newState = listReducer(state, action);\n  switch (action.type) {\n    case ListActionTypes.keyDown:\n      if (state.open) {\n        if (action.event.key === 'Escape') {\n          return _extends({}, newState, {\n            open: false\n          });\n        }\n      } else {\n        if (action.event.key === 'ArrowDown') {\n          var _state$selectedValues2;\n          return _extends({}, state, {\n            open: true,\n            highlightedValue: (_state$selectedValues2 = state.selectedValues[0]) != null ? _state$selectedValues2 : moveHighlight(null, 'start', action.context)\n          });\n        }\n        if (action.event.key === 'ArrowUp') {\n          var _state$selectedValues3;\n          return _extends({}, state, {\n            open: true,\n            highlightedValue: (_state$selectedValues3 = state.selectedValues[0]) != null ? _state$selectedValues3 : moveHighlight(null, 'end', action.context)\n          });\n        }\n      }\n      break;\n    case ListActionTypes.itemClick:\n      if (selectionMode === 'single') {\n        return _extends({}, newState, {\n          open: false\n        });\n      }\n      break;\n    case ListActionTypes.blur:\n      return _extends({}, newState, {\n        open: false\n      });\n    default:\n      return newState;\n  }\n  return newState;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,aAAa,EAAEC,WAAW,EAAEC,eAAe,EAAEC,mBAAmB,QAAQ,YAAY;AAC7F,SAASC,iBAAiB,QAAQ,mBAAmB;AACrD,OAAO,SAASC,aAAaA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAC3C,MAAM;IACJC;EACF,CAAC,GAAGF,KAAK;EACT,MAAM;IACJG,OAAO,EAAE;MACPC;IACF;EACF,CAAC,GAAGH,MAAM;EACV,IAAIA,MAAM,CAACI,IAAI,KAAKP,iBAAiB,CAACQ,WAAW,EAAE;IACjD,IAAIC,qBAAqB;IACzB,MAAMC,eAAe,GAAG,CAACD,qBAAqB,GAAGP,KAAK,CAACS,cAAc,CAAC,CAAC,CAAC,KAAK,IAAI,GAAGF,qBAAqB,GAAGb,aAAa,CAAC,IAAI,EAAE,OAAO,EAAEO,MAAM,CAACE,OAAO,CAAC;IACxJ,OAAOV,QAAQ,CAAC,CAAC,CAAC,EAAEO,KAAK,EAAE;MACzBE,IAAI,EAAE,CAACA,IAAI;MACXQ,gBAAgB,EAAE,CAACR,IAAI,GAAGM,eAAe,GAAG;IAC9C,CAAC,CAAC;EACJ;EACA,IAAIP,MAAM,CAACI,IAAI,KAAKP,iBAAiB,CAACa,eAAe,EAAE;IACrD,OAAOd,mBAAmB,CAACI,MAAM,CAACW,IAAI,EAAEZ,KAAK,EAAEC,MAAM,CAACE,OAAO,CAAC;EAChE;EACA,MAAMU,QAAQ,GAAGlB,WAAW,CAACK,KAAK,EAAEC,MAAM,CAAC;EAC3C,QAAQA,MAAM,CAACI,IAAI;IACjB,KAAKT,eAAe,CAACkB,OAAO;MAC1B,IAAId,KAAK,CAACE,IAAI,EAAE;QACd,IAAID,MAAM,CAACc,KAAK,CAACC,GAAG,KAAK,QAAQ,EAAE;UACjC,OAAOvB,QAAQ,CAAC,CAAC,CAAC,EAAEoB,QAAQ,EAAE;YAC5BX,IAAI,EAAE;UACR,CAAC,CAAC;QACJ;MACF,CAAC,MAAM;QACL,IAAID,MAAM,CAACc,KAAK,CAACC,GAAG,KAAK,WAAW,EAAE;UACpC,IAAIC,sBAAsB;UAC1B,OAAOxB,QAAQ,CAAC,CAAC,CAAC,EAAEO,KAAK,EAAE;YACzBE,IAAI,EAAE,IAAI;YACVQ,gBAAgB,EAAE,CAACO,sBAAsB,GAAGjB,KAAK,CAACS,cAAc,CAAC,CAAC,CAAC,KAAK,IAAI,GAAGQ,sBAAsB,GAAGvB,aAAa,CAAC,IAAI,EAAE,OAAO,EAAEO,MAAM,CAACE,OAAO;UACrJ,CAAC,CAAC;QACJ;QACA,IAAIF,MAAM,CAACc,KAAK,CAACC,GAAG,KAAK,SAAS,EAAE;UAClC,IAAIE,sBAAsB;UAC1B,OAAOzB,QAAQ,CAAC,CAAC,CAAC,EAAEO,KAAK,EAAE;YACzBE,IAAI,EAAE,IAAI;YACVQ,gBAAgB,EAAE,CAACQ,sBAAsB,GAAGlB,KAAK,CAACS,cAAc,CAAC,CAAC,CAAC,KAAK,IAAI,GAAGS,sBAAsB,GAAGxB,aAAa,CAAC,IAAI,EAAE,KAAK,EAAEO,MAAM,CAACE,OAAO;UACnJ,CAAC,CAAC;QACJ;MACF;MACA;IACF,KAAKP,eAAe,CAACuB,SAAS;MAC5B,IAAIf,aAAa,KAAK,QAAQ,EAAE;QAC9B,OAAOX,QAAQ,CAAC,CAAC,CAAC,EAAEoB,QAAQ,EAAE;UAC5BX,IAAI,EAAE;QACR,CAAC,CAAC;MACJ;MACA;IACF,KAAKN,eAAe,CAACwB,IAAI;MACvB,OAAO3B,QAAQ,CAAC,CAAC,CAAC,EAAEoB,QAAQ,EAAE;QAC5BX,IAAI,EAAE;MACR,CAAC,CAAC;IACJ;MACE,OAAOW,QAAQ;EACnB;EACA,OAAOA,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}