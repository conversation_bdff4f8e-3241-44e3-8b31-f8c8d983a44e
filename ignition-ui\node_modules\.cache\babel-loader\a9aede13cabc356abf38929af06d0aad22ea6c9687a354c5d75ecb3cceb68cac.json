{"ast": null, "code": "'use client';\n\nexport { useMenuButton } from './useMenuButton';\nexport * from './useMenuButton.types';", "map": {"version": 3, "names": ["useMenuButton"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/useMenuButton/index.js"], "sourcesContent": ["'use client';\n\nexport { useMenuButton } from './useMenuButton';\nexport * from './useMenuButton.types';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,aAAa,QAAQ,iBAAiB;AAC/C,cAAc,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}