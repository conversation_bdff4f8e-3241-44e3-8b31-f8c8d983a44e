{"ast": null, "code": "'use strict';\n\n/** @type {import('./abs')} */\nmodule.exports = Math.abs;", "map": {"version": 3, "names": ["module", "exports", "Math", "abs"], "sources": ["C:/ignition/ignition-ui/node_modules/math-intrinsics/abs.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./abs')} */\nmodule.exports = Math.abs;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACAA,MAAM,CAACC,OAAO,GAAGC,IAAI,CAACC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}