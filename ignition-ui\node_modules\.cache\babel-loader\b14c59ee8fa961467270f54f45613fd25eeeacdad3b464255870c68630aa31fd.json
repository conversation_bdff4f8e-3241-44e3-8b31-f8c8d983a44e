{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getFormControlUtilityClass(slot) {\n  return generateUtilityClass('MuiFormControl', slot);\n}\nconst formControlClasses = generateUtilityClasses('MuiFormControl', ['root', 'error', 'disabled', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'sizeSm', 'sizeMd', 'sizeLg', 'horizontal', 'vertical']);\nexport default formControlClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getFormControlUtilityClass", "slot", "formControlClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/FormControl/formControlClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getFormControlUtilityClass(slot) {\n  return generateUtilityClass('MuiFormControl', slot);\n}\nconst formControlClasses = generateUtilityClasses('MuiFormControl', ['root', 'error', 'disabled', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'sizeSm', 'sizeMd', 'sizeLg', 'horizontal', 'vertical']);\nexport default formControlClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,0BAA0BA,CAACC,IAAI,EAAE;EAC/C,OAAOH,oBAAoB,CAAC,gBAAgB,EAAEG,IAAI,CAAC;AACrD;AACA,MAAMC,kBAAkB,GAAGH,sBAAsB,CAAC,gBAAgB,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;AACzO,eAAeG,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}