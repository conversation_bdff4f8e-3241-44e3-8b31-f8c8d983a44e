{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getStepperUtilityClass(slot) {\n  return generateUtilityClass('MuiStepper', slot);\n}\nconst stepperClasses = generateUtilityClasses('MuiStepper', ['root', 'sizeSm', 'sizeMd', 'sizeLg', 'horizontal', 'vertical']);\nexport default stepperClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getStepperUtilityClass", "slot", "stepperClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Stepper/stepperClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getStepperUtilityClass(slot) {\n  return generateUtilityClass('MuiStepper', slot);\n}\nconst stepperClasses = generateUtilityClasses('MuiStepper', ['root', 'sizeSm', 'sizeMd', 'sizeLg', 'horizontal', 'vertical']);\nexport default stepperClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,sBAAsBA,CAACC,IAAI,EAAE;EAC3C,OAAOH,oBAAoB,CAAC,YAAY,EAAEG,IAAI,CAAC;AACjD;AACA,MAAMC,cAAc,GAAGH,sBAAsB,CAAC,YAAY,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;AAC7H,eAAeG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}