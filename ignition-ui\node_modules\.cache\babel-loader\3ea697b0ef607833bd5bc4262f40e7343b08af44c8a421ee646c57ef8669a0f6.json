{"ast": null, "code": "import * as React from 'react';\nconst WrapListContext = /*#__PURE__*/React.createContext(false);\nif (process.env.NODE_ENV !== 'production') {\n  WrapListContext.displayName = 'WrapListContext';\n}\nexport default WrapListContext;", "map": {"version": 3, "names": ["React", "WrapListContext", "createContext", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/List/WrapListContext.js"], "sourcesContent": ["import * as React from 'react';\nconst WrapListContext = /*#__PURE__*/React.createContext(false);\nif (process.env.NODE_ENV !== 'production') {\n  WrapListContext.displayName = 'WrapListContext';\n}\nexport default WrapListContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,eAAe,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,KAAK,CAAC;AAC/D,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,eAAe,CAACK,WAAW,GAAG,iBAAiB;AACjD;AACA,eAAeL,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}