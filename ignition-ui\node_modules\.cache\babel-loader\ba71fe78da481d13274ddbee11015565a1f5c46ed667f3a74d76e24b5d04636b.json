{"ast": null, "code": "'use strict';\n\nvar define = require('define-properties');\nvar callBind = require('call-bind');\nvar implementation = require('./implementation');\nvar getPolyfill = require('./polyfill');\nvar shim = require('./shim');\nvar polyfill = callBind(getPolyfill(), Object);\ndefine(polyfill, {\n  getPolyfill: getPolyfill,\n  implementation: implementation,\n  shim: shim\n});\nmodule.exports = polyfill;", "map": {"version": 3, "names": ["define", "require", "callBind", "implementation", "getPolyfill", "shim", "polyfill", "Object", "module", "exports"], "sources": ["C:/ignition/ignition-ui/node_modules/object-is/index.js"], "sourcesContent": ["'use strict';\n\nvar define = require('define-properties');\nvar callBind = require('call-bind');\n\nvar implementation = require('./implementation');\nvar getPolyfill = require('./polyfill');\nvar shim = require('./shim');\n\nvar polyfill = callBind(getPolyfill(), Object);\n\ndefine(polyfill, {\n\tgetPolyfill: getPolyfill,\n\timplementation: implementation,\n\tshim: shim\n});\n\nmodule.exports = polyfill;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAGC,OAAO,CAAC,mBAAmB,CAAC;AACzC,IAAIC,QAAQ,GAAGD,OAAO,CAAC,WAAW,CAAC;AAEnC,IAAIE,cAAc,GAAGF,OAAO,CAAC,kBAAkB,CAAC;AAChD,IAAIG,WAAW,GAAGH,OAAO,CAAC,YAAY,CAAC;AACvC,IAAII,IAAI,GAAGJ,OAAO,CAAC,QAAQ,CAAC;AAE5B,IAAIK,QAAQ,GAAGJ,QAAQ,CAACE,WAAW,CAAC,CAAC,EAAEG,MAAM,CAAC;AAE9CP,MAAM,CAACM,QAAQ,EAAE;EAChBF,WAAW,EAAEA,WAAW;EACxBD,cAAc,EAAEA,cAAc;EAC9BE,IAAI,EAAEA;AACP,CAAC,CAAC;AAEFG,MAAM,CAACC,OAAO,GAAGH,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}