{"ast": null, "code": "import { generateUtilityClasses } from '../generateUtilityClasses';\nimport { generateUtilityClass } from '../generateUtilityClass';\nconst COMPONENT_NAME = 'Slider';\nexport function getSliderUtilityClass(slot) {\n  return generateUtilityClass(COMPONENT_NAME, slot);\n}\nexport const sliderClasses = generateUtilityClasses(COMPONENT_NAME, ['root', 'active', 'focusVisible', 'disabled', 'dragging', 'marked', 'vertical', 'trackInverted', 'trackFalse', 'rail', 'track', 'mark', 'markActive', 'markLabel', 'markLabelActive', 'thumb']);", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "COMPONENT_NAME", "getSliderUtilityClass", "slot", "sliderClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/Slider/sliderClasses.js"], "sourcesContent": ["import { generateUtilityClasses } from '../generateUtilityClasses';\nimport { generateUtilityClass } from '../generateUtilityClass';\nconst COMPONENT_NAME = 'Slider';\nexport function getSliderUtilityClass(slot) {\n  return generateUtilityClass(COMPONENT_NAME, slot);\n}\nexport const sliderClasses = generateUtilityClasses(COMPONENT_NAME, ['root', 'active', 'focusVisible', 'disabled', 'dragging', 'marked', 'vertical', 'trackInverted', 'trackFalse', 'rail', 'track', 'mark', 'markActive', 'markLabel', 'markLabelActive', 'thumb']);"], "mappings": "AAAA,SAASA,sBAAsB,QAAQ,2BAA2B;AAClE,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,MAAMC,cAAc,GAAG,QAAQ;AAC/B,OAAO,SAASC,qBAAqBA,CAACC,IAAI,EAAE;EAC1C,OAAOH,oBAAoB,CAACC,cAAc,EAAEE,IAAI,CAAC;AACnD;AACA,OAAO,MAAMC,aAAa,GAAGL,sBAAsB,CAACE,cAAc,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,eAAe,EAAE,YAAY,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,WAAW,EAAE,iBAAiB,EAAE,OAAO,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}