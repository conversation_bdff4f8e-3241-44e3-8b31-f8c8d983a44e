[{"C:\\ignition\\ignition-ui\\src\\index.js": "1", "C:\\ignition\\ignition-ui\\src\\redux\\store.js": "2", "C:\\ignition\\ignition-ui\\src\\views\\main\\Landing.js": "3", "C:\\ignition\\ignition-ui\\src\\layouts\\Auth.js": "4", "C:\\ignition\\ignition-ui\\src\\layouts\\Admin.js": "5", "C:\\ignition\\ignition-ui\\src\\layouts\\Public.js": "6", "C:\\ignition\\ignition-ui\\src\\redux\\userSlice.js": "7", "C:\\ignition\\ignition-ui\\src\\components\\Landing\\About.js": "8", "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Services.js": "9", "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Header.js": "10", "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Testimonials.js": "11", "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Features.js": "12", "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Hero.js": "13", "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Contact.js": "14", "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Footer.js": "15", "C:\\ignition\\ignition-ui\\src\\helpers\\auth.js": "16", "C:\\ignition\\ignition-ui\\src\\routes\\index.js": "17", "C:\\ignition\\ignition-ui\\src\\components\\Sidebar\\SidebarComponent.js": "18", "C:\\ignition\\ignition-ui\\src\\helpers\\constants.js": "19", "C:\\ignition\\ignition-ui\\src\\components\\Sidebar\\RightSidebar.js": "20", "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Gallery.js": "21", "C:\\ignition\\ignition-ui\\src\\components\\Sidebar\\MobileHeader.js": "22", "C:\\ignition\\ignition-ui\\src\\components\\Footers\\AdminFooter.js": "23", "C:\\ignition\\ignition-ui\\src\\views\\plan\\create.js": "24", "C:\\ignition\\ignition-ui\\src\\views\\home\\index.js": "25", "C:\\ignition\\ignition-ui\\src\\views\\calendar\\index.js": "26", "C:\\ignition\\ignition-ui\\src\\views\\auth\\Login.js": "27", "C:\\ignition\\ignition-ui\\src\\views\\auth\\Activate.js": "28", "C:\\ignition\\ignition-ui\\src\\views\\auth\\Register.js": "29", "C:\\ignition\\ignition-ui\\src\\views\\auth\\ForgotPassword.js": "30", "C:\\ignition\\ignition-ui\\src\\views\\auth\\Reset.js": "31", "C:\\ignition\\ignition-ui\\src\\views\\auth\\GoogleAuthHandle.js": "32", "C:\\ignition\\ignition-ui\\src\\helpers\\functions.js": "33", "C:\\ignition\\ignition-ui\\src\\views\\plan\\invitation.js": "34", "C:\\ignition\\ignition-ui\\src\\views\\auth\\RegisterByInvitation.js": "35", "C:\\ignition\\ignition-ui\\src\\views\\todo\\index.js": "36", "C:\\ignition\\ignition-ui\\src\\views\\notifications\\index.js": "37", "C:\\ignition\\ignition-ui\\src\\views\\loading\\index.js": "38", "C:\\ignition\\ignition-ui\\src\\RouteWrapper.js": "39", "C:\\ignition\\ignition-ui\\src\\views\\users\\profile\\index.js": "40", "C:\\ignition\\ignition-ui\\src\\components\\Iconify\\index.js": "41", "C:\\ignition\\ignition-ui\\src\\views\\public\\policies.js": "42", "C:\\ignition\\ignition-ui\\src\\views\\users\\connect_friend\\index.js": "43", "C:\\ignition\\ignition-ui\\src\\views\\users\\other_profile\\index.js": "44", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\index.js": "45", "C:\\ignition\\ignition-ui\\src\\views\\home\\popper.js": "46", "C:\\ignition\\ignition-ui\\src\\components\\Input\\TextAreaBase.js": "47", "C:\\ignition\\ignition-ui\\src\\components\\Input\\InputSelectBase.js": "48", "C:\\ignition\\ignition-ui\\src\\components\\Loading\\HexagonBallLoading.js": "49", "C:\\ignition\\ignition-ui\\src\\components\\Input\\InputBase.js": "50", "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\EmptyState.js": "51", "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\ViewControls.js": "52", "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\PlanList.js": "53", "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\FilterBar.js": "54", "C:\\ignition\\ignition-ui\\src\\components\\Input\\InputPasswordBase.js": "55", "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\LoadingPlans.js": "56", "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\CreatePlanButton.js": "57", "C:\\ignition\\ignition-ui\\src\\views\\notifications\\mockData.js": "58", "C:\\ignition\\ignition-ui\\src\\views\\users\\services.js": "59", "C:\\ignition\\ignition-ui\\src\\views\\home\\hooks\\useFilters.js": "60", "C:\\ignition\\ignition-ui\\src\\views\\home\\hooks\\useGrouping.js": "61", "C:\\ignition\\ignition-ui\\src\\views\\home\\hooks\\usePlanFetching.js": "62", "C:\\ignition\\ignition-ui\\src\\views\\users\\other_profile\\_profile.js": "63", "C:\\ignition\\ignition-ui\\src\\views\\users\\connect_friend\\popper.js": "64", "C:\\ignition\\ignition-ui\\src\\views\\users\\other_profile\\_contact.js": "65", "C:\\ignition\\ignition-ui\\src\\views\\users\\other_profile\\_skill.js": "66", "C:\\ignition\\ignition-ui\\src\\components\\Snackbar\\index.js": "67", "C:\\ignition\\ignition-ui\\src\\components\\Button\\GoogleSignInButton.js": "68", "C:\\ignition\\ignition-ui\\src\\views\\plan\\services.js": "69", "C:\\ignition\\ignition-ui\\src\\views\\todo\\components\\dateSelectionDialog.js": "70", "C:\\ignition\\ignition-ui\\src\\components\\Dialog\\confirm.js": "71", "C:\\ignition\\ignition-ui\\src\\views\\todo\\components\\constants.js": "72", "C:\\ignition\\ignition-ui\\src\\views\\todo\\components\\addTaskDialog.js": "73", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\Header.js": "74", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\Progress.js": "75", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\Description.js": "76", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\Statistics.js": "77", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\TaskList.js": "78", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\MilestoneList.js": "79", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\MilestoneOverview.js": "80", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\Members.js": "81", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\hooks\\usePlanData.js": "82", "C:\\ignition\\ignition-ui\\src\\hooks\\useProfile.js": "83", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\hooks\\useViewMode.js": "84", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\ConfirmDialog.js": "85", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\InviteDialog.js": "86", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\DeleteDialog.js": "87", "C:\\ignition\\ignition-ui\\src\\components\\Input\\CustomDatePicker.js": "88", "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\PlanCard.js": "89", "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\SkeletonCard.js": "90", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\TaskWithSubtasks.js": "91", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\MilestoneCard.js": "92", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\CommentDialog.js": "93", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\AssignMemberDialog.js": "94", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\DueDateDialog.js": "95", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\OptOutDialog.js": "96"}, {"size": 1083, "mtime": 1749040749319, "results": "97", "hashOfConfig": "98"}, {"size": 182, "mtime": 1749040749320, "results": "99", "hashOfConfig": "98"}, {"size": 1569, "mtime": 1749040749333, "results": "100", "hashOfConfig": "98"}, {"size": 988, "mtime": 1749040749320, "results": "101", "hashOfConfig": "98"}, {"size": 5048, "mtime": 1749040749319, "results": "102", "hashOfConfig": "98"}, {"size": 708, "mtime": 1749040749320, "results": "103", "hashOfConfig": "98"}, {"size": 707, "mtime": 1749040749321, "results": "104", "hashOfConfig": "98"}, {"size": 1516, "mtime": 1749040749302, "results": "105", "hashOfConfig": "98"}, {"size": 1846, "mtime": 1749040749304, "results": "106", "hashOfConfig": "98"}, {"size": 2462, "mtime": 1749040749304, "results": "107", "hashOfConfig": "98"}, {"size": 2349, "mtime": 1749040749305, "results": "108", "hashOfConfig": "98"}, {"size": 2193, "mtime": 1749040749303, "results": "109", "hashOfConfig": "98"}, {"size": 429, "mtime": 1749040749304, "results": "110", "hashOfConfig": "98"}, {"size": 951, "mtime": 1749040749303, "results": "111", "hashOfConfig": "98"}, {"size": 324, "mtime": 1749040749303, "results": "112", "hashOfConfig": "98"}, {"size": 782, "mtime": 1749040749316, "results": "113", "hashOfConfig": "98"}, {"size": 4082, "mtime": 1749040749321, "results": "114", "hashOfConfig": "98"}, {"size": 7348, "mtime": 1749040749314, "results": "115", "hashOfConfig": "98"}, {"size": 1704, "mtime": 1749040749316, "results": "116", "hashOfConfig": "98"}, {"size": 9947, "mtime": 1749040749314, "results": "117", "hashOfConfig": "98"}, {"size": 1192, "mtime": 1749040749303, "results": "118", "hashOfConfig": "98"}, {"size": 5408, "mtime": 1749040749313, "results": "119", "hashOfConfig": "98"}, {"size": 1563, "mtime": 1749040749297, "results": "120", "hashOfConfig": "98"}, {"size": 15512, "mtime": 1749044390021, "results": "121", "hashOfConfig": "98"}, {"size": 3810, "mtime": 1749040749331, "results": "122", "hashOfConfig": "98"}, {"size": 36381, "mtime": 1749040749326, "results": "123", "hashOfConfig": "98"}, {"size": 7156, "mtime": 1749040749324, "results": "124", "hashOfConfig": "98"}, {"size": 4868, "mtime": 1749040749322, "results": "125", "hashOfConfig": "98"}, {"size": 11745, "mtime": 1749040749324, "results": "126", "hashOfConfig": "98"}, {"size": 7541, "mtime": 1749040749323, "results": "127", "hashOfConfig": "98"}, {"size": 11414, "mtime": 1749040749324, "results": "128", "hashOfConfig": "98"}, {"size": 5983, "mtime": 1749040749323, "results": "129", "hashOfConfig": "98"}, {"size": 989, "mtime": 1749040749316, "results": "130", "hashOfConfig": "98"}, {"size": 14801, "mtime": 1749040749347, "results": "131", "hashOfConfig": "98"}, {"size": 14611, "mtime": 1749040749324, "results": "132", "hashOfConfig": "98"}, {"size": 16276, "mtime": 1749040749351, "results": "133", "hashOfConfig": "98"}, {"size": 7161, "mtime": 1749040749335, "results": "134", "hashOfConfig": "98"}, {"size": 352, "mtime": 1749040749333, "results": "135", "hashOfConfig": "98"}, {"size": 1011, "mtime": 1749040749156, "results": "136", "hashOfConfig": "98"}, {"size": 31280, "mtime": 1749040749358, "results": "137", "hashOfConfig": "98"}, {"size": 566, "mtime": 1749040749297, "results": "138", "hashOfConfig": "98"}, {"size": 7601, "mtime": 1749040749350, "results": "139", "hashOfConfig": "98"}, {"size": 23019, "mtime": 1749040749354, "results": "140", "hashOfConfig": "98"}, {"size": 11567, "mtime": 1749040749356, "results": "141", "hashOfConfig": "98"}, {"size": 21535, "mtime": 1749043305109, "results": "142", "hashOfConfig": "98"}, {"size": 2441, "mtime": 1749040749331, "results": "143", "hashOfConfig": "98"}, {"size": 2077, "mtime": 1749040749301, "results": "144", "hashOfConfig": "98"}, {"size": 4227, "mtime": 1749040749300, "results": "145", "hashOfConfig": "98"}, {"size": 8432, "mtime": 1749040749306, "results": "146", "hashOfConfig": "98"}, {"size": 2665, "mtime": 1749040749298, "results": "147", "hashOfConfig": "98"}, {"size": 1863, "mtime": 1749040749327, "results": "148", "hashOfConfig": "98"}, {"size": 2537, "mtime": 1749040749330, "results": "149", "hashOfConfig": "98"}, {"size": 2121, "mtime": 1749040749329, "results": "150", "hashOfConfig": "98"}, {"size": 2423, "mtime": 1749040749328, "results": "151", "hashOfConfig": "98"}, {"size": 2198, "mtime": 1749040749300, "results": "152", "hashOfConfig": "98"}, {"size": 613, "mtime": 1749040749328, "results": "153", "hashOfConfig": "98"}, {"size": 983, "mtime": 1749040749327, "results": "154", "hashOfConfig": "98"}, {"size": 10000, "mtime": 1749040749335, "results": "155", "hashOfConfig": "98"}, {"size": 2035, "mtime": 1749040749359, "results": "156", "hashOfConfig": "98"}, {"size": 1611, "mtime": 1749040749330, "results": "157", "hashOfConfig": "98"}, {"size": 2826, "mtime": 1749040749331, "results": "158", "hashOfConfig": "98"}, {"size": 2582, "mtime": 1749040749331, "results": "159", "hashOfConfig": "98"}, {"size": 5853, "mtime": 1749040749355, "results": "160", "hashOfConfig": "98"}, {"size": 2032, "mtime": 1749040749354, "results": "161", "hashOfConfig": "98"}, {"size": 7248, "mtime": 1749040749354, "results": "162", "hashOfConfig": "98"}, {"size": 3298, "mtime": 1749040749355, "results": "163", "hashOfConfig": "98"}, {"size": 3737, "mtime": 1749040749316, "results": "164", "hashOfConfig": "98"}, {"size": 1872, "mtime": 1749040749295, "results": "165", "hashOfConfig": "98"}, {"size": 12332, "mtime": 1749043102491, "results": "166", "hashOfConfig": "98"}, {"size": 2806, "mtime": 1749040749351, "results": "167", "hashOfConfig": "98"}, {"size": 1754, "mtime": 1749040749296, "results": "168", "hashOfConfig": "98"}, {"size": 2891, "mtime": 1749040749351, "results": "169", "hashOfConfig": "98"}, {"size": 2195, "mtime": 1749040749351, "results": "170", "hashOfConfig": "98"}, {"size": 5940, "mtime": 1749043699474, "results": "171", "hashOfConfig": "98"}, {"size": 4753, "mtime": 1749040749341, "results": "172", "hashOfConfig": "98"}, {"size": 1301, "mtime": 1749040749337, "results": "173", "hashOfConfig": "98"}, {"size": 2978, "mtime": 1749040749341, "results": "174", "hashOfConfig": "98"}, {"size": 7055, "mtime": 1749040749342, "results": "175", "hashOfConfig": "98"}, {"size": 2411, "mtime": 1749040749340, "results": "176", "hashOfConfig": "98"}, {"size": 24189, "mtime": 1749040749340, "results": "177", "hashOfConfig": "98"}, {"size": 9016, "mtime": 1749040749339, "results": "178", "hashOfConfig": "98"}, {"size": 12009, "mtime": 1749043238475, "results": "179", "hashOfConfig": "98"}, {"size": 2030, "mtime": 1749040749317, "results": "180", "hashOfConfig": "98"}, {"size": 366, "mtime": 1749040749346, "results": "181", "hashOfConfig": "98"}, {"size": 2418, "mtime": 1749040749344, "results": "182", "hashOfConfig": "98"}, {"size": 4635, "mtime": 1749040749345, "results": "183", "hashOfConfig": "98"}, {"size": 3241, "mtime": 1749040749344, "results": "184", "hashOfConfig": "98"}, {"size": 519, "mtime": 1749040749298, "results": "185", "hashOfConfig": "98"}, {"size": 17760, "mtime": 1749040749328, "results": "186", "hashOfConfig": "98"}, {"size": 3093, "mtime": 1749040749329, "results": "187", "hashOfConfig": "98"}, {"size": 24502, "mtime": 1749040749342, "results": "188", "hashOfConfig": "98"}, {"size": 50253, "mtime": 1749040749339, "results": "189", "hashOfConfig": "98"}, {"size": 11968, "mtime": 1749040749344, "results": "190", "hashOfConfig": "98"}, {"size": 16428, "mtime": 1749040749343, "results": "191", "hashOfConfig": "98"}, {"size": 10533, "mtime": 1749040749345, "results": "192", "hashOfConfig": "98"}, {"size": 3291, "mtime": 1749043130540, "results": "193", "hashOfConfig": "98"}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1k7u7zg", {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\ignition\\ignition-ui\\src\\index.js", [], [], "C:\\ignition\\ignition-ui\\src\\redux\\store.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\main\\Landing.js", [], [], "C:\\ignition\\ignition-ui\\src\\layouts\\Auth.js", [], [], "C:\\ignition\\ignition-ui\\src\\layouts\\Admin.js", [], [], "C:\\ignition\\ignition-ui\\src\\layouts\\Public.js", [], [], "C:\\ignition\\ignition-ui\\src\\redux\\userSlice.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Landing\\About.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Services.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Header.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Testimonials.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Features.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Hero.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Contact.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Footer.js", [], [], "C:\\ignition\\ignition-ui\\src\\helpers\\auth.js", [], [], "C:\\ignition\\ignition-ui\\src\\routes\\index.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Sidebar\\SidebarComponent.js", [], ["482"], "C:\\ignition\\ignition-ui\\src\\helpers\\constants.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Sidebar\\RightSidebar.js", [], ["483"], "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Gallery.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Sidebar\\MobileHeader.js", [], ["484", "485"], "C:\\ignition\\ignition-ui\\src\\components\\Footers\\AdminFooter.js", [], ["486"], "C:\\ignition\\ignition-ui\\src\\views\\plan\\create.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\index.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\calendar\\index.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\auth\\Login.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\auth\\Activate.js", [], ["487"], "C:\\ignition\\ignition-ui\\src\\views\\auth\\Register.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\auth\\ForgotPassword.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\auth\\Reset.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\auth\\GoogleAuthHandle.js", [], ["488"], "C:\\ignition\\ignition-ui\\src\\helpers\\functions.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\invitation.js", [], ["489"], "C:\\ignition\\ignition-ui\\src\\views\\auth\\RegisterByInvitation.js", [], ["490"], "C:\\ignition\\ignition-ui\\src\\views\\todo\\index.js", [], ["491"], "C:\\ignition\\ignition-ui\\src\\views\\notifications\\index.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\loading\\index.js", [], [], "C:\\ignition\\ignition-ui\\src\\RouteWrapper.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\users\\profile\\index.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Iconify\\index.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\public\\policies.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\users\\connect_friend\\index.js", [], ["492", "493", "494"], "C:\\ignition\\ignition-ui\\src\\views\\users\\other_profile\\index.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\index.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\popper.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Input\\TextAreaBase.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Input\\InputSelectBase.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Loading\\HexagonBallLoading.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Input\\InputBase.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\EmptyState.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\ViewControls.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\PlanList.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\FilterBar.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Input\\InputPasswordBase.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\LoadingPlans.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\CreatePlanButton.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\notifications\\mockData.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\users\\services.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\hooks\\useFilters.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\hooks\\useGrouping.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\hooks\\usePlanFetching.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\users\\other_profile\\_profile.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\users\\connect_friend\\popper.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\users\\other_profile\\_contact.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\users\\other_profile\\_skill.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Snackbar\\index.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Button\\GoogleSignInButton.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\services.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\todo\\components\\dateSelectionDialog.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Dialog\\confirm.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\todo\\components\\constants.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\todo\\components\\addTaskDialog.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\Header.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\Progress.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\Description.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\Statistics.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\TaskList.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\MilestoneList.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\MilestoneOverview.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\Members.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\hooks\\usePlanData.js", [], [], "C:\\ignition\\ignition-ui\\src\\hooks\\useProfile.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\hooks\\useViewMode.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\ConfirmDialog.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\InviteDialog.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\DeleteDialog.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Input\\CustomDatePicker.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\PlanCard.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\SkeletonCard.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\TaskWithSubtasks.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\MilestoneCard.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\CommentDialog.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\AssignMemberDialog.js", [], ["495"], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\DueDateDialog.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\OptOutDialog.js", [], [], {"ruleId": "496", "severity": 1, "message": "497", "line": 24, "column": 6, "nodeType": "498", "endLine": 24, "endColumn": 8, "suggestions": "499", "suppressions": "500"}, {"ruleId": "496", "severity": 1, "message": "501", "line": 63, "column": 6, "nodeType": "498", "endLine": 63, "endColumn": 8, "suggestions": "502", "suppressions": "503"}, {"ruleId": "496", "severity": 1, "message": "497", "line": 28, "column": 6, "nodeType": "498", "endLine": 28, "endColumn": 8, "suggestions": "504", "suppressions": "505"}, {"ruleId": "506", "severity": 1, "message": "507", "line": 124, "column": 11, "nodeType": "508", "endLine": 124, "endColumn": 65, "suppressions": "509"}, {"ruleId": "510", "severity": 1, "message": "511", "line": 11, "column": 13, "nodeType": "508", "endLine": 12, "endColumn": 31, "suppressions": "512"}, {"ruleId": "496", "severity": 1, "message": "513", "line": 26, "column": 6, "nodeType": "498", "endLine": 26, "endColumn": 22, "suggestions": "514", "suppressions": "515"}, {"ruleId": "496", "severity": 1, "message": "516", "line": 27, "column": 6, "nodeType": "498", "endLine": 27, "endColumn": 8, "suggestions": "517", "suppressions": "518"}, {"ruleId": "496", "severity": 1, "message": "519", "line": 76, "column": 6, "nodeType": "498", "endLine": 76, "endColumn": 8, "suggestions": "520", "suppressions": "521"}, {"ruleId": "496", "severity": 1, "message": "522", "line": 110, "column": 6, "nodeType": "498", "endLine": 110, "endColumn": 8, "suggestions": "523", "suppressions": "524"}, {"ruleId": "496", "severity": 1, "message": "525", "line": 59, "column": 6, "nodeType": "498", "endLine": 59, "endColumn": 8, "suggestions": "526", "suppressions": "527"}, {"ruleId": "496", "severity": 1, "message": "528", "line": 471, "column": 6, "nodeType": "498", "endLine": 471, "endColumn": 31, "suggestions": "529", "suppressions": "530"}, {"ruleId": "496", "severity": 1, "message": "531", "line": 476, "column": 6, "nodeType": "498", "endLine": 476, "endColumn": 15, "suggestions": "532", "suppressions": "533"}, {"ruleId": "496", "severity": 1, "message": "531", "line": 482, "column": 6, "nodeType": "498", "endLine": 482, "endColumn": 12, "suggestions": "534", "suppressions": "535"}, {"ruleId": "496", "severity": 1, "message": "536", "line": 202, "column": 6, "nodeType": "498", "endLine": 202, "endColumn": 20, "suggestions": "537", "suppressions": "538"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchAccountInfo'. Either include it or remove the dependency array.", "ArrayExpression", ["539"], ["540"], "React Hook useEffect has a missing dependency: 'fetchTodayTasks'. Either include it or remove the dependency array.", ["541"], ["542"], ["543"], ["544"], "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "JSXOpeningElement", ["545"], "jsx-a11y/anchor-is-valid", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", ["546"], "React Hook useEffect has a missing dependency: 'handleActivate'. Either include it or remove the dependency array.", ["547"], ["548"], "React Hook useEffect has missing dependencies: 'location.search' and 'onGooglelogin'. Either include them or remove the dependency array.", ["549"], ["550"], "React Hook useEffect has a missing dependency: 'handleCheckInvitation'. Either include it or remove the dependency array.", ["551"], ["552"], "React Hook useEffect has a missing dependency: 'checkSignedId'. Either include it or remove the dependency array.", ["553"], ["554"], "React Hook useEffect has a missing dependency: 'fetchTasks'. Either include it or remove the dependency array.", ["555"], ["556"], "React Hook useEffect has a missing dependency: 'handleObserver'. Either include it or remove the dependency array.", ["557"], ["558"], "React Hook useEffect has a missing dependency: 'fetchUserFriendData'. Either include it or remove the dependency array.", ["559"], ["560"], ["561"], ["562"], "React Hook useEffect has a missing dependency: 'processUsersData'. Either include it or remove the dependency array.", ["563"], ["564"], {"desc": "565", "fix": "566"}, {"kind": "567", "justification": "568"}, {"desc": "569", "fix": "570"}, {"kind": "567", "justification": "568"}, {"desc": "565", "fix": "571"}, {"kind": "567", "justification": "568"}, {"kind": "567", "justification": "568"}, {"kind": "567", "justification": "568"}, {"desc": "572", "fix": "573"}, {"kind": "567", "justification": "568"}, {"desc": "574", "fix": "575"}, {"kind": "567", "justification": "568"}, {"desc": "576", "fix": "577"}, {"kind": "567", "justification": "568"}, {"desc": "578", "fix": "579"}, {"kind": "567", "justification": "568"}, {"desc": "580", "fix": "581"}, {"kind": "567", "justification": "568"}, {"desc": "582", "fix": "583"}, {"kind": "567", "justification": "568"}, {"desc": "584", "fix": "585"}, {"kind": "567", "justification": "568"}, {"desc": "586", "fix": "587"}, {"kind": "567", "justification": "568"}, {"desc": "588", "fix": "589"}, {"kind": "567", "justification": "568"}, "Update the dependencies array to be: [fetchAccountInfo]", {"range": "590", "text": "591"}, "directive", "", "Update the dependencies array to be: [fetchTodayTasks]", {"range": "592", "text": "593"}, {"range": "594", "text": "591"}, "Update the dependencies array to be: [handleActivate, param1, param2]", {"range": "595", "text": "596"}, "Update the dependencies array to be: [location.search, onGooglelogin]", {"range": "597", "text": "598"}, "Update the dependencies array to be: [handleCheckInvitation]", {"range": "599", "text": "600"}, "Update the dependencies array to be: [checkSignedId]", {"range": "601", "text": "602"}, "Update the dependencies array to be: [fetchTasks]", {"range": "603", "text": "604"}, "Update the dependencies array to be: [handleObserver, loadingMore, totalPages]", {"range": "605", "text": "606"}, "Update the dependencies array to be: [fetchUserFriendData, filters]", {"range": "607", "text": "608"}, "Update the dependencies array to be: [fetchUserFriendData, page]", {"range": "609", "text": "610"}, "Update the dependencies array to be: [invitedUsers, processUsersData]", {"range": "611", "text": "612"}, [1021, 1023], "[fetchAccountInfo]", [2087, 2089], "[fetchTodayTasks]", [799, 801], [800, 816], "[handleActivate, param1, param2]", [1073, 1075], "[location.search, onGooglelogin]", [2532, 2534], "[handleCheckInvitation]", [3427, 3429], "[checkSignedId]", [2266, 2268], "[fetchTasks]", [14298, 14323], "[handleObserver, loadingMore, totalPages]", [14400, 14409], "[fetchUserFriendData, filters]", [14503, 14509], "[fetchUserFriendData, page]", [6690, 6704], "[invitedUsers, processUsersData]"]