[{"C:\\ignition\\ignition-ui\\src\\index.js": "1", "C:\\ignition\\ignition-ui\\src\\redux\\store.js": "2", "C:\\ignition\\ignition-ui\\src\\views\\main\\Landing.js": "3", "C:\\ignition\\ignition-ui\\src\\layouts\\Auth.js": "4", "C:\\ignition\\ignition-ui\\src\\layouts\\Admin.js": "5", "C:\\ignition\\ignition-ui\\src\\layouts\\Public.js": "6", "C:\\ignition\\ignition-ui\\src\\redux\\userSlice.js": "7", "C:\\ignition\\ignition-ui\\src\\components\\Landing\\About.js": "8", "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Services.js": "9", "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Header.js": "10", "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Testimonials.js": "11", "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Features.js": "12", "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Hero.js": "13", "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Contact.js": "14", "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Footer.js": "15", "C:\\ignition\\ignition-ui\\src\\helpers\\auth.js": "16", "C:\\ignition\\ignition-ui\\src\\routes\\index.js": "17", "C:\\ignition\\ignition-ui\\src\\components\\Sidebar\\SidebarComponent.js": "18", "C:\\ignition\\ignition-ui\\src\\helpers\\constants.js": "19", "C:\\ignition\\ignition-ui\\src\\components\\Sidebar\\RightSidebar.js": "20", "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Gallery.js": "21", "C:\\ignition\\ignition-ui\\src\\components\\Sidebar\\MobileHeader.js": "22", "C:\\ignition\\ignition-ui\\src\\components\\Footers\\AdminFooter.js": "23", "C:\\ignition\\ignition-ui\\src\\views\\plan\\create.js": "24", "C:\\ignition\\ignition-ui\\src\\views\\home\\index.js": "25", "C:\\ignition\\ignition-ui\\src\\views\\calendar\\index.js": "26", "C:\\ignition\\ignition-ui\\src\\views\\auth\\Login.js": "27", "C:\\ignition\\ignition-ui\\src\\views\\auth\\Activate.js": "28", "C:\\ignition\\ignition-ui\\src\\views\\auth\\Register.js": "29", "C:\\ignition\\ignition-ui\\src\\views\\auth\\ForgotPassword.js": "30", "C:\\ignition\\ignition-ui\\src\\views\\auth\\Reset.js": "31", "C:\\ignition\\ignition-ui\\src\\views\\auth\\GoogleAuthHandle.js": "32", "C:\\ignition\\ignition-ui\\src\\helpers\\functions.js": "33", "C:\\ignition\\ignition-ui\\src\\views\\plan\\invitation.js": "34", "C:\\ignition\\ignition-ui\\src\\views\\auth\\RegisterByInvitation.js": "35", "C:\\ignition\\ignition-ui\\src\\views\\todo\\index.js": "36", "C:\\ignition\\ignition-ui\\src\\views\\notifications\\index.js": "37", "C:\\ignition\\ignition-ui\\src\\views\\loading\\index.js": "38", "C:\\ignition\\ignition-ui\\src\\RouteWrapper.js": "39", "C:\\ignition\\ignition-ui\\src\\views\\users\\profile\\index.js": "40", "C:\\ignition\\ignition-ui\\src\\components\\Iconify\\index.js": "41", "C:\\ignition\\ignition-ui\\src\\views\\public\\policies.js": "42", "C:\\ignition\\ignition-ui\\src\\views\\users\\connect_friend\\index.js": "43", "C:\\ignition\\ignition-ui\\src\\views\\users\\other_profile\\index.js": "44", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\index.js": "45", "C:\\ignition\\ignition-ui\\src\\views\\home\\popper.js": "46", "C:\\ignition\\ignition-ui\\src\\components\\Input\\TextAreaBase.js": "47", "C:\\ignition\\ignition-ui\\src\\components\\Input\\InputSelectBase.js": "48", "C:\\ignition\\ignition-ui\\src\\components\\Loading\\HexagonBallLoading.js": "49", "C:\\ignition\\ignition-ui\\src\\components\\Input\\InputBase.js": "50", "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\EmptyState.js": "51", "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\ViewControls.js": "52", "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\PlanList.js": "53", "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\FilterBar.js": "54", "C:\\ignition\\ignition-ui\\src\\components\\Input\\InputPasswordBase.js": "55", "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\LoadingPlans.js": "56", "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\CreatePlanButton.js": "57", "C:\\ignition\\ignition-ui\\src\\views\\notifications\\mockData.js": "58", "C:\\ignition\\ignition-ui\\src\\views\\users\\services.js": "59", "C:\\ignition\\ignition-ui\\src\\views\\home\\hooks\\useFilters.js": "60", "C:\\ignition\\ignition-ui\\src\\views\\home\\hooks\\useGrouping.js": "61", "C:\\ignition\\ignition-ui\\src\\views\\home\\hooks\\usePlanFetching.js": "62", "C:\\ignition\\ignition-ui\\src\\views\\users\\other_profile\\_profile.js": "63", "C:\\ignition\\ignition-ui\\src\\views\\users\\connect_friend\\popper.js": "64", "C:\\ignition\\ignition-ui\\src\\views\\users\\other_profile\\_contact.js": "65", "C:\\ignition\\ignition-ui\\src\\views\\users\\other_profile\\_skill.js": "66", "C:\\ignition\\ignition-ui\\src\\components\\Snackbar\\index.js": "67", "C:\\ignition\\ignition-ui\\src\\components\\Button\\GoogleSignInButton.js": "68", "C:\\ignition\\ignition-ui\\src\\views\\plan\\services.js": "69", "C:\\ignition\\ignition-ui\\src\\views\\todo\\components\\dateSelectionDialog.js": "70", "C:\\ignition\\ignition-ui\\src\\components\\Dialog\\confirm.js": "71", "C:\\ignition\\ignition-ui\\src\\views\\todo\\components\\constants.js": "72", "C:\\ignition\\ignition-ui\\src\\views\\todo\\components\\addTaskDialog.js": "73", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\Header.js": "74", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\Progress.js": "75", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\Description.js": "76", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\Statistics.js": "77", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\TaskList.js": "78", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\MilestoneList.js": "79", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\MilestoneOverview.js": "80", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\Members.js": "81", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\hooks\\usePlanData.js": "82", "C:\\ignition\\ignition-ui\\src\\hooks\\useProfile.js": "83", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\hooks\\useViewMode.js": "84", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\ConfirmDialog.js": "85", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\InviteDialog.js": "86", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\DeleteDialog.js": "87", "C:\\ignition\\ignition-ui\\src\\components\\Input\\CustomDatePicker.js": "88", "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\PlanCard.js": "89", "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\SkeletonCard.js": "90", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\TaskWithSubtasks.js": "91", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\MilestoneCard.js": "92", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\CommentDialog.js": "93", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\AssignMemberDialog.js": "94", "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\DueDateDialog.js": "95"}, {"size": 1083, "mtime": 1749040749319, "results": "96", "hashOfConfig": "97"}, {"size": 182, "mtime": 1749040749320, "results": "98", "hashOfConfig": "97"}, {"size": 1569, "mtime": 1749040749333, "results": "99", "hashOfConfig": "97"}, {"size": 988, "mtime": 1749040749320, "results": "100", "hashOfConfig": "97"}, {"size": 5048, "mtime": 1749040749319, "results": "101", "hashOfConfig": "97"}, {"size": 708, "mtime": 1749040749320, "results": "102", "hashOfConfig": "97"}, {"size": 707, "mtime": 1749040749321, "results": "103", "hashOfConfig": "97"}, {"size": 1516, "mtime": 1749040749302, "results": "104", "hashOfConfig": "97"}, {"size": 1846, "mtime": 1749040749304, "results": "105", "hashOfConfig": "97"}, {"size": 2462, "mtime": 1749040749304, "results": "106", "hashOfConfig": "97"}, {"size": 2349, "mtime": 1749040749305, "results": "107", "hashOfConfig": "97"}, {"size": 2193, "mtime": 1749040749303, "results": "108", "hashOfConfig": "97"}, {"size": 429, "mtime": 1749040749304, "results": "109", "hashOfConfig": "97"}, {"size": 951, "mtime": 1749040749303, "results": "110", "hashOfConfig": "97"}, {"size": 324, "mtime": 1749040749303, "results": "111", "hashOfConfig": "97"}, {"size": 782, "mtime": 1749040749316, "results": "112", "hashOfConfig": "97"}, {"size": 4082, "mtime": 1749040749321, "results": "113", "hashOfConfig": "97"}, {"size": 7348, "mtime": 1749040749314, "results": "114", "hashOfConfig": "97"}, {"size": 1704, "mtime": 1749040749316, "results": "115", "hashOfConfig": "97"}, {"size": 9947, "mtime": 1749040749314, "results": "116", "hashOfConfig": "97"}, {"size": 1192, "mtime": 1749040749303, "results": "117", "hashOfConfig": "97"}, {"size": 5408, "mtime": 1749040749313, "results": "118", "hashOfConfig": "97"}, {"size": 1563, "mtime": 1749040749297, "results": "119", "hashOfConfig": "97"}, {"size": 21745, "mtime": 1749040749337, "results": "120", "hashOfConfig": "97"}, {"size": 3810, "mtime": 1749040749331, "results": "121", "hashOfConfig": "97"}, {"size": 36381, "mtime": 1749040749326, "results": "122", "hashOfConfig": "97"}, {"size": 7156, "mtime": 1749040749324, "results": "123", "hashOfConfig": "97"}, {"size": 4868, "mtime": 1749040749322, "results": "124", "hashOfConfig": "97"}, {"size": 11745, "mtime": 1749040749324, "results": "125", "hashOfConfig": "97"}, {"size": 7541, "mtime": 1749040749323, "results": "126", "hashOfConfig": "97"}, {"size": 11414, "mtime": 1749040749324, "results": "127", "hashOfConfig": "97"}, {"size": 5983, "mtime": 1749040749323, "results": "128", "hashOfConfig": "97"}, {"size": 989, "mtime": 1749040749316, "results": "129", "hashOfConfig": "97"}, {"size": 14801, "mtime": 1749040749347, "results": "130", "hashOfConfig": "97"}, {"size": 14611, "mtime": 1749040749324, "results": "131", "hashOfConfig": "97"}, {"size": 16276, "mtime": 1749040749351, "results": "132", "hashOfConfig": "97"}, {"size": 7161, "mtime": 1749040749335, "results": "133", "hashOfConfig": "97"}, {"size": 352, "mtime": 1749040749333, "results": "134", "hashOfConfig": "97"}, {"size": 1011, "mtime": 1749040749156, "results": "135", "hashOfConfig": "97"}, {"size": 31280, "mtime": 1749040749358, "results": "136", "hashOfConfig": "97"}, {"size": 566, "mtime": 1749040749297, "results": "137", "hashOfConfig": "97"}, {"size": 7601, "mtime": 1749040749350, "results": "138", "hashOfConfig": "97"}, {"size": 23019, "mtime": 1749040749354, "results": "139", "hashOfConfig": "97"}, {"size": 11567, "mtime": 1749040749356, "results": "140", "hashOfConfig": "97"}, {"size": 21227, "mtime": 1749040749346, "results": "141", "hashOfConfig": "97"}, {"size": 2441, "mtime": 1749040749331, "results": "142", "hashOfConfig": "97"}, {"size": 2077, "mtime": 1749040749301, "results": "143", "hashOfConfig": "97"}, {"size": 4227, "mtime": 1749040749300, "results": "144", "hashOfConfig": "97"}, {"size": 8432, "mtime": 1749040749306, "results": "145", "hashOfConfig": "97"}, {"size": 2665, "mtime": 1749040749298, "results": "146", "hashOfConfig": "97"}, {"size": 1863, "mtime": 1749040749327, "results": "147", "hashOfConfig": "97"}, {"size": 2537, "mtime": 1749040749330, "results": "148", "hashOfConfig": "97"}, {"size": 2121, "mtime": 1749040749329, "results": "149", "hashOfConfig": "97"}, {"size": 2423, "mtime": 1749040749328, "results": "150", "hashOfConfig": "97"}, {"size": 2198, "mtime": 1749040749300, "results": "151", "hashOfConfig": "97"}, {"size": 613, "mtime": 1749040749328, "results": "152", "hashOfConfig": "97"}, {"size": 983, "mtime": 1749040749327, "results": "153", "hashOfConfig": "97"}, {"size": 10000, "mtime": 1749040749335, "results": "154", "hashOfConfig": "97"}, {"size": 2035, "mtime": 1749040749359, "results": "155", "hashOfConfig": "97"}, {"size": 1611, "mtime": 1749040749330, "results": "156", "hashOfConfig": "97"}, {"size": 2826, "mtime": 1749040749331, "results": "157", "hashOfConfig": "97"}, {"size": 2582, "mtime": 1749040749331, "results": "158", "hashOfConfig": "97"}, {"size": 5853, "mtime": 1749040749355, "results": "159", "hashOfConfig": "97"}, {"size": 2032, "mtime": 1749040749354, "results": "160", "hashOfConfig": "97"}, {"size": 7248, "mtime": 1749040749354, "results": "161", "hashOfConfig": "97"}, {"size": 3298, "mtime": 1749040749355, "results": "162", "hashOfConfig": "97"}, {"size": 3737, "mtime": 1749040749316, "results": "163", "hashOfConfig": "97"}, {"size": 1872, "mtime": 1749040749295, "results": "164", "hashOfConfig": "97"}, {"size": 12023, "mtime": 1749040749348, "results": "165", "hashOfConfig": "97"}, {"size": 2806, "mtime": 1749040749351, "results": "166", "hashOfConfig": "97"}, {"size": 1754, "mtime": 1749040749296, "results": "167", "hashOfConfig": "97"}, {"size": 2891, "mtime": 1749040749351, "results": "168", "hashOfConfig": "97"}, {"size": 2195, "mtime": 1749040749351, "results": "169", "hashOfConfig": "97"}, {"size": 5122, "mtime": 1749040749337, "results": "170", "hashOfConfig": "97"}, {"size": 4753, "mtime": 1749040749341, "results": "171", "hashOfConfig": "97"}, {"size": 1301, "mtime": 1749040749337, "results": "172", "hashOfConfig": "97"}, {"size": 2978, "mtime": 1749040749341, "results": "173", "hashOfConfig": "97"}, {"size": 7055, "mtime": 1749040749342, "results": "174", "hashOfConfig": "97"}, {"size": 2411, "mtime": 1749040749340, "results": "175", "hashOfConfig": "97"}, {"size": 24189, "mtime": 1749040749340, "results": "176", "hashOfConfig": "97"}, {"size": 9016, "mtime": 1749040749339, "results": "177", "hashOfConfig": "97"}, {"size": 11570, "mtime": 1749040749346, "results": "178", "hashOfConfig": "97"}, {"size": 2030, "mtime": 1749040749317, "results": "179", "hashOfConfig": "97"}, {"size": 366, "mtime": 1749040749346, "results": "180", "hashOfConfig": "97"}, {"size": 2418, "mtime": 1749040749344, "results": "181", "hashOfConfig": "97"}, {"size": 4635, "mtime": 1749040749345, "results": "182", "hashOfConfig": "97"}, {"size": 3241, "mtime": 1749040749344, "results": "183", "hashOfConfig": "97"}, {"size": 519, "mtime": 1749040749298, "results": "184", "hashOfConfig": "97"}, {"size": 17760, "mtime": 1749040749328, "results": "185", "hashOfConfig": "97"}, {"size": 3093, "mtime": 1749040749329, "results": "186", "hashOfConfig": "97"}, {"size": 24502, "mtime": 1749040749342, "results": "187", "hashOfConfig": "97"}, {"size": 50253, "mtime": 1749040749339, "results": "188", "hashOfConfig": "97"}, {"size": 11968, "mtime": 1749040749344, "results": "189", "hashOfConfig": "97"}, {"size": 16428, "mtime": 1749040749343, "results": "190", "hashOfConfig": "97"}, {"size": 10533, "mtime": 1749040749345, "results": "191", "hashOfConfig": "97"}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1k7u7zg", {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\ignition\\ignition-ui\\src\\index.js", [], [], "C:\\ignition\\ignition-ui\\src\\redux\\store.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\main\\Landing.js", [], [], "C:\\ignition\\ignition-ui\\src\\layouts\\Auth.js", [], [], "C:\\ignition\\ignition-ui\\src\\layouts\\Admin.js", [], [], "C:\\ignition\\ignition-ui\\src\\layouts\\Public.js", [], [], "C:\\ignition\\ignition-ui\\src\\redux\\userSlice.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Landing\\About.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Services.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Header.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Testimonials.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Features.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Hero.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Contact.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Footer.js", [], [], "C:\\ignition\\ignition-ui\\src\\helpers\\auth.js", [], [], "C:\\ignition\\ignition-ui\\src\\routes\\index.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Sidebar\\SidebarComponent.js", [], ["477"], "C:\\ignition\\ignition-ui\\src\\helpers\\constants.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Sidebar\\RightSidebar.js", [], ["478"], "C:\\ignition\\ignition-ui\\src\\components\\Landing\\Gallery.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Sidebar\\MobileHeader.js", [], ["479", "480"], "C:\\ignition\\ignition-ui\\src\\components\\Footers\\AdminFooter.js", [], ["481"], "C:\\ignition\\ignition-ui\\src\\views\\plan\\create.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\index.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\calendar\\index.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\auth\\Login.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\auth\\Activate.js", [], ["482"], "C:\\ignition\\ignition-ui\\src\\views\\auth\\Register.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\auth\\ForgotPassword.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\auth\\Reset.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\auth\\GoogleAuthHandle.js", [], ["483"], "C:\\ignition\\ignition-ui\\src\\helpers\\functions.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\invitation.js", [], ["484"], "C:\\ignition\\ignition-ui\\src\\views\\auth\\RegisterByInvitation.js", [], ["485"], "C:\\ignition\\ignition-ui\\src\\views\\todo\\index.js", [], ["486"], "C:\\ignition\\ignition-ui\\src\\views\\notifications\\index.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\loading\\index.js", [], [], "C:\\ignition\\ignition-ui\\src\\RouteWrapper.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\users\\profile\\index.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Iconify\\index.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\public\\policies.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\users\\connect_friend\\index.js", [], ["487", "488", "489"], "C:\\ignition\\ignition-ui\\src\\views\\users\\other_profile\\index.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\index.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\popper.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Input\\TextAreaBase.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Input\\InputSelectBase.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Loading\\HexagonBallLoading.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Input\\InputBase.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\EmptyState.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\ViewControls.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\PlanList.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\FilterBar.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Input\\InputPasswordBase.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\LoadingPlans.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\CreatePlanButton.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\notifications\\mockData.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\users\\services.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\hooks\\useFilters.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\hooks\\useGrouping.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\hooks\\usePlanFetching.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\users\\other_profile\\_profile.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\users\\connect_friend\\popper.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\users\\other_profile\\_contact.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\users\\other_profile\\_skill.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Snackbar\\index.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Button\\GoogleSignInButton.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\services.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\todo\\components\\dateSelectionDialog.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Dialog\\confirm.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\todo\\components\\constants.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\todo\\components\\addTaskDialog.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\Header.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\Progress.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\Description.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\Statistics.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\TaskList.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\MilestoneList.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\MilestoneOverview.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\Members.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\hooks\\usePlanData.js", [], [], "C:\\ignition\\ignition-ui\\src\\hooks\\useProfile.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\hooks\\useViewMode.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\ConfirmDialog.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\InviteDialog.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\DeleteDialog.js", [], [], "C:\\ignition\\ignition-ui\\src\\components\\Input\\CustomDatePicker.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\PlanCard.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\home\\components\\SkeletonCard.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\TaskWithSubtasks.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\components\\MilestoneCard.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\CommentDialog.js", [], [], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\AssignMemberDialog.js", [], ["490"], "C:\\ignition\\ignition-ui\\src\\views\\plan\\detail\\dialogs\\DueDateDialog.js", [], [], {"ruleId": "491", "severity": 1, "message": "492", "line": 24, "column": 6, "nodeType": "493", "endLine": 24, "endColumn": 8, "suggestions": "494", "suppressions": "495"}, {"ruleId": "491", "severity": 1, "message": "496", "line": 63, "column": 6, "nodeType": "493", "endLine": 63, "endColumn": 8, "suggestions": "497", "suppressions": "498"}, {"ruleId": "491", "severity": 1, "message": "492", "line": 28, "column": 6, "nodeType": "493", "endLine": 28, "endColumn": 8, "suggestions": "499", "suppressions": "500"}, {"ruleId": "501", "severity": 1, "message": "502", "line": 124, "column": 11, "nodeType": "503", "endLine": 124, "endColumn": 65, "suppressions": "504"}, {"ruleId": "505", "severity": 1, "message": "506", "line": 11, "column": 13, "nodeType": "503", "endLine": 12, "endColumn": 31, "suppressions": "507"}, {"ruleId": "491", "severity": 1, "message": "508", "line": 26, "column": 6, "nodeType": "493", "endLine": 26, "endColumn": 22, "suggestions": "509", "suppressions": "510"}, {"ruleId": "491", "severity": 1, "message": "511", "line": 27, "column": 6, "nodeType": "493", "endLine": 27, "endColumn": 8, "suggestions": "512", "suppressions": "513"}, {"ruleId": "491", "severity": 1, "message": "514", "line": 76, "column": 6, "nodeType": "493", "endLine": 76, "endColumn": 8, "suggestions": "515", "suppressions": "516"}, {"ruleId": "491", "severity": 1, "message": "517", "line": 110, "column": 6, "nodeType": "493", "endLine": 110, "endColumn": 8, "suggestions": "518", "suppressions": "519"}, {"ruleId": "491", "severity": 1, "message": "520", "line": 59, "column": 6, "nodeType": "493", "endLine": 59, "endColumn": 8, "suggestions": "521", "suppressions": "522"}, {"ruleId": "491", "severity": 1, "message": "523", "line": 471, "column": 6, "nodeType": "493", "endLine": 471, "endColumn": 31, "suggestions": "524", "suppressions": "525"}, {"ruleId": "491", "severity": 1, "message": "526", "line": 476, "column": 6, "nodeType": "493", "endLine": 476, "endColumn": 15, "suggestions": "527", "suppressions": "528"}, {"ruleId": "491", "severity": 1, "message": "526", "line": 482, "column": 6, "nodeType": "493", "endLine": 482, "endColumn": 12, "suggestions": "529", "suppressions": "530"}, {"ruleId": "491", "severity": 1, "message": "531", "line": 202, "column": 6, "nodeType": "493", "endLine": 202, "endColumn": 20, "suggestions": "532", "suppressions": "533"}, "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchAccountInfo'. Either include it or remove the dependency array.", "ArrayExpression", ["534"], ["535"], "React Hook useEffect has a missing dependency: 'fetchTodayTasks'. Either include it or remove the dependency array.", ["536"], ["537"], ["538"], ["539"], "jsx-a11y/alt-text", "img elements must have an alt prop, either with meaningful text, or an empty string for decorative images.", "JSXOpeningElement", ["540"], "jsx-a11y/anchor-is-valid", "The href attribute is required for an anchor to be keyboard accessible. Provide a valid, navigable address as the href value. If you cannot provide an href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", ["541"], "React Hook useEffect has a missing dependency: 'handleActivate'. Either include it or remove the dependency array.", ["542"], ["543"], "React Hook useEffect has missing dependencies: 'location.search' and 'onGooglelogin'. Either include them or remove the dependency array.", ["544"], ["545"], "React Hook useEffect has a missing dependency: 'handleCheckInvitation'. Either include it or remove the dependency array.", ["546"], ["547"], "React Hook useEffect has a missing dependency: 'checkSignedId'. Either include it or remove the dependency array.", ["548"], ["549"], "React Hook useEffect has a missing dependency: 'fetchTasks'. Either include it or remove the dependency array.", ["550"], ["551"], "React Hook useEffect has a missing dependency: 'handleObserver'. Either include it or remove the dependency array.", ["552"], ["553"], "React Hook useEffect has a missing dependency: 'fetchUserFriendData'. Either include it or remove the dependency array.", ["554"], ["555"], ["556"], ["557"], "React Hook useEffect has a missing dependency: 'processUsersData'. Either include it or remove the dependency array.", ["558"], ["559"], {"desc": "560", "fix": "561"}, {"kind": "562", "justification": "563"}, {"desc": "564", "fix": "565"}, {"kind": "562", "justification": "563"}, {"desc": "560", "fix": "566"}, {"kind": "562", "justification": "563"}, {"kind": "562", "justification": "563"}, {"kind": "562", "justification": "563"}, {"desc": "567", "fix": "568"}, {"kind": "562", "justification": "563"}, {"desc": "569", "fix": "570"}, {"kind": "562", "justification": "563"}, {"desc": "571", "fix": "572"}, {"kind": "562", "justification": "563"}, {"desc": "573", "fix": "574"}, {"kind": "562", "justification": "563"}, {"desc": "575", "fix": "576"}, {"kind": "562", "justification": "563"}, {"desc": "577", "fix": "578"}, {"kind": "562", "justification": "563"}, {"desc": "579", "fix": "580"}, {"kind": "562", "justification": "563"}, {"desc": "581", "fix": "582"}, {"kind": "562", "justification": "563"}, {"desc": "583", "fix": "584"}, {"kind": "562", "justification": "563"}, "Update the dependencies array to be: [fetchAccountInfo]", {"range": "585", "text": "586"}, "directive", "", "Update the dependencies array to be: [fetchTodayTasks]", {"range": "587", "text": "588"}, {"range": "589", "text": "586"}, "Update the dependencies array to be: [handleActivate, param1, param2]", {"range": "590", "text": "591"}, "Update the dependencies array to be: [location.search, onGooglelogin]", {"range": "592", "text": "593"}, "Update the dependencies array to be: [handleCheckInvitation]", {"range": "594", "text": "595"}, "Update the dependencies array to be: [checkSignedId]", {"range": "596", "text": "597"}, "Update the dependencies array to be: [fetchTasks]", {"range": "598", "text": "599"}, "Update the dependencies array to be: [handleObserver, loadingMore, totalPages]", {"range": "600", "text": "601"}, "Update the dependencies array to be: [fetchUserFriendData, filters]", {"range": "602", "text": "603"}, "Update the dependencies array to be: [fetchUserFriendData, page]", {"range": "604", "text": "605"}, "Update the dependencies array to be: [invitedUsers, processUsersData]", {"range": "606", "text": "607"}, [1021, 1023], "[fetchAccountInfo]", [2087, 2089], "[fetchTodayTasks]", [799, 801], [800, 816], "[handleActivate, param1, param2]", [1073, 1075], "[location.search, onGooglelogin]", [2532, 2534], "[handleCheckInvitation]", [3427, 3429], "[checkSignedId]", [2266, 2268], "[fetchTasks]", [14298, 14323], "[handleObserver, loadingMore, totalPages]", [14400, 14409], "[fetchUserFriendData, filters]", [14503, 14509], "[fetchUserFriendData, page]", [6690, 6704], "[invitedUsers, processUsersData]"]