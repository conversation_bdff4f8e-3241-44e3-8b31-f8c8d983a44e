import React from 'react';
import { Box, Typography, Grid } from '@mui/material';
import Iconify from 'components/Iconify/index';
import { mainYellowColor } from "helpers/constants";
import styles from "../styles.module.scss";
import PlanCard from './PlanCard';

const PlanList = ({
  groupedPlans,
  viewMode,
  currentUser,
  observerRef,
  loadingMore
}) => {
  return (
    <>
      {Object.entries(groupedPlans).map(([groupName, groupPlans]) => (
        <Box key={groupName} className={styles.planGroup}>
          <Typography
            variant="h6"
            className={styles.groupTitle}
            sx={{
              fontFamily: '"Recursive Variable", sans-serif',
              fontWeight: 600,
              color: '#333',
              marginBottom: '16px',
              paddingBottom: '8px',
              borderBottom: '2px solid #f0f0f0',
              display: 'flex',
              alignItems: 'center',
              '&::before': {
                content: '""',
                display: 'inline-block',
                width: '4px',
                height: '16px',
                backgroundColor: '#F0A500',
                marginRight: '8px',
                borderRadius: '2px'
              }
            }}
          >
            {groupName} ({groupPlans.length})
          </Typography>

          <Grid container spacing={viewMode === 'grid' ? 2 : 1} className={styles.planGrid}>
            {groupPlans.map((plan, index) => (
              <Grid item xs={12} sm={viewMode === 'grid' ? 6 : 12} md={viewMode === 'grid' ? 4 : 12} key={index}>
                <PlanCard plan={plan} currentUser={currentUser} index={index} />
              </Grid>
            ))}
          </Grid>
        </Box>
      ))}

      <Box ref={observerRef} className={styles.loadMoreTrigger}></Box>

      {loadingMore && (
        <Box className={styles.loadingMoreIndicator}>
          <Iconify icon="eos-icons:bubble-loading" width={32} height={32} color={mainYellowColor} />
        </Box>
      )}
    </>
  );
};

export default PlanList;
