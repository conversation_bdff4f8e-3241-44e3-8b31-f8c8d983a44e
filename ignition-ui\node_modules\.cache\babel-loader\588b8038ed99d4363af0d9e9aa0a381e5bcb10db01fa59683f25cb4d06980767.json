{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getAutocompleteOptionUtilityClass(slot) {\n  return generateUtilityClass('MuiAutocompleteOption', slot);\n}\nconst autocompleteOptionClasses = generateUtilityClasses('MuiAutocompleteOption', ['root', 'focused', 'focusVisible', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantSoft', 'variantOutlined', 'variantSolid']);\nexport default autocompleteOptionClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getAutocompleteOptionUtilityClass", "slot", "autocompleteOptionClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/AutocompleteOption/autocompleteOptionClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getAutocompleteOptionUtilityClass(slot) {\n  return generateUtilityClass('MuiAutocompleteOption', slot);\n}\nconst autocompleteOptionClasses = generateUtilityClasses('MuiAutocompleteOption', ['root', 'focused', 'focusVisible', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantSoft', 'variantOutlined', 'variantSolid']);\nexport default autocompleteOptionClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,iCAAiCA,CAACC,IAAI,EAAE;EACtD,OAAOH,oBAAoB,CAAC,uBAAuB,EAAEG,IAAI,CAAC;AAC5D;AACA,MAAMC,yBAAyB,GAAGH,sBAAsB,CAAC,uBAAuB,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,iBAAiB,EAAE,cAAc,CAAC,CAAC;AACvR,eAAeG,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}