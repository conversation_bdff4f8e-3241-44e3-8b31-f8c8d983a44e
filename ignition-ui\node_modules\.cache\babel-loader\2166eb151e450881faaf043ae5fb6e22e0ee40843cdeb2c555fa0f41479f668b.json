{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport RowListContext from './RowListContext';\nimport WrapListContext from './WrapListContext';\nimport NestedListContext from './NestedListContext';\n\n/**\n * This variables should be used in a List to create a scope\n * that will not inherit variables from the upper scope.\n *\n * Used in `Menu`, `MenuList`, `TabList`, `Select`, and `Autocomplete` to communicate with nested List.\n *\n * e.g. menu group:\n * <Menu>\n *   <List>...</List>\n *   <List>...</List>\n * </Menu>\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const scopedVariables = {\n  '--NestedList-marginRight': '0px',\n  '--NestedList-marginLeft': '0px',\n  '--NestedListItem-paddingLeft': 'var(--ListItem-paddingX)',\n  // reset ListItem, ListItemButton negative margin (caused by NestedListItem)\n  '--ListItemButton-marginBlock': '0px',\n  '--ListItemButton-marginInline': '0px',\n  '--ListItem-marginBlock': '0px',\n  '--ListItem-marginInline': '0px'\n};\n/**\n * @ignore - internal component.\n */\nfunction ListProvider(props) {\n  const {\n    children,\n    nested,\n    row = false,\n    wrap = false\n  } = props;\n  const baseProviders = /*#__PURE__*/_jsx(RowListContext.Provider, {\n    value: row,\n    children: /*#__PURE__*/_jsx(WrapListContext.Provider, {\n      value: wrap,\n      children: React.Children.map(children, (child, index) => /*#__PURE__*/React.isValidElement(child) ? /*#__PURE__*/React.cloneElement(child, _extends({}, index === 0 && {\n        'data-first-child': ''\n      }, index === React.Children.count(children) - 1 && {\n        'data-last-child': ''\n      })) : child)\n    })\n  });\n  if (nested === undefined) {\n    return baseProviders;\n  }\n  return /*#__PURE__*/_jsx(NestedListContext.Provider, {\n    value: nested,\n    children: baseProviders\n  });\n}\nexport default ListProvider;", "map": {"version": 3, "names": ["_extends", "React", "RowListContext", "WrapListContext", "NestedListContext", "jsx", "_jsx", "scopedVariables", "ListProvider", "props", "children", "nested", "row", "wrap", "baseProviders", "Provider", "value", "Children", "map", "child", "index", "isValidElement", "cloneElement", "count", "undefined"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/List/ListProvider.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport RowListContext from './RowListContext';\nimport WrapListContext from './WrapListContext';\nimport NestedListContext from './NestedListContext';\n\n/**\n * This variables should be used in a List to create a scope\n * that will not inherit variables from the upper scope.\n *\n * Used in `Menu`, `MenuList`, `TabList`, `Select`, and `Autocomplete` to communicate with nested List.\n *\n * e.g. menu group:\n * <Menu>\n *   <List>...</List>\n *   <List>...</List>\n * </Menu>\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const scopedVariables = {\n  '--NestedList-marginRight': '0px',\n  '--NestedList-marginLeft': '0px',\n  '--NestedListItem-paddingLeft': 'var(--ListItem-paddingX)',\n  // reset ListItem, ListItemButton negative margin (caused by NestedListItem)\n  '--ListItemButton-marginBlock': '0px',\n  '--ListItemButton-marginInline': '0px',\n  '--ListItem-marginBlock': '0px',\n  '--ListItem-marginInline': '0px'\n};\n/**\n * @ignore - internal component.\n */\nfunction ListProvider(props) {\n  const {\n    children,\n    nested,\n    row = false,\n    wrap = false\n  } = props;\n  const baseProviders = /*#__PURE__*/_jsx(RowListContext.Provider, {\n    value: row,\n    children: /*#__PURE__*/_jsx(WrapListContext.Provider, {\n      value: wrap,\n      children: React.Children.map(children, (child, index) => /*#__PURE__*/React.isValidElement(child) ? /*#__PURE__*/React.cloneElement(child, _extends({}, index === 0 && {\n        'data-first-child': ''\n      }, index === React.Children.count(children) - 1 && {\n        'data-last-child': ''\n      })) : child)\n    })\n  });\n  if (nested === undefined) {\n    return baseProviders;\n  }\n  return /*#__PURE__*/_jsx(NestedListContext.Provider, {\n    value: nested,\n    children: baseProviders\n  });\n}\nexport default ListProvider;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,iBAAiB,MAAM,qBAAqB;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,MAAMC,eAAe,GAAG;EAC7B,0BAA0B,EAAE,KAAK;EACjC,yBAAyB,EAAE,KAAK;EAChC,8BAA8B,EAAE,0BAA0B;EAC1D;EACA,8BAA8B,EAAE,KAAK;EACrC,+BAA+B,EAAE,KAAK;EACtC,wBAAwB,EAAE,KAAK;EAC/B,yBAAyB,EAAE;AAC7B,CAAC;AACD;AACA;AACA;AACA,SAASC,YAAYA,CAACC,KAAK,EAAE;EAC3B,MAAM;IACJC,QAAQ;IACRC,MAAM;IACNC,GAAG,GAAG,KAAK;IACXC,IAAI,GAAG;EACT,CAAC,GAAGJ,KAAK;EACT,MAAMK,aAAa,GAAG,aAAaR,IAAI,CAACJ,cAAc,CAACa,QAAQ,EAAE;IAC/DC,KAAK,EAAEJ,GAAG;IACVF,QAAQ,EAAE,aAAaJ,IAAI,CAACH,eAAe,CAACY,QAAQ,EAAE;MACpDC,KAAK,EAAEH,IAAI;MACXH,QAAQ,EAAET,KAAK,CAACgB,QAAQ,CAACC,GAAG,CAACR,QAAQ,EAAE,CAACS,KAAK,EAAEC,KAAK,KAAK,aAAanB,KAAK,CAACoB,cAAc,CAACF,KAAK,CAAC,GAAG,aAAalB,KAAK,CAACqB,YAAY,CAACH,KAAK,EAAEnB,QAAQ,CAAC,CAAC,CAAC,EAAEoB,KAAK,KAAK,CAAC,IAAI;QACrK,kBAAkB,EAAE;MACtB,CAAC,EAAEA,KAAK,KAAKnB,KAAK,CAACgB,QAAQ,CAACM,KAAK,CAACb,QAAQ,CAAC,GAAG,CAAC,IAAI;QACjD,iBAAiB,EAAE;MACrB,CAAC,CAAC,CAAC,GAAGS,KAAK;IACb,CAAC;EACH,CAAC,CAAC;EACF,IAAIR,MAAM,KAAKa,SAAS,EAAE;IACxB,OAAOV,aAAa;EACtB;EACA,OAAO,aAAaR,IAAI,CAACF,iBAAiB,CAACW,QAAQ,EAAE;IACnDC,KAAK,EAAEL,MAAM;IACbD,QAAQ,EAAEI;EACZ,CAAC,CAAC;AACJ;AACA,eAAeN,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}