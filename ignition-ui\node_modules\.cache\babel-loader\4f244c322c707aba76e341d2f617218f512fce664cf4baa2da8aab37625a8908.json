{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getTableUtilityClass(slot) {\n  return generateUtilityClass('MuiTable', slot);\n}\nconst tableClasses = generateUtilityClasses('MuiTable', ['root', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid', 'sizeSm', 'sizeMd', 'sizeLg', 'stickyHeader', 'stickyFooter', 'noWrap', 'hoverRow', 'borderAxisNone', 'borderAxisX', 'borderAxisXBetween', 'borderAxisY', 'borderAxisYBetween', 'borderAxisBoth', 'borderAxisBothBetween']);\nexport default tableClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getTableUtilityClass", "slot", "tableClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Table/tableClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getTableUtilityClass(slot) {\n  return generateUtilityClass('MuiTable', slot);\n}\nconst tableClasses = generateUtilityClasses('MuiTable', ['root', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid', 'sizeSm', 'sizeMd', 'sizeLg', 'stickyHeader', 'stickyFooter', 'noWrap', 'hoverRow', 'borderAxisNone', 'borderAxisX', 'borderAxisXBetween', 'borderAxisY', 'borderAxisYBetween', 'borderAxisBoth', 'borderAxisBothBetween']);\nexport default tableClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,oBAAoBA,CAACC,IAAI,EAAE;EACzC,OAAOH,oBAAoB,CAAC,UAAU,EAAEG,IAAI,CAAC;AAC/C;AACA,MAAMC,YAAY,GAAGH,sBAAsB,CAAC,UAAU,EAAE,CAAC,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,iBAAiB,EAAE,aAAa,EAAE,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,cAAc,EAAE,cAAc,EAAE,QAAQ,EAAE,UAAU,EAAE,gBAAgB,EAAE,aAAa,EAAE,oBAAoB,EAAE,aAAa,EAAE,oBAAoB,EAAE,gBAAgB,EAAE,uBAAuB,CAAC,CAAC;AAC7b,eAAeG,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}