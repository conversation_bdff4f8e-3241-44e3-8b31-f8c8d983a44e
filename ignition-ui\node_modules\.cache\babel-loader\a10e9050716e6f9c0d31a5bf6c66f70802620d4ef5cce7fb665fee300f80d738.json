{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getCardCoverUtilityClass(slot) {\n  return generateUtilityClass('MuiCardCover', slot);\n}\nconst cardCoverClasses = generateUtilityClasses('MuiCardCover', ['root']);\nexport default cardCoverClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getCardCoverUtilityClass", "slot", "cardCoverClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/CardCover/cardCoverClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getCardCoverUtilityClass(slot) {\n  return generateUtilityClass('MuiCardCover', slot);\n}\nconst cardCoverClasses = generateUtilityClasses('MuiCardCover', ['root']);\nexport default cardCoverClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,wBAAwBA,CAACC,IAAI,EAAE;EAC7C,OAAOH,oBAAoB,CAAC,cAAc,EAAEG,IAAI,CAAC;AACnD;AACA,MAAMC,gBAAgB,GAAGH,sBAAsB,CAAC,cAAc,EAAE,CAAC,MAAM,CAAC,CAAC;AACzE,eAAeG,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}