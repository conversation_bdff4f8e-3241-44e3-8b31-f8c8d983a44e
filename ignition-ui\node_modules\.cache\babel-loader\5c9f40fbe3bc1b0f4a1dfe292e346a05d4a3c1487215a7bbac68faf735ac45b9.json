{"ast": null, "code": "'use client';\n\nimport PropTypes from 'prop-types';\nimport { createGrid } from '@mui/system/Unstable_Grid';\nimport { styled, useThemeProps } from '../styles';\n/**\n *\n * Demos:\n *\n * - [Grid](https://mui.com/joy-ui/react-grid/)\n *\n * API:\n *\n * - [Grid API](https://mui.com/joy-ui/api/grid/)\n */\nconst Grid = createGrid({\n  createStyledComponent: styled('div', {\n    name: 'JoyGrid',\n    slot: 'Root',\n    overridesResolver: (props, styles) => styles.root\n  }),\n  useThemeProps: inProps => useThemeProps({\n    props: inProps,\n    name: 'JoyGrid'\n  })\n});\nprocess.env.NODE_ENV !== \"production\" ? Grid.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Grid;", "map": {"version": 3, "names": ["PropTypes", "createGrid", "styled", "useThemeProps", "Grid", "createStyledComponent", "name", "slot", "overridesResolver", "props", "styles", "root", "inProps", "process", "env", "NODE_ENV", "propTypes", "children", "node", "sx", "oneOfType", "arrayOf", "func", "object", "bool"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Grid/Grid.js"], "sourcesContent": ["'use client';\n\nimport PropTypes from 'prop-types';\nimport { createGrid } from '@mui/system/Unstable_Grid';\nimport { styled, useThemeProps } from '../styles';\n/**\n *\n * Demos:\n *\n * - [Grid](https://mui.com/joy-ui/react-grid/)\n *\n * API:\n *\n * - [Grid API](https://mui.com/joy-ui/api/grid/)\n */\nconst Grid = createGrid({\n  createStyledComponent: styled('div', {\n    name: 'JoyGrid',\n    slot: 'Root',\n    overridesResolver: (props, styles) => styles.root\n  }),\n  useThemeProps: inProps => useThemeProps({\n    props: inProps,\n    name: 'JoyGrid'\n  })\n});\nprocess.env.NODE_ENV !== \"production\" ? Grid.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Grid;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,SAAS,MAAM,YAAY;AAClC,SAASC,UAAU,QAAQ,2BAA2B;AACtD,SAASC,MAAM,EAAEC,aAAa,QAAQ,WAAW;AACjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,IAAI,GAAGH,UAAU,CAAC;EACtBI,qBAAqB,EAAEH,MAAM,CAAC,KAAK,EAAE;IACnCI,IAAI,EAAE,SAAS;IACfC,IAAI,EAAE,MAAM;IACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACC;EAC/C,CAAC,CAAC;EACFR,aAAa,EAAES,OAAO,IAAIT,aAAa,CAAC;IACtCM,KAAK,EAAEG,OAAO;IACdN,IAAI,EAAE;EACR,CAAC;AACH,CAAC,CAAC;AACFO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGX,IAAI,CAACY,SAAS,CAAC,yBAAyB;EAC9E;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAEjB,SAAS,CAACkB,IAAI;EACxB;AACF;AACA;EACEC,EAAE,EAAEnB,SAAS,CAACoB,SAAS,CAAC,CAACpB,SAAS,CAACqB,OAAO,CAACrB,SAAS,CAACoB,SAAS,CAAC,CAACpB,SAAS,CAACsB,IAAI,EAAEtB,SAAS,CAACuB,MAAM,EAAEvB,SAAS,CAACwB,IAAI,CAAC,CAAC,CAAC,EAAExB,SAAS,CAACsB,IAAI,EAAEtB,SAAS,CAACuB,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAenB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}