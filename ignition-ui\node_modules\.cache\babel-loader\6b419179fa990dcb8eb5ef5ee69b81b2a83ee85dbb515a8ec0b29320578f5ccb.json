{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getSheetUtilityClass(slot) {\n  return generateUtilityClass('MuiSheet', slot);\n}\nconst sheetClasses = generateUtilityClasses('MuiSheet', ['root', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid']);\nexport default sheetClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getSheetUtilityClass", "slot", "sheetClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Sheet/sheetClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getSheetUtilityClass(slot) {\n  return generateUtilityClass('MuiSheet', slot);\n}\nconst sheetClasses = generateUtilityClasses('MuiSheet', ['root', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid']);\nexport default sheetClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,oBAAoBA,CAACC,IAAI,EAAE;EACzC,OAAOH,oBAAoB,CAAC,UAAU,EAAEG,IAAI,CAAC;AAC/C;AACA,MAAMC,YAAY,GAAGH,sBAAsB,CAAC,UAAU,EAAE,CAAC,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,iBAAiB,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;AAClO,eAAeG,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}