{"ast": null, "code": "/**\r\n * @param {any} obj The object to inspect.\r\n * @returns {boolean} True if the argument appears to be a plain object.\r\n */\nexport default function isPlainObject(obj) {\n  if (typeof obj !== 'object' || obj === null) return false;\n  var proto = Object.getPrototypeOf(obj);\n  if (proto === null) return true;\n  var baseProto = proto;\n  while (Object.getPrototypeOf(baseProto) !== null) {\n    baseProto = Object.getPrototypeOf(baseProto);\n  }\n  return proto === baseProto;\n}", "map": {"version": 3, "names": ["isPlainObject", "obj", "proto", "Object", "getPrototypeOf", "baseProto"], "sources": ["C:/ignition/ignition-ui/node_modules/react-beautiful-dnd/node_modules/react-redux/es/utils/isPlainObject.js"], "sourcesContent": ["/**\r\n * @param {any} obj The object to inspect.\r\n * @returns {boolean} True if the argument appears to be a plain object.\r\n */\nexport default function isPlainObject(obj) {\n  if (typeof obj !== 'object' || obj === null) return false;\n  var proto = Object.getPrototypeOf(obj);\n  if (proto === null) return true;\n  var baseProto = proto;\n\n  while (Object.getPrototypeOf(baseProto) !== null) {\n    baseProto = Object.getPrototypeOf(baseProto);\n  }\n\n  return proto === baseProto;\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA,eAAe,SAASA,aAAaA,CAACC,GAAG,EAAE;EACzC,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,EAAE,OAAO,KAAK;EACzD,IAAIC,KAAK,GAAGC,MAAM,CAACC,cAAc,CAACH,GAAG,CAAC;EACtC,IAAIC,KAAK,KAAK,IAAI,EAAE,OAAO,IAAI;EAC/B,IAAIG,SAAS,GAAGH,KAAK;EAErB,OAAOC,MAAM,CAACC,cAAc,CAACC,SAAS,CAAC,KAAK,IAAI,EAAE;IAChDA,SAAS,GAAGF,MAAM,CAACC,cAAc,CAACC,SAAS,CAAC;EAC9C;EAEA,OAAOH,KAAK,KAAKG,SAAS;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}