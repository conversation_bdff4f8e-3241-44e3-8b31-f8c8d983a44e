{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getSkeletonUtilityClass(slot) {\n  return generateUtilityClass('MuiSkeleton', slot);\n}\nconst skeletonClasses = generateUtilityClasses('MuiSkeleton', ['root', 'variantOverlay', 'variantCircular', 'variantRectangular', 'variantText', 'variantInline', 'h1', 'h2', 'h3', 'h4', 'title-lg', 'title-md', 'title-sm', 'body-lg', 'body-md', 'body-sm', 'body-xs']);\nexport default skeletonClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getSkeletonUtilityClass", "slot", "skeletonClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Skeleton/skeletonClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getSkeletonUtilityClass(slot) {\n  return generateUtilityClass('MuiSkeleton', slot);\n}\nconst skeletonClasses = generateUtilityClasses('MuiSkeleton', ['root', 'variantOverlay', 'variantCircular', 'variantRectangular', 'variantText', 'variantInline', 'h1', 'h2', 'h3', 'h4', 'title-lg', 'title-md', 'title-sm', 'body-lg', 'body-md', 'body-sm', 'body-xs']);\nexport default skeletonClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,uBAAuBA,CAACC,IAAI,EAAE;EAC5C,OAAOH,oBAAoB,CAAC,aAAa,EAAEG,IAAI,CAAC;AAClD;AACA,MAAMC,eAAe,GAAGH,sBAAsB,CAAC,aAAa,EAAE,CAAC,MAAM,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,aAAa,EAAE,eAAe,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;AAC1Q,eAAeG,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}