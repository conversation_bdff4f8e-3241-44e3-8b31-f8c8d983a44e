import React, { useEffect, useRef } from 'react';
import { Box } from '@mui/material';
import Matter from 'matter-js';
import styles from './styles.module.scss';

/**
 * Component displays loading screen with a bouncing ball inside a rotating hexagon
 * @param {Object} props - Component props
 * @param {boolean} props.fromCreatePlan - Flag to determine if component is called from CreatePlan page
 * @returns {JSX.Element} - HexagonBallLoading component
 */
const HexagonBallLoading = ({ fromCreatePlan }) => {
  const canvasRef = useRef(null);
  const engineRef = useRef(null);
  const requestRef = useRef(null);
  const ballRef = useRef(null);
  const hexagonEdgesRef = useRef([]);

  useEffect(() => {
    // Set optimal parameters for natural motion
    const rotationSpeed = 0.03; // Moderate rotation speed
    const gravity = 0.001; // Sufficient gravity
    const restitution = 0.9; // High bounce for continuous ball movement

    // Initialize Matter.js modules
    const Engine = Matter.Engine;
    const Render = Matter.Render;
    const World = Matter.World;
    const Bodies = Matter.Bodies;
    const Body = Matter.Body;

    // Create engine
    engineRef.current = Engine.create({
      gravity: { x: 0, y: gravity, scale: 1 },
    });

    // Create renderer
    const render = Render.create({
      canvas: canvasRef.current,
      engine: engineRef.current,
      options: {
        width: 352,
        height: 352,
        wireframes: false,
        background: '#000000',
        showAngleIndicator: false,
        showCollisions: false,
        showVelocity: false,
      },
    });

    // Create hexagon
    const hexagonRadius = 132;
    const hexagonSides = 6;
    const centerX = render.options.width / 2;
    const centerY = render.options.height / 2;

    // Create hexagon vertices
    const hexagonVertices = [];
    for (let i = 0; i < hexagonSides; i++) {
      const angle = (Math.PI * 2 * i) / hexagonSides;
      const x = hexagonRadius * Math.cos(angle);
      const y = hexagonRadius * Math.sin(angle);
      hexagonVertices.push({ x, y });
    }

    // Create hexagon edges
    hexagonEdgesRef.current = [];
    for (let i = 0; i < hexagonSides; i++) {
      const j = (i + 1) % hexagonSides;
      const edgeOptions = {
        restitution: 0.7,
        friction: 0.1,
        isStatic: true,
        render: {
          fillStyle: 'transparent',
          strokeStyle: '#FFFFFF',
          lineWidth: 1,
        },
      };

      // Calculate edge position and angle
      const vertex1 = hexagonVertices[i];
      const vertex2 = hexagonVertices[j];
      const edgeLength = Math.sqrt(
        Math.pow(vertex2.x - vertex1.x, 2) + Math.pow(vertex2.y - vertex1.y, 2)
      );
      const edgeAngle = Math.atan2(vertex2.y - vertex1.y, vertex2.x - vertex1.x);
      const edgeCenterX = centerX + (vertex1.x + vertex2.x) / 2;
      const edgeCenterY = centerY + (vertex1.y + vertex2.y) / 2;

      // Create edge
      const edge = Bodies.rectangle(
        edgeCenterX,
        edgeCenterY,
        edgeLength,
        1,
        edgeOptions
      );

      // Rotate edge to angle
      Body.rotate(edge, edgeAngle);

      hexagonEdgesRef.current.push(edge);
    }

    // Create ball
    const originalBallRadius = 15;
    // Reduce by additional 10% from current size (72% * 0.9 = 64.8%)
    const ballRadius = originalBallRadius * 0.648; // 72% * 0.9 = 64.8% of original size

    // Place ball inside hexagon, closer to center
    ballRef.current = Bodies.circle(centerX, centerY, ballRadius, {
      restitution: restitution,
      friction: 0.05,
      frictionAir: 0.001,
      density: 0.001,
      render: {
        fillStyle: '#F0A500',
      },
    });

    // Add initial force to start ball movement
    Body.applyForce(ballRef.current, ballRef.current.position, {
      x: 0.0002,
      y: -0.0002
    });

    // Add bodies to world
    World.add(engineRef.current.world, [...hexagonEdgesRef.current, ballRef.current]);

    // Run renderer
    Render.run(render);

    // Animation loop function
    const animate = () => {
      // Update engine
      Engine.update(engineRef.current, 16.667);

      // Rotate hexagon edges
      if (hexagonEdgesRef.current.length > 0) {
        hexagonEdgesRef.current.forEach(edge => {
          // Rotate edge around hexagon center
          Body.setPosition(edge, {
            x: centerX + (edge.position.x - centerX) * Math.cos(rotationSpeed) - (edge.position.y - centerY) * Math.sin(rotationSpeed),
            y: centerY + (edge.position.x - centerX) * Math.sin(rotationSpeed) + (edge.position.y - centerY) * Math.cos(rotationSpeed)
          });
          Body.rotate(edge, rotationSpeed);
        });
      }

      // Check ball position
      if (ballRef.current) {
        const ballPos = ballRef.current.position;

        // Calculate distance from ball to hexagon center
        const distanceFromCenter = Math.sqrt(
          Math.pow(ballPos.x - centerX, 2) + Math.pow(ballPos.y - centerY, 2)
        );

        // If ball goes beyond hexagon safe boundary
        const safeHexagonRadius = hexagonRadius * 0.85; // Safe boundary inside hexagon

        if (distanceFromCenter > safeHexagonRadius) {
          // Calculate vector from hexagon center to ball
          const directionX = ballPos.x - centerX;
          const directionY = ballPos.y - centerY;
          const magnitude = Math.sqrt(directionX * directionX + directionY * directionY);

          // Normalize vector
          const normalizedX = directionX / magnitude;
          const normalizedY = directionY / magnitude;

          // Reset ball position to within safe boundary
          const newPosX = centerX + normalizedX * safeHexagonRadius;
          const newPosY = centerY + normalizedY * safeHexagonRadius;

          // Set new ball position
          Matter.Body.setPosition(ballRef.current, {
            x: newPosX,
            y: newPosY
          });

          // Adjust velocity for ball to bounce inward
          const currentVelocity = ballRef.current.velocity;
          const dotProduct =
            currentVelocity.x * normalizedX +
            currentVelocity.y * normalizedY;

          // If ball is moving outward, reverse direction
          if (dotProduct > 0) {
            const newVelocityX = currentVelocity.x - 2 * dotProduct * normalizedX;
            const newVelocityY = currentVelocity.y - 2 * dotProduct * normalizedY;

            Matter.Body.setVelocity(ballRef.current, {
              x: newVelocityX * restitution,
              y: newVelocityY * restitution
            });
          }
        }

        // Ensure ball keeps moving
        const velocity = ballRef.current.velocity;
        const speed = Math.sqrt(velocity.x * velocity.x + velocity.y * velocity.y);

        // If ball moves too slowly, add small force
        if (speed < 0.5) {
          const randomAngle = Math.random() * Math.PI * 2;
          const forceMagnitude = 0.0001;
          Body.applyForce(ballRef.current, ballRef.current.position, {
            x: forceMagnitude * Math.cos(randomAngle),
            y: forceMagnitude * Math.sin(randomAngle)
          });
        }
      }

      // Continue animation loop
      requestRef.current = requestAnimationFrame(animate);
    };

    // Start animation loop
    requestRef.current = requestAnimationFrame(animate);

    // Cleanup when component unmounts
    return () => {
      // Cancel animation loop
      if (requestRef.current) {
        cancelAnimationFrame(requestRef.current);
      }

      // Cleanup renderer and engine
      Render.stop(render);
      World.clear(engineRef.current.world);
      Engine.clear(engineRef.current);
      render.canvas = null;
      render.context = null;
      render.textures = {};
    };
  }, []);

  return (
    <Box className={styles.loadingContainer}
      sx={{ ...(!fromCreatePlan && { minHeight: '90vh' }) }}>
      <Box className={styles.gameWrapper}>
        <Box className={styles.hexagonLoadingContainer}>
          <canvas ref={canvasRef} className={styles.hexagonCanvas} />
        </Box>
      </Box>
    </Box>
  );
};

export default HexagonBallLoading;
