{"ast": null, "code": "'use strict';\n\nexports.__esModule = true;\nvar _react = require('react');\nvar _react2 = _interopRequireDefault(_react);\nvar _propTypes = require('prop-types');\nvar _propTypes2 = _interopRequireDefault(_propTypes);\nvar _gud = require('gud');\nvar _gud2 = _interopRequireDefault(_gud);\nvar _warning = require('warning');\nvar _warning2 = _interopRequireDefault(_warning);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (!self) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      enumerable: false,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n}\nvar MAX_SIGNED_31_BIT_INT = 1073741823;\n\n// Inlined Object.is polyfill.\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\nfunction objectIs(x, y) {\n  if (x === y) {\n    return x !== 0 || 1 / x === 1 / y;\n  } else {\n    return x !== x && y !== y;\n  }\n}\nfunction createEventEmitter(value) {\n  var handlers = [];\n  return {\n    on: function on(handler) {\n      handlers.push(handler);\n    },\n    off: function off(handler) {\n      handlers = handlers.filter(function (h) {\n        return h !== handler;\n      });\n    },\n    get: function get() {\n      return value;\n    },\n    set: function set(newValue, changedBits) {\n      value = newValue;\n      handlers.forEach(function (handler) {\n        return handler(value, changedBits);\n      });\n    }\n  };\n}\nfunction onlyChild(children) {\n  return Array.isArray(children) ? children[0] : children;\n}\nfunction createReactContext(defaultValue, calculateChangedBits) {\n  var _Provider$childContex, _Consumer$contextType;\n  var contextProp = '__create-react-context-' + (0, _gud2.default)() + '__';\n  var Provider = function (_Component) {\n    _inherits(Provider, _Component);\n    function Provider() {\n      var _temp, _this, _ret;\n      _classCallCheck(this, Provider);\n      for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      return _ret = (_temp = (_this = _possibleConstructorReturn(this, _Component.call.apply(_Component, [this].concat(args))), _this), _this.emitter = createEventEmitter(_this.props.value), _temp), _possibleConstructorReturn(_this, _ret);\n    }\n    Provider.prototype.getChildContext = function getChildContext() {\n      var _ref;\n      return _ref = {}, _ref[contextProp] = this.emitter, _ref;\n    };\n    Provider.prototype.componentWillReceiveProps = function componentWillReceiveProps(nextProps) {\n      if (this.props.value !== nextProps.value) {\n        var oldValue = this.props.value;\n        var newValue = nextProps.value;\n        var changedBits = void 0;\n        if (objectIs(oldValue, newValue)) {\n          changedBits = 0; // No change\n        } else {\n          changedBits = typeof calculateChangedBits === 'function' ? calculateChangedBits(oldValue, newValue) : MAX_SIGNED_31_BIT_INT;\n          if (process.env.NODE_ENV !== 'production') {\n            (0, _warning2.default)((changedBits & MAX_SIGNED_31_BIT_INT) === changedBits, 'calculateChangedBits: Expected the return value to be a ' + '31-bit integer. Instead received: %s', changedBits);\n          }\n          changedBits |= 0;\n          if (changedBits !== 0) {\n            this.emitter.set(nextProps.value, changedBits);\n          }\n        }\n      }\n    };\n    Provider.prototype.render = function render() {\n      return this.props.children;\n    };\n    return Provider;\n  }(_react.Component);\n  Provider.childContextTypes = (_Provider$childContex = {}, _Provider$childContex[contextProp] = _propTypes2.default.object.isRequired, _Provider$childContex);\n  var Consumer = function (_Component2) {\n    _inherits(Consumer, _Component2);\n    function Consumer() {\n      var _temp2, _this2, _ret2;\n      _classCallCheck(this, Consumer);\n      for (var _len2 = arguments.length, args = Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      return _ret2 = (_temp2 = (_this2 = _possibleConstructorReturn(this, _Component2.call.apply(_Component2, [this].concat(args))), _this2), _this2.state = {\n        value: _this2.getValue()\n      }, _this2.onUpdate = function (newValue, changedBits) {\n        var observedBits = _this2.observedBits | 0;\n        if ((observedBits & changedBits) !== 0) {\n          _this2.setState({\n            value: _this2.getValue()\n          });\n        }\n      }, _temp2), _possibleConstructorReturn(_this2, _ret2);\n    }\n    Consumer.prototype.componentWillReceiveProps = function componentWillReceiveProps(nextProps) {\n      var observedBits = nextProps.observedBits;\n      this.observedBits = observedBits === undefined || observedBits === null ? MAX_SIGNED_31_BIT_INT // Subscribe to all changes by default\n      : observedBits;\n    };\n    Consumer.prototype.componentDidMount = function componentDidMount() {\n      if (this.context[contextProp]) {\n        this.context[contextProp].on(this.onUpdate);\n      }\n      var observedBits = this.props.observedBits;\n      this.observedBits = observedBits === undefined || observedBits === null ? MAX_SIGNED_31_BIT_INT // Subscribe to all changes by default\n      : observedBits;\n    };\n    Consumer.prototype.componentWillUnmount = function componentWillUnmount() {\n      if (this.context[contextProp]) {\n        this.context[contextProp].off(this.onUpdate);\n      }\n    };\n    Consumer.prototype.getValue = function getValue() {\n      if (this.context[contextProp]) {\n        return this.context[contextProp].get();\n      } else {\n        return defaultValue;\n      }\n    };\n    Consumer.prototype.render = function render() {\n      return onlyChild(this.props.children)(this.state.value);\n    };\n    return Consumer;\n  }(_react.Component);\n  Consumer.contextTypes = (_Consumer$contextType = {}, _Consumer$contextType[contextProp] = _propTypes2.default.object, _Consumer$contextType);\n  return {\n    Provider: Provider,\n    Consumer: Consumer\n  };\n}\nexports.default = createReactContext;\nmodule.exports = exports['default'];", "map": {"version": 3, "names": ["exports", "__esModule", "_react", "require", "_react2", "_interopRequireDefault", "_propTypes", "_propTypes2", "_gud", "_gud2", "_warning", "_warning2", "obj", "default", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_possibleConstructorReturn", "self", "call", "ReferenceError", "_inherits", "subClass", "superClass", "prototype", "Object", "create", "constructor", "value", "enumerable", "writable", "configurable", "setPrototypeOf", "__proto__", "MAX_SIGNED_31_BIT_INT", "objectIs", "x", "y", "createEventEmitter", "handlers", "on", "handler", "push", "off", "filter", "h", "get", "set", "newValue", "changedBits", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON>", "children", "Array", "isArray", "createReactContext", "defaultValue", "calculateChangedBits", "_Provider$childContex", "_Consumer$contextType", "contextProp", "Provider", "_Component", "_temp", "_this", "_ret", "_len", "arguments", "length", "args", "_key", "apply", "concat", "emitter", "props", "getChildContext", "_ref", "componentWillReceiveProps", "nextProps", "oldValue", "process", "env", "NODE_ENV", "render", "Component", "childContextTypes", "object", "isRequired", "Consumer", "_Component2", "_temp2", "_this2", "_ret2", "_len2", "_key2", "state", "getValue", "onUpdate", "observedBits", "setState", "undefined", "componentDidMount", "context", "componentWillUnmount", "contextTypes", "module"], "sources": ["C:/ignition/ignition-ui/node_modules/@hypnosphi/create-react-context/lib/implementation.js"], "sourcesContent": ["'use strict';\n\nexports.__esModule = true;\n\nvar _react = require('react');\n\nvar _react2 = _interopRequireDefault(_react);\n\nvar _propTypes = require('prop-types');\n\nvar _propTypes2 = _interopRequireDefault(_propTypes);\n\nvar _gud = require('gud');\n\nvar _gud2 = _interopRequireDefault(_gud);\n\nvar _warning = require('warning');\n\nvar _warning2 = _interopRequireDefault(_warning);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\n\nvar MAX_SIGNED_31_BIT_INT = 1073741823;\n\n// Inlined Object.is polyfill.\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Object/is\nfunction objectIs(x, y) {\n  if (x === y) {\n    return x !== 0 || 1 / x === 1 / y;\n  } else {\n    return x !== x && y !== y;\n  }\n}\n\nfunction createEventEmitter(value) {\n  var handlers = [];\n  return {\n    on: function on(handler) {\n      handlers.push(handler);\n    },\n    off: function off(handler) {\n      handlers = handlers.filter(function (h) {\n        return h !== handler;\n      });\n    },\n    get: function get() {\n      return value;\n    },\n    set: function set(newValue, changedBits) {\n      value = newValue;\n      handlers.forEach(function (handler) {\n        return handler(value, changedBits);\n      });\n    }\n  };\n}\n\nfunction onlyChild(children) {\n  return Array.isArray(children) ? children[0] : children;\n}\n\nfunction createReactContext(defaultValue, calculateChangedBits) {\n  var _Provider$childContex, _Consumer$contextType;\n\n  var contextProp = '__create-react-context-' + (0, _gud2.default)() + '__';\n\n  var Provider = function (_Component) {\n    _inherits(Provider, _Component);\n\n    function Provider() {\n      var _temp, _this, _ret;\n\n      _classCallCheck(this, Provider);\n\n      for (var _len = arguments.length, args = Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n\n      return _ret = (_temp = (_this = _possibleConstructorReturn(this, _Component.call.apply(_Component, [this].concat(args))), _this), _this.emitter = createEventEmitter(_this.props.value), _temp), _possibleConstructorReturn(_this, _ret);\n    }\n\n    Provider.prototype.getChildContext = function getChildContext() {\n      var _ref;\n\n      return _ref = {}, _ref[contextProp] = this.emitter, _ref;\n    };\n\n    Provider.prototype.componentWillReceiveProps = function componentWillReceiveProps(nextProps) {\n      if (this.props.value !== nextProps.value) {\n        var oldValue = this.props.value;\n        var newValue = nextProps.value;\n        var changedBits = void 0;\n\n        if (objectIs(oldValue, newValue)) {\n          changedBits = 0; // No change\n        } else {\n          changedBits = typeof calculateChangedBits === 'function' ? calculateChangedBits(oldValue, newValue) : MAX_SIGNED_31_BIT_INT;\n          if (process.env.NODE_ENV !== 'production') {\n            (0, _warning2.default)((changedBits & MAX_SIGNED_31_BIT_INT) === changedBits, 'calculateChangedBits: Expected the return value to be a ' + '31-bit integer. Instead received: %s', changedBits);\n          }\n\n          changedBits |= 0;\n\n          if (changedBits !== 0) {\n            this.emitter.set(nextProps.value, changedBits);\n          }\n        }\n      }\n    };\n\n    Provider.prototype.render = function render() {\n      return this.props.children;\n    };\n\n    return Provider;\n  }(_react.Component);\n\n  Provider.childContextTypes = (_Provider$childContex = {}, _Provider$childContex[contextProp] = _propTypes2.default.object.isRequired, _Provider$childContex);\n\n  var Consumer = function (_Component2) {\n    _inherits(Consumer, _Component2);\n\n    function Consumer() {\n      var _temp2, _this2, _ret2;\n\n      _classCallCheck(this, Consumer);\n\n      for (var _len2 = arguments.length, args = Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n\n      return _ret2 = (_temp2 = (_this2 = _possibleConstructorReturn(this, _Component2.call.apply(_Component2, [this].concat(args))), _this2), _this2.state = {\n        value: _this2.getValue()\n      }, _this2.onUpdate = function (newValue, changedBits) {\n        var observedBits = _this2.observedBits | 0;\n        if ((observedBits & changedBits) !== 0) {\n          _this2.setState({ value: _this2.getValue() });\n        }\n      }, _temp2), _possibleConstructorReturn(_this2, _ret2);\n    }\n\n    Consumer.prototype.componentWillReceiveProps = function componentWillReceiveProps(nextProps) {\n      var observedBits = nextProps.observedBits;\n\n      this.observedBits = observedBits === undefined || observedBits === null ? MAX_SIGNED_31_BIT_INT // Subscribe to all changes by default\n      : observedBits;\n    };\n\n    Consumer.prototype.componentDidMount = function componentDidMount() {\n      if (this.context[contextProp]) {\n        this.context[contextProp].on(this.onUpdate);\n      }\n      var observedBits = this.props.observedBits;\n\n      this.observedBits = observedBits === undefined || observedBits === null ? MAX_SIGNED_31_BIT_INT // Subscribe to all changes by default\n      : observedBits;\n    };\n\n    Consumer.prototype.componentWillUnmount = function componentWillUnmount() {\n      if (this.context[contextProp]) {\n        this.context[contextProp].off(this.onUpdate);\n      }\n    };\n\n    Consumer.prototype.getValue = function getValue() {\n      if (this.context[contextProp]) {\n        return this.context[contextProp].get();\n      } else {\n        return defaultValue;\n      }\n    };\n\n    Consumer.prototype.render = function render() {\n      return onlyChild(this.props.children)(this.state.value);\n    };\n\n    return Consumer;\n  }(_react.Component);\n\n  Consumer.contextTypes = (_Consumer$contextType = {}, _Consumer$contextType[contextProp] = _propTypes2.default.object, _Consumer$contextType);\n\n\n  return {\n    Provider: Provider,\n    Consumer: Consumer\n  };\n}\n\nexports.default = createReactContext;\nmodule.exports = exports['default'];"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AAEzB,IAAIC,MAAM,GAAGC,OAAO,CAAC,OAAO,CAAC;AAE7B,IAAIC,OAAO,GAAGC,sBAAsB,CAACH,MAAM,CAAC;AAE5C,IAAII,UAAU,GAAGH,OAAO,CAAC,YAAY,CAAC;AAEtC,IAAII,WAAW,GAAGF,sBAAsB,CAACC,UAAU,CAAC;AAEpD,IAAIE,IAAI,GAAGL,OAAO,CAAC,KAAK,CAAC;AAEzB,IAAIM,KAAK,GAAGJ,sBAAsB,CAACG,IAAI,CAAC;AAExC,IAAIE,QAAQ,GAAGP,OAAO,CAAC,SAAS,CAAC;AAEjC,IAAIQ,SAAS,GAAGN,sBAAsB,CAACK,QAAQ,CAAC;AAEhD,SAASL,sBAAsBA,CAACO,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACX,UAAU,GAAGW,GAAG,GAAG;IAAEC,OAAO,EAAED;EAAI,CAAC;AAAE;AAE9F,SAASE,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;EAAE,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AAExJ,SAASC,0BAA0BA,CAACC,IAAI,EAAEC,IAAI,EAAE;EAAE,IAAI,CAACD,IAAI,EAAE;IAAE,MAAM,IAAIE,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAOD,IAAI,KAAK,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,GAAGA,IAAI,GAAGD,IAAI;AAAE;AAE/O,SAASG,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAIP,SAAS,CAAC,0DAA0D,GAAG,OAAOO,UAAU,CAAC;EAAE;EAAED,QAAQ,CAACE,SAAS,GAAGC,MAAM,CAACC,MAAM,CAACH,UAAU,IAAIA,UAAU,CAACC,SAAS,EAAE;IAAEG,WAAW,EAAE;MAAEC,KAAK,EAAEN,QAAQ;MAAEO,UAAU,EAAE,KAAK;MAAEC,QAAQ,EAAE,IAAI;MAAEC,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAE,IAAIR,UAAU,EAAEE,MAAM,CAACO,cAAc,GAAGP,MAAM,CAACO,cAAc,CAACV,QAAQ,EAAEC,UAAU,CAAC,GAAGD,QAAQ,CAACW,SAAS,GAAGV,UAAU;AAAE;AAE7e,IAAIW,qBAAqB,GAAG,UAAU;;AAEtC;AACA;AACA,SAASC,QAAQA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACtB,IAAID,CAAC,KAAKC,CAAC,EAAE;IACX,OAAOD,CAAC,KAAK,CAAC,IAAI,CAAC,GAAGA,CAAC,KAAK,CAAC,GAAGC,CAAC;EACnC,CAAC,MAAM;IACL,OAAOD,CAAC,KAAKA,CAAC,IAAIC,CAAC,KAAKA,CAAC;EAC3B;AACF;AAEA,SAASC,kBAAkBA,CAACV,KAAK,EAAE;EACjC,IAAIW,QAAQ,GAAG,EAAE;EACjB,OAAO;IACLC,EAAE,EAAE,SAASA,EAAEA,CAACC,OAAO,EAAE;MACvBF,QAAQ,CAACG,IAAI,CAACD,OAAO,CAAC;IACxB,CAAC;IACDE,GAAG,EAAE,SAASA,GAAGA,CAACF,OAAO,EAAE;MACzBF,QAAQ,GAAGA,QAAQ,CAACK,MAAM,CAAC,UAAUC,CAAC,EAAE;QACtC,OAAOA,CAAC,KAAKJ,OAAO;MACtB,CAAC,CAAC;IACJ,CAAC;IACDK,GAAG,EAAE,SAASA,GAAGA,CAAA,EAAG;MAClB,OAAOlB,KAAK;IACd,CAAC;IACDmB,GAAG,EAAE,SAASA,GAAGA,CAACC,QAAQ,EAAEC,WAAW,EAAE;MACvCrB,KAAK,GAAGoB,QAAQ;MAChBT,QAAQ,CAACW,OAAO,CAAC,UAAUT,OAAO,EAAE;QAClC,OAAOA,OAAO,CAACb,KAAK,EAAEqB,WAAW,CAAC;MACpC,CAAC,CAAC;IACJ;EACF,CAAC;AACH;AAEA,SAASE,SAASA,CAACC,QAAQ,EAAE;EAC3B,OAAOC,KAAK,CAACC,OAAO,CAACF,QAAQ,CAAC,GAAGA,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ;AACzD;AAEA,SAASG,kBAAkBA,CAACC,YAAY,EAAEC,oBAAoB,EAAE;EAC9D,IAAIC,qBAAqB,EAAEC,qBAAqB;EAEhD,IAAIC,WAAW,GAAG,yBAAyB,GAAG,CAAC,CAAC,EAAEpD,KAAK,CAACI,OAAO,EAAE,CAAC,GAAG,IAAI;EAEzE,IAAIiD,QAAQ,GAAG,UAAUC,UAAU,EAAE;IACnCzC,SAAS,CAACwC,QAAQ,EAAEC,UAAU,CAAC;IAE/B,SAASD,QAAQA,CAAA,EAAG;MAClB,IAAIE,KAAK,EAAEC,KAAK,EAAEC,IAAI;MAEtBpD,eAAe,CAAC,IAAI,EAAEgD,QAAQ,CAAC;MAE/B,KAAK,IAAIK,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAGhB,KAAK,CAACa,IAAI,CAAC,EAAEI,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGJ,IAAI,EAAEI,IAAI,EAAE,EAAE;QACnFD,IAAI,CAACC,IAAI,CAAC,GAAGH,SAAS,CAACG,IAAI,CAAC;MAC9B;MAEA,OAAOL,IAAI,IAAIF,KAAK,IAAIC,KAAK,GAAG/C,0BAA0B,CAAC,IAAI,EAAE6C,UAAU,CAAC3C,IAAI,CAACoD,KAAK,CAACT,UAAU,EAAE,CAAC,IAAI,CAAC,CAACU,MAAM,CAACH,IAAI,CAAC,CAAC,CAAC,EAAEL,KAAK,CAAC,EAAEA,KAAK,CAACS,OAAO,GAAGnC,kBAAkB,CAAC0B,KAAK,CAACU,KAAK,CAAC9C,KAAK,CAAC,EAAEmC,KAAK,CAAC,EAAE9C,0BAA0B,CAAC+C,KAAK,EAAEC,IAAI,CAAC;IAC1O;IAEAJ,QAAQ,CAACrC,SAAS,CAACmD,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;MAC9D,IAAIC,IAAI;MAER,OAAOA,IAAI,GAAG,CAAC,CAAC,EAAEA,IAAI,CAAChB,WAAW,CAAC,GAAG,IAAI,CAACa,OAAO,EAAEG,IAAI;IAC1D,CAAC;IAEDf,QAAQ,CAACrC,SAAS,CAACqD,yBAAyB,GAAG,SAASA,yBAAyBA,CAACC,SAAS,EAAE;MAC3F,IAAI,IAAI,CAACJ,KAAK,CAAC9C,KAAK,KAAKkD,SAAS,CAAClD,KAAK,EAAE;QACxC,IAAImD,QAAQ,GAAG,IAAI,CAACL,KAAK,CAAC9C,KAAK;QAC/B,IAAIoB,QAAQ,GAAG8B,SAAS,CAAClD,KAAK;QAC9B,IAAIqB,WAAW,GAAG,KAAK,CAAC;QAExB,IAAId,QAAQ,CAAC4C,QAAQ,EAAE/B,QAAQ,CAAC,EAAE;UAChCC,WAAW,GAAG,CAAC,CAAC,CAAC;QACnB,CAAC,MAAM;UACLA,WAAW,GAAG,OAAOQ,oBAAoB,KAAK,UAAU,GAAGA,oBAAoB,CAACsB,QAAQ,EAAE/B,QAAQ,CAAC,GAAGd,qBAAqB;UAC3H,IAAI8C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;YACzC,CAAC,CAAC,EAAExE,SAAS,CAACE,OAAO,EAAE,CAACqC,WAAW,GAAGf,qBAAqB,MAAMe,WAAW,EAAE,0DAA0D,GAAG,sCAAsC,EAAEA,WAAW,CAAC;UACjM;UAEAA,WAAW,IAAI,CAAC;UAEhB,IAAIA,WAAW,KAAK,CAAC,EAAE;YACrB,IAAI,CAACwB,OAAO,CAAC1B,GAAG,CAAC+B,SAAS,CAAClD,KAAK,EAAEqB,WAAW,CAAC;UAChD;QACF;MACF;IACF,CAAC;IAEDY,QAAQ,CAACrC,SAAS,CAAC2D,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;MAC5C,OAAO,IAAI,CAACT,KAAK,CAACtB,QAAQ;IAC5B,CAAC;IAED,OAAOS,QAAQ;EACjB,CAAC,CAAC5D,MAAM,CAACmF,SAAS,CAAC;EAEnBvB,QAAQ,CAACwB,iBAAiB,IAAI3B,qBAAqB,GAAG,CAAC,CAAC,EAAEA,qBAAqB,CAACE,WAAW,CAAC,GAAGtD,WAAW,CAACM,OAAO,CAAC0E,MAAM,CAACC,UAAU,EAAE7B,qBAAqB,CAAC;EAE5J,IAAI8B,QAAQ,GAAG,UAAUC,WAAW,EAAE;IACpCpE,SAAS,CAACmE,QAAQ,EAAEC,WAAW,CAAC;IAEhC,SAASD,QAAQA,CAAA,EAAG;MAClB,IAAIE,MAAM,EAAEC,MAAM,EAAEC,KAAK;MAEzB/E,eAAe,CAAC,IAAI,EAAE2E,QAAQ,CAAC;MAE/B,KAAK,IAAIK,KAAK,GAAG1B,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAGhB,KAAK,CAACwC,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;QACzFzB,IAAI,CAACyB,KAAK,CAAC,GAAG3B,SAAS,CAAC2B,KAAK,CAAC;MAChC;MAEA,OAAOF,KAAK,IAAIF,MAAM,IAAIC,MAAM,GAAG1E,0BAA0B,CAAC,IAAI,EAAEwE,WAAW,CAACtE,IAAI,CAACoD,KAAK,CAACkB,WAAW,EAAE,CAAC,IAAI,CAAC,CAACjB,MAAM,CAACH,IAAI,CAAC,CAAC,CAAC,EAAEsB,MAAM,CAAC,EAAEA,MAAM,CAACI,KAAK,GAAG;QACrJnE,KAAK,EAAE+D,MAAM,CAACK,QAAQ,CAAC;MACzB,CAAC,EAAEL,MAAM,CAACM,QAAQ,GAAG,UAAUjD,QAAQ,EAAEC,WAAW,EAAE;QACpD,IAAIiD,YAAY,GAAGP,MAAM,CAACO,YAAY,GAAG,CAAC;QAC1C,IAAI,CAACA,YAAY,GAAGjD,WAAW,MAAM,CAAC,EAAE;UACtC0C,MAAM,CAACQ,QAAQ,CAAC;YAAEvE,KAAK,EAAE+D,MAAM,CAACK,QAAQ,CAAC;UAAE,CAAC,CAAC;QAC/C;MACF,CAAC,EAAEN,MAAM,CAAC,EAAEzE,0BAA0B,CAAC0E,MAAM,EAAEC,KAAK,CAAC;IACvD;IAEAJ,QAAQ,CAAChE,SAAS,CAACqD,yBAAyB,GAAG,SAASA,yBAAyBA,CAACC,SAAS,EAAE;MAC3F,IAAIoB,YAAY,GAAGpB,SAAS,CAACoB,YAAY;MAEzC,IAAI,CAACA,YAAY,GAAGA,YAAY,KAAKE,SAAS,IAAIF,YAAY,KAAK,IAAI,GAAGhE,qBAAqB,CAAC;MAAA,EAC9FgE,YAAY;IAChB,CAAC;IAEDV,QAAQ,CAAChE,SAAS,CAAC6E,iBAAiB,GAAG,SAASA,iBAAiBA,CAAA,EAAG;MAClE,IAAI,IAAI,CAACC,OAAO,CAAC1C,WAAW,CAAC,EAAE;QAC7B,IAAI,CAAC0C,OAAO,CAAC1C,WAAW,CAAC,CAACpB,EAAE,CAAC,IAAI,CAACyD,QAAQ,CAAC;MAC7C;MACA,IAAIC,YAAY,GAAG,IAAI,CAACxB,KAAK,CAACwB,YAAY;MAE1C,IAAI,CAACA,YAAY,GAAGA,YAAY,KAAKE,SAAS,IAAIF,YAAY,KAAK,IAAI,GAAGhE,qBAAqB,CAAC;MAAA,EAC9FgE,YAAY;IAChB,CAAC;IAEDV,QAAQ,CAAChE,SAAS,CAAC+E,oBAAoB,GAAG,SAASA,oBAAoBA,CAAA,EAAG;MACxE,IAAI,IAAI,CAACD,OAAO,CAAC1C,WAAW,CAAC,EAAE;QAC7B,IAAI,CAAC0C,OAAO,CAAC1C,WAAW,CAAC,CAACjB,GAAG,CAAC,IAAI,CAACsD,QAAQ,CAAC;MAC9C;IACF,CAAC;IAEDT,QAAQ,CAAChE,SAAS,CAACwE,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;MAChD,IAAI,IAAI,CAACM,OAAO,CAAC1C,WAAW,CAAC,EAAE;QAC7B,OAAO,IAAI,CAAC0C,OAAO,CAAC1C,WAAW,CAAC,CAACd,GAAG,CAAC,CAAC;MACxC,CAAC,MAAM;QACL,OAAOU,YAAY;MACrB;IACF,CAAC;IAEDgC,QAAQ,CAAChE,SAAS,CAAC2D,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;MAC5C,OAAOhC,SAAS,CAAC,IAAI,CAACuB,KAAK,CAACtB,QAAQ,CAAC,CAAC,IAAI,CAAC2C,KAAK,CAACnE,KAAK,CAAC;IACzD,CAAC;IAED,OAAO4D,QAAQ;EACjB,CAAC,CAACvF,MAAM,CAACmF,SAAS,CAAC;EAEnBI,QAAQ,CAACgB,YAAY,IAAI7C,qBAAqB,GAAG,CAAC,CAAC,EAAEA,qBAAqB,CAACC,WAAW,CAAC,GAAGtD,WAAW,CAACM,OAAO,CAAC0E,MAAM,EAAE3B,qBAAqB,CAAC;EAG5I,OAAO;IACLE,QAAQ,EAAEA,QAAQ;IAClB2B,QAAQ,EAAEA;EACZ,CAAC;AACH;AAEAzF,OAAO,CAACa,OAAO,GAAG2C,kBAAkB;AACpCkD,MAAM,CAAC1G,OAAO,GAAGA,OAAO,CAAC,SAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}