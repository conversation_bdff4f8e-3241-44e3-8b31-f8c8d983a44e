{"ast": null, "code": "/**\n * Copyright (c) 2014-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\n/**\n * Similar to invariant but only logs a warning if the condition is not met.\n * This can be used to log issues in development environments in critical\n * paths. Removing the logging code for production environments will keep the\n * same logic and follow the same code paths.\n */\nvar __DEV__ = process.env.NODE_ENV !== 'production';\nvar warning = function () {};\nif (__DEV__) {\n  var printWarning = function printWarning(format, args) {\n    var len = arguments.length;\n    args = new Array(len > 1 ? len - 1 : 0);\n    for (var key = 1; key < len; key++) {\n      args[key - 1] = arguments[key];\n    }\n    var argIndex = 0;\n    var message = 'Warning: ' + format.replace(/%s/g, function () {\n      return args[argIndex++];\n    });\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) {}\n  };\n  warning = function (condition, format, args) {\n    var len = arguments.length;\n    args = new Array(len > 2 ? len - 2 : 0);\n    for (var key = 2; key < len; key++) {\n      args[key - 2] = arguments[key];\n    }\n    if (format === undefined) {\n      throw new Error('`warning(condition, format, ...args)` requires a warning ' + 'message argument');\n    }\n    if (!condition) {\n      printWarning.apply(null, [format].concat(args));\n    }\n  };\n}\nmodule.exports = warning;", "map": {"version": 3, "names": ["__DEV__", "process", "env", "NODE_ENV", "warning", "printWarning", "format", "args", "len", "arguments", "length", "Array", "key", "argIndex", "message", "replace", "console", "error", "Error", "x", "condition", "undefined", "apply", "concat", "module", "exports"], "sources": ["C:/ignition/ignition-ui/node_modules/warning/warning.js"], "sourcesContent": ["/**\n * Copyright (c) 2014-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\n/**\n * Similar to invariant but only logs a warning if the condition is not met.\n * This can be used to log issues in development environments in critical\n * paths. Removing the logging code for production environments will keep the\n * same logic and follow the same code paths.\n */\n\nvar __DEV__ = process.env.NODE_ENV !== 'production';\n\nvar warning = function() {};\n\nif (__DEV__) {\n  var printWarning = function printWarning(format, args) {\n    var len = arguments.length;\n    args = new Array(len > 1 ? len - 1 : 0);\n    for (var key = 1; key < len; key++) {\n      args[key - 1] = arguments[key];\n    }\n    var argIndex = 0;\n    var message = 'Warning: ' +\n      format.replace(/%s/g, function() {\n        return args[argIndex++];\n      });\n    if (typeof console !== 'undefined') {\n      console.error(message);\n    }\n    try {\n      // --- Welcome to debugging React ---\n      // This error was thrown as a convenience so that you can use this stack\n      // to find the callsite that caused this warning to fire.\n      throw new Error(message);\n    } catch (x) {}\n  }\n\n  warning = function(condition, format, args) {\n    var len = arguments.length;\n    args = new Array(len > 2 ? len - 2 : 0);\n    for (var key = 2; key < len; key++) {\n      args[key - 2] = arguments[key];\n    }\n    if (format === undefined) {\n      throw new Error(\n          '`warning(condition, format, ...args)` requires a warning ' +\n          'message argument'\n      );\n    }\n    if (!condition) {\n      printWarning.apply(null, [format].concat(args));\n    }\n  };\n}\n\nmodule.exports = warning;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ;AACA;AACA;AACA;AACA;AACA;AAEA,IAAIA,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY;AAEnD,IAAIC,OAAO,GAAG,SAAAA,CAAA,EAAW,CAAC,CAAC;AAE3B,IAAIJ,OAAO,EAAE;EACX,IAAIK,YAAY,GAAG,SAASA,YAAYA,CAACC,MAAM,EAAEC,IAAI,EAAE;IACrD,IAAIC,GAAG,GAAGC,SAAS,CAACC,MAAM;IAC1BH,IAAI,GAAG,IAAII,KAAK,CAACH,GAAG,GAAG,CAAC,GAAGA,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IACvC,KAAK,IAAII,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGJ,GAAG,EAAEI,GAAG,EAAE,EAAE;MAClCL,IAAI,CAACK,GAAG,GAAG,CAAC,CAAC,GAAGH,SAAS,CAACG,GAAG,CAAC;IAChC;IACA,IAAIC,QAAQ,GAAG,CAAC;IAChB,IAAIC,OAAO,GAAG,WAAW,GACvBR,MAAM,CAACS,OAAO,CAAC,KAAK,EAAE,YAAW;MAC/B,OAAOR,IAAI,CAACM,QAAQ,EAAE,CAAC;IACzB,CAAC,CAAC;IACJ,IAAI,OAAOG,OAAO,KAAK,WAAW,EAAE;MAClCA,OAAO,CAACC,KAAK,CAACH,OAAO,CAAC;IACxB;IACA,IAAI;MACF;MACA;MACA;MACA,MAAM,IAAII,KAAK,CAACJ,OAAO,CAAC;IAC1B,CAAC,CAAC,OAAOK,CAAC,EAAE,CAAC;EACf,CAAC;EAEDf,OAAO,GAAG,SAAAA,CAASgB,SAAS,EAAEd,MAAM,EAAEC,IAAI,EAAE;IAC1C,IAAIC,GAAG,GAAGC,SAAS,CAACC,MAAM;IAC1BH,IAAI,GAAG,IAAII,KAAK,CAACH,GAAG,GAAG,CAAC,GAAGA,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;IACvC,KAAK,IAAII,GAAG,GAAG,CAAC,EAAEA,GAAG,GAAGJ,GAAG,EAAEI,GAAG,EAAE,EAAE;MAClCL,IAAI,CAACK,GAAG,GAAG,CAAC,CAAC,GAAGH,SAAS,CAACG,GAAG,CAAC;IAChC;IACA,IAAIN,MAAM,KAAKe,SAAS,EAAE;MACxB,MAAM,IAAIH,KAAK,CACX,2DAA2D,GAC3D,kBACJ,CAAC;IACH;IACA,IAAI,CAACE,SAAS,EAAE;MACdf,YAAY,CAACiB,KAAK,CAAC,IAAI,EAAE,CAAChB,MAAM,CAAC,CAACiB,MAAM,CAAChB,IAAI,CAAC,CAAC;IACjD;EACF,CAAC;AACH;AAEAiB,MAAM,CAACC,OAAO,GAAGrB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}