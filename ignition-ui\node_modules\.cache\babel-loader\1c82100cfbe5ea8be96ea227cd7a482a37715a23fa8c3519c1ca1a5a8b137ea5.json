{"ast": null, "code": "import { unstable_generateUtilityClass as generateUtilityClass, unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nexport function getBreadcrumbsUtilityClass(slot) {\n  return generateUtilityClass('MuiBreadcrumbs', slot);\n}\nconst breadcrumbsClasses = generateUtilityClasses('MuiBreadcrumbs', ['root', 'ol', 'li', 'separator', 'sizeSm', 'sizeMd', 'sizeLg']);\nexport default breadcrumbsClasses;", "map": {"version": 3, "names": ["unstable_generateUtilityClass", "generateUtilityClass", "unstable_generateUtilityClasses", "generateUtilityClasses", "getBreadcrumbsUtilityClass", "slot", "breadcrumbsClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Breadcrumbs/breadcrumbsClasses.js"], "sourcesContent": ["import { unstable_generateUtilityClass as generateUtilityClass, unstable_generateUtilityClasses as generateUtilityClasses } from '@mui/utils';\nexport function getBreadcrumbsUtilityClass(slot) {\n  return generateUtilityClass('MuiBreadcrumbs', slot);\n}\nconst breadcrumbsClasses = generateUtilityClasses('MuiBreadcrumbs', ['root', 'ol', 'li', 'separator', 'sizeSm', 'sizeMd', 'sizeLg']);\nexport default breadcrumbsClasses;"], "mappings": "AAAA,SAASA,6BAA6B,IAAIC,oBAAoB,EAAEC,+BAA+B,IAAIC,sBAAsB,QAAQ,YAAY;AAC7I,OAAO,SAASC,0BAA0BA,CAACC,IAAI,EAAE;EAC/C,OAAOJ,oBAAoB,CAAC,gBAAgB,EAAEI,IAAI,CAAC;AACrD;AACA,MAAMC,kBAAkB,GAAGH,sBAAsB,CAAC,gBAAgB,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;AACpI,eAAeG,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}