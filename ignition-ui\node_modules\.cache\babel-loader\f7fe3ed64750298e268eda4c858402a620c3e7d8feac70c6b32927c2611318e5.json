{"ast": null, "code": "'use strict';\n\nvar define = require('define-properties');\nvar callBind = require('call-bind');\nvar implementation = require('./implementation');\nvar getPolyfill = require('./polyfill');\nvar shim = require('./shim');\nvar flagsBound = callBind(getPolyfill());\ndefine(flagsBound, {\n  getPolyfill: getPolyfill,\n  implementation: implementation,\n  shim: shim\n});\nmodule.exports = flagsBound;", "map": {"version": 3, "names": ["define", "require", "callBind", "implementation", "getPolyfill", "shim", "flagsBound", "module", "exports"], "sources": ["C:/ignition/ignition-ui/node_modules/regexp.prototype.flags/index.js"], "sourcesContent": ["'use strict';\n\nvar define = require('define-properties');\nvar callBind = require('call-bind');\n\nvar implementation = require('./implementation');\nvar getPolyfill = require('./polyfill');\nvar shim = require('./shim');\n\nvar flagsBound = callBind(getPolyfill());\n\ndefine(flagsBound, {\n\tgetPolyfill: getPolyfill,\n\timplementation: implementation,\n\tshim: shim\n});\n\nmodule.exports = flagsBound;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAGC,OAAO,CAAC,mBAAmB,CAAC;AACzC,IAAIC,QAAQ,GAAGD,OAAO,CAAC,WAAW,CAAC;AAEnC,IAAIE,cAAc,GAAGF,OAAO,CAAC,kBAAkB,CAAC;AAChD,IAAIG,WAAW,GAAGH,OAAO,CAAC,YAAY,CAAC;AACvC,IAAII,IAAI,GAAGJ,OAAO,CAAC,QAAQ,CAAC;AAE5B,IAAIK,UAAU,GAAGJ,QAAQ,CAACE,WAAW,CAAC,CAAC,CAAC;AAExCJ,MAAM,CAACM,UAAU,EAAE;EAClBF,WAAW,EAAEA,WAAW;EACxBD,cAAc,EAAEA,cAAc;EAC9BE,IAAI,EAAEA;AACP,CAAC,CAAC;AAEFE,MAAM,CAACC,OAAO,GAAGF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}