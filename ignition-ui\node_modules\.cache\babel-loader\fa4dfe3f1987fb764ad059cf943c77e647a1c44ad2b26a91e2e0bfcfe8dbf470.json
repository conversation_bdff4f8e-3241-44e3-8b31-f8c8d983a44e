{"ast": null, "code": "var _jsxFileName = \"C:\\\\ignition\\\\ignition-ui\\\\src\\\\views\\\\plan\\\\detail\\\\components\\\\AgentTab.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Box, Typography, Paper, TextField, IconButton, Avatar, Divider, CircularProgress, Chip, Tooltip } from '@mui/material';\nimport Iconify from 'components/Iconify/index';\nimport { mainYellowColor } from \"helpers/constants\";\nimport { useLocation } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AgentTab = _ref => {\n  _s();\n  let {\n    planInfo,\n    onPlanUpdate\n  } = _ref;\n  const [conversations, setConversations] = useState([]);\n  const [currentMessage, setCurrentMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef(null);\n  const inputRef = useRef(null);\n  const location = useLocation();\n\n  // Load conversations from localStorage\n  useEffect(() => {\n    var _location$state;\n    const savedConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\n    const planConversations = savedConversations.filter(conv => conv.planId === (planInfo === null || planInfo === void 0 ? void 0 : planInfo.id));\n    setConversations(planConversations);\n\n    // Check for pending message from ChatbotBar\n    const pendingMessage = localStorage.getItem('pending_agent_message');\n    if (pendingMessage) {\n      try {\n        var _messageData$planInfo;\n        const messageData = JSON.parse(pendingMessage);\n        if (((_messageData$planInfo = messageData.planInfo) === null || _messageData$planInfo === void 0 ? void 0 : _messageData$planInfo.id) === (planInfo === null || planInfo === void 0 ? void 0 : planInfo.id)) {\n          handleSendMessage(messageData.message);\n        }\n        // Clear the pending message\n        localStorage.removeItem('pending_agent_message');\n      } catch (error) {\n        console.error('Error processing pending message:', error);\n        localStorage.removeItem('pending_agent_message');\n      }\n    }\n\n    // If coming from chatbot bar via navigation state, add the initial message\n    if ((_location$state = location.state) !== null && _location$state !== void 0 && _location$state.message) {\n      handleSendMessage(location.state.message);\n    }\n  }, [planInfo === null || planInfo === void 0 ? void 0 : planInfo.id, location.state]);\n\n  // Auto scroll to bottom\n  useEffect(() => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  }, [conversations]);\n  const handleSendMessage = async function () {\n    let messageText = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : currentMessage;\n    if (!messageText.trim() || isLoading) return;\n    setIsLoading(true);\n    const userMessage = {\n      id: Date.now(),\n      type: 'user',\n      content: messageText.trim(),\n      timestamp: new Date().toISOString()\n    };\n    const newConversations = [...conversations, userMessage];\n    setConversations(newConversations);\n    setCurrentMessage('');\n    try {\n      // Simulate AI processing (replace with actual AI API call)\n      await new Promise(resolve => setTimeout(resolve, 2000));\n      const aiResponse = {\n        id: Date.now() + 1,\n        type: 'assistant',\n        content: generateAIResponse(messageText, planInfo),\n        timestamp: new Date().toISOString(),\n        actions: extractActions(messageText)\n      };\n      const updatedConversations = [...newConversations, aiResponse];\n      setConversations(updatedConversations);\n\n      // Save to localStorage\n      const allConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\n      const otherPlanConversations = allConversations.filter(conv => conv.planId !== (planInfo === null || planInfo === void 0 ? void 0 : planInfo.id));\n      const planConversations = updatedConversations.map(conv => ({\n        ...conv,\n        planId: planInfo === null || planInfo === void 0 ? void 0 : planInfo.id,\n        planName: planInfo === null || planInfo === void 0 ? void 0 : planInfo.name\n      }));\n      localStorage.setItem('agent_conversations', JSON.stringify([...otherPlanConversations, ...planConversations]));\n    } catch (error) {\n      console.error('Error processing message:', error);\n      const errorResponse = {\n        id: Date.now() + 1,\n        type: 'assistant',\n        content: \"I'm sorry, I encountered an error processing your request. Please try again.\",\n        timestamp: new Date().toISOString(),\n        isError: true\n      };\n      setConversations(prev => [...prev, errorResponse]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  const generateAIResponse = (message, planInfo) => {\n    const lowerMessage = message.toLowerCase();\n    const milestones = (planInfo === null || planInfo === void 0 ? void 0 : planInfo.milestones) || [];\n    const totalTasks = milestones.reduce((acc, milestone) => {\n      var _milestone$tasks;\n      return acc + (((_milestone$tasks = milestone.tasks) === null || _milestone$tasks === void 0 ? void 0 : _milestone$tasks.length) || 0);\n    }, 0);\n    const totalSubtasks = milestones.reduce((acc, milestone) => {\n      var _milestone$tasks2;\n      return acc + ((_milestone$tasks2 = milestone.tasks) === null || _milestone$tasks2 === void 0 ? void 0 : _milestone$tasks2.reduce((taskAcc, task) => {\n        var _task$subtasks;\n        return taskAcc + (((_task$subtasks = task.subtasks) === null || _task$subtasks === void 0 ? void 0 : _task$subtasks.length) || 0);\n      }, 0)) || 0;\n    }, 0);\n\n    // Analyze project context\n    const projectContext = {\n      name: (planInfo === null || planInfo === void 0 ? void 0 : planInfo.name) || 'your project',\n      milestoneCount: milestones.length,\n      taskCount: totalTasks,\n      subtaskCount: totalSubtasks,\n      milestoneNames: milestones.map(m => m.name).slice(0, 3) // First 3 milestone names\n    };\n\n    // Advanced intent recognition and response generation\n    if (lowerMessage.includes('milestone') && (lowerMessage.includes('add') || lowerMessage.includes('create') || lowerMessage.includes('new'))) {\n      return `I can help you add a new milestone to \"${projectContext.name}\". Currently, you have ${projectContext.milestoneCount} milestones: ${projectContext.milestoneNames.join(', ')}${projectContext.milestoneCount > 3 ? '...' : ''}.\n\nTo add a new milestone, I'll need:\n1. **Milestone name** - What should we call it?\n2. **Description** - What's the main objective?\n3. **Position** - Should it come before/after a specific milestone?\n4. **Tasks** - Any initial tasks to include?\n\nPlease provide these details, or just tell me the milestone name and I'll help structure the rest!`;\n    }\n    if (lowerMessage.includes('progress') || lowerMessage.includes('status') || lowerMessage.includes('overview')) {\n      const progressDetails = milestones.map(milestone => {\n        var _milestone$tasks3, _milestone$tasks4, _milestone$tasks4$fil;\n        const taskCount = ((_milestone$tasks3 = milestone.tasks) === null || _milestone$tasks3 === void 0 ? void 0 : _milestone$tasks3.length) || 0;\n        const completedTasks = ((_milestone$tasks4 = milestone.tasks) === null || _milestone$tasks4 === void 0 ? void 0 : (_milestone$tasks4$fil = _milestone$tasks4.filter(task => task.status === 'completed')) === null || _milestone$tasks4$fil === void 0 ? void 0 : _milestone$tasks4$fil.length) || 0;\n        return `• ${milestone.name}: ${completedTasks}/${taskCount} tasks completed`;\n      }).join('\\n');\n      return `Here's your project progress for \"${projectContext.name}\":\n\n📊 **Overall Statistics:**\n• ${projectContext.milestoneCount} milestones\n• ${projectContext.taskCount} total tasks\n• ${projectContext.subtaskCount} total subtasks\n\n📋 **Milestone Progress:**\n${progressDetails}\n\nWould you like me to:\n• Show detailed progress for a specific milestone?\n• Identify overdue or at-risk tasks?\n• Suggest next actions to move the project forward?`;\n    }\n    if (lowerMessage.includes('complete') || lowerMessage.includes('done') || lowerMessage.includes('finish')) {\n      const availableTasks = milestones.flatMap(milestone => {\n        var _milestone$tasks5;\n        return ((_milestone$tasks5 = milestone.tasks) === null || _milestone$tasks5 === void 0 ? void 0 : _milestone$tasks5.filter(task => task.status !== 'completed').map(task => `\"${task.name}\" in ${milestone.name}`)) || [];\n      }).slice(0, 5);\n      return `I can mark tasks as completed for you! Here are some pending tasks I found:\n\n${availableTasks.map(task => `• ${task}`).join('\\n')}\n\nTo mark a task as complete, just tell me:\n• \"Mark [task name] as completed\"\n• \"Complete the [task name] task\"\n• Or simply \"Done with [task name]\"\n\nWhich task would you like to mark as completed?`;\n    }\n    if (lowerMessage.includes('add') || lowerMessage.includes('create') || lowerMessage.includes('new')) {\n      if (lowerMessage.includes('task')) {\n        return `I can add new tasks to any of your milestones. You currently have ${projectContext.milestoneCount} milestones:\n\n${projectContext.milestoneNames.map((name, i) => `${i + 1}. ${name}`).join('\\n')}\n\nTo add a task, tell me:\n• **Which milestone** to add it to\n• **Task name** and description\n• **Any subtasks** to include\n\nExample: \"Add task 'Set up development environment' to the first milestone with subtasks for installing tools and configuring settings\"\n\nWhat task would you like to add?`;\n      }\n      return `I can help you add new content to \"${projectContext.name}\". I can create:\n\n🎯 **Milestones** - Major project phases\n📋 **Tasks** - Specific work items within milestones\n✅ **Subtasks** - Detailed steps for tasks\n📝 **Descriptions** - Enhanced details for any item\n\nWhat would you like to add? Just describe it naturally, like:\n• \"Add a milestone for user testing\"\n• \"Create a task for database setup in the development milestone\"\n• \"Add subtasks for the API integration task\"`;\n    }\n    if (lowerMessage.includes('delete') || lowerMessage.includes('remove')) {\n      return `I can help you remove items from your project. For safety, I'll always confirm before deleting anything.\n\nI can remove:\n• **Tasks** that are no longer needed\n• **Subtasks** that are redundant\n• **Completed items** to clean up the project\n• **Duplicate entries**\n\n⚠️ **Note:** I cannot delete milestones as they're core to your project structure.\n\nWhat would you like to remove? Please be specific about the item name and location.`;\n    }\n    if (lowerMessage.includes('update') || lowerMessage.includes('change') || lowerMessage.includes('edit') || lowerMessage.includes('modify')) {\n      return `I can update various aspects of your project \"${projectContext.name}\":\n\n📝 **Content Updates:**\n• Task and subtask descriptions\n• Milestone objectives\n• Due dates and priorities\n• Task assignments\n\n🔄 **Status Changes:**\n• Mark items as in-progress, completed, or blocked\n• Update milestone phases\n• Change task priorities\n\n📊 **Structural Changes:**\n• Move tasks between milestones\n• Reorder items\n• Split large tasks into smaller ones\n\nWhat would you like to update? Describe the change you want to make.`;\n    }\n    if (lowerMessage.includes('help') || lowerMessage.includes('what can you do') || lowerMessage.includes('capabilities')) {\n      return `I'm your AI Project Assistant for \"${projectContext.name}\"! Here's what I can do:\n\n🎯 **Project Management:**\n• Add/remove tasks, subtasks, and milestones\n• Update descriptions, statuses, and priorities\n• Mark items as completed or in-progress\n• Move tasks between milestones\n\n📊 **Project Analysis:**\n• Show progress reports and statistics\n• Identify bottlenecks and overdue items\n• Suggest next actions and optimizations\n• Generate project summaries\n\n🔍 **Smart Search:**\n• Find specific tasks or milestones\n• Filter by status, assignee, or due date\n• Locate related items across the project\n\n💡 **Recommendations:**\n• Suggest task breakdowns\n• Recommend milestone structures\n• Identify missing dependencies\n• Propose timeline optimizations\n\nJust tell me what you want to do in natural language - I'll understand and help you get it done!`;\n    }\n\n    // Default intelligent response\n    return `I understand you want to \"${message}\".\n\nBased on your project \"${projectContext.name}\" with ${projectContext.milestoneCount} milestones and ${projectContext.taskCount} tasks, I can help you:\n\n🎯 **Quick Actions:**\n• \"Show me project progress\"\n• \"Add a new task to [milestone name]\"\n• \"Mark [task name] as completed\"\n• \"Update the description for [item name]\"\n\n💡 **Smart Suggestions:**\n• \"What should I work on next?\"\n• \"Show me overdue items\"\n• \"Help me organize this milestone\"\n• \"Create a timeline for this project\"\n\nWhat specific action would you like me to take? I'm here to make managing your project easier!`;\n  };\n  const extractActions = message => {\n    const actions = [];\n    const lowerMessage = message.toLowerCase();\n\n    // Enhanced action extraction with context\n    const actionPatterns = [{\n      pattern: /(complete|done|finish|mark.*complete)/,\n      type: 'complete_task',\n      confidence: 0.9,\n      description: 'Mark task as completed'\n    }, {\n      pattern: /(add|create|new).*milestone/,\n      type: 'add_milestone',\n      confidence: 0.9,\n      description: 'Add new milestone'\n    }, {\n      pattern: /(add|create|new).*task/,\n      type: 'add_task',\n      confidence: 0.9,\n      description: 'Add new task'\n    }, {\n      pattern: /(add|create|new).*subtask/,\n      type: 'add_subtask',\n      confidence: 0.9,\n      description: 'Add new subtask'\n    }, {\n      pattern: /(delete|remove).*task/,\n      type: 'delete_task',\n      confidence: 0.8,\n      description: 'Delete task'\n    }, {\n      pattern: /(update|change|edit|modify)/,\n      type: 'update_item',\n      confidence: 0.8,\n      description: 'Update item details'\n    }, {\n      pattern: /(progress|status|overview)/,\n      type: 'show_progress',\n      confidence: 0.9,\n      description: 'Show project progress'\n    }, {\n      pattern: /(move|transfer).*task/,\n      type: 'move_task',\n      confidence: 0.8,\n      description: 'Move task between milestones'\n    }, {\n      pattern: /(assign|delegate)/,\n      type: 'assign_task',\n      confidence: 0.8,\n      description: 'Assign task to team member'\n    }, {\n      pattern: /(due date|deadline|schedule)/,\n      type: 'set_deadline',\n      confidence: 0.8,\n      description: 'Set or update due date'\n    }];\n\n    // Extract entities (task names, milestone names, etc.)\n    const entities = {\n      taskNames: [],\n      milestoneNames: [],\n      dates: [],\n      priorities: []\n    };\n\n    // Look for quoted text (likely task/milestone names)\n    const quotedText = message.match(/\"([^\"]+)\"/g);\n    if (quotedText) {\n      entities.taskNames = quotedText.map(q => q.replace(/\"/g, ''));\n    }\n\n    // Look for date patterns\n    const datePatterns = message.match(/\\b\\d{1,2}\\/\\d{1,2}\\/\\d{4}\\b|\\b\\d{1,2}-\\d{1,2}-\\d{4}\\b|tomorrow|today|next week|next month/gi);\n    if (datePatterns) {\n      entities.dates = datePatterns;\n    }\n\n    // Look for priority keywords\n    const priorityPatterns = message.match(/\\b(high|low|medium|urgent|critical|normal)\\s*priority\\b/gi);\n    if (priorityPatterns) {\n      entities.priorities = priorityPatterns;\n    }\n\n    // Match actions against patterns\n    actionPatterns.forEach(_ref2 => {\n      let {\n        pattern,\n        type,\n        confidence,\n        description\n      } = _ref2;\n      if (pattern.test(lowerMessage)) {\n        actions.push({\n          type,\n          confidence,\n          description,\n          entities: entities,\n          originalMessage: message\n        });\n      }\n    });\n    return actions;\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n  const formatTimestamp = timestamp => {\n    return new Date(timestamp).toLocaleTimeString('en-US', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      height: '70vh',\n      display: 'flex',\n      flexDirection: 'column'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 0,\n      sx: {\n        p: 2,\n        borderRadius: '12px 12px 0 0',\n        border: '1px solid #f0f0f0',\n        borderBottom: 'none',\n        backgroundColor: '#fafafa'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          sx: {\n            backgroundColor: mainYellowColor,\n            width: 40,\n            height: 40\n          },\n          children: /*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"mdi:robot\",\n            width: 24,\n            height: 24,\n            color: \"#fff\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 448,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 441,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              fontWeight: 600,\n              color: '#333'\n            },\n            children: \"AI Project Agent\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            sx: {\n              color: '#666',\n              fontFamily: '\"Recursive Variable\", sans-serif'\n            },\n            children: [\"Managing: \", planInfo === null || planInfo === void 0 ? void 0 : planInfo.name]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 450,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Chip, {\n          label: \"Beta\",\n          size: \"small\",\n          sx: {\n            backgroundColor: `${mainYellowColor}20`,\n            color: mainYellowColor,\n            fontWeight: 600,\n            ml: 'auto'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 471,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 440,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 430,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 0,\n      sx: {\n        flex: 1,\n        border: '1px solid #f0f0f0',\n        borderTop: 'none',\n        borderBottom: 'none',\n        overflow: 'auto',\n        p: 2,\n        backgroundColor: '#fff'\n      },\n      children: conversations.length === 0 ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          flexDirection: 'column',\n          alignItems: 'center',\n          justifyContent: 'center',\n          height: '100%',\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Avatar, {\n          sx: {\n            backgroundColor: `${mainYellowColor}20`,\n            width: 60,\n            height: 60,\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"mdi:robot\",\n            width: 32,\n            height: 32,\n            color: mainYellowColor\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          sx: {\n            fontFamily: '\"Recursive Variable\", sans-serif',\n            fontWeight: 600,\n            color: '#333',\n            mb: 1\n          },\n          children: \"Welcome to AI Project Agent\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 518,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            color: '#666',\n            fontFamily: '\"Recursive Variable\", sans-serif',\n            maxWidth: 400\n          },\n          children: \"I'm here to help you manage your project. Ask me questions or request changes to tasks, milestones, and more!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 529,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 498,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Box, {\n        children: [conversations.map(message => /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start',\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              maxWidth: '70%',\n              display: 'flex',\n              flexDirection: message.type === 'user' ? 'row-reverse' : 'row',\n              alignItems: 'flex-start',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                width: 32,\n                height: 32,\n                backgroundColor: message.type === 'user' ? '#e0e0e0' : mainYellowColor\n              },\n              children: /*#__PURE__*/_jsxDEV(Iconify, {\n                icon: message.type === 'user' ? \"material-symbols:person\" : \"mdi:robot\",\n                width: 18,\n                height: 18,\n                color: message.type === 'user' ? '#666' : '#fff'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 567,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              children: [/*#__PURE__*/_jsxDEV(Paper, {\n                elevation: 0,\n                sx: {\n                  p: 1.5,\n                  borderRadius: '12px',\n                  backgroundColor: message.type === 'user' ? mainYellowColor : '#f5f5f5',\n                  color: message.type === 'user' ? '#fff' : '#333',\n                  border: message.isError ? '1px solid #f44336' : 'none'\n                },\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  sx: {\n                    fontFamily: '\"Recursive Variable\", sans-serif',\n                    lineHeight: 1.5,\n                    whiteSpace: 'pre-line'\n                  },\n                  children: message.content\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 585,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 575,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"caption\",\n                sx: {\n                  color: '#999',\n                  fontFamily: '\"Recursive Variable\", sans-serif',\n                  fontSize: '0.7rem',\n                  mt: 0.5,\n                  display: 'block',\n                  textAlign: message.type === 'user' ? 'right' : 'left'\n                },\n                children: formatTimestamp(message.timestamp)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 596,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 574,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 551,\n            columnNumber: 17\n          }, this)\n        }, message.id, false, {\n          fileName: _jsxFileName,\n          lineNumber: 543,\n          columnNumber: 15\n        }, this)), isLoading && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'flex-start',\n            mb: 2\n          },\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'flex-start',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Avatar, {\n              sx: {\n                width: 32,\n                height: 32,\n                backgroundColor: mainYellowColor\n              },\n              children: /*#__PURE__*/_jsxDEV(Iconify, {\n                icon: \"mdi:robot\",\n                width: 18,\n                height: 18,\n                color: \"#fff\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 623,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 616,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Paper, {\n              elevation: 0,\n              sx: {\n                p: 1.5,\n                borderRadius: '12px',\n                backgroundColor: '#f5f5f5',\n                display: 'flex',\n                alignItems: 'center',\n                gap: 1\n              },\n              children: [/*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 16,\n                sx: {\n                  color: mainYellowColor\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 636,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                sx: {\n                  fontFamily: '\"Recursive Variable\", sans-serif',\n                  color: '#666'\n                },\n                children: \"Thinking...\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 637,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 625,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 615,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 614,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          ref: messagesEndRef\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 650,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 541,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 485,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      elevation: 0,\n      sx: {\n        p: 2,\n        borderRadius: '0 0 12px 12px',\n        border: '1px solid #f0f0f0',\n        borderTop: 'none'\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          gap: 1,\n          alignItems: 'flex-end'\n        },\n        children: [/*#__PURE__*/_jsxDEV(TextField, {\n          inputRef: inputRef,\n          value: currentMessage,\n          onChange: e => setCurrentMessage(e.target.value),\n          onKeyPress: handleKeyPress,\n          placeholder: \"Ask me anything about your project...\",\n          multiline: true,\n          maxRows: 3,\n          fullWidth: true,\n          variant: \"outlined\",\n          disabled: isLoading,\n          sx: {\n            '& .MuiOutlinedInput-root': {\n              borderRadius: '8px',\n              fontFamily: '\"Recursive Variable\", sans-serif'\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 666,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"Send message\",\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            onClick: () => handleSendMessage(),\n            disabled: !currentMessage.trim() || isLoading,\n            sx: {\n              backgroundColor: currentMessage.trim() && !isLoading ? mainYellowColor : '#f0f0f0',\n              color: currentMessage.trim() && !isLoading ? '#fff' : '#999',\n              '&:hover': {\n                backgroundColor: currentMessage.trim() && !isLoading ? '#E69500' : '#f0f0f0'\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"material-symbols:send\",\n              width: 20,\n              height: 20\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 696,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 685,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 684,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 665,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 656,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 428,\n    columnNumber: 5\n  }, this);\n};\n_s(AgentTab, \"ko81Bu+6D1KQ7+ETWOFI/xjwLwk=\", false, function () {\n  return [useLocation];\n});\n_c = AgentTab;\nexport default AgentTab;\nvar _c;\n$RefreshReg$(_c, \"AgentTab\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Box", "Typography", "Paper", "TextField", "IconButton", "Avatar", "Divider", "CircularProgress", "Chip", "<PERSON><PERSON><PERSON>", "Iconify", "mainYellowColor", "useLocation", "jsxDEV", "_jsxDEV", "AgentTab", "_ref", "_s", "planInfo", "onPlanUpdate", "conversations", "setConversations", "currentMessage", "setCurrentMessage", "isLoading", "setIsLoading", "messagesEndRef", "inputRef", "location", "_location$state", "savedConversations", "JSON", "parse", "localStorage", "getItem", "planConversations", "filter", "conv", "planId", "id", "pendingMessage", "_messageData$planInfo", "messageData", "handleSendMessage", "message", "removeItem", "error", "console", "state", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "messageText", "arguments", "length", "undefined", "trim", "userMessage", "Date", "now", "type", "content", "timestamp", "toISOString", "newConversations", "Promise", "resolve", "setTimeout", "aiResponse", "generateAIResponse", "actions", "extractActions", "updatedConversations", "allConversations", "otherPlanConversations", "map", "planName", "name", "setItem", "stringify", "errorResponse", "isError", "prev", "lowerMessage", "toLowerCase", "milestones", "totalTasks", "reduce", "acc", "milestone", "_milestone$tasks", "tasks", "totalSubtasks", "_milestone$tasks2", "taskAcc", "task", "_task$subtasks", "subtasks", "projectContext", "milestoneCount", "taskCount", "subtaskCount", "milestoneNames", "m", "slice", "includes", "join", "progressDetails", "_milestone$tasks3", "_milestone$tasks4", "_milestone$tasks4$fil", "completedTasks", "status", "availableTasks", "flatMap", "_milestone$tasks5", "i", "actionPatterns", "pattern", "confidence", "description", "entities", "taskNames", "dates", "priorities", "quotedText", "match", "q", "replace", "datePatterns", "priorityPatterns", "for<PERSON>ach", "_ref2", "test", "push", "originalMessage", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "formatTimestamp", "toLocaleTimeString", "hour", "minute", "sx", "height", "display", "flexDirection", "children", "elevation", "p", "borderRadius", "border", "borderBottom", "backgroundColor", "alignItems", "gap", "width", "icon", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontFamily", "fontWeight", "label", "size", "ml", "flex", "borderTop", "overflow", "justifyContent", "textAlign", "mb", "max<PERSON><PERSON><PERSON>", "lineHeight", "whiteSpace", "fontSize", "mt", "ref", "value", "onChange", "target", "onKeyPress", "placeholder", "multiline", "maxRows", "fullWidth", "disabled", "title", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/ignition/ignition-ui/src/views/plan/detail/components/AgentTab.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport {\n  Box,\n  Typography,\n  Paper,\n  TextField,\n  IconButton,\n  Avatar,\n  Divider,\n  CircularProgress,\n  Chip,\n  Tooltip\n} from '@mui/material';\nimport Iconify from 'components/Iconify/index';\nimport { mainYellowColor } from \"helpers/constants\";\nimport { useLocation } from 'react-router-dom';\n\nconst AgentTab = ({ planInfo, onPlanUpdate }) => {\n  const [conversations, setConversations] = useState([]);\n  const [currentMessage, setCurrentMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef(null);\n  const inputRef = useRef(null);\n  const location = useLocation();\n\n  // Load conversations from localStorage\n  useEffect(() => {\n    const savedConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\n    const planConversations = savedConversations.filter(conv => conv.planId === planInfo?.id);\n    setConversations(planConversations);\n\n    // Check for pending message from ChatbotBar\n    const pendingMessage = localStorage.getItem('pending_agent_message');\n    if (pendingMessage) {\n      try {\n        const messageData = JSON.parse(pendingMessage);\n        if (messageData.planInfo?.id === planInfo?.id) {\n          handleSendMessage(messageData.message);\n        }\n        // Clear the pending message\n        localStorage.removeItem('pending_agent_message');\n      } catch (error) {\n        console.error('Error processing pending message:', error);\n        localStorage.removeItem('pending_agent_message');\n      }\n    }\n\n    // If coming from chatbot bar via navigation state, add the initial message\n    if (location.state?.message) {\n      handleSendMessage(location.state.message);\n    }\n  }, [planInfo?.id, location.state]);\n\n  // Auto scroll to bottom\n  useEffect(() => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  }, [conversations]);\n\n  const handleSendMessage = async (messageText = currentMessage) => {\n    if (!messageText.trim() || isLoading) return;\n\n    setIsLoading(true);\n    const userMessage = {\n      id: Date.now(),\n      type: 'user',\n      content: messageText.trim(),\n      timestamp: new Date().toISOString()\n    };\n\n    const newConversations = [...conversations, userMessage];\n    setConversations(newConversations);\n    setCurrentMessage('');\n\n    try {\n      // Simulate AI processing (replace with actual AI API call)\n      await new Promise(resolve => setTimeout(resolve, 2000));\n\n      const aiResponse = {\n        id: Date.now() + 1,\n        type: 'assistant',\n        content: generateAIResponse(messageText, planInfo),\n        timestamp: new Date().toISOString(),\n        actions: extractActions(messageText)\n      };\n\n      const updatedConversations = [...newConversations, aiResponse];\n      setConversations(updatedConversations);\n\n      // Save to localStorage\n      const allConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\n      const otherPlanConversations = allConversations.filter(conv => conv.planId !== planInfo?.id);\n      const planConversations = updatedConversations.map(conv => ({\n        ...conv,\n        planId: planInfo?.id,\n        planName: planInfo?.name\n      }));\n      \n      localStorage.setItem('agent_conversations', JSON.stringify([\n        ...otherPlanConversations,\n        ...planConversations\n      ]));\n\n    } catch (error) {\n      console.error('Error processing message:', error);\n      const errorResponse = {\n        id: Date.now() + 1,\n        type: 'assistant',\n        content: \"I'm sorry, I encountered an error processing your request. Please try again.\",\n        timestamp: new Date().toISOString(),\n        isError: true\n      };\n      setConversations(prev => [...prev, errorResponse]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const generateAIResponse = (message, planInfo) => {\n    const lowerMessage = message.toLowerCase();\n    const milestones = planInfo?.milestones || [];\n    const totalTasks = milestones.reduce((acc, milestone) => acc + (milestone.tasks?.length || 0), 0);\n    const totalSubtasks = milestones.reduce((acc, milestone) =>\n      acc + milestone.tasks?.reduce((taskAcc, task) => taskAcc + (task.subtasks?.length || 0), 0) || 0, 0);\n\n    // Analyze project context\n    const projectContext = {\n      name: planInfo?.name || 'your project',\n      milestoneCount: milestones.length,\n      taskCount: totalTasks,\n      subtaskCount: totalSubtasks,\n      milestoneNames: milestones.map(m => m.name).slice(0, 3), // First 3 milestone names\n    };\n\n    // Advanced intent recognition and response generation\n    if (lowerMessage.includes('milestone') && (lowerMessage.includes('add') || lowerMessage.includes('create') || lowerMessage.includes('new'))) {\n      return `I can help you add a new milestone to \"${projectContext.name}\". Currently, you have ${projectContext.milestoneCount} milestones: ${projectContext.milestoneNames.join(', ')}${projectContext.milestoneCount > 3 ? '...' : ''}.\n\nTo add a new milestone, I'll need:\n1. **Milestone name** - What should we call it?\n2. **Description** - What's the main objective?\n3. **Position** - Should it come before/after a specific milestone?\n4. **Tasks** - Any initial tasks to include?\n\nPlease provide these details, or just tell me the milestone name and I'll help structure the rest!`;\n    }\n\n    if (lowerMessage.includes('progress') || lowerMessage.includes('status') || lowerMessage.includes('overview')) {\n      const progressDetails = milestones.map(milestone => {\n        const taskCount = milestone.tasks?.length || 0;\n        const completedTasks = milestone.tasks?.filter(task => task.status === 'completed')?.length || 0;\n        return `• ${milestone.name}: ${completedTasks}/${taskCount} tasks completed`;\n      }).join('\\n');\n\n      return `Here's your project progress for \"${projectContext.name}\":\n\n📊 **Overall Statistics:**\n• ${projectContext.milestoneCount} milestones\n• ${projectContext.taskCount} total tasks\n• ${projectContext.subtaskCount} total subtasks\n\n📋 **Milestone Progress:**\n${progressDetails}\n\nWould you like me to:\n• Show detailed progress for a specific milestone?\n• Identify overdue or at-risk tasks?\n• Suggest next actions to move the project forward?`;\n    }\n\n    if (lowerMessage.includes('complete') || lowerMessage.includes('done') || lowerMessage.includes('finish')) {\n      const availableTasks = milestones.flatMap(milestone =>\n        milestone.tasks?.filter(task => task.status !== 'completed').map(task =>\n          `\"${task.name}\" in ${milestone.name}`\n        ) || []\n      ).slice(0, 5);\n\n      return `I can mark tasks as completed for you! Here are some pending tasks I found:\n\n${availableTasks.map(task => `• ${task}`).join('\\n')}\n\nTo mark a task as complete, just tell me:\n• \"Mark [task name] as completed\"\n• \"Complete the [task name] task\"\n• Or simply \"Done with [task name]\"\n\nWhich task would you like to mark as completed?`;\n    }\n\n    if (lowerMessage.includes('add') || lowerMessage.includes('create') || lowerMessage.includes('new')) {\n      if (lowerMessage.includes('task')) {\n        return `I can add new tasks to any of your milestones. You currently have ${projectContext.milestoneCount} milestones:\n\n${projectContext.milestoneNames.map((name, i) => `${i + 1}. ${name}`).join('\\n')}\n\nTo add a task, tell me:\n• **Which milestone** to add it to\n• **Task name** and description\n• **Any subtasks** to include\n\nExample: \"Add task 'Set up development environment' to the first milestone with subtasks for installing tools and configuring settings\"\n\nWhat task would you like to add?`;\n      }\n\n      return `I can help you add new content to \"${projectContext.name}\". I can create:\n\n🎯 **Milestones** - Major project phases\n📋 **Tasks** - Specific work items within milestones\n✅ **Subtasks** - Detailed steps for tasks\n📝 **Descriptions** - Enhanced details for any item\n\nWhat would you like to add? Just describe it naturally, like:\n• \"Add a milestone for user testing\"\n• \"Create a task for database setup in the development milestone\"\n• \"Add subtasks for the API integration task\"`;\n    }\n\n    if (lowerMessage.includes('delete') || lowerMessage.includes('remove')) {\n      return `I can help you remove items from your project. For safety, I'll always confirm before deleting anything.\n\nI can remove:\n• **Tasks** that are no longer needed\n• **Subtasks** that are redundant\n• **Completed items** to clean up the project\n• **Duplicate entries**\n\n⚠️ **Note:** I cannot delete milestones as they're core to your project structure.\n\nWhat would you like to remove? Please be specific about the item name and location.`;\n    }\n\n    if (lowerMessage.includes('update') || lowerMessage.includes('change') || lowerMessage.includes('edit') || lowerMessage.includes('modify')) {\n      return `I can update various aspects of your project \"${projectContext.name}\":\n\n📝 **Content Updates:**\n• Task and subtask descriptions\n• Milestone objectives\n• Due dates and priorities\n• Task assignments\n\n🔄 **Status Changes:**\n• Mark items as in-progress, completed, or blocked\n• Update milestone phases\n• Change task priorities\n\n📊 **Structural Changes:**\n• Move tasks between milestones\n• Reorder items\n• Split large tasks into smaller ones\n\nWhat would you like to update? Describe the change you want to make.`;\n    }\n\n    if (lowerMessage.includes('help') || lowerMessage.includes('what can you do') || lowerMessage.includes('capabilities')) {\n      return `I'm your AI Project Assistant for \"${projectContext.name}\"! Here's what I can do:\n\n🎯 **Project Management:**\n• Add/remove tasks, subtasks, and milestones\n• Update descriptions, statuses, and priorities\n• Mark items as completed or in-progress\n• Move tasks between milestones\n\n📊 **Project Analysis:**\n• Show progress reports and statistics\n• Identify bottlenecks and overdue items\n• Suggest next actions and optimizations\n• Generate project summaries\n\n🔍 **Smart Search:**\n• Find specific tasks or milestones\n• Filter by status, assignee, or due date\n• Locate related items across the project\n\n💡 **Recommendations:**\n• Suggest task breakdowns\n• Recommend milestone structures\n• Identify missing dependencies\n• Propose timeline optimizations\n\nJust tell me what you want to do in natural language - I'll understand and help you get it done!`;\n    }\n\n    // Default intelligent response\n    return `I understand you want to \"${message}\".\n\nBased on your project \"${projectContext.name}\" with ${projectContext.milestoneCount} milestones and ${projectContext.taskCount} tasks, I can help you:\n\n🎯 **Quick Actions:**\n• \"Show me project progress\"\n• \"Add a new task to [milestone name]\"\n• \"Mark [task name] as completed\"\n• \"Update the description for [item name]\"\n\n💡 **Smart Suggestions:**\n• \"What should I work on next?\"\n• \"Show me overdue items\"\n• \"Help me organize this milestone\"\n• \"Create a timeline for this project\"\n\nWhat specific action would you like me to take? I'm here to make managing your project easier!`;\n  };\n\n  const extractActions = (message) => {\n    const actions = [];\n    const lowerMessage = message.toLowerCase();\n\n    // Enhanced action extraction with context\n    const actionPatterns = [\n      {\n        pattern: /(complete|done|finish|mark.*complete)/,\n        type: 'complete_task',\n        confidence: 0.9,\n        description: 'Mark task as completed'\n      },\n      {\n        pattern: /(add|create|new).*milestone/,\n        type: 'add_milestone',\n        confidence: 0.9,\n        description: 'Add new milestone'\n      },\n      {\n        pattern: /(add|create|new).*task/,\n        type: 'add_task',\n        confidence: 0.9,\n        description: 'Add new task'\n      },\n      {\n        pattern: /(add|create|new).*subtask/,\n        type: 'add_subtask',\n        confidence: 0.9,\n        description: 'Add new subtask'\n      },\n      {\n        pattern: /(delete|remove).*task/,\n        type: 'delete_task',\n        confidence: 0.8,\n        description: 'Delete task'\n      },\n      {\n        pattern: /(update|change|edit|modify)/,\n        type: 'update_item',\n        confidence: 0.8,\n        description: 'Update item details'\n      },\n      {\n        pattern: /(progress|status|overview)/,\n        type: 'show_progress',\n        confidence: 0.9,\n        description: 'Show project progress'\n      },\n      {\n        pattern: /(move|transfer).*task/,\n        type: 'move_task',\n        confidence: 0.8,\n        description: 'Move task between milestones'\n      },\n      {\n        pattern: /(assign|delegate)/,\n        type: 'assign_task',\n        confidence: 0.8,\n        description: 'Assign task to team member'\n      },\n      {\n        pattern: /(due date|deadline|schedule)/,\n        type: 'set_deadline',\n        confidence: 0.8,\n        description: 'Set or update due date'\n      }\n    ];\n\n    // Extract entities (task names, milestone names, etc.)\n    const entities = {\n      taskNames: [],\n      milestoneNames: [],\n      dates: [],\n      priorities: []\n    };\n\n    // Look for quoted text (likely task/milestone names)\n    const quotedText = message.match(/\"([^\"]+)\"/g);\n    if (quotedText) {\n      entities.taskNames = quotedText.map(q => q.replace(/\"/g, ''));\n    }\n\n    // Look for date patterns\n    const datePatterns = message.match(/\\b\\d{1,2}\\/\\d{1,2}\\/\\d{4}\\b|\\b\\d{1,2}-\\d{1,2}-\\d{4}\\b|tomorrow|today|next week|next month/gi);\n    if (datePatterns) {\n      entities.dates = datePatterns;\n    }\n\n    // Look for priority keywords\n    const priorityPatterns = message.match(/\\b(high|low|medium|urgent|critical|normal)\\s*priority\\b/gi);\n    if (priorityPatterns) {\n      entities.priorities = priorityPatterns;\n    }\n\n    // Match actions against patterns\n    actionPatterns.forEach(({ pattern, type, confidence, description }) => {\n      if (pattern.test(lowerMessage)) {\n        actions.push({\n          type,\n          confidence,\n          description,\n          entities: entities,\n          originalMessage: message\n        });\n      }\n    });\n\n    return actions;\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  const formatTimestamp = (timestamp) => {\n    return new Date(timestamp).toLocaleTimeString('en-US', {\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  return (\n    <Box sx={{ height: '70vh', display: 'flex', flexDirection: 'column' }}>\n      {/* Header */}\n      <Paper\n        elevation={0}\n        sx={{\n          p: 2,\n          borderRadius: '12px 12px 0 0',\n          border: '1px solid #f0f0f0',\n          borderBottom: 'none',\n          backgroundColor: '#fafafa'\n        }}\n      >\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\n          <Avatar\n            sx={{\n              backgroundColor: mainYellowColor,\n              width: 40,\n              height: 40\n            }}\n          >\n            <Iconify icon=\"mdi:robot\" width={24} height={24} color=\"#fff\" />\n          </Avatar>\n          <Box>\n            <Typography\n              variant=\"h6\"\n              sx={{\n                fontFamily: '\"Recursive Variable\", sans-serif',\n                fontWeight: 600,\n                color: '#333'\n              }}\n            >\n              AI Project Agent\n            </Typography>\n            <Typography\n              variant=\"caption\"\n              sx={{\n                color: '#666',\n                fontFamily: '\"Recursive Variable\", sans-serif'\n              }}\n            >\n              Managing: {planInfo?.name}\n            </Typography>\n          </Box>\n          <Chip\n            label=\"Beta\"\n            size=\"small\"\n            sx={{\n              backgroundColor: `${mainYellowColor}20`,\n              color: mainYellowColor,\n              fontWeight: 600,\n              ml: 'auto'\n            }}\n          />\n        </Box>\n      </Paper>\n\n      {/* Messages Area */}\n      <Paper\n        elevation={0}\n        sx={{\n          flex: 1,\n          border: '1px solid #f0f0f0',\n          borderTop: 'none',\n          borderBottom: 'none',\n          overflow: 'auto',\n          p: 2,\n          backgroundColor: '#fff'\n        }}\n      >\n        {conversations.length === 0 ? (\n          <Box\n            sx={{\n              display: 'flex',\n              flexDirection: 'column',\n              alignItems: 'center',\n              justifyContent: 'center',\n              height: '100%',\n              textAlign: 'center'\n            }}\n          >\n            <Avatar\n              sx={{\n                backgroundColor: `${mainYellowColor}20`,\n                width: 60,\n                height: 60,\n                mb: 2\n              }}\n            >\n              <Iconify icon=\"mdi:robot\" width={32} height={32} color={mainYellowColor} />\n            </Avatar>\n            <Typography\n              variant=\"h6\"\n              sx={{\n                fontFamily: '\"Recursive Variable\", sans-serif',\n                fontWeight: 600,\n                color: '#333',\n                mb: 1\n              }}\n            >\n              Welcome to AI Project Agent\n            </Typography>\n            <Typography\n              variant=\"body2\"\n              sx={{\n                color: '#666',\n                fontFamily: '\"Recursive Variable\", sans-serif',\n                maxWidth: 400\n              }}\n            >\n              I'm here to help you manage your project. Ask me questions or request changes to tasks, milestones, and more!\n            </Typography>\n          </Box>\n        ) : (\n          <Box>\n            {conversations.map((message) => (\n              <Box\n                key={message.id}\n                sx={{\n                  display: 'flex',\n                  justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start',\n                  mb: 2\n                }}\n              >\n                <Box\n                  sx={{\n                    maxWidth: '70%',\n                    display: 'flex',\n                    flexDirection: message.type === 'user' ? 'row-reverse' : 'row',\n                    alignItems: 'flex-start',\n                    gap: 1\n                  }}\n                >\n                  <Avatar\n                    sx={{\n                      width: 32,\n                      height: 32,\n                      backgroundColor: message.type === 'user' ? '#e0e0e0' : mainYellowColor\n                    }}\n                  >\n                    <Iconify\n                      icon={message.type === 'user' ? \"material-symbols:person\" : \"mdi:robot\"}\n                      width={18}\n                      height={18}\n                      color={message.type === 'user' ? '#666' : '#fff'}\n                    />\n                  </Avatar>\n                  <Box>\n                    <Paper\n                      elevation={0}\n                      sx={{\n                        p: 1.5,\n                        borderRadius: '12px',\n                        backgroundColor: message.type === 'user' ? mainYellowColor : '#f5f5f5',\n                        color: message.type === 'user' ? '#fff' : '#333',\n                        border: message.isError ? '1px solid #f44336' : 'none'\n                      }}\n                    >\n                      <Typography\n                        variant=\"body2\"\n                        sx={{\n                          fontFamily: '\"Recursive Variable\", sans-serif',\n                          lineHeight: 1.5,\n                          whiteSpace: 'pre-line'\n                        }}\n                      >\n                        {message.content}\n                      </Typography>\n                    </Paper>\n                    <Typography\n                      variant=\"caption\"\n                      sx={{\n                        color: '#999',\n                        fontFamily: '\"Recursive Variable\", sans-serif',\n                        fontSize: '0.7rem',\n                        mt: 0.5,\n                        display: 'block',\n                        textAlign: message.type === 'user' ? 'right' : 'left'\n                      }}\n                    >\n                      {formatTimestamp(message.timestamp)}\n                    </Typography>\n                  </Box>\n                </Box>\n              </Box>\n            ))}\n            {isLoading && (\n              <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 2 }}>\n                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>\n                  <Avatar\n                    sx={{\n                      width: 32,\n                      height: 32,\n                      backgroundColor: mainYellowColor\n                    }}\n                  >\n                    <Iconify icon=\"mdi:robot\" width={18} height={18} color=\"#fff\" />\n                  </Avatar>\n                  <Paper\n                    elevation={0}\n                    sx={{\n                      p: 1.5,\n                      borderRadius: '12px',\n                      backgroundColor: '#f5f5f5',\n                      display: 'flex',\n                      alignItems: 'center',\n                      gap: 1\n                    }}\n                  >\n                    <CircularProgress size={16} sx={{ color: mainYellowColor }} />\n                    <Typography\n                      variant=\"body2\"\n                      sx={{\n                        fontFamily: '\"Recursive Variable\", sans-serif',\n                        color: '#666'\n                      }}\n                    >\n                      Thinking...\n                    </Typography>\n                  </Paper>\n                </Box>\n              </Box>\n            )}\n            <div ref={messagesEndRef} />\n          </Box>\n        )}\n      </Paper>\n\n      {/* Input Area */}\n      <Paper\n        elevation={0}\n        sx={{\n          p: 2,\n          borderRadius: '0 0 12px 12px',\n          border: '1px solid #f0f0f0',\n          borderTop: 'none'\n        }}\n      >\n        <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>\n          <TextField\n            inputRef={inputRef}\n            value={currentMessage}\n            onChange={(e) => setCurrentMessage(e.target.value)}\n            onKeyPress={handleKeyPress}\n            placeholder=\"Ask me anything about your project...\"\n            multiline\n            maxRows={3}\n            fullWidth\n            variant=\"outlined\"\n            disabled={isLoading}\n            sx={{\n              '& .MuiOutlinedInput-root': {\n                borderRadius: '8px',\n                fontFamily: '\"Recursive Variable\", sans-serif'\n              }\n            }}\n          />\n          <Tooltip title=\"Send message\">\n            <IconButton\n              onClick={() => handleSendMessage()}\n              disabled={!currentMessage.trim() || isLoading}\n              sx={{\n                backgroundColor: currentMessage.trim() && !isLoading ? mainYellowColor : '#f0f0f0',\n                color: currentMessage.trim() && !isLoading ? '#fff' : '#999',\n                '&:hover': {\n                  backgroundColor: currentMessage.trim() && !isLoading ? '#E69500' : '#f0f0f0'\n                }\n              }}\n            >\n              <Iconify icon=\"material-symbols:send\" width={20} height={20} />\n            </IconButton>\n          </Tooltip>\n        </Box>\n      </Paper>\n    </Box>\n  );\n};\n\nexport default AgentTab;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,SAAS,EACTC,UAAU,EACVC,MAAM,EACNC,OAAO,EACPC,gBAAgB,EAChBC,IAAI,EACJC,OAAO,QACF,eAAe;AACtB,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,QAAQ,GAAGC,IAAA,IAAgC;EAAAC,EAAA;EAAA,IAA/B;IAAEC,QAAQ;IAAEC;EAAa,CAAC,GAAAH,IAAA;EAC1C,MAAM,CAACI,aAAa,EAAEC,gBAAgB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACyB,cAAc,EAAEC,iBAAiB,CAAC,GAAG1B,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAAC2B,SAAS,EAAEC,YAAY,CAAC,GAAG5B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM6B,cAAc,GAAG3B,MAAM,CAAC,IAAI,CAAC;EACnC,MAAM4B,QAAQ,GAAG5B,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAM6B,QAAQ,GAAGhB,WAAW,CAAC,CAAC;;EAE9B;EACAd,SAAS,CAAC,MAAM;IAAA,IAAA+B,eAAA;IACd,MAAMC,kBAAkB,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC,IAAI,IAAI,CAAC;IAC1F,MAAMC,iBAAiB,GAAGL,kBAAkB,CAACM,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,MAAM,MAAKpB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqB,EAAE,EAAC;IACzFlB,gBAAgB,CAACc,iBAAiB,CAAC;;IAEnC;IACA,MAAMK,cAAc,GAAGP,YAAY,CAACC,OAAO,CAAC,uBAAuB,CAAC;IACpE,IAAIM,cAAc,EAAE;MAClB,IAAI;QAAA,IAAAC,qBAAA;QACF,MAAMC,WAAW,GAAGX,IAAI,CAACC,KAAK,CAACQ,cAAc,CAAC;QAC9C,IAAI,EAAAC,qBAAA,GAAAC,WAAW,CAACxB,QAAQ,cAAAuB,qBAAA,uBAApBA,qBAAA,CAAsBF,EAAE,OAAKrB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqB,EAAE,GAAE;UAC7CI,iBAAiB,CAACD,WAAW,CAACE,OAAO,CAAC;QACxC;QACA;QACAX,YAAY,CAACY,UAAU,CAAC,uBAAuB,CAAC;MAClD,CAAC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;QACzDb,YAAY,CAACY,UAAU,CAAC,uBAAuB,CAAC;MAClD;IACF;;IAEA;IACA,KAAAhB,eAAA,GAAID,QAAQ,CAACoB,KAAK,cAAAnB,eAAA,eAAdA,eAAA,CAAgBe,OAAO,EAAE;MAC3BD,iBAAiB,CAACf,QAAQ,CAACoB,KAAK,CAACJ,OAAO,CAAC;IAC3C;EACF,CAAC,EAAE,CAAC1B,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqB,EAAE,EAAEX,QAAQ,CAACoB,KAAK,CAAC,CAAC;;EAElC;EACAlD,SAAS,CAAC,MAAM;IAAA,IAAAmD,qBAAA;IACd,CAAAA,qBAAA,GAAAvB,cAAc,CAACwB,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAChE,CAAC,EAAE,CAAChC,aAAa,CAAC,CAAC;EAEnB,MAAMuB,iBAAiB,GAAG,eAAAA,CAAA,EAAwC;IAAA,IAAjCU,WAAW,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGhC,cAAc;IAC3D,IAAI,CAAC+B,WAAW,CAACI,IAAI,CAAC,CAAC,IAAIjC,SAAS,EAAE;IAEtCC,YAAY,CAAC,IAAI,CAAC;IAClB,MAAMiC,WAAW,GAAG;MAClBnB,EAAE,EAAEoB,IAAI,CAACC,GAAG,CAAC,CAAC;MACdC,IAAI,EAAE,MAAM;MACZC,OAAO,EAAET,WAAW,CAACI,IAAI,CAAC,CAAC;MAC3BM,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC;IACpC,CAAC;IAED,MAAMC,gBAAgB,GAAG,CAAC,GAAG7C,aAAa,EAAEsC,WAAW,CAAC;IACxDrC,gBAAgB,CAAC4C,gBAAgB,CAAC;IAClC1C,iBAAiB,CAAC,EAAE,CAAC;IAErB,IAAI;MACF;MACA,MAAM,IAAI2C,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MAEvD,MAAME,UAAU,GAAG;QACjB9B,EAAE,EAAEoB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;QAClBC,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAEQ,kBAAkB,CAACjB,WAAW,EAAEnC,QAAQ,CAAC;QAClD6C,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC;QACnCO,OAAO,EAAEC,cAAc,CAACnB,WAAW;MACrC,CAAC;MAED,MAAMoB,oBAAoB,GAAG,CAAC,GAAGR,gBAAgB,EAAEI,UAAU,CAAC;MAC9DhD,gBAAgB,CAACoD,oBAAoB,CAAC;;MAEtC;MACA,MAAMC,gBAAgB,GAAG3C,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC,IAAI,IAAI,CAAC;MACxF,MAAMyC,sBAAsB,GAAGD,gBAAgB,CAACtC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,MAAM,MAAKpB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqB,EAAE,EAAC;MAC5F,MAAMJ,iBAAiB,GAAGsC,oBAAoB,CAACG,GAAG,CAACvC,IAAI,KAAK;QAC1D,GAAGA,IAAI;QACPC,MAAM,EAAEpB,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqB,EAAE;QACpBsC,QAAQ,EAAE3D,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE4D;MACtB,CAAC,CAAC,CAAC;MAEH7C,YAAY,CAAC8C,OAAO,CAAC,qBAAqB,EAAEhD,IAAI,CAACiD,SAAS,CAAC,CACzD,GAAGL,sBAAsB,EACzB,GAAGxC,iBAAiB,CACrB,CAAC,CAAC;IAEL,CAAC,CAAC,OAAOW,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMmC,aAAa,GAAG;QACpB1C,EAAE,EAAEoB,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC;QAClBC,IAAI,EAAE,WAAW;QACjBC,OAAO,EAAE,8EAA8E;QACvFC,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC;QACnCkB,OAAO,EAAE;MACX,CAAC;MACD7D,gBAAgB,CAAC8D,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEF,aAAa,CAAC,CAAC;IACpD,CAAC,SAAS;MACRxD,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM6C,kBAAkB,GAAGA,CAAC1B,OAAO,EAAE1B,QAAQ,KAAK;IAChD,MAAMkE,YAAY,GAAGxC,OAAO,CAACyC,WAAW,CAAC,CAAC;IAC1C,MAAMC,UAAU,GAAG,CAAApE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEoE,UAAU,KAAI,EAAE;IAC7C,MAAMC,UAAU,GAAGD,UAAU,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,SAAS;MAAA,IAAAC,gBAAA;MAAA,OAAKF,GAAG,IAAI,EAAAE,gBAAA,GAAAD,SAAS,CAACE,KAAK,cAAAD,gBAAA,uBAAfA,gBAAA,CAAiBpC,MAAM,KAAI,CAAC,CAAC;IAAA,GAAE,CAAC,CAAC;IACjG,MAAMsC,aAAa,GAAGP,UAAU,CAACE,MAAM,CAAC,CAACC,GAAG,EAAEC,SAAS;MAAA,IAAAI,iBAAA;MAAA,OACrDL,GAAG,KAAAK,iBAAA,GAAGJ,SAAS,CAACE,KAAK,cAAAE,iBAAA,uBAAfA,iBAAA,CAAiBN,MAAM,CAAC,CAACO,OAAO,EAAEC,IAAI;QAAA,IAAAC,cAAA;QAAA,OAAKF,OAAO,IAAI,EAAAE,cAAA,GAAAD,IAAI,CAACE,QAAQ,cAAAD,cAAA,uBAAbA,cAAA,CAAe1C,MAAM,KAAI,CAAC,CAAC;MAAA,GAAE,CAAC,CAAC,KAAI,CAAC;IAAA,GAAE,CAAC,CAAC;;IAEtG;IACA,MAAM4C,cAAc,GAAG;MACrBrB,IAAI,EAAE,CAAA5D,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE4D,IAAI,KAAI,cAAc;MACtCsB,cAAc,EAAEd,UAAU,CAAC/B,MAAM;MACjC8C,SAAS,EAAEd,UAAU;MACrBe,YAAY,EAAET,aAAa;MAC3BU,cAAc,EAAEjB,UAAU,CAACV,GAAG,CAAC4B,CAAC,IAAIA,CAAC,CAAC1B,IAAI,CAAC,CAAC2B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAE;IAC3D,CAAC;;IAED;IACA,IAAIrB,YAAY,CAACsB,QAAQ,CAAC,WAAW,CAAC,KAAKtB,YAAY,CAACsB,QAAQ,CAAC,KAAK,CAAC,IAAItB,YAAY,CAACsB,QAAQ,CAAC,QAAQ,CAAC,IAAItB,YAAY,CAACsB,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE;MAC3I,OAAO,0CAA0CP,cAAc,CAACrB,IAAI,0BAA0BqB,cAAc,CAACC,cAAc,gBAAgBD,cAAc,CAACI,cAAc,CAACI,IAAI,CAAC,IAAI,CAAC,GAAGR,cAAc,CAACC,cAAc,GAAG,CAAC,GAAG,KAAK,GAAG,EAAE;AAC1O;AACA;AACA;AACA;AACA;AACA;AACA;AACA,mGAAmG;IAC/F;IAEA,IAAIhB,YAAY,CAACsB,QAAQ,CAAC,UAAU,CAAC,IAAItB,YAAY,CAACsB,QAAQ,CAAC,QAAQ,CAAC,IAAItB,YAAY,CAACsB,QAAQ,CAAC,UAAU,CAAC,EAAE;MAC7G,MAAME,eAAe,GAAGtB,UAAU,CAACV,GAAG,CAACc,SAAS,IAAI;QAAA,IAAAmB,iBAAA,EAAAC,iBAAA,EAAAC,qBAAA;QAClD,MAAMV,SAAS,GAAG,EAAAQ,iBAAA,GAAAnB,SAAS,CAACE,KAAK,cAAAiB,iBAAA,uBAAfA,iBAAA,CAAiBtD,MAAM,KAAI,CAAC;QAC9C,MAAMyD,cAAc,GAAG,EAAAF,iBAAA,GAAApB,SAAS,CAACE,KAAK,cAAAkB,iBAAA,wBAAAC,qBAAA,GAAfD,iBAAA,CAAiB1E,MAAM,CAAC4D,IAAI,IAAIA,IAAI,CAACiB,MAAM,KAAK,WAAW,CAAC,cAAAF,qBAAA,uBAA5DA,qBAAA,CAA8DxD,MAAM,KAAI,CAAC;QAChG,OAAO,KAAKmC,SAAS,CAACZ,IAAI,KAAKkC,cAAc,IAAIX,SAAS,kBAAkB;MAC9E,CAAC,CAAC,CAACM,IAAI,CAAC,IAAI,CAAC;MAEb,OAAO,qCAAqCR,cAAc,CAACrB,IAAI;AACrE;AACA;AACA,IAAIqB,cAAc,CAACC,cAAc;AACjC,IAAID,cAAc,CAACE,SAAS;AAC5B,IAAIF,cAAc,CAACG,YAAY;AAC/B;AACA;AACA,EAAEM,eAAe;AACjB;AACA;AACA;AACA;AACA,oDAAoD;IAChD;IAEA,IAAIxB,YAAY,CAACsB,QAAQ,CAAC,UAAU,CAAC,IAAItB,YAAY,CAACsB,QAAQ,CAAC,MAAM,CAAC,IAAItB,YAAY,CAACsB,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACzG,MAAMQ,cAAc,GAAG5B,UAAU,CAAC6B,OAAO,CAACzB,SAAS;QAAA,IAAA0B,iBAAA;QAAA,OACjD,EAAAA,iBAAA,GAAA1B,SAAS,CAACE,KAAK,cAAAwB,iBAAA,uBAAfA,iBAAA,CAAiBhF,MAAM,CAAC4D,IAAI,IAAIA,IAAI,CAACiB,MAAM,KAAK,WAAW,CAAC,CAACrC,GAAG,CAACoB,IAAI,IACnE,IAAIA,IAAI,CAAClB,IAAI,QAAQY,SAAS,CAACZ,IAAI,EACrC,CAAC,KAAI,EAAE;MAAA,CACT,CAAC,CAAC2B,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;MAEb,OAAO;AACb;AACA,EAAES,cAAc,CAACtC,GAAG,CAACoB,IAAI,IAAI,KAAKA,IAAI,EAAE,CAAC,CAACW,IAAI,CAAC,IAAI,CAAC;AACpD;AACA;AACA;AACA;AACA;AACA;AACA,gDAAgD;IAC5C;IAEA,IAAIvB,YAAY,CAACsB,QAAQ,CAAC,KAAK,CAAC,IAAItB,YAAY,CAACsB,QAAQ,CAAC,QAAQ,CAAC,IAAItB,YAAY,CAACsB,QAAQ,CAAC,KAAK,CAAC,EAAE;MACnG,IAAItB,YAAY,CAACsB,QAAQ,CAAC,MAAM,CAAC,EAAE;QACjC,OAAO,qEAAqEP,cAAc,CAACC,cAAc;AACjH;AACA,EAAED,cAAc,CAACI,cAAc,CAAC3B,GAAG,CAAC,CAACE,IAAI,EAAEuC,CAAC,KAAK,GAAGA,CAAC,GAAG,CAAC,KAAKvC,IAAI,EAAE,CAAC,CAAC6B,IAAI,CAAC,IAAI,CAAC;AAChF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iCAAiC;MAC3B;MAEA,OAAO,sCAAsCR,cAAc,CAACrB,IAAI;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,8CAA8C;IAC1C;IAEA,IAAIM,YAAY,CAACsB,QAAQ,CAAC,QAAQ,CAAC,IAAItB,YAAY,CAACsB,QAAQ,CAAC,QAAQ,CAAC,EAAE;MACtE,OAAO;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,oFAAoF;IAChF;IAEA,IAAItB,YAAY,CAACsB,QAAQ,CAAC,QAAQ,CAAC,IAAItB,YAAY,CAACsB,QAAQ,CAAC,QAAQ,CAAC,IAAItB,YAAY,CAACsB,QAAQ,CAAC,MAAM,CAAC,IAAItB,YAAY,CAACsB,QAAQ,CAAC,QAAQ,CAAC,EAAE;MAC1I,OAAO,iDAAiDP,cAAc,CAACrB,IAAI;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,qEAAqE;IACjE;IAEA,IAAIM,YAAY,CAACsB,QAAQ,CAAC,MAAM,CAAC,IAAItB,YAAY,CAACsB,QAAQ,CAAC,iBAAiB,CAAC,IAAItB,YAAY,CAACsB,QAAQ,CAAC,cAAc,CAAC,EAAE;MACtH,OAAO,sCAAsCP,cAAc,CAACrB,IAAI;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,iGAAiG;IAC7F;;IAEA;IACA,OAAO,6BAA6BlC,OAAO;AAC/C;AACA,yBAAyBuD,cAAc,CAACrB,IAAI,UAAUqB,cAAc,CAACC,cAAc,mBAAmBD,cAAc,CAACE,SAAS;AAC9H;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,+FAA+F;EAC7F,CAAC;EAED,MAAM7B,cAAc,GAAI5B,OAAO,IAAK;IAClC,MAAM2B,OAAO,GAAG,EAAE;IAClB,MAAMa,YAAY,GAAGxC,OAAO,CAACyC,WAAW,CAAC,CAAC;;IAE1C;IACA,MAAMiC,cAAc,GAAG,CACrB;MACEC,OAAO,EAAE,uCAAuC;MAChD1D,IAAI,EAAE,eAAe;MACrB2D,UAAU,EAAE,GAAG;MACfC,WAAW,EAAE;IACf,CAAC,EACD;MACEF,OAAO,EAAE,6BAA6B;MACtC1D,IAAI,EAAE,eAAe;MACrB2D,UAAU,EAAE,GAAG;MACfC,WAAW,EAAE;IACf,CAAC,EACD;MACEF,OAAO,EAAE,wBAAwB;MACjC1D,IAAI,EAAE,UAAU;MAChB2D,UAAU,EAAE,GAAG;MACfC,WAAW,EAAE;IACf,CAAC,EACD;MACEF,OAAO,EAAE,2BAA2B;MACpC1D,IAAI,EAAE,aAAa;MACnB2D,UAAU,EAAE,GAAG;MACfC,WAAW,EAAE;IACf,CAAC,EACD;MACEF,OAAO,EAAE,uBAAuB;MAChC1D,IAAI,EAAE,aAAa;MACnB2D,UAAU,EAAE,GAAG;MACfC,WAAW,EAAE;IACf,CAAC,EACD;MACEF,OAAO,EAAE,6BAA6B;MACtC1D,IAAI,EAAE,aAAa;MACnB2D,UAAU,EAAE,GAAG;MACfC,WAAW,EAAE;IACf,CAAC,EACD;MACEF,OAAO,EAAE,4BAA4B;MACrC1D,IAAI,EAAE,eAAe;MACrB2D,UAAU,EAAE,GAAG;MACfC,WAAW,EAAE;IACf,CAAC,EACD;MACEF,OAAO,EAAE,uBAAuB;MAChC1D,IAAI,EAAE,WAAW;MACjB2D,UAAU,EAAE,GAAG;MACfC,WAAW,EAAE;IACf,CAAC,EACD;MACEF,OAAO,EAAE,mBAAmB;MAC5B1D,IAAI,EAAE,aAAa;MACnB2D,UAAU,EAAE,GAAG;MACfC,WAAW,EAAE;IACf,CAAC,EACD;MACEF,OAAO,EAAE,8BAA8B;MACvC1D,IAAI,EAAE,cAAc;MACpB2D,UAAU,EAAE,GAAG;MACfC,WAAW,EAAE;IACf,CAAC,CACF;;IAED;IACA,MAAMC,QAAQ,GAAG;MACfC,SAAS,EAAE,EAAE;MACbpB,cAAc,EAAE,EAAE;MAClBqB,KAAK,EAAE,EAAE;MACTC,UAAU,EAAE;IACd,CAAC;;IAED;IACA,MAAMC,UAAU,GAAGlF,OAAO,CAACmF,KAAK,CAAC,YAAY,CAAC;IAC9C,IAAID,UAAU,EAAE;MACdJ,QAAQ,CAACC,SAAS,GAAGG,UAAU,CAAClD,GAAG,CAACoD,CAAC,IAAIA,CAAC,CAACC,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;IAC/D;;IAEA;IACA,MAAMC,YAAY,GAAGtF,OAAO,CAACmF,KAAK,CAAC,6FAA6F,CAAC;IACjI,IAAIG,YAAY,EAAE;MAChBR,QAAQ,CAACE,KAAK,GAAGM,YAAY;IAC/B;;IAEA;IACA,MAAMC,gBAAgB,GAAGvF,OAAO,CAACmF,KAAK,CAAC,2DAA2D,CAAC;IACnG,IAAII,gBAAgB,EAAE;MACpBT,QAAQ,CAACG,UAAU,GAAGM,gBAAgB;IACxC;;IAEA;IACAb,cAAc,CAACc,OAAO,CAACC,KAAA,IAAgD;MAAA,IAA/C;QAAEd,OAAO;QAAE1D,IAAI;QAAE2D,UAAU;QAAEC;MAAY,CAAC,GAAAY,KAAA;MAChE,IAAId,OAAO,CAACe,IAAI,CAAClD,YAAY,CAAC,EAAE;QAC9Bb,OAAO,CAACgE,IAAI,CAAC;UACX1E,IAAI;UACJ2D,UAAU;UACVC,WAAW;UACXC,QAAQ,EAAEA,QAAQ;UAClBc,eAAe,EAAE5F;QACnB,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;IAEF,OAAO2B,OAAO;EAChB,CAAC;EAED,MAAMkE,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClBlG,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;EAED,MAAMmG,eAAe,GAAI/E,SAAS,IAAK;IACrC,OAAO,IAAIJ,IAAI,CAACI,SAAS,CAAC,CAACgF,kBAAkB,CAAC,OAAO,EAAE;MACrDC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,oBACEnI,OAAA,CAACd,GAAG;IAACkJ,EAAE,EAAE;MAAEC,MAAM,EAAE,MAAM;MAAEC,OAAO,EAAE,MAAM;MAAEC,aAAa,EAAE;IAAS,CAAE;IAAAC,QAAA,gBAEpExI,OAAA,CAACZ,KAAK;MACJqJ,SAAS,EAAE,CAAE;MACbL,EAAE,EAAE;QACFM,CAAC,EAAE,CAAC;QACJC,YAAY,EAAE,eAAe;QAC7BC,MAAM,EAAE,mBAAmB;QAC3BC,YAAY,EAAE,MAAM;QACpBC,eAAe,EAAE;MACnB,CAAE;MAAAN,QAAA,eAEFxI,OAAA,CAACd,GAAG;QAACkJ,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAES,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAE,CAAE;QAAAR,QAAA,gBACzDxI,OAAA,CAACT,MAAM;UACL6I,EAAE,EAAE;YACFU,eAAe,EAAEjJ,eAAe;YAChCoJ,KAAK,EAAE,EAAE;YACTZ,MAAM,EAAE;UACV,CAAE;UAAAG,QAAA,eAEFxI,OAAA,CAACJ,OAAO;YAACsJ,IAAI,EAAC,WAAW;YAACD,KAAK,EAAE,EAAG;YAACZ,MAAM,EAAE,EAAG;YAACc,KAAK,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1D,CAAC,eACTvJ,OAAA,CAACd,GAAG;UAAAsJ,QAAA,gBACFxI,OAAA,CAACb,UAAU;YACTqK,OAAO,EAAC,IAAI;YACZpB,EAAE,EAAE;cACFqB,UAAU,EAAE,kCAAkC;cAC9CC,UAAU,EAAE,GAAG;cACfP,KAAK,EAAE;YACT,CAAE;YAAAX,QAAA,EACH;UAED;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbvJ,OAAA,CAACb,UAAU;YACTqK,OAAO,EAAC,SAAS;YACjBpB,EAAE,EAAE;cACFe,KAAK,EAAE,MAAM;cACbM,UAAU,EAAE;YACd,CAAE;YAAAjB,QAAA,GACH,YACW,EAACpI,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE4D,IAAI;UAAA;YAAAoF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACf,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACNvJ,OAAA,CAACN,IAAI;UACHiK,KAAK,EAAC,MAAM;UACZC,IAAI,EAAC,OAAO;UACZxB,EAAE,EAAE;YACFU,eAAe,EAAE,GAAGjJ,eAAe,IAAI;YACvCsJ,KAAK,EAAEtJ,eAAe;YACtB6J,UAAU,EAAE,GAAG;YACfG,EAAE,EAAE;UACN;QAAE;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGRvJ,OAAA,CAACZ,KAAK;MACJqJ,SAAS,EAAE,CAAE;MACbL,EAAE,EAAE;QACF0B,IAAI,EAAE,CAAC;QACPlB,MAAM,EAAE,mBAAmB;QAC3BmB,SAAS,EAAE,MAAM;QACjBlB,YAAY,EAAE,MAAM;QACpBmB,QAAQ,EAAE,MAAM;QAChBtB,CAAC,EAAE,CAAC;QACJI,eAAe,EAAE;MACnB,CAAE;MAAAN,QAAA,EAEDlI,aAAa,CAACmC,MAAM,KAAK,CAAC,gBACzBzC,OAAA,CAACd,GAAG;QACFkJ,EAAE,EAAE;UACFE,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBQ,UAAU,EAAE,QAAQ;UACpBkB,cAAc,EAAE,QAAQ;UACxB5B,MAAM,EAAE,MAAM;UACd6B,SAAS,EAAE;QACb,CAAE;QAAA1B,QAAA,gBAEFxI,OAAA,CAACT,MAAM;UACL6I,EAAE,EAAE;YACFU,eAAe,EAAE,GAAGjJ,eAAe,IAAI;YACvCoJ,KAAK,EAAE,EAAE;YACTZ,MAAM,EAAE,EAAE;YACV8B,EAAE,EAAE;UACN,CAAE;UAAA3B,QAAA,eAEFxI,OAAA,CAACJ,OAAO;YAACsJ,IAAI,EAAC,WAAW;YAACD,KAAK,EAAE,EAAG;YAACZ,MAAM,EAAE,EAAG;YAACc,KAAK,EAAEtJ;UAAgB;YAAAuJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE,CAAC,eACTvJ,OAAA,CAACb,UAAU;UACTqK,OAAO,EAAC,IAAI;UACZpB,EAAE,EAAE;YACFqB,UAAU,EAAE,kCAAkC;YAC9CC,UAAU,EAAE,GAAG;YACfP,KAAK,EAAE,MAAM;YACbgB,EAAE,EAAE;UACN,CAAE;UAAA3B,QAAA,EACH;QAED;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbvJ,OAAA,CAACb,UAAU;UACTqK,OAAO,EAAC,OAAO;UACfpB,EAAE,EAAE;YACFe,KAAK,EAAE,MAAM;YACbM,UAAU,EAAE,kCAAkC;YAC9CW,QAAQ,EAAE;UACZ,CAAE;UAAA5B,QAAA,EACH;QAED;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,gBAENvJ,OAAA,CAACd,GAAG;QAAAsJ,QAAA,GACDlI,aAAa,CAACwD,GAAG,CAAEhC,OAAO,iBACzB9B,OAAA,CAACd,GAAG;UAEFkJ,EAAE,EAAE;YACFE,OAAO,EAAE,MAAM;YACf2B,cAAc,EAAEnI,OAAO,CAACiB,IAAI,KAAK,MAAM,GAAG,UAAU,GAAG,YAAY;YACnEoH,EAAE,EAAE;UACN,CAAE;UAAA3B,QAAA,eAEFxI,OAAA,CAACd,GAAG;YACFkJ,EAAE,EAAE;cACFgC,QAAQ,EAAE,KAAK;cACf9B,OAAO,EAAE,MAAM;cACfC,aAAa,EAAEzG,OAAO,CAACiB,IAAI,KAAK,MAAM,GAAG,aAAa,GAAG,KAAK;cAC9DgG,UAAU,EAAE,YAAY;cACxBC,GAAG,EAAE;YACP,CAAE;YAAAR,QAAA,gBAEFxI,OAAA,CAACT,MAAM;cACL6I,EAAE,EAAE;gBACFa,KAAK,EAAE,EAAE;gBACTZ,MAAM,EAAE,EAAE;gBACVS,eAAe,EAAEhH,OAAO,CAACiB,IAAI,KAAK,MAAM,GAAG,SAAS,GAAGlD;cACzD,CAAE;cAAA2I,QAAA,eAEFxI,OAAA,CAACJ,OAAO;gBACNsJ,IAAI,EAAEpH,OAAO,CAACiB,IAAI,KAAK,MAAM,GAAG,yBAAyB,GAAG,WAAY;gBACxEkG,KAAK,EAAE,EAAG;gBACVZ,MAAM,EAAE,EAAG;gBACXc,KAAK,EAAErH,OAAO,CAACiB,IAAI,KAAK,MAAM,GAAG,MAAM,GAAG;cAAO;gBAAAqG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACI,CAAC,eACTvJ,OAAA,CAACd,GAAG;cAAAsJ,QAAA,gBACFxI,OAAA,CAACZ,KAAK;gBACJqJ,SAAS,EAAE,CAAE;gBACbL,EAAE,EAAE;kBACFM,CAAC,EAAE,GAAG;kBACNC,YAAY,EAAE,MAAM;kBACpBG,eAAe,EAAEhH,OAAO,CAACiB,IAAI,KAAK,MAAM,GAAGlD,eAAe,GAAG,SAAS;kBACtEsJ,KAAK,EAAErH,OAAO,CAACiB,IAAI,KAAK,MAAM,GAAG,MAAM,GAAG,MAAM;kBAChD6F,MAAM,EAAE9G,OAAO,CAACsC,OAAO,GAAG,mBAAmB,GAAG;gBAClD,CAAE;gBAAAoE,QAAA,eAEFxI,OAAA,CAACb,UAAU;kBACTqK,OAAO,EAAC,OAAO;kBACfpB,EAAE,EAAE;oBACFqB,UAAU,EAAE,kCAAkC;oBAC9CY,UAAU,EAAE,GAAG;oBACfC,UAAU,EAAE;kBACd,CAAE;kBAAA9B,QAAA,EAED1G,OAAO,CAACkB;gBAAO;kBAAAoG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR,CAAC,eACRvJ,OAAA,CAACb,UAAU;gBACTqK,OAAO,EAAC,SAAS;gBACjBpB,EAAE,EAAE;kBACFe,KAAK,EAAE,MAAM;kBACbM,UAAU,EAAE,kCAAkC;kBAC9Cc,QAAQ,EAAE,QAAQ;kBAClBC,EAAE,EAAE,GAAG;kBACPlC,OAAO,EAAE,OAAO;kBAChB4B,SAAS,EAAEpI,OAAO,CAACiB,IAAI,KAAK,MAAM,GAAG,OAAO,GAAG;gBACjD,CAAE;gBAAAyF,QAAA,EAEDR,eAAe,CAAClG,OAAO,CAACmB,SAAS;cAAC;gBAAAmG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAlEDzH,OAAO,CAACL,EAAE;UAAA2H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmEZ,CACN,CAAC,EACD7I,SAAS,iBACRV,OAAA,CAACd,GAAG;UAACkJ,EAAE,EAAE;YAAEE,OAAO,EAAE,MAAM;YAAE2B,cAAc,EAAE,YAAY;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAA3B,QAAA,eAChExI,OAAA,CAACd,GAAG;YAACkJ,EAAE,EAAE;cAAEE,OAAO,EAAE,MAAM;cAAES,UAAU,EAAE,YAAY;cAAEC,GAAG,EAAE;YAAE,CAAE;YAAAR,QAAA,gBAC7DxI,OAAA,CAACT,MAAM;cACL6I,EAAE,EAAE;gBACFa,KAAK,EAAE,EAAE;gBACTZ,MAAM,EAAE,EAAE;gBACVS,eAAe,EAAEjJ;cACnB,CAAE;cAAA2I,QAAA,eAEFxI,OAAA,CAACJ,OAAO;gBAACsJ,IAAI,EAAC,WAAW;gBAACD,KAAK,EAAE,EAAG;gBAACZ,MAAM,EAAE,EAAG;gBAACc,KAAK,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACTvJ,OAAA,CAACZ,KAAK;cACJqJ,SAAS,EAAE,CAAE;cACbL,EAAE,EAAE;gBACFM,CAAC,EAAE,GAAG;gBACNC,YAAY,EAAE,MAAM;gBACpBG,eAAe,EAAE,SAAS;gBAC1BR,OAAO,EAAE,MAAM;gBACfS,UAAU,EAAE,QAAQ;gBACpBC,GAAG,EAAE;cACP,CAAE;cAAAR,QAAA,gBAEFxI,OAAA,CAACP,gBAAgB;gBAACmK,IAAI,EAAE,EAAG;gBAACxB,EAAE,EAAE;kBAAEe,KAAK,EAAEtJ;gBAAgB;cAAE;gBAAAuJ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC9DvJ,OAAA,CAACb,UAAU;gBACTqK,OAAO,EAAC,OAAO;gBACfpB,EAAE,EAAE;kBACFqB,UAAU,EAAE,kCAAkC;kBAC9CN,KAAK,EAAE;gBACT,CAAE;gBAAAX,QAAA,EACH;cAED;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAY,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eACDvJ,OAAA;UAAKyK,GAAG,EAAE7J;QAAe;UAAAwI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,eAGRvJ,OAAA,CAACZ,KAAK;MACJqJ,SAAS,EAAE,CAAE;MACbL,EAAE,EAAE;QACFM,CAAC,EAAE,CAAC;QACJC,YAAY,EAAE,eAAe;QAC7BC,MAAM,EAAE,mBAAmB;QAC3BmB,SAAS,EAAE;MACb,CAAE;MAAAvB,QAAA,eAEFxI,OAAA,CAACd,GAAG;QAACkJ,EAAE,EAAE;UAAEE,OAAO,EAAE,MAAM;UAAEU,GAAG,EAAE,CAAC;UAAED,UAAU,EAAE;QAAW,CAAE;QAAAP,QAAA,gBAC3DxI,OAAA,CAACX,SAAS;UACRwB,QAAQ,EAAEA,QAAS;UACnB6J,KAAK,EAAElK,cAAe;UACtBmK,QAAQ,EAAG/C,CAAC,IAAKnH,iBAAiB,CAACmH,CAAC,CAACgD,MAAM,CAACF,KAAK,CAAE;UACnDG,UAAU,EAAElD,cAAe;UAC3BmD,WAAW,EAAC,uCAAuC;UACnDC,SAAS;UACTC,OAAO,EAAE,CAAE;UACXC,SAAS;UACTzB,OAAO,EAAC,UAAU;UAClB0B,QAAQ,EAAExK,SAAU;UACpB0H,EAAE,EAAE;YACF,0BAA0B,EAAE;cAC1BO,YAAY,EAAE,KAAK;cACnBc,UAAU,EAAE;YACd;UACF;QAAE;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFvJ,OAAA,CAACL,OAAO;UAACwL,KAAK,EAAC,cAAc;UAAA3C,QAAA,eAC3BxI,OAAA,CAACV,UAAU;YACT8L,OAAO,EAAEA,CAAA,KAAMvJ,iBAAiB,CAAC,CAAE;YACnCqJ,QAAQ,EAAE,CAAC1K,cAAc,CAACmC,IAAI,CAAC,CAAC,IAAIjC,SAAU;YAC9C0H,EAAE,EAAE;cACFU,eAAe,EAAEtI,cAAc,CAACmC,IAAI,CAAC,CAAC,IAAI,CAACjC,SAAS,GAAGb,eAAe,GAAG,SAAS;cAClFsJ,KAAK,EAAE3I,cAAc,CAACmC,IAAI,CAAC,CAAC,IAAI,CAACjC,SAAS,GAAG,MAAM,GAAG,MAAM;cAC5D,SAAS,EAAE;gBACToI,eAAe,EAAEtI,cAAc,CAACmC,IAAI,CAAC,CAAC,IAAI,CAACjC,SAAS,GAAG,SAAS,GAAG;cACrE;YACF,CAAE;YAAA8H,QAAA,eAEFxI,OAAA,CAACJ,OAAO;cAACsJ,IAAI,EAAC,uBAAuB;cAACD,KAAK,EAAE,EAAG;cAACZ,MAAM,EAAE;YAAG;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACpJ,EAAA,CA7qBIF,QAAQ;EAAA,QAMKH,WAAW;AAAA;AAAAuL,EAAA,GANxBpL,QAAQ;AA+qBd,eAAeA,QAAQ;AAAC,IAAAoL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}