{"ast": null, "code": "import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nconst COMPONENT_NAME = 'Button';\nexport function getButtonUtilityClass(slot) {\n  return generateUtilityClass(COMPONENT_NAME, slot);\n}\nexport const buttonClasses = generateUtilityClasses(COMPONENT_NAME, ['root', 'active', 'disabled', 'focusVisible']);", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "COMPONENT_NAME", "getButtonUtilityClass", "slot", "buttonClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/Button/buttonClasses.js"], "sourcesContent": ["import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nconst COMPONENT_NAME = 'Button';\nexport function getButtonUtilityClass(slot) {\n  return generateUtilityClass(COMPONENT_NAME, slot);\n}\nexport const buttonClasses = generateUtilityClasses(COMPONENT_NAME, ['root', 'active', 'disabled', 'focusVisible']);"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,MAAMC,cAAc,GAAG,QAAQ;AAC/B,OAAO,SAASC,qBAAqBA,CAACC,IAAI,EAAE;EAC1C,OAAOJ,oBAAoB,CAACE,cAAc,EAAEE,IAAI,CAAC;AACnD;AACA,OAAO,MAAMC,aAAa,GAAGJ,sBAAsB,CAACC,cAAc,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}