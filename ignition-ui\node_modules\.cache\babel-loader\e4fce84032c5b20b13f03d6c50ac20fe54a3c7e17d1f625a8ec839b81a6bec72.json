{"ast": null, "code": "var _jsxFileName = \"C:\\\\ignition\\\\ignition-ui\\\\src\\\\views\\\\plan\\\\detail\\\\components\\\\MilestoneCard.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Box, Typography, Paper, Chip, LinearProgress, Button, Divider, TextField, IconButton, Tooltip, Checkbox, CircularProgress, Avatar, AvatarGroup } from '@mui/material';\nimport Iconify from 'components/Iconify/index';\nimport { mainYellowColor } from \"helpers/constants\";\nimport { STATUS, STATUS_CONFIG } from '../hooks/usePlanData';\nimport styles from '../styles.module.scss';\nimport CommentDialog from '../dialogs/CommentDialog';\nimport DueDateDialog from '../dialogs/DueDateDialog';\nimport AssignMemberDialog from '../dialogs/AssignMemberDialog';\nimport { getComments, addComment, updateComment, deleteComment, updateTask, assignMembersToTask } from '../../services';\nimport { toast } from 'react-toastify';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MilestoneCard = _ref => {\n  _s();\n  var _milestone$tasks, _milestone$tasks2, _milestone$tasks3;\n  let {\n    milestone,\n    compact = false,\n    showSubtasks = false,\n    calculateMilestoneProgress,\n    getMilestoneStatus,\n    calculateTaskProgress,\n    getTaskStatus,\n    calculateSubtaskProgress,\n    getSubtaskStatus,\n    onUpdateMilestone,\n    onUpdateTask,\n    onUpdateSubtask,\n    onAddTask,\n    onAddSubtask,\n    onDeleteTask,\n    onDeleteSubtask,\n    onAssignMembers,\n    invitedUsers,\n    planOwner\n  } = _ref;\n  const [isEditing, setIsEditing] = useState(false);\n  const [milestoneName, setMilestoneName] = useState(milestone.name);\n  const [isAddingTask, setIsAddingTask] = useState(false);\n  const [newTaskName, setNewTaskName] = useState('');\n  const [isExpanded, setIsExpanded] = useState(true); // Add collapsible state\n  const inputRef = useRef(null);\n  const newTaskInputRef = useRef(null);\n\n  // Calculate milestone progress\n  const progress = calculateMilestoneProgress ? calculateMilestoneProgress(milestone) : 0;\n\n  // Determine status based on progress\n  const milestoneStatus = getMilestoneStatus ? getMilestoneStatus(milestone) : STATUS.NOT_STARTED;\n  const statusConfig = STATUS_CONFIG[milestoneStatus] || STATUS_CONFIG[STATUS.NOT_STARTED];\n\n  // Check if description exists and is not empty\n  const hasDescription = milestone.description && milestone.description.trim().length > 0;\n\n  // Handle edit mode\n  const handleEditClick = () => {\n    setIsEditing(true);\n  };\n\n  // Handle save changes\n  const handleSave = () => {\n    // Trim the name to remove leading/trailing whitespace\n    const trimmedName = milestoneName.trim();\n\n    // Only update if the name has actually changed (after trimming)\n    if (trimmedName !== '' && trimmedName !== milestone.name) {\n      if (onUpdateMilestone) {\n        onUpdateMilestone({\n          ...milestone,\n          name: trimmedName\n        });\n      }\n    } else {\n      // Reset to original if empty or unchanged\n      setMilestoneName(milestone.name);\n    }\n    setIsEditing(false);\n  };\n\n  // Handle key press events\n  const handleKeyPress = e => {\n    if (e.key === 'Enter') {\n      handleSave();\n    } else if (e.key === 'Escape') {\n      setMilestoneName(milestone.name);\n      setIsEditing(false);\n    }\n  };\n\n  // Handle add task\n  const handleAddTaskClick = () => {\n    setIsAddingTask(true);\n  };\n\n  // Handle save new task\n  const handleSaveNewTask = () => {\n    const trimmedName = newTaskName.trim();\n    if (trimmedName && onAddTask) {\n      const newTask = {\n        name: trimmedName,\n        milestone: milestone.id,\n        status: STATUS.NOT_STARTED,\n        progress: 0\n      };\n      onAddTask(newTask);\n      setNewTaskName('');\n    }\n    setIsAddingTask(false);\n  };\n\n  // Handle key press events for new task\n  const handleNewTaskKeyPress = e => {\n    if (e.key === 'Enter') {\n      handleSaveNewTask();\n    } else if (e.key === 'Escape') {\n      setNewTaskName('');\n      setIsAddingTask(false);\n    }\n  };\n\n  // Focus input when editing starts\n  useEffect(() => {\n    if (isEditing && inputRef.current) {\n      inputRef.current.focus();\n    }\n  }, [isEditing]);\n\n  // Focus input when adding task\n  useEffect(() => {\n    if (isAddingTask && newTaskInputRef.current) {\n      newTaskInputRef.current.focus();\n    }\n  }, [isAddingTask]);\n  return /*#__PURE__*/_jsxDEV(Paper, {\n    elevation: 0,\n    className: styles.milestoneCard,\n    sx: {\n      padding: '16px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      className: styles.milestoneHeader,\n      children: [isEditing ? /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Iconify, {\n          icon: \"material-symbols:flag\",\n          width: 20,\n          height: 20,\n          color: mainYellowColor,\n          sx: {\n            mr: 1\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 154,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(TextField, {\n          inputRef: inputRef,\n          value: milestoneName,\n          onChange: e => setMilestoneName(e.target.value),\n          onKeyDown: handleKeyPress,\n          onBlur: handleSave,\n          variant: \"standard\",\n          fullWidth: true,\n          autoFocus: true,\n          sx: {\n            fontFamily: '\"Recursive Variable\", sans-serif',\n            fontWeight: 600,\n            fontSize: '1.1rem',\n            '& .MuiInputBase-input': {\n              fontWeight: 600,\n              fontSize: '1.1rem',\n              fontFamily: '\"Recursive Variable\", sans-serif'\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          onClick: handleSave,\n          sx: {\n            ml: 1\n          },\n          children: /*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"material-symbols:check\",\n            width: 20,\n            height: 20,\n            color: \"#4CAF50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        className: styles.milestoneTitle,\n        sx: {\n          fontFamily: '\"Recursive Variable\", sans-serif',\n          display: 'flex',\n          alignItems: 'center',\n          cursor: 'pointer',\n          '&:hover': {\n            '& .edit-icon': {\n              opacity: 1\n            }\n          }\n        },\n        onClick: handleEditClick,\n        children: [/*#__PURE__*/_jsxDEV(Iconify, {\n          icon: \"material-symbols:flag\",\n          width: 20,\n          height: 20,\n          color: mainYellowColor\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 13\n        }, this), milestoneName, /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"Edit milestone name\",\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            className: \"edit-icon\",\n            sx: {\n              ml: 1,\n              opacity: 0,\n              transition: 'opacity 0.2s',\n              padding: '2px'\n            },\n            children: /*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"material-symbols:edit-outline\",\n              width: 16,\n              height: 16,\n              color: \"#666\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n        icon: /*#__PURE__*/_jsxDEV(Iconify, {\n          icon: statusConfig.icon,\n          width: 16,\n          height: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 17\n        }, this),\n        label: statusConfig.label,\n        size: \"small\",\n        sx: {\n          backgroundColor: `${statusConfig.color}20`,\n          color: statusConfig.color,\n          fontWeight: 600,\n          borderRadius: '4px',\n          fontFamily: '\"Recursive Variable\", sans-serif'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 215,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 7\n    }, this), !compact && hasDescription && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 2,\n        mt: 1.5\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          backgroundColor: '#f9f9f9',\n          p: 1.5,\n          borderRadius: '8px',\n          border: '1px solid #f0f0f0'\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            color: '#333',\n            fontFamily: '\"Recursive Variable\", sans-serif',\n            whiteSpace: 'pre-line',\n            lineHeight: 1.6,\n            fontWeight: 500\n          },\n          children: milestone.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {\n      sx: {\n        my: 2,\n        opacity: 0.6\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 256,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          mb: 0.5\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            fontWeight: 600,\n            fontFamily: '\"Recursive Variable\", sans-serif',\n            color: '#333'\n          },\n          children: \"Progress\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 260,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            fontWeight: 700,\n            fontFamily: '\"Recursive Variable\", sans-serif',\n            color: '#333'\n          },\n          children: [progress, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 259,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n        variant: \"determinate\",\n        value: progress,\n        sx: {\n          height: 6,\n          borderRadius: 3,\n          backgroundColor: '#f0f0f0',\n          '& .MuiLinearProgress-bar': {\n            backgroundColor: statusConfig.color\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 1\n      },\n      children: milestone.estimated_duration && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Iconify, {\n          icon: \"material-symbols:timer\",\n          width: 16,\n          height: 16,\n          color: \"#333\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            fontFamily: '\"Recursive Variable\", sans-serif',\n            color: '#333',\n            fontWeight: 500\n          },\n          children: milestone.estimated_duration\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 281,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          mb: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"material-symbols:checklist\",\n            width: 16,\n            height: 16,\n            sx: {\n              color: '#333'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 303,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 600,\n              color: '#333',\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              fontSize: '0.9rem'\n            },\n            children: \"Tasks\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 309,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 302,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"Add new task\",\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            onClick: handleAddTaskClick,\n            sx: {\n              color: mainYellowColor,\n              '&:hover': {\n                backgroundColor: `${mainYellowColor}10`\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"material-symbols:add-task\",\n              width: 18,\n              height: 18\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this), compact ? /*#__PURE__*/_jsxDEV(Box, {\n        className: styles.taskList,\n        children: [(_milestone$tasks = milestone.tasks) === null || _milestone$tasks === void 0 ? void 0 : _milestone$tasks.slice(0, 2).map((task, index) => /*#__PURE__*/_jsxDEV(TaskItem, {\n          task: task,\n          showSubtasks: showSubtasks,\n          compact: true,\n          calculateTaskProgress: calculateTaskProgress,\n          getTaskStatus: getTaskStatus,\n          calculateSubtaskProgress: calculateSubtaskProgress,\n          getSubtaskStatus: getSubtaskStatus,\n          onUpdateTask: onUpdateTask,\n          onUpdateSubtask: onUpdateSubtask,\n          onAddSubtask: onAddSubtask,\n          onDeleteTask: onDeleteTask,\n          onDeleteSubtask: onDeleteSubtask,\n          onAssignMembers: onAssignMembers,\n          invitedUsers: invitedUsers,\n          planOwner: planOwner\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 342,\n          columnNumber: 15\n        }, this)), ((_milestone$tasks2 = milestone.tasks) === null || _milestone$tasks2 === void 0 ? void 0 : _milestone$tasks2.length) > 2 && /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"text\",\n          size: \"small\",\n          sx: {\n            color: mainYellowColor,\n            fontWeight: 600,\n            textTransform: 'none',\n            p: 0,\n            mt: 1,\n            fontFamily: '\"Recursive Variable\", sans-serif'\n          },\n          children: [\"+ \", milestone.tasks.length - 2, \" more tasks\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 363,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 340,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Box, {\n        className: styles.taskList,\n        children: [(_milestone$tasks3 = milestone.tasks) === null || _milestone$tasks3 === void 0 ? void 0 : _milestone$tasks3.map((task, index) => /*#__PURE__*/_jsxDEV(TaskItem, {\n          task: task,\n          showSubtasks: showSubtasks,\n          compact: false,\n          calculateTaskProgress: calculateTaskProgress,\n          getTaskStatus: getTaskStatus,\n          calculateSubtaskProgress: calculateSubtaskProgress,\n          getSubtaskStatus: getSubtaskStatus,\n          onUpdateTask: onUpdateTask,\n          onUpdateSubtask: onUpdateSubtask,\n          onAddSubtask: onAddSubtask,\n          onDeleteTask: onDeleteTask,\n          onDeleteSubtask: onDeleteSubtask,\n          onAssignMembers: onAssignMembers,\n          invitedUsers: invitedUsers,\n          planOwner: planOwner\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 15\n        }, this)), isAddingTask && /*#__PURE__*/_jsxDEV(Box, {\n          className: styles.taskItem,\n          sx: {\n            position: 'relative',\n            mt: 1,\n            display: 'flex',\n            alignItems: 'center',\n            backgroundColor: '#f9f9f9',\n            borderRadius: '6px',\n            padding: '8px 12px',\n            border: '1px solid #f0f0f0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: 10,\n              height: 10,\n              borderRadius: '50%',\n              backgroundColor: '#CCCCCC',\n              mr: 1.5,\n              flexShrink: 0\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            inputRef: newTaskInputRef,\n            value: newTaskName,\n            onChange: e => setNewTaskName(e.target.value),\n            onKeyDown: handleNewTaskKeyPress,\n            placeholder: \"Enter new task name...\",\n            variant: \"standard\",\n            fullWidth: true,\n            autoFocus: true,\n            sx: {\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              '& .MuiInputBase-input': {\n                fontSize: '0.9rem',\n                fontFamily: '\"Recursive Variable\", sans-serif',\n                padding: '4px 0'\n              },\n              '& .MuiInput-underline:before': {\n                borderBottomColor: 'rgba(0, 0, 0, 0.1)'\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              ml: 'auto'\n            },\n            children: [/*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: handleSaveNewTask,\n              sx: {\n                ml: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Iconify, {\n                icon: \"material-symbols:check\",\n                width: 18,\n                height: 18,\n                color: \"#4CAF50\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 452,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 451,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: () => setIsAddingTask(false),\n              sx: {\n                ml: 0.5\n              },\n              children: /*#__PURE__*/_jsxDEV(Iconify, {\n                icon: \"material-symbols:close\",\n                width: 18,\n                height: 18,\n                color: \"#F44336\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 456,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 455,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 450,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 404,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 380,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 146,\n    columnNumber: 5\n  }, this);\n};\n// Component to display task and subtask\n_s(MilestoneCard, \"4qfr46vuLtQawaY9XK3XyHondE8=\");\n_c = MilestoneCard;\nconst TaskItem = _ref2 => {\n  _s2();\n  var _localTask$assignees;\n  let {\n    task,\n    showSubtasks = false,\n    compact = false,\n    calculateTaskProgress,\n    getTaskStatus,\n    calculateSubtaskProgress,\n    getSubtaskStatus,\n    onUpdateTask,\n    onUpdateSubtask,\n    onAddSubtask,\n    onDeleteTask,\n    onDeleteSubtask,\n    onAssignMembers,\n    invitedUsers,\n    planOwner\n  } = _ref2;\n  const [isEditing, setIsEditing] = useState(false);\n  const [taskName, setTaskName] = useState(task.name);\n  const taskInputRef = useRef(null);\n  const [isAddingSubtask, setIsAddingSubtask] = useState(false);\n  const [newSubtaskName, setNewSubtaskName] = useState('');\n  const newSubtaskInputRef = useRef(null);\n\n  // Comment functionality\n  const [commentDialogOpen, setCommentDialogOpen] = useState(false);\n  const [comments, setComments] = useState([]);\n  const [loadingComments, setLoadingComments] = useState(false);\n\n  // Enable due date functionality\n  const [dueDateDialogOpen, setDueDateDialogOpen] = useState(false);\n  const [updatingDueDate, setUpdatingDueDate] = useState(false);\n\n  // Add state for assign member dialog\n  const [assignMemberDialogOpen, setAssignMemberDialogOpen] = useState(false);\n\n  // Add state to store current task information\n  const [localTask, setLocalTask] = useState(task);\n\n  // Update localTask when task changes from props\n  useEffect(() => {\n    setLocalTask(task);\n  }, [task]);\n  const handleOpenComments = e => {\n    e.stopPropagation();\n    setCommentDialogOpen(true);\n    setLoadingComments(true);\n    getComments(task.id).then(response => {\n      var _response$data, _response$data2;\n      console.log('Comments data:', response.data);\n      // Check data structure and get correct comments array\n      let commentsData = [];\n      if (Array.isArray(response.data)) {\n        commentsData = response.data;\n      } else if ((_response$data = response.data) !== null && _response$data !== void 0 && _response$data.data && Array.isArray(response.data.data)) {\n        commentsData = response.data.data;\n      } else if ((_response$data2 = response.data) !== null && _response$data2 !== void 0 && _response$data2.comments && Array.isArray(response.data.comments)) {\n        commentsData = response.data.comments;\n      }\n      setComments(commentsData);\n    }).catch(error => {\n      console.error('Error fetching comments:', error);\n      toast.error('Failed to load comments');\n    }).finally(() => {\n      setLoadingComments(false);\n    });\n  };\n  const handleAddComment = async content => {\n    try {\n      var _response$data3;\n      const response = await addComment(task.id, content);\n      console.log('Add comment response:', response);\n\n      // Create new comment from response or create temporary object\n      let newComment;\n      if ((_response$data3 = response.data) !== null && _response$data3 !== void 0 && _response$data3.data) {\n        newComment = response.data.data;\n      } else {\n        // Create temporary object if API does not return new comment\n        const currentUser = JSON.parse(localStorage.getItem('user')) || {};\n        newComment = {\n          id: Date.now(),\n          // Temporary ID\n          content: content,\n          user: {\n            id: currentUser.id,\n            first_name: currentUser.first_name,\n            last_name: currentUser.last_name,\n            avatar: currentUser.avatar\n          },\n          created_at: new Date().toLocaleString('en-US', {\n            hour: '2-digit',\n            minute: '2-digit',\n            day: '2-digit',\n            month: '2-digit'\n          }).replace(',', '')\n        };\n      }\n\n      // Update state\n      setComments(prevComments => [...prevComments, newComment]);\n\n      // Refresh comments to ensure latest data\n      refreshComments();\n    } catch (error) {\n      console.error('Error adding comment:', error);\n      toast.error('Failed to add comment');\n    }\n  };\n  const handleUpdateComment = async (commentId, content) => {\n    try {\n      // Update UI first\n      const updatedCommentTemp = comments.find(c => c.id === commentId);\n      if (updatedCommentTemp) {\n        const updatedComments = comments.map(c => c.id === commentId ? {\n          ...c,\n          content: content\n        } : c);\n        setComments(updatedComments);\n      }\n\n      // Call API\n      const response = await updateComment(commentId, content);\n      console.log('Update comment response:', response);\n      toast.success('Comment updated successfully');\n\n      // Refresh comments to ensure latest data\n      refreshComments();\n    } catch (error) {\n      console.error('Error updating comment:', error);\n      toast.error('Failed to update comment');\n      // Refresh comments to restore original state if there is an error\n      refreshComments();\n    }\n  };\n  const handleDeleteComment = async commentId => {\n    try {\n      // Update UI first\n      setComments(prevComments => prevComments.filter(c => c.id !== commentId));\n\n      // Call API\n      await deleteComment(commentId);\n      console.log('Comment deleted successfully');\n      toast.success('Comment deleted successfully');\n\n      // Refresh comments to ensure latest data\n      refreshComments();\n    } catch (error) {\n      console.error('Error deleting comment:', error);\n      toast.error('Failed to delete comment');\n      // Refresh comments to restore original state if there is an error\n      refreshComments();\n    }\n  };\n\n  // Refresh comments function\n  const refreshComments = async () => {\n    try {\n      var _response$data4, _response$data5;\n      const response = await getComments(task.id);\n      console.log('Refreshed comments:', response.data);\n\n      // Handle returned data\n      let commentsData = [];\n      if (Array.isArray(response.data)) {\n        commentsData = response.data;\n      } else if ((_response$data4 = response.data) !== null && _response$data4 !== void 0 && _response$data4.data && Array.isArray(response.data.data)) {\n        commentsData = response.data.data;\n      } else if ((_response$data5 = response.data) !== null && _response$data5 !== void 0 && _response$data5.comments && Array.isArray(response.data.comments)) {\n        commentsData = response.data.comments;\n      }\n      setComments(commentsData);\n    } catch (error) {\n      console.error('Error refreshing comments:', error);\n    }\n  };\n\n  // Calculate task progress\n  const taskProgress = calculateTaskProgress ? calculateTaskProgress(task) : 0;\n\n  // Determine task status\n  const taskStatus = getTaskStatus ? getTaskStatus(task) : task.status || STATUS.NOT_STARTED;\n  const statusConfig = STATUS_CONFIG[taskStatus] || STATUS_CONFIG[STATUS.NOT_STARTED];\n\n  // Check if task has subtasks\n  const hasSubtasks = task.subtasks && task.subtasks.length > 0;\n\n  // Check if task is completed\n  const isCompleted = taskStatus === STATUS.COMPLETED;\n\n  // Handle edit mode for task\n  const handleTaskEditClick = () => {\n    setIsEditing(true);\n  };\n\n  // Handle save changes for task\n  const handleTaskSave = () => {\n    // Trim the name to remove leading/trailing whitespace\n    const trimmedName = taskName.trim();\n\n    // Only update if the name has actually changed (after trimming)\n    if (trimmedName !== '' && trimmedName !== task.name) {\n      if (onUpdateTask) {\n        onUpdateTask({\n          ...task,\n          name: trimmedName\n        });\n      }\n    } else {\n      // Reset to original if empty or unchanged\n      setTaskName(task.name);\n    }\n    setIsEditing(false);\n  };\n\n  // Handle key press events for task\n  const handleTaskKeyPress = e => {\n    if (e.key === 'Enter') {\n      handleTaskSave();\n    } else if (e.key === 'Escape') {\n      setTaskName(task.name);\n      setIsEditing(false);\n    }\n  };\n\n  // Handle add subtask\n  const handleAddSubtaskClick = () => {\n    setIsAddingSubtask(true);\n  };\n\n  // Handle delete task\n  const handleDeleteTaskClick = () => {\n    if (onDeleteTask) {\n      onDeleteTask(task);\n    }\n  };\n\n  // Handle save new subtask\n  const handleSaveNewSubtask = () => {\n    const trimmedName = newSubtaskName.trim();\n    if (trimmedName && onAddSubtask) {\n      const newSubtask = {\n        name: trimmedName,\n        task: task.slug,\n        status: STATUS.NOT_STARTED,\n        progress: 0\n      };\n      onAddSubtask(newSubtask);\n      setNewSubtaskName('');\n    }\n    setIsAddingSubtask(false);\n  };\n\n  // Handle key press events for new subtask\n  const handleNewSubtaskKeyPress = e => {\n    if (e.key === 'Enter') {\n      handleSaveNewSubtask();\n    } else if (e.key === 'Escape') {\n      setNewSubtaskName('');\n      setIsAddingSubtask(false);\n    }\n  };\n\n  // Focus input when editing starts\n  useEffect(() => {\n    if (isEditing && taskInputRef.current) {\n      taskInputRef.current.focus();\n    }\n  }, [isEditing]);\n\n  // Focus input when adding subtask\n  useEffect(() => {\n    if (isAddingSubtask && newSubtaskInputRef.current) {\n      newSubtaskInputRef.current.focus();\n    }\n  }, [isAddingSubtask]);\n\n  // Check if task has due dates - use localTask instead of task\n  const hasDueDates = localTask.start_date || localTask.end_date;\n  const handleOpenDueDateDialog = e => {\n    e.stopPropagation();\n    setDueDateDialogOpen(true);\n  };\n  const handleUpdateDueDate = async updatedTask => {\n    setUpdatingDueDate(true);\n    try {\n      const taskToUpdate = {\n        ...localTask,\n        start_date: updatedTask.start_date,\n        end_date: updatedTask.end_date,\n        progress: localTask.progress || 0,\n        status: localTask.status || 1\n      };\n\n      // Update local state first\n      setLocalTask(taskToUpdate);\n\n      // Call API\n      const response = await updateTask(taskToUpdate);\n\n      // If API returns data, update local state\n      if (response && response.data) {\n        setLocalTask(response.data);\n      }\n\n      // Update state in parent component if necessary\n      if (onUpdateTask) {\n        onUpdateTask(taskToUpdate);\n      }\n      toast.success('Due date updated successfully');\n    } catch (error) {\n      console.error('Error updating due date:', error);\n      toast.error('Failed to update due date');\n\n      // If there is an error, restore original state\n      setLocalTask(task);\n    } finally {\n      setUpdatingDueDate(false);\n      setDueDateDialogOpen(false); // Close dialog after completion\n    }\n  };\n\n  // Add handler for member assignment updates\n  const handleAssignMembers = async assignedUserIds => {\n    try {\n      // Convert IDs to numbers\n      const numericIds = assignedUserIds.map(id => Number(id));\n      // Call API to assign members\n      const response = await assignMembersToTask(localTask.slug, numericIds);\n      // Create updated task with new assignees\n      const updatedTask = {\n        ...localTask,\n        assignees: response.assignees || numericIds.map(id => ({\n          id: id,\n          first_name: '',\n          last_name: '',\n          email: ''\n        }))\n      };\n\n      // Update both local state and parent state\n      setLocalTask(updatedTask);\n      if (onUpdateTask) {\n        onUpdateTask(updatedTask);\n      }\n      setAssignMemberDialogOpen(false);\n      toast.success('Members assigned successfully');\n    } catch (error) {\n      console.error('Error assigning members:', error);\n      toast.error('Failed to assign members');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      className: styles.taskItemContainer,\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        className: styles.taskItem,\n        sx: {\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: 10,\n            height: 10,\n            borderRadius: '50%',\n            backgroundColor: statusConfig.color,\n            mr: 1.5,\n            flexShrink: 0\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 834,\n          columnNumber: 11\n        }, this), isEditing ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            flexGrow: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            inputRef: taskInputRef,\n            value: taskName,\n            onChange: e => setTaskName(e.target.value),\n            onKeyDown: handleTaskKeyPress,\n            onBlur: handleTaskSave,\n            variant: \"standard\",\n            fullWidth: true,\n            autoFocus: true,\n            sx: {\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              '& .MuiInputBase-input': {\n                fontSize: '0.9rem',\n                fontFamily: '\"Recursive Variable\", sans-serif'\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 847,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            onClick: handleTaskSave,\n            sx: {\n              ml: 1\n            },\n            children: /*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"material-symbols:check\",\n              width: 16,\n              height: 16,\n              color: \"#4CAF50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 865,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 864,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 846,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            flexGrow: 1,\n            display: 'flex',\n            flexDirection: 'column',\n            gap: 0.5\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between',\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 2,\n                flexGrow: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                className: styles.taskName,\n                sx: {\n                  fontFamily: '\"Recursive Variable\", sans-serif',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  color: isCompleted ? '#4CAF50' : 'inherit',\n                  fontWeight: isCompleted ? 500 : 400,\n                  flexGrow: 1,\n                  '&:hover': {\n                    '& .task-edit-icon': {\n                      opacity: 1\n                    }\n                  }\n                },\n                onClick: handleTaskEditClick,\n                children: [taskName, /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Edit task name\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    className: \"task-edit-icon\",\n                    sx: {\n                      ml: 0.5,\n                      opacity: 0,\n                      transition: 'opacity 0.2s',\n                      padding: '2px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Iconify, {\n                      icon: \"material-symbols:edit-outline\",\n                      width: 14,\n                      height: 14,\n                      color: \"#666\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 902,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 892,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 891,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 872,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 871,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 2\n              },\n              children: [localTask.assignees && localTask.assignees.length > 0 && /*#__PURE__*/_jsxDEV(AvatarGroup, {\n                max: 3,\n                sx: {\n                  '& .MuiAvatar-root': {\n                    width: 24,\n                    height: 24,\n                    fontSize: '0.75rem',\n                    border: '1.5px solid #fff'\n                  }\n                },\n                children: localTask.assignees.map((assignee, index) => /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: assignee.first_name && assignee.last_name ? `${assignee.first_name} ${assignee.last_name}` : assignee.email,\n                  arrow: true,\n                  children: /*#__PURE__*/_jsxDEV(Avatar, {\n                    src: assignee.avatar,\n                    alt: assignee.first_name || assignee.email,\n                    sx: {\n                      bgcolor: mainYellowColor\n                    },\n                    children: assignee.first_name ? assignee.first_name.charAt(0).toUpperCase() : assignee.email ? assignee.email.charAt(0).toUpperCase() : ''\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 930,\n                    columnNumber: 27\n                  }, this)\n                }, assignee.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 923,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 911,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  border: '1px solid #eee',\n                  borderRadius: '8px',\n                  padding: '2px 4px',\n                  background: '#f9f9f9'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: hasDueDates ? \"Edit due date\" : \"Set due date\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: handleOpenDueDateDialog,\n                    sx: {\n                      color: hasDueDates ? mainYellowColor : '#888',\n                      '&:hover': {\n                        color: mainYellowColor,\n                        bgcolor: 'rgba(255, 193, 7, 0.08)'\n                      }\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Iconify, {\n                      icon: hasDueDates ? \"material-symbols:calendar-month\" : \"material-symbols:calendar-add-on\",\n                      width: 16,\n                      height: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 972,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 961,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 960,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Comments\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: handleOpenComments,\n                    disabled: loadingComments,\n                    sx: {\n                      color: mainYellowColor,\n                      '&:hover': {\n                        bgcolor: 'rgba(255, 193, 7, 0.08)'\n                      }\n                    },\n                    children: loadingComments ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                      size: 16,\n                      sx: {\n                        color: mainYellowColor\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 994,\n                      columnNumber: 27\n                    }, this) : /*#__PURE__*/_jsxDEV(Iconify, {\n                      icon: \"material-symbols:comment-outline\",\n                      width: 16,\n                      height: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 996,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 982,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 981,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Assign members\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => setAssignMemberDialogOpen(true),\n                    sx: {\n                      color: ((_localTask$assignees = localTask.assignees) === null || _localTask$assignees === void 0 ? void 0 : _localTask$assignees.length) > 0 ? mainYellowColor : '#888',\n                      '&:hover': {\n                        color: mainYellowColor,\n                        bgcolor: 'rgba(255, 193, 7, 0.08)'\n                      }\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Iconify, {\n                      icon: \"mdi:account-multiple-plus\",\n                      width: 16,\n                      height: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1014,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1003,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1002,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Add subtask\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: handleAddSubtaskClick,\n                    sx: {\n                      color: mainYellowColor,\n                      '&:hover': {\n                        bgcolor: 'rgba(255, 193, 7, 0.08)'\n                      }\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Iconify, {\n                      icon: \"material-symbols:add-task\",\n                      width: 16,\n                      height: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1034,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1024,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1023,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Edit task\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: handleTaskEditClick,\n                    sx: {\n                      color: '#666',\n                      '&:hover': {\n                        bgcolor: 'rgba(0, 0, 0, 0.04)'\n                      }\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Iconify, {\n                      icon: \"eva:edit-fill\",\n                      width: 16,\n                      height: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1050,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1040,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1039,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Delete task\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: handleDeleteTaskClick,\n                    sx: {\n                      color: '#F44336',\n                      '&:hover': {\n                        bgcolor: 'rgba(244, 67, 54, 0.08)'\n                      }\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Iconify, {\n                      icon: \"material-symbols:delete-outline\",\n                      width: 16,\n                      height: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1066,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1056,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1055,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 949,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  bgcolor: statusConfig.color + '20',\n                  px: 1,\n                  py: 0.5,\n                  borderRadius: '4px'\n                },\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    fontWeight: 600,\n                    color: statusConfig.color,\n                    fontFamily: '\"Recursive Variable\", sans-serif'\n                  },\n                  children: [taskProgress, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1082,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1072,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 908,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 870,\n            columnNumber: 15\n          }, this), hasDueDates && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              backgroundColor: `${mainYellowColor}08`,\n              borderRadius: '4px',\n              py: 0.5,\n              px: 0.75,\n              width: 'fit-content'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"material-symbols:calendar-month\",\n              width: 14,\n              height: 14,\n              color: mainYellowColor\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1109,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                fontFamily: '\"Recursive Variable\", sans-serif',\n                fontSize: '0.75rem',\n                color: '#555',\n                fontWeight: 500,\n                lineHeight: 1,\n                ml: 0.5\n              },\n              children: [formatDate(localTask.start_date), \" ~ \", formatDate(localTask.end_date)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1114,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1098,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 869,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 831,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          pl: 4,\n          pr: 1,\n          pt: 0.5,\n          pb: 0.5,\n          borderLeft: `1px dashed ${statusConfig.color}`,\n          ml: 1.5,\n          mt: 0.5,\n          display: (hasSubtasks || isAddingSubtask) && showSubtasks ? 'block' : 'none'\n        },\n        children: [task.subtasks && task.subtasks.map((subtask, index) => /*#__PURE__*/_jsxDEV(SubtaskItem, {\n          subtask: subtask,\n          index: index,\n          totalSubtasks: task.subtasks.length,\n          taskSlug: task.slug,\n          calculateSubtaskProgress: calculateSubtaskProgress,\n          getSubtaskStatus: getSubtaskStatus,\n          onUpdateSubtask: onUpdateSubtask,\n          onDeleteSubtask: onDeleteSubtask,\n          onAssignMembers: onAssignMembers,\n          invitedUsers: invitedUsers,\n          planOwner: planOwner\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1145,\n          columnNumber: 13\n        }, this)), isAddingSubtask && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            py: 0.5,\n            borderBottom: '1px dotted #f0f0f0',\n            backgroundColor: '#f9f9f9',\n            borderRadius: '4px',\n            px: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n            disabled: true,\n            size: \"small\",\n            sx: {\n              p: 0.5,\n              mr: 0.5,\n              color: '#CCCCCC'\n            },\n            icon: /*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"material-symbols:check-box-outline-blank\",\n              width: 18,\n              height: 18\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1182,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1174,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            inputRef: newSubtaskInputRef,\n            value: newSubtaskName,\n            onChange: e => setNewSubtaskName(e.target.value),\n            onKeyDown: handleNewSubtaskKeyPress,\n            placeholder: \"Enter new subtask name...\",\n            variant: \"standard\",\n            fullWidth: true,\n            autoFocus: true,\n            sx: {\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              '& .MuiInputBase-input': {\n                fontSize: '0.85rem',\n                fontFamily: '\"Recursive Variable\", sans-serif',\n                padding: '4px 0'\n              },\n              '& .MuiInput-underline:before': {\n                borderBottomColor: 'rgba(0, 0, 0, 0.1)'\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1185,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              ml: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: handleSaveNewSubtask,\n              sx: {\n                p: 0.5\n              },\n              children: /*#__PURE__*/_jsxDEV(Iconify, {\n                icon: \"material-symbols:check\",\n                width: 18,\n                height: 18,\n                color: \"#4CAF50\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1209,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1208,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: () => setIsAddingSubtask(false),\n              sx: {\n                p: 0.5,\n                ml: 0.5\n              },\n              children: /*#__PURE__*/_jsxDEV(Iconify, {\n                icon: \"material-symbols:close\",\n                width: 18,\n                height: 18,\n                color: \"#F44336\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1213,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1212,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1207,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1163,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1132,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 830,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CommentDialog, {\n      open: commentDialogOpen,\n      onClose: () => setCommentDialogOpen(false),\n      comments: comments,\n      onAddComment: handleAddComment,\n      onUpdateComment: handleUpdateComment,\n      onDeleteComment: handleDeleteComment,\n      loading: loadingComments,\n      targetName: task.name,\n      targetType: \"task\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1222,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DueDateDialog, {\n      open: dueDateDialogOpen,\n      onClose: () => setDueDateDialogOpen(false),\n      task: localTask,\n      onUpdateDueDate: handleUpdateDueDate,\n      isUpdating: updatingDueDate\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1235,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AssignMemberDialog, {\n      open: assignMemberDialogOpen,\n      onClose: () => setAssignMemberDialogOpen(false),\n      task: localTask,\n      invitedUsers: invitedUsers,\n      planOwner: planOwner,\n      onAssignMembers: handleAssignMembers\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1244,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// Component for subtask\n_s2(TaskItem, \"dnh9c1rYa0G+f59syNTxEgU3FYU=\");\n_c2 = TaskItem;\nconst SubtaskItem = _ref3 => {\n  _s3();\n  let {\n    subtask,\n    index,\n    totalSubtasks,\n    taskSlug,\n    calculateSubtaskProgress,\n    getSubtaskStatus,\n    onUpdateSubtask,\n    onDeleteSubtask,\n    onAssignMembers,\n    invitedUsers,\n    planOwner\n  } = _ref3;\n  const [isEditing, setIsEditing] = useState(false);\n  const [subtaskName, setSubtaskName] = useState(subtask.name);\n  const [isChecked, setIsChecked] = useState(subtask.status === STATUS.COMPLETED || subtask.progress === 100);\n  const subtaskInputRef = useRef(null);\n\n  // Calculate subtask progress and status\n  const subtaskProgress = calculateSubtaskProgress ? calculateSubtaskProgress(subtask) : 0;\n  const subtaskStatus = getSubtaskStatus ? getSubtaskStatus(subtask) : subtask.status || STATUS.NOT_STARTED;\n  const subtaskStatusConfig = STATUS_CONFIG[subtaskStatus] || STATUS_CONFIG[STATUS.NOT_STARTED];\n\n  // Update isChecked when subtask changes from outside\n  useEffect(() => {\n    setIsChecked(subtask.status === STATUS.COMPLETED || subtask.progress === 100);\n  }, [subtask.status, subtask.progress]);\n\n  // Handle edit mode for subtask\n  const handleSubtaskEditClick = () => {\n    setIsEditing(true);\n  };\n\n  // Handle save changes for subtask\n  const handleSubtaskSave = () => {\n    // Trim the name to remove leading/trailing whitespace\n    const trimmedName = subtaskName.trim();\n\n    // Only update if the name has actually changed (after trimming)\n    if (trimmedName !== '' && trimmedName !== subtask.name) {\n      if (onUpdateSubtask) {\n        onUpdateSubtask({\n          ...subtask,\n          name: trimmedName,\n          task: taskSlug\n        });\n      }\n    } else {\n      // Reset to original if empty or unchanged\n      setSubtaskName(subtask.name);\n    }\n    setIsEditing(false);\n  };\n\n  // Handle key press events for subtask\n  const handleSubtaskKeyPress = e => {\n    if (e.key === 'Enter') {\n      handleSubtaskSave();\n    } else if (e.key === 'Escape') {\n      setSubtaskName(subtask.name);\n      setIsEditing(false);\n    }\n  };\n\n  // Handle status toggle\n  const handleStatusToggle = () => {\n    // Update UI immediately to provide user feedback\n    const newCheckedState = !isChecked;\n    setIsChecked(newCheckedState);\n    const newStatus = newCheckedState ? STATUS.COMPLETED : STATUS.NOT_STARTED;\n    const newProgress = newCheckedState ? 100 : 0;\n    if (onUpdateSubtask) {\n      // Ensure to send both status and progress, even when progress = 0\n      const updatedData = {\n        ...subtask,\n        status: newStatus,\n        progress: newProgress,\n        task: taskSlug\n      };\n      console.log('Sending subtask update:', updatedData);\n      onUpdateSubtask(updatedData);\n    }\n  };\n\n  // Handle delete subtask\n  const handleDeleteSubtaskClick = () => {\n    if (window.confirm(`Are you sure you want to delete this subtask?`)) {\n      onDeleteSubtask(subtask, taskSlug);\n    }\n  };\n\n  // Focus input when editing starts\n  useEffect(() => {\n    if (isEditing && subtaskInputRef.current) {\n      subtaskInputRef.current.focus();\n    }\n  }, [isEditing]);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex',\n      alignItems: 'center',\n      py: 0.5,\n      borderBottom: index < totalSubtasks - 1 ? '1px dotted #f0f0f0' : 'none'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n      checked: isChecked,\n      onChange: handleStatusToggle,\n      size: \"small\",\n      sx: {\n        p: 0.5,\n        mr: 0.5,\n        color: '#CCCCCC',\n        '&.Mui-checked': {\n          color: '#4CAF50'\n        }\n      },\n      icon: /*#__PURE__*/_jsxDEV(Iconify, {\n        icon: \"material-symbols:check-box-outline-blank\",\n        width: 18,\n        height: 18\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1376,\n        columnNumber: 15\n      }, this),\n      checkedIcon: /*#__PURE__*/_jsxDEV(Iconify, {\n        icon: \"material-symbols:check-box\",\n        width: 18,\n        height: 18\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1377,\n        columnNumber: 22\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1364,\n      columnNumber: 7\n    }, this), isEditing ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center',\n        flexGrow: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(TextField, {\n        inputRef: subtaskInputRef,\n        value: subtaskName,\n        onChange: e => setSubtaskName(e.target.value),\n        onKeyDown: handleSubtaskKeyPress,\n        onBlur: handleSubtaskSave,\n        variant: \"standard\",\n        fullWidth: true,\n        autoFocus: true,\n        sx: {\n          fontFamily: '\"Recursive Variable\", sans-serif',\n          '& .MuiInputBase-input': {\n            fontSize: '0.85rem',\n            fontFamily: '\"Recursive Variable\", sans-serif'\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1382,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n        size: \"small\",\n        onClick: handleSubtaskSave,\n        sx: {\n          ml: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(Iconify, {\n          icon: \"material-symbols:check\",\n          width: 14,\n          height: 14,\n          color: \"#4CAF50\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1400,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1399,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1381,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body2\",\n      sx: {\n        flexGrow: 1,\n        fontSize: '0.85rem',\n        fontFamily: '\"Recursive Variable\", sans-serif',\n        color: isChecked ? '#4CAF50' : '#555',\n        fontWeight: isChecked ? 500 : 400,\n        cursor: 'pointer',\n        display: 'flex',\n        alignItems: 'center',\n        '&:hover': {\n          '& .subtask-edit-icon': {\n            opacity: 1\n          }\n        }\n      },\n      onClick: handleSubtaskEditClick,\n      children: [subtaskName, /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"Edit subtask name\",\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          className: \"subtask-edit-icon\",\n          sx: {\n            ml: 0.5,\n            opacity: 0,\n            transition: 'opacity 0.2s',\n            padding: '1px'\n          },\n          children: /*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"material-symbols:edit-outline\",\n            width: 12,\n            height: 12,\n            color: \"#666\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1435,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1425,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1424,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1404,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center'\n      },\n      children: [subtaskProgress !== null && subtaskProgress !== undefined && /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        sx: {\n          fontSize: '0.75rem',\n          color: subtaskStatusConfig.color,\n          ml: 1,\n          mr: 1,\n          fontFamily: '\"Recursive Variable\", sans-serif',\n          fontWeight: 600\n        },\n        children: [subtaskProgress, \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1443,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"Delete subtask\",\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          onClick: handleDeleteSubtaskClick,\n          sx: {\n            p: 0.5,\n            color: '#F44336',\n            '&:hover': {\n              backgroundColor: '#FFEBEE'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"material-symbols:delete-outline\",\n            width: 14,\n            height: 14\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1471,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1460,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1459,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1441,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1356,\n    columnNumber: 5\n  }, this);\n};\n\n// Format date for display\n_s3(SubtaskItem, \"95kGDfgernN1QkX5iwg2z2cf2rU=\");\n_c3 = SubtaskItem;\nfunction formatDate(dateString) {\n  if (!dateString) return '';\n  try {\n    const date = new Date(dateString);\n    if (isNaN(date.getTime())) return '';\n\n    // Only display day and month, not year\n    const options = {\n      month: 'short',\n      day: 'numeric'\n    };\n    return date.toLocaleDateString('en-US', options);\n  } catch (error) {\n    return '';\n  }\n}\nexport default MilestoneCard;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"MilestoneCard\");\n$RefreshReg$(_c2, \"TaskItem\");\n$RefreshReg$(_c3, \"SubtaskItem\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Box", "Typography", "Paper", "Chip", "LinearProgress", "<PERSON><PERSON>", "Divider", "TextField", "IconButton", "<PERSON><PERSON><PERSON>", "Checkbox", "CircularProgress", "Avatar", "AvatarGroup", "Iconify", "mainYellowColor", "STATUS", "STATUS_CONFIG", "styles", "CommentDialog", "DueDateDialog", "AssignMemberDialog", "getComments", "addComment", "updateComment", "deleteComment", "updateTask", "assignMembersToTask", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MilestoneCard", "_ref", "_s", "_milestone$tasks", "_milestone$tasks2", "_milestone$tasks3", "milestone", "compact", "showSubtasks", "calculateMilestoneProgress", "getMilestoneStatus", "calculateTaskProgress", "getTaskStatus", "calculateSubtaskProgress", "getSubtaskStatus", "onUpdateMilestone", "onUpdateTask", "onUpdateSubtask", "onAddTask", "onAddSubtask", "onDeleteTask", "onDeleteSubtask", "onAssignMembers", "invitedUsers", "<PERSON><PERSON><PERSON><PERSON>", "isEditing", "setIsEditing", "milestoneName", "setMilestoneName", "name", "isAddingTask", "setIsAddingTask", "newTaskName", "setNewTaskName", "isExpanded", "setIsExpanded", "inputRef", "newTaskInputRef", "progress", "milestoneStatus", "NOT_STARTED", "statusConfig", "hasDescription", "description", "trim", "length", "handleEditClick", "handleSave", "trimmedName", "handleKeyPress", "e", "key", "handleAddTaskClick", "handleSaveNewTask", "newTask", "id", "status", "handleNewTaskKeyPress", "current", "focus", "elevation", "className", "milestoneCard", "sx", "padding", "children", "<PERSON><PERSON><PERSON><PERSON>", "display", "alignItems", "width", "icon", "height", "color", "mr", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "onChange", "target", "onKeyDown", "onBlur", "variant", "fullWidth", "autoFocus", "fontFamily", "fontWeight", "fontSize", "size", "onClick", "ml", "milestoneTitle", "cursor", "opacity", "title", "transition", "label", "backgroundColor", "borderRadius", "mb", "mt", "p", "border", "whiteSpace", "lineHeight", "my", "justifyContent", "estimated_duration", "gap", "taskList", "tasks", "slice", "map", "task", "index", "TaskItem", "textTransform", "taskItem", "position", "flexShrink", "placeholder", "borderBottomColor", "_c", "_ref2", "_s2", "_localTask$assignees", "taskName", "setTaskName", "taskInputRef", "isAddingSubtask", "setIsAddingSubtask", "newSubtaskName", "setNewSubtaskName", "newSubtaskInputRef", "commentDialogOpen", "setCommentDialogOpen", "comments", "setComments", "loadingComments", "setLoadingComments", "dueDateDialogOpen", "setDueDateDialogOpen", "updatingDueDate", "setUpdatingDueDate", "assignMemberDialogOpen", "setAssignMemberDialogOpen", "localTask", "setLocalTask", "handleOpenComments", "stopPropagation", "then", "response", "_response$data", "_response$data2", "console", "log", "data", "commentsData", "Array", "isArray", "catch", "error", "finally", "handleAddComment", "content", "_response$data3", "newComment", "currentUser", "JSON", "parse", "localStorage", "getItem", "Date", "now", "user", "first_name", "last_name", "avatar", "created_at", "toLocaleString", "hour", "minute", "day", "month", "replace", "prevComments", "refreshComments", "handleUpdateComment", "commentId", "updatedCommentTemp", "find", "c", "updatedComments", "success", "handleDeleteComment", "filter", "_response$data4", "_response$data5", "taskProgress", "taskStatus", "hasSubtasks", "subtasks", "isCompleted", "COMPLETED", "handleTaskEditClick", "handleTaskSave", "handleTaskKeyPress", "handleAddSubtaskClick", "handleDeleteTaskClick", "handleSaveNewSubtask", "newSubtask", "slug", "handleNewSubtaskKeyPress", "hasDueDates", "start_date", "end_date", "handleOpenDueDateDialog", "handleUpdateDueDate", "updatedTask", "taskToUpdate", "handleAssignMembers", "assignedUserIds", "numericIds", "Number", "assignees", "email", "taskItemContainer", "flexGrow", "flexDirection", "max", "assignee", "arrow", "src", "alt", "bgcolor", "char<PERSON>t", "toUpperCase", "background", "disabled", "px", "py", "formatDate", "pl", "pr", "pt", "pb", "borderLeft", "subtask", "SubtaskItem", "totalSubtasks", "taskSlug", "borderBottom", "open", "onClose", "onAddComment", "onUpdateComment", "onDeleteComment", "loading", "targetName", "targetType", "onUpdateDueDate", "isUpdating", "_c2", "_ref3", "_s3", "subtaskName", "setSubtaskName", "isChecked", "setIsChecked", "subtaskInputRef", "subtaskProgress", "subtaskStatus", "subtaskStatusConfig", "handleSubtaskEditClick", "handleSubtaskSave", "handleSubtaskKeyPress", "handleStatusToggle", "newCheckedState", "newStatus", "newProgress", "updatedData", "handleDeleteSubtaskClick", "window", "confirm", "checked", "checkedIcon", "undefined", "_c3", "dateString", "date", "isNaN", "getTime", "options", "toLocaleDateString", "$RefreshReg$"], "sources": ["C:/ignition/ignition-ui/src/views/plan/detail/components/MilestoneCard.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Paper,\r\n  Chip,\r\n  LinearProgress,\r\n  Button,\r\n  Divider,\r\n  TextField,\r\n  IconButton,\r\n  Tooltip,\r\n  Checkbox,\r\n  CircularProgress,\r\n  Avatar,\r\n  AvatarGroup\r\n} from '@mui/material';\r\nimport Iconify from 'components/Iconify/index';\r\nimport { mainYellowColor } from \"helpers/constants\";\r\nimport { STATUS, STATUS_CONFIG } from '../hooks/usePlanData';\r\nimport styles from '../styles.module.scss';\r\nimport CommentDialog from '../dialogs/CommentDialog';\r\nimport DueDateDialog from '../dialogs/DueDateDialog';\r\nimport AssignMemberDialog from '../dialogs/AssignMemberDialog';\r\nimport { getComments, addComment, updateComment, deleteComment, updateTask, assignMembersToTask } from '../../services';\r\nimport { toast } from 'react-toastify';\r\n\r\nconst MilestoneCard = ({\r\n  milestone,\r\n  compact = false,\r\n  showSubtasks = false,\r\n  calculateMilestoneProgress,\r\n  getMilestoneStatus,\r\n  calculateTaskProgress,\r\n  getTaskStatus,\r\n  calculateSubtaskProgress,\r\n  getSubtaskStatus,\r\n  onUpdateMilestone,\r\n  onUpdateTask,\r\n  onUpdateSubtask,\r\n  onAddTask,\r\n  onAddSubtask,\r\n  onDeleteTask,\r\n  onDeleteSubtask,\r\n  onAssignMembers,\r\n  invitedUsers,\r\n  planOwner\r\n}) => {\r\n  const [isEditing, setIsEditing] = useState(false);\r\n  const [milestoneName, setMilestoneName] = useState(milestone.name);\r\n  const [isAddingTask, setIsAddingTask] = useState(false);\r\n  const [newTaskName, setNewTaskName] = useState('');\r\n  const [isExpanded, setIsExpanded] = useState(true); // Add collapsible state\r\n  const inputRef = useRef(null);\r\n  const newTaskInputRef = useRef(null);\r\n\r\n  // Calculate milestone progress\r\n  const progress = calculateMilestoneProgress ? calculateMilestoneProgress(milestone) : 0;\r\n\r\n  // Determine status based on progress\r\n  const milestoneStatus = getMilestoneStatus ? getMilestoneStatus(milestone) : STATUS.NOT_STARTED;\r\n  const statusConfig = STATUS_CONFIG[milestoneStatus] || STATUS_CONFIG[STATUS.NOT_STARTED];\r\n\r\n  // Check if description exists and is not empty\r\n  const hasDescription = milestone.description && milestone.description.trim().length > 0;\r\n\r\n  // Handle edit mode\r\n  const handleEditClick = () => {\r\n    setIsEditing(true);\r\n  };\r\n\r\n  // Handle save changes\r\n  const handleSave = () => {\r\n    // Trim the name to remove leading/trailing whitespace\r\n    const trimmedName = milestoneName.trim();\r\n\r\n    // Only update if the name has actually changed (after trimming)\r\n    if (trimmedName !== '' && trimmedName !== milestone.name) {\r\n      if (onUpdateMilestone) {\r\n        onUpdateMilestone({ ...milestone, name: trimmedName });\r\n      }\r\n    } else {\r\n      // Reset to original if empty or unchanged\r\n      setMilestoneName(milestone.name);\r\n    }\r\n\r\n    setIsEditing(false);\r\n  };\r\n\r\n  // Handle key press events\r\n  const handleKeyPress = (e) => {\r\n    if (e.key === 'Enter') {\r\n      handleSave();\r\n    } else if (e.key === 'Escape') {\r\n      setMilestoneName(milestone.name);\r\n      setIsEditing(false);\r\n    }\r\n  };\r\n\r\n  // Handle add task\r\n  const handleAddTaskClick = () => {\r\n    setIsAddingTask(true);\r\n  };\r\n\r\n  // Handle save new task\r\n  const handleSaveNewTask = () => {\r\n    const trimmedName = newTaskName.trim();\r\n    if (trimmedName && onAddTask) {\r\n      const newTask = {\r\n        name: trimmedName,\r\n        milestone: milestone.id,\r\n        status: STATUS.NOT_STARTED,\r\n        progress: 0\r\n      };\r\n      onAddTask(newTask);\r\n      setNewTaskName('');\r\n    }\r\n    setIsAddingTask(false);\r\n  };\r\n\r\n  // Handle key press events for new task\r\n  const handleNewTaskKeyPress = (e) => {\r\n    if (e.key === 'Enter') {\r\n      handleSaveNewTask();\r\n    } else if (e.key === 'Escape') {\r\n      setNewTaskName('');\r\n      setIsAddingTask(false);\r\n    }\r\n  };\r\n\r\n  // Focus input when editing starts\r\n  useEffect(() => {\r\n    if (isEditing && inputRef.current) {\r\n      inputRef.current.focus();\r\n    }\r\n  }, [isEditing]);\r\n\r\n  // Focus input when adding task\r\n  useEffect(() => {\r\n    if (isAddingTask && newTaskInputRef.current) {\r\n      newTaskInputRef.current.focus();\r\n    }\r\n  }, [isAddingTask]);\r\n\r\n  return (\r\n    <Paper\r\n      elevation={0}\r\n      className={styles.milestoneCard}\r\n      sx={{ padding: '16px' }}\r\n    >\r\n      <Box className={styles.milestoneHeader}>\r\n        {isEditing ? (\r\n          <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>\r\n            <Iconify icon=\"material-symbols:flag\" width={20} height={20} color={mainYellowColor} sx={{ mr: 1 }} />\r\n            <TextField\r\n              inputRef={inputRef}\r\n              value={milestoneName}\r\n              onChange={(e) => setMilestoneName(e.target.value)}\r\n              onKeyDown={handleKeyPress}\r\n              onBlur={handleSave}\r\n              variant=\"standard\"\r\n              fullWidth\r\n              autoFocus\r\n              sx={{\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                fontWeight: 600,\r\n                fontSize: '1.1rem',\r\n                '& .MuiInputBase-input': {\r\n                  fontWeight: 600,\r\n                  fontSize: '1.1rem',\r\n                  fontFamily: '\"Recursive Variable\", sans-serif',\r\n                }\r\n              }}\r\n            />\r\n            <IconButton size=\"small\" onClick={handleSave} sx={{ ml: 1 }}>\r\n              <Iconify icon=\"material-symbols:check\" width={20} height={20} color=\"#4CAF50\" />\r\n            </IconButton>\r\n          </Box>\r\n        ) : (\r\n          <Typography\r\n            variant=\"h6\"\r\n            className={styles.milestoneTitle}\r\n            sx={{\r\n              fontFamily: '\"Recursive Variable\", sans-serif',\r\n              display: 'flex',\r\n              alignItems: 'center',\r\n              cursor: 'pointer',\r\n              '&:hover': {\r\n                '& .edit-icon': {\r\n                  opacity: 1,\r\n                }\r\n              }\r\n            }}\r\n            onClick={handleEditClick}\r\n          >\r\n            <Iconify icon=\"material-symbols:flag\" width={20} height={20} color={mainYellowColor} />\r\n            {milestoneName}\r\n            <Tooltip title=\"Edit milestone name\">\r\n              <IconButton\r\n                size=\"small\"\r\n                className=\"edit-icon\"\r\n                sx={{\r\n                  ml: 1,\r\n                  opacity: 0,\r\n                  transition: 'opacity 0.2s',\r\n                  padding: '2px'\r\n                }}\r\n              >\r\n                <Iconify icon=\"material-symbols:edit-outline\" width={16} height={16} color=\"#666\" />\r\n              </IconButton>\r\n            </Tooltip>\r\n          </Typography>\r\n        )}\r\n\r\n        <Chip\r\n          icon={<Iconify icon={statusConfig.icon} width={16} height={16} />}\r\n          label={statusConfig.label}\r\n          size=\"small\"\r\n          sx={{\r\n            backgroundColor: `${statusConfig.color}20`,\r\n            color: statusConfig.color,\r\n            fontWeight: 600,\r\n            borderRadius: '4px',\r\n            fontFamily: '\"Recursive Variable\", sans-serif'\r\n          }}\r\n        />\r\n      </Box>\r\n\r\n      {/* Description Section */}\r\n      {!compact && hasDescription && (\r\n        <Box sx={{ mb: 2, mt: 1.5 }}>\r\n          <Box\r\n            sx={{\r\n              backgroundColor: '#f9f9f9',\r\n              p: 1.5,\r\n              borderRadius: '8px',\r\n              border: '1px solid #f0f0f0'\r\n            }}\r\n          >\r\n            <Typography\r\n              variant=\"body2\"\r\n              sx={{\r\n                color: '#333',\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                whiteSpace: 'pre-line',\r\n                lineHeight: 1.6,\r\n                fontWeight: 500\r\n              }}\r\n            >\r\n              {milestone.description}\r\n            </Typography>\r\n          </Box>\r\n        </Box>\r\n      )}\r\n\r\n      <Divider sx={{ my: 2, opacity: 0.6 }} />\r\n\r\n      <Box sx={{ mb: 2 }}>\r\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>\r\n          <Typography variant=\"body2\" sx={{ fontWeight: 600, fontFamily: '\"Recursive Variable\", sans-serif', color: '#333' }}>\r\n            Progress\r\n          </Typography>\r\n          <Typography variant=\"body2\" sx={{ fontWeight: 700, fontFamily: '\"Recursive Variable\", sans-serif', color: '#333' }}>\r\n            {progress}%\r\n          </Typography>\r\n        </Box>\r\n        <LinearProgress\r\n          variant=\"determinate\"\r\n          value={progress}\r\n          sx={{\r\n            height: 6,\r\n            borderRadius: 3,\r\n            backgroundColor: '#f0f0f0',\r\n            '& .MuiLinearProgress-bar': {\r\n              backgroundColor: statusConfig.color\r\n            }\r\n          }}\r\n        />\r\n      </Box>\r\n\r\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>\r\n        {milestone.estimated_duration && (\r\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n            <Iconify icon=\"material-symbols:timer\" width={16} height={16} color=\"#333\" />\r\n            <Typography variant=\"body2\" sx={{ fontFamily: '\"Recursive Variable\", sans-serif', color: '#333', fontWeight: 500 }}>\r\n              {milestone.estimated_duration}\r\n            </Typography>\r\n          </Box>\r\n        )}\r\n      </Box>\r\n\r\n      {/* Tasks Section */}\r\n      <Box sx={{ mb: 1 }}>\r\n        <Box\r\n          sx={{\r\n            display: 'flex',\r\n            alignItems: 'center',\r\n            justifyContent: 'space-between',\r\n            mb: 1\r\n          }}\r\n        >\r\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n            <Iconify\r\n              icon=\"material-symbols:checklist\"\r\n              width={16}\r\n              height={16}\r\n              sx={{ color: '#333' }}\r\n            />\r\n            <Typography\r\n              variant=\"body2\"\r\n              sx={{\r\n                fontWeight: 600,\r\n                color: '#333',\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                fontSize: '0.9rem'\r\n              }}\r\n            >\r\n              Tasks\r\n            </Typography>\r\n          </Box>\r\n\r\n          {/* Add task button */}\r\n          <Tooltip title=\"Add new task\">\r\n            <IconButton\r\n              size=\"small\"\r\n              onClick={handleAddTaskClick}\r\n              sx={{\r\n                color: mainYellowColor,\r\n                '&:hover': {\r\n                  backgroundColor: `${mainYellowColor}10`\r\n                }\r\n              }}\r\n            >\r\n              <Iconify icon=\"material-symbols:add-task\" width={18} height={18} />\r\n            </IconButton>\r\n          </Tooltip>\r\n        </Box>\r\n\r\n        {compact ? (\r\n          <Box className={styles.taskList}>\r\n            {milestone.tasks?.slice(0, 2).map((task, index) => (\r\n              <TaskItem\r\n                key={index}\r\n                task={task}\r\n                showSubtasks={showSubtasks}\r\n                compact={true}\r\n                calculateTaskProgress={calculateTaskProgress}\r\n                getTaskStatus={getTaskStatus}\r\n                calculateSubtaskProgress={calculateSubtaskProgress}\r\n                getSubtaskStatus={getSubtaskStatus}\r\n                onUpdateTask={onUpdateTask}\r\n                onUpdateSubtask={onUpdateSubtask}\r\n                onAddSubtask={onAddSubtask}\r\n                onDeleteTask={onDeleteTask}\r\n                onDeleteSubtask={onDeleteSubtask}\r\n                onAssignMembers={onAssignMembers}\r\n                invitedUsers={invitedUsers}\r\n                planOwner={planOwner}\r\n              />\r\n            ))}\r\n\r\n            {milestone.tasks?.length > 2 && (\r\n              <Button\r\n                variant=\"text\"\r\n                size=\"small\"\r\n                sx={{\r\n                  color: mainYellowColor,\r\n                  fontWeight: 600,\r\n                  textTransform: 'none',\r\n                  p: 0,\r\n                  mt: 1,\r\n                  fontFamily: '\"Recursive Variable\", sans-serif'\r\n                }}\r\n              >\r\n                + {milestone.tasks.length - 2} more tasks\r\n              </Button>\r\n            )}\r\n          </Box>\r\n        ) : (\r\n          <Box className={styles.taskList}>\r\n            {milestone.tasks?.map((task, index) => (\r\n              <TaskItem\r\n                key={index}\r\n                task={task}\r\n                showSubtasks={showSubtasks}\r\n                compact={false}\r\n                calculateTaskProgress={calculateTaskProgress}\r\n                getTaskStatus={getTaskStatus}\r\n                calculateSubtaskProgress={calculateSubtaskProgress}\r\n                getSubtaskStatus={getSubtaskStatus}\r\n                onUpdateTask={onUpdateTask}\r\n                onUpdateSubtask={onUpdateSubtask}\r\n                onAddSubtask={onAddSubtask}\r\n                onDeleteTask={onDeleteTask}\r\n                onDeleteSubtask={onDeleteSubtask}\r\n                onAssignMembers={onAssignMembers}\r\n                invitedUsers={invitedUsers}\r\n                planOwner={planOwner}\r\n              />\r\n            ))}\r\n\r\n            {/* New task input field */}\r\n            {isAddingTask && (\r\n              <Box\r\n                className={styles.taskItem}\r\n                sx={{\r\n                  position: 'relative',\r\n                  mt: 1,\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  backgroundColor: '#f9f9f9',\r\n                  borderRadius: '6px',\r\n                  padding: '8px 12px',\r\n                  border: '1px solid #f0f0f0'\r\n                }}\r\n              >\r\n                <Box\r\n                  sx={{\r\n                    width: 10,\r\n                    height: 10,\r\n                    borderRadius: '50%',\r\n                    backgroundColor: '#CCCCCC',\r\n                    mr: 1.5,\r\n                    flexShrink: 0\r\n                  }}\r\n                />\r\n\r\n                <TextField\r\n                  inputRef={newTaskInputRef}\r\n                  value={newTaskName}\r\n                  onChange={(e) => setNewTaskName(e.target.value)}\r\n                  onKeyDown={handleNewTaskKeyPress}\r\n                  placeholder=\"Enter new task name...\"\r\n                  variant=\"standard\"\r\n                  fullWidth\r\n                  autoFocus\r\n                  sx={{\r\n                    fontFamily: '\"Recursive Variable\", sans-serif',\r\n                    '& .MuiInputBase-input': {\r\n                      fontSize: '0.9rem',\r\n                      fontFamily: '\"Recursive Variable\", sans-serif',\r\n                      padding: '4px 0'\r\n                    },\r\n                    '& .MuiInput-underline:before': {\r\n                      borderBottomColor: 'rgba(0, 0, 0, 0.1)'\r\n                    }\r\n                  }}\r\n                />\r\n\r\n                <Box sx={{ display: 'flex', alignItems: 'center', ml: 'auto' }}>\r\n                  <IconButton size=\"small\" onClick={handleSaveNewTask} sx={{ ml: 1 }}>\r\n                    <Iconify icon=\"material-symbols:check\" width={18} height={18} color=\"#4CAF50\" />\r\n                  </IconButton>\r\n\r\n                  <IconButton size=\"small\" onClick={() => setIsAddingTask(false)} sx={{ ml: 0.5 }}>\r\n                    <Iconify icon=\"material-symbols:close\" width={18} height={18} color=\"#F44336\" />\r\n                  </IconButton>\r\n                </Box>\r\n              </Box>\r\n            )}\r\n          </Box>\r\n        )}\r\n      </Box>\r\n    </Paper>\r\n  );\r\n};\r\n// Component to display task and subtask\r\nconst TaskItem = ({\r\n  task,\r\n  showSubtasks = false,\r\n  compact = false,\r\n  calculateTaskProgress,\r\n  getTaskStatus,\r\n  calculateSubtaskProgress,\r\n  getSubtaskStatus,\r\n  onUpdateTask,\r\n  onUpdateSubtask,\r\n  onAddSubtask,\r\n  onDeleteTask,\r\n  onDeleteSubtask,\r\n  onAssignMembers,\r\n  invitedUsers,\r\n  planOwner\r\n}) => {\r\n  const [isEditing, setIsEditing] = useState(false);\r\n  const [taskName, setTaskName] = useState(task.name);\r\n  const taskInputRef = useRef(null);\r\n  const [isAddingSubtask, setIsAddingSubtask] = useState(false);\r\n  const [newSubtaskName, setNewSubtaskName] = useState('');\r\n  const newSubtaskInputRef = useRef(null);\r\n\r\n  // Comment functionality\r\n  const [commentDialogOpen, setCommentDialogOpen] = useState(false);\r\n  const [comments, setComments] = useState([]);\r\n  const [loadingComments, setLoadingComments] = useState(false);\r\n\r\n  // Enable due date functionality\r\n  const [dueDateDialogOpen, setDueDateDialogOpen] = useState(false);\r\n  const [updatingDueDate, setUpdatingDueDate] = useState(false);\r\n\r\n  // Add state for assign member dialog\r\n  const [assignMemberDialogOpen, setAssignMemberDialogOpen] = useState(false);\r\n\r\n  // Add state to store current task information\r\n  const [localTask, setLocalTask] = useState(task);\r\n\r\n  // Update localTask when task changes from props\r\n  useEffect(() => {\r\n    setLocalTask(task);\r\n  }, [task]);\r\n\r\n  const handleOpenComments = (e) => {\r\n    e.stopPropagation();\r\n    setCommentDialogOpen(true);\r\n    setLoadingComments(true);\r\n    getComments(task.id)\r\n      .then(response => {\r\n        console.log('Comments data:', response.data);\r\n        // Check data structure and get correct comments array\r\n        let commentsData = [];\r\n        if (Array.isArray(response.data)) {\r\n          commentsData = response.data;\r\n        } else if (response.data?.data && Array.isArray(response.data.data)) {\r\n          commentsData = response.data.data;\r\n        } else if (response.data?.comments && Array.isArray(response.data.comments)) {\r\n          commentsData = response.data.comments;\r\n        }\r\n        setComments(commentsData);\r\n      })\r\n      .catch(error => {\r\n        console.error('Error fetching comments:', error);\r\n        toast.error('Failed to load comments');\r\n      })\r\n      .finally(() => {\r\n        setLoadingComments(false);\r\n      });\r\n  };\r\n\r\n  const handleAddComment = async (content) => {\r\n    try {\r\n      const response = await addComment(task.id, content);\r\n      console.log('Add comment response:', response);\r\n\r\n      // Create new comment from response or create temporary object\r\n      let newComment;\r\n      if (response.data?.data) {\r\n        newComment = response.data.data;\r\n      } else {\r\n        // Create temporary object if API does not return new comment\r\n        const currentUser = JSON.parse(localStorage.getItem('user')) || {};\r\n        newComment = {\r\n          id: Date.now(), // Temporary ID\r\n          content: content,\r\n          user: {\r\n            id: currentUser.id,\r\n            first_name: currentUser.first_name,\r\n            last_name: currentUser.last_name,\r\n            avatar: currentUser.avatar\r\n          },\r\n          created_at: new Date().toLocaleString('en-US', {\r\n            hour: '2-digit',\r\n            minute: '2-digit',\r\n            day: '2-digit',\r\n            month: '2-digit'\r\n          }).replace(',', '')\r\n        };\r\n      }\r\n\r\n      // Update state\r\n      setComments(prevComments => [...prevComments, newComment]);\r\n\r\n      // Refresh comments to ensure latest data\r\n      refreshComments();\r\n    } catch (error) {\r\n      console.error('Error adding comment:', error);\r\n      toast.error('Failed to add comment');\r\n    }\r\n  };\r\n\r\n  const handleUpdateComment = async (commentId, content) => {\r\n    try {\r\n      // Update UI first\r\n      const updatedCommentTemp = comments.find(c => c.id === commentId);\r\n      if (updatedCommentTemp) {\r\n        const updatedComments = comments.map(c =>\r\n          c.id === commentId ? { ...c, content: content } : c\r\n        );\r\n        setComments(updatedComments);\r\n      }\r\n\r\n      // Call API\r\n      const response = await updateComment(commentId, content);\r\n      console.log('Update comment response:', response);\r\n\r\n      toast.success('Comment updated successfully');\r\n\r\n      // Refresh comments to ensure latest data\r\n      refreshComments();\r\n    } catch (error) {\r\n      console.error('Error updating comment:', error);\r\n      toast.error('Failed to update comment');\r\n      // Refresh comments to restore original state if there is an error\r\n      refreshComments();\r\n    }\r\n  };\r\n\r\n  const handleDeleteComment = async (commentId) => {\r\n    try {\r\n      // Update UI first\r\n      setComments(prevComments => prevComments.filter(c => c.id !== commentId));\r\n\r\n      // Call API\r\n      await deleteComment(commentId);\r\n      console.log('Comment deleted successfully');\r\n\r\n      toast.success('Comment deleted successfully');\r\n\r\n      // Refresh comments to ensure latest data\r\n      refreshComments();\r\n    } catch (error) {\r\n      console.error('Error deleting comment:', error);\r\n      toast.error('Failed to delete comment');\r\n      // Refresh comments to restore original state if there is an error\r\n      refreshComments();\r\n    }\r\n  };\r\n\r\n  // Refresh comments function\r\n  const refreshComments = async () => {\r\n    try {\r\n      const response = await getComments(task.id);\r\n      console.log('Refreshed comments:', response.data);\r\n\r\n      // Handle returned data\r\n      let commentsData = [];\r\n      if (Array.isArray(response.data)) {\r\n        commentsData = response.data;\r\n      } else if (response.data?.data && Array.isArray(response.data.data)) {\r\n        commentsData = response.data.data;\r\n      } else if (response.data?.comments && Array.isArray(response.data.comments)) {\r\n        commentsData = response.data.comments;\r\n      }\r\n\r\n      setComments(commentsData);\r\n    } catch (error) {\r\n      console.error('Error refreshing comments:', error);\r\n    }\r\n  };\r\n\r\n  // Calculate task progress\r\n  const taskProgress = calculateTaskProgress ? calculateTaskProgress(task) : 0;\r\n\r\n  // Determine task status\r\n  const taskStatus = getTaskStatus ? getTaskStatus(task) : (task.status || STATUS.NOT_STARTED);\r\n  const statusConfig = STATUS_CONFIG[taskStatus] || STATUS_CONFIG[STATUS.NOT_STARTED];\r\n\r\n  // Check if task has subtasks\r\n  const hasSubtasks = task.subtasks && task.subtasks.length > 0;\r\n\r\n  // Check if task is completed\r\n  const isCompleted = taskStatus === STATUS.COMPLETED;\r\n\r\n  // Handle edit mode for task\r\n  const handleTaskEditClick = () => {\r\n    setIsEditing(true);\r\n  };\r\n\r\n  // Handle save changes for task\r\n  const handleTaskSave = () => {\r\n    // Trim the name to remove leading/trailing whitespace\r\n    const trimmedName = taskName.trim();\r\n\r\n    // Only update if the name has actually changed (after trimming)\r\n    if (trimmedName !== '' && trimmedName !== task.name) {\r\n      if (onUpdateTask) {\r\n        onUpdateTask({ ...task, name: trimmedName });\r\n      }\r\n    } else {\r\n      // Reset to original if empty or unchanged\r\n      setTaskName(task.name);\r\n    }\r\n\r\n    setIsEditing(false);\r\n  };\r\n\r\n  // Handle key press events for task\r\n  const handleTaskKeyPress = (e) => {\r\n    if (e.key === 'Enter') {\r\n      handleTaskSave();\r\n    } else if (e.key === 'Escape') {\r\n      setTaskName(task.name);\r\n      setIsEditing(false);\r\n    }\r\n  };\r\n\r\n  // Handle add subtask\r\n  const handleAddSubtaskClick = () => {\r\n    setIsAddingSubtask(true);\r\n  };\r\n\r\n  // Handle delete task\r\n  const handleDeleteTaskClick = () => {\r\n    if (onDeleteTask) {\r\n      onDeleteTask(task);\r\n    }\r\n  };\r\n\r\n  // Handle save new subtask\r\n  const handleSaveNewSubtask = () => {\r\n    const trimmedName = newSubtaskName.trim();\r\n    if (trimmedName && onAddSubtask) {\r\n      const newSubtask = {\r\n        name: trimmedName,\r\n        task: task.slug,\r\n        status: STATUS.NOT_STARTED,\r\n        progress: 0\r\n      };\r\n      onAddSubtask(newSubtask);\r\n      setNewSubtaskName('');\r\n    }\r\n    setIsAddingSubtask(false);\r\n  };\r\n\r\n  // Handle key press events for new subtask\r\n  const handleNewSubtaskKeyPress = (e) => {\r\n    if (e.key === 'Enter') {\r\n      handleSaveNewSubtask();\r\n    } else if (e.key === 'Escape') {\r\n      setNewSubtaskName('');\r\n      setIsAddingSubtask(false);\r\n    }\r\n  };\r\n\r\n  // Focus input when editing starts\r\n  useEffect(() => {\r\n    if (isEditing && taskInputRef.current) {\r\n      taskInputRef.current.focus();\r\n    }\r\n  }, [isEditing]);\r\n\r\n  // Focus input when adding subtask\r\n  useEffect(() => {\r\n    if (isAddingSubtask && newSubtaskInputRef.current) {\r\n      newSubtaskInputRef.current.focus();\r\n    }\r\n  }, [isAddingSubtask]);\r\n\r\n  // Check if task has due dates - use localTask instead of task\r\n  const hasDueDates = localTask.start_date || localTask.end_date;\r\n\r\n  const handleOpenDueDateDialog = (e) => {\r\n    e.stopPropagation();\r\n    setDueDateDialogOpen(true);\r\n  };\r\n\r\n  const handleUpdateDueDate = async (updatedTask) => {\r\n    setUpdatingDueDate(true);\r\n    try {\r\n      const taskToUpdate = {\r\n        ...localTask,\r\n        start_date: updatedTask.start_date,\r\n        end_date: updatedTask.end_date,\r\n        progress: localTask.progress || 0,\r\n        status: localTask.status || 1\r\n      };\r\n\r\n      // Update local state first\r\n      setLocalTask(taskToUpdate);\r\n\r\n      // Call API\r\n      const response = await updateTask(taskToUpdate);\r\n\r\n      // If API returns data, update local state\r\n      if (response && response.data) {\r\n        setLocalTask(response.data);\r\n      }\r\n\r\n      // Update state in parent component if necessary\r\n      if (onUpdateTask) {\r\n        onUpdateTask(taskToUpdate);\r\n      }\r\n\r\n      toast.success('Due date updated successfully');\r\n    } catch (error) {\r\n      console.error('Error updating due date:', error);\r\n      toast.error('Failed to update due date');\r\n\r\n      // If there is an error, restore original state\r\n      setLocalTask(task);\r\n    } finally {\r\n      setUpdatingDueDate(false);\r\n      setDueDateDialogOpen(false); // Close dialog after completion\r\n    }\r\n  };\r\n\r\n  // Add handler for member assignment updates\r\n  const handleAssignMembers = async (assignedUserIds) => {\r\n    try {\r\n      // Convert IDs to numbers\r\n      const numericIds = assignedUserIds.map(id => Number(id));\r\n      // Call API to assign members\r\n      const response = await assignMembersToTask(localTask.slug, numericIds);\r\n      // Create updated task with new assignees\r\n      const updatedTask = {\r\n        ...localTask,\r\n        assignees: response.assignees || numericIds.map(id => ({\r\n          id: id,\r\n          first_name: '',\r\n          last_name: '',\r\n          email: ''\r\n        }))\r\n      };\r\n\r\n      // Update both local state and parent state\r\n      setLocalTask(updatedTask);\r\n      if (onUpdateTask) {\r\n        onUpdateTask(updatedTask);\r\n      }\r\n\r\n      setAssignMemberDialogOpen(false);\r\n      toast.success('Members assigned successfully');\r\n    } catch (error) {\r\n      console.error('Error assigning members:', error);\r\n      toast.error('Failed to assign members');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Box className={styles.taskItemContainer}>\r\n        <Box\r\n          className={styles.taskItem}\r\n          sx={{ position: 'relative' }}>\r\n          <Box\r\n            sx={{\r\n              width: 10,\r\n              height: 10,\r\n              borderRadius: '50%',\r\n              backgroundColor: statusConfig.color,\r\n              mr: 1.5,\r\n              flexShrink: 0\r\n            }}\r\n          />\r\n\r\n          {isEditing ? (\r\n            <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>\r\n              <TextField\r\n                inputRef={taskInputRef}\r\n                value={taskName}\r\n                onChange={(e) => setTaskName(e.target.value)}\r\n                onKeyDown={handleTaskKeyPress}\r\n                onBlur={handleTaskSave}\r\n                variant=\"standard\"\r\n                fullWidth\r\n                autoFocus\r\n                sx={{\r\n                  fontFamily: '\"Recursive Variable\", sans-serif',\r\n                  '& .MuiInputBase-input': {\r\n                    fontSize: '0.9rem',\r\n                    fontFamily: '\"Recursive Variable\", sans-serif',\r\n                  }\r\n                }}\r\n              />\r\n              <IconButton size=\"small\" onClick={handleTaskSave} sx={{ ml: 1 }}>\r\n                <Iconify icon=\"material-symbols:check\" width={16} height={16} color=\"#4CAF50\" />\r\n              </IconButton>\r\n            </Box>\r\n          ) : (\r\n            <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', gap: 0.5 }}>\r\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>\r\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexGrow: 1 }}>\r\n                  <Typography\r\n                    variant=\"body2\"\r\n                    className={styles.taskName}\r\n                    sx={{\r\n                      fontFamily: '\"Recursive Variable\", sans-serif',\r\n                      cursor: 'pointer',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      color: isCompleted ? '#4CAF50' : 'inherit',\r\n                      fontWeight: isCompleted ? 500 : 400,\r\n                      flexGrow: 1,\r\n                      '&:hover': {\r\n                        '& .task-edit-icon': {\r\n                          opacity: 1,\r\n                        }\r\n                      }\r\n                    }}\r\n                    onClick={handleTaskEditClick}>\r\n                    {taskName}\r\n                    <Tooltip title=\"Edit task name\">\r\n                      <IconButton\r\n                        size=\"small\"\r\n                        className=\"task-edit-icon\"\r\n                        sx={{\r\n                          ml: 0.5,\r\n                          opacity: 0,\r\n                          transition: 'opacity 0.2s',\r\n                          padding: '2px'\r\n                        }}\r\n                      >\r\n                        <Iconify icon=\"material-symbols:edit-outline\" width={14} height={14} color=\"#666\" />\r\n                      </IconButton>\r\n                    </Tooltip>\r\n                  </Typography>\r\n                </Box>\r\n\r\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n                  {/* Display assigned members avatars */}\r\n                  {localTask.assignees && localTask.assignees.length > 0 && (\r\n                    <AvatarGroup\r\n                      max={3}\r\n                      sx={{\r\n                        '& .MuiAvatar-root': {\r\n                          width: 24,\r\n                          height: 24,\r\n                          fontSize: '0.75rem',\r\n                          border: '1.5px solid #fff'\r\n                        }\r\n                      }}\r\n                    >\r\n                      {localTask.assignees.map((assignee, index) => (\r\n                        <Tooltip\r\n                          key={assignee.id}\r\n                          title={assignee.first_name && assignee.last_name\r\n                            ? `${assignee.first_name} ${assignee.last_name}`\r\n                            : assignee.email}\r\n                          arrow\r\n                        >\r\n                          <Avatar\r\n                            src={assignee.avatar}\r\n                            alt={assignee.first_name || assignee.email}\r\n                            sx={{\r\n                              bgcolor: mainYellowColor,\r\n                            }}\r\n                          >\r\n                            {assignee.first_name\r\n                              ? assignee.first_name.charAt(0).toUpperCase()\r\n                              : assignee.email\r\n                                ? assignee.email.charAt(0).toUpperCase()\r\n                                : ''}\r\n                          </Avatar>\r\n                        </Tooltip>\r\n                      ))}\r\n                    </AvatarGroup>\r\n                  )}\r\n\r\n                  {/* Group all actions into a Box with border */}\r\n                  <Box\r\n                    sx={{\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      border: '1px solid #eee',\r\n                      borderRadius: '8px',\r\n                      padding: '2px 4px',\r\n                      background: '#f9f9f9'\r\n                    }}\r\n                  >\r\n                    {/* Due Date Button - only display icon */}\r\n                    <Tooltip title={hasDueDates ? \"Edit due date\" : \"Set due date\"}>\r\n                      <IconButton\r\n                        size=\"small\"\r\n                        onClick={handleOpenDueDateDialog}\r\n                        sx={{\r\n                          color: hasDueDates ? mainYellowColor : '#888',\r\n                          '&:hover': {\r\n                            color: mainYellowColor,\r\n                            bgcolor: 'rgba(255, 193, 7, 0.08)'\r\n                          }\r\n                        }}\r\n                      >\r\n                        <Iconify\r\n                          icon={hasDueDates ? \"material-symbols:calendar-month\" : \"material-symbols:calendar-add-on\"}\r\n                          width={16}\r\n                          height={16}\r\n                        />\r\n                      </IconButton>\r\n                    </Tooltip>\r\n\r\n                    {/* Comment Button */}\r\n                    <Tooltip title=\"Comments\">\r\n                      <IconButton\r\n                        size=\"small\"\r\n                        onClick={handleOpenComments}\r\n                        disabled={loadingComments}\r\n                        sx={{\r\n                          color: mainYellowColor,\r\n                          '&:hover': {\r\n                            bgcolor: 'rgba(255, 193, 7, 0.08)'\r\n                          }\r\n                        }}\r\n                      >\r\n                        {loadingComments ? (\r\n                          <CircularProgress size={16} sx={{ color: mainYellowColor }} />\r\n                        ) : (\r\n                          <Iconify icon=\"material-symbols:comment-outline\" width={16} height={16} />\r\n                        )}\r\n                      </IconButton>\r\n                    </Tooltip>\r\n\r\n                    {/* Assign Member Button */}\r\n                    <Tooltip title=\"Assign members\">\r\n                      <IconButton\r\n                        size=\"small\"\r\n                        onClick={() => setAssignMemberDialogOpen(true)}\r\n                        sx={{\r\n                          color: localTask.assignees?.length > 0 ? mainYellowColor : '#888',\r\n                          '&:hover': {\r\n                            color: mainYellowColor,\r\n                            bgcolor: 'rgba(255, 193, 7, 0.08)'\r\n                          }\r\n                        }}\r\n                      >\r\n                        <Iconify\r\n                          icon=\"mdi:account-multiple-plus\"\r\n                          width={16}\r\n                          height={16}\r\n                        />\r\n                      </IconButton>\r\n                    </Tooltip>\r\n\r\n                    {/* Add subtask button */}\r\n                    <Tooltip title=\"Add subtask\">\r\n                      <IconButton\r\n                        size=\"small\"\r\n                        onClick={handleAddSubtaskClick}\r\n                        sx={{\r\n                          color: mainYellowColor,\r\n                          '&:hover': {\r\n                            bgcolor: 'rgba(255, 193, 7, 0.08)'\r\n                          }\r\n                        }}\r\n                      >\r\n                        <Iconify icon=\"material-symbols:add-task\" width={16} height={16} />\r\n                      </IconButton>\r\n                    </Tooltip>\r\n\r\n                    {/* Edit Task Button */}\r\n                    <Tooltip title=\"Edit task\">\r\n                      <IconButton\r\n                        size=\"small\"\r\n                        onClick={handleTaskEditClick}\r\n                        sx={{\r\n                          color: '#666',\r\n                          '&:hover': {\r\n                            bgcolor: 'rgba(0, 0, 0, 0.04)'\r\n                          }\r\n                        }}\r\n                      >\r\n                        <Iconify icon=\"eva:edit-fill\" width={16} height={16} />\r\n                      </IconButton>\r\n                    </Tooltip>\r\n\r\n                    {/* Delete task button */}\r\n                    <Tooltip title=\"Delete task\">\r\n                      <IconButton\r\n                        size=\"small\"\r\n                        onClick={handleDeleteTaskClick}\r\n                        sx={{\r\n                          color: '#F44336',\r\n                          '&:hover': {\r\n                            bgcolor: 'rgba(244, 67, 54, 0.08)'\r\n                          }\r\n                        }}\r\n                      >\r\n                        <Iconify icon=\"material-symbols:delete-outline\" width={16} height={16} />\r\n                      </IconButton>\r\n                    </Tooltip>\r\n                  </Box>\r\n\r\n                  {/* Display task progress */}\r\n                  <Box\r\n                    sx={{\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      bgcolor: statusConfig.color + '20',\r\n                      px: 1,\r\n                      py: 0.5,\r\n                      borderRadius: '4px',\r\n                    }}\r\n                  >\r\n                    <Typography\r\n                      variant=\"caption\"\r\n                      sx={{\r\n                        fontWeight: 600,\r\n                        color: statusConfig.color,\r\n                        fontFamily: '\"Recursive Variable\", sans-serif'\r\n                      }}\r\n                    >\r\n                      {taskProgress}%\r\n                    </Typography>\r\n                  </Box>\r\n                </Box>\r\n              </Box>\r\n\r\n              {/* Due date display below task name */}\r\n              {hasDueDates && (\r\n                <Box\r\n                  sx={{\r\n                    display: 'flex',\r\n                    alignItems: 'center',\r\n                    backgroundColor: `${mainYellowColor}08`,\r\n                    borderRadius: '4px',\r\n                    py: 0.5,\r\n                    px: 0.75,\r\n                    width: 'fit-content'\r\n                  }}\r\n                >\r\n                  <Iconify\r\n                    icon=\"material-symbols:calendar-month\"\r\n                    width={14}\r\n                    height={14}\r\n                    color={mainYellowColor} />\r\n                  <Typography\r\n                    variant=\"caption\"\r\n                    sx={{\r\n                      fontFamily: '\"Recursive Variable\", sans-serif',\r\n                      fontSize: '0.75rem',\r\n                      color: '#555',\r\n                      fontWeight: 500,\r\n                      lineHeight: 1,\r\n                      ml: 0.5\r\n                    }}>\r\n                    {formatDate(localTask.start_date)} ~ {formatDate(localTask.end_date)}\r\n                  </Typography>\r\n                </Box>\r\n              )}\r\n            </Box>\r\n          )}\r\n        </Box>\r\n\r\n        <Box\r\n          sx={{\r\n            pl: 4,\r\n            pr: 1,\r\n            pt: 0.5,\r\n            pb: 0.5,\r\n            borderLeft: `1px dashed ${statusConfig.color}`,\r\n            ml: 1.5,\r\n            mt: 0.5,\r\n            display: (hasSubtasks || isAddingSubtask) && showSubtasks ? 'block' : 'none'\r\n          }}\r\n        >\r\n          {task.subtasks && task.subtasks.map((subtask, index) => (\r\n            <SubtaskItem\r\n              key={index}\r\n              subtask={subtask}\r\n              index={index}\r\n              totalSubtasks={task.subtasks.length}\r\n              taskSlug={task.slug}\r\n              calculateSubtaskProgress={calculateSubtaskProgress}\r\n              getSubtaskStatus={getSubtaskStatus}\r\n              onUpdateSubtask={onUpdateSubtask}\r\n              onDeleteSubtask={onDeleteSubtask}\r\n              onAssignMembers={onAssignMembers}\r\n              invitedUsers={invitedUsers}\r\n              planOwner={planOwner}\r\n            />\r\n          ))}\r\n\r\n          {/* New subtask input field */}\r\n          {isAddingSubtask && (\r\n            <Box\r\n              sx={{\r\n                display: 'flex',\r\n                alignItems: 'center',\r\n                py: 0.5,\r\n                borderBottom: '1px dotted #f0f0f0',\r\n                backgroundColor: '#f9f9f9',\r\n                borderRadius: '4px',\r\n                px: 1\r\n              }}\r\n            >\r\n              <Checkbox\r\n                disabled\r\n                size=\"small\"\r\n                sx={{\r\n                  p: 0.5,\r\n                  mr: 0.5,\r\n                  color: '#CCCCCC'\r\n                }}\r\n                icon={<Iconify icon=\"material-symbols:check-box-outline-blank\" width={18} height={18} />}\r\n              />\r\n\r\n              <TextField\r\n                inputRef={newSubtaskInputRef}\r\n                value={newSubtaskName}\r\n                onChange={(e) => setNewSubtaskName(e.target.value)}\r\n                onKeyDown={handleNewSubtaskKeyPress}\r\n                placeholder=\"Enter new subtask name...\"\r\n                variant=\"standard\"\r\n                fullWidth\r\n                autoFocus\r\n                sx={{\r\n                  fontFamily: '\"Recursive Variable\", sans-serif',\r\n                  '& .MuiInputBase-input': {\r\n                    fontSize: '0.85rem',\r\n                    fontFamily: '\"Recursive Variable\", sans-serif',\r\n                    padding: '4px 0'\r\n                  },\r\n                  '& .MuiInput-underline:before': {\r\n                    borderBottomColor: 'rgba(0, 0, 0, 0.1)'\r\n                  }\r\n                }}\r\n              />\r\n\r\n              <Box sx={{ display: 'flex', ml: 1 }}>\r\n                <IconButton size=\"small\" onClick={handleSaveNewSubtask} sx={{ p: 0.5 }}>\r\n                  <Iconify icon=\"material-symbols:check\" width={18} height={18} color=\"#4CAF50\" />\r\n                </IconButton>\r\n\r\n                <IconButton size=\"small\" onClick={() => setIsAddingSubtask(false)} sx={{ p: 0.5, ml: 0.5 }}>\r\n                  <Iconify icon=\"material-symbols:close\" width={18} height={18} color=\"#F44336\" />\r\n                </IconButton>\r\n              </Box>\r\n            </Box>\r\n          )}\r\n        </Box>\r\n      </Box>\r\n\r\n      {/* Comment Dialog */}\r\n      <CommentDialog\r\n        open={commentDialogOpen}\r\n        onClose={() => setCommentDialogOpen(false)}\r\n        comments={comments}\r\n        onAddComment={handleAddComment}\r\n        onUpdateComment={handleUpdateComment}\r\n        onDeleteComment={handleDeleteComment}\r\n        loading={loadingComments}\r\n        targetName={task.name}\r\n        targetType=\"task\"\r\n      />\r\n\r\n      {/* Due Date Dialog */}\r\n      <DueDateDialog\r\n        open={dueDateDialogOpen}\r\n        onClose={() => setDueDateDialogOpen(false)}\r\n        task={localTask}\r\n        onUpdateDueDate={handleUpdateDueDate}\r\n        isUpdating={updatingDueDate}\r\n      />\r\n\r\n      {/* Assign Member Dialog */}\r\n      <AssignMemberDialog\r\n        open={assignMemberDialogOpen}\r\n        onClose={() => setAssignMemberDialogOpen(false)}\r\n        task={localTask}\r\n        invitedUsers={invitedUsers}\r\n        planOwner={planOwner}\r\n        onAssignMembers={handleAssignMembers}\r\n      />\r\n    </>\r\n  );\r\n};\r\n\r\n// Component for subtask\r\nconst SubtaskItem = ({\r\n  subtask,\r\n  index,\r\n  totalSubtasks,\r\n  taskSlug,\r\n  calculateSubtaskProgress,\r\n  getSubtaskStatus,\r\n  onUpdateSubtask,\r\n  onDeleteSubtask,\r\n  onAssignMembers,\r\n  invitedUsers,\r\n  planOwner\r\n}) => {\r\n  const [isEditing, setIsEditing] = useState(false);\r\n  const [subtaskName, setSubtaskName] = useState(subtask.name);\r\n  const [isChecked, setIsChecked] = useState(subtask.status === STATUS.COMPLETED || subtask.progress === 100);\r\n  const subtaskInputRef = useRef(null);\r\n\r\n  // Calculate subtask progress and status\r\n  const subtaskProgress = calculateSubtaskProgress ? calculateSubtaskProgress(subtask) : 0;\r\n  const subtaskStatus = getSubtaskStatus ? getSubtaskStatus(subtask) : (subtask.status || STATUS.NOT_STARTED);\r\n  const subtaskStatusConfig = STATUS_CONFIG[subtaskStatus] || STATUS_CONFIG[STATUS.NOT_STARTED];\r\n\r\n  // Update isChecked when subtask changes from outside\r\n  useEffect(() => {\r\n    setIsChecked(subtask.status === STATUS.COMPLETED || subtask.progress === 100);\r\n  }, [subtask.status, subtask.progress]);\r\n\r\n  // Handle edit mode for subtask\r\n  const handleSubtaskEditClick = () => {\r\n    setIsEditing(true);\r\n  };\r\n\r\n  // Handle save changes for subtask\r\n  const handleSubtaskSave = () => {\r\n    // Trim the name to remove leading/trailing whitespace\r\n    const trimmedName = subtaskName.trim();\r\n\r\n    // Only update if the name has actually changed (after trimming)\r\n    if (trimmedName !== '' && trimmedName !== subtask.name) {\r\n      if (onUpdateSubtask) {\r\n        onUpdateSubtask({ ...subtask, name: trimmedName, task: taskSlug });\r\n      }\r\n    } else {\r\n      // Reset to original if empty or unchanged\r\n      setSubtaskName(subtask.name);\r\n    }\r\n\r\n    setIsEditing(false);\r\n  };\r\n\r\n  // Handle key press events for subtask\r\n  const handleSubtaskKeyPress = (e) => {\r\n    if (e.key === 'Enter') {\r\n      handleSubtaskSave();\r\n    } else if (e.key === 'Escape') {\r\n      setSubtaskName(subtask.name);\r\n      setIsEditing(false);\r\n    }\r\n  };\r\n\r\n  // Handle status toggle\r\n  const handleStatusToggle = () => {\r\n    // Update UI immediately to provide user feedback\r\n    const newCheckedState = !isChecked;\r\n    setIsChecked(newCheckedState);\r\n\r\n    const newStatus = newCheckedState ? STATUS.COMPLETED : STATUS.NOT_STARTED;\r\n    const newProgress = newCheckedState ? 100 : 0;\r\n\r\n    if (onUpdateSubtask) {\r\n      // Ensure to send both status and progress, even when progress = 0\r\n      const updatedData = {\r\n        ...subtask,\r\n        status: newStatus,\r\n        progress: newProgress,\r\n        task: taskSlug\r\n      };\r\n\r\n      console.log('Sending subtask update:', updatedData);\r\n      onUpdateSubtask(updatedData);\r\n    }\r\n  };\r\n\r\n  // Handle delete subtask\r\n  const handleDeleteSubtaskClick = () => {\r\n    if (window.confirm(`Are you sure you want to delete this subtask?`)) {\r\n      onDeleteSubtask(subtask, taskSlug);\r\n    }\r\n  };\r\n\r\n  // Focus input when editing starts\r\n  useEffect(() => {\r\n    if (isEditing && subtaskInputRef.current) {\r\n      subtaskInputRef.current.focus();\r\n    }\r\n  }, [isEditing]);\r\n\r\n  return (\r\n    <Box\r\n      sx={{\r\n        display: 'flex',\r\n        alignItems: 'center',\r\n        py: 0.5,\r\n        borderBottom: index < totalSubtasks - 1 ? '1px dotted #f0f0f0' : 'none'\r\n      }}\r\n    >\r\n      <Checkbox\r\n        checked={isChecked}\r\n        onChange={handleStatusToggle}\r\n        size=\"small\"\r\n        sx={{\r\n          p: 0.5,\r\n          mr: 0.5,\r\n          color: '#CCCCCC',\r\n          '&.Mui-checked': {\r\n            color: '#4CAF50',\r\n          }\r\n        }}\r\n        icon={<Iconify icon=\"material-symbols:check-box-outline-blank\" width={18} height={18} />}\r\n        checkedIcon={<Iconify icon=\"material-symbols:check-box\" width={18} height={18} />}\r\n      />\r\n\r\n      {isEditing ? (\r\n        <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>\r\n          <TextField\r\n            inputRef={subtaskInputRef}\r\n            value={subtaskName}\r\n            onChange={(e) => setSubtaskName(e.target.value)}\r\n            onKeyDown={handleSubtaskKeyPress}\r\n            onBlur={handleSubtaskSave}\r\n            variant=\"standard\"\r\n            fullWidth\r\n            autoFocus\r\n            sx={{\r\n              fontFamily: '\"Recursive Variable\", sans-serif',\r\n              '& .MuiInputBase-input': {\r\n                fontSize: '0.85rem',\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n              }\r\n            }}\r\n          />\r\n          <IconButton size=\"small\" onClick={handleSubtaskSave} sx={{ ml: 1 }}>\r\n            <Iconify icon=\"material-symbols:check\" width={14} height={14} color=\"#4CAF50\" />\r\n          </IconButton>\r\n        </Box>\r\n      ) : (\r\n        <Typography\r\n          variant=\"body2\"\r\n          sx={{\r\n            flexGrow: 1,\r\n            fontSize: '0.85rem',\r\n            fontFamily: '\"Recursive Variable\", sans-serif',\r\n            color: isChecked ? '#4CAF50' : '#555',\r\n            fontWeight: isChecked ? 500 : 400,\r\n            cursor: 'pointer',\r\n            display: 'flex',\r\n            alignItems: 'center',\r\n            '&:hover': {\r\n              '& .subtask-edit-icon': {\r\n                opacity: 1,\r\n              }\r\n            }\r\n          }}\r\n          onClick={handleSubtaskEditClick}\r\n        >\r\n          {subtaskName}\r\n          <Tooltip title=\"Edit subtask name\">\r\n            <IconButton\r\n              size=\"small\"\r\n              className=\"subtask-edit-icon\"\r\n              sx={{\r\n                ml: 0.5,\r\n                opacity: 0,\r\n                transition: 'opacity 0.2s',\r\n                padding: '1px'\r\n              }}\r\n            >\r\n              <Iconify icon=\"material-symbols:edit-outline\" width={12} height={12} color=\"#666\" />\r\n            </IconButton>\r\n          </Tooltip>\r\n        </Typography>\r\n      )}\r\n\r\n      <Box sx={{ display: 'flex', alignItems: 'center' }}>\r\n        {subtaskProgress !== null && subtaskProgress !== undefined && (\r\n          <Typography\r\n            variant=\"caption\"\r\n            sx={{\r\n              fontSize: '0.75rem',\r\n              color: subtaskStatusConfig.color,\r\n              ml: 1,\r\n              mr: 1,\r\n              fontFamily: '\"Recursive Variable\", sans-serif',\r\n              fontWeight: 600\r\n            }}\r\n          >\r\n            {subtaskProgress}%\r\n          </Typography>\r\n        )}\r\n\r\n        {/* Delete subtask button */}\r\n        <Tooltip title=\"Delete subtask\">\r\n          <IconButton\r\n            size=\"small\"\r\n            onClick={handleDeleteSubtaskClick}\r\n            sx={{\r\n              p: 0.5,\r\n              color: '#F44336',\r\n              '&:hover': {\r\n                backgroundColor: '#FFEBEE'\r\n              }\r\n            }}\r\n          >\r\n            <Iconify icon=\"material-symbols:delete-outline\" width={14} height={14} />\r\n          </IconButton>\r\n        </Tooltip>\r\n      </Box>\r\n    </Box>\r\n  );\r\n};\r\n\r\n// Format date for display\r\nfunction formatDate(dateString) {\r\n  if (!dateString) return '';\r\n\r\n  try {\r\n    const date = new Date(dateString);\r\n    if (isNaN(date.getTime())) return '';\r\n\r\n    // Only display day and month, not year\r\n    const options = { month: 'short', day: 'numeric' };\r\n    return date.toLocaleDateString('en-US', options);\r\n  } catch (error) {\r\n    return '';\r\n  }\r\n}\r\n\r\nexport default MilestoneCard;\r\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,cAAc,EACdC,MAAM,EACNC,OAAO,EACPC,SAAS,EACTC,UAAU,EACVC,OAAO,EACPC,QAAQ,EACRC,gBAAgB,EAChBC,MAAM,EACNC,WAAW,QACN,eAAe;AACtB,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,MAAM,EAAEC,aAAa,QAAQ,sBAAsB;AAC5D,OAAOC,MAAM,MAAM,uBAAuB;AAC1C,OAAOC,aAAa,MAAM,0BAA0B;AACpD,OAAOC,aAAa,MAAM,0BAA0B;AACpD,OAAOC,kBAAkB,MAAM,+BAA+B;AAC9D,SAASC,WAAW,EAAEC,UAAU,EAAEC,aAAa,EAAEC,aAAa,EAAEC,UAAU,EAAEC,mBAAmB,QAAQ,gBAAgB;AACvH,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvC,MAAMC,aAAa,GAAGC,IAAA,IAoBhB;EAAAC,EAAA;EAAA,IAAAC,gBAAA,EAAAC,iBAAA,EAAAC,iBAAA;EAAA,IApBiB;IACrBC,SAAS;IACTC,OAAO,GAAG,KAAK;IACfC,YAAY,GAAG,KAAK;IACpBC,0BAA0B;IAC1BC,kBAAkB;IAClBC,qBAAqB;IACrBC,aAAa;IACbC,wBAAwB;IACxBC,gBAAgB;IAChBC,iBAAiB;IACjBC,YAAY;IACZC,eAAe;IACfC,SAAS;IACTC,YAAY;IACZC,YAAY;IACZC,eAAe;IACfC,eAAe;IACfC,YAAY;IACZC;EACF,CAAC,GAAAvB,IAAA;EACC,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC+D,aAAa,EAAEC,gBAAgB,CAAC,GAAGhE,QAAQ,CAAC0C,SAAS,CAACuB,IAAI,CAAC;EAClE,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGnE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoE,WAAW,EAAEC,cAAc,CAAC,GAAGrE,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACsE,UAAU,EAAEC,aAAa,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EACpD,MAAMwE,QAAQ,GAAGvE,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMwE,eAAe,GAAGxE,MAAM,CAAC,IAAI,CAAC;;EAEpC;EACA,MAAMyE,QAAQ,GAAG7B,0BAA0B,GAAGA,0BAA0B,CAACH,SAAS,CAAC,GAAG,CAAC;;EAEvF;EACA,MAAMiC,eAAe,GAAG7B,kBAAkB,GAAGA,kBAAkB,CAACJ,SAAS,CAAC,GAAGvB,MAAM,CAACyD,WAAW;EAC/F,MAAMC,YAAY,GAAGzD,aAAa,CAACuD,eAAe,CAAC,IAAIvD,aAAa,CAACD,MAAM,CAACyD,WAAW,CAAC;;EAExF;EACA,MAAME,cAAc,GAAGpC,SAAS,CAACqC,WAAW,IAAIrC,SAAS,CAACqC,WAAW,CAACC,IAAI,CAAC,CAAC,CAACC,MAAM,GAAG,CAAC;;EAEvF;EACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5BpB,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;;EAED;EACA,MAAMqB,UAAU,GAAGA,CAAA,KAAM;IACvB;IACA,MAAMC,WAAW,GAAGrB,aAAa,CAACiB,IAAI,CAAC,CAAC;;IAExC;IACA,IAAII,WAAW,KAAK,EAAE,IAAIA,WAAW,KAAK1C,SAAS,CAACuB,IAAI,EAAE;MACxD,IAAId,iBAAiB,EAAE;QACrBA,iBAAiB,CAAC;UAAE,GAAGT,SAAS;UAAEuB,IAAI,EAAEmB;QAAY,CAAC,CAAC;MACxD;IACF,CAAC,MAAM;MACL;MACApB,gBAAgB,CAACtB,SAAS,CAACuB,IAAI,CAAC;IAClC;IAEAH,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;;EAED;EACA,MAAMuB,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,EAAE;MACrBJ,UAAU,CAAC,CAAC;IACd,CAAC,MAAM,IAAIG,CAAC,CAACC,GAAG,KAAK,QAAQ,EAAE;MAC7BvB,gBAAgB,CAACtB,SAAS,CAACuB,IAAI,CAAC;MAChCH,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAM0B,kBAAkB,GAAGA,CAAA,KAAM;IAC/BrB,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMsB,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAML,WAAW,GAAGhB,WAAW,CAACY,IAAI,CAAC,CAAC;IACtC,IAAII,WAAW,IAAI9B,SAAS,EAAE;MAC5B,MAAMoC,OAAO,GAAG;QACdzB,IAAI,EAAEmB,WAAW;QACjB1C,SAAS,EAAEA,SAAS,CAACiD,EAAE;QACvBC,MAAM,EAAEzE,MAAM,CAACyD,WAAW;QAC1BF,QAAQ,EAAE;MACZ,CAAC;MACDpB,SAAS,CAACoC,OAAO,CAAC;MAClBrB,cAAc,CAAC,EAAE,CAAC;IACpB;IACAF,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;;EAED;EACA,MAAM0B,qBAAqB,GAAIP,CAAC,IAAK;IACnC,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,EAAE;MACrBE,iBAAiB,CAAC,CAAC;IACrB,CAAC,MAAM,IAAIH,CAAC,CAACC,GAAG,KAAK,QAAQ,EAAE;MAC7BlB,cAAc,CAAC,EAAE,CAAC;MAClBF,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACAjE,SAAS,CAAC,MAAM;IACd,IAAI2D,SAAS,IAAIW,QAAQ,CAACsB,OAAO,EAAE;MACjCtB,QAAQ,CAACsB,OAAO,CAACC,KAAK,CAAC,CAAC;IAC1B;EACF,CAAC,EAAE,CAAClC,SAAS,CAAC,CAAC;;EAEf;EACA3D,SAAS,CAAC,MAAM;IACd,IAAIgE,YAAY,IAAIO,eAAe,CAACqB,OAAO,EAAE;MAC3CrB,eAAe,CAACqB,OAAO,CAACC,KAAK,CAAC,CAAC;IACjC;EACF,CAAC,EAAE,CAAC7B,YAAY,CAAC,CAAC;EAElB,oBACEjC,OAAA,CAAC5B,KAAK;IACJ2F,SAAS,EAAE,CAAE;IACbC,SAAS,EAAE5E,MAAM,CAAC6E,aAAc;IAChCC,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAExBpE,OAAA,CAAC9B,GAAG;MAAC8F,SAAS,EAAE5E,MAAM,CAACiF,eAAgB;MAAAD,QAAA,GACpCxC,SAAS,gBACR5B,OAAA,CAAC9B,GAAG;QAACgG,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,KAAK,EAAE;QAAO,CAAE;QAAAJ,QAAA,gBAChEpE,OAAA,CAAChB,OAAO;UAACyF,IAAI,EAAC,uBAAuB;UAACD,KAAK,EAAE,EAAG;UAACE,MAAM,EAAE,EAAG;UAACC,KAAK,EAAE1F,eAAgB;UAACiF,EAAE,EAAE;YAAEU,EAAE,EAAE;UAAE;QAAE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtGhF,OAAA,CAACvB,SAAS;UACR8D,QAAQ,EAAEA,QAAS;UACnB0C,KAAK,EAAEnD,aAAc;UACrBoD,QAAQ,EAAG7B,CAAC,IAAKtB,gBAAgB,CAACsB,CAAC,CAAC8B,MAAM,CAACF,KAAK,CAAE;UAClDG,SAAS,EAAEhC,cAAe;UAC1BiC,MAAM,EAAEnC,UAAW;UACnBoC,OAAO,EAAC,UAAU;UAClBC,SAAS;UACTC,SAAS;UACTtB,EAAE,EAAE;YACFuB,UAAU,EAAE,kCAAkC;YAC9CC,UAAU,EAAE,GAAG;YACfC,QAAQ,EAAE,QAAQ;YAClB,uBAAuB,EAAE;cACvBD,UAAU,EAAE,GAAG;cACfC,QAAQ,EAAE,QAAQ;cAClBF,UAAU,EAAE;YACd;UACF;QAAE;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFhF,OAAA,CAACtB,UAAU;UAACkH,IAAI,EAAC,OAAO;UAACC,OAAO,EAAE3C,UAAW;UAACgB,EAAE,EAAE;YAAE4B,EAAE,EAAE;UAAE,CAAE;UAAA1B,QAAA,eAC1DpE,OAAA,CAAChB,OAAO;YAACyF,IAAI,EAAC,wBAAwB;YAACD,KAAK,EAAE,EAAG;YAACE,MAAM,EAAE,EAAG;YAACC,KAAK,EAAC;UAAS;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,gBAENhF,OAAA,CAAC7B,UAAU;QACTmH,OAAO,EAAC,IAAI;QACZtB,SAAS,EAAE5E,MAAM,CAAC2G,cAAe;QACjC7B,EAAE,EAAE;UACFuB,UAAU,EAAE,kCAAkC;UAC9CnB,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpByB,MAAM,EAAE,SAAS;UACjB,SAAS,EAAE;YACT,cAAc,EAAE;cACdC,OAAO,EAAE;YACX;UACF;QACF,CAAE;QACFJ,OAAO,EAAE5C,eAAgB;QAAAmB,QAAA,gBAEzBpE,OAAA,CAAChB,OAAO;UAACyF,IAAI,EAAC,uBAAuB;UAACD,KAAK,EAAE,EAAG;UAACE,MAAM,EAAE,EAAG;UAACC,KAAK,EAAE1F;QAAgB;UAAA4F,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACtFlD,aAAa,eACd9B,OAAA,CAACrB,OAAO;UAACuH,KAAK,EAAC,qBAAqB;UAAA9B,QAAA,eAClCpE,OAAA,CAACtB,UAAU;YACTkH,IAAI,EAAC,OAAO;YACZ5B,SAAS,EAAC,WAAW;YACrBE,EAAE,EAAE;cACF4B,EAAE,EAAE,CAAC;cACLG,OAAO,EAAE,CAAC;cACVE,UAAU,EAAE,cAAc;cAC1BhC,OAAO,EAAE;YACX,CAAE;YAAAC,QAAA,eAEFpE,OAAA,CAAChB,OAAO;cAACyF,IAAI,EAAC,+BAA+B;cAACD,KAAK,EAAE,EAAG;cAACE,MAAM,EAAE,EAAG;cAACC,KAAK,EAAC;YAAM;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1E;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA,CACb,eAEDhF,OAAA,CAAC3B,IAAI;QACHoG,IAAI,eAAEzE,OAAA,CAAChB,OAAO;UAACyF,IAAI,EAAE7B,YAAY,CAAC6B,IAAK;UAACD,KAAK,EAAE,EAAG;UAACE,MAAM,EAAE;QAAG;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAClEoB,KAAK,EAAExD,YAAY,CAACwD,KAAM;QAC1BR,IAAI,EAAC,OAAO;QACZ1B,EAAE,EAAE;UACFmC,eAAe,EAAE,GAAGzD,YAAY,CAAC+B,KAAK,IAAI;UAC1CA,KAAK,EAAE/B,YAAY,CAAC+B,KAAK;UACzBe,UAAU,EAAE,GAAG;UACfY,YAAY,EAAE,KAAK;UACnBb,UAAU,EAAE;QACd;MAAE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGL,CAACtE,OAAO,IAAImC,cAAc,iBACzB7C,OAAA,CAAC9B,GAAG;MAACgG,EAAE,EAAE;QAAEqC,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAI,CAAE;MAAApC,QAAA,eAC1BpE,OAAA,CAAC9B,GAAG;QACFgG,EAAE,EAAE;UACFmC,eAAe,EAAE,SAAS;UAC1BI,CAAC,EAAE,GAAG;UACNH,YAAY,EAAE,KAAK;UACnBI,MAAM,EAAE;QACV,CAAE;QAAAtC,QAAA,eAEFpE,OAAA,CAAC7B,UAAU;UACTmH,OAAO,EAAC,OAAO;UACfpB,EAAE,EAAE;YACFS,KAAK,EAAE,MAAM;YACbc,UAAU,EAAE,kCAAkC;YAC9CkB,UAAU,EAAE,UAAU;YACtBC,UAAU,EAAE,GAAG;YACflB,UAAU,EAAE;UACd,CAAE;UAAAtB,QAAA,EAED3D,SAAS,CAACqC;QAAW;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDhF,OAAA,CAACxB,OAAO;MAAC0F,EAAE,EAAE;QAAE2C,EAAE,EAAE,CAAC;QAAEZ,OAAO,EAAE;MAAI;IAAE;MAAApB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAExChF,OAAA,CAAC9B,GAAG;MAACgG,EAAE,EAAE;QAAEqC,EAAE,EAAE;MAAE,CAAE;MAAAnC,QAAA,gBACjBpE,OAAA,CAAC9B,GAAG;QAACgG,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEwC,cAAc,EAAE,eAAe;UAAEvC,UAAU,EAAE,QAAQ;UAAEgC,EAAE,EAAE;QAAI,CAAE;QAAAnC,QAAA,gBAC3FpE,OAAA,CAAC7B,UAAU;UAACmH,OAAO,EAAC,OAAO;UAACpB,EAAE,EAAE;YAAEwB,UAAU,EAAE,GAAG;YAAED,UAAU,EAAE,kCAAkC;YAAEd,KAAK,EAAE;UAAO,CAAE;UAAAP,QAAA,EAAC;QAEpH;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbhF,OAAA,CAAC7B,UAAU;UAACmH,OAAO,EAAC,OAAO;UAACpB,EAAE,EAAE;YAAEwB,UAAU,EAAE,GAAG;YAAED,UAAU,EAAE,kCAAkC;YAAEd,KAAK,EAAE;UAAO,CAAE;UAAAP,QAAA,GAChH3B,QAAQ,EAAC,GACZ;QAAA;UAAAoC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACNhF,OAAA,CAAC1B,cAAc;QACbgH,OAAO,EAAC,aAAa;QACrBL,KAAK,EAAExC,QAAS;QAChByB,EAAE,EAAE;UACFQ,MAAM,EAAE,CAAC;UACT4B,YAAY,EAAE,CAAC;UACfD,eAAe,EAAE,SAAS;UAC1B,0BAA0B,EAAE;YAC1BA,eAAe,EAAEzD,YAAY,CAAC+B;UAChC;QACF;MAAE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENhF,OAAA,CAAC9B,GAAG;MAACgG,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEwC,cAAc,EAAE,eAAe;QAAEvC,UAAU,EAAE,QAAQ;QAAEgC,EAAE,EAAE;MAAE,CAAE;MAAAnC,QAAA,EACxF3D,SAAS,CAACsG,kBAAkB,iBAC3B/G,OAAA,CAAC9B,GAAG;QAACgG,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEyC,GAAG,EAAE;QAAE,CAAE;QAAA5C,QAAA,gBACzDpE,OAAA,CAAChB,OAAO;UAACyF,IAAI,EAAC,wBAAwB;UAACD,KAAK,EAAE,EAAG;UAACE,MAAM,EAAE,EAAG;UAACC,KAAK,EAAC;QAAM;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7EhF,OAAA,CAAC7B,UAAU;UAACmH,OAAO,EAAC,OAAO;UAACpB,EAAE,EAAE;YAAEuB,UAAU,EAAE,kCAAkC;YAAEd,KAAK,EAAE,MAAM;YAAEe,UAAU,EAAE;UAAI,CAAE;UAAAtB,QAAA,EAChH3D,SAAS,CAACsG;QAAkB;UAAAlC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNhF,OAAA,CAAC9B,GAAG;MAACgG,EAAE,EAAE;QAAEqC,EAAE,EAAE;MAAE,CAAE;MAAAnC,QAAA,gBACjBpE,OAAA,CAAC9B,GAAG;QACFgG,EAAE,EAAE;UACFI,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBuC,cAAc,EAAE,eAAe;UAC/BP,EAAE,EAAE;QACN,CAAE;QAAAnC,QAAA,gBAEFpE,OAAA,CAAC9B,GAAG;UAACgG,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEyC,GAAG,EAAE;UAAE,CAAE;UAAA5C,QAAA,gBACzDpE,OAAA,CAAChB,OAAO;YACNyF,IAAI,EAAC,4BAA4B;YACjCD,KAAK,EAAE,EAAG;YACVE,MAAM,EAAE,EAAG;YACXR,EAAE,EAAE;cAAES,KAAK,EAAE;YAAO;UAAE;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eACFhF,OAAA,CAAC7B,UAAU;YACTmH,OAAO,EAAC,OAAO;YACfpB,EAAE,EAAE;cACFwB,UAAU,EAAE,GAAG;cACff,KAAK,EAAE,MAAM;cACbc,UAAU,EAAE,kCAAkC;cAC9CE,QAAQ,EAAE;YACZ,CAAE;YAAAvB,QAAA,EACH;UAED;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGNhF,OAAA,CAACrB,OAAO;UAACuH,KAAK,EAAC,cAAc;UAAA9B,QAAA,eAC3BpE,OAAA,CAACtB,UAAU;YACTkH,IAAI,EAAC,OAAO;YACZC,OAAO,EAAEtC,kBAAmB;YAC5BW,EAAE,EAAE;cACFS,KAAK,EAAE1F,eAAe;cACtB,SAAS,EAAE;gBACToH,eAAe,EAAE,GAAGpH,eAAe;cACrC;YACF,CAAE;YAAAmF,QAAA,eAEFpE,OAAA,CAAChB,OAAO;cAACyF,IAAI,EAAC,2BAA2B;cAACD,KAAK,EAAE,EAAG;cAACE,MAAM,EAAE;YAAG;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,EAELtE,OAAO,gBACNV,OAAA,CAAC9B,GAAG;QAAC8F,SAAS,EAAE5E,MAAM,CAAC6H,QAAS;QAAA7C,QAAA,IAAA9D,gBAAA,GAC7BG,SAAS,CAACyG,KAAK,cAAA5G,gBAAA,uBAAfA,gBAAA,CAAiB6G,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC5CtH,OAAA,CAACuH,QAAQ;UAEPF,IAAI,EAAEA,IAAK;UACX1G,YAAY,EAAEA,YAAa;UAC3BD,OAAO,EAAE,IAAK;UACdI,qBAAqB,EAAEA,qBAAsB;UAC7CC,aAAa,EAAEA,aAAc;UAC7BC,wBAAwB,EAAEA,wBAAyB;UACnDC,gBAAgB,EAAEA,gBAAiB;UACnCE,YAAY,EAAEA,YAAa;UAC3BC,eAAe,EAAEA,eAAgB;UACjCE,YAAY,EAAEA,YAAa;UAC3BC,YAAY,EAAEA,YAAa;UAC3BC,eAAe,EAAEA,eAAgB;UACjCC,eAAe,EAAEA,eAAgB;UACjCC,YAAY,EAAEA,YAAa;UAC3BC,SAAS,EAAEA;QAAU,GAfhB2F,KAAK;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgBX,CACF,CAAC,EAED,EAAAzE,iBAAA,GAAAE,SAAS,CAACyG,KAAK,cAAA3G,iBAAA,uBAAfA,iBAAA,CAAiByC,MAAM,IAAG,CAAC,iBAC1BhD,OAAA,CAACzB,MAAM;UACL+G,OAAO,EAAC,MAAM;UACdM,IAAI,EAAC,OAAO;UACZ1B,EAAE,EAAE;YACFS,KAAK,EAAE1F,eAAe;YACtByG,UAAU,EAAE,GAAG;YACf8B,aAAa,EAAE,MAAM;YACrBf,CAAC,EAAE,CAAC;YACJD,EAAE,EAAE,CAAC;YACLf,UAAU,EAAE;UACd,CAAE;UAAArB,QAAA,GACH,IACG,EAAC3D,SAAS,CAACyG,KAAK,CAAClE,MAAM,GAAG,CAAC,EAAC,aAChC;QAAA;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,gBAENhF,OAAA,CAAC9B,GAAG;QAAC8F,SAAS,EAAE5E,MAAM,CAAC6H,QAAS;QAAA7C,QAAA,IAAA5D,iBAAA,GAC7BC,SAAS,CAACyG,KAAK,cAAA1G,iBAAA,uBAAfA,iBAAA,CAAiB4G,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAChCtH,OAAA,CAACuH,QAAQ;UAEPF,IAAI,EAAEA,IAAK;UACX1G,YAAY,EAAEA,YAAa;UAC3BD,OAAO,EAAE,KAAM;UACfI,qBAAqB,EAAEA,qBAAsB;UAC7CC,aAAa,EAAEA,aAAc;UAC7BC,wBAAwB,EAAEA,wBAAyB;UACnDC,gBAAgB,EAAEA,gBAAiB;UACnCE,YAAY,EAAEA,YAAa;UAC3BC,eAAe,EAAEA,eAAgB;UACjCE,YAAY,EAAEA,YAAa;UAC3BC,YAAY,EAAEA,YAAa;UAC3BC,eAAe,EAAEA,eAAgB;UACjCC,eAAe,EAAEA,eAAgB;UACjCC,YAAY,EAAEA,YAAa;UAC3BC,SAAS,EAAEA;QAAU,GAfhB2F,KAAK;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgBX,CACF,CAAC,EAGD/C,YAAY,iBACXjC,OAAA,CAAC9B,GAAG;UACF8F,SAAS,EAAE5E,MAAM,CAACqI,QAAS;UAC3BvD,EAAE,EAAE;YACFwD,QAAQ,EAAE,UAAU;YACpBlB,EAAE,EAAE,CAAC;YACLlC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpB8B,eAAe,EAAE,SAAS;YAC1BC,YAAY,EAAE,KAAK;YACnBnC,OAAO,EAAE,UAAU;YACnBuC,MAAM,EAAE;UACV,CAAE;UAAAtC,QAAA,gBAEFpE,OAAA,CAAC9B,GAAG;YACFgG,EAAE,EAAE;cACFM,KAAK,EAAE,EAAE;cACTE,MAAM,EAAE,EAAE;cACV4B,YAAY,EAAE,KAAK;cACnBD,eAAe,EAAE,SAAS;cAC1BzB,EAAE,EAAE,GAAG;cACP+C,UAAU,EAAE;YACd;UAAE;YAAA9C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEFhF,OAAA,CAACvB,SAAS;YACR8D,QAAQ,EAAEC,eAAgB;YAC1ByC,KAAK,EAAE9C,WAAY;YACnB+C,QAAQ,EAAG7B,CAAC,IAAKjB,cAAc,CAACiB,CAAC,CAAC8B,MAAM,CAACF,KAAK,CAAE;YAChDG,SAAS,EAAExB,qBAAsB;YACjCgE,WAAW,EAAC,wBAAwB;YACpCtC,OAAO,EAAC,UAAU;YAClBC,SAAS;YACTC,SAAS;YACTtB,EAAE,EAAE;cACFuB,UAAU,EAAE,kCAAkC;cAC9C,uBAAuB,EAAE;gBACvBE,QAAQ,EAAE,QAAQ;gBAClBF,UAAU,EAAE,kCAAkC;gBAC9CtB,OAAO,EAAE;cACX,CAAC;cACD,8BAA8B,EAAE;gBAC9B0D,iBAAiB,EAAE;cACrB;YACF;UAAE;YAAAhD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEFhF,OAAA,CAAC9B,GAAG;YAACgG,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEuB,EAAE,EAAE;YAAO,CAAE;YAAA1B,QAAA,gBAC7DpE,OAAA,CAACtB,UAAU;cAACkH,IAAI,EAAC,OAAO;cAACC,OAAO,EAAErC,iBAAkB;cAACU,EAAE,EAAE;gBAAE4B,EAAE,EAAE;cAAE,CAAE;cAAA1B,QAAA,eACjEpE,OAAA,CAAChB,OAAO;gBAACyF,IAAI,EAAC,wBAAwB;gBAACD,KAAK,EAAE,EAAG;gBAACE,MAAM,EAAE,EAAG;gBAACC,KAAK,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC,eAEbhF,OAAA,CAACtB,UAAU;cAACkH,IAAI,EAAC,OAAO;cAACC,OAAO,EAAEA,CAAA,KAAM3D,eAAe,CAAC,KAAK,CAAE;cAACgC,EAAE,EAAE;gBAAE4B,EAAE,EAAE;cAAI,CAAE;cAAA1B,QAAA,eAC9EpE,OAAA,CAAChB,OAAO;gBAACyF,IAAI,EAAC,wBAAwB;gBAACD,KAAK,EAAE,EAAG;gBAACE,MAAM,EAAE,EAAG;gBAACC,KAAK,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEZ,CAAC;AACD;AAAA3E,EAAA,CAvbMF,aAAa;AAAA2H,EAAA,GAAb3H,aAAa;AAwbnB,MAAMoH,QAAQ,GAAGQ,KAAA,IAgBX;EAAAC,GAAA;EAAA,IAAAC,oBAAA;EAAA,IAhBY;IAChBZ,IAAI;IACJ1G,YAAY,GAAG,KAAK;IACpBD,OAAO,GAAG,KAAK;IACfI,qBAAqB;IACrBC,aAAa;IACbC,wBAAwB;IACxBC,gBAAgB;IAChBE,YAAY;IACZC,eAAe;IACfE,YAAY;IACZC,YAAY;IACZC,eAAe;IACfC,eAAe;IACfC,YAAY;IACZC;EACF,CAAC,GAAAoG,KAAA;EACC,MAAM,CAACnG,SAAS,EAAEC,YAAY,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACmK,QAAQ,EAAEC,WAAW,CAAC,GAAGpK,QAAQ,CAACsJ,IAAI,CAACrF,IAAI,CAAC;EACnD,MAAMoG,YAAY,GAAGpK,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM,CAACqK,eAAe,EAAEC,kBAAkB,CAAC,GAAGvK,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACwK,cAAc,EAAEC,iBAAiB,CAAC,GAAGzK,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM0K,kBAAkB,GAAGzK,MAAM,CAAC,IAAI,CAAC;;EAEvC;EACA,MAAM,CAAC0K,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5K,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC6K,QAAQ,EAAEC,WAAW,CAAC,GAAG9K,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC+K,eAAe,EAAEC,kBAAkB,CAAC,GAAGhL,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAACiL,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGlL,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACmL,eAAe,EAAEC,kBAAkB,CAAC,GAAGpL,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAACqL,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGtL,QAAQ,CAAC,KAAK,CAAC;;EAE3E;EACA,MAAM,CAACuL,SAAS,EAAEC,YAAY,CAAC,GAAGxL,QAAQ,CAACsJ,IAAI,CAAC;;EAEhD;EACApJ,SAAS,CAAC,MAAM;IACdsL,YAAY,CAAClC,IAAI,CAAC;EACpB,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;EAEV,MAAMmC,kBAAkB,GAAInG,CAAC,IAAK;IAChCA,CAAC,CAACoG,eAAe,CAAC,CAAC;IACnBd,oBAAoB,CAAC,IAAI,CAAC;IAC1BI,kBAAkB,CAAC,IAAI,CAAC;IACxBvJ,WAAW,CAAC6H,IAAI,CAAC3D,EAAE,CAAC,CACjBgG,IAAI,CAACC,QAAQ,IAAI;MAAA,IAAAC,cAAA,EAAAC,eAAA;MAChBC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEJ,QAAQ,CAACK,IAAI,CAAC;MAC5C;MACA,IAAIC,YAAY,GAAG,EAAE;MACrB,IAAIC,KAAK,CAACC,OAAO,CAACR,QAAQ,CAACK,IAAI,CAAC,EAAE;QAChCC,YAAY,GAAGN,QAAQ,CAACK,IAAI;MAC9B,CAAC,MAAM,IAAI,CAAAJ,cAAA,GAAAD,QAAQ,CAACK,IAAI,cAAAJ,cAAA,eAAbA,cAAA,CAAeI,IAAI,IAAIE,KAAK,CAACC,OAAO,CAACR,QAAQ,CAACK,IAAI,CAACA,IAAI,CAAC,EAAE;QACnEC,YAAY,GAAGN,QAAQ,CAACK,IAAI,CAACA,IAAI;MACnC,CAAC,MAAM,IAAI,CAAAH,eAAA,GAAAF,QAAQ,CAACK,IAAI,cAAAH,eAAA,eAAbA,eAAA,CAAejB,QAAQ,IAAIsB,KAAK,CAACC,OAAO,CAACR,QAAQ,CAACK,IAAI,CAACpB,QAAQ,CAAC,EAAE;QAC3EqB,YAAY,GAAGN,QAAQ,CAACK,IAAI,CAACpB,QAAQ;MACvC;MACAC,WAAW,CAACoB,YAAY,CAAC;IAC3B,CAAC,CAAC,CACDG,KAAK,CAACC,KAAK,IAAI;MACdP,OAAO,CAACO,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDvK,KAAK,CAACuK,KAAK,CAAC,yBAAyB,CAAC;IACxC,CAAC,CAAC,CACDC,OAAO,CAAC,MAAM;MACbvB,kBAAkB,CAAC,KAAK,CAAC;IAC3B,CAAC,CAAC;EACN,CAAC;EAED,MAAMwB,gBAAgB,GAAG,MAAOC,OAAO,IAAK;IAC1C,IAAI;MAAA,IAAAC,eAAA;MACF,MAAMd,QAAQ,GAAG,MAAMlK,UAAU,CAAC4H,IAAI,CAAC3D,EAAE,EAAE8G,OAAO,CAAC;MACnDV,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEJ,QAAQ,CAAC;;MAE9C;MACA,IAAIe,UAAU;MACd,KAAAD,eAAA,GAAId,QAAQ,CAACK,IAAI,cAAAS,eAAA,eAAbA,eAAA,CAAeT,IAAI,EAAE;QACvBU,UAAU,GAAGf,QAAQ,CAACK,IAAI,CAACA,IAAI;MACjC,CAAC,MAAM;QACL;QACA,MAAMW,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC;QAClEL,UAAU,GAAG;UACXhH,EAAE,EAAEsH,IAAI,CAACC,GAAG,CAAC,CAAC;UAAE;UAChBT,OAAO,EAAEA,OAAO;UAChBU,IAAI,EAAE;YACJxH,EAAE,EAAEiH,WAAW,CAACjH,EAAE;YAClByH,UAAU,EAAER,WAAW,CAACQ,UAAU;YAClCC,SAAS,EAAET,WAAW,CAACS,SAAS;YAChCC,MAAM,EAAEV,WAAW,CAACU;UACtB,CAAC;UACDC,UAAU,EAAE,IAAIN,IAAI,CAAC,CAAC,CAACO,cAAc,CAAC,OAAO,EAAE;YAC7CC,IAAI,EAAE,SAAS;YACfC,MAAM,EAAE,SAAS;YACjBC,GAAG,EAAE,SAAS;YACdC,KAAK,EAAE;UACT,CAAC,CAAC,CAACC,OAAO,CAAC,GAAG,EAAE,EAAE;QACpB,CAAC;MACH;;MAEA;MACA/C,WAAW,CAACgD,YAAY,IAAI,CAAC,GAAGA,YAAY,EAAEnB,UAAU,CAAC,CAAC;;MAE1D;MACAoB,eAAe,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CvK,KAAK,CAACuK,KAAK,CAAC,uBAAuB,CAAC;IACtC;EACF,CAAC;EAED,MAAM0B,mBAAmB,GAAG,MAAAA,CAAOC,SAAS,EAAExB,OAAO,KAAK;IACxD,IAAI;MACF;MACA,MAAMyB,kBAAkB,GAAGrD,QAAQ,CAACsD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzI,EAAE,KAAKsI,SAAS,CAAC;MACjE,IAAIC,kBAAkB,EAAE;QACtB,MAAMG,eAAe,GAAGxD,QAAQ,CAACxB,GAAG,CAAC+E,CAAC,IACpCA,CAAC,CAACzI,EAAE,KAAKsI,SAAS,GAAG;UAAE,GAAGG,CAAC;UAAE3B,OAAO,EAAEA;QAAQ,CAAC,GAAG2B,CACpD,CAAC;QACDtD,WAAW,CAACuD,eAAe,CAAC;MAC9B;;MAEA;MACA,MAAMzC,QAAQ,GAAG,MAAMjK,aAAa,CAACsM,SAAS,EAAExB,OAAO,CAAC;MACxDV,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEJ,QAAQ,CAAC;MAEjD7J,KAAK,CAACuM,OAAO,CAAC,8BAA8B,CAAC;;MAE7C;MACAP,eAAe,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CvK,KAAK,CAACuK,KAAK,CAAC,0BAA0B,CAAC;MACvC;MACAyB,eAAe,CAAC,CAAC;IACnB;EACF,CAAC;EAED,MAAMQ,mBAAmB,GAAG,MAAON,SAAS,IAAK;IAC/C,IAAI;MACF;MACAnD,WAAW,CAACgD,YAAY,IAAIA,YAAY,CAACU,MAAM,CAACJ,CAAC,IAAIA,CAAC,CAACzI,EAAE,KAAKsI,SAAS,CAAC,CAAC;;MAEzE;MACA,MAAMrM,aAAa,CAACqM,SAAS,CAAC;MAC9BlC,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAE3CjK,KAAK,CAACuM,OAAO,CAAC,8BAA8B,CAAC;;MAE7C;MACAP,eAAe,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CvK,KAAK,CAACuK,KAAK,CAAC,0BAA0B,CAAC;MACvC;MACAyB,eAAe,CAAC,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMA,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MAAA,IAAAU,eAAA,EAAAC,eAAA;MACF,MAAM9C,QAAQ,GAAG,MAAMnK,WAAW,CAAC6H,IAAI,CAAC3D,EAAE,CAAC;MAC3CoG,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEJ,QAAQ,CAACK,IAAI,CAAC;;MAEjD;MACA,IAAIC,YAAY,GAAG,EAAE;MACrB,IAAIC,KAAK,CAACC,OAAO,CAACR,QAAQ,CAACK,IAAI,CAAC,EAAE;QAChCC,YAAY,GAAGN,QAAQ,CAACK,IAAI;MAC9B,CAAC,MAAM,IAAI,CAAAwC,eAAA,GAAA7C,QAAQ,CAACK,IAAI,cAAAwC,eAAA,eAAbA,eAAA,CAAexC,IAAI,IAAIE,KAAK,CAACC,OAAO,CAACR,QAAQ,CAACK,IAAI,CAACA,IAAI,CAAC,EAAE;QACnEC,YAAY,GAAGN,QAAQ,CAACK,IAAI,CAACA,IAAI;MACnC,CAAC,MAAM,IAAI,CAAAyC,eAAA,GAAA9C,QAAQ,CAACK,IAAI,cAAAyC,eAAA,eAAbA,eAAA,CAAe7D,QAAQ,IAAIsB,KAAK,CAACC,OAAO,CAACR,QAAQ,CAACK,IAAI,CAACpB,QAAQ,CAAC,EAAE;QAC3EqB,YAAY,GAAGN,QAAQ,CAACK,IAAI,CAACpB,QAAQ;MACvC;MAEAC,WAAW,CAACoB,YAAY,CAAC;IAC3B,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF,CAAC;;EAED;EACA,MAAMqC,YAAY,GAAG5L,qBAAqB,GAAGA,qBAAqB,CAACuG,IAAI,CAAC,GAAG,CAAC;;EAE5E;EACA,MAAMsF,UAAU,GAAG5L,aAAa,GAAGA,aAAa,CAACsG,IAAI,CAAC,GAAIA,IAAI,CAAC1D,MAAM,IAAIzE,MAAM,CAACyD,WAAY;EAC5F,MAAMC,YAAY,GAAGzD,aAAa,CAACwN,UAAU,CAAC,IAAIxN,aAAa,CAACD,MAAM,CAACyD,WAAW,CAAC;;EAEnF;EACA,MAAMiK,WAAW,GAAGvF,IAAI,CAACwF,QAAQ,IAAIxF,IAAI,CAACwF,QAAQ,CAAC7J,MAAM,GAAG,CAAC;;EAE7D;EACA,MAAM8J,WAAW,GAAGH,UAAU,KAAKzN,MAAM,CAAC6N,SAAS;;EAEnD;EACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChCnL,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;;EAED;EACA,MAAMoL,cAAc,GAAGA,CAAA,KAAM;IAC3B;IACA,MAAM9J,WAAW,GAAG+E,QAAQ,CAACnF,IAAI,CAAC,CAAC;;IAEnC;IACA,IAAII,WAAW,KAAK,EAAE,IAAIA,WAAW,KAAKkE,IAAI,CAACrF,IAAI,EAAE;MACnD,IAAIb,YAAY,EAAE;QAChBA,YAAY,CAAC;UAAE,GAAGkG,IAAI;UAAErF,IAAI,EAAEmB;QAAY,CAAC,CAAC;MAC9C;IACF,CAAC,MAAM;MACL;MACAgF,WAAW,CAACd,IAAI,CAACrF,IAAI,CAAC;IACxB;IAEAH,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;;EAED;EACA,MAAMqL,kBAAkB,GAAI7J,CAAC,IAAK;IAChC,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,EAAE;MACrB2J,cAAc,CAAC,CAAC;IAClB,CAAC,MAAM,IAAI5J,CAAC,CAACC,GAAG,KAAK,QAAQ,EAAE;MAC7B6E,WAAW,CAACd,IAAI,CAACrF,IAAI,CAAC;MACtBH,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMsL,qBAAqB,GAAGA,CAAA,KAAM;IAClC7E,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAM8E,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI7L,YAAY,EAAE;MAChBA,YAAY,CAAC8F,IAAI,CAAC;IACpB;EACF,CAAC;;EAED;EACA,MAAMgG,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMlK,WAAW,GAAGoF,cAAc,CAACxF,IAAI,CAAC,CAAC;IACzC,IAAII,WAAW,IAAI7B,YAAY,EAAE;MAC/B,MAAMgM,UAAU,GAAG;QACjBtL,IAAI,EAAEmB,WAAW;QACjBkE,IAAI,EAAEA,IAAI,CAACkG,IAAI;QACf5J,MAAM,EAAEzE,MAAM,CAACyD,WAAW;QAC1BF,QAAQ,EAAE;MACZ,CAAC;MACDnB,YAAY,CAACgM,UAAU,CAAC;MACxB9E,iBAAiB,CAAC,EAAE,CAAC;IACvB;IACAF,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMkF,wBAAwB,GAAInK,CAAC,IAAK;IACtC,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,EAAE;MACrB+J,oBAAoB,CAAC,CAAC;IACxB,CAAC,MAAM,IAAIhK,CAAC,CAACC,GAAG,KAAK,QAAQ,EAAE;MAC7BkF,iBAAiB,CAAC,EAAE,CAAC;MACrBF,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACArK,SAAS,CAAC,MAAM;IACd,IAAI2D,SAAS,IAAIwG,YAAY,CAACvE,OAAO,EAAE;MACrCuE,YAAY,CAACvE,OAAO,CAACC,KAAK,CAAC,CAAC;IAC9B;EACF,CAAC,EAAE,CAAClC,SAAS,CAAC,CAAC;;EAEf;EACA3D,SAAS,CAAC,MAAM;IACd,IAAIoK,eAAe,IAAII,kBAAkB,CAAC5E,OAAO,EAAE;MACjD4E,kBAAkB,CAAC5E,OAAO,CAACC,KAAK,CAAC,CAAC;IACpC;EACF,CAAC,EAAE,CAACuE,eAAe,CAAC,CAAC;;EAErB;EACA,MAAMoF,WAAW,GAAGnE,SAAS,CAACoE,UAAU,IAAIpE,SAAS,CAACqE,QAAQ;EAE9D,MAAMC,uBAAuB,GAAIvK,CAAC,IAAK;IACrCA,CAAC,CAACoG,eAAe,CAAC,CAAC;IACnBR,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAM4E,mBAAmB,GAAG,MAAOC,WAAW,IAAK;IACjD3E,kBAAkB,CAAC,IAAI,CAAC;IACxB,IAAI;MACF,MAAM4E,YAAY,GAAG;QACnB,GAAGzE,SAAS;QACZoE,UAAU,EAAEI,WAAW,CAACJ,UAAU;QAClCC,QAAQ,EAAEG,WAAW,CAACH,QAAQ;QAC9BlL,QAAQ,EAAE6G,SAAS,CAAC7G,QAAQ,IAAI,CAAC;QACjCkB,MAAM,EAAE2F,SAAS,CAAC3F,MAAM,IAAI;MAC9B,CAAC;;MAED;MACA4F,YAAY,CAACwE,YAAY,CAAC;;MAE1B;MACA,MAAMpE,QAAQ,GAAG,MAAM/J,UAAU,CAACmO,YAAY,CAAC;;MAE/C;MACA,IAAIpE,QAAQ,IAAIA,QAAQ,CAACK,IAAI,EAAE;QAC7BT,YAAY,CAACI,QAAQ,CAACK,IAAI,CAAC;MAC7B;;MAEA;MACA,IAAI7I,YAAY,EAAE;QAChBA,YAAY,CAAC4M,YAAY,CAAC;MAC5B;MAEAjO,KAAK,CAACuM,OAAO,CAAC,+BAA+B,CAAC;IAChD,CAAC,CAAC,OAAOhC,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDvK,KAAK,CAACuK,KAAK,CAAC,2BAA2B,CAAC;;MAExC;MACAd,YAAY,CAAClC,IAAI,CAAC;IACpB,CAAC,SAAS;MACR8B,kBAAkB,CAAC,KAAK,CAAC;MACzBF,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,MAAM+E,mBAAmB,GAAG,MAAOC,eAAe,IAAK;IACrD,IAAI;MACF;MACA,MAAMC,UAAU,GAAGD,eAAe,CAAC7G,GAAG,CAAC1D,EAAE,IAAIyK,MAAM,CAACzK,EAAE,CAAC,CAAC;MACxD;MACA,MAAMiG,QAAQ,GAAG,MAAM9J,mBAAmB,CAACyJ,SAAS,CAACiE,IAAI,EAAEW,UAAU,CAAC;MACtE;MACA,MAAMJ,WAAW,GAAG;QAClB,GAAGxE,SAAS;QACZ8E,SAAS,EAAEzE,QAAQ,CAACyE,SAAS,IAAIF,UAAU,CAAC9G,GAAG,CAAC1D,EAAE,KAAK;UACrDA,EAAE,EAAEA,EAAE;UACNyH,UAAU,EAAE,EAAE;UACdC,SAAS,EAAE,EAAE;UACbiD,KAAK,EAAE;QACT,CAAC,CAAC;MACJ,CAAC;;MAED;MACA9E,YAAY,CAACuE,WAAW,CAAC;MACzB,IAAI3M,YAAY,EAAE;QAChBA,YAAY,CAAC2M,WAAW,CAAC;MAC3B;MAEAzE,yBAAyB,CAAC,KAAK,CAAC;MAChCvJ,KAAK,CAACuM,OAAO,CAAC,+BAA+B,CAAC;IAChD,CAAC,CAAC,OAAOhC,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDvK,KAAK,CAACuK,KAAK,CAAC,0BAA0B,CAAC;IACzC;EACF,CAAC;EAED,oBACErK,OAAA,CAAAE,SAAA;IAAAkE,QAAA,gBACEpE,OAAA,CAAC9B,GAAG;MAAC8F,SAAS,EAAE5E,MAAM,CAACkP,iBAAkB;MAAAlK,QAAA,gBACvCpE,OAAA,CAAC9B,GAAG;QACF8F,SAAS,EAAE5E,MAAM,CAACqI,QAAS;QAC3BvD,EAAE,EAAE;UAAEwD,QAAQ,EAAE;QAAW,CAAE;QAAAtD,QAAA,gBAC7BpE,OAAA,CAAC9B,GAAG;UACFgG,EAAE,EAAE;YACFM,KAAK,EAAE,EAAE;YACTE,MAAM,EAAE,EAAE;YACV4B,YAAY,EAAE,KAAK;YACnBD,eAAe,EAAEzD,YAAY,CAAC+B,KAAK;YACnCC,EAAE,EAAE,GAAG;YACP+C,UAAU,EAAE;UACd;QAAE;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAEDpD,SAAS,gBACR5B,OAAA,CAAC9B,GAAG;UAACgG,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEgK,QAAQ,EAAE;UAAE,CAAE;UAAAnK,QAAA,gBAC9DpE,OAAA,CAACvB,SAAS;YACR8D,QAAQ,EAAE6F,YAAa;YACvBnD,KAAK,EAAEiD,QAAS;YAChBhD,QAAQ,EAAG7B,CAAC,IAAK8E,WAAW,CAAC9E,CAAC,CAAC8B,MAAM,CAACF,KAAK,CAAE;YAC7CG,SAAS,EAAE8H,kBAAmB;YAC9B7H,MAAM,EAAE4H,cAAe;YACvB3H,OAAO,EAAC,UAAU;YAClBC,SAAS;YACTC,SAAS;YACTtB,EAAE,EAAE;cACFuB,UAAU,EAAE,kCAAkC;cAC9C,uBAAuB,EAAE;gBACvBE,QAAQ,EAAE,QAAQ;gBAClBF,UAAU,EAAE;cACd;YACF;UAAE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACFhF,OAAA,CAACtB,UAAU;YAACkH,IAAI,EAAC,OAAO;YAACC,OAAO,EAAEoH,cAAe;YAAC/I,EAAE,EAAE;cAAE4B,EAAE,EAAE;YAAE,CAAE;YAAA1B,QAAA,eAC9DpE,OAAA,CAAChB,OAAO;cAACyF,IAAI,EAAC,wBAAwB;cAACD,KAAK,EAAE,EAAG;cAACE,MAAM,EAAE,EAAG;cAACC,KAAK,EAAC;YAAS;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,gBAENhF,OAAA,CAAC9B,GAAG;UAACgG,EAAE,EAAE;YAAEqK,QAAQ,EAAE,CAAC;YAAEjK,OAAO,EAAE,MAAM;YAAEkK,aAAa,EAAE,QAAQ;YAAExH,GAAG,EAAE;UAAI,CAAE;UAAA5C,QAAA,gBAC3EpE,OAAA,CAAC9B,GAAG;YAACgG,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEuC,cAAc,EAAE,eAAe;cAAEtC,KAAK,EAAE;YAAO,CAAE;YAAAJ,QAAA,gBACjGpE,OAAA,CAAC9B,GAAG;cAACgG,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEyC,GAAG,EAAE,CAAC;gBAAEuH,QAAQ,EAAE;cAAE,CAAE;cAAAnK,QAAA,eACtEpE,OAAA,CAAC7B,UAAU;gBACTmH,OAAO,EAAC,OAAO;gBACftB,SAAS,EAAE5E,MAAM,CAAC8I,QAAS;gBAC3BhE,EAAE,EAAE;kBACFuB,UAAU,EAAE,kCAAkC;kBAC9CO,MAAM,EAAE,SAAS;kBACjB1B,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBI,KAAK,EAAEmI,WAAW,GAAG,SAAS,GAAG,SAAS;kBAC1CpH,UAAU,EAAEoH,WAAW,GAAG,GAAG,GAAG,GAAG;kBACnCyB,QAAQ,EAAE,CAAC;kBACX,SAAS,EAAE;oBACT,mBAAmB,EAAE;sBACnBtI,OAAO,EAAE;oBACX;kBACF;gBACF,CAAE;gBACFJ,OAAO,EAAEmH,mBAAoB;gBAAA5I,QAAA,GAC5B8D,QAAQ,eACTlI,OAAA,CAACrB,OAAO;kBAACuH,KAAK,EAAC,gBAAgB;kBAAA9B,QAAA,eAC7BpE,OAAA,CAACtB,UAAU;oBACTkH,IAAI,EAAC,OAAO;oBACZ5B,SAAS,EAAC,gBAAgB;oBAC1BE,EAAE,EAAE;sBACF4B,EAAE,EAAE,GAAG;sBACPG,OAAO,EAAE,CAAC;sBACVE,UAAU,EAAE,cAAc;sBAC1BhC,OAAO,EAAE;oBACX,CAAE;oBAAAC,QAAA,eAEFpE,OAAA,CAAChB,OAAO;sBAACyF,IAAI,EAAC,+BAA+B;sBAACD,KAAK,EAAE,EAAG;sBAACE,MAAM,EAAE,EAAG;sBAACC,KAAK,EAAC;oBAAM;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAENhF,OAAA,CAAC9B,GAAG;cAACgG,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEyC,GAAG,EAAE;cAAE,CAAE;cAAA5C,QAAA,GAExDkF,SAAS,CAAC8E,SAAS,IAAI9E,SAAS,CAAC8E,SAAS,CAACpL,MAAM,GAAG,CAAC,iBACpDhD,OAAA,CAACjB,WAAW;gBACV0P,GAAG,EAAE,CAAE;gBACPvK,EAAE,EAAE;kBACF,mBAAmB,EAAE;oBACnBM,KAAK,EAAE,EAAE;oBACTE,MAAM,EAAE,EAAE;oBACViB,QAAQ,EAAE,SAAS;oBACnBe,MAAM,EAAE;kBACV;gBACF,CAAE;gBAAAtC,QAAA,EAEDkF,SAAS,CAAC8E,SAAS,CAAChH,GAAG,CAAC,CAACsH,QAAQ,EAAEpH,KAAK,kBACvCtH,OAAA,CAACrB,OAAO;kBAENuH,KAAK,EAAEwI,QAAQ,CAACvD,UAAU,IAAIuD,QAAQ,CAACtD,SAAS,GAC5C,GAAGsD,QAAQ,CAACvD,UAAU,IAAIuD,QAAQ,CAACtD,SAAS,EAAE,GAC9CsD,QAAQ,CAACL,KAAM;kBACnBM,KAAK;kBAAAvK,QAAA,eAELpE,OAAA,CAAClB,MAAM;oBACL8P,GAAG,EAAEF,QAAQ,CAACrD,MAAO;oBACrBwD,GAAG,EAAEH,QAAQ,CAACvD,UAAU,IAAIuD,QAAQ,CAACL,KAAM;oBAC3CnK,EAAE,EAAE;sBACF4K,OAAO,EAAE7P;oBACX,CAAE;oBAAAmF,QAAA,EAEDsK,QAAQ,CAACvD,UAAU,GAChBuD,QAAQ,CAACvD,UAAU,CAAC4D,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAC3CN,QAAQ,CAACL,KAAK,GACZK,QAAQ,CAACL,KAAK,CAACU,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GACtC;kBAAE;oBAAAnK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBAAC,GAlBJ0J,QAAQ,CAAChL,EAAE;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAmBT,CACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CACd,eAGDhF,OAAA,CAAC9B,GAAG;gBACFgG,EAAE,EAAE;kBACFI,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBmC,MAAM,EAAE,gBAAgB;kBACxBJ,YAAY,EAAE,KAAK;kBACnBnC,OAAO,EAAE,SAAS;kBAClB8K,UAAU,EAAE;gBACd,CAAE;gBAAA7K,QAAA,gBAGFpE,OAAA,CAACrB,OAAO;kBAACuH,KAAK,EAAEuH,WAAW,GAAG,eAAe,GAAG,cAAe;kBAAArJ,QAAA,eAC7DpE,OAAA,CAACtB,UAAU;oBACTkH,IAAI,EAAC,OAAO;oBACZC,OAAO,EAAE+H,uBAAwB;oBACjC1J,EAAE,EAAE;sBACFS,KAAK,EAAE8I,WAAW,GAAGxO,eAAe,GAAG,MAAM;sBAC7C,SAAS,EAAE;wBACT0F,KAAK,EAAE1F,eAAe;wBACtB6P,OAAO,EAAE;sBACX;oBACF,CAAE;oBAAA1K,QAAA,eAEFpE,OAAA,CAAChB,OAAO;sBACNyF,IAAI,EAAEgJ,WAAW,GAAG,iCAAiC,GAAG,kCAAmC;sBAC3FjJ,KAAK,EAAE,EAAG;sBACVE,MAAM,EAAE;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAGVhF,OAAA,CAACrB,OAAO;kBAACuH,KAAK,EAAC,UAAU;kBAAA9B,QAAA,eACvBpE,OAAA,CAACtB,UAAU;oBACTkH,IAAI,EAAC,OAAO;oBACZC,OAAO,EAAE2D,kBAAmB;oBAC5B0F,QAAQ,EAAEpG,eAAgB;oBAC1B5E,EAAE,EAAE;sBACFS,KAAK,EAAE1F,eAAe;sBACtB,SAAS,EAAE;wBACT6P,OAAO,EAAE;sBACX;oBACF,CAAE;oBAAA1K,QAAA,EAED0E,eAAe,gBACd9I,OAAA,CAACnB,gBAAgB;sBAAC+G,IAAI,EAAE,EAAG;sBAAC1B,EAAE,EAAE;wBAAES,KAAK,EAAE1F;sBAAgB;oBAAE;sBAAA4F,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAE9DhF,OAAA,CAAChB,OAAO;sBAACyF,IAAI,EAAC,kCAAkC;sBAACD,KAAK,EAAE,EAAG;sBAACE,MAAM,EAAE;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAC1E;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAGVhF,OAAA,CAACrB,OAAO;kBAACuH,KAAK,EAAC,gBAAgB;kBAAA9B,QAAA,eAC7BpE,OAAA,CAACtB,UAAU;oBACTkH,IAAI,EAAC,OAAO;oBACZC,OAAO,EAAEA,CAAA,KAAMwD,yBAAyB,CAAC,IAAI,CAAE;oBAC/CnF,EAAE,EAAE;sBACFS,KAAK,EAAE,EAAAsD,oBAAA,GAAAqB,SAAS,CAAC8E,SAAS,cAAAnG,oBAAA,uBAAnBA,oBAAA,CAAqBjF,MAAM,IAAG,CAAC,GAAG/D,eAAe,GAAG,MAAM;sBACjE,SAAS,EAAE;wBACT0F,KAAK,EAAE1F,eAAe;wBACtB6P,OAAO,EAAE;sBACX;oBACF,CAAE;oBAAA1K,QAAA,eAEFpE,OAAA,CAAChB,OAAO;sBACNyF,IAAI,EAAC,2BAA2B;sBAChCD,KAAK,EAAE,EAAG;sBACVE,MAAM,EAAE;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAGVhF,OAAA,CAACrB,OAAO;kBAACuH,KAAK,EAAC,aAAa;kBAAA9B,QAAA,eAC1BpE,OAAA,CAACtB,UAAU;oBACTkH,IAAI,EAAC,OAAO;oBACZC,OAAO,EAAEsH,qBAAsB;oBAC/BjJ,EAAE,EAAE;sBACFS,KAAK,EAAE1F,eAAe;sBACtB,SAAS,EAAE;wBACT6P,OAAO,EAAE;sBACX;oBACF,CAAE;oBAAA1K,QAAA,eAEFpE,OAAA,CAAChB,OAAO;sBAACyF,IAAI,EAAC,2BAA2B;sBAACD,KAAK,EAAE,EAAG;sBAACE,MAAM,EAAE;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAGVhF,OAAA,CAACrB,OAAO;kBAACuH,KAAK,EAAC,WAAW;kBAAA9B,QAAA,eACxBpE,OAAA,CAACtB,UAAU;oBACTkH,IAAI,EAAC,OAAO;oBACZC,OAAO,EAAEmH,mBAAoB;oBAC7B9I,EAAE,EAAE;sBACFS,KAAK,EAAE,MAAM;sBACb,SAAS,EAAE;wBACTmK,OAAO,EAAE;sBACX;oBACF,CAAE;oBAAA1K,QAAA,eAEFpE,OAAA,CAAChB,OAAO;sBAACyF,IAAI,EAAC,eAAe;sBAACD,KAAK,EAAE,EAAG;sBAACE,MAAM,EAAE;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAGVhF,OAAA,CAACrB,OAAO;kBAACuH,KAAK,EAAC,aAAa;kBAAA9B,QAAA,eAC1BpE,OAAA,CAACtB,UAAU;oBACTkH,IAAI,EAAC,OAAO;oBACZC,OAAO,EAAEuH,qBAAsB;oBAC/BlJ,EAAE,EAAE;sBACFS,KAAK,EAAE,SAAS;sBAChB,SAAS,EAAE;wBACTmK,OAAO,EAAE;sBACX;oBACF,CAAE;oBAAA1K,QAAA,eAEFpE,OAAA,CAAChB,OAAO;sBAACyF,IAAI,EAAC,iCAAiC;sBAACD,KAAK,EAAE,EAAG;sBAACE,MAAM,EAAE;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,eAGNhF,OAAA,CAAC9B,GAAG;gBACFgG,EAAE,EAAE;kBACFI,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBuK,OAAO,EAAElM,YAAY,CAAC+B,KAAK,GAAG,IAAI;kBAClCwK,EAAE,EAAE,CAAC;kBACLC,EAAE,EAAE,GAAG;kBACP9I,YAAY,EAAE;gBAChB,CAAE;gBAAAlC,QAAA,eAEFpE,OAAA,CAAC7B,UAAU;kBACTmH,OAAO,EAAC,SAAS;kBACjBpB,EAAE,EAAE;oBACFwB,UAAU,EAAE,GAAG;oBACff,KAAK,EAAE/B,YAAY,CAAC+B,KAAK;oBACzBc,UAAU,EAAE;kBACd,CAAE;kBAAArB,QAAA,GAEDsI,YAAY,EAAC,GAChB;gBAAA;kBAAA7H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLyI,WAAW,iBACVzN,OAAA,CAAC9B,GAAG;YACFgG,EAAE,EAAE;cACFI,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpB8B,eAAe,EAAE,GAAGpH,eAAe,IAAI;cACvCqH,YAAY,EAAE,KAAK;cACnB8I,EAAE,EAAE,GAAG;cACPD,EAAE,EAAE,IAAI;cACR3K,KAAK,EAAE;YACT,CAAE;YAAAJ,QAAA,gBAEFpE,OAAA,CAAChB,OAAO;cACNyF,IAAI,EAAC,iCAAiC;cACtCD,KAAK,EAAE,EAAG;cACVE,MAAM,EAAE,EAAG;cACXC,KAAK,EAAE1F;YAAgB;cAAA4F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5BhF,OAAA,CAAC7B,UAAU;cACTmH,OAAO,EAAC,SAAS;cACjBpB,EAAE,EAAE;gBACFuB,UAAU,EAAE,kCAAkC;gBAC9CE,QAAQ,EAAE,SAAS;gBACnBhB,KAAK,EAAE,MAAM;gBACbe,UAAU,EAAE,GAAG;gBACfkB,UAAU,EAAE,CAAC;gBACbd,EAAE,EAAE;cACN,CAAE;cAAA1B,QAAA,GACDiL,UAAU,CAAC/F,SAAS,CAACoE,UAAU,CAAC,EAAC,KAAG,EAAC2B,UAAU,CAAC/F,SAAS,CAACqE,QAAQ,CAAC;YAAA;cAAA9I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENhF,OAAA,CAAC9B,GAAG;QACFgG,EAAE,EAAE;UACFoL,EAAE,EAAE,CAAC;UACLC,EAAE,EAAE,CAAC;UACLC,EAAE,EAAE,GAAG;UACPC,EAAE,EAAE,GAAG;UACPC,UAAU,EAAE,cAAc9M,YAAY,CAAC+B,KAAK,EAAE;UAC9CmB,EAAE,EAAE,GAAG;UACPU,EAAE,EAAE,GAAG;UACPlC,OAAO,EAAE,CAACsI,WAAW,IAAIvE,eAAe,KAAK1H,YAAY,GAAG,OAAO,GAAG;QACxE,CAAE;QAAAyD,QAAA,GAEDiD,IAAI,CAACwF,QAAQ,IAAIxF,IAAI,CAACwF,QAAQ,CAACzF,GAAG,CAAC,CAACuI,OAAO,EAAErI,KAAK,kBACjDtH,OAAA,CAAC4P,WAAW;UAEVD,OAAO,EAAEA,OAAQ;UACjBrI,KAAK,EAAEA,KAAM;UACbuI,aAAa,EAAExI,IAAI,CAACwF,QAAQ,CAAC7J,MAAO;UACpC8M,QAAQ,EAAEzI,IAAI,CAACkG,IAAK;UACpBvM,wBAAwB,EAAEA,wBAAyB;UACnDC,gBAAgB,EAAEA,gBAAiB;UACnCG,eAAe,EAAEA,eAAgB;UACjCI,eAAe,EAAEA,eAAgB;UACjCC,eAAe,EAAEA,eAAgB;UACjCC,YAAY,EAAEA,YAAa;UAC3BC,SAAS,EAAEA;QAAU,GAXhB2F,KAAK;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAYX,CACF,CAAC,EAGDqD,eAAe,iBACdrI,OAAA,CAAC9B,GAAG;UACFgG,EAAE,EAAE;YACFI,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpB6K,EAAE,EAAE,GAAG;YACPW,YAAY,EAAE,oBAAoB;YAClC1J,eAAe,EAAE,SAAS;YAC1BC,YAAY,EAAE,KAAK;YACnB6I,EAAE,EAAE;UACN,CAAE;UAAA/K,QAAA,gBAEFpE,OAAA,CAACpB,QAAQ;YACPsQ,QAAQ;YACRtJ,IAAI,EAAC,OAAO;YACZ1B,EAAE,EAAE;cACFuC,CAAC,EAAE,GAAG;cACN7B,EAAE,EAAE,GAAG;cACPD,KAAK,EAAE;YACT,CAAE;YACFF,IAAI,eAAEzE,OAAA,CAAChB,OAAO;cAACyF,IAAI,EAAC,0CAA0C;cAACD,KAAK,EAAE,EAAG;cAACE,MAAM,EAAE;YAAG;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1F,CAAC,eAEFhF,OAAA,CAACvB,SAAS;YACR8D,QAAQ,EAAEkG,kBAAmB;YAC7BxD,KAAK,EAAEsD,cAAe;YACtBrD,QAAQ,EAAG7B,CAAC,IAAKmF,iBAAiB,CAACnF,CAAC,CAAC8B,MAAM,CAACF,KAAK,CAAE;YACnDG,SAAS,EAAEoI,wBAAyB;YACpC5F,WAAW,EAAC,2BAA2B;YACvCtC,OAAO,EAAC,UAAU;YAClBC,SAAS;YACTC,SAAS;YACTtB,EAAE,EAAE;cACFuB,UAAU,EAAE,kCAAkC;cAC9C,uBAAuB,EAAE;gBACvBE,QAAQ,EAAE,SAAS;gBACnBF,UAAU,EAAE,kCAAkC;gBAC9CtB,OAAO,EAAE;cACX,CAAC;cACD,8BAA8B,EAAE;gBAC9B0D,iBAAiB,EAAE;cACrB;YACF;UAAE;YAAAhD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEFhF,OAAA,CAAC9B,GAAG;YAACgG,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEwB,EAAE,EAAE;YAAE,CAAE;YAAA1B,QAAA,gBAClCpE,OAAA,CAACtB,UAAU;cAACkH,IAAI,EAAC,OAAO;cAACC,OAAO,EAAEwH,oBAAqB;cAACnJ,EAAE,EAAE;gBAAEuC,CAAC,EAAE;cAAI,CAAE;cAAArC,QAAA,eACrEpE,OAAA,CAAChB,OAAO;gBAACyF,IAAI,EAAC,wBAAwB;gBAACD,KAAK,EAAE,EAAG;gBAACE,MAAM,EAAE,EAAG;gBAACC,KAAK,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC,eAEbhF,OAAA,CAACtB,UAAU;cAACkH,IAAI,EAAC,OAAO;cAACC,OAAO,EAAEA,CAAA,KAAMyC,kBAAkB,CAAC,KAAK,CAAE;cAACpE,EAAE,EAAE;gBAAEuC,CAAC,EAAE,GAAG;gBAAEX,EAAE,EAAE;cAAI,CAAE;cAAA1B,QAAA,eACzFpE,OAAA,CAAChB,OAAO;gBAACyF,IAAI,EAAC,wBAAwB;gBAACD,KAAK,EAAE,EAAG;gBAACE,MAAM,EAAE,EAAG;gBAACC,KAAK,EAAC;cAAS;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNhF,OAAA,CAACX,aAAa;MACZ2Q,IAAI,EAAEtH,iBAAkB;MACxBuH,OAAO,EAAEA,CAAA,KAAMtH,oBAAoB,CAAC,KAAK,CAAE;MAC3CC,QAAQ,EAAEA,QAAS;MACnBsH,YAAY,EAAE3F,gBAAiB;MAC/B4F,eAAe,EAAEpE,mBAAoB;MACrCqE,eAAe,EAAE9D,mBAAoB;MACrC+D,OAAO,EAAEvH,eAAgB;MACzBwH,UAAU,EAAEjJ,IAAI,CAACrF,IAAK;MACtBuO,UAAU,EAAC;IAAM;MAAA1L,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,eAGFhF,OAAA,CAACV,aAAa;MACZ0Q,IAAI,EAAEhH,iBAAkB;MACxBiH,OAAO,EAAEA,CAAA,KAAMhH,oBAAoB,CAAC,KAAK,CAAE;MAC3C5B,IAAI,EAAEiC,SAAU;MAChBkH,eAAe,EAAE3C,mBAAoB;MACrC4C,UAAU,EAAEvH;IAAgB;MAAArE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC,eAGFhF,OAAA,CAACT,kBAAkB;MACjByQ,IAAI,EAAE5G,sBAAuB;MAC7B6G,OAAO,EAAEA,CAAA,KAAM5G,yBAAyB,CAAC,KAAK,CAAE;MAChDhC,IAAI,EAAEiC,SAAU;MAChB5H,YAAY,EAAEA,YAAa;MAC3BC,SAAS,EAAEA,SAAU;MACrBF,eAAe,EAAEuM;IAAoB;MAAAnJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC;EAAA,eACF,CAAC;AAEP,CAAC;;AAED;AAAAgD,GAAA,CApxBMT,QAAQ;AAAAmJ,GAAA,GAARnJ,QAAQ;AAqxBd,MAAMqI,WAAW,GAAGe,KAAA,IAYd;EAAAC,GAAA;EAAA,IAZe;IACnBjB,OAAO;IACPrI,KAAK;IACLuI,aAAa;IACbC,QAAQ;IACR9O,wBAAwB;IACxBC,gBAAgB;IAChBG,eAAe;IACfI,eAAe;IACfC,eAAe;IACfC,YAAY;IACZC;EACF,CAAC,GAAAgP,KAAA;EACC,MAAM,CAAC/O,SAAS,EAAEC,YAAY,CAAC,GAAG9D,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC8S,WAAW,EAAEC,cAAc,CAAC,GAAG/S,QAAQ,CAAC4R,OAAO,CAAC3N,IAAI,CAAC;EAC5D,MAAM,CAAC+O,SAAS,EAAEC,YAAY,CAAC,GAAGjT,QAAQ,CAAC4R,OAAO,CAAChM,MAAM,KAAKzE,MAAM,CAAC6N,SAAS,IAAI4C,OAAO,CAAClN,QAAQ,KAAK,GAAG,CAAC;EAC3G,MAAMwO,eAAe,GAAGjT,MAAM,CAAC,IAAI,CAAC;;EAEpC;EACA,MAAMkT,eAAe,GAAGlQ,wBAAwB,GAAGA,wBAAwB,CAAC2O,OAAO,CAAC,GAAG,CAAC;EACxF,MAAMwB,aAAa,GAAGlQ,gBAAgB,GAAGA,gBAAgB,CAAC0O,OAAO,CAAC,GAAIA,OAAO,CAAChM,MAAM,IAAIzE,MAAM,CAACyD,WAAY;EAC3G,MAAMyO,mBAAmB,GAAGjS,aAAa,CAACgS,aAAa,CAAC,IAAIhS,aAAa,CAACD,MAAM,CAACyD,WAAW,CAAC;;EAE7F;EACA1E,SAAS,CAAC,MAAM;IACd+S,YAAY,CAACrB,OAAO,CAAChM,MAAM,KAAKzE,MAAM,CAAC6N,SAAS,IAAI4C,OAAO,CAAClN,QAAQ,KAAK,GAAG,CAAC;EAC/E,CAAC,EAAE,CAACkN,OAAO,CAAChM,MAAM,EAAEgM,OAAO,CAAClN,QAAQ,CAAC,CAAC;;EAEtC;EACA,MAAM4O,sBAAsB,GAAGA,CAAA,KAAM;IACnCxP,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;;EAED;EACA,MAAMyP,iBAAiB,GAAGA,CAAA,KAAM;IAC9B;IACA,MAAMnO,WAAW,GAAG0N,WAAW,CAAC9N,IAAI,CAAC,CAAC;;IAEtC;IACA,IAAII,WAAW,KAAK,EAAE,IAAIA,WAAW,KAAKwM,OAAO,CAAC3N,IAAI,EAAE;MACtD,IAAIZ,eAAe,EAAE;QACnBA,eAAe,CAAC;UAAE,GAAGuO,OAAO;UAAE3N,IAAI,EAAEmB,WAAW;UAAEkE,IAAI,EAAEyI;QAAS,CAAC,CAAC;MACpE;IACF,CAAC,MAAM;MACL;MACAgB,cAAc,CAACnB,OAAO,CAAC3N,IAAI,CAAC;IAC9B;IAEAH,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;;EAED;EACA,MAAM0P,qBAAqB,GAAIlO,CAAC,IAAK;IACnC,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,EAAE;MACrBgO,iBAAiB,CAAC,CAAC;IACrB,CAAC,MAAM,IAAIjO,CAAC,CAACC,GAAG,KAAK,QAAQ,EAAE;MAC7BwN,cAAc,CAACnB,OAAO,CAAC3N,IAAI,CAAC;MAC5BH,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAM2P,kBAAkB,GAAGA,CAAA,KAAM;IAC/B;IACA,MAAMC,eAAe,GAAG,CAACV,SAAS;IAClCC,YAAY,CAACS,eAAe,CAAC;IAE7B,MAAMC,SAAS,GAAGD,eAAe,GAAGvS,MAAM,CAAC6N,SAAS,GAAG7N,MAAM,CAACyD,WAAW;IACzE,MAAMgP,WAAW,GAAGF,eAAe,GAAG,GAAG,GAAG,CAAC;IAE7C,IAAIrQ,eAAe,EAAE;MACnB;MACA,MAAMwQ,WAAW,GAAG;QAClB,GAAGjC,OAAO;QACVhM,MAAM,EAAE+N,SAAS;QACjBjP,QAAQ,EAAEkP,WAAW;QACrBtK,IAAI,EAAEyI;MACR,CAAC;MAEDhG,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE6H,WAAW,CAAC;MACnDxQ,eAAe,CAACwQ,WAAW,CAAC;IAC9B;EACF,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAIC,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;MACnEvQ,eAAe,CAACmO,OAAO,EAAEG,QAAQ,CAAC;IACpC;EACF,CAAC;;EAED;EACA7R,SAAS,CAAC,MAAM;IACd,IAAI2D,SAAS,IAAIqP,eAAe,CAACpN,OAAO,EAAE;MACxCoN,eAAe,CAACpN,OAAO,CAACC,KAAK,CAAC,CAAC;IACjC;EACF,CAAC,EAAE,CAAClC,SAAS,CAAC,CAAC;EAEf,oBACE5B,OAAA,CAAC9B,GAAG;IACFgG,EAAE,EAAE;MACFI,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpB6K,EAAE,EAAE,GAAG;MACPW,YAAY,EAAEzI,KAAK,GAAGuI,aAAa,GAAG,CAAC,GAAG,oBAAoB,GAAG;IACnE,CAAE;IAAAzL,QAAA,gBAEFpE,OAAA,CAACpB,QAAQ;MACPoT,OAAO,EAAEjB,SAAU;MACnB7L,QAAQ,EAAEsM,kBAAmB;MAC7B5L,IAAI,EAAC,OAAO;MACZ1B,EAAE,EAAE;QACFuC,CAAC,EAAE,GAAG;QACN7B,EAAE,EAAE,GAAG;QACPD,KAAK,EAAE,SAAS;QAChB,eAAe,EAAE;UACfA,KAAK,EAAE;QACT;MACF,CAAE;MACFF,IAAI,eAAEzE,OAAA,CAAChB,OAAO;QAACyF,IAAI,EAAC,0CAA0C;QAACD,KAAK,EAAE,EAAG;QAACE,MAAM,EAAE;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MACzFiN,WAAW,eAAEjS,OAAA,CAAChB,OAAO;QAACyF,IAAI,EAAC,4BAA4B;QAACD,KAAK,EAAE,EAAG;QAACE,MAAM,EAAE;MAAG;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnF,CAAC,EAEDpD,SAAS,gBACR5B,OAAA,CAAC9B,GAAG;MAACgG,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEgK,QAAQ,EAAE;MAAE,CAAE;MAAAnK,QAAA,gBAC9DpE,OAAA,CAACvB,SAAS;QACR8D,QAAQ,EAAE0O,eAAgB;QAC1BhM,KAAK,EAAE4L,WAAY;QACnB3L,QAAQ,EAAG7B,CAAC,IAAKyN,cAAc,CAACzN,CAAC,CAAC8B,MAAM,CAACF,KAAK,CAAE;QAChDG,SAAS,EAAEmM,qBAAsB;QACjClM,MAAM,EAAEiM,iBAAkB;QAC1BhM,OAAO,EAAC,UAAU;QAClBC,SAAS;QACTC,SAAS;QACTtB,EAAE,EAAE;UACFuB,UAAU,EAAE,kCAAkC;UAC9C,uBAAuB,EAAE;YACvBE,QAAQ,EAAE,SAAS;YACnBF,UAAU,EAAE;UACd;QACF;MAAE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACFhF,OAAA,CAACtB,UAAU;QAACkH,IAAI,EAAC,OAAO;QAACC,OAAO,EAAEyL,iBAAkB;QAACpN,EAAE,EAAE;UAAE4B,EAAE,EAAE;QAAE,CAAE;QAAA1B,QAAA,eACjEpE,OAAA,CAAChB,OAAO;UAACyF,IAAI,EAAC,wBAAwB;UAACD,KAAK,EAAE,EAAG;UAACE,MAAM,EAAE,EAAG;UAACC,KAAK,EAAC;QAAS;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,gBAENhF,OAAA,CAAC7B,UAAU;MACTmH,OAAO,EAAC,OAAO;MACfpB,EAAE,EAAE;QACFqK,QAAQ,EAAE,CAAC;QACX5I,QAAQ,EAAE,SAAS;QACnBF,UAAU,EAAE,kCAAkC;QAC9Cd,KAAK,EAAEoM,SAAS,GAAG,SAAS,GAAG,MAAM;QACrCrL,UAAU,EAAEqL,SAAS,GAAG,GAAG,GAAG,GAAG;QACjC/K,MAAM,EAAE,SAAS;QACjB1B,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpB,SAAS,EAAE;UACT,sBAAsB,EAAE;YACtB0B,OAAO,EAAE;UACX;QACF;MACF,CAAE;MACFJ,OAAO,EAAEwL,sBAAuB;MAAAjN,QAAA,GAE/ByM,WAAW,eACZ7Q,OAAA,CAACrB,OAAO;QAACuH,KAAK,EAAC,mBAAmB;QAAA9B,QAAA,eAChCpE,OAAA,CAACtB,UAAU;UACTkH,IAAI,EAAC,OAAO;UACZ5B,SAAS,EAAC,mBAAmB;UAC7BE,EAAE,EAAE;YACF4B,EAAE,EAAE,GAAG;YACPG,OAAO,EAAE,CAAC;YACVE,UAAU,EAAE,cAAc;YAC1BhC,OAAO,EAAE;UACX,CAAE;UAAAC,QAAA,eAEFpE,OAAA,CAAChB,OAAO;YAACyF,IAAI,EAAC,+BAA+B;YAACD,KAAK,EAAE,EAAG;YAACE,MAAM,EAAE,EAAG;YAACC,KAAK,EAAC;UAAM;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CACb,eAEDhF,OAAA,CAAC9B,GAAG;MAACgG,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAH,QAAA,GAChD8M,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAKgB,SAAS,iBACxDlS,OAAA,CAAC7B,UAAU;QACTmH,OAAO,EAAC,SAAS;QACjBpB,EAAE,EAAE;UACFyB,QAAQ,EAAE,SAAS;UACnBhB,KAAK,EAAEyM,mBAAmB,CAACzM,KAAK;UAChCmB,EAAE,EAAE,CAAC;UACLlB,EAAE,EAAE,CAAC;UACLa,UAAU,EAAE,kCAAkC;UAC9CC,UAAU,EAAE;QACd,CAAE;QAAAtB,QAAA,GAED8M,eAAe,EAAC,GACnB;MAAA;QAAArM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CACb,eAGDhF,OAAA,CAACrB,OAAO;QAACuH,KAAK,EAAC,gBAAgB;QAAA9B,QAAA,eAC7BpE,OAAA,CAACtB,UAAU;UACTkH,IAAI,EAAC,OAAO;UACZC,OAAO,EAAEgM,wBAAyB;UAClC3N,EAAE,EAAE;YACFuC,CAAC,EAAE,GAAG;YACN9B,KAAK,EAAE,SAAS;YAChB,SAAS,EAAE;cACT0B,eAAe,EAAE;YACnB;UACF,CAAE;UAAAjC,QAAA,eAEFpE,OAAA,CAAChB,OAAO;YAACyF,IAAI,EAAC,iCAAiC;YAACD,KAAK,EAAE,EAAG;YAACE,MAAM,EAAE;UAAG;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAA4L,GAAA,CA9NMhB,WAAW;AAAAuC,GAAA,GAAXvC,WAAW;AA+NjB,SAASP,UAAUA,CAAC+C,UAAU,EAAE;EAC9B,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;EAE1B,IAAI;IACF,MAAMC,IAAI,GAAG,IAAIrH,IAAI,CAACoH,UAAU,CAAC;IACjC,IAAIE,KAAK,CAACD,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE;;IAEpC;IACA,MAAMC,OAAO,GAAG;MAAE7G,KAAK,EAAE,OAAO;MAAED,GAAG,EAAE;IAAU,CAAC;IAClD,OAAO2G,IAAI,CAACI,kBAAkB,CAAC,OAAO,EAAED,OAAO,CAAC;EAClD,CAAC,CAAC,OAAOnI,KAAK,EAAE;IACd,OAAO,EAAE;EACX;AACF;AAEA,eAAelK,aAAa;AAAC,IAAA2H,EAAA,EAAA4I,GAAA,EAAAyB,GAAA;AAAAO,YAAA,CAAA5K,EAAA;AAAA4K,YAAA,CAAAhC,GAAA;AAAAgC,YAAA,CAAAP,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}