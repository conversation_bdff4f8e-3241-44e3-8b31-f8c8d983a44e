import { useState, useEffect, useRef, useCallback } from 'react';
import axios from 'axios';
import { getHeaders } from "helpers/functions";
import { APIURL } from "helpers/constants";

const usePlanFetching = (filters) => {
  const [plans, setPlans] = useState([]);
  const [loading, setLoading] = useState(true);
  const [loadingMore, setLoadingMore] = useState(false);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const observerRef = useRef(null);
  const observer = useRef(null);

  const fetchPlans = useCallback(async (append = false) => {
    if (append) {
      setLoadingMore(true);
    } else {
      setLoading(true);
    }

    try {
      let queryParams = '';
      if (filters.text) queryParams += `&name=${filters.text}`;
      if (filters.inviteUser) queryParams += `&invite_user=${filters.inviteUser}`;
      if (filters.createdDate) queryParams += `&created_date=${filters.createdDate}`;

      let url = `${APIURL}/api/retrieve/myplan?${queryParams}&page=${page}`;
      const response = await axios.get(url, { headers: getHeaders() });

      if (append) {
        setPlans((prevPlans) => [...prevPlans, ...response.data.results]);
      } else {
        setPlans(response.data.results);
      }

      setHasMore(response.data.next !== null);
    } catch (error) {
      console.log('Error loading: ', error);
    } finally {
      setLoading(false);
      setLoadingMore(false);
    }
  }, [filters, page]);

  useEffect(() => {
    setPage(1);
    setPlans([]);
    setHasMore(true);
    fetchPlans();
  }, [filters, fetchPlans]);

  const handleObserver = useCallback((entries) => {
    const target = entries[0];
    if (target.isIntersecting && hasMore && !loadingMore && !loading) {
      setPage((prevPage) => prevPage + 1);
    }
  }, [hasMore, loadingMore, loading]);

  useEffect(() => {
    if (observer.current) observer.current.disconnect();

    observer.current = new IntersectionObserver(handleObserver, {
      rootMargin: '100px',
    });

    if (observerRef.current) {
      observer.current.observe(observerRef.current);
    }

    return () => {
      if (observer.current) {
        observer.current.disconnect();
      }
    };
  }, [observerRef, handleObserver]);

  useEffect(() => {
    if (page > 1) {
      fetchPlans(true);
    }
  }, [page, fetchPlans]);

  return {
    plans,
    loading,
    loadingMore,
    hasMore,
    observerRef,
    setPage
  };
};

export default usePlanFetching;
