{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nvar _excluded = [\"getDisplayName\", \"methodName\", \"renderCountProp\", \"shouldHandleStateChanges\", \"storeKey\", \"withRef\", \"forwardRef\", \"context\"],\n  _excluded2 = [\"reactReduxForwardedRef\"];\nimport hoistStatics from 'hoist-non-react-statics';\nimport React, { useContext, useMemo, useRef, useReducer } from 'react';\nimport { isValidElementType, isContextConsumer } from 'react-is';\nimport { createSubscription } from '../utils/Subscription';\nimport { useIsomorphicLayoutEffect } from '../utils/useIsomorphicLayoutEffect';\nimport { ReactReduxContext } from './Context'; // Define some constant arrays just to avoid re-creating these\n\nvar EMPTY_ARRAY = [];\nvar NO_SUBSCRIPTION_ARRAY = [null, null];\nvar stringifyComponent = function stringifyComponent(Comp) {\n  try {\n    return JSON.stringify(Comp);\n  } catch (err) {\n    return String(Comp);\n  }\n};\nfunction storeStateUpdatesReducer(state, action) {\n  var updateCount = state[1];\n  return [action.payload, updateCount + 1];\n}\nfunction useIsomorphicLayoutEffectWithArgs(effectFunc, effectArgs, dependencies) {\n  useIsomorphicLayoutEffect(function () {\n    return effectFunc.apply(void 0, effectArgs);\n  }, dependencies);\n}\nfunction captureWrapperProps(lastWrapperProps, lastChildProps, renderIsScheduled, wrapperProps, actualChildProps, childPropsFromStoreUpdate, notifyNestedSubs) {\n  // We want to capture the wrapper props and child props we used for later comparisons\n  lastWrapperProps.current = wrapperProps;\n  lastChildProps.current = actualChildProps;\n  renderIsScheduled.current = false; // If the render was from a store update, clear out that reference and cascade the subscriber update\n\n  if (childPropsFromStoreUpdate.current) {\n    childPropsFromStoreUpdate.current = null;\n    notifyNestedSubs();\n  }\n}\nfunction subscribeUpdates(shouldHandleStateChanges, store, subscription, childPropsSelector, lastWrapperProps, lastChildProps, renderIsScheduled, childPropsFromStoreUpdate, notifyNestedSubs, forceComponentUpdateDispatch) {\n  // If we're not subscribed to the store, nothing to do here\n  if (!shouldHandleStateChanges) return; // Capture values for checking if and when this component unmounts\n\n  var didUnsubscribe = false;\n  var lastThrownError = null; // We'll run this callback every time a store subscription update propagates to this component\n\n  var checkForUpdates = function checkForUpdates() {\n    if (didUnsubscribe) {\n      // Don't run stale listeners.\n      // Redux doesn't guarantee unsubscriptions happen until next dispatch.\n      return;\n    }\n    var latestStoreState = store.getState();\n    var newChildProps, error;\n    try {\n      // Actually run the selector with the most recent store state and wrapper props\n      // to determine what the child props should be\n      newChildProps = childPropsSelector(latestStoreState, lastWrapperProps.current);\n    } catch (e) {\n      error = e;\n      lastThrownError = e;\n    }\n    if (!error) {\n      lastThrownError = null;\n    } // If the child props haven't changed, nothing to do here - cascade the subscription update\n\n    if (newChildProps === lastChildProps.current) {\n      if (!renderIsScheduled.current) {\n        notifyNestedSubs();\n      }\n    } else {\n      // Save references to the new child props.  Note that we track the \"child props from store update\"\n      // as a ref instead of a useState/useReducer because we need a way to determine if that value has\n      // been processed.  If this went into useState/useReducer, we couldn't clear out the value without\n      // forcing another re-render, which we don't want.\n      lastChildProps.current = newChildProps;\n      childPropsFromStoreUpdate.current = newChildProps;\n      renderIsScheduled.current = true; // If the child props _did_ change (or we caught an error), this wrapper component needs to re-render\n\n      forceComponentUpdateDispatch({\n        type: 'STORE_UPDATED',\n        payload: {\n          error: error\n        }\n      });\n    }\n  }; // Actually subscribe to the nearest connected ancestor (or store)\n\n  subscription.onStateChange = checkForUpdates;\n  subscription.trySubscribe(); // Pull data from the store after first render in case the store has\n  // changed since we began.\n\n  checkForUpdates();\n  var unsubscribeWrapper = function unsubscribeWrapper() {\n    didUnsubscribe = true;\n    subscription.tryUnsubscribe();\n    subscription.onStateChange = null;\n    if (lastThrownError) {\n      // It's possible that we caught an error due to a bad mapState function, but the\n      // parent re-rendered without this component and we're about to unmount.\n      // This shouldn't happen as long as we do top-down subscriptions correctly, but\n      // if we ever do those wrong, this throw will surface the error in our tests.\n      // In that case, throw the error from here so it doesn't get lost.\n      throw lastThrownError;\n    }\n  };\n  return unsubscribeWrapper;\n}\nvar initStateUpdates = function initStateUpdates() {\n  return [null, 0];\n};\nexport default function connectAdvanced(\n/*\r\n  selectorFactory is a func that is responsible for returning the selector function used to\r\n  compute new props from state, props, and dispatch. For example:\r\n      export default connectAdvanced((dispatch, options) => (state, props) => ({\r\n      thing: state.things[props.thingId],\r\n      saveThing: fields => dispatch(actionCreators.saveThing(props.thingId, fields)),\r\n    }))(YourComponent)\r\n    Access to dispatch is provided to the factory so selectorFactories can bind actionCreators\r\n  outside of their selector as an optimization. Options passed to connectAdvanced are passed to\r\n  the selectorFactory, along with displayName and WrappedComponent, as the second argument.\r\n    Note that selectorFactory is responsible for all caching/memoization of inbound and outbound\r\n  props. Do not use connectAdvanced directly without memoizing results between calls to your\r\n  selector, otherwise the Connect component will re-render on every state or props change.\r\n*/\nselectorFactory,\n// options object:\n_ref) {\n  if (_ref === void 0) {\n    _ref = {};\n  }\n  var _ref2 = _ref,\n    _ref2$getDisplayName = _ref2.getDisplayName,\n    getDisplayName = _ref2$getDisplayName === void 0 ? function (name) {\n      return \"ConnectAdvanced(\" + name + \")\";\n    } : _ref2$getDisplayName,\n    _ref2$methodName = _ref2.methodName,\n    methodName = _ref2$methodName === void 0 ? 'connectAdvanced' : _ref2$methodName,\n    _ref2$renderCountProp = _ref2.renderCountProp,\n    renderCountProp = _ref2$renderCountProp === void 0 ? undefined : _ref2$renderCountProp,\n    _ref2$shouldHandleSta = _ref2.shouldHandleStateChanges,\n    shouldHandleStateChanges = _ref2$shouldHandleSta === void 0 ? true : _ref2$shouldHandleSta,\n    _ref2$storeKey = _ref2.storeKey,\n    storeKey = _ref2$storeKey === void 0 ? 'store' : _ref2$storeKey,\n    _ref2$withRef = _ref2.withRef,\n    withRef = _ref2$withRef === void 0 ? false : _ref2$withRef,\n    _ref2$forwardRef = _ref2.forwardRef,\n    forwardRef = _ref2$forwardRef === void 0 ? false : _ref2$forwardRef,\n    _ref2$context = _ref2.context,\n    context = _ref2$context === void 0 ? ReactReduxContext : _ref2$context,\n    connectOptions = _objectWithoutPropertiesLoose(_ref2, _excluded);\n  if (process.env.NODE_ENV !== 'production') {\n    if (renderCountProp !== undefined) {\n      throw new Error(\"renderCountProp is removed. render counting is built into the latest React Dev Tools profiling extension\");\n    }\n    if (withRef) {\n      throw new Error('withRef is removed. To access the wrapped instance, use a ref on the connected component');\n    }\n    var customStoreWarningMessage = 'To use a custom Redux store for specific components, create a custom React context with ' + \"React.createContext(), and pass the context object to React Redux's Provider and specific components\" + ' like: <Provider context={MyContext}><ConnectedComponent context={MyContext} /></Provider>. ' + 'You may also pass a {context : MyContext} option to connect';\n    if (storeKey !== 'store') {\n      throw new Error('storeKey has been removed and does not do anything. ' + customStoreWarningMessage);\n    }\n  }\n  var Context = context;\n  return function wrapWithConnect(WrappedComponent) {\n    if (process.env.NODE_ENV !== 'production' && !isValidElementType(WrappedComponent)) {\n      throw new Error(\"You must pass a component to the function returned by \" + (methodName + \". Instead received \" + stringifyComponent(WrappedComponent)));\n    }\n    var wrappedComponentName = WrappedComponent.displayName || WrappedComponent.name || 'Component';\n    var displayName = getDisplayName(wrappedComponentName);\n    var selectorFactoryOptions = _extends({}, connectOptions, {\n      getDisplayName: getDisplayName,\n      methodName: methodName,\n      renderCountProp: renderCountProp,\n      shouldHandleStateChanges: shouldHandleStateChanges,\n      storeKey: storeKey,\n      displayName: displayName,\n      wrappedComponentName: wrappedComponentName,\n      WrappedComponent: WrappedComponent\n    });\n    var pure = connectOptions.pure;\n    function createChildSelector(store) {\n      return selectorFactory(store.dispatch, selectorFactoryOptions);\n    } // If we aren't running in \"pure\" mode, we don't want to memoize values.\n    // To avoid conditionally calling hooks, we fall back to a tiny wrapper\n    // that just executes the given callback immediately.\n\n    var usePureOnlyMemo = pure ? useMemo : function (callback) {\n      return callback();\n    };\n    function ConnectFunction(props) {\n      var _useMemo = useMemo(function () {\n          // Distinguish between actual \"data\" props that were passed to the wrapper component,\n          // and values needed to control behavior (forwarded refs, alternate context instances).\n          // To maintain the wrapperProps object reference, memoize this destructuring.\n          var reactReduxForwardedRef = props.reactReduxForwardedRef,\n            wrapperProps = _objectWithoutPropertiesLoose(props, _excluded2);\n          return [props.context, reactReduxForwardedRef, wrapperProps];\n        }, [props]),\n        propsContext = _useMemo[0],\n        reactReduxForwardedRef = _useMemo[1],\n        wrapperProps = _useMemo[2];\n      var ContextToUse = useMemo(function () {\n        // Users may optionally pass in a custom context instance to use instead of our ReactReduxContext.\n        // Memoize the check that determines which context instance we should use.\n        return propsContext && propsContext.Consumer && isContextConsumer(/*#__PURE__*/React.createElement(propsContext.Consumer, null)) ? propsContext : Context;\n      }, [propsContext, Context]); // Retrieve the store and ancestor subscription via context, if available\n\n      var contextValue = useContext(ContextToUse); // The store _must_ exist as either a prop or in context.\n      // We'll check to see if it _looks_ like a Redux store first.\n      // This allows us to pass through a `store` prop that is just a plain value.\n\n      var didStoreComeFromProps = Boolean(props.store) && Boolean(props.store.getState) && Boolean(props.store.dispatch);\n      var didStoreComeFromContext = Boolean(contextValue) && Boolean(contextValue.store);\n      if (process.env.NODE_ENV !== 'production' && !didStoreComeFromProps && !didStoreComeFromContext) {\n        throw new Error(\"Could not find \\\"store\\\" in the context of \" + (\"\\\"\" + displayName + \"\\\". Either wrap the root component in a <Provider>, \") + \"or pass a custom React context provider to <Provider> and the corresponding \" + (\"React context consumer to \" + displayName + \" in connect options.\"));\n      } // Based on the previous check, one of these must be true\n\n      var store = didStoreComeFromProps ? props.store : contextValue.store;\n      var childPropsSelector = useMemo(function () {\n        // The child props selector needs the store reference as an input.\n        // Re-create this selector whenever the store changes.\n        return createChildSelector(store);\n      }, [store]);\n      var _useMemo2 = useMemo(function () {\n          if (!shouldHandleStateChanges) return NO_SUBSCRIPTION_ARRAY; // This Subscription's source should match where store came from: props vs. context. A component\n          // connected to the store via props shouldn't use subscription from context, or vice versa.\n\n          // This Subscription's source should match where store came from: props vs. context. A component\n          // connected to the store via props shouldn't use subscription from context, or vice versa.\n          var subscription = createSubscription(store, didStoreComeFromProps ? null : contextValue.subscription); // `notifyNestedSubs` is duplicated to handle the case where the component is unmounted in\n          // the middle of the notification loop, where `subscription` will then be null. This can\n          // probably be avoided if Subscription's listeners logic is changed to not call listeners\n          // that have been unsubscribed in the  middle of the notification loop.\n\n          // `notifyNestedSubs` is duplicated to handle the case where the component is unmounted in\n          // the middle of the notification loop, where `subscription` will then be null. This can\n          // probably be avoided if Subscription's listeners logic is changed to not call listeners\n          // that have been unsubscribed in the  middle of the notification loop.\n          var notifyNestedSubs = subscription.notifyNestedSubs.bind(subscription);\n          return [subscription, notifyNestedSubs];\n        }, [store, didStoreComeFromProps, contextValue]),\n        subscription = _useMemo2[0],\n        notifyNestedSubs = _useMemo2[1]; // Determine what {store, subscription} value should be put into nested context, if necessary,\n      // and memoize that value to avoid unnecessary context updates.\n\n      var overriddenContextValue = useMemo(function () {\n        if (didStoreComeFromProps) {\n          // This component is directly subscribed to a store from props.\n          // We don't want descendants reading from this store - pass down whatever\n          // the existing context value is from the nearest connected ancestor.\n          return contextValue;\n        } // Otherwise, put this component's subscription instance into context, so that\n        // connected descendants won't update until after this component is done\n\n        return _extends({}, contextValue, {\n          subscription: subscription\n        });\n      }, [didStoreComeFromProps, contextValue, subscription]); // We need to force this wrapper component to re-render whenever a Redux store update\n      // causes a change to the calculated child component props (or we caught an error in mapState)\n\n      var _useReducer = useReducer(storeStateUpdatesReducer, EMPTY_ARRAY, initStateUpdates),\n        _useReducer$ = _useReducer[0],\n        previousStateUpdateResult = _useReducer$[0],\n        forceComponentUpdateDispatch = _useReducer[1]; // Propagate any mapState/mapDispatch errors upwards\n\n      if (previousStateUpdateResult && previousStateUpdateResult.error) {\n        throw previousStateUpdateResult.error;\n      } // Set up refs to coordinate values between the subscription effect and the render logic\n\n      var lastChildProps = useRef();\n      var lastWrapperProps = useRef(wrapperProps);\n      var childPropsFromStoreUpdate = useRef();\n      var renderIsScheduled = useRef(false);\n      var actualChildProps = usePureOnlyMemo(function () {\n        // Tricky logic here:\n        // - This render may have been triggered by a Redux store update that produced new child props\n        // - However, we may have gotten new wrapper props after that\n        // If we have new child props, and the same wrapper props, we know we should use the new child props as-is.\n        // But, if we have new wrapper props, those might change the child props, so we have to recalculate things.\n        // So, we'll use the child props from store update only if the wrapper props are the same as last time.\n        if (childPropsFromStoreUpdate.current && wrapperProps === lastWrapperProps.current) {\n          return childPropsFromStoreUpdate.current;\n        } // TODO We're reading the store directly in render() here. Bad idea?\n        // This will likely cause Bad Things (TM) to happen in Concurrent Mode.\n        // Note that we do this because on renders _not_ caused by store updates, we need the latest store state\n        // to determine what the child props should be.\n\n        return childPropsSelector(store.getState(), wrapperProps);\n      }, [store, previousStateUpdateResult, wrapperProps]); // We need this to execute synchronously every time we re-render. However, React warns\n      // about useLayoutEffect in SSR, so we try to detect environment and fall back to\n      // just useEffect instead to avoid the warning, since neither will run anyway.\n\n      useIsomorphicLayoutEffectWithArgs(captureWrapperProps, [lastWrapperProps, lastChildProps, renderIsScheduled, wrapperProps, actualChildProps, childPropsFromStoreUpdate, notifyNestedSubs]); // Our re-subscribe logic only runs when the store/subscription setup changes\n\n      useIsomorphicLayoutEffectWithArgs(subscribeUpdates, [shouldHandleStateChanges, store, subscription, childPropsSelector, lastWrapperProps, lastChildProps, renderIsScheduled, childPropsFromStoreUpdate, notifyNestedSubs, forceComponentUpdateDispatch], [store, subscription, childPropsSelector]); // Now that all that's done, we can finally try to actually render the child component.\n      // We memoize the elements for the rendered child component as an optimization.\n\n      var renderedWrappedComponent = useMemo(function () {\n        return /*#__PURE__*/React.createElement(WrappedComponent, _extends({}, actualChildProps, {\n          ref: reactReduxForwardedRef\n        }));\n      }, [reactReduxForwardedRef, WrappedComponent, actualChildProps]); // If React sees the exact same element reference as last time, it bails out of re-rendering\n      // that child, same as if it was wrapped in React.memo() or returned false from shouldComponentUpdate.\n\n      var renderedChild = useMemo(function () {\n        if (shouldHandleStateChanges) {\n          // If this component is subscribed to store updates, we need to pass its own\n          // subscription instance down to our descendants. That means rendering the same\n          // Context instance, and putting a different value into the context.\n          return /*#__PURE__*/React.createElement(ContextToUse.Provider, {\n            value: overriddenContextValue\n          }, renderedWrappedComponent);\n        }\n        return renderedWrappedComponent;\n      }, [ContextToUse, renderedWrappedComponent, overriddenContextValue]);\n      return renderedChild;\n    } // If we're in \"pure\" mode, ensure our wrapper component only re-renders when incoming props have changed.\n\n    var Connect = pure ? React.memo(ConnectFunction) : ConnectFunction;\n    Connect.WrappedComponent = WrappedComponent;\n    Connect.displayName = ConnectFunction.displayName = displayName;\n    if (forwardRef) {\n      var forwarded = React.forwardRef(function forwardConnectRef(props, ref) {\n        return /*#__PURE__*/React.createElement(Connect, _extends({}, props, {\n          reactReduxForwardedRef: ref\n        }));\n      });\n      forwarded.displayName = displayName;\n      forwarded.WrappedComponent = WrappedComponent;\n      return hoistStatics(forwarded, WrappedComponent);\n    }\n    return hoistStatics(Connect, WrappedComponent);\n  };\n}", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "_excluded2", "hoistStatics", "React", "useContext", "useMemo", "useRef", "useReducer", "isValidElementType", "isContextConsumer", "createSubscription", "useIsomorphicLayoutEffect", "ReactReduxContext", "EMPTY_ARRAY", "NO_SUBSCRIPTION_ARRAY", "stringifyComponent", "Comp", "JSON", "stringify", "err", "String", "storeStateUpdatesReducer", "state", "action", "updateCount", "payload", "useIsomorphicLayoutEffectWithArgs", "effectFunc", "effectArgs", "dependencies", "apply", "captureWrapperProps", "lastWrapperProps", "lastChildProps", "renderIsScheduled", "wrapperProps", "actualChildProps", "childPropsFromStoreUpdate", "notifyNestedSubs", "current", "subscribeUpdates", "shouldHandleStateChanges", "store", "subscription", "childPropsSelector", "forceComponentUpdateDispatch", "didUnsubscribe", "lastThrownError", "checkForUpdates", "latestStoreState", "getState", "newChildProps", "error", "e", "type", "onStateChange", "trySubscribe", "unsubscribeWrapper", "tryUnsubscribe", "initStateUpdates", "connectAdvanced", "selectorFactory", "_ref", "_ref2", "_ref2$getDisplayName", "getDisplayName", "name", "_ref2$methodName", "methodName", "_ref2$renderCountProp", "renderCountProp", "undefined", "_ref2$shouldHandleSta", "_ref2$storeKey", "storeKey", "_ref2$withRef", "with<PERSON>ef", "_ref2$forwardRef", "forwardRef", "_ref2$context", "context", "connectOptions", "process", "env", "NODE_ENV", "Error", "customStoreWarningMessage", "Context", "wrapWithConnect", "WrappedComponent", "wrappedComponentName", "displayName", "selectorFactoryOptions", "pure", "createChildSelector", "dispatch", "usePureOnlyMemo", "callback", "ConnectFunction", "props", "_useMemo", "reactReduxForwardedRef", "props<PERSON><PERSON><PERSON><PERSON>", "ContextToUse", "Consumer", "createElement", "contextValue", "didStoreComeFromProps", "Boolean", "didStoreComeFromContext", "_useMemo2", "bind", "overriddenContextValue", "_useReducer", "_useReducer$", "previousStateUpdateResult", "renderedWrappedComponent", "ref", "<PERSON><PERSON><PERSON><PERSON>", "Provider", "value", "Connect", "memo", "forwarded", "forwardConnectRef"], "sources": ["C:/ignition/ignition-ui/node_modules/react-beautiful-dnd/node_modules/react-redux/es/components/connectAdvanced.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nvar _excluded = [\"getDisplayName\", \"methodName\", \"renderCountProp\", \"shouldHandleStateChanges\", \"storeKey\", \"withRef\", \"forwardRef\", \"context\"],\n    _excluded2 = [\"reactReduxForwardedRef\"];\nimport hoistStatics from 'hoist-non-react-statics';\nimport React, { useContext, useMemo, useRef, useReducer } from 'react';\nimport { isValidElementType, isContextConsumer } from 'react-is';\nimport { createSubscription } from '../utils/Subscription';\nimport { useIsomorphicLayoutEffect } from '../utils/useIsomorphicLayoutEffect';\nimport { ReactReduxContext } from './Context'; // Define some constant arrays just to avoid re-creating these\n\nvar EMPTY_ARRAY = [];\nvar NO_SUBSCRIPTION_ARRAY = [null, null];\n\nvar stringifyComponent = function stringifyComponent(Comp) {\n  try {\n    return JSON.stringify(Comp);\n  } catch (err) {\n    return String(Comp);\n  }\n};\n\nfunction storeStateUpdatesReducer(state, action) {\n  var updateCount = state[1];\n  return [action.payload, updateCount + 1];\n}\n\nfunction useIsomorphicLayoutEffectWithArgs(effectFunc, effectArgs, dependencies) {\n  useIsomorphicLayoutEffect(function () {\n    return effectFunc.apply(void 0, effectArgs);\n  }, dependencies);\n}\n\nfunction captureWrapperProps(lastWrapperProps, lastChildProps, renderIsScheduled, wrapperProps, actualChildProps, childPropsFromStoreUpdate, notifyNestedSubs) {\n  // We want to capture the wrapper props and child props we used for later comparisons\n  lastWrapperProps.current = wrapperProps;\n  lastChildProps.current = actualChildProps;\n  renderIsScheduled.current = false; // If the render was from a store update, clear out that reference and cascade the subscriber update\n\n  if (childPropsFromStoreUpdate.current) {\n    childPropsFromStoreUpdate.current = null;\n    notifyNestedSubs();\n  }\n}\n\nfunction subscribeUpdates(shouldHandleStateChanges, store, subscription, childPropsSelector, lastWrapperProps, lastChildProps, renderIsScheduled, childPropsFromStoreUpdate, notifyNestedSubs, forceComponentUpdateDispatch) {\n  // If we're not subscribed to the store, nothing to do here\n  if (!shouldHandleStateChanges) return; // Capture values for checking if and when this component unmounts\n\n  var didUnsubscribe = false;\n  var lastThrownError = null; // We'll run this callback every time a store subscription update propagates to this component\n\n  var checkForUpdates = function checkForUpdates() {\n    if (didUnsubscribe) {\n      // Don't run stale listeners.\n      // Redux doesn't guarantee unsubscriptions happen until next dispatch.\n      return;\n    }\n\n    var latestStoreState = store.getState();\n    var newChildProps, error;\n\n    try {\n      // Actually run the selector with the most recent store state and wrapper props\n      // to determine what the child props should be\n      newChildProps = childPropsSelector(latestStoreState, lastWrapperProps.current);\n    } catch (e) {\n      error = e;\n      lastThrownError = e;\n    }\n\n    if (!error) {\n      lastThrownError = null;\n    } // If the child props haven't changed, nothing to do here - cascade the subscription update\n\n\n    if (newChildProps === lastChildProps.current) {\n      if (!renderIsScheduled.current) {\n        notifyNestedSubs();\n      }\n    } else {\n      // Save references to the new child props.  Note that we track the \"child props from store update\"\n      // as a ref instead of a useState/useReducer because we need a way to determine if that value has\n      // been processed.  If this went into useState/useReducer, we couldn't clear out the value without\n      // forcing another re-render, which we don't want.\n      lastChildProps.current = newChildProps;\n      childPropsFromStoreUpdate.current = newChildProps;\n      renderIsScheduled.current = true; // If the child props _did_ change (or we caught an error), this wrapper component needs to re-render\n\n      forceComponentUpdateDispatch({\n        type: 'STORE_UPDATED',\n        payload: {\n          error: error\n        }\n      });\n    }\n  }; // Actually subscribe to the nearest connected ancestor (or store)\n\n\n  subscription.onStateChange = checkForUpdates;\n  subscription.trySubscribe(); // Pull data from the store after first render in case the store has\n  // changed since we began.\n\n  checkForUpdates();\n\n  var unsubscribeWrapper = function unsubscribeWrapper() {\n    didUnsubscribe = true;\n    subscription.tryUnsubscribe();\n    subscription.onStateChange = null;\n\n    if (lastThrownError) {\n      // It's possible that we caught an error due to a bad mapState function, but the\n      // parent re-rendered without this component and we're about to unmount.\n      // This shouldn't happen as long as we do top-down subscriptions correctly, but\n      // if we ever do those wrong, this throw will surface the error in our tests.\n      // In that case, throw the error from here so it doesn't get lost.\n      throw lastThrownError;\n    }\n  };\n\n  return unsubscribeWrapper;\n}\n\nvar initStateUpdates = function initStateUpdates() {\n  return [null, 0];\n};\n\nexport default function connectAdvanced(\n/*\r\n  selectorFactory is a func that is responsible for returning the selector function used to\r\n  compute new props from state, props, and dispatch. For example:\r\n      export default connectAdvanced((dispatch, options) => (state, props) => ({\r\n      thing: state.things[props.thingId],\r\n      saveThing: fields => dispatch(actionCreators.saveThing(props.thingId, fields)),\r\n    }))(YourComponent)\r\n    Access to dispatch is provided to the factory so selectorFactories can bind actionCreators\r\n  outside of their selector as an optimization. Options passed to connectAdvanced are passed to\r\n  the selectorFactory, along with displayName and WrappedComponent, as the second argument.\r\n    Note that selectorFactory is responsible for all caching/memoization of inbound and outbound\r\n  props. Do not use connectAdvanced directly without memoizing results between calls to your\r\n  selector, otherwise the Connect component will re-render on every state or props change.\r\n*/\nselectorFactory, // options object:\n_ref) {\n  if (_ref === void 0) {\n    _ref = {};\n  }\n\n  var _ref2 = _ref,\n      _ref2$getDisplayName = _ref2.getDisplayName,\n      getDisplayName = _ref2$getDisplayName === void 0 ? function (name) {\n    return \"ConnectAdvanced(\" + name + \")\";\n  } : _ref2$getDisplayName,\n      _ref2$methodName = _ref2.methodName,\n      methodName = _ref2$methodName === void 0 ? 'connectAdvanced' : _ref2$methodName,\n      _ref2$renderCountProp = _ref2.renderCountProp,\n      renderCountProp = _ref2$renderCountProp === void 0 ? undefined : _ref2$renderCountProp,\n      _ref2$shouldHandleSta = _ref2.shouldHandleStateChanges,\n      shouldHandleStateChanges = _ref2$shouldHandleSta === void 0 ? true : _ref2$shouldHandleSta,\n      _ref2$storeKey = _ref2.storeKey,\n      storeKey = _ref2$storeKey === void 0 ? 'store' : _ref2$storeKey,\n      _ref2$withRef = _ref2.withRef,\n      withRef = _ref2$withRef === void 0 ? false : _ref2$withRef,\n      _ref2$forwardRef = _ref2.forwardRef,\n      forwardRef = _ref2$forwardRef === void 0 ? false : _ref2$forwardRef,\n      _ref2$context = _ref2.context,\n      context = _ref2$context === void 0 ? ReactReduxContext : _ref2$context,\n      connectOptions = _objectWithoutPropertiesLoose(_ref2, _excluded);\n\n  if (process.env.NODE_ENV !== 'production') {\n    if (renderCountProp !== undefined) {\n      throw new Error(\"renderCountProp is removed. render counting is built into the latest React Dev Tools profiling extension\");\n    }\n\n    if (withRef) {\n      throw new Error('withRef is removed. To access the wrapped instance, use a ref on the connected component');\n    }\n\n    var customStoreWarningMessage = 'To use a custom Redux store for specific components, create a custom React context with ' + \"React.createContext(), and pass the context object to React Redux's Provider and specific components\" + ' like: <Provider context={MyContext}><ConnectedComponent context={MyContext} /></Provider>. ' + 'You may also pass a {context : MyContext} option to connect';\n\n    if (storeKey !== 'store') {\n      throw new Error('storeKey has been removed and does not do anything. ' + customStoreWarningMessage);\n    }\n  }\n\n  var Context = context;\n  return function wrapWithConnect(WrappedComponent) {\n    if (process.env.NODE_ENV !== 'production' && !isValidElementType(WrappedComponent)) {\n      throw new Error(\"You must pass a component to the function returned by \" + (methodName + \". Instead received \" + stringifyComponent(WrappedComponent)));\n    }\n\n    var wrappedComponentName = WrappedComponent.displayName || WrappedComponent.name || 'Component';\n    var displayName = getDisplayName(wrappedComponentName);\n\n    var selectorFactoryOptions = _extends({}, connectOptions, {\n      getDisplayName: getDisplayName,\n      methodName: methodName,\n      renderCountProp: renderCountProp,\n      shouldHandleStateChanges: shouldHandleStateChanges,\n      storeKey: storeKey,\n      displayName: displayName,\n      wrappedComponentName: wrappedComponentName,\n      WrappedComponent: WrappedComponent\n    });\n\n    var pure = connectOptions.pure;\n\n    function createChildSelector(store) {\n      return selectorFactory(store.dispatch, selectorFactoryOptions);\n    } // If we aren't running in \"pure\" mode, we don't want to memoize values.\n    // To avoid conditionally calling hooks, we fall back to a tiny wrapper\n    // that just executes the given callback immediately.\n\n\n    var usePureOnlyMemo = pure ? useMemo : function (callback) {\n      return callback();\n    };\n\n    function ConnectFunction(props) {\n      var _useMemo = useMemo(function () {\n        // Distinguish between actual \"data\" props that were passed to the wrapper component,\n        // and values needed to control behavior (forwarded refs, alternate context instances).\n        // To maintain the wrapperProps object reference, memoize this destructuring.\n        var reactReduxForwardedRef = props.reactReduxForwardedRef,\n            wrapperProps = _objectWithoutPropertiesLoose(props, _excluded2);\n\n        return [props.context, reactReduxForwardedRef, wrapperProps];\n      }, [props]),\n          propsContext = _useMemo[0],\n          reactReduxForwardedRef = _useMemo[1],\n          wrapperProps = _useMemo[2];\n\n      var ContextToUse = useMemo(function () {\n        // Users may optionally pass in a custom context instance to use instead of our ReactReduxContext.\n        // Memoize the check that determines which context instance we should use.\n        return propsContext && propsContext.Consumer && isContextConsumer( /*#__PURE__*/React.createElement(propsContext.Consumer, null)) ? propsContext : Context;\n      }, [propsContext, Context]); // Retrieve the store and ancestor subscription via context, if available\n\n      var contextValue = useContext(ContextToUse); // The store _must_ exist as either a prop or in context.\n      // We'll check to see if it _looks_ like a Redux store first.\n      // This allows us to pass through a `store` prop that is just a plain value.\n\n      var didStoreComeFromProps = Boolean(props.store) && Boolean(props.store.getState) && Boolean(props.store.dispatch);\n      var didStoreComeFromContext = Boolean(contextValue) && Boolean(contextValue.store);\n\n      if (process.env.NODE_ENV !== 'production' && !didStoreComeFromProps && !didStoreComeFromContext) {\n        throw new Error(\"Could not find \\\"store\\\" in the context of \" + (\"\\\"\" + displayName + \"\\\". Either wrap the root component in a <Provider>, \") + \"or pass a custom React context provider to <Provider> and the corresponding \" + (\"React context consumer to \" + displayName + \" in connect options.\"));\n      } // Based on the previous check, one of these must be true\n\n\n      var store = didStoreComeFromProps ? props.store : contextValue.store;\n      var childPropsSelector = useMemo(function () {\n        // The child props selector needs the store reference as an input.\n        // Re-create this selector whenever the store changes.\n        return createChildSelector(store);\n      }, [store]);\n\n      var _useMemo2 = useMemo(function () {\n        if (!shouldHandleStateChanges) return NO_SUBSCRIPTION_ARRAY; // This Subscription's source should match where store came from: props vs. context. A component\n        // connected to the store via props shouldn't use subscription from context, or vice versa.\n\n        // This Subscription's source should match where store came from: props vs. context. A component\n        // connected to the store via props shouldn't use subscription from context, or vice versa.\n        var subscription = createSubscription(store, didStoreComeFromProps ? null : contextValue.subscription); // `notifyNestedSubs` is duplicated to handle the case where the component is unmounted in\n        // the middle of the notification loop, where `subscription` will then be null. This can\n        // probably be avoided if Subscription's listeners logic is changed to not call listeners\n        // that have been unsubscribed in the  middle of the notification loop.\n\n        // `notifyNestedSubs` is duplicated to handle the case where the component is unmounted in\n        // the middle of the notification loop, where `subscription` will then be null. This can\n        // probably be avoided if Subscription's listeners logic is changed to not call listeners\n        // that have been unsubscribed in the  middle of the notification loop.\n        var notifyNestedSubs = subscription.notifyNestedSubs.bind(subscription);\n        return [subscription, notifyNestedSubs];\n      }, [store, didStoreComeFromProps, contextValue]),\n          subscription = _useMemo2[0],\n          notifyNestedSubs = _useMemo2[1]; // Determine what {store, subscription} value should be put into nested context, if necessary,\n      // and memoize that value to avoid unnecessary context updates.\n\n\n      var overriddenContextValue = useMemo(function () {\n        if (didStoreComeFromProps) {\n          // This component is directly subscribed to a store from props.\n          // We don't want descendants reading from this store - pass down whatever\n          // the existing context value is from the nearest connected ancestor.\n          return contextValue;\n        } // Otherwise, put this component's subscription instance into context, so that\n        // connected descendants won't update until after this component is done\n\n\n        return _extends({}, contextValue, {\n          subscription: subscription\n        });\n      }, [didStoreComeFromProps, contextValue, subscription]); // We need to force this wrapper component to re-render whenever a Redux store update\n      // causes a change to the calculated child component props (or we caught an error in mapState)\n\n      var _useReducer = useReducer(storeStateUpdatesReducer, EMPTY_ARRAY, initStateUpdates),\n          _useReducer$ = _useReducer[0],\n          previousStateUpdateResult = _useReducer$[0],\n          forceComponentUpdateDispatch = _useReducer[1]; // Propagate any mapState/mapDispatch errors upwards\n\n\n      if (previousStateUpdateResult && previousStateUpdateResult.error) {\n        throw previousStateUpdateResult.error;\n      } // Set up refs to coordinate values between the subscription effect and the render logic\n\n\n      var lastChildProps = useRef();\n      var lastWrapperProps = useRef(wrapperProps);\n      var childPropsFromStoreUpdate = useRef();\n      var renderIsScheduled = useRef(false);\n      var actualChildProps = usePureOnlyMemo(function () {\n        // Tricky logic here:\n        // - This render may have been triggered by a Redux store update that produced new child props\n        // - However, we may have gotten new wrapper props after that\n        // If we have new child props, and the same wrapper props, we know we should use the new child props as-is.\n        // But, if we have new wrapper props, those might change the child props, so we have to recalculate things.\n        // So, we'll use the child props from store update only if the wrapper props are the same as last time.\n        if (childPropsFromStoreUpdate.current && wrapperProps === lastWrapperProps.current) {\n          return childPropsFromStoreUpdate.current;\n        } // TODO We're reading the store directly in render() here. Bad idea?\n        // This will likely cause Bad Things (TM) to happen in Concurrent Mode.\n        // Note that we do this because on renders _not_ caused by store updates, we need the latest store state\n        // to determine what the child props should be.\n\n\n        return childPropsSelector(store.getState(), wrapperProps);\n      }, [store, previousStateUpdateResult, wrapperProps]); // We need this to execute synchronously every time we re-render. However, React warns\n      // about useLayoutEffect in SSR, so we try to detect environment and fall back to\n      // just useEffect instead to avoid the warning, since neither will run anyway.\n\n      useIsomorphicLayoutEffectWithArgs(captureWrapperProps, [lastWrapperProps, lastChildProps, renderIsScheduled, wrapperProps, actualChildProps, childPropsFromStoreUpdate, notifyNestedSubs]); // Our re-subscribe logic only runs when the store/subscription setup changes\n\n      useIsomorphicLayoutEffectWithArgs(subscribeUpdates, [shouldHandleStateChanges, store, subscription, childPropsSelector, lastWrapperProps, lastChildProps, renderIsScheduled, childPropsFromStoreUpdate, notifyNestedSubs, forceComponentUpdateDispatch], [store, subscription, childPropsSelector]); // Now that all that's done, we can finally try to actually render the child component.\n      // We memoize the elements for the rendered child component as an optimization.\n\n      var renderedWrappedComponent = useMemo(function () {\n        return /*#__PURE__*/React.createElement(WrappedComponent, _extends({}, actualChildProps, {\n          ref: reactReduxForwardedRef\n        }));\n      }, [reactReduxForwardedRef, WrappedComponent, actualChildProps]); // If React sees the exact same element reference as last time, it bails out of re-rendering\n      // that child, same as if it was wrapped in React.memo() or returned false from shouldComponentUpdate.\n\n      var renderedChild = useMemo(function () {\n        if (shouldHandleStateChanges) {\n          // If this component is subscribed to store updates, we need to pass its own\n          // subscription instance down to our descendants. That means rendering the same\n          // Context instance, and putting a different value into the context.\n          return /*#__PURE__*/React.createElement(ContextToUse.Provider, {\n            value: overriddenContextValue\n          }, renderedWrappedComponent);\n        }\n\n        return renderedWrappedComponent;\n      }, [ContextToUse, renderedWrappedComponent, overriddenContextValue]);\n      return renderedChild;\n    } // If we're in \"pure\" mode, ensure our wrapper component only re-renders when incoming props have changed.\n\n\n    var Connect = pure ? React.memo(ConnectFunction) : ConnectFunction;\n    Connect.WrappedComponent = WrappedComponent;\n    Connect.displayName = ConnectFunction.displayName = displayName;\n\n    if (forwardRef) {\n      var forwarded = React.forwardRef(function forwardConnectRef(props, ref) {\n        return /*#__PURE__*/React.createElement(Connect, _extends({}, props, {\n          reactReduxForwardedRef: ref\n        }));\n      });\n      forwarded.displayName = displayName;\n      forwarded.WrappedComponent = WrappedComponent;\n      return hoistStatics(forwarded, WrappedComponent);\n    }\n\n    return hoistStatics(Connect, WrappedComponent);\n  };\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,IAAIC,SAAS,GAAG,CAAC,gBAAgB,EAAE,YAAY,EAAE,iBAAiB,EAAE,0BAA0B,EAAE,UAAU,EAAE,SAAS,EAAE,YAAY,EAAE,SAAS,CAAC;EAC3IC,UAAU,GAAG,CAAC,wBAAwB,CAAC;AAC3C,OAAOC,YAAY,MAAM,yBAAyB;AAClD,OAAOC,KAAK,IAAIC,UAAU,EAAEC,OAAO,EAAEC,MAAM,EAAEC,UAAU,QAAQ,OAAO;AACtE,SAASC,kBAAkB,EAAEC,iBAAiB,QAAQ,UAAU;AAChE,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,yBAAyB,QAAQ,oCAAoC;AAC9E,SAASC,iBAAiB,QAAQ,WAAW,CAAC,CAAC;;AAE/C,IAAIC,WAAW,GAAG,EAAE;AACpB,IAAIC,qBAAqB,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC;AAExC,IAAIC,kBAAkB,GAAG,SAASA,kBAAkBA,CAACC,IAAI,EAAE;EACzD,IAAI;IACF,OAAOC,IAAI,CAACC,SAAS,CAACF,IAAI,CAAC;EAC7B,CAAC,CAAC,OAAOG,GAAG,EAAE;IACZ,OAAOC,MAAM,CAACJ,IAAI,CAAC;EACrB;AACF,CAAC;AAED,SAASK,wBAAwBA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAC/C,IAAIC,WAAW,GAAGF,KAAK,CAAC,CAAC,CAAC;EAC1B,OAAO,CAACC,MAAM,CAACE,OAAO,EAAED,WAAW,GAAG,CAAC,CAAC;AAC1C;AAEA,SAASE,iCAAiCA,CAACC,UAAU,EAAEC,UAAU,EAAEC,YAAY,EAAE;EAC/ElB,yBAAyB,CAAC,YAAY;IACpC,OAAOgB,UAAU,CAACG,KAAK,CAAC,KAAK,CAAC,EAAEF,UAAU,CAAC;EAC7C,CAAC,EAAEC,YAAY,CAAC;AAClB;AAEA,SAASE,mBAAmBA,CAACC,gBAAgB,EAAEC,cAAc,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,yBAAyB,EAAEC,gBAAgB,EAAE;EAC7J;EACAN,gBAAgB,CAACO,OAAO,GAAGJ,YAAY;EACvCF,cAAc,CAACM,OAAO,GAAGH,gBAAgB;EACzCF,iBAAiB,CAACK,OAAO,GAAG,KAAK,CAAC,CAAC;;EAEnC,IAAIF,yBAAyB,CAACE,OAAO,EAAE;IACrCF,yBAAyB,CAACE,OAAO,GAAG,IAAI;IACxCD,gBAAgB,CAAC,CAAC;EACpB;AACF;AAEA,SAASE,gBAAgBA,CAACC,wBAAwB,EAAEC,KAAK,EAAEC,YAAY,EAAEC,kBAAkB,EAAEZ,gBAAgB,EAAEC,cAAc,EAAEC,iBAAiB,EAAEG,yBAAyB,EAAEC,gBAAgB,EAAEO,4BAA4B,EAAE;EAC3N;EACA,IAAI,CAACJ,wBAAwB,EAAE,OAAO,CAAC;;EAEvC,IAAIK,cAAc,GAAG,KAAK;EAC1B,IAAIC,eAAe,GAAG,IAAI,CAAC,CAAC;;EAE5B,IAAIC,eAAe,GAAG,SAASA,eAAeA,CAAA,EAAG;IAC/C,IAAIF,cAAc,EAAE;MAClB;MACA;MACA;IACF;IAEA,IAAIG,gBAAgB,GAAGP,KAAK,CAACQ,QAAQ,CAAC,CAAC;IACvC,IAAIC,aAAa,EAAEC,KAAK;IAExB,IAAI;MACF;MACA;MACAD,aAAa,GAAGP,kBAAkB,CAACK,gBAAgB,EAAEjB,gBAAgB,CAACO,OAAO,CAAC;IAChF,CAAC,CAAC,OAAOc,CAAC,EAAE;MACVD,KAAK,GAAGC,CAAC;MACTN,eAAe,GAAGM,CAAC;IACrB;IAEA,IAAI,CAACD,KAAK,EAAE;MACVL,eAAe,GAAG,IAAI;IACxB,CAAC,CAAC;;IAGF,IAAII,aAAa,KAAKlB,cAAc,CAACM,OAAO,EAAE;MAC5C,IAAI,CAACL,iBAAiB,CAACK,OAAO,EAAE;QAC9BD,gBAAgB,CAAC,CAAC;MACpB;IACF,CAAC,MAAM;MACL;MACA;MACA;MACA;MACAL,cAAc,CAACM,OAAO,GAAGY,aAAa;MACtCd,yBAAyB,CAACE,OAAO,GAAGY,aAAa;MACjDjB,iBAAiB,CAACK,OAAO,GAAG,IAAI,CAAC,CAAC;;MAElCM,4BAA4B,CAAC;QAC3BS,IAAI,EAAE,eAAe;QACrB7B,OAAO,EAAE;UACP2B,KAAK,EAAEA;QACT;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC,CAAC;;EAGHT,YAAY,CAACY,aAAa,GAAGP,eAAe;EAC5CL,YAAY,CAACa,YAAY,CAAC,CAAC,CAAC,CAAC;EAC7B;;EAEAR,eAAe,CAAC,CAAC;EAEjB,IAAIS,kBAAkB,GAAG,SAASA,kBAAkBA,CAAA,EAAG;IACrDX,cAAc,GAAG,IAAI;IACrBH,YAAY,CAACe,cAAc,CAAC,CAAC;IAC7Bf,YAAY,CAACY,aAAa,GAAG,IAAI;IAEjC,IAAIR,eAAe,EAAE;MACnB;MACA;MACA;MACA;MACA;MACA,MAAMA,eAAe;IACvB;EACF,CAAC;EAED,OAAOU,kBAAkB;AAC3B;AAEA,IAAIE,gBAAgB,GAAG,SAASA,gBAAgBA,CAAA,EAAG;EACjD,OAAO,CAAC,IAAI,EAAE,CAAC,CAAC;AAClB,CAAC;AAED,eAAe,SAASC,eAAeA;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACAC,eAAe;AAAE;AACjBC,IAAI,EAAE;EACJ,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;IACnBA,IAAI,GAAG,CAAC,CAAC;EACX;EAEA,IAAIC,KAAK,GAAGD,IAAI;IACZE,oBAAoB,GAAGD,KAAK,CAACE,cAAc;IAC3CA,cAAc,GAAGD,oBAAoB,KAAK,KAAK,CAAC,GAAG,UAAUE,IAAI,EAAE;MACrE,OAAO,kBAAkB,GAAGA,IAAI,GAAG,GAAG;IACxC,CAAC,GAAGF,oBAAoB;IACpBG,gBAAgB,GAAGJ,KAAK,CAACK,UAAU;IACnCA,UAAU,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,iBAAiB,GAAGA,gBAAgB;IAC/EE,qBAAqB,GAAGN,KAAK,CAACO,eAAe;IAC7CA,eAAe,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAGE,SAAS,GAAGF,qBAAqB;IACtFG,qBAAqB,GAAGT,KAAK,CAACtB,wBAAwB;IACtDA,wBAAwB,GAAG+B,qBAAqB,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,qBAAqB;IAC1FC,cAAc,GAAGV,KAAK,CAACW,QAAQ;IAC/BA,QAAQ,GAAGD,cAAc,KAAK,KAAK,CAAC,GAAG,OAAO,GAAGA,cAAc;IAC/DE,aAAa,GAAGZ,KAAK,CAACa,OAAO;IAC7BA,OAAO,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,aAAa;IAC1DE,gBAAgB,GAAGd,KAAK,CAACe,UAAU;IACnCA,UAAU,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,gBAAgB;IACnEE,aAAa,GAAGhB,KAAK,CAACiB,OAAO;IAC7BA,OAAO,GAAGD,aAAa,KAAK,KAAK,CAAC,GAAGnE,iBAAiB,GAAGmE,aAAa;IACtEE,cAAc,GAAGlF,6BAA6B,CAACgE,KAAK,EAAE/D,SAAS,CAAC;EAEpE,IAAIkF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAId,eAAe,KAAKC,SAAS,EAAE;MACjC,MAAM,IAAIc,KAAK,CAAC,0GAA0G,CAAC;IAC7H;IAEA,IAAIT,OAAO,EAAE;MACX,MAAM,IAAIS,KAAK,CAAC,0FAA0F,CAAC;IAC7G;IAEA,IAAIC,yBAAyB,GAAG,0FAA0F,GAAG,sGAAsG,GAAG,8FAA8F,GAAG,6DAA6D;IAEpY,IAAIZ,QAAQ,KAAK,OAAO,EAAE;MACxB,MAAM,IAAIW,KAAK,CAAC,sDAAsD,GAAGC,yBAAyB,CAAC;IACrG;EACF;EAEA,IAAIC,OAAO,GAAGP,OAAO;EACrB,OAAO,SAASQ,eAAeA,CAACC,gBAAgB,EAAE;IAChD,IAAIP,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAAC5E,kBAAkB,CAACiF,gBAAgB,CAAC,EAAE;MAClF,MAAM,IAAIJ,KAAK,CAAC,wDAAwD,IAAIjB,UAAU,GAAG,qBAAqB,GAAGrD,kBAAkB,CAAC0E,gBAAgB,CAAC,CAAC,CAAC;IACzJ;IAEA,IAAIC,oBAAoB,GAAGD,gBAAgB,CAACE,WAAW,IAAIF,gBAAgB,CAACvB,IAAI,IAAI,WAAW;IAC/F,IAAIyB,WAAW,GAAG1B,cAAc,CAACyB,oBAAoB,CAAC;IAEtD,IAAIE,sBAAsB,GAAG9F,QAAQ,CAAC,CAAC,CAAC,EAAEmF,cAAc,EAAE;MACxDhB,cAAc,EAAEA,cAAc;MAC9BG,UAAU,EAAEA,UAAU;MACtBE,eAAe,EAAEA,eAAe;MAChC7B,wBAAwB,EAAEA,wBAAwB;MAClDiC,QAAQ,EAAEA,QAAQ;MAClBiB,WAAW,EAAEA,WAAW;MACxBD,oBAAoB,EAAEA,oBAAoB;MAC1CD,gBAAgB,EAAEA;IACpB,CAAC,CAAC;IAEF,IAAII,IAAI,GAAGZ,cAAc,CAACY,IAAI;IAE9B,SAASC,mBAAmBA,CAACpD,KAAK,EAAE;MAClC,OAAOmB,eAAe,CAACnB,KAAK,CAACqD,QAAQ,EAAEH,sBAAsB,CAAC;IAChE,CAAC,CAAC;IACF;IACA;;IAGA,IAAII,eAAe,GAAGH,IAAI,GAAGxF,OAAO,GAAG,UAAU4F,QAAQ,EAAE;MACzD,OAAOA,QAAQ,CAAC,CAAC;IACnB,CAAC;IAED,SAASC,eAAeA,CAACC,KAAK,EAAE;MAC9B,IAAIC,QAAQ,GAAG/F,OAAO,CAAC,YAAY;UACjC;UACA;UACA;UACA,IAAIgG,sBAAsB,GAAGF,KAAK,CAACE,sBAAsB;YACrDlE,YAAY,GAAGpC,6BAA6B,CAACoG,KAAK,EAAElG,UAAU,CAAC;UAEnE,OAAO,CAACkG,KAAK,CAACnB,OAAO,EAAEqB,sBAAsB,EAAElE,YAAY,CAAC;QAC9D,CAAC,EAAE,CAACgE,KAAK,CAAC,CAAC;QACPG,YAAY,GAAGF,QAAQ,CAAC,CAAC,CAAC;QAC1BC,sBAAsB,GAAGD,QAAQ,CAAC,CAAC,CAAC;QACpCjE,YAAY,GAAGiE,QAAQ,CAAC,CAAC,CAAC;MAE9B,IAAIG,YAAY,GAAGlG,OAAO,CAAC,YAAY;QACrC;QACA;QACA,OAAOiG,YAAY,IAAIA,YAAY,CAACE,QAAQ,IAAI/F,iBAAiB,CAAE,aAAaN,KAAK,CAACsG,aAAa,CAACH,YAAY,CAACE,QAAQ,EAAE,IAAI,CAAC,CAAC,GAAGF,YAAY,GAAGf,OAAO;MAC5J,CAAC,EAAE,CAACe,YAAY,EAAEf,OAAO,CAAC,CAAC,CAAC,CAAC;;MAE7B,IAAImB,YAAY,GAAGtG,UAAU,CAACmG,YAAY,CAAC,CAAC,CAAC;MAC7C;MACA;;MAEA,IAAII,qBAAqB,GAAGC,OAAO,CAACT,KAAK,CAACzD,KAAK,CAAC,IAAIkE,OAAO,CAACT,KAAK,CAACzD,KAAK,CAACQ,QAAQ,CAAC,IAAI0D,OAAO,CAACT,KAAK,CAACzD,KAAK,CAACqD,QAAQ,CAAC;MAClH,IAAIc,uBAAuB,GAAGD,OAAO,CAACF,YAAY,CAAC,IAAIE,OAAO,CAACF,YAAY,CAAChE,KAAK,CAAC;MAElF,IAAIwC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAACuB,qBAAqB,IAAI,CAACE,uBAAuB,EAAE;QAC/F,MAAM,IAAIxB,KAAK,CAAC,6CAA6C,IAAI,IAAI,GAAGM,WAAW,GAAG,sDAAsD,CAAC,GAAG,8EAA8E,IAAI,4BAA4B,GAAGA,WAAW,GAAG,sBAAsB,CAAC,CAAC;MACzS,CAAC,CAAC;;MAGF,IAAIjD,KAAK,GAAGiE,qBAAqB,GAAGR,KAAK,CAACzD,KAAK,GAAGgE,YAAY,CAAChE,KAAK;MACpE,IAAIE,kBAAkB,GAAGvC,OAAO,CAAC,YAAY;QAC3C;QACA;QACA,OAAOyF,mBAAmB,CAACpD,KAAK,CAAC;MACnC,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;MAEX,IAAIoE,SAAS,GAAGzG,OAAO,CAAC,YAAY;UAClC,IAAI,CAACoC,wBAAwB,EAAE,OAAO3B,qBAAqB,CAAC,CAAC;UAC7D;;UAEA;UACA;UACA,IAAI6B,YAAY,GAAGjC,kBAAkB,CAACgC,KAAK,EAAEiE,qBAAqB,GAAG,IAAI,GAAGD,YAAY,CAAC/D,YAAY,CAAC,CAAC,CAAC;UACxG;UACA;UACA;;UAEA;UACA;UACA;UACA;UACA,IAAIL,gBAAgB,GAAGK,YAAY,CAACL,gBAAgB,CAACyE,IAAI,CAACpE,YAAY,CAAC;UACvE,OAAO,CAACA,YAAY,EAAEL,gBAAgB,CAAC;QACzC,CAAC,EAAE,CAACI,KAAK,EAAEiE,qBAAqB,EAAED,YAAY,CAAC,CAAC;QAC5C/D,YAAY,GAAGmE,SAAS,CAAC,CAAC,CAAC;QAC3BxE,gBAAgB,GAAGwE,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MACrC;;MAGA,IAAIE,sBAAsB,GAAG3G,OAAO,CAAC,YAAY;QAC/C,IAAIsG,qBAAqB,EAAE;UACzB;UACA;UACA;UACA,OAAOD,YAAY;QACrB,CAAC,CAAC;QACF;;QAGA,OAAO5G,QAAQ,CAAC,CAAC,CAAC,EAAE4G,YAAY,EAAE;UAChC/D,YAAY,EAAEA;QAChB,CAAC,CAAC;MACJ,CAAC,EAAE,CAACgE,qBAAqB,EAAED,YAAY,EAAE/D,YAAY,CAAC,CAAC,CAAC,CAAC;MACzD;;MAEA,IAAIsE,WAAW,GAAG1G,UAAU,CAACc,wBAAwB,EAAER,WAAW,EAAE8C,gBAAgB,CAAC;QACjFuD,YAAY,GAAGD,WAAW,CAAC,CAAC,CAAC;QAC7BE,yBAAyB,GAAGD,YAAY,CAAC,CAAC,CAAC;QAC3CrE,4BAA4B,GAAGoE,WAAW,CAAC,CAAC,CAAC,CAAC,CAAC;;MAGnD,IAAIE,yBAAyB,IAAIA,yBAAyB,CAAC/D,KAAK,EAAE;QAChE,MAAM+D,yBAAyB,CAAC/D,KAAK;MACvC,CAAC,CAAC;;MAGF,IAAInB,cAAc,GAAG3B,MAAM,CAAC,CAAC;MAC7B,IAAI0B,gBAAgB,GAAG1B,MAAM,CAAC6B,YAAY,CAAC;MAC3C,IAAIE,yBAAyB,GAAG/B,MAAM,CAAC,CAAC;MACxC,IAAI4B,iBAAiB,GAAG5B,MAAM,CAAC,KAAK,CAAC;MACrC,IAAI8B,gBAAgB,GAAG4D,eAAe,CAAC,YAAY;QACjD;QACA;QACA;QACA;QACA;QACA;QACA,IAAI3D,yBAAyB,CAACE,OAAO,IAAIJ,YAAY,KAAKH,gBAAgB,CAACO,OAAO,EAAE;UAClF,OAAOF,yBAAyB,CAACE,OAAO;QAC1C,CAAC,CAAC;QACF;QACA;QACA;;QAGA,OAAOK,kBAAkB,CAACF,KAAK,CAACQ,QAAQ,CAAC,CAAC,EAAEf,YAAY,CAAC;MAC3D,CAAC,EAAE,CAACO,KAAK,EAAEyE,yBAAyB,EAAEhF,YAAY,CAAC,CAAC,CAAC,CAAC;MACtD;MACA;;MAEAT,iCAAiC,CAACK,mBAAmB,EAAE,CAACC,gBAAgB,EAAEC,cAAc,EAAEC,iBAAiB,EAAEC,YAAY,EAAEC,gBAAgB,EAAEC,yBAAyB,EAAEC,gBAAgB,CAAC,CAAC,CAAC,CAAC;;MAE5LZ,iCAAiC,CAACc,gBAAgB,EAAE,CAACC,wBAAwB,EAAEC,KAAK,EAAEC,YAAY,EAAEC,kBAAkB,EAAEZ,gBAAgB,EAAEC,cAAc,EAAEC,iBAAiB,EAAEG,yBAAyB,EAAEC,gBAAgB,EAAEO,4BAA4B,CAAC,EAAE,CAACH,KAAK,EAAEC,YAAY,EAAEC,kBAAkB,CAAC,CAAC,CAAC,CAAC;MACrS;;MAEA,IAAIwE,wBAAwB,GAAG/G,OAAO,CAAC,YAAY;QACjD,OAAO,aAAaF,KAAK,CAACsG,aAAa,CAAChB,gBAAgB,EAAE3F,QAAQ,CAAC,CAAC,CAAC,EAAEsC,gBAAgB,EAAE;UACvFiF,GAAG,EAAEhB;QACP,CAAC,CAAC,CAAC;MACL,CAAC,EAAE,CAACA,sBAAsB,EAAEZ,gBAAgB,EAAErD,gBAAgB,CAAC,CAAC,CAAC,CAAC;MAClE;;MAEA,IAAIkF,aAAa,GAAGjH,OAAO,CAAC,YAAY;QACtC,IAAIoC,wBAAwB,EAAE;UAC5B;UACA;UACA;UACA,OAAO,aAAatC,KAAK,CAACsG,aAAa,CAACF,YAAY,CAACgB,QAAQ,EAAE;YAC7DC,KAAK,EAAER;UACT,CAAC,EAAEI,wBAAwB,CAAC;QAC9B;QAEA,OAAOA,wBAAwB;MACjC,CAAC,EAAE,CAACb,YAAY,EAAEa,wBAAwB,EAAEJ,sBAAsB,CAAC,CAAC;MACpE,OAAOM,aAAa;IACtB,CAAC,CAAC;;IAGF,IAAIG,OAAO,GAAG5B,IAAI,GAAG1F,KAAK,CAACuH,IAAI,CAACxB,eAAe,CAAC,GAAGA,eAAe;IAClEuB,OAAO,CAAChC,gBAAgB,GAAGA,gBAAgB;IAC3CgC,OAAO,CAAC9B,WAAW,GAAGO,eAAe,CAACP,WAAW,GAAGA,WAAW;IAE/D,IAAIb,UAAU,EAAE;MACd,IAAI6C,SAAS,GAAGxH,KAAK,CAAC2E,UAAU,CAAC,SAAS8C,iBAAiBA,CAACzB,KAAK,EAAEkB,GAAG,EAAE;QACtE,OAAO,aAAalH,KAAK,CAACsG,aAAa,CAACgB,OAAO,EAAE3H,QAAQ,CAAC,CAAC,CAAC,EAAEqG,KAAK,EAAE;UACnEE,sBAAsB,EAAEgB;QAC1B,CAAC,CAAC,CAAC;MACL,CAAC,CAAC;MACFM,SAAS,CAAChC,WAAW,GAAGA,WAAW;MACnCgC,SAAS,CAAClC,gBAAgB,GAAGA,gBAAgB;MAC7C,OAAOvF,YAAY,CAACyH,SAAS,EAAElC,gBAAgB,CAAC;IAClD;IAEA,OAAOvF,YAAY,CAACuH,OAAO,EAAEhC,gBAAgB,CAAC;EAChD,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}