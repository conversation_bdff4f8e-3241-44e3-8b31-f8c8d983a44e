{"ast": null, "code": "import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nconst COMPONENT_NAME = 'Tabs';\nexport function getTabsUtilityClass(slot) {\n  return generateUtilityClass(COMPONENT_NAME, slot);\n}\nexport const tabsClasses = generateUtilityClasses(COMPONENT_NAME, ['root', 'horizontal', 'vertical']);", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "COMPONENT_NAME", "getTabsUtilityClass", "slot", "tabsClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/Tabs/tabsClasses.js"], "sourcesContent": ["import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nconst COMPONENT_NAME = 'Tabs';\nexport function getTabsUtilityClass(slot) {\n  return generateUtilityClass(COMPONENT_NAME, slot);\n}\nexport const tabsClasses = generateUtilityClasses(COMPONENT_NAME, ['root', 'horizontal', 'vertical']);"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,MAAMC,cAAc,GAAG,MAAM;AAC7B,OAAO,SAASC,mBAAmBA,CAACC,IAAI,EAAE;EACxC,OAAOJ,oBAAoB,CAACE,cAAc,EAAEE,IAAI,CAAC;AACnD;AACA,OAAO,MAAMC,WAAW,GAAGJ,sBAAsB,CAACC,cAAc,EAAE,CAAC,MAAM,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}