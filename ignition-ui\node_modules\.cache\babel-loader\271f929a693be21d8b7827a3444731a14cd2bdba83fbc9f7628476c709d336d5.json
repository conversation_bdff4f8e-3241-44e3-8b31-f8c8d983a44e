{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"component\", \"disableColorScheme\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport useThemeProps from '../styles/useThemeProps';\nimport styled from '../styles/styled';\nimport { getScopedCssBaselineUtilityClass } from './scopedCssBaselineClasses';\nimport useSlot from '../utils/useSlot';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = () => {\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getScopedCssBaselineUtilityClass, {});\n};\nconst ScopedCssBaselineRoot = styled('div', {\n  name: 'JoyScopedCssBaseline',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(_ref => {\n  let {\n    theme,\n    ownerState\n  } = _ref;\n  const colorSchemeStyles = {};\n  if (!ownerState.disableColorScheme && theme.colorSchemes) {\n    Object.entries(theme.colorSchemes).forEach(_ref2 => {\n      let [key, scheme] = _ref2;\n      var _scheme$palette;\n      colorSchemeStyles[`&${theme.getColorSchemeSelector(key).replace(/\\s*&/, '')}`] = {\n        colorScheme: (_scheme$palette = scheme.palette) == null ? void 0 : _scheme$palette.mode\n      };\n    });\n  }\n  return _extends({\n    WebkitFontSmoothing: 'antialiased',\n    MozOsxFontSmoothing: 'grayscale',\n    // Change from `box-sizing: content-box` so that `width`\n    // is not affected by `padding` or `border`.\n    boxSizing: 'border-box',\n    // Fix font resize problem in iOS\n    WebkitTextSizeAdjust: '100%',\n    color: theme.vars.palette.text.primary\n  }, theme.typography['body-md'], {\n    backgroundColor: theme.vars.palette.background.body,\n    '@media print': {\n      // Save printer ink.\n      backgroundColor: '#fff'\n    },\n    '& *, & *::before, & *::after': {\n      boxSizing: 'inherit'\n    },\n    '& strong, & b': {\n      fontWeight: 'bold'\n    }\n  }, colorSchemeStyles);\n});\n/**\n *\n * Demos:\n *\n * - [CSS Baseline](https://mui.com/joy-ui/react-css-baseline/)\n *\n * API:\n *\n * - [ScopedCssBaseline API](https://mui.com/joy-ui/api/scoped-css-baseline/)\n */\nconst ScopedCssBaseline = /*#__PURE__*/React.forwardRef(function ScopedCssBaseline(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyScopedCssBaseline'\n  });\n  const {\n      className,\n      component = 'div',\n      disableColorScheme = false,\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    component,\n    disableColorScheme\n  });\n  const classes = useUtilityClasses();\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    className: clsx(classes.root, className),\n    elementType: ScopedCssBaselineRoot,\n    externalForwardedProps,\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(SlotRoot, _extends({}, rootProps));\n});\nprocess.env.NODE_ENV !== \"production\" ? ScopedCssBaseline.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * You can wrap a node.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Disable `color-scheme` CSS property.\n   * For more details, check out https://developer.mozilla.org/en-US/docs/Web/CSS/color-scheme\n   * For browser support, check out https://caniuse.com/?search=color-scheme\n   * @default false\n   */\n  disableColorScheme: PropTypes.bool,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default ScopedCssBaseline;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "unstable_composeClasses", "composeClasses", "useThemeProps", "styled", "getScopedCssBaselineUtilityClass", "useSlot", "jsx", "_jsx", "useUtilityClasses", "slots", "root", "ScopedCssBaselineRoot", "name", "slot", "overridesResolver", "props", "styles", "_ref", "theme", "ownerState", "colorSchemeStyles", "disableColorScheme", "colorSchemes", "Object", "entries", "for<PERSON>ach", "_ref2", "key", "scheme", "_scheme$palette", "getColorSchemeSelector", "replace", "colorScheme", "palette", "mode", "WebkitFontSmoothing", "MozOsxFontSmoothing", "boxSizing", "WebkitTextSizeAdjust", "color", "vars", "text", "primary", "typography", "backgroundColor", "background", "body", "fontWeight", "ScopedCssBaseline", "forwardRef", "inProps", "ref", "className", "component", "slotProps", "other", "classes", "externalForwardedProps", "SlotRoot", "rootProps", "elementType", "process", "env", "NODE_ENV", "propTypes", "children", "node", "string", "bool", "shape", "oneOfType", "func", "object", "sx", "arrayOf"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/ScopedCssBaseline/ScopedCssBaseline.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"component\", \"disableColorScheme\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport useThemeProps from '../styles/useThemeProps';\nimport styled from '../styles/styled';\nimport { getScopedCssBaselineUtilityClass } from './scopedCssBaselineClasses';\nimport useSlot from '../utils/useSlot';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = () => {\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getScopedCssBaselineUtilityClass, {});\n};\nconst ScopedCssBaselineRoot = styled('div', {\n  name: 'JoyScopedCssBaseline',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(({\n  theme,\n  ownerState\n}) => {\n  const colorSchemeStyles = {};\n  if (!ownerState.disableColorScheme && theme.colorSchemes) {\n    Object.entries(theme.colorSchemes).forEach(([key, scheme]) => {\n      var _scheme$palette;\n      colorSchemeStyles[`&${theme.getColorSchemeSelector(key).replace(/\\s*&/, '')}`] = {\n        colorScheme: (_scheme$palette = scheme.palette) == null ? void 0 : _scheme$palette.mode\n      };\n    });\n  }\n  return _extends({\n    WebkitFontSmoothing: 'antialiased',\n    MozOsxFontSmoothing: 'grayscale',\n    // Change from `box-sizing: content-box` so that `width`\n    // is not affected by `padding` or `border`.\n    boxSizing: 'border-box',\n    // Fix font resize problem in iOS\n    WebkitTextSizeAdjust: '100%',\n    color: theme.vars.palette.text.primary\n  }, theme.typography['body-md'], {\n    backgroundColor: theme.vars.palette.background.body,\n    '@media print': {\n      // Save printer ink.\n      backgroundColor: '#fff'\n    },\n    '& *, & *::before, & *::after': {\n      boxSizing: 'inherit'\n    },\n    '& strong, & b': {\n      fontWeight: 'bold'\n    }\n  }, colorSchemeStyles);\n});\n/**\n *\n * Demos:\n *\n * - [CSS Baseline](https://mui.com/joy-ui/react-css-baseline/)\n *\n * API:\n *\n * - [ScopedCssBaseline API](https://mui.com/joy-ui/api/scoped-css-baseline/)\n */\nconst ScopedCssBaseline = /*#__PURE__*/React.forwardRef(function ScopedCssBaseline(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyScopedCssBaseline'\n  });\n  const {\n      className,\n      component = 'div',\n      disableColorScheme = false,\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    component,\n    disableColorScheme\n  });\n  const classes = useUtilityClasses();\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    className: clsx(classes.root, className),\n    elementType: ScopedCssBaselineRoot,\n    externalForwardedProps,\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(SlotRoot, _extends({}, rootProps));\n});\nprocess.env.NODE_ENV !== \"production\" ? ScopedCssBaseline.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * You can wrap a node.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Disable `color-scheme` CSS property.\n   * For more details, check out https://developer.mozilla.org/en-US/docs/Web/CSS/color-scheme\n   * For browser support, check out https://caniuse.com/?search=color-scheme\n   * @default false\n   */\n  disableColorScheme: PropTypes.bool,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default ScopedCssBaseline;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,oBAAoB,EAAE,OAAO,EAAE,WAAW,CAAC;AACxF,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,WAAW;AACrE,OAAOC,aAAa,MAAM,yBAAyB;AACnD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,gCAAgC,QAAQ,4BAA4B;AAC7E,OAAOC,OAAO,MAAM,kBAAkB;AACtC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAC9B,MAAMC,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,OAAOT,cAAc,CAACQ,KAAK,EAAEL,gCAAgC,EAAE,CAAC,CAAC,CAAC;AACpE,CAAC;AACD,MAAMO,qBAAqB,GAAGR,MAAM,CAAC,KAAK,EAAE;EAC1CS,IAAI,EAAE,sBAAsB;EAC5BC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC/C,CAAC,CAAC,CAACO,IAAA,IAGG;EAAA,IAHF;IACFC,KAAK;IACLC;EACF,CAAC,GAAAF,IAAA;EACC,MAAMG,iBAAiB,GAAG,CAAC,CAAC;EAC5B,IAAI,CAACD,UAAU,CAACE,kBAAkB,IAAIH,KAAK,CAACI,YAAY,EAAE;IACxDC,MAAM,CAACC,OAAO,CAACN,KAAK,CAACI,YAAY,CAAC,CAACG,OAAO,CAACC,KAAA,IAAmB;MAAA,IAAlB,CAACC,GAAG,EAAEC,MAAM,CAAC,GAAAF,KAAA;MACvD,IAAIG,eAAe;MACnBT,iBAAiB,CAAC,IAAIF,KAAK,CAACY,sBAAsB,CAACH,GAAG,CAAC,CAACI,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG;QAC/EC,WAAW,EAAE,CAACH,eAAe,GAAGD,MAAM,CAACK,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGJ,eAAe,CAACK;MACrF,CAAC;IACH,CAAC,CAAC;EACJ;EACA,OAAOvC,QAAQ,CAAC;IACdwC,mBAAmB,EAAE,aAAa;IAClCC,mBAAmB,EAAE,WAAW;IAChC;IACA;IACAC,SAAS,EAAE,YAAY;IACvB;IACAC,oBAAoB,EAAE,MAAM;IAC5BC,KAAK,EAAErB,KAAK,CAACsB,IAAI,CAACP,OAAO,CAACQ,IAAI,CAACC;EACjC,CAAC,EAAExB,KAAK,CAACyB,UAAU,CAAC,SAAS,CAAC,EAAE;IAC9BC,eAAe,EAAE1B,KAAK,CAACsB,IAAI,CAACP,OAAO,CAACY,UAAU,CAACC,IAAI;IACnD,cAAc,EAAE;MACd;MACAF,eAAe,EAAE;IACnB,CAAC;IACD,8BAA8B,EAAE;MAC9BP,SAAS,EAAE;IACb,CAAC;IACD,eAAe,EAAE;MACfU,UAAU,EAAE;IACd;EACF,CAAC,EAAE3B,iBAAiB,CAAC;AACvB,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4B,iBAAiB,GAAG,aAAanD,KAAK,CAACoD,UAAU,CAAC,SAASD,iBAAiBA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC/F,MAAMpC,KAAK,GAAGb,aAAa,CAAC;IAC1Ba,KAAK,EAAEmC,OAAO;IACdtC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFwC,SAAS;MACTC,SAAS,GAAG,KAAK;MACjBhC,kBAAkB,GAAG,KAAK;MAC1BZ,KAAK,GAAG,CAAC,CAAC;MACV6C,SAAS,GAAG,CAAC;IACf,CAAC,GAAGvC,KAAK;IACTwC,KAAK,GAAG7D,6BAA6B,CAACqB,KAAK,EAAEnB,SAAS,CAAC;EACzD,MAAMuB,UAAU,GAAGxB,QAAQ,CAAC,CAAC,CAAC,EAAEoB,KAAK,EAAE;IACrCsC,SAAS;IACThC;EACF,CAAC,CAAC;EACF,MAAMmC,OAAO,GAAGhD,iBAAiB,CAAC,CAAC;EACnC,MAAMiD,sBAAsB,GAAG9D,QAAQ,CAAC,CAAC,CAAC,EAAE4D,KAAK,EAAE;IACjDF,SAAS;IACT5C,KAAK;IACL6C;EACF,CAAC,CAAC;EACF,MAAM,CAACI,QAAQ,EAAEC,SAAS,CAAC,GAAGtD,OAAO,CAAC,MAAM,EAAE;IAC5C8C,GAAG;IACHC,SAAS,EAAErD,IAAI,CAACyD,OAAO,CAAC9C,IAAI,EAAE0C,SAAS,CAAC;IACxCQ,WAAW,EAAEjD,qBAAqB;IAClC8C,sBAAsB;IACtBtC;EACF,CAAC,CAAC;EACF,OAAO,aAAaZ,IAAI,CAACmD,QAAQ,EAAE/D,QAAQ,CAAC,CAAC,CAAC,EAAEgE,SAAS,CAAC,CAAC;AAC7D,CAAC,CAAC;AACFE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGf,iBAAiB,CAACgB,SAAS,CAAC,yBAAyB;EAC3F;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAEnE,SAAS,CAACoE,IAAI;EACxB;AACF;AACA;EACEd,SAAS,EAAEtD,SAAS,CAACqE,MAAM;EAC3B;AACF;AACA;AACA;EACEd,SAAS,EAAEvD,SAAS,CAAC8D,WAAW;EAChC;AACF;AACA;AACA;AACA;AACA;EACEvC,kBAAkB,EAAEvB,SAAS,CAACsE,IAAI;EAClC;AACF;AACA;AACA;EACEd,SAAS,EAAExD,SAAS,CAACuE,KAAK,CAAC;IACzB3D,IAAI,EAAEZ,SAAS,CAACwE,SAAS,CAAC,CAACxE,SAAS,CAACyE,IAAI,EAAEzE,SAAS,CAAC0E,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;EACE/D,KAAK,EAAEX,SAAS,CAACuE,KAAK,CAAC;IACrB3D,IAAI,EAAEZ,SAAS,CAAC8D;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACEa,EAAE,EAAE3E,SAAS,CAACwE,SAAS,CAAC,CAACxE,SAAS,CAAC4E,OAAO,CAAC5E,SAAS,CAACwE,SAAS,CAAC,CAACxE,SAAS,CAACyE,IAAI,EAAEzE,SAAS,CAAC0E,MAAM,EAAE1E,SAAS,CAACsE,IAAI,CAAC,CAAC,CAAC,EAAEtE,SAAS,CAACyE,IAAI,EAAEzE,SAAS,CAAC0E,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAexB,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}