{"ast": null, "code": "import * as React from 'react';\nconst ChipColorContext = /*#__PURE__*/React.createContext({\n  disabled: undefined,\n  variant: undefined,\n  color: undefined\n});\nif (process.env.NODE_ENV !== 'production') {\n  ChipColorContext.displayName = 'ChipColorContext';\n}\nexport default ChipColorContext;", "map": {"version": 3, "names": ["React", "ChipColorContext", "createContext", "disabled", "undefined", "variant", "color", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Chip/ChipContext.js"], "sourcesContent": ["import * as React from 'react';\nconst ChipColorContext = /*#__PURE__*/React.createContext({\n  disabled: undefined,\n  variant: undefined,\n  color: undefined\n});\nif (process.env.NODE_ENV !== 'production') {\n  ChipColorContext.displayName = 'ChipColorContext';\n}\nexport default ChipColorContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,gBAAgB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC;EACxDC,QAAQ,EAAEC,SAAS;EACnBC,OAAO,EAAED,SAAS;EAClBE,KAAK,EAAEF;AACT,CAAC,CAAC;AACF,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCR,gBAAgB,CAACS,WAAW,GAAG,kBAAkB;AACnD;AACA,eAAeT,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}