{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getAvatarUtilityClass(slot) {\n  return generateUtilityClass('MuiAvatar', slot);\n}\nconst avatarClasses = generateUtilityClasses('MuiAvatar', ['root', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'fallback', 'sizeSm', 'sizeMd', 'sizeLg', 'img', 'variantOutlined', 'variantSoft', 'variantSolid']);\nexport default avatarClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getAvatarUtilityClass", "slot", "avatarClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Avatar/avatarClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getAvatarUtilityClass(slot) {\n  return generateUtilityClass('MuiAvatar', slot);\n}\nconst avatarClasses = generateUtilityClasses('MuiAvatar', ['root', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'fallback', 'sizeSm', 'sizeMd', 'sizeLg', 'img', 'variantOutlined', 'variantSoft', 'variantSolid']);\nexport default avatarClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,qBAAqBA,CAACC,IAAI,EAAE;EAC1C,OAAOH,oBAAoB,CAAC,WAAW,EAAEG,IAAI,CAAC;AAChD;AACA,MAAMC,aAAa,GAAGH,sBAAsB,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,KAAK,EAAE,iBAAiB,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;AACrQ,eAAeG,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}