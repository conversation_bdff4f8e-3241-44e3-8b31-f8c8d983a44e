import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Chip,
  Avatar,
  Tooltip
} from '@mui/material';
import Iconify from 'components/Iconify/index';
import { mainYellowColor } from "helpers/constants";

const AccessManagement = ({ 
  planInfo, 
  userAccessLevel,
  onAddAccess,
  onUpdateAccess,
  onRemoveAccess 
}) => {
  const [addDialogOpen, setAddDialogOpen] = useState(false);
  const [newUserEmail, setNewUserEmail] = useState('');
  const [newAccessLevel, setNewAccessLevel] = useState('viewer');
  const [loading, setLoading] = useState(false);

  const accessLevels = planInfo?.access_levels || [];
  const isOwner = userAccessLevel?.access_level === 'owner';
  const isHeadOwner = userAccessLevel?.is_head_owner;

  const getAccessLevelColor = (level) => {
    switch (level) {
      case 'owner': return '#ef4444';
      case 'editor': return '#f97316';
      case 'viewer': return '#10b981';
      default: return '#6b7280';
    }
  };

  const getAccessLevelIcon = (level) => {
    switch (level) {
      case 'owner': return 'material-symbols:admin-panel-settings';
      case 'editor': return 'material-symbols:edit';
      case 'viewer': return 'material-symbols:visibility';
      default: return 'material-symbols:person';
    }
  };

  const handleAddAccess = async () => {
    if (!newUserEmail.trim()) return;
    
    setLoading(true);
    try {
      await onAddAccess(newUserEmail, newAccessLevel);
      setNewUserEmail('');
      setNewAccessLevel('viewer');
      setAddDialogOpen(false);
    } catch (error) {
      console.error('Error adding access:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateAccess = async (accessId, newLevel, makeHeadOwner = false) => {
    try {
      await onUpdateAccess(accessId, newLevel, makeHeadOwner);
    } catch (error) {
      console.error('Error updating access:', error);
    }
  };

  const handleRemoveAccess = async (accessId) => {
    try {
      await onRemoveAccess(accessId);
    } catch (error) {
      console.error('Error removing access:', error);
    }
  };

  const canManageUser = (access) => {
    if (access.is_head_owner) return false; // Cannot manage head owner
    if (access.access_level === 'owner' && !isHeadOwner) return false; // Only head owner can manage owners
    return isOwner; // Owners can manage editors and viewers
  };

  return (
    <Paper 
      elevation={0} 
      sx={{ 
        p: 3, 
        borderRadius: '12px',
        border: '1px solid #f0f0f0',
        backgroundColor: '#fff'
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography 
          variant="h6" 
          sx={{ 
            fontWeight: 600, 
            color: '#333',
            fontFamily: '"Recursive Variable", sans-serif',
            display: 'flex',
            alignItems: 'center',
            gap: 1
          }}
        >
          <Iconify icon="material-symbols:security" width={24} height={24} color={mainYellowColor} />
          Access Management
        </Typography>
        
        {isOwner && (
          <Button
            variant="contained"
            startIcon={<Iconify icon="material-symbols:person-add" />}
            onClick={() => setAddDialogOpen(true)}
            sx={{
              backgroundColor: mainYellowColor,
              color: '#fff',
              textTransform: 'none',
              fontFamily: '"Recursive Variable", sans-serif',
              '&:hover': {
                backgroundColor: '#e6940a'
              }
            }}
          >
            Add Member
          </Button>
        )}
      </Box>

      <List>
        {accessLevels.map((access) => (
          <ListItem 
            key={access.id}
            sx={{ 
              border: '1px solid #f0f0f0',
              borderRadius: '8px',
              mb: 1,
              backgroundColor: '#fafafa'
            }}
          >
            <Avatar
              src={access.user.avatar}
              alt={access.user.first_name || access.user.email}
              sx={{ mr: 2, bgcolor: mainYellowColor }}
            >
              {access.user.first_name 
                ? access.user.first_name.charAt(0).toUpperCase()
                : access.user.email.charAt(0).toUpperCase()
              }
            </Avatar>
            
            <ListItemText
              primary={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <Typography 
                    variant="subtitle1" 
                    sx={{ 
                      fontWeight: 600,
                      fontFamily: '"Recursive Variable", sans-serif'
                    }}
                  >
                    {access.user.first_name && access.user.last_name 
                      ? `${access.user.first_name} ${access.user.last_name}`
                      : access.user.email
                    }
                  </Typography>
                  {access.is_head_owner && (
                    <Chip
                      label="Head Owner"
                      size="small"
                      sx={{
                        backgroundColor: '#fef3c7',
                        color: '#92400e',
                        fontWeight: 600,
                        fontSize: '0.7rem'
                      }}
                    />
                  )}
                </Box>
              }
              secondary={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                  <Chip
                    icon={<Iconify icon={getAccessLevelIcon(access.access_level)} width={16} height={16} />}
                    label={access.access_level.charAt(0).toUpperCase() + access.access_level.slice(1)}
                    size="small"
                    sx={{
                      backgroundColor: `${getAccessLevelColor(access.access_level)}20`,
                      color: getAccessLevelColor(access.access_level),
                      fontWeight: 600,
                      fontFamily: '"Recursive Variable", sans-serif'
                    }}
                  />
                  <Typography 
                    variant="caption" 
                    sx={{ 
                      color: '#666',
                      fontFamily: '"Recursive Variable", sans-serif'
                    }}
                  >
                    {access.user.email}
                  </Typography>
                </Box>
              }
            />
            
            <ListItemSecondaryAction>
              {canManageUser(access) && (
                <Box sx={{ display: 'flex', gap: 1 }}>
                  {access.access_level === 'owner' && isHeadOwner && (
                    <Tooltip title="Transfer Head Owner">
                      <IconButton
                        size="small"
                        onClick={() => handleUpdateAccess(access.id, 'owner', true)}
                        sx={{ color: '#f59e0b' }}
                      >
                        <Iconify icon="material-symbols:crown" width={20} height={20} />
                      </IconButton>
                    </Tooltip>
                  )}
                  
                  <Tooltip title="Remove Access">
                    <IconButton
                      size="small"
                      onClick={() => handleRemoveAccess(access.id)}
                      sx={{ color: '#ef4444' }}
                    >
                      <Iconify icon="material-symbols:person-remove" width={20} height={20} />
                    </IconButton>
                  </Tooltip>
                </Box>
              )}
            </ListItemSecondaryAction>
          </ListItem>
        ))}
      </List>

      {/* Add Access Dialog */}
      <Dialog 
        open={addDialogOpen} 
        onClose={() => setAddDialogOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ fontFamily: '"Recursive Variable", sans-serif' }}>
          Add Team Member
        </DialogTitle>
        <DialogContent>
          <TextField
            autoFocus
            margin="dense"
            label="Email Address"
            type="email"
            fullWidth
            variant="outlined"
            value={newUserEmail}
            onChange={(e) => setNewUserEmail(e.target.value)}
            sx={{ mb: 2 }}
          />
          
          <FormControl fullWidth>
            <InputLabel>Access Level</InputLabel>
            <Select
              value={newAccessLevel}
              label="Access Level"
              onChange={(e) => setNewAccessLevel(e.target.value)}
            >
              <MenuItem value="viewer">Viewer - Can view plan content</MenuItem>
              <MenuItem value="editor">Editor - Can edit plan content</MenuItem>
              {isHeadOwner && (
                <MenuItem value="owner">Owner - Can manage team members</MenuItem>
              )}
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button 
            onClick={() => setAddDialogOpen(false)}
            sx={{ fontFamily: '"Recursive Variable", sans-serif' }}
          >
            Cancel
          </Button>
          <Button 
            onClick={handleAddAccess}
            variant="contained"
            disabled={loading || !newUserEmail.trim()}
            sx={{
              backgroundColor: mainYellowColor,
              fontFamily: '"Recursive Variable", sans-serif',
              '&:hover': { backgroundColor: '#e6940a' }
            }}
          >
            {loading ? 'Adding...' : 'Add Member'}
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};

export default AccessManagement;
