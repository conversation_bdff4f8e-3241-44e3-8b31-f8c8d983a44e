{"ast": null, "code": "export default function bindActionCreators(actionCreators, dispatch) {\n  var boundActionCreators = {};\n  var _loop = function _loop(key) {\n    var actionCreator = actionCreators[key];\n    if (typeof actionCreator === 'function') {\n      boundActionCreators[key] = function () {\n        return dispatch(actionCreator.apply(void 0, arguments));\n      };\n    }\n  };\n  for (var key in actionCreators) {\n    _loop(key);\n  }\n  return boundActionCreators;\n}", "map": {"version": 3, "names": ["bindActionCreators", "actionCreators", "dispatch", "boundActionCreators", "_loop", "key", "actionCreator", "apply", "arguments"], "sources": ["C:/ignition/ignition-ui/node_modules/react-beautiful-dnd/node_modules/react-redux/es/utils/bindActionCreators.js"], "sourcesContent": ["export default function bindActionCreators(actionCreators, dispatch) {\n  var boundActionCreators = {};\n\n  var _loop = function _loop(key) {\n    var actionCreator = actionCreators[key];\n\n    if (typeof actionCreator === 'function') {\n      boundActionCreators[key] = function () {\n        return dispatch(actionCreator.apply(void 0, arguments));\n      };\n    }\n  };\n\n  for (var key in actionCreators) {\n    _loop(key);\n  }\n\n  return boundActionCreators;\n}"], "mappings": "AAAA,eAAe,SAASA,kBAAkBA,CAACC,cAAc,EAAEC,QAAQ,EAAE;EACnE,IAAIC,mBAAmB,GAAG,CAAC,CAAC;EAE5B,IAAIC,KAAK,GAAG,SAASA,KAAKA,CAACC,GAAG,EAAE;IAC9B,IAAIC,aAAa,GAAGL,cAAc,CAACI,GAAG,CAAC;IAEvC,IAAI,OAAOC,aAAa,KAAK,UAAU,EAAE;MACvCH,mBAAmB,CAACE,GAAG,CAAC,GAAG,YAAY;QACrC,OAAOH,QAAQ,CAACI,aAAa,CAACC,KAAK,CAAC,KAAK,CAAC,EAAEC,SAAS,CAAC,CAAC;MACzD,CAAC;IACH;EACF,CAAC;EAED,KAAK,IAAIH,GAAG,IAAIJ,cAAc,EAAE;IAC9BG,KAAK,CAACC,GAAG,CAAC;EACZ;EAEA,OAAOF,mBAAmB;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}