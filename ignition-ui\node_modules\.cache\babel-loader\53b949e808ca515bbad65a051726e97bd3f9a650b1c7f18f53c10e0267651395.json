{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"color\", \"textColor\"],\n  _excluded2 = [\"component\", \"gutterBottom\", \"noWrap\", \"level\", \"levelMapping\", \"children\", \"endDecorator\", \"startDecorator\", \"variant\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_capitalize as capitalize, unstable_isMuiElement as isMuiElement } from '@mui/utils';\nimport { unstable_extendSxProp as extendSxProp } from '@mui/system';\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport styled from '../styles/styled';\nimport useThemeProps from '../styles/useThemeProps';\nimport useSlot from '../utils/useSlot';\nimport { getTypographyUtilityClass } from './typographyClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\n/**\n * @internal\n * For creating nested Typography to have inherit level (unless an explicit `level` prop is provided)\n * and change the HTML tag to `span` (unless an explicit `component` prop is provided).\n */\nexport const TypographyNestedContext = /*#__PURE__*/React.createContext(false);\nif (process.env.NODE_ENV !== 'production') {\n  TypographyNestedContext.displayName = 'TypographyNestedContext';\n}\n\n/**\n * @internal\n * Typography's level will be inherit within this context unless an explicit `level` prop is provided.\n *\n * This is used in components, for example Table, to inherit the parent's size by default.\n */\nexport const TypographyInheritContext = /*#__PURE__*/React.createContext(false);\nif (process.env.NODE_ENV !== 'production') {\n  TypographyInheritContext.displayName = 'TypographyInheritContext';\n}\nconst useUtilityClasses = ownerState => {\n  const {\n    gutterBottom,\n    noWrap,\n    level,\n    color,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', level, gutterBottom && 'gutterBottom', noWrap && 'noWrap', color && `color${capitalize(color)}`, variant && `variant${capitalize(variant)}`],\n    startDecorator: ['startDecorator'],\n    endDecorator: ['endDecorator']\n  };\n  return composeClasses(slots, getTypographyUtilityClass, {});\n};\nconst StartDecorator = styled('span', {\n  name: 'JoyTypography',\n  slot: 'StartDecorator',\n  overridesResolver: (props, styles) => styles.startDecorator\n})({\n  display: 'inline-flex',\n  marginInlineEnd: 'clamp(4px, var(--Typography-gap, 0.375em), 0.75rem)'\n});\nconst EndDecorator = styled('span', {\n  name: 'JoyTypography',\n  slot: 'endDecorator',\n  overridesResolver: (props, styles) => styles.endDecorator\n})({\n  display: 'inline-flex',\n  marginInlineStart: 'clamp(4px, var(--Typography-gap, 0.375em), 0.75rem)'\n});\nconst TypographyRoot = styled('span', {\n  name: 'JoyTypography',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(_ref => {\n  let {\n    theme,\n    ownerState\n  } = _ref;\n  var _theme$typography, _theme$typography$own, _theme$typography$own2, _theme$vars$palette$o, _theme$variants$owner;\n  const lineHeight = ownerState.level !== 'inherit' ? (_theme$typography = theme.typography[ownerState.level]) == null ? void 0 : _theme$typography.lineHeight : '1';\n  return _extends({\n    '--Icon-fontSize': `calc(1em * ${lineHeight})`\n  }, ownerState.color && {\n    '--Icon-color': 'currentColor'\n  }, {\n    margin: 'var(--Typography-margin, 0px)'\n  }, ownerState.nesting ? {\n    display: 'inline' // looks better than `inline-block` when using with `variant` prop.\n  } : _extends({\n    display: 'block'\n  }, ownerState.unstable_hasSkeleton && {\n    position: 'relative'\n  }), (ownerState.startDecorator || ownerState.endDecorator) && _extends({\n    display: 'flex',\n    alignItems: 'center'\n  }, ownerState.nesting && _extends({\n    display: 'inline-flex'\n  }, ownerState.startDecorator && {\n    verticalAlign: 'bottom' // to make the text align with the parent's content\n  })), ownerState.level && ownerState.level !== 'inherit' && theme.typography[ownerState.level], {\n    fontSize: `var(--Typography-fontSize, ${ownerState.level && ownerState.level !== 'inherit' ? (_theme$typography$own = (_theme$typography$own2 = theme.typography[ownerState.level]) == null ? void 0 : _theme$typography$own2.fontSize) != null ? _theme$typography$own : 'inherit' : 'inherit'})`\n  }, ownerState.noWrap && {\n    overflow: 'hidden',\n    textOverflow: 'ellipsis',\n    whiteSpace: 'nowrap'\n  }, ownerState.gutterBottom && {\n    marginBottom: '0.35em'\n  }, ownerState.color && {\n    color: `var(--variant-plainColor, rgba(${(_theme$vars$palette$o = theme.vars.palette[ownerState.color]) == null ? void 0 : _theme$vars$palette$o.mainChannel} / 1))`\n  }, ownerState.variant && _extends({\n    borderRadius: theme.vars.radius.xs,\n    paddingBlock: 'min(0.1em, 4px)',\n    paddingInline: '0.25em'\n  }, !ownerState.nesting && {\n    marginInline: '-0.25em'\n  }, (_theme$variants$owner = theme.variants[ownerState.variant]) == null ? void 0 : _theme$variants$owner[ownerState.color]));\n});\nconst defaultVariantMapping = {\n  h1: 'h1',\n  h2: 'h2',\n  h3: 'h3',\n  h4: 'h4',\n  'title-lg': 'p',\n  'title-md': 'p',\n  'title-sm': 'p',\n  'body-lg': 'p',\n  'body-md': 'p',\n  'body-sm': 'p',\n  'body-xs': 'span',\n  inherit: 'p'\n};\n/**\n *\n * Demos:\n *\n * - [Skeleton](https://mui.com/joy-ui/react-skeleton/)\n * - [Typography](https://mui.com/joy-ui/react-typography/)\n *\n * API:\n *\n * - [Typography API](https://mui.com/joy-ui/api/typography/)\n */\nconst Typography = /*#__PURE__*/React.forwardRef(function Typography(inProps, ref) {\n  var _inProps$color;\n  const _useThemeProps = useThemeProps({\n      props: inProps,\n      name: 'JoyTypography'\n    }),\n    {\n      color: colorProp,\n      textColor\n    } = _useThemeProps,\n    themeProps = _objectWithoutPropertiesLoose(_useThemeProps, _excluded);\n  const nesting = React.useContext(TypographyNestedContext);\n  const inheriting = React.useContext(TypographyInheritContext);\n  const props = extendSxProp(_extends({}, themeProps, {\n    color: textColor\n  }));\n  const {\n      component: componentProp,\n      gutterBottom = false,\n      noWrap = false,\n      level: levelProp = 'body-md',\n      levelMapping = defaultVariantMapping,\n      children,\n      endDecorator,\n      startDecorator,\n      variant,\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded2);\n  const color = (_inProps$color = inProps.color) != null ? _inProps$color : variant ? colorProp != null ? colorProp : 'neutral' : colorProp;\n  const level = nesting || inheriting ? inProps.level || 'inherit' : levelProp;\n  const hasSkeleton = isMuiElement(children, ['Skeleton']);\n  const component = componentProp || (nesting ? 'span' : levelMapping[level] || defaultVariantMapping[level] || 'span');\n  const ownerState = _extends({}, props, {\n    level,\n    component,\n    color,\n    gutterBottom,\n    noWrap,\n    nesting,\n    variant,\n    unstable_hasSkeleton: hasSkeleton\n  });\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    className: classes.root,\n    elementType: TypographyRoot,\n    externalForwardedProps,\n    ownerState\n  });\n  const [SlotStartDecorator, startDecoratorProps] = useSlot('startDecorator', {\n    className: classes.startDecorator,\n    elementType: StartDecorator,\n    externalForwardedProps,\n    ownerState\n  });\n  const [SlotEndDecorator, endDecoratorProps] = useSlot('endDecorator', {\n    className: classes.endDecorator,\n    elementType: EndDecorator,\n    externalForwardedProps,\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(TypographyNestedContext.Provider, {\n    value: true,\n    children: /*#__PURE__*/_jsxs(SlotRoot, _extends({}, rootProps, {\n      children: [startDecorator && /*#__PURE__*/_jsx(SlotStartDecorator, _extends({}, startDecoratorProps, {\n        children: startDecorator\n      })), hasSkeleton ? /*#__PURE__*/React.cloneElement(children, {\n        variant: children.props.variant || 'inline'\n      }) : children, endDecorator && /*#__PURE__*/_jsx(SlotEndDecorator, _extends({}, endDecoratorProps, {\n        children: endDecorator\n      }))]\n    }))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Typography.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * The color of the component. It supports those theme colors that make sense for this component.\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['danger', 'neutral', 'primary', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Element placed after the children.\n   */\n  endDecorator: PropTypes.node,\n  /**\n   * If `true`, the text will have a bottom margin.\n   * @default false\n   */\n  gutterBottom: PropTypes.bool,\n  /**\n   * Applies the theme typography styles.\n   * @default 'body-md'\n   */\n  level: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['h1', 'h2', 'h3', 'h4', 'title-lg', 'title-md', 'title-sm', 'body-lg', 'body-md', 'body-sm', 'body-xs', 'inherit']), PropTypes.string]),\n  /**\n   * The component maps the variant prop to a range of different HTML element types.\n   * For instance, body1 to `<h6>`.\n   * If you wish to change that mapping, you can provide your own.\n   * Alternatively, you can use the `component` prop.\n   * @default {\n   *   h1: 'h1',\n   *   h2: 'h2',\n   *   h3: 'h3',\n   *   h4: 'h4',\n   *   'title-lg': 'p',\n   *   'title-md': 'p',\n   *   'title-sm': 'p',\n   *   'body-lg': 'p',\n   *   'body-md': 'p',\n   *   'body-sm': 'p',\n   *   'body-xs': 'span',\n   *   inherit: 'p',\n   * }\n   */\n  levelMapping: PropTypes /* @typescript-to-proptypes-ignore */.object,\n  /**\n   * If `true`, the text will not wrap, but instead will truncate with a text overflow ellipsis.\n   *\n   * Note that text overflow can only happen with block or inline-block level elements\n   * (the element needs to have a width in order to overflow).\n   * @default false\n   */\n  noWrap: PropTypes.bool,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    endDecorator: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    startDecorator: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    endDecorator: PropTypes.elementType,\n    root: PropTypes.elementType,\n    startDecorator: PropTypes.elementType\n  }),\n  /**\n   * Element placed before the children.\n   */\n  startDecorator: PropTypes.node,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The system color.\n   */\n  textColor: PropTypes /* @typescript-to-proptypes-ignore */.any,\n  /**\n   * The [global variant](https://mui.com/joy-ui/main-features/global-variants/) to use.\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['outlined', 'plain', 'soft', 'solid']), PropTypes.string])\n} : void 0;\n\n// @ts-ignore internal logic to let communicate with Breadcrumbs\nTypography.muiName = 'Typography';\nexport default Typography;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "_excluded2", "React", "PropTypes", "unstable_capitalize", "capitalize", "unstable_isMuiElement", "isMuiElement", "unstable_extendSxProp", "extendSxProp", "unstable_composeClasses", "composeClasses", "styled", "useThemeProps", "useSlot", "getTypographyUtilityClass", "jsx", "_jsx", "jsxs", "_jsxs", "TypographyNestedContext", "createContext", "process", "env", "NODE_ENV", "displayName", "TypographyInheritContext", "useUtilityClasses", "ownerState", "gutterBottom", "noWrap", "level", "color", "variant", "slots", "root", "startDecorator", "endDecorator", "StartDecorator", "name", "slot", "overridesResolver", "props", "styles", "display", "marginInlineEnd", "EndDecorator", "marginInlineStart", "TypographyRoot", "_ref", "theme", "_theme$typography", "_theme$typography$own", "_theme$typography$own2", "_theme$vars$palette$o", "_theme$variants$owner", "lineHeight", "typography", "margin", "nesting", "unstable_hasSkeleton", "position", "alignItems", "verticalAlign", "fontSize", "overflow", "textOverflow", "whiteSpace", "marginBottom", "vars", "palette", "mainChannel", "borderRadius", "radius", "xs", "paddingBlock", "paddingInline", "marginInline", "variants", "defaultVariantMapping", "h1", "h2", "h3", "h4", "inherit", "Typography", "forwardRef", "inProps", "ref", "_inProps$color", "_useThemeProps", "colorProp", "textColor", "themeProps", "useContext", "inheriting", "component", "componentProp", "levelProp", "levelMapping", "children", "slotProps", "other", "hasSkeleton", "classes", "externalForwardedProps", "SlotRoot", "rootProps", "className", "elementType", "SlotStartDecorator", "startDecoratorProps", "SlotEndDecorator", "endDecoratorProps", "Provider", "value", "cloneElement", "propTypes", "node", "oneOfType", "oneOf", "string", "bool", "object", "shape", "func", "sx", "arrayOf", "any", "mui<PERSON><PERSON>"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Typography/Typography.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"color\", \"textColor\"],\n  _excluded2 = [\"component\", \"gutterBottom\", \"noWrap\", \"level\", \"levelMapping\", \"children\", \"endDecorator\", \"startDecorator\", \"variant\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_capitalize as capitalize, unstable_isMuiElement as isMuiElement } from '@mui/utils';\nimport { unstable_extendSxProp as extendSxProp } from '@mui/system';\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport styled from '../styles/styled';\nimport useThemeProps from '../styles/useThemeProps';\nimport useSlot from '../utils/useSlot';\nimport { getTypographyUtilityClass } from './typographyClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\n/**\n * @internal\n * For creating nested Typography to have inherit level (unless an explicit `level` prop is provided)\n * and change the HTML tag to `span` (unless an explicit `component` prop is provided).\n */\nexport const TypographyNestedContext = /*#__PURE__*/React.createContext(false);\nif (process.env.NODE_ENV !== 'production') {\n  TypographyNestedContext.displayName = 'TypographyNestedContext';\n}\n\n/**\n * @internal\n * Typography's level will be inherit within this context unless an explicit `level` prop is provided.\n *\n * This is used in components, for example Table, to inherit the parent's size by default.\n */\nexport const TypographyInheritContext = /*#__PURE__*/React.createContext(false);\nif (process.env.NODE_ENV !== 'production') {\n  TypographyInheritContext.displayName = 'TypographyInheritContext';\n}\nconst useUtilityClasses = ownerState => {\n  const {\n    gutterBottom,\n    noWrap,\n    level,\n    color,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', level, gutterBottom && 'gutterBottom', noWrap && 'noWrap', color && `color${capitalize(color)}`, variant && `variant${capitalize(variant)}`],\n    startDecorator: ['startDecorator'],\n    endDecorator: ['endDecorator']\n  };\n  return composeClasses(slots, getTypographyUtilityClass, {});\n};\nconst StartDecorator = styled('span', {\n  name: 'JoyTypography',\n  slot: 'StartDecorator',\n  overridesResolver: (props, styles) => styles.startDecorator\n})({\n  display: 'inline-flex',\n  marginInlineEnd: 'clamp(4px, var(--Typography-gap, 0.375em), 0.75rem)'\n});\nconst EndDecorator = styled('span', {\n  name: 'JoyTypography',\n  slot: 'endDecorator',\n  overridesResolver: (props, styles) => styles.endDecorator\n})({\n  display: 'inline-flex',\n  marginInlineStart: 'clamp(4px, var(--Typography-gap, 0.375em), 0.75rem)'\n});\nconst TypographyRoot = styled('span', {\n  name: 'JoyTypography',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(({\n  theme,\n  ownerState\n}) => {\n  var _theme$typography, _theme$typography$own, _theme$typography$own2, _theme$vars$palette$o, _theme$variants$owner;\n  const lineHeight = ownerState.level !== 'inherit' ? (_theme$typography = theme.typography[ownerState.level]) == null ? void 0 : _theme$typography.lineHeight : '1';\n  return _extends({\n    '--Icon-fontSize': `calc(1em * ${lineHeight})`\n  }, ownerState.color && {\n    '--Icon-color': 'currentColor'\n  }, {\n    margin: 'var(--Typography-margin, 0px)'\n  }, ownerState.nesting ? {\n    display: 'inline' // looks better than `inline-block` when using with `variant` prop.\n  } : _extends({\n    display: 'block'\n  }, ownerState.unstable_hasSkeleton && {\n    position: 'relative'\n  }), (ownerState.startDecorator || ownerState.endDecorator) && _extends({\n    display: 'flex',\n    alignItems: 'center'\n  }, ownerState.nesting && _extends({\n    display: 'inline-flex'\n  }, ownerState.startDecorator && {\n    verticalAlign: 'bottom' // to make the text align with the parent's content\n  })), ownerState.level && ownerState.level !== 'inherit' && theme.typography[ownerState.level], {\n    fontSize: `var(--Typography-fontSize, ${ownerState.level && ownerState.level !== 'inherit' ? (_theme$typography$own = (_theme$typography$own2 = theme.typography[ownerState.level]) == null ? void 0 : _theme$typography$own2.fontSize) != null ? _theme$typography$own : 'inherit' : 'inherit'})`\n  }, ownerState.noWrap && {\n    overflow: 'hidden',\n    textOverflow: 'ellipsis',\n    whiteSpace: 'nowrap'\n  }, ownerState.gutterBottom && {\n    marginBottom: '0.35em'\n  }, ownerState.color && {\n    color: `var(--variant-plainColor, rgba(${(_theme$vars$palette$o = theme.vars.palette[ownerState.color]) == null ? void 0 : _theme$vars$palette$o.mainChannel} / 1))`\n  }, ownerState.variant && _extends({\n    borderRadius: theme.vars.radius.xs,\n    paddingBlock: 'min(0.1em, 4px)',\n    paddingInline: '0.25em'\n  }, !ownerState.nesting && {\n    marginInline: '-0.25em'\n  }, (_theme$variants$owner = theme.variants[ownerState.variant]) == null ? void 0 : _theme$variants$owner[ownerState.color]));\n});\nconst defaultVariantMapping = {\n  h1: 'h1',\n  h2: 'h2',\n  h3: 'h3',\n  h4: 'h4',\n  'title-lg': 'p',\n  'title-md': 'p',\n  'title-sm': 'p',\n  'body-lg': 'p',\n  'body-md': 'p',\n  'body-sm': 'p',\n  'body-xs': 'span',\n  inherit: 'p'\n};\n/**\n *\n * Demos:\n *\n * - [Skeleton](https://mui.com/joy-ui/react-skeleton/)\n * - [Typography](https://mui.com/joy-ui/react-typography/)\n *\n * API:\n *\n * - [Typography API](https://mui.com/joy-ui/api/typography/)\n */\nconst Typography = /*#__PURE__*/React.forwardRef(function Typography(inProps, ref) {\n  var _inProps$color;\n  const _useThemeProps = useThemeProps({\n      props: inProps,\n      name: 'JoyTypography'\n    }),\n    {\n      color: colorProp,\n      textColor\n    } = _useThemeProps,\n    themeProps = _objectWithoutPropertiesLoose(_useThemeProps, _excluded);\n  const nesting = React.useContext(TypographyNestedContext);\n  const inheriting = React.useContext(TypographyInheritContext);\n  const props = extendSxProp(_extends({}, themeProps, {\n    color: textColor\n  }));\n  const {\n      component: componentProp,\n      gutterBottom = false,\n      noWrap = false,\n      level: levelProp = 'body-md',\n      levelMapping = defaultVariantMapping,\n      children,\n      endDecorator,\n      startDecorator,\n      variant,\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded2);\n  const color = (_inProps$color = inProps.color) != null ? _inProps$color : variant ? colorProp != null ? colorProp : 'neutral' : colorProp;\n  const level = nesting || inheriting ? inProps.level || 'inherit' : levelProp;\n  const hasSkeleton = isMuiElement(children, ['Skeleton']);\n  const component = componentProp || (nesting ? 'span' : levelMapping[level] || defaultVariantMapping[level] || 'span');\n  const ownerState = _extends({}, props, {\n    level,\n    component,\n    color,\n    gutterBottom,\n    noWrap,\n    nesting,\n    variant,\n    unstable_hasSkeleton: hasSkeleton\n  });\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    className: classes.root,\n    elementType: TypographyRoot,\n    externalForwardedProps,\n    ownerState\n  });\n  const [SlotStartDecorator, startDecoratorProps] = useSlot('startDecorator', {\n    className: classes.startDecorator,\n    elementType: StartDecorator,\n    externalForwardedProps,\n    ownerState\n  });\n  const [SlotEndDecorator, endDecoratorProps] = useSlot('endDecorator', {\n    className: classes.endDecorator,\n    elementType: EndDecorator,\n    externalForwardedProps,\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(TypographyNestedContext.Provider, {\n    value: true,\n    children: /*#__PURE__*/_jsxs(SlotRoot, _extends({}, rootProps, {\n      children: [startDecorator && /*#__PURE__*/_jsx(SlotStartDecorator, _extends({}, startDecoratorProps, {\n        children: startDecorator\n      })), hasSkeleton ? /*#__PURE__*/React.cloneElement(children, {\n        variant: children.props.variant || 'inline'\n      }) : children, endDecorator && /*#__PURE__*/_jsx(SlotEndDecorator, _extends({}, endDecoratorProps, {\n        children: endDecorator\n      }))]\n    }))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Typography.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * The color of the component. It supports those theme colors that make sense for this component.\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['danger', 'neutral', 'primary', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Element placed after the children.\n   */\n  endDecorator: PropTypes.node,\n  /**\n   * If `true`, the text will have a bottom margin.\n   * @default false\n   */\n  gutterBottom: PropTypes.bool,\n  /**\n   * Applies the theme typography styles.\n   * @default 'body-md'\n   */\n  level: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['h1', 'h2', 'h3', 'h4', 'title-lg', 'title-md', 'title-sm', 'body-lg', 'body-md', 'body-sm', 'body-xs', 'inherit']), PropTypes.string]),\n  /**\n   * The component maps the variant prop to a range of different HTML element types.\n   * For instance, body1 to `<h6>`.\n   * If you wish to change that mapping, you can provide your own.\n   * Alternatively, you can use the `component` prop.\n   * @default {\n   *   h1: 'h1',\n   *   h2: 'h2',\n   *   h3: 'h3',\n   *   h4: 'h4',\n   *   'title-lg': 'p',\n   *   'title-md': 'p',\n   *   'title-sm': 'p',\n   *   'body-lg': 'p',\n   *   'body-md': 'p',\n   *   'body-sm': 'p',\n   *   'body-xs': 'span',\n   *   inherit: 'p',\n   * }\n   */\n  levelMapping: PropTypes /* @typescript-to-proptypes-ignore */.object,\n  /**\n   * If `true`, the text will not wrap, but instead will truncate with a text overflow ellipsis.\n   *\n   * Note that text overflow can only happen with block or inline-block level elements\n   * (the element needs to have a width in order to overflow).\n   * @default false\n   */\n  noWrap: PropTypes.bool,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    endDecorator: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    startDecorator: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    endDecorator: PropTypes.elementType,\n    root: PropTypes.elementType,\n    startDecorator: PropTypes.elementType\n  }),\n  /**\n   * Element placed before the children.\n   */\n  startDecorator: PropTypes.node,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The system color.\n   */\n  textColor: PropTypes /* @typescript-to-proptypes-ignore */.any,\n  /**\n   * The [global variant](https://mui.com/joy-ui/main-features/global-variants/) to use.\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['outlined', 'plain', 'soft', 'solid']), PropTypes.string])\n} : void 0;\n\n// @ts-ignore internal logic to let communicate with Breadcrumbs\nTypography.muiName = 'Typography';\nexport default Typography;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,OAAO,EAAE,WAAW,CAAC;EACtCC,UAAU,GAAG,CAAC,WAAW,EAAE,cAAc,EAAE,QAAQ,EAAE,OAAO,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,gBAAgB,EAAE,SAAS,EAAE,OAAO,EAAE,WAAW,CAAC;AAC9J,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,mBAAmB,IAAIC,UAAU,EAAEC,qBAAqB,IAAIC,YAAY,QAAQ,YAAY;AACrG,SAASC,qBAAqB,IAAIC,YAAY,QAAQ,aAAa;AACnE,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,0BAA0B;AACpF,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,aAAa,MAAM,yBAAyB;AACnD,OAAOC,OAAO,MAAM,kBAAkB;AACtC,SAASC,yBAAyB,QAAQ,qBAAqB;AAC/D,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,uBAAuB,GAAG,aAAalB,KAAK,CAACmB,aAAa,CAAC,KAAK,CAAC;AAC9E,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,uBAAuB,CAACK,WAAW,GAAG,yBAAyB;AACjE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,wBAAwB,GAAG,aAAaxB,KAAK,CAACmB,aAAa,CAAC,KAAK,CAAC;AAC/E,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCE,wBAAwB,CAACD,WAAW,GAAG,0BAA0B;AACnE;AACA,MAAME,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,YAAY;IACZC,MAAM;IACNC,KAAK;IACLC,KAAK;IACLC;EACF,CAAC,GAAGL,UAAU;EACd,MAAMM,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEJ,KAAK,EAAEF,YAAY,IAAI,cAAc,EAAEC,MAAM,IAAI,QAAQ,EAAEE,KAAK,IAAI,QAAQ3B,UAAU,CAAC2B,KAAK,CAAC,EAAE,EAAEC,OAAO,IAAI,UAAU5B,UAAU,CAAC4B,OAAO,CAAC,EAAE,CAAC;IAC3JG,cAAc,EAAE,CAAC,gBAAgB,CAAC;IAClCC,YAAY,EAAE,CAAC,cAAc;EAC/B,CAAC;EACD,OAAO1B,cAAc,CAACuB,KAAK,EAAEnB,yBAAyB,EAAE,CAAC,CAAC,CAAC;AAC7D,CAAC;AACD,MAAMuB,cAAc,GAAG1B,MAAM,CAAC,MAAM,EAAE;EACpC2B,IAAI,EAAE,eAAe;EACrBC,IAAI,EAAE,gBAAgB;EACtBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACP;AAC/C,CAAC,CAAC,CAAC;EACDQ,OAAO,EAAE,aAAa;EACtBC,eAAe,EAAE;AACnB,CAAC,CAAC;AACF,MAAMC,YAAY,GAAGlC,MAAM,CAAC,MAAM,EAAE;EAClC2B,IAAI,EAAE,eAAe;EACrBC,IAAI,EAAE,cAAc;EACpBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC/C,CAAC,CAAC,CAAC;EACDO,OAAO,EAAE,aAAa;EACtBG,iBAAiB,EAAE;AACrB,CAAC,CAAC;AACF,MAAMC,cAAc,GAAGpC,MAAM,CAAC,MAAM,EAAE;EACpC2B,IAAI,EAAE,eAAe;EACrBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACR;AAC/C,CAAC,CAAC,CAACc,IAAA,IAGG;EAAA,IAHF;IACFC,KAAK;IACLtB;EACF,CAAC,GAAAqB,IAAA;EACC,IAAIE,iBAAiB,EAAEC,qBAAqB,EAAEC,sBAAsB,EAAEC,qBAAqB,EAAEC,qBAAqB;EAClH,MAAMC,UAAU,GAAG5B,UAAU,CAACG,KAAK,KAAK,SAAS,GAAG,CAACoB,iBAAiB,GAAGD,KAAK,CAACO,UAAU,CAAC7B,UAAU,CAACG,KAAK,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGoB,iBAAiB,CAACK,UAAU,GAAG,GAAG;EAClK,OAAOzD,QAAQ,CAAC;IACd,iBAAiB,EAAE,cAAcyD,UAAU;EAC7C,CAAC,EAAE5B,UAAU,CAACI,KAAK,IAAI;IACrB,cAAc,EAAE;EAClB,CAAC,EAAE;IACD0B,MAAM,EAAE;EACV,CAAC,EAAE9B,UAAU,CAAC+B,OAAO,GAAG;IACtBf,OAAO,EAAE,QAAQ,CAAC;EACpB,CAAC,GAAG7C,QAAQ,CAAC;IACX6C,OAAO,EAAE;EACX,CAAC,EAAEhB,UAAU,CAACgC,oBAAoB,IAAI;IACpCC,QAAQ,EAAE;EACZ,CAAC,CAAC,EAAE,CAACjC,UAAU,CAACQ,cAAc,IAAIR,UAAU,CAACS,YAAY,KAAKtC,QAAQ,CAAC;IACrE6C,OAAO,EAAE,MAAM;IACfkB,UAAU,EAAE;EACd,CAAC,EAAElC,UAAU,CAAC+B,OAAO,IAAI5D,QAAQ,CAAC;IAChC6C,OAAO,EAAE;EACX,CAAC,EAAEhB,UAAU,CAACQ,cAAc,IAAI;IAC9B2B,aAAa,EAAE,QAAQ,CAAC;EAC1B,CAAC,CAAC,CAAC,EAAEnC,UAAU,CAACG,KAAK,IAAIH,UAAU,CAACG,KAAK,KAAK,SAAS,IAAImB,KAAK,CAACO,UAAU,CAAC7B,UAAU,CAACG,KAAK,CAAC,EAAE;IAC7FiC,QAAQ,EAAE,8BAA8BpC,UAAU,CAACG,KAAK,IAAIH,UAAU,CAACG,KAAK,KAAK,SAAS,GAAG,CAACqB,qBAAqB,GAAG,CAACC,sBAAsB,GAAGH,KAAK,CAACO,UAAU,CAAC7B,UAAU,CAACG,KAAK,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGsB,sBAAsB,CAACW,QAAQ,KAAK,IAAI,GAAGZ,qBAAqB,GAAG,SAAS,GAAG,SAAS;EACjS,CAAC,EAAExB,UAAU,CAACE,MAAM,IAAI;IACtBmC,QAAQ,EAAE,QAAQ;IAClBC,YAAY,EAAE,UAAU;IACxBC,UAAU,EAAE;EACd,CAAC,EAAEvC,UAAU,CAACC,YAAY,IAAI;IAC5BuC,YAAY,EAAE;EAChB,CAAC,EAAExC,UAAU,CAACI,KAAK,IAAI;IACrBA,KAAK,EAAE,kCAAkC,CAACsB,qBAAqB,GAAGJ,KAAK,CAACmB,IAAI,CAACC,OAAO,CAAC1C,UAAU,CAACI,KAAK,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGsB,qBAAqB,CAACiB,WAAW;EAC9J,CAAC,EAAE3C,UAAU,CAACK,OAAO,IAAIlC,QAAQ,CAAC;IAChCyE,YAAY,EAAEtB,KAAK,CAACmB,IAAI,CAACI,MAAM,CAACC,EAAE;IAClCC,YAAY,EAAE,iBAAiB;IAC/BC,aAAa,EAAE;EACjB,CAAC,EAAE,CAAChD,UAAU,CAAC+B,OAAO,IAAI;IACxBkB,YAAY,EAAE;EAChB,CAAC,EAAE,CAACtB,qBAAqB,GAAGL,KAAK,CAAC4B,QAAQ,CAAClD,UAAU,CAACK,OAAO,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGsB,qBAAqB,CAAC3B,UAAU,CAACI,KAAK,CAAC,CAAC,CAAC;AAC9H,CAAC,CAAC;AACF,MAAM+C,qBAAqB,GAAG;EAC5BC,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,IAAI;EACR,UAAU,EAAE,GAAG;EACf,UAAU,EAAE,GAAG;EACf,UAAU,EAAE,GAAG;EACf,SAAS,EAAE,GAAG;EACd,SAAS,EAAE,GAAG;EACd,SAAS,EAAE,GAAG;EACd,SAAS,EAAE,MAAM;EACjBC,OAAO,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,GAAG,aAAanF,KAAK,CAACoF,UAAU,CAAC,SAASD,UAAUA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACjF,IAAIC,cAAc;EAClB,MAAMC,cAAc,GAAG7E,aAAa,CAAC;MACjC6B,KAAK,EAAE6C,OAAO;MACdhD,IAAI,EAAE;IACR,CAAC,CAAC;IACF;MACEP,KAAK,EAAE2D,SAAS;MAChBC;IACF,CAAC,GAAGF,cAAc;IAClBG,UAAU,GAAG/F,6BAA6B,CAAC4F,cAAc,EAAE1F,SAAS,CAAC;EACvE,MAAM2D,OAAO,GAAGzD,KAAK,CAAC4F,UAAU,CAAC1E,uBAAuB,CAAC;EACzD,MAAM2E,UAAU,GAAG7F,KAAK,CAAC4F,UAAU,CAACpE,wBAAwB,CAAC;EAC7D,MAAMgB,KAAK,GAAGjC,YAAY,CAACV,QAAQ,CAAC,CAAC,CAAC,EAAE8F,UAAU,EAAE;IAClD7D,KAAK,EAAE4D;EACT,CAAC,CAAC,CAAC;EACH,MAAM;MACFI,SAAS,EAAEC,aAAa;MACxBpE,YAAY,GAAG,KAAK;MACpBC,MAAM,GAAG,KAAK;MACdC,KAAK,EAAEmE,SAAS,GAAG,SAAS;MAC5BC,YAAY,GAAGpB,qBAAqB;MACpCqB,QAAQ;MACR/D,YAAY;MACZD,cAAc;MACdH,OAAO;MACPC,KAAK,GAAG,CAAC,CAAC;MACVmE,SAAS,GAAG,CAAC;IACf,CAAC,GAAG3D,KAAK;IACT4D,KAAK,GAAGxG,6BAA6B,CAAC4C,KAAK,EAAEzC,UAAU,CAAC;EAC1D,MAAM+B,KAAK,GAAG,CAACyD,cAAc,GAAGF,OAAO,CAACvD,KAAK,KAAK,IAAI,GAAGyD,cAAc,GAAGxD,OAAO,GAAG0D,SAAS,IAAI,IAAI,GAAGA,SAAS,GAAG,SAAS,GAAGA,SAAS;EACzI,MAAM5D,KAAK,GAAG4B,OAAO,IAAIoC,UAAU,GAAGR,OAAO,CAACxD,KAAK,IAAI,SAAS,GAAGmE,SAAS;EAC5E,MAAMK,WAAW,GAAGhG,YAAY,CAAC6F,QAAQ,EAAE,CAAC,UAAU,CAAC,CAAC;EACxD,MAAMJ,SAAS,GAAGC,aAAa,KAAKtC,OAAO,GAAG,MAAM,GAAGwC,YAAY,CAACpE,KAAK,CAAC,IAAIgD,qBAAqB,CAAChD,KAAK,CAAC,IAAI,MAAM,CAAC;EACrH,MAAMH,UAAU,GAAG7B,QAAQ,CAAC,CAAC,CAAC,EAAE2C,KAAK,EAAE;IACrCX,KAAK;IACLiE,SAAS;IACThE,KAAK;IACLH,YAAY;IACZC,MAAM;IACN6B,OAAO;IACP1B,OAAO;IACP2B,oBAAoB,EAAE2C;EACxB,CAAC,CAAC;EACF,MAAMC,OAAO,GAAG7E,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAM6E,sBAAsB,GAAG1G,QAAQ,CAAC,CAAC,CAAC,EAAEuG,KAAK,EAAE;IACjDN,SAAS;IACT9D,KAAK;IACLmE;EACF,CAAC,CAAC;EACF,MAAM,CAACK,QAAQ,EAAEC,SAAS,CAAC,GAAG7F,OAAO,CAAC,MAAM,EAAE;IAC5C0E,GAAG;IACHoB,SAAS,EAAEJ,OAAO,CAACrE,IAAI;IACvB0E,WAAW,EAAE7D,cAAc;IAC3ByD,sBAAsB;IACtB7E;EACF,CAAC,CAAC;EACF,MAAM,CAACkF,kBAAkB,EAAEC,mBAAmB,CAAC,GAAGjG,OAAO,CAAC,gBAAgB,EAAE;IAC1E8F,SAAS,EAAEJ,OAAO,CAACpE,cAAc;IACjCyE,WAAW,EAAEvE,cAAc;IAC3BmE,sBAAsB;IACtB7E;EACF,CAAC,CAAC;EACF,MAAM,CAACoF,gBAAgB,EAAEC,iBAAiB,CAAC,GAAGnG,OAAO,CAAC,cAAc,EAAE;IACpE8F,SAAS,EAAEJ,OAAO,CAACnE,YAAY;IAC/BwE,WAAW,EAAE/D,YAAY;IACzB2D,sBAAsB;IACtB7E;EACF,CAAC,CAAC;EACF,OAAO,aAAaX,IAAI,CAACG,uBAAuB,CAAC8F,QAAQ,EAAE;IACzDC,KAAK,EAAE,IAAI;IACXf,QAAQ,EAAE,aAAajF,KAAK,CAACuF,QAAQ,EAAE3G,QAAQ,CAAC,CAAC,CAAC,EAAE4G,SAAS,EAAE;MAC7DP,QAAQ,EAAE,CAAChE,cAAc,IAAI,aAAanB,IAAI,CAAC6F,kBAAkB,EAAE/G,QAAQ,CAAC,CAAC,CAAC,EAAEgH,mBAAmB,EAAE;QACnGX,QAAQ,EAAEhE;MACZ,CAAC,CAAC,CAAC,EAAEmE,WAAW,GAAG,aAAarG,KAAK,CAACkH,YAAY,CAAChB,QAAQ,EAAE;QAC3DnE,OAAO,EAAEmE,QAAQ,CAAC1D,KAAK,CAACT,OAAO,IAAI;MACrC,CAAC,CAAC,GAAGmE,QAAQ,EAAE/D,YAAY,IAAI,aAAapB,IAAI,CAAC+F,gBAAgB,EAAEjH,QAAQ,CAAC,CAAC,CAAC,EAAEkH,iBAAiB,EAAE;QACjGb,QAAQ,EAAE/D;MACZ,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFf,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG6D,UAAU,CAACgC,SAAS,CAAC,yBAAyB;EACpF;EACA;EACA;EACA;EACA;AACF;AACA;EACEjB,QAAQ,EAAEjG,SAAS,CAACmH,IAAI;EACxB;AACF;AACA;EACEtF,KAAK,EAAE7B,SAAS,CAAC,sCAAsCoH,SAAS,CAAC,CAACpH,SAAS,CAACqH,KAAK,CAAC,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,EAAErH,SAAS,CAACsH,MAAM,CAAC,CAAC;EAC7J;AACF;AACA;AACA;EACEzB,SAAS,EAAE7F,SAAS,CAAC0G,WAAW;EAChC;AACF;AACA;EACExE,YAAY,EAAElC,SAAS,CAACmH,IAAI;EAC5B;AACF;AACA;AACA;EACEzF,YAAY,EAAE1B,SAAS,CAACuH,IAAI;EAC5B;AACF;AACA;AACA;EACE3F,KAAK,EAAE5B,SAAS,CAAC,sCAAsCoH,SAAS,CAAC,CAACpH,SAAS,CAACqH,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,EAAErH,SAAS,CAACsH,MAAM,CAAC,CAAC;EAC1N;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEtB,YAAY,EAAEhG,SAAS,CAAC,sCAAsCwH,MAAM;EACpE;AACF;AACA;AACA;AACA;AACA;AACA;EACE7F,MAAM,EAAE3B,SAAS,CAACuH,IAAI;EACtB;AACF;AACA;AACA;EACErB,SAAS,EAAElG,SAAS,CAACyH,KAAK,CAAC;IACzBvF,YAAY,EAAElC,SAAS,CAACoH,SAAS,CAAC,CAACpH,SAAS,CAAC0H,IAAI,EAAE1H,SAAS,CAACwH,MAAM,CAAC,CAAC;IACrExF,IAAI,EAAEhC,SAAS,CAACoH,SAAS,CAAC,CAACpH,SAAS,CAAC0H,IAAI,EAAE1H,SAAS,CAACwH,MAAM,CAAC,CAAC;IAC7DvF,cAAc,EAAEjC,SAAS,CAACoH,SAAS,CAAC,CAACpH,SAAS,CAAC0H,IAAI,EAAE1H,SAAS,CAACwH,MAAM,CAAC;EACxE,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEzF,KAAK,EAAE/B,SAAS,CAACyH,KAAK,CAAC;IACrBvF,YAAY,EAAElC,SAAS,CAAC0G,WAAW;IACnC1E,IAAI,EAAEhC,SAAS,CAAC0G,WAAW;IAC3BzE,cAAc,EAAEjC,SAAS,CAAC0G;EAC5B,CAAC,CAAC;EACF;AACF;AACA;EACEzE,cAAc,EAAEjC,SAAS,CAACmH,IAAI;EAC9B;AACF;AACA;EACEQ,EAAE,EAAE3H,SAAS,CAACoH,SAAS,CAAC,CAACpH,SAAS,CAAC4H,OAAO,CAAC5H,SAAS,CAACoH,SAAS,CAAC,CAACpH,SAAS,CAAC0H,IAAI,EAAE1H,SAAS,CAACwH,MAAM,EAAExH,SAAS,CAACuH,IAAI,CAAC,CAAC,CAAC,EAAEvH,SAAS,CAAC0H,IAAI,EAAE1H,SAAS,CAACwH,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;EACE/B,SAAS,EAAEzF,SAAS,CAAC,sCAAsC6H,GAAG;EAC9D;AACF;AACA;EACE/F,OAAO,EAAE9B,SAAS,CAAC,sCAAsCoH,SAAS,CAAC,CAACpH,SAAS,CAACqH,KAAK,CAAC,CAAC,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,EAAErH,SAAS,CAACsH,MAAM,CAAC;AAChJ,CAAC,GAAG,KAAK,CAAC;;AAEV;AACApC,UAAU,CAAC4C,OAAO,GAAG,YAAY;AACjC,eAAe5C,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}