{"ast": null, "code": "'use strict';\n\nvar numberIsNaN = function (value) {\n  return value !== value;\n};\nmodule.exports = function is(a, b) {\n  if (a === 0 && b === 0) {\n    return 1 / a === 1 / b;\n  }\n  if (a === b) {\n    return true;\n  }\n  if (numberIsNaN(a) && numberIsNaN(b)) {\n    return true;\n  }\n  return false;\n};", "map": {"version": 3, "names": ["numberIsNaN", "value", "module", "exports", "is", "a", "b"], "sources": ["C:/ignition/ignition-ui/node_modules/object-is/implementation.js"], "sourcesContent": ["'use strict';\n\nvar numberIsNaN = function (value) {\n\treturn value !== value;\n};\n\nmodule.exports = function is(a, b) {\n\tif (a === 0 && b === 0) {\n\t\treturn 1 / a === 1 / b;\n\t}\n\tif (a === b) {\n\t\treturn true;\n\t}\n\tif (numberIsNaN(a) && numberIsNaN(b)) {\n\t\treturn true;\n\t}\n\treturn false;\n};\n\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,WAAW,GAAG,SAAAA,CAAUC,KAAK,EAAE;EAClC,OAAOA,KAAK,KAAKA,KAAK;AACvB,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAG,SAASC,EAAEA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAClC,IAAID,CAAC,KAAK,CAAC,IAAIC,CAAC,KAAK,CAAC,EAAE;IACvB,OAAO,CAAC,GAAGD,CAAC,KAAK,CAAC,GAAGC,CAAC;EACvB;EACA,IAAID,CAAC,KAAKC,CAAC,EAAE;IACZ,OAAO,IAAI;EACZ;EACA,IAAIN,WAAW,CAACK,CAAC,CAAC,IAAIL,WAAW,CAACM,CAAC,CAAC,EAAE;IACrC,OAAO,IAAI;EACZ;EACA,OAAO,KAAK;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}