{"ast": null, "code": "'use strict';\n\n/* eslint no-invalid-this: 1 */\nvar ERROR_MESSAGE = 'Function.prototype.bind called on incompatible ';\nvar toStr = Object.prototype.toString;\nvar max = Math.max;\nvar funcType = '[object Function]';\nvar concatty = function concatty(a, b) {\n  var arr = [];\n  for (var i = 0; i < a.length; i += 1) {\n    arr[i] = a[i];\n  }\n  for (var j = 0; j < b.length; j += 1) {\n    arr[j + a.length] = b[j];\n  }\n  return arr;\n};\nvar slicy = function slicy(arrLike, offset) {\n  var arr = [];\n  for (var i = offset || 0, j = 0; i < arrLike.length; i += 1, j += 1) {\n    arr[j] = arrLike[i];\n  }\n  return arr;\n};\nvar joiny = function (arr, joiner) {\n  var str = '';\n  for (var i = 0; i < arr.length; i += 1) {\n    str += arr[i];\n    if (i + 1 < arr.length) {\n      str += joiner;\n    }\n  }\n  return str;\n};\nmodule.exports = function bind(that) {\n  var target = this;\n  if (typeof target !== 'function' || toStr.apply(target) !== funcType) {\n    throw new TypeError(ERROR_MESSAGE + target);\n  }\n  var args = slicy(arguments, 1);\n  var bound;\n  var binder = function () {\n    if (this instanceof bound) {\n      var result = target.apply(this, concatty(args, arguments));\n      if (Object(result) === result) {\n        return result;\n      }\n      return this;\n    }\n    return target.apply(that, concatty(args, arguments));\n  };\n  var boundLength = max(0, target.length - args.length);\n  var boundArgs = [];\n  for (var i = 0; i < boundLength; i++) {\n    boundArgs[i] = '$' + i;\n  }\n  bound = Function('binder', 'return function (' + joiny(boundArgs, ',') + '){ return binder.apply(this,arguments); }')(binder);\n  if (target.prototype) {\n    var Empty = function Empty() {};\n    Empty.prototype = target.prototype;\n    bound.prototype = new Empty();\n    Empty.prototype = null;\n  }\n  return bound;\n};", "map": {"version": 3, "names": ["ERROR_MESSAGE", "toStr", "Object", "prototype", "toString", "max", "Math", "funcType", "concatty", "a", "b", "arr", "i", "length", "j", "slicy", "arrLike", "offset", "joiny", "joiner", "str", "module", "exports", "bind", "that", "target", "apply", "TypeError", "args", "arguments", "bound", "binder", "result", "<PERSON><PERSON><PERSON><PERSON>", "boundArgs", "Function", "Empty"], "sources": ["C:/ignition/ignition-ui/node_modules/function-bind/implementation.js"], "sourcesContent": ["'use strict';\n\n/* eslint no-invalid-this: 1 */\n\nvar ERROR_MESSAGE = 'Function.prototype.bind called on incompatible ';\nvar toStr = Object.prototype.toString;\nvar max = Math.max;\nvar funcType = '[object Function]';\n\nvar concatty = function concatty(a, b) {\n    var arr = [];\n\n    for (var i = 0; i < a.length; i += 1) {\n        arr[i] = a[i];\n    }\n    for (var j = 0; j < b.length; j += 1) {\n        arr[j + a.length] = b[j];\n    }\n\n    return arr;\n};\n\nvar slicy = function slicy(arrLike, offset) {\n    var arr = [];\n    for (var i = offset || 0, j = 0; i < arrLike.length; i += 1, j += 1) {\n        arr[j] = arrLike[i];\n    }\n    return arr;\n};\n\nvar joiny = function (arr, joiner) {\n    var str = '';\n    for (var i = 0; i < arr.length; i += 1) {\n        str += arr[i];\n        if (i + 1 < arr.length) {\n            str += joiner;\n        }\n    }\n    return str;\n};\n\nmodule.exports = function bind(that) {\n    var target = this;\n    if (typeof target !== 'function' || toStr.apply(target) !== funcType) {\n        throw new TypeError(ERROR_MESSAGE + target);\n    }\n    var args = slicy(arguments, 1);\n\n    var bound;\n    var binder = function () {\n        if (this instanceof bound) {\n            var result = target.apply(\n                this,\n                concatty(args, arguments)\n            );\n            if (Object(result) === result) {\n                return result;\n            }\n            return this;\n        }\n        return target.apply(\n            that,\n            concatty(args, arguments)\n        );\n\n    };\n\n    var boundLength = max(0, target.length - args.length);\n    var boundArgs = [];\n    for (var i = 0; i < boundLength; i++) {\n        boundArgs[i] = '$' + i;\n    }\n\n    bound = Function('binder', 'return function (' + joiny(boundArgs, ',') + '){ return binder.apply(this,arguments); }')(binder);\n\n    if (target.prototype) {\n        var Empty = function Empty() {};\n        Empty.prototype = target.prototype;\n        bound.prototype = new Empty();\n        Empty.prototype = null;\n    }\n\n    return bound;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AAEA,IAAIA,aAAa,GAAG,iDAAiD;AACrE,IAAIC,KAAK,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ;AACrC,IAAIC,GAAG,GAAGC,IAAI,CAACD,GAAG;AAClB,IAAIE,QAAQ,GAAG,mBAAmB;AAElC,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACnC,IAAIC,GAAG,GAAG,EAAE;EAEZ,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,CAAC,CAACI,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IAClCD,GAAG,CAACC,CAAC,CAAC,GAAGH,CAAC,CAACG,CAAC,CAAC;EACjB;EACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,CAAC,CAACG,MAAM,EAAEC,CAAC,IAAI,CAAC,EAAE;IAClCH,GAAG,CAACG,CAAC,GAAGL,CAAC,CAACI,MAAM,CAAC,GAAGH,CAAC,CAACI,CAAC,CAAC;EAC5B;EAEA,OAAOH,GAAG;AACd,CAAC;AAED,IAAII,KAAK,GAAG,SAASA,KAAKA,CAACC,OAAO,EAAEC,MAAM,EAAE;EACxC,IAAIN,GAAG,GAAG,EAAE;EACZ,KAAK,IAAIC,CAAC,GAAGK,MAAM,IAAI,CAAC,EAAEH,CAAC,GAAG,CAAC,EAAEF,CAAC,GAAGI,OAAO,CAACH,MAAM,EAAED,CAAC,IAAI,CAAC,EAAEE,CAAC,IAAI,CAAC,EAAE;IACjEH,GAAG,CAACG,CAAC,CAAC,GAAGE,OAAO,CAACJ,CAAC,CAAC;EACvB;EACA,OAAOD,GAAG;AACd,CAAC;AAED,IAAIO,KAAK,GAAG,SAAAA,CAAUP,GAAG,EAAEQ,MAAM,EAAE;EAC/B,IAAIC,GAAG,GAAG,EAAE;EACZ,KAAK,IAAIR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,GAAG,CAACE,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;IACpCQ,GAAG,IAAIT,GAAG,CAACC,CAAC,CAAC;IACb,IAAIA,CAAC,GAAG,CAAC,GAAGD,GAAG,CAACE,MAAM,EAAE;MACpBO,GAAG,IAAID,MAAM;IACjB;EACJ;EACA,OAAOC,GAAG;AACd,CAAC;AAEDC,MAAM,CAACC,OAAO,GAAG,SAASC,IAAIA,CAACC,IAAI,EAAE;EACjC,IAAIC,MAAM,GAAG,IAAI;EACjB,IAAI,OAAOA,MAAM,KAAK,UAAU,IAAIxB,KAAK,CAACyB,KAAK,CAACD,MAAM,CAAC,KAAKlB,QAAQ,EAAE;IAClE,MAAM,IAAIoB,SAAS,CAAC3B,aAAa,GAAGyB,MAAM,CAAC;EAC/C;EACA,IAAIG,IAAI,GAAGb,KAAK,CAACc,SAAS,EAAE,CAAC,CAAC;EAE9B,IAAIC,KAAK;EACT,IAAIC,MAAM,GAAG,SAAAA,CAAA,EAAY;IACrB,IAAI,IAAI,YAAYD,KAAK,EAAE;MACvB,IAAIE,MAAM,GAAGP,MAAM,CAACC,KAAK,CACrB,IAAI,EACJlB,QAAQ,CAACoB,IAAI,EAAEC,SAAS,CAC5B,CAAC;MACD,IAAI3B,MAAM,CAAC8B,MAAM,CAAC,KAAKA,MAAM,EAAE;QAC3B,OAAOA,MAAM;MACjB;MACA,OAAO,IAAI;IACf;IACA,OAAOP,MAAM,CAACC,KAAK,CACfF,IAAI,EACJhB,QAAQ,CAACoB,IAAI,EAAEC,SAAS,CAC5B,CAAC;EAEL,CAAC;EAED,IAAII,WAAW,GAAG5B,GAAG,CAAC,CAAC,EAAEoB,MAAM,CAACZ,MAAM,GAAGe,IAAI,CAACf,MAAM,CAAC;EACrD,IAAIqB,SAAS,GAAG,EAAE;EAClB,KAAK,IAAItB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGqB,WAAW,EAAErB,CAAC,EAAE,EAAE;IAClCsB,SAAS,CAACtB,CAAC,CAAC,GAAG,GAAG,GAAGA,CAAC;EAC1B;EAEAkB,KAAK,GAAGK,QAAQ,CAAC,QAAQ,EAAE,mBAAmB,GAAGjB,KAAK,CAACgB,SAAS,EAAE,GAAG,CAAC,GAAG,2CAA2C,CAAC,CAACH,MAAM,CAAC;EAE7H,IAAIN,MAAM,CAACtB,SAAS,EAAE;IAClB,IAAIiC,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG,CAAC,CAAC;IAC/BA,KAAK,CAACjC,SAAS,GAAGsB,MAAM,CAACtB,SAAS;IAClC2B,KAAK,CAAC3B,SAAS,GAAG,IAAIiC,KAAK,CAAC,CAAC;IAC7BA,KAAK,CAACjC,SAAS,GAAG,IAAI;EAC1B;EAEA,OAAO2B,KAAK;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}