{"ast": null, "code": "'use client';\n\nimport { createContainer } from '@mui/system';\nimport PropTypes from 'prop-types';\nimport styled from '../styles/styled';\nimport { useThemeProps } from '../styles';\nconst Container = createContainer({\n  createStyledComponent: styled('div', {\n    name: '<PERSON><PERSON>ontaine<PERSON>',\n    slot: 'Root',\n    overridesResolver: (props, styles) => styles.root\n  }),\n  useThemeProps: inProps => useThemeProps({\n    props: inProps,\n    name: 'JoyContainer'\n  })\n});\nprocess.env.NODE_ENV !== \"production\" ? Container.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the left and right padding is removed.\n   * @default false\n   */\n  disableGutters: PropTypes.bool,\n  /**\n   * Set the max-width to match the min-width of the current breakpoint.\n   * This is useful if you'd prefer to design for a fixed set of sizes\n   * instead of trying to accommodate a fully fluid viewport.\n   * It's fluid by default.\n   * @default false\n   */\n  fixed: PropTypes.bool,\n  /**\n   * Determine the max-width of the container.\n   * The container width grows with the size of the screen.\n   * Set to `false` to disable `maxWidth`.\n   * @default 'lg'\n   */\n  maxWidth: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl', false]), PropTypes.string]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Container;", "map": {"version": 3, "names": ["createContainer", "PropTypes", "styled", "useThemeProps", "Container", "createStyledComponent", "name", "slot", "overridesResolver", "props", "styles", "root", "inProps", "process", "env", "NODE_ENV", "propTypes", "children", "node", "component", "elementType", "disableGutters", "bool", "fixed", "max<PERSON><PERSON><PERSON>", "oneOfType", "oneOf", "string", "sx", "arrayOf", "func", "object"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Container/Container.js"], "sourcesContent": ["'use client';\n\nimport { createContainer } from '@mui/system';\nimport PropTypes from 'prop-types';\nimport styled from '../styles/styled';\nimport { useThemeProps } from '../styles';\nconst Container = createContainer({\n  createStyledComponent: styled('div', {\n    name: '<PERSON><PERSON>ontaine<PERSON>',\n    slot: 'Root',\n    overridesResolver: (props, styles) => styles.root\n  }),\n  useThemeProps: inProps => useThemeProps({\n    props: inProps,\n    name: 'JoyContainer'\n  })\n});\nprocess.env.NODE_ENV !== \"production\" ? Container.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the left and right padding is removed.\n   * @default false\n   */\n  disableGutters: PropTypes.bool,\n  /**\n   * Set the max-width to match the min-width of the current breakpoint.\n   * This is useful if you'd prefer to design for a fixed set of sizes\n   * instead of trying to accommodate a fully fluid viewport.\n   * It's fluid by default.\n   * @default false\n   */\n  fixed: PropTypes.bool,\n  /**\n   * Determine the max-width of the container.\n   * The container width grows with the size of the screen.\n   * Set to `false` to disable `maxWidth`.\n   * @default 'lg'\n   */\n  maxWidth: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl', false]), PropTypes.string]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Container;"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,eAAe,QAAQ,aAAa;AAC7C,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,aAAa,QAAQ,WAAW;AACzC,MAAMC,SAAS,GAAGJ,eAAe,CAAC;EAChCK,qBAAqB,EAAEH,MAAM,CAAC,KAAK,EAAE;IACnCI,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,MAAM;IACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACC;EAC/C,CAAC,CAAC;EACFR,aAAa,EAAES,OAAO,IAAIT,aAAa,CAAC;IACtCM,KAAK,EAAEG,OAAO;IACdN,IAAI,EAAE;EACR,CAAC;AACH,CAAC,CAAC;AACFO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGX,SAAS,CAACY,SAAS,CAAC,yBAAyB;EACnF;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAEhB,SAAS,CAACiB,IAAI;EACxB;AACF;AACA;AACA;EACEC,SAAS,EAAElB,SAAS,CAACmB,WAAW;EAChC;AACF;AACA;AACA;EACEC,cAAc,EAAEpB,SAAS,CAACqB,IAAI;EAC9B;AACF;AACA;AACA;AACA;AACA;AACA;EACEC,KAAK,EAAEtB,SAAS,CAACqB,IAAI;EACrB;AACF;AACA;AACA;AACA;AACA;EACEE,QAAQ,EAAEvB,SAAS,CAAC,sCAAsCwB,SAAS,CAAC,CAACxB,SAAS,CAACyB,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,EAAEzB,SAAS,CAAC0B,MAAM,CAAC,CAAC;EAC/I;AACF;AACA;EACEC,EAAE,EAAE3B,SAAS,CAACwB,SAAS,CAAC,CAACxB,SAAS,CAAC4B,OAAO,CAAC5B,SAAS,CAACwB,SAAS,CAAC,CAACxB,SAAS,CAAC6B,IAAI,EAAE7B,SAAS,CAAC8B,MAAM,EAAE9B,SAAS,CAACqB,IAAI,CAAC,CAAC,CAAC,EAAErB,SAAS,CAAC6B,IAAI,EAAE7B,SAAS,CAAC8B,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAe3B,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}