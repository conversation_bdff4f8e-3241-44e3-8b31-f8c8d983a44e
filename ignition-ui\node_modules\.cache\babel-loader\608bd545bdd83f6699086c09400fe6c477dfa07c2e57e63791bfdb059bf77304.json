{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getDialogContentUtilityClass(slot) {\n  return generateUtilityClass('MuiDialogContent', slot);\n}\nconst dialogContentClasses = generateUtilityClasses('MuiDialogContent', ['root']);\nexport default dialogContentClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getDialogContentUtilityClass", "slot", "dialogContentClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/DialogContent/dialogContentClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getDialogContentUtilityClass(slot) {\n  return generateUtilityClass('MuiDialogContent', slot);\n}\nconst dialogContentClasses = generateUtilityClasses('MuiDialogContent', ['root']);\nexport default dialogContentClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,4BAA4BA,CAACC,IAAI,EAAE;EACjD,OAAOH,oBAAoB,CAAC,kBAAkB,EAAEG,IAAI,CAAC;AACvD;AACA,MAAMC,oBAAoB,GAAGH,sBAAsB,CAAC,kBAAkB,EAAE,CAAC,MAAM,CAAC,CAAC;AACjF,eAAeG,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}