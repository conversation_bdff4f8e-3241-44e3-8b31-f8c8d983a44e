{"ast": null, "code": "var _jsxFileName = \"C:\\\\ignition\\\\ignition-ui\\\\src\\\\views\\\\plan\\\\detail\\\\components\\\\MilestoneCard.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Box, Typography, Paper, Chip, LinearProgress, Button, Divider, TextField, IconButton, Tooltip, Checkbox, CircularProgress, Avatar, AvatarGroup, Collapse } from '@mui/material';\nimport Iconify from 'components/Iconify/index';\nimport { mainYellowColor } from \"helpers/constants\";\nimport { STATUS, STATUS_CONFIG } from '../hooks/usePlanData';\nimport styles from '../styles.module.scss';\nimport CommentDialog from '../dialogs/CommentDialog';\nimport DueDateDialog from '../dialogs/DueDateDialog';\nimport AssignMemberDialog from '../dialogs/AssignMemberDialog';\nimport { getComments, addComment, updateComment, deleteComment, updateTask, assignMembersToTask } from '../../services';\nimport { toast } from 'react-toastify';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MilestoneCard = _ref => {\n  _s();\n  var _milestone$tasks, _milestone$tasks2, _milestone$tasks3;\n  let {\n    milestone,\n    compact = false,\n    showSubtasks = false,\n    calculateMilestoneProgress,\n    getMilestoneStatus,\n    calculateTaskProgress,\n    getTaskStatus,\n    calculateSubtaskProgress,\n    getSubtaskStatus,\n    onUpdateMilestone,\n    onUpdateTask,\n    onUpdateSubtask,\n    onAddTask,\n    onAddSubtask,\n    onDeleteTask,\n    onDeleteSubtask,\n    onAssignMembers,\n    invitedUsers,\n    planOwner\n  } = _ref;\n  const [isEditing, setIsEditing] = useState(false);\n  const [milestoneName, setMilestoneName] = useState(milestone.name);\n  const [isAddingTask, setIsAddingTask] = useState(false);\n  const [newTaskName, setNewTaskName] = useState('');\n  const [isExpanded, setIsExpanded] = useState(true); // Add collapsible state\n  const inputRef = useRef(null);\n  const newTaskInputRef = useRef(null);\n\n  // Calculate milestone progress\n  const progress = calculateMilestoneProgress ? calculateMilestoneProgress(milestone) : 0;\n\n  // Determine status based on progress\n  const milestoneStatus = getMilestoneStatus ? getMilestoneStatus(milestone) : STATUS.NOT_STARTED;\n  const statusConfig = STATUS_CONFIG[milestoneStatus] || STATUS_CONFIG[STATUS.NOT_STARTED];\n\n  // Check if description exists and is not empty\n  const hasDescription = milestone.description && milestone.description.trim().length > 0;\n\n  // Handle edit mode\n  const handleEditClick = () => {\n    setIsEditing(true);\n  };\n\n  // Handle save changes\n  const handleSave = () => {\n    // Trim the name to remove leading/trailing whitespace\n    const trimmedName = milestoneName.trim();\n\n    // Only update if the name has actually changed (after trimming)\n    if (trimmedName !== '' && trimmedName !== milestone.name) {\n      if (onUpdateMilestone) {\n        onUpdateMilestone({\n          ...milestone,\n          name: trimmedName\n        });\n      }\n    } else {\n      // Reset to original if empty or unchanged\n      setMilestoneName(milestone.name);\n    }\n    setIsEditing(false);\n  };\n\n  // Handle key press events\n  const handleKeyPress = e => {\n    if (e.key === 'Enter') {\n      handleSave();\n    } else if (e.key === 'Escape') {\n      setMilestoneName(milestone.name);\n      setIsEditing(false);\n    }\n  };\n\n  // Handle add task\n  const handleAddTaskClick = () => {\n    setIsAddingTask(true);\n  };\n\n  // Handle save new task\n  const handleSaveNewTask = () => {\n    const trimmedName = newTaskName.trim();\n    if (trimmedName && onAddTask) {\n      const newTask = {\n        name: trimmedName,\n        milestone: milestone.id,\n        status: STATUS.NOT_STARTED,\n        progress: 0\n      };\n      onAddTask(newTask);\n      setNewTaskName('');\n    }\n    setIsAddingTask(false);\n  };\n\n  // Handle key press events for new task\n  const handleNewTaskKeyPress = e => {\n    if (e.key === 'Enter') {\n      handleSaveNewTask();\n    } else if (e.key === 'Escape') {\n      setNewTaskName('');\n      setIsAddingTask(false);\n    }\n  };\n\n  // Focus input when editing starts\n  useEffect(() => {\n    if (isEditing && inputRef.current) {\n      inputRef.current.focus();\n    }\n  }, [isEditing]);\n\n  // Focus input when adding task\n  useEffect(() => {\n    if (isAddingTask && newTaskInputRef.current) {\n      newTaskInputRef.current.focus();\n    }\n  }, [isAddingTask]);\n  return /*#__PURE__*/_jsxDEV(Paper, {\n    elevation: 0,\n    className: styles.milestoneCard,\n    sx: {\n      padding: '16px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      className: styles.milestoneHeader,\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          flex: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          onClick: () => setIsExpanded(!isExpanded),\n          sx: {\n            mr: 1,\n            color: '#666',\n            '&:hover': {\n              backgroundColor: 'rgba(0, 0, 0, 0.04)'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(Iconify, {\n            icon: isExpanded ? \"material-symbols:expand-less\" : \"material-symbols:expand-more\",\n            width: 20,\n            height: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this), isEditing ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"material-symbols:flag\",\n            width: 20,\n            height: 20,\n            color: mainYellowColor,\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            inputRef: inputRef,\n            value: milestoneName,\n            onChange: e => setMilestoneName(e.target.value),\n            onKeyDown: handleKeyPress,\n            onBlur: handleSave,\n            variant: \"standard\",\n            fullWidth: true,\n            autoFocus: true,\n            sx: {\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              fontWeight: 600,\n              fontSize: '1.1rem',\n              '& .MuiInputBase-input': {\n                fontWeight: 600,\n                fontSize: '1.1rem',\n                fontFamily: '\"Recursive Variable\", sans-serif'\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 176,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            onClick: handleSave,\n            sx: {\n              ml: 1\n            },\n            children: /*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"material-symbols:check\",\n              width: 20,\n              height: 20,\n              color: \"#4CAF50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 197,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 196,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          className: styles.milestoneTitle,\n          sx: {\n            fontFamily: '\"Recursive Variable\", sans-serif',\n            display: 'flex',\n            alignItems: 'center',\n            cursor: 'pointer',\n            flex: 1,\n            '&:hover': {\n              '& .edit-icon': {\n                opacity: 1\n              }\n            }\n          },\n          onClick: handleEditClick,\n          children: [/*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"material-symbols:flag\",\n            width: 20,\n            height: 20,\n            color: mainYellowColor\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 15\n          }, this), milestoneName, /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: \"Edit milestone name\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              className: \"edit-icon\",\n              sx: {\n                ml: 1,\n                opacity: 0,\n                transition: 'opacity 0.2s',\n                padding: '2px'\n              },\n              children: /*#__PURE__*/_jsxDEV(Iconify, {\n                icon: \"material-symbols:edit-outline\",\n                width: 16,\n                height: 16,\n                color: \"#666\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 231,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 220,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 153,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n        icon: /*#__PURE__*/_jsxDEV(Iconify, {\n          icon: statusConfig.icon,\n          width: 16,\n          height: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 239,\n          columnNumber: 17\n        }, this),\n        label: statusConfig.label,\n        size: \"small\",\n        sx: {\n          backgroundColor: `${statusConfig.color}20`,\n          color: statusConfig.color,\n          fontWeight: 600,\n          borderRadius: '4px',\n          fontFamily: '\"Recursive Variable\", sans-serif'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 238,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 152,\n      columnNumber: 7\n    }, this), !compact && hasDescription && /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 2,\n        mt: 1.5\n      },\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          backgroundColor: '#f9f9f9',\n          p: 1.5,\n          borderRadius: '8px',\n          border: '1px solid #f0f0f0'\n        },\n        children: /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            color: '#333',\n            fontFamily: '\"Recursive Variable\", sans-serif',\n            whiteSpace: 'pre-line',\n            lineHeight: 1.6,\n            fontWeight: 500\n          },\n          children: milestone.description\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 255,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 254,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Divider, {\n      sx: {\n        my: 2,\n        opacity: 0.6\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 279,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 2\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          mb: 0.5\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            fontWeight: 600,\n            fontFamily: '\"Recursive Variable\", sans-serif',\n            color: '#333'\n          },\n          children: \"Progress\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            fontWeight: 700,\n            fontFamily: '\"Recursive Variable\", sans-serif',\n            color: '#333'\n          },\n          children: [progress, \"%\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 286,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n        variant: \"determinate\",\n        value: progress,\n        sx: {\n          height: 6,\n          borderRadius: 3,\n          backgroundColor: '#f0f0f0',\n          '& .MuiLinearProgress-bar': {\n            backgroundColor: statusConfig.color\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 281,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        alignItems: 'center',\n        mb: 1\n      },\n      children: milestone.estimated_duration && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Iconify, {\n          icon: \"material-symbols:timer\",\n          width: 16,\n          height: 16,\n          color: \"#333\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 307,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          sx: {\n            fontFamily: '\"Recursive Variable\", sans-serif',\n            color: '#333',\n            fontWeight: 500\n          },\n          children: milestone.estimated_duration\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 308,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 306,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 304,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          mb: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"material-symbols:checklist\",\n            width: 16,\n            height: 16,\n            sx: {\n              color: '#333'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 600,\n              color: '#333',\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              fontSize: '0.9rem'\n            },\n            children: \"Tasks\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 332,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 325,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n          title: \"Add new task\",\n          children: /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            onClick: handleAddTaskClick,\n            sx: {\n              color: mainYellowColor,\n              '&:hover': {\n                backgroundColor: `${mainYellowColor}10`\n              }\n            },\n            children: /*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"material-symbols:add-task\",\n              width: 18,\n              height: 18\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 357,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 347,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 346,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 9\n      }, this), compact ? /*#__PURE__*/_jsxDEV(Box, {\n        className: styles.taskList,\n        children: [(_milestone$tasks = milestone.tasks) === null || _milestone$tasks === void 0 ? void 0 : _milestone$tasks.slice(0, 2).map((task, index) => /*#__PURE__*/_jsxDEV(TaskItem, {\n          task: task,\n          showSubtasks: showSubtasks,\n          compact: true,\n          calculateTaskProgress: calculateTaskProgress,\n          getTaskStatus: getTaskStatus,\n          calculateSubtaskProgress: calculateSubtaskProgress,\n          getSubtaskStatus: getSubtaskStatus,\n          onUpdateTask: onUpdateTask,\n          onUpdateSubtask: onUpdateSubtask,\n          onAddSubtask: onAddSubtask,\n          onDeleteTask: onDeleteTask,\n          onDeleteSubtask: onDeleteSubtask,\n          onAssignMembers: onAssignMembers,\n          invitedUsers: invitedUsers,\n          planOwner: planOwner\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 15\n        }, this)), ((_milestone$tasks2 = milestone.tasks) === null || _milestone$tasks2 === void 0 ? void 0 : _milestone$tasks2.length) > 2 && /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"text\",\n          size: \"small\",\n          sx: {\n            color: mainYellowColor,\n            fontWeight: 600,\n            textTransform: 'none',\n            p: 0,\n            mt: 1,\n            fontFamily: '\"Recursive Variable\", sans-serif'\n          },\n          children: [\"+ \", milestone.tasks.length - 2, \" more tasks\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 363,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(Box, {\n        className: styles.taskList,\n        children: [(_milestone$tasks3 = milestone.tasks) === null || _milestone$tasks3 === void 0 ? void 0 : _milestone$tasks3.map((task, index) => /*#__PURE__*/_jsxDEV(TaskItem, {\n          task: task,\n          showSubtasks: showSubtasks,\n          compact: false,\n          calculateTaskProgress: calculateTaskProgress,\n          getTaskStatus: getTaskStatus,\n          calculateSubtaskProgress: calculateSubtaskProgress,\n          getSubtaskStatus: getSubtaskStatus,\n          onUpdateTask: onUpdateTask,\n          onUpdateSubtask: onUpdateSubtask,\n          onAddSubtask: onAddSubtask,\n          onDeleteTask: onDeleteTask,\n          onDeleteSubtask: onDeleteSubtask,\n          onAssignMembers: onAssignMembers,\n          invitedUsers: invitedUsers,\n          planOwner: planOwner\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 405,\n          columnNumber: 15\n        }, this)), isAddingTask && /*#__PURE__*/_jsxDEV(Box, {\n          className: styles.taskItem,\n          sx: {\n            position: 'relative',\n            mt: 1,\n            display: 'flex',\n            alignItems: 'center',\n            backgroundColor: '#f9f9f9',\n            borderRadius: '6px',\n            padding: '8px 12px',\n            border: '1px solid #f0f0f0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              width: 10,\n              height: 10,\n              borderRadius: '50%',\n              backgroundColor: '#CCCCCC',\n              mr: 1.5,\n              flexShrink: 0\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            inputRef: newTaskInputRef,\n            value: newTaskName,\n            onChange: e => setNewTaskName(e.target.value),\n            onKeyDown: handleNewTaskKeyPress,\n            placeholder: \"Enter new task name...\",\n            variant: \"standard\",\n            fullWidth: true,\n            autoFocus: true,\n            sx: {\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              '& .MuiInputBase-input': {\n                fontSize: '0.9rem',\n                fontFamily: '\"Recursive Variable\", sans-serif',\n                padding: '4px 0'\n              },\n              '& .MuiInput-underline:before': {\n                borderBottomColor: 'rgba(0, 0, 0, 0.1)'\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              ml: 'auto'\n            },\n            children: [/*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: handleSaveNewTask,\n              sx: {\n                ml: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Iconify, {\n                icon: \"material-symbols:check\",\n                width: 18,\n                height: 18,\n                color: \"#4CAF50\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 475,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: () => setIsAddingTask(false),\n              sx: {\n                ml: 0.5\n              },\n              children: /*#__PURE__*/_jsxDEV(Iconify, {\n                icon: \"material-symbols:close\",\n                width: 18,\n                height: 18,\n                color: \"#F44336\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 479,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 478,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 427,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 403,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 316,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 147,\n    columnNumber: 5\n  }, this);\n};\n// Component to display task and subtask\n_s(MilestoneCard, \"4qfr46vuLtQawaY9XK3XyHondE8=\");\n_c = MilestoneCard;\nconst TaskItem = _ref2 => {\n  _s2();\n  var _localTask$assignees;\n  let {\n    task,\n    showSubtasks = false,\n    compact = false,\n    calculateTaskProgress,\n    getTaskStatus,\n    calculateSubtaskProgress,\n    getSubtaskStatus,\n    onUpdateTask,\n    onUpdateSubtask,\n    onAddSubtask,\n    onDeleteTask,\n    onDeleteSubtask,\n    onAssignMembers,\n    invitedUsers,\n    planOwner\n  } = _ref2;\n  const [isEditing, setIsEditing] = useState(false);\n  const [taskName, setTaskName] = useState(task.name);\n  const taskInputRef = useRef(null);\n  const [isAddingSubtask, setIsAddingSubtask] = useState(false);\n  const [newSubtaskName, setNewSubtaskName] = useState('');\n  const newSubtaskInputRef = useRef(null);\n\n  // Comment functionality\n  const [commentDialogOpen, setCommentDialogOpen] = useState(false);\n  const [comments, setComments] = useState([]);\n  const [loadingComments, setLoadingComments] = useState(false);\n\n  // Enable due date functionality\n  const [dueDateDialogOpen, setDueDateDialogOpen] = useState(false);\n  const [updatingDueDate, setUpdatingDueDate] = useState(false);\n\n  // Add state for assign member dialog\n  const [assignMemberDialogOpen, setAssignMemberDialogOpen] = useState(false);\n\n  // Add state to store current task information\n  const [localTask, setLocalTask] = useState(task);\n\n  // Update localTask when task changes from props\n  useEffect(() => {\n    setLocalTask(task);\n  }, [task]);\n  const handleOpenComments = e => {\n    e.stopPropagation();\n    setCommentDialogOpen(true);\n    setLoadingComments(true);\n    getComments(task.id).then(response => {\n      var _response$data, _response$data2;\n      console.log('Comments data:', response.data);\n      // Check data structure and get correct comments array\n      let commentsData = [];\n      if (Array.isArray(response.data)) {\n        commentsData = response.data;\n      } else if ((_response$data = response.data) !== null && _response$data !== void 0 && _response$data.data && Array.isArray(response.data.data)) {\n        commentsData = response.data.data;\n      } else if ((_response$data2 = response.data) !== null && _response$data2 !== void 0 && _response$data2.comments && Array.isArray(response.data.comments)) {\n        commentsData = response.data.comments;\n      }\n      setComments(commentsData);\n    }).catch(error => {\n      console.error('Error fetching comments:', error);\n      toast.error('Failed to load comments');\n    }).finally(() => {\n      setLoadingComments(false);\n    });\n  };\n  const handleAddComment = async content => {\n    try {\n      var _response$data3;\n      const response = await addComment(task.id, content);\n      console.log('Add comment response:', response);\n\n      // Create new comment from response or create temporary object\n      let newComment;\n      if ((_response$data3 = response.data) !== null && _response$data3 !== void 0 && _response$data3.data) {\n        newComment = response.data.data;\n      } else {\n        // Create temporary object if API does not return new comment\n        const currentUser = JSON.parse(localStorage.getItem('user')) || {};\n        newComment = {\n          id: Date.now(),\n          // Temporary ID\n          content: content,\n          user: {\n            id: currentUser.id,\n            first_name: currentUser.first_name,\n            last_name: currentUser.last_name,\n            avatar: currentUser.avatar\n          },\n          created_at: new Date().toLocaleString('en-US', {\n            hour: '2-digit',\n            minute: '2-digit',\n            day: '2-digit',\n            month: '2-digit'\n          }).replace(',', '')\n        };\n      }\n\n      // Update state\n      setComments(prevComments => [...prevComments, newComment]);\n\n      // Refresh comments to ensure latest data\n      refreshComments();\n    } catch (error) {\n      console.error('Error adding comment:', error);\n      toast.error('Failed to add comment');\n    }\n  };\n  const handleUpdateComment = async (commentId, content) => {\n    try {\n      // Update UI first\n      const updatedCommentTemp = comments.find(c => c.id === commentId);\n      if (updatedCommentTemp) {\n        const updatedComments = comments.map(c => c.id === commentId ? {\n          ...c,\n          content: content\n        } : c);\n        setComments(updatedComments);\n      }\n\n      // Call API\n      const response = await updateComment(commentId, content);\n      console.log('Update comment response:', response);\n      toast.success('Comment updated successfully');\n\n      // Refresh comments to ensure latest data\n      refreshComments();\n    } catch (error) {\n      console.error('Error updating comment:', error);\n      toast.error('Failed to update comment');\n      // Refresh comments to restore original state if there is an error\n      refreshComments();\n    }\n  };\n  const handleDeleteComment = async commentId => {\n    try {\n      // Update UI first\n      setComments(prevComments => prevComments.filter(c => c.id !== commentId));\n\n      // Call API\n      await deleteComment(commentId);\n      console.log('Comment deleted successfully');\n      toast.success('Comment deleted successfully');\n\n      // Refresh comments to ensure latest data\n      refreshComments();\n    } catch (error) {\n      console.error('Error deleting comment:', error);\n      toast.error('Failed to delete comment');\n      // Refresh comments to restore original state if there is an error\n      refreshComments();\n    }\n  };\n\n  // Refresh comments function\n  const refreshComments = async () => {\n    try {\n      var _response$data4, _response$data5;\n      const response = await getComments(task.id);\n      console.log('Refreshed comments:', response.data);\n\n      // Handle returned data\n      let commentsData = [];\n      if (Array.isArray(response.data)) {\n        commentsData = response.data;\n      } else if ((_response$data4 = response.data) !== null && _response$data4 !== void 0 && _response$data4.data && Array.isArray(response.data.data)) {\n        commentsData = response.data.data;\n      } else if ((_response$data5 = response.data) !== null && _response$data5 !== void 0 && _response$data5.comments && Array.isArray(response.data.comments)) {\n        commentsData = response.data.comments;\n      }\n      setComments(commentsData);\n    } catch (error) {\n      console.error('Error refreshing comments:', error);\n    }\n  };\n\n  // Calculate task progress\n  const taskProgress = calculateTaskProgress ? calculateTaskProgress(task) : 0;\n\n  // Determine task status\n  const taskStatus = getTaskStatus ? getTaskStatus(task) : task.status || STATUS.NOT_STARTED;\n  const statusConfig = STATUS_CONFIG[taskStatus] || STATUS_CONFIG[STATUS.NOT_STARTED];\n\n  // Check if task has subtasks\n  const hasSubtasks = task.subtasks && task.subtasks.length > 0;\n\n  // Check if task is completed\n  const isCompleted = taskStatus === STATUS.COMPLETED;\n\n  // Handle edit mode for task\n  const handleTaskEditClick = () => {\n    setIsEditing(true);\n  };\n\n  // Handle save changes for task\n  const handleTaskSave = () => {\n    // Trim the name to remove leading/trailing whitespace\n    const trimmedName = taskName.trim();\n\n    // Only update if the name has actually changed (after trimming)\n    if (trimmedName !== '' && trimmedName !== task.name) {\n      if (onUpdateTask) {\n        onUpdateTask({\n          ...task,\n          name: trimmedName\n        });\n      }\n    } else {\n      // Reset to original if empty or unchanged\n      setTaskName(task.name);\n    }\n    setIsEditing(false);\n  };\n\n  // Handle key press events for task\n  const handleTaskKeyPress = e => {\n    if (e.key === 'Enter') {\n      handleTaskSave();\n    } else if (e.key === 'Escape') {\n      setTaskName(task.name);\n      setIsEditing(false);\n    }\n  };\n\n  // Handle add subtask\n  const handleAddSubtaskClick = () => {\n    setIsAddingSubtask(true);\n  };\n\n  // Handle delete task\n  const handleDeleteTaskClick = () => {\n    if (onDeleteTask) {\n      onDeleteTask(task);\n    }\n  };\n\n  // Handle save new subtask\n  const handleSaveNewSubtask = () => {\n    const trimmedName = newSubtaskName.trim();\n    if (trimmedName && onAddSubtask) {\n      const newSubtask = {\n        name: trimmedName,\n        task: task.slug,\n        status: STATUS.NOT_STARTED,\n        progress: 0\n      };\n      onAddSubtask(newSubtask);\n      setNewSubtaskName('');\n    }\n    setIsAddingSubtask(false);\n  };\n\n  // Handle key press events for new subtask\n  const handleNewSubtaskKeyPress = e => {\n    if (e.key === 'Enter') {\n      handleSaveNewSubtask();\n    } else if (e.key === 'Escape') {\n      setNewSubtaskName('');\n      setIsAddingSubtask(false);\n    }\n  };\n\n  // Focus input when editing starts\n  useEffect(() => {\n    if (isEditing && taskInputRef.current) {\n      taskInputRef.current.focus();\n    }\n  }, [isEditing]);\n\n  // Focus input when adding subtask\n  useEffect(() => {\n    if (isAddingSubtask && newSubtaskInputRef.current) {\n      newSubtaskInputRef.current.focus();\n    }\n  }, [isAddingSubtask]);\n\n  // Check if task has due dates - use localTask instead of task\n  const hasDueDates = localTask.start_date || localTask.end_date;\n  const handleOpenDueDateDialog = e => {\n    e.stopPropagation();\n    setDueDateDialogOpen(true);\n  };\n  const handleUpdateDueDate = async updatedTask => {\n    setUpdatingDueDate(true);\n    try {\n      const taskToUpdate = {\n        ...localTask,\n        start_date: updatedTask.start_date,\n        end_date: updatedTask.end_date,\n        progress: localTask.progress || 0,\n        status: localTask.status || 1\n      };\n\n      // Update local state first\n      setLocalTask(taskToUpdate);\n\n      // Call API\n      const response = await updateTask(taskToUpdate);\n\n      // If API returns data, update local state\n      if (response && response.data) {\n        setLocalTask(response.data);\n      }\n\n      // Update state in parent component if necessary\n      if (onUpdateTask) {\n        onUpdateTask(taskToUpdate);\n      }\n      toast.success('Due date updated successfully');\n    } catch (error) {\n      console.error('Error updating due date:', error);\n      toast.error('Failed to update due date');\n\n      // If there is an error, restore original state\n      setLocalTask(task);\n    } finally {\n      setUpdatingDueDate(false);\n      setDueDateDialogOpen(false); // Close dialog after completion\n    }\n  };\n\n  // Add handler for member assignment updates\n  const handleAssignMembers = async assignedUserIds => {\n    try {\n      // Convert IDs to numbers\n      const numericIds = assignedUserIds.map(id => Number(id));\n      // Call API to assign members\n      const response = await assignMembersToTask(localTask.slug, numericIds);\n      // Create updated task with new assignees\n      const updatedTask = {\n        ...localTask,\n        assignees: response.assignees || numericIds.map(id => ({\n          id: id,\n          first_name: '',\n          last_name: '',\n          email: ''\n        }))\n      };\n\n      // Update both local state and parent state\n      setLocalTask(updatedTask);\n      if (onUpdateTask) {\n        onUpdateTask(updatedTask);\n      }\n      setAssignMemberDialogOpen(false);\n      toast.success('Members assigned successfully');\n    } catch (error) {\n      console.error('Error assigning members:', error);\n      toast.error('Failed to assign members');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      className: styles.taskItemContainer,\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        className: styles.taskItem,\n        sx: {\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: 10,\n            height: 10,\n            borderRadius: '50%',\n            backgroundColor: statusConfig.color,\n            mr: 1.5,\n            flexShrink: 0\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 857,\n          columnNumber: 11\n        }, this), isEditing ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            flexGrow: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            inputRef: taskInputRef,\n            value: taskName,\n            onChange: e => setTaskName(e.target.value),\n            onKeyDown: handleTaskKeyPress,\n            onBlur: handleTaskSave,\n            variant: \"standard\",\n            fullWidth: true,\n            autoFocus: true,\n            sx: {\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              '& .MuiInputBase-input': {\n                fontSize: '0.9rem',\n                fontFamily: '\"Recursive Variable\", sans-serif'\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 870,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            onClick: handleTaskSave,\n            sx: {\n              ml: 1\n            },\n            children: /*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"material-symbols:check\",\n              width: 16,\n              height: 16,\n              color: \"#4CAF50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 888,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 887,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 869,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            flexGrow: 1,\n            display: 'flex',\n            flexDirection: 'column',\n            gap: 0.5\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between',\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 2,\n                flexGrow: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                className: styles.taskName,\n                sx: {\n                  fontFamily: '\"Recursive Variable\", sans-serif',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  color: isCompleted ? '#4CAF50' : 'inherit',\n                  fontWeight: isCompleted ? 500 : 400,\n                  flexGrow: 1,\n                  '&:hover': {\n                    '& .task-edit-icon': {\n                      opacity: 1\n                    }\n                  }\n                },\n                onClick: handleTaskEditClick,\n                children: [taskName, /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Edit task name\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    className: \"task-edit-icon\",\n                    sx: {\n                      ml: 0.5,\n                      opacity: 0,\n                      transition: 'opacity 0.2s',\n                      padding: '2px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Iconify, {\n                      icon: \"material-symbols:edit-outline\",\n                      width: 14,\n                      height: 14,\n                      color: \"#666\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 925,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 915,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 914,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 895,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 894,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 2\n              },\n              children: [localTask.assignees && localTask.assignees.length > 0 && /*#__PURE__*/_jsxDEV(AvatarGroup, {\n                max: 3,\n                sx: {\n                  '& .MuiAvatar-root': {\n                    width: 24,\n                    height: 24,\n                    fontSize: '0.75rem',\n                    border: '1.5px solid #fff'\n                  }\n                },\n                children: localTask.assignees.map((assignee, index) => /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: assignee.first_name && assignee.last_name ? `${assignee.first_name} ${assignee.last_name}` : assignee.email,\n                  arrow: true,\n                  children: /*#__PURE__*/_jsxDEV(Avatar, {\n                    src: assignee.avatar,\n                    alt: assignee.first_name || assignee.email,\n                    sx: {\n                      bgcolor: mainYellowColor\n                    },\n                    children: assignee.first_name ? assignee.first_name.charAt(0).toUpperCase() : assignee.email ? assignee.email.charAt(0).toUpperCase() : ''\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 953,\n                    columnNumber: 27\n                  }, this)\n                }, assignee.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 946,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 934,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  border: '1px solid #eee',\n                  borderRadius: '8px',\n                  padding: '2px 4px',\n                  background: '#f9f9f9'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: hasDueDates ? \"Edit due date\" : \"Set due date\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: handleOpenDueDateDialog,\n                    sx: {\n                      color: hasDueDates ? mainYellowColor : '#888',\n                      '&:hover': {\n                        color: mainYellowColor,\n                        bgcolor: 'rgba(255, 193, 7, 0.08)'\n                      }\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Iconify, {\n                      icon: hasDueDates ? \"material-symbols:calendar-month\" : \"material-symbols:calendar-add-on\",\n                      width: 16,\n                      height: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 995,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 984,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 983,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Comments\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: handleOpenComments,\n                    disabled: loadingComments,\n                    sx: {\n                      color: mainYellowColor,\n                      '&:hover': {\n                        bgcolor: 'rgba(255, 193, 7, 0.08)'\n                      }\n                    },\n                    children: loadingComments ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                      size: 16,\n                      sx: {\n                        color: mainYellowColor\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1017,\n                      columnNumber: 27\n                    }, this) : /*#__PURE__*/_jsxDEV(Iconify, {\n                      icon: \"material-symbols:comment-outline\",\n                      width: 16,\n                      height: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1019,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1005,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1004,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Assign members\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: () => setAssignMemberDialogOpen(true),\n                    sx: {\n                      color: ((_localTask$assignees = localTask.assignees) === null || _localTask$assignees === void 0 ? void 0 : _localTask$assignees.length) > 0 ? mainYellowColor : '#888',\n                      '&:hover': {\n                        color: mainYellowColor,\n                        bgcolor: 'rgba(255, 193, 7, 0.08)'\n                      }\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Iconify, {\n                      icon: \"mdi:account-multiple-plus\",\n                      width: 16,\n                      height: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1037,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1026,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1025,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Add subtask\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: handleAddSubtaskClick,\n                    sx: {\n                      color: mainYellowColor,\n                      '&:hover': {\n                        bgcolor: 'rgba(255, 193, 7, 0.08)'\n                      }\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Iconify, {\n                      icon: \"material-symbols:add-task\",\n                      width: 16,\n                      height: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1057,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1047,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1046,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Edit task\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: handleTaskEditClick,\n                    sx: {\n                      color: '#666',\n                      '&:hover': {\n                        bgcolor: 'rgba(0, 0, 0, 0.04)'\n                      }\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Iconify, {\n                      icon: \"eva:edit-fill\",\n                      width: 16,\n                      height: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1073,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1063,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1062,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Delete task\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    onClick: handleDeleteTaskClick,\n                    sx: {\n                      color: '#F44336',\n                      '&:hover': {\n                        bgcolor: 'rgba(244, 67, 54, 0.08)'\n                      }\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Iconify, {\n                      icon: \"material-symbols:delete-outline\",\n                      width: 16,\n                      height: 16\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1089,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1079,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1078,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 972,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  bgcolor: statusConfig.color + '20',\n                  px: 1,\n                  py: 0.5,\n                  borderRadius: '4px'\n                },\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    fontWeight: 600,\n                    color: statusConfig.color,\n                    fontFamily: '\"Recursive Variable\", sans-serif'\n                  },\n                  children: [taskProgress, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1105,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1095,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 931,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 893,\n            columnNumber: 15\n          }, this), hasDueDates && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              backgroundColor: `${mainYellowColor}08`,\n              borderRadius: '4px',\n              py: 0.5,\n              px: 0.75,\n              width: 'fit-content'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"material-symbols:calendar-month\",\n              width: 14,\n              height: 14,\n              color: mainYellowColor\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1132,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                fontFamily: '\"Recursive Variable\", sans-serif',\n                fontSize: '0.75rem',\n                color: '#555',\n                fontWeight: 500,\n                lineHeight: 1,\n                ml: 0.5\n              },\n              children: [formatDate(localTask.start_date), \" ~ \", formatDate(localTask.end_date)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1137,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1121,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 892,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 854,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          pl: 4,\n          pr: 1,\n          pt: 0.5,\n          pb: 0.5,\n          borderLeft: `1px dashed ${statusConfig.color}`,\n          ml: 1.5,\n          mt: 0.5,\n          display: (hasSubtasks || isAddingSubtask) && showSubtasks ? 'block' : 'none'\n        },\n        children: [task.subtasks && task.subtasks.map((subtask, index) => /*#__PURE__*/_jsxDEV(SubtaskItem, {\n          subtask: subtask,\n          index: index,\n          totalSubtasks: task.subtasks.length,\n          taskSlug: task.slug,\n          calculateSubtaskProgress: calculateSubtaskProgress,\n          getSubtaskStatus: getSubtaskStatus,\n          onUpdateSubtask: onUpdateSubtask,\n          onDeleteSubtask: onDeleteSubtask,\n          onAssignMembers: onAssignMembers,\n          invitedUsers: invitedUsers,\n          planOwner: planOwner\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1168,\n          columnNumber: 13\n        }, this)), isAddingSubtask && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            py: 0.5,\n            borderBottom: '1px dotted #f0f0f0',\n            backgroundColor: '#f9f9f9',\n            borderRadius: '4px',\n            px: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n            disabled: true,\n            size: \"small\",\n            sx: {\n              p: 0.5,\n              mr: 0.5,\n              color: '#CCCCCC'\n            },\n            icon: /*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"material-symbols:check-box-outline-blank\",\n              width: 18,\n              height: 18\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1205,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1197,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            inputRef: newSubtaskInputRef,\n            value: newSubtaskName,\n            onChange: e => setNewSubtaskName(e.target.value),\n            onKeyDown: handleNewSubtaskKeyPress,\n            placeholder: \"Enter new subtask name...\",\n            variant: \"standard\",\n            fullWidth: true,\n            autoFocus: true,\n            sx: {\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              '& .MuiInputBase-input': {\n                fontSize: '0.85rem',\n                fontFamily: '\"Recursive Variable\", sans-serif',\n                padding: '4px 0'\n              },\n              '& .MuiInput-underline:before': {\n                borderBottomColor: 'rgba(0, 0, 0, 0.1)'\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1208,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              ml: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: handleSaveNewSubtask,\n              sx: {\n                p: 0.5\n              },\n              children: /*#__PURE__*/_jsxDEV(Iconify, {\n                icon: \"material-symbols:check\",\n                width: 18,\n                height: 18,\n                color: \"#4CAF50\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1232,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1231,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: () => setIsAddingSubtask(false),\n              sx: {\n                p: 0.5,\n                ml: 0.5\n              },\n              children: /*#__PURE__*/_jsxDEV(Iconify, {\n                icon: \"material-symbols:close\",\n                width: 18,\n                height: 18,\n                color: \"#F44336\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1236,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1235,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1230,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1186,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1155,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 853,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CommentDialog, {\n      open: commentDialogOpen,\n      onClose: () => setCommentDialogOpen(false),\n      comments: comments,\n      onAddComment: handleAddComment,\n      onUpdateComment: handleUpdateComment,\n      onDeleteComment: handleDeleteComment,\n      loading: loadingComments,\n      targetName: task.name,\n      targetType: \"task\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1245,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DueDateDialog, {\n      open: dueDateDialogOpen,\n      onClose: () => setDueDateDialogOpen(false),\n      task: localTask,\n      onUpdateDueDate: handleUpdateDueDate,\n      isUpdating: updatingDueDate\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1258,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AssignMemberDialog, {\n      open: assignMemberDialogOpen,\n      onClose: () => setAssignMemberDialogOpen(false),\n      task: localTask,\n      invitedUsers: invitedUsers,\n      planOwner: planOwner,\n      onAssignMembers: handleAssignMembers\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1267,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// Component for subtask\n_s2(TaskItem, \"dnh9c1rYa0G+f59syNTxEgU3FYU=\");\n_c2 = TaskItem;\nconst SubtaskItem = _ref3 => {\n  _s3();\n  let {\n    subtask,\n    index,\n    totalSubtasks,\n    taskSlug,\n    calculateSubtaskProgress,\n    getSubtaskStatus,\n    onUpdateSubtask,\n    onDeleteSubtask,\n    onAssignMembers,\n    invitedUsers,\n    planOwner\n  } = _ref3;\n  const [isEditing, setIsEditing] = useState(false);\n  const [subtaskName, setSubtaskName] = useState(subtask.name);\n  const [isChecked, setIsChecked] = useState(subtask.status === STATUS.COMPLETED || subtask.progress === 100);\n  const subtaskInputRef = useRef(null);\n\n  // Calculate subtask progress and status\n  const subtaskProgress = calculateSubtaskProgress ? calculateSubtaskProgress(subtask) : 0;\n  const subtaskStatus = getSubtaskStatus ? getSubtaskStatus(subtask) : subtask.status || STATUS.NOT_STARTED;\n  const subtaskStatusConfig = STATUS_CONFIG[subtaskStatus] || STATUS_CONFIG[STATUS.NOT_STARTED];\n\n  // Update isChecked when subtask changes from outside\n  useEffect(() => {\n    setIsChecked(subtask.status === STATUS.COMPLETED || subtask.progress === 100);\n  }, [subtask.status, subtask.progress]);\n\n  // Handle edit mode for subtask\n  const handleSubtaskEditClick = () => {\n    setIsEditing(true);\n  };\n\n  // Handle save changes for subtask\n  const handleSubtaskSave = () => {\n    // Trim the name to remove leading/trailing whitespace\n    const trimmedName = subtaskName.trim();\n\n    // Only update if the name has actually changed (after trimming)\n    if (trimmedName !== '' && trimmedName !== subtask.name) {\n      if (onUpdateSubtask) {\n        onUpdateSubtask({\n          ...subtask,\n          name: trimmedName,\n          task: taskSlug\n        });\n      }\n    } else {\n      // Reset to original if empty or unchanged\n      setSubtaskName(subtask.name);\n    }\n    setIsEditing(false);\n  };\n\n  // Handle key press events for subtask\n  const handleSubtaskKeyPress = e => {\n    if (e.key === 'Enter') {\n      handleSubtaskSave();\n    } else if (e.key === 'Escape') {\n      setSubtaskName(subtask.name);\n      setIsEditing(false);\n    }\n  };\n\n  // Handle status toggle\n  const handleStatusToggle = () => {\n    // Update UI immediately to provide user feedback\n    const newCheckedState = !isChecked;\n    setIsChecked(newCheckedState);\n    const newStatus = newCheckedState ? STATUS.COMPLETED : STATUS.NOT_STARTED;\n    const newProgress = newCheckedState ? 100 : 0;\n    if (onUpdateSubtask) {\n      // Ensure to send both status and progress, even when progress = 0\n      const updatedData = {\n        ...subtask,\n        status: newStatus,\n        progress: newProgress,\n        task: taskSlug\n      };\n      console.log('Sending subtask update:', updatedData);\n      onUpdateSubtask(updatedData);\n    }\n  };\n\n  // Handle delete subtask\n  const handleDeleteSubtaskClick = () => {\n    if (window.confirm(`Are you sure you want to delete this subtask?`)) {\n      onDeleteSubtask(subtask, taskSlug);\n    }\n  };\n\n  // Focus input when editing starts\n  useEffect(() => {\n    if (isEditing && subtaskInputRef.current) {\n      subtaskInputRef.current.focus();\n    }\n  }, [isEditing]);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex',\n      alignItems: 'center',\n      py: 0.5,\n      borderBottom: index < totalSubtasks - 1 ? '1px dotted #f0f0f0' : 'none'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n      checked: isChecked,\n      onChange: handleStatusToggle,\n      size: \"small\",\n      sx: {\n        p: 0.5,\n        mr: 0.5,\n        color: '#CCCCCC',\n        '&.Mui-checked': {\n          color: '#4CAF50'\n        }\n      },\n      icon: /*#__PURE__*/_jsxDEV(Iconify, {\n        icon: \"material-symbols:check-box-outline-blank\",\n        width: 18,\n        height: 18\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1399,\n        columnNumber: 15\n      }, this),\n      checkedIcon: /*#__PURE__*/_jsxDEV(Iconify, {\n        icon: \"material-symbols:check-box\",\n        width: 18,\n        height: 18\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1400,\n        columnNumber: 22\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1387,\n      columnNumber: 7\n    }, this), isEditing ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center',\n        flexGrow: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(TextField, {\n        inputRef: subtaskInputRef,\n        value: subtaskName,\n        onChange: e => setSubtaskName(e.target.value),\n        onKeyDown: handleSubtaskKeyPress,\n        onBlur: handleSubtaskSave,\n        variant: \"standard\",\n        fullWidth: true,\n        autoFocus: true,\n        sx: {\n          fontFamily: '\"Recursive Variable\", sans-serif',\n          '& .MuiInputBase-input': {\n            fontSize: '0.85rem',\n            fontFamily: '\"Recursive Variable\", sans-serif'\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1405,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n        size: \"small\",\n        onClick: handleSubtaskSave,\n        sx: {\n          ml: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(Iconify, {\n          icon: \"material-symbols:check\",\n          width: 14,\n          height: 14,\n          color: \"#4CAF50\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1423,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1422,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1404,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body2\",\n      sx: {\n        flexGrow: 1,\n        fontSize: '0.85rem',\n        fontFamily: '\"Recursive Variable\", sans-serif',\n        color: isChecked ? '#4CAF50' : '#555',\n        fontWeight: isChecked ? 500 : 400,\n        cursor: 'pointer',\n        display: 'flex',\n        alignItems: 'center',\n        '&:hover': {\n          '& .subtask-edit-icon': {\n            opacity: 1\n          }\n        }\n      },\n      onClick: handleSubtaskEditClick,\n      children: [subtaskName, /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"Edit subtask name\",\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          className: \"subtask-edit-icon\",\n          sx: {\n            ml: 0.5,\n            opacity: 0,\n            transition: 'opacity 0.2s',\n            padding: '1px'\n          },\n          children: /*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"material-symbols:edit-outline\",\n            width: 12,\n            height: 12,\n            color: \"#666\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1458,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1448,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1447,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1427,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center'\n      },\n      children: [subtaskProgress !== null && subtaskProgress !== undefined && /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        sx: {\n          fontSize: '0.75rem',\n          color: subtaskStatusConfig.color,\n          ml: 1,\n          mr: 1,\n          fontFamily: '\"Recursive Variable\", sans-serif',\n          fontWeight: 600\n        },\n        children: [subtaskProgress, \"%\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1466,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"Delete subtask\",\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          onClick: handleDeleteSubtaskClick,\n          sx: {\n            p: 0.5,\n            color: '#F44336',\n            '&:hover': {\n              backgroundColor: '#FFEBEE'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"material-symbols:delete-outline\",\n            width: 14,\n            height: 14\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1494,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1483,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1482,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1464,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1379,\n    columnNumber: 5\n  }, this);\n};\n\n// Format date for display\n_s3(SubtaskItem, \"95kGDfgernN1QkX5iwg2z2cf2rU=\");\n_c3 = SubtaskItem;\nfunction formatDate(dateString) {\n  if (!dateString) return '';\n  try {\n    const date = new Date(dateString);\n    if (isNaN(date.getTime())) return '';\n\n    // Only display day and month, not year\n    const options = {\n      month: 'short',\n      day: 'numeric'\n    };\n    return date.toLocaleDateString('en-US', options);\n  } catch (error) {\n    return '';\n  }\n}\nexport default MilestoneCard;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"MilestoneCard\");\n$RefreshReg$(_c2, \"TaskItem\");\n$RefreshReg$(_c3, \"SubtaskItem\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Box", "Typography", "Paper", "Chip", "LinearProgress", "<PERSON><PERSON>", "Divider", "TextField", "IconButton", "<PERSON><PERSON><PERSON>", "Checkbox", "CircularProgress", "Avatar", "AvatarGroup", "Collapse", "Iconify", "mainYellowColor", "STATUS", "STATUS_CONFIG", "styles", "CommentDialog", "DueDateDialog", "AssignMemberDialog", "getComments", "addComment", "updateComment", "deleteComment", "updateTask", "assignMembersToTask", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MilestoneCard", "_ref", "_s", "_milestone$tasks", "_milestone$tasks2", "_milestone$tasks3", "milestone", "compact", "showSubtasks", "calculateMilestoneProgress", "getMilestoneStatus", "calculateTaskProgress", "getTaskStatus", "calculateSubtaskProgress", "getSubtaskStatus", "onUpdateMilestone", "onUpdateTask", "onUpdateSubtask", "onAddTask", "onAddSubtask", "onDeleteTask", "onDeleteSubtask", "onAssignMembers", "invitedUsers", "<PERSON><PERSON><PERSON><PERSON>", "isEditing", "setIsEditing", "milestoneName", "setMilestoneName", "name", "isAddingTask", "setIsAddingTask", "newTaskName", "setNewTaskName", "isExpanded", "setIsExpanded", "inputRef", "newTaskInputRef", "progress", "milestoneStatus", "NOT_STARTED", "statusConfig", "hasDescription", "description", "trim", "length", "handleEditClick", "handleSave", "trimmedName", "handleKeyPress", "e", "key", "handleAddTaskClick", "handleSaveNewTask", "newTask", "id", "status", "handleNewTaskKeyPress", "current", "focus", "elevation", "className", "milestoneCard", "sx", "padding", "children", "<PERSON><PERSON><PERSON><PERSON>", "display", "alignItems", "flex", "size", "onClick", "mr", "color", "backgroundColor", "icon", "width", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "onChange", "target", "onKeyDown", "onBlur", "variant", "fullWidth", "autoFocus", "fontFamily", "fontWeight", "fontSize", "ml", "milestoneTitle", "cursor", "opacity", "title", "transition", "label", "borderRadius", "mb", "mt", "p", "border", "whiteSpace", "lineHeight", "my", "justifyContent", "estimated_duration", "gap", "taskList", "tasks", "slice", "map", "task", "index", "TaskItem", "textTransform", "taskItem", "position", "flexShrink", "placeholder", "borderBottomColor", "_c", "_ref2", "_s2", "_localTask$assignees", "taskName", "setTaskName", "taskInputRef", "isAddingSubtask", "setIsAddingSubtask", "newSubtaskName", "setNewSubtaskName", "newSubtaskInputRef", "commentDialogOpen", "setCommentDialogOpen", "comments", "setComments", "loadingComments", "setLoadingComments", "dueDateDialogOpen", "setDueDateDialogOpen", "updatingDueDate", "setUpdatingDueDate", "assignMemberDialogOpen", "setAssignMemberDialogOpen", "localTask", "setLocalTask", "handleOpenComments", "stopPropagation", "then", "response", "_response$data", "_response$data2", "console", "log", "data", "commentsData", "Array", "isArray", "catch", "error", "finally", "handleAddComment", "content", "_response$data3", "newComment", "currentUser", "JSON", "parse", "localStorage", "getItem", "Date", "now", "user", "first_name", "last_name", "avatar", "created_at", "toLocaleString", "hour", "minute", "day", "month", "replace", "prevComments", "refreshComments", "handleUpdateComment", "commentId", "updatedCommentTemp", "find", "c", "updatedComments", "success", "handleDeleteComment", "filter", "_response$data4", "_response$data5", "taskProgress", "taskStatus", "hasSubtasks", "subtasks", "isCompleted", "COMPLETED", "handleTaskEditClick", "handleTaskSave", "handleTaskKeyPress", "handleAddSubtaskClick", "handleDeleteTaskClick", "handleSaveNewSubtask", "newSubtask", "slug", "handleNewSubtaskKeyPress", "hasDueDates", "start_date", "end_date", "handleOpenDueDateDialog", "handleUpdateDueDate", "updatedTask", "taskToUpdate", "handleAssignMembers", "assignedUserIds", "numericIds", "Number", "assignees", "email", "taskItemContainer", "flexGrow", "flexDirection", "max", "assignee", "arrow", "src", "alt", "bgcolor", "char<PERSON>t", "toUpperCase", "background", "disabled", "px", "py", "formatDate", "pl", "pr", "pt", "pb", "borderLeft", "subtask", "SubtaskItem", "totalSubtasks", "taskSlug", "borderBottom", "open", "onClose", "onAddComment", "onUpdateComment", "onDeleteComment", "loading", "targetName", "targetType", "onUpdateDueDate", "isUpdating", "_c2", "_ref3", "_s3", "subtaskName", "setSubtaskName", "isChecked", "setIsChecked", "subtaskInputRef", "subtaskProgress", "subtaskStatus", "subtaskStatusConfig", "handleSubtaskEditClick", "handleSubtaskSave", "handleSubtaskKeyPress", "handleStatusToggle", "newCheckedState", "newStatus", "newProgress", "updatedData", "handleDeleteSubtaskClick", "window", "confirm", "checked", "checkedIcon", "undefined", "_c3", "dateString", "date", "isNaN", "getTime", "options", "toLocaleDateString", "$RefreshReg$"], "sources": ["C:/ignition/ignition-ui/src/views/plan/detail/components/MilestoneCard.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Paper,\r\n  Chip,\r\n  LinearProgress,\r\n  Button,\r\n  Divider,\r\n  TextField,\r\n  IconButton,\r\n  Tooltip,\r\n  Checkbox,\r\n  CircularProgress,\r\n  Avatar,\r\n  AvatarGroup,\r\n  Collapse\r\n} from '@mui/material';\r\nimport Iconify from 'components/Iconify/index';\r\nimport { mainYellowColor } from \"helpers/constants\";\r\nimport { STATUS, STATUS_CONFIG } from '../hooks/usePlanData';\r\nimport styles from '../styles.module.scss';\r\nimport CommentDialog from '../dialogs/CommentDialog';\r\nimport DueDateDialog from '../dialogs/DueDateDialog';\r\nimport AssignMemberDialog from '../dialogs/AssignMemberDialog';\r\nimport { getComments, addComment, updateComment, deleteComment, updateTask, assignMembersToTask } from '../../services';\r\nimport { toast } from 'react-toastify';\r\n\r\nconst MilestoneCard = ({\r\n  milestone,\r\n  compact = false,\r\n  showSubtasks = false,\r\n  calculateMilestoneProgress,\r\n  getMilestoneStatus,\r\n  calculateTaskProgress,\r\n  getTaskStatus,\r\n  calculateSubtaskProgress,\r\n  getSubtaskStatus,\r\n  onUpdateMilestone,\r\n  onUpdateTask,\r\n  onUpdateSubtask,\r\n  onAddTask,\r\n  onAddSubtask,\r\n  onDeleteTask,\r\n  onDeleteSubtask,\r\n  onAssignMembers,\r\n  invitedUsers,\r\n  planOwner\r\n}) => {\r\n  const [isEditing, setIsEditing] = useState(false);\r\n  const [milestoneName, setMilestoneName] = useState(milestone.name);\r\n  const [isAddingTask, setIsAddingTask] = useState(false);\r\n  const [newTaskName, setNewTaskName] = useState('');\r\n  const [isExpanded, setIsExpanded] = useState(true); // Add collapsible state\r\n  const inputRef = useRef(null);\r\n  const newTaskInputRef = useRef(null);\r\n\r\n  // Calculate milestone progress\r\n  const progress = calculateMilestoneProgress ? calculateMilestoneProgress(milestone) : 0;\r\n\r\n  // Determine status based on progress\r\n  const milestoneStatus = getMilestoneStatus ? getMilestoneStatus(milestone) : STATUS.NOT_STARTED;\r\n  const statusConfig = STATUS_CONFIG[milestoneStatus] || STATUS_CONFIG[STATUS.NOT_STARTED];\r\n\r\n  // Check if description exists and is not empty\r\n  const hasDescription = milestone.description && milestone.description.trim().length > 0;\r\n\r\n  // Handle edit mode\r\n  const handleEditClick = () => {\r\n    setIsEditing(true);\r\n  };\r\n\r\n  // Handle save changes\r\n  const handleSave = () => {\r\n    // Trim the name to remove leading/trailing whitespace\r\n    const trimmedName = milestoneName.trim();\r\n\r\n    // Only update if the name has actually changed (after trimming)\r\n    if (trimmedName !== '' && trimmedName !== milestone.name) {\r\n      if (onUpdateMilestone) {\r\n        onUpdateMilestone({ ...milestone, name: trimmedName });\r\n      }\r\n    } else {\r\n      // Reset to original if empty or unchanged\r\n      setMilestoneName(milestone.name);\r\n    }\r\n\r\n    setIsEditing(false);\r\n  };\r\n\r\n  // Handle key press events\r\n  const handleKeyPress = (e) => {\r\n    if (e.key === 'Enter') {\r\n      handleSave();\r\n    } else if (e.key === 'Escape') {\r\n      setMilestoneName(milestone.name);\r\n      setIsEditing(false);\r\n    }\r\n  };\r\n\r\n  // Handle add task\r\n  const handleAddTaskClick = () => {\r\n    setIsAddingTask(true);\r\n  };\r\n\r\n  // Handle save new task\r\n  const handleSaveNewTask = () => {\r\n    const trimmedName = newTaskName.trim();\r\n    if (trimmedName && onAddTask) {\r\n      const newTask = {\r\n        name: trimmedName,\r\n        milestone: milestone.id,\r\n        status: STATUS.NOT_STARTED,\r\n        progress: 0\r\n      };\r\n      onAddTask(newTask);\r\n      setNewTaskName('');\r\n    }\r\n    setIsAddingTask(false);\r\n  };\r\n\r\n  // Handle key press events for new task\r\n  const handleNewTaskKeyPress = (e) => {\r\n    if (e.key === 'Enter') {\r\n      handleSaveNewTask();\r\n    } else if (e.key === 'Escape') {\r\n      setNewTaskName('');\r\n      setIsAddingTask(false);\r\n    }\r\n  };\r\n\r\n  // Focus input when editing starts\r\n  useEffect(() => {\r\n    if (isEditing && inputRef.current) {\r\n      inputRef.current.focus();\r\n    }\r\n  }, [isEditing]);\r\n\r\n  // Focus input when adding task\r\n  useEffect(() => {\r\n    if (isAddingTask && newTaskInputRef.current) {\r\n      newTaskInputRef.current.focus();\r\n    }\r\n  }, [isAddingTask]);\r\n\r\n  return (\r\n    <Paper\r\n      elevation={0}\r\n      className={styles.milestoneCard}\r\n      sx={{ padding: '16px' }}\r\n    >\r\n      <Box className={styles.milestoneHeader}>\r\n        <Box sx={{ display: 'flex', alignItems: 'center', flex: 1 }}>\r\n          {/* Expand/Collapse Button */}\r\n          <IconButton\r\n            size=\"small\"\r\n            onClick={() => setIsExpanded(!isExpanded)}\r\n            sx={{\r\n              mr: 1,\r\n              color: '#666',\r\n              '&:hover': {\r\n                backgroundColor: 'rgba(0, 0, 0, 0.04)'\r\n              }\r\n            }}\r\n          >\r\n            <Iconify\r\n              icon={isExpanded ? \"material-symbols:expand-less\" : \"material-symbols:expand-more\"}\r\n              width={20}\r\n              height={20}\r\n            />\r\n          </IconButton>\r\n\r\n          {isEditing ? (\r\n            <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>\r\n              <Iconify icon=\"material-symbols:flag\" width={20} height={20} color={mainYellowColor} sx={{ mr: 1 }} />\r\n              <TextField\r\n                inputRef={inputRef}\r\n                value={milestoneName}\r\n                onChange={(e) => setMilestoneName(e.target.value)}\r\n                onKeyDown={handleKeyPress}\r\n                onBlur={handleSave}\r\n                variant=\"standard\"\r\n                fullWidth\r\n                autoFocus\r\n                sx={{\r\n                  fontFamily: '\"Recursive Variable\", sans-serif',\r\n                  fontWeight: 600,\r\n                  fontSize: '1.1rem',\r\n                  '& .MuiInputBase-input': {\r\n                    fontWeight: 600,\r\n                    fontSize: '1.1rem',\r\n                    fontFamily: '\"Recursive Variable\", sans-serif',\r\n                  }\r\n                }}\r\n              />\r\n              <IconButton size=\"small\" onClick={handleSave} sx={{ ml: 1 }}>\r\n                <Iconify icon=\"material-symbols:check\" width={20} height={20} color=\"#4CAF50\" />\r\n              </IconButton>\r\n            </Box>\r\n          ) : (\r\n            <Typography\r\n              variant=\"h6\"\r\n              className={styles.milestoneTitle}\r\n              sx={{\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                display: 'flex',\r\n                alignItems: 'center',\r\n                cursor: 'pointer',\r\n                flex: 1,\r\n                '&:hover': {\r\n                  '& .edit-icon': {\r\n                    opacity: 1,\r\n                  }\r\n                }\r\n              }}\r\n              onClick={handleEditClick}\r\n            >\r\n              <Iconify icon=\"material-symbols:flag\" width={20} height={20} color={mainYellowColor} />\r\n              {milestoneName}\r\n              <Tooltip title=\"Edit milestone name\">\r\n                <IconButton\r\n                  size=\"small\"\r\n                  className=\"edit-icon\"\r\n                  sx={{\r\n                    ml: 1,\r\n                    opacity: 0,\r\n                    transition: 'opacity 0.2s',\r\n                    padding: '2px'\r\n                  }}\r\n                >\r\n                  <Iconify icon=\"material-symbols:edit-outline\" width={16} height={16} color=\"#666\" />\r\n                </IconButton>\r\n              </Tooltip>\r\n            </Typography>\r\n          )}\r\n        </Box>\r\n\r\n        <Chip\r\n          icon={<Iconify icon={statusConfig.icon} width={16} height={16} />}\r\n          label={statusConfig.label}\r\n          size=\"small\"\r\n          sx={{\r\n            backgroundColor: `${statusConfig.color}20`,\r\n            color: statusConfig.color,\r\n            fontWeight: 600,\r\n            borderRadius: '4px',\r\n            fontFamily: '\"Recursive Variable\", sans-serif'\r\n          }}\r\n        />\r\n      </Box>\r\n\r\n      {/* Description Section */}\r\n      {!compact && hasDescription && (\r\n        <Box sx={{ mb: 2, mt: 1.5 }}>\r\n          <Box\r\n            sx={{\r\n              backgroundColor: '#f9f9f9',\r\n              p: 1.5,\r\n              borderRadius: '8px',\r\n              border: '1px solid #f0f0f0'\r\n            }}\r\n          >\r\n            <Typography\r\n              variant=\"body2\"\r\n              sx={{\r\n                color: '#333',\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                whiteSpace: 'pre-line',\r\n                lineHeight: 1.6,\r\n                fontWeight: 500\r\n              }}\r\n            >\r\n              {milestone.description}\r\n            </Typography>\r\n          </Box>\r\n        </Box>\r\n      )}\r\n\r\n      <Divider sx={{ my: 2, opacity: 0.6 }} />\r\n\r\n      <Box sx={{ mb: 2 }}>\r\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>\r\n          <Typography variant=\"body2\" sx={{ fontWeight: 600, fontFamily: '\"Recursive Variable\", sans-serif', color: '#333' }}>\r\n            Progress\r\n          </Typography>\r\n          <Typography variant=\"body2\" sx={{ fontWeight: 700, fontFamily: '\"Recursive Variable\", sans-serif', color: '#333' }}>\r\n            {progress}%\r\n          </Typography>\r\n        </Box>\r\n        <LinearProgress\r\n          variant=\"determinate\"\r\n          value={progress}\r\n          sx={{\r\n            height: 6,\r\n            borderRadius: 3,\r\n            backgroundColor: '#f0f0f0',\r\n            '& .MuiLinearProgress-bar': {\r\n              backgroundColor: statusConfig.color\r\n            }\r\n          }}\r\n        />\r\n      </Box>\r\n\r\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>\r\n        {milestone.estimated_duration && (\r\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n            <Iconify icon=\"material-symbols:timer\" width={16} height={16} color=\"#333\" />\r\n            <Typography variant=\"body2\" sx={{ fontFamily: '\"Recursive Variable\", sans-serif', color: '#333', fontWeight: 500 }}>\r\n              {milestone.estimated_duration}\r\n            </Typography>\r\n          </Box>\r\n        )}\r\n      </Box>\r\n\r\n      {/* Tasks Section */}\r\n      <Box sx={{ mb: 1 }}>\r\n        <Box\r\n          sx={{\r\n            display: 'flex',\r\n            alignItems: 'center',\r\n            justifyContent: 'space-between',\r\n            mb: 1\r\n          }}\r\n        >\r\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n            <Iconify\r\n              icon=\"material-symbols:checklist\"\r\n              width={16}\r\n              height={16}\r\n              sx={{ color: '#333' }}\r\n            />\r\n            <Typography\r\n              variant=\"body2\"\r\n              sx={{\r\n                fontWeight: 600,\r\n                color: '#333',\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                fontSize: '0.9rem'\r\n              }}\r\n            >\r\n              Tasks\r\n            </Typography>\r\n          </Box>\r\n\r\n          {/* Add task button */}\r\n          <Tooltip title=\"Add new task\">\r\n            <IconButton\r\n              size=\"small\"\r\n              onClick={handleAddTaskClick}\r\n              sx={{\r\n                color: mainYellowColor,\r\n                '&:hover': {\r\n                  backgroundColor: `${mainYellowColor}10`\r\n                }\r\n              }}\r\n            >\r\n              <Iconify icon=\"material-symbols:add-task\" width={18} height={18} />\r\n            </IconButton>\r\n          </Tooltip>\r\n        </Box>\r\n\r\n        {compact ? (\r\n          <Box className={styles.taskList}>\r\n            {milestone.tasks?.slice(0, 2).map((task, index) => (\r\n              <TaskItem\r\n                key={index}\r\n                task={task}\r\n                showSubtasks={showSubtasks}\r\n                compact={true}\r\n                calculateTaskProgress={calculateTaskProgress}\r\n                getTaskStatus={getTaskStatus}\r\n                calculateSubtaskProgress={calculateSubtaskProgress}\r\n                getSubtaskStatus={getSubtaskStatus}\r\n                onUpdateTask={onUpdateTask}\r\n                onUpdateSubtask={onUpdateSubtask}\r\n                onAddSubtask={onAddSubtask}\r\n                onDeleteTask={onDeleteTask}\r\n                onDeleteSubtask={onDeleteSubtask}\r\n                onAssignMembers={onAssignMembers}\r\n                invitedUsers={invitedUsers}\r\n                planOwner={planOwner}\r\n              />\r\n            ))}\r\n\r\n            {milestone.tasks?.length > 2 && (\r\n              <Button\r\n                variant=\"text\"\r\n                size=\"small\"\r\n                sx={{\r\n                  color: mainYellowColor,\r\n                  fontWeight: 600,\r\n                  textTransform: 'none',\r\n                  p: 0,\r\n                  mt: 1,\r\n                  fontFamily: '\"Recursive Variable\", sans-serif'\r\n                }}\r\n              >\r\n                + {milestone.tasks.length - 2} more tasks\r\n              </Button>\r\n            )}\r\n          </Box>\r\n        ) : (\r\n          <Box className={styles.taskList}>\r\n            {milestone.tasks?.map((task, index) => (\r\n              <TaskItem\r\n                key={index}\r\n                task={task}\r\n                showSubtasks={showSubtasks}\r\n                compact={false}\r\n                calculateTaskProgress={calculateTaskProgress}\r\n                getTaskStatus={getTaskStatus}\r\n                calculateSubtaskProgress={calculateSubtaskProgress}\r\n                getSubtaskStatus={getSubtaskStatus}\r\n                onUpdateTask={onUpdateTask}\r\n                onUpdateSubtask={onUpdateSubtask}\r\n                onAddSubtask={onAddSubtask}\r\n                onDeleteTask={onDeleteTask}\r\n                onDeleteSubtask={onDeleteSubtask}\r\n                onAssignMembers={onAssignMembers}\r\n                invitedUsers={invitedUsers}\r\n                planOwner={planOwner}\r\n              />\r\n            ))}\r\n\r\n            {/* New task input field */}\r\n            {isAddingTask && (\r\n              <Box\r\n                className={styles.taskItem}\r\n                sx={{\r\n                  position: 'relative',\r\n                  mt: 1,\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  backgroundColor: '#f9f9f9',\r\n                  borderRadius: '6px',\r\n                  padding: '8px 12px',\r\n                  border: '1px solid #f0f0f0'\r\n                }}\r\n              >\r\n                <Box\r\n                  sx={{\r\n                    width: 10,\r\n                    height: 10,\r\n                    borderRadius: '50%',\r\n                    backgroundColor: '#CCCCCC',\r\n                    mr: 1.5,\r\n                    flexShrink: 0\r\n                  }}\r\n                />\r\n\r\n                <TextField\r\n                  inputRef={newTaskInputRef}\r\n                  value={newTaskName}\r\n                  onChange={(e) => setNewTaskName(e.target.value)}\r\n                  onKeyDown={handleNewTaskKeyPress}\r\n                  placeholder=\"Enter new task name...\"\r\n                  variant=\"standard\"\r\n                  fullWidth\r\n                  autoFocus\r\n                  sx={{\r\n                    fontFamily: '\"Recursive Variable\", sans-serif',\r\n                    '& .MuiInputBase-input': {\r\n                      fontSize: '0.9rem',\r\n                      fontFamily: '\"Recursive Variable\", sans-serif',\r\n                      padding: '4px 0'\r\n                    },\r\n                    '& .MuiInput-underline:before': {\r\n                      borderBottomColor: 'rgba(0, 0, 0, 0.1)'\r\n                    }\r\n                  }}\r\n                />\r\n\r\n                <Box sx={{ display: 'flex', alignItems: 'center', ml: 'auto' }}>\r\n                  <IconButton size=\"small\" onClick={handleSaveNewTask} sx={{ ml: 1 }}>\r\n                    <Iconify icon=\"material-symbols:check\" width={18} height={18} color=\"#4CAF50\" />\r\n                  </IconButton>\r\n\r\n                  <IconButton size=\"small\" onClick={() => setIsAddingTask(false)} sx={{ ml: 0.5 }}>\r\n                    <Iconify icon=\"material-symbols:close\" width={18} height={18} color=\"#F44336\" />\r\n                  </IconButton>\r\n                </Box>\r\n              </Box>\r\n            )}\r\n          </Box>\r\n        )}\r\n      </Box>\r\n    </Paper>\r\n  );\r\n};\r\n// Component to display task and subtask\r\nconst TaskItem = ({\r\n  task,\r\n  showSubtasks = false,\r\n  compact = false,\r\n  calculateTaskProgress,\r\n  getTaskStatus,\r\n  calculateSubtaskProgress,\r\n  getSubtaskStatus,\r\n  onUpdateTask,\r\n  onUpdateSubtask,\r\n  onAddSubtask,\r\n  onDeleteTask,\r\n  onDeleteSubtask,\r\n  onAssignMembers,\r\n  invitedUsers,\r\n  planOwner\r\n}) => {\r\n  const [isEditing, setIsEditing] = useState(false);\r\n  const [taskName, setTaskName] = useState(task.name);\r\n  const taskInputRef = useRef(null);\r\n  const [isAddingSubtask, setIsAddingSubtask] = useState(false);\r\n  const [newSubtaskName, setNewSubtaskName] = useState('');\r\n  const newSubtaskInputRef = useRef(null);\r\n\r\n  // Comment functionality\r\n  const [commentDialogOpen, setCommentDialogOpen] = useState(false);\r\n  const [comments, setComments] = useState([]);\r\n  const [loadingComments, setLoadingComments] = useState(false);\r\n\r\n  // Enable due date functionality\r\n  const [dueDateDialogOpen, setDueDateDialogOpen] = useState(false);\r\n  const [updatingDueDate, setUpdatingDueDate] = useState(false);\r\n\r\n  // Add state for assign member dialog\r\n  const [assignMemberDialogOpen, setAssignMemberDialogOpen] = useState(false);\r\n\r\n  // Add state to store current task information\r\n  const [localTask, setLocalTask] = useState(task);\r\n\r\n  // Update localTask when task changes from props\r\n  useEffect(() => {\r\n    setLocalTask(task);\r\n  }, [task]);\r\n\r\n  const handleOpenComments = (e) => {\r\n    e.stopPropagation();\r\n    setCommentDialogOpen(true);\r\n    setLoadingComments(true);\r\n    getComments(task.id)\r\n      .then(response => {\r\n        console.log('Comments data:', response.data);\r\n        // Check data structure and get correct comments array\r\n        let commentsData = [];\r\n        if (Array.isArray(response.data)) {\r\n          commentsData = response.data;\r\n        } else if (response.data?.data && Array.isArray(response.data.data)) {\r\n          commentsData = response.data.data;\r\n        } else if (response.data?.comments && Array.isArray(response.data.comments)) {\r\n          commentsData = response.data.comments;\r\n        }\r\n        setComments(commentsData);\r\n      })\r\n      .catch(error => {\r\n        console.error('Error fetching comments:', error);\r\n        toast.error('Failed to load comments');\r\n      })\r\n      .finally(() => {\r\n        setLoadingComments(false);\r\n      });\r\n  };\r\n\r\n  const handleAddComment = async (content) => {\r\n    try {\r\n      const response = await addComment(task.id, content);\r\n      console.log('Add comment response:', response);\r\n\r\n      // Create new comment from response or create temporary object\r\n      let newComment;\r\n      if (response.data?.data) {\r\n        newComment = response.data.data;\r\n      } else {\r\n        // Create temporary object if API does not return new comment\r\n        const currentUser = JSON.parse(localStorage.getItem('user')) || {};\r\n        newComment = {\r\n          id: Date.now(), // Temporary ID\r\n          content: content,\r\n          user: {\r\n            id: currentUser.id,\r\n            first_name: currentUser.first_name,\r\n            last_name: currentUser.last_name,\r\n            avatar: currentUser.avatar\r\n          },\r\n          created_at: new Date().toLocaleString('en-US', {\r\n            hour: '2-digit',\r\n            minute: '2-digit',\r\n            day: '2-digit',\r\n            month: '2-digit'\r\n          }).replace(',', '')\r\n        };\r\n      }\r\n\r\n      // Update state\r\n      setComments(prevComments => [...prevComments, newComment]);\r\n\r\n      // Refresh comments to ensure latest data\r\n      refreshComments();\r\n    } catch (error) {\r\n      console.error('Error adding comment:', error);\r\n      toast.error('Failed to add comment');\r\n    }\r\n  };\r\n\r\n  const handleUpdateComment = async (commentId, content) => {\r\n    try {\r\n      // Update UI first\r\n      const updatedCommentTemp = comments.find(c => c.id === commentId);\r\n      if (updatedCommentTemp) {\r\n        const updatedComments = comments.map(c =>\r\n          c.id === commentId ? { ...c, content: content } : c\r\n        );\r\n        setComments(updatedComments);\r\n      }\r\n\r\n      // Call API\r\n      const response = await updateComment(commentId, content);\r\n      console.log('Update comment response:', response);\r\n\r\n      toast.success('Comment updated successfully');\r\n\r\n      // Refresh comments to ensure latest data\r\n      refreshComments();\r\n    } catch (error) {\r\n      console.error('Error updating comment:', error);\r\n      toast.error('Failed to update comment');\r\n      // Refresh comments to restore original state if there is an error\r\n      refreshComments();\r\n    }\r\n  };\r\n\r\n  const handleDeleteComment = async (commentId) => {\r\n    try {\r\n      // Update UI first\r\n      setComments(prevComments => prevComments.filter(c => c.id !== commentId));\r\n\r\n      // Call API\r\n      await deleteComment(commentId);\r\n      console.log('Comment deleted successfully');\r\n\r\n      toast.success('Comment deleted successfully');\r\n\r\n      // Refresh comments to ensure latest data\r\n      refreshComments();\r\n    } catch (error) {\r\n      console.error('Error deleting comment:', error);\r\n      toast.error('Failed to delete comment');\r\n      // Refresh comments to restore original state if there is an error\r\n      refreshComments();\r\n    }\r\n  };\r\n\r\n  // Refresh comments function\r\n  const refreshComments = async () => {\r\n    try {\r\n      const response = await getComments(task.id);\r\n      console.log('Refreshed comments:', response.data);\r\n\r\n      // Handle returned data\r\n      let commentsData = [];\r\n      if (Array.isArray(response.data)) {\r\n        commentsData = response.data;\r\n      } else if (response.data?.data && Array.isArray(response.data.data)) {\r\n        commentsData = response.data.data;\r\n      } else if (response.data?.comments && Array.isArray(response.data.comments)) {\r\n        commentsData = response.data.comments;\r\n      }\r\n\r\n      setComments(commentsData);\r\n    } catch (error) {\r\n      console.error('Error refreshing comments:', error);\r\n    }\r\n  };\r\n\r\n  // Calculate task progress\r\n  const taskProgress = calculateTaskProgress ? calculateTaskProgress(task) : 0;\r\n\r\n  // Determine task status\r\n  const taskStatus = getTaskStatus ? getTaskStatus(task) : (task.status || STATUS.NOT_STARTED);\r\n  const statusConfig = STATUS_CONFIG[taskStatus] || STATUS_CONFIG[STATUS.NOT_STARTED];\r\n\r\n  // Check if task has subtasks\r\n  const hasSubtasks = task.subtasks && task.subtasks.length > 0;\r\n\r\n  // Check if task is completed\r\n  const isCompleted = taskStatus === STATUS.COMPLETED;\r\n\r\n  // Handle edit mode for task\r\n  const handleTaskEditClick = () => {\r\n    setIsEditing(true);\r\n  };\r\n\r\n  // Handle save changes for task\r\n  const handleTaskSave = () => {\r\n    // Trim the name to remove leading/trailing whitespace\r\n    const trimmedName = taskName.trim();\r\n\r\n    // Only update if the name has actually changed (after trimming)\r\n    if (trimmedName !== '' && trimmedName !== task.name) {\r\n      if (onUpdateTask) {\r\n        onUpdateTask({ ...task, name: trimmedName });\r\n      }\r\n    } else {\r\n      // Reset to original if empty or unchanged\r\n      setTaskName(task.name);\r\n    }\r\n\r\n    setIsEditing(false);\r\n  };\r\n\r\n  // Handle key press events for task\r\n  const handleTaskKeyPress = (e) => {\r\n    if (e.key === 'Enter') {\r\n      handleTaskSave();\r\n    } else if (e.key === 'Escape') {\r\n      setTaskName(task.name);\r\n      setIsEditing(false);\r\n    }\r\n  };\r\n\r\n  // Handle add subtask\r\n  const handleAddSubtaskClick = () => {\r\n    setIsAddingSubtask(true);\r\n  };\r\n\r\n  // Handle delete task\r\n  const handleDeleteTaskClick = () => {\r\n    if (onDeleteTask) {\r\n      onDeleteTask(task);\r\n    }\r\n  };\r\n\r\n  // Handle save new subtask\r\n  const handleSaveNewSubtask = () => {\r\n    const trimmedName = newSubtaskName.trim();\r\n    if (trimmedName && onAddSubtask) {\r\n      const newSubtask = {\r\n        name: trimmedName,\r\n        task: task.slug,\r\n        status: STATUS.NOT_STARTED,\r\n        progress: 0\r\n      };\r\n      onAddSubtask(newSubtask);\r\n      setNewSubtaskName('');\r\n    }\r\n    setIsAddingSubtask(false);\r\n  };\r\n\r\n  // Handle key press events for new subtask\r\n  const handleNewSubtaskKeyPress = (e) => {\r\n    if (e.key === 'Enter') {\r\n      handleSaveNewSubtask();\r\n    } else if (e.key === 'Escape') {\r\n      setNewSubtaskName('');\r\n      setIsAddingSubtask(false);\r\n    }\r\n  };\r\n\r\n  // Focus input when editing starts\r\n  useEffect(() => {\r\n    if (isEditing && taskInputRef.current) {\r\n      taskInputRef.current.focus();\r\n    }\r\n  }, [isEditing]);\r\n\r\n  // Focus input when adding subtask\r\n  useEffect(() => {\r\n    if (isAddingSubtask && newSubtaskInputRef.current) {\r\n      newSubtaskInputRef.current.focus();\r\n    }\r\n  }, [isAddingSubtask]);\r\n\r\n  // Check if task has due dates - use localTask instead of task\r\n  const hasDueDates = localTask.start_date || localTask.end_date;\r\n\r\n  const handleOpenDueDateDialog = (e) => {\r\n    e.stopPropagation();\r\n    setDueDateDialogOpen(true);\r\n  };\r\n\r\n  const handleUpdateDueDate = async (updatedTask) => {\r\n    setUpdatingDueDate(true);\r\n    try {\r\n      const taskToUpdate = {\r\n        ...localTask,\r\n        start_date: updatedTask.start_date,\r\n        end_date: updatedTask.end_date,\r\n        progress: localTask.progress || 0,\r\n        status: localTask.status || 1\r\n      };\r\n\r\n      // Update local state first\r\n      setLocalTask(taskToUpdate);\r\n\r\n      // Call API\r\n      const response = await updateTask(taskToUpdate);\r\n\r\n      // If API returns data, update local state\r\n      if (response && response.data) {\r\n        setLocalTask(response.data);\r\n      }\r\n\r\n      // Update state in parent component if necessary\r\n      if (onUpdateTask) {\r\n        onUpdateTask(taskToUpdate);\r\n      }\r\n\r\n      toast.success('Due date updated successfully');\r\n    } catch (error) {\r\n      console.error('Error updating due date:', error);\r\n      toast.error('Failed to update due date');\r\n\r\n      // If there is an error, restore original state\r\n      setLocalTask(task);\r\n    } finally {\r\n      setUpdatingDueDate(false);\r\n      setDueDateDialogOpen(false); // Close dialog after completion\r\n    }\r\n  };\r\n\r\n  // Add handler for member assignment updates\r\n  const handleAssignMembers = async (assignedUserIds) => {\r\n    try {\r\n      // Convert IDs to numbers\r\n      const numericIds = assignedUserIds.map(id => Number(id));\r\n      // Call API to assign members\r\n      const response = await assignMembersToTask(localTask.slug, numericIds);\r\n      // Create updated task with new assignees\r\n      const updatedTask = {\r\n        ...localTask,\r\n        assignees: response.assignees || numericIds.map(id => ({\r\n          id: id,\r\n          first_name: '',\r\n          last_name: '',\r\n          email: ''\r\n        }))\r\n      };\r\n\r\n      // Update both local state and parent state\r\n      setLocalTask(updatedTask);\r\n      if (onUpdateTask) {\r\n        onUpdateTask(updatedTask);\r\n      }\r\n\r\n      setAssignMemberDialogOpen(false);\r\n      toast.success('Members assigned successfully');\r\n    } catch (error) {\r\n      console.error('Error assigning members:', error);\r\n      toast.error('Failed to assign members');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Box className={styles.taskItemContainer}>\r\n        <Box\r\n          className={styles.taskItem}\r\n          sx={{ position: 'relative' }}>\r\n          <Box\r\n            sx={{\r\n              width: 10,\r\n              height: 10,\r\n              borderRadius: '50%',\r\n              backgroundColor: statusConfig.color,\r\n              mr: 1.5,\r\n              flexShrink: 0\r\n            }}\r\n          />\r\n\r\n          {isEditing ? (\r\n            <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>\r\n              <TextField\r\n                inputRef={taskInputRef}\r\n                value={taskName}\r\n                onChange={(e) => setTaskName(e.target.value)}\r\n                onKeyDown={handleTaskKeyPress}\r\n                onBlur={handleTaskSave}\r\n                variant=\"standard\"\r\n                fullWidth\r\n                autoFocus\r\n                sx={{\r\n                  fontFamily: '\"Recursive Variable\", sans-serif',\r\n                  '& .MuiInputBase-input': {\r\n                    fontSize: '0.9rem',\r\n                    fontFamily: '\"Recursive Variable\", sans-serif',\r\n                  }\r\n                }}\r\n              />\r\n              <IconButton size=\"small\" onClick={handleTaskSave} sx={{ ml: 1 }}>\r\n                <Iconify icon=\"material-symbols:check\" width={16} height={16} color=\"#4CAF50\" />\r\n              </IconButton>\r\n            </Box>\r\n          ) : (\r\n            <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', gap: 0.5 }}>\r\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>\r\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexGrow: 1 }}>\r\n                  <Typography\r\n                    variant=\"body2\"\r\n                    className={styles.taskName}\r\n                    sx={{\r\n                      fontFamily: '\"Recursive Variable\", sans-serif',\r\n                      cursor: 'pointer',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      color: isCompleted ? '#4CAF50' : 'inherit',\r\n                      fontWeight: isCompleted ? 500 : 400,\r\n                      flexGrow: 1,\r\n                      '&:hover': {\r\n                        '& .task-edit-icon': {\r\n                          opacity: 1,\r\n                        }\r\n                      }\r\n                    }}\r\n                    onClick={handleTaskEditClick}>\r\n                    {taskName}\r\n                    <Tooltip title=\"Edit task name\">\r\n                      <IconButton\r\n                        size=\"small\"\r\n                        className=\"task-edit-icon\"\r\n                        sx={{\r\n                          ml: 0.5,\r\n                          opacity: 0,\r\n                          transition: 'opacity 0.2s',\r\n                          padding: '2px'\r\n                        }}\r\n                      >\r\n                        <Iconify icon=\"material-symbols:edit-outline\" width={14} height={14} color=\"#666\" />\r\n                      </IconButton>\r\n                    </Tooltip>\r\n                  </Typography>\r\n                </Box>\r\n\r\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n                  {/* Display assigned members avatars */}\r\n                  {localTask.assignees && localTask.assignees.length > 0 && (\r\n                    <AvatarGroup\r\n                      max={3}\r\n                      sx={{\r\n                        '& .MuiAvatar-root': {\r\n                          width: 24,\r\n                          height: 24,\r\n                          fontSize: '0.75rem',\r\n                          border: '1.5px solid #fff'\r\n                        }\r\n                      }}\r\n                    >\r\n                      {localTask.assignees.map((assignee, index) => (\r\n                        <Tooltip\r\n                          key={assignee.id}\r\n                          title={assignee.first_name && assignee.last_name\r\n                            ? `${assignee.first_name} ${assignee.last_name}`\r\n                            : assignee.email}\r\n                          arrow\r\n                        >\r\n                          <Avatar\r\n                            src={assignee.avatar}\r\n                            alt={assignee.first_name || assignee.email}\r\n                            sx={{\r\n                              bgcolor: mainYellowColor,\r\n                            }}\r\n                          >\r\n                            {assignee.first_name\r\n                              ? assignee.first_name.charAt(0).toUpperCase()\r\n                              : assignee.email\r\n                                ? assignee.email.charAt(0).toUpperCase()\r\n                                : ''}\r\n                          </Avatar>\r\n                        </Tooltip>\r\n                      ))}\r\n                    </AvatarGroup>\r\n                  )}\r\n\r\n                  {/* Group all actions into a Box with border */}\r\n                  <Box\r\n                    sx={{\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      border: '1px solid #eee',\r\n                      borderRadius: '8px',\r\n                      padding: '2px 4px',\r\n                      background: '#f9f9f9'\r\n                    }}\r\n                  >\r\n                    {/* Due Date Button - only display icon */}\r\n                    <Tooltip title={hasDueDates ? \"Edit due date\" : \"Set due date\"}>\r\n                      <IconButton\r\n                        size=\"small\"\r\n                        onClick={handleOpenDueDateDialog}\r\n                        sx={{\r\n                          color: hasDueDates ? mainYellowColor : '#888',\r\n                          '&:hover': {\r\n                            color: mainYellowColor,\r\n                            bgcolor: 'rgba(255, 193, 7, 0.08)'\r\n                          }\r\n                        }}\r\n                      >\r\n                        <Iconify\r\n                          icon={hasDueDates ? \"material-symbols:calendar-month\" : \"material-symbols:calendar-add-on\"}\r\n                          width={16}\r\n                          height={16}\r\n                        />\r\n                      </IconButton>\r\n                    </Tooltip>\r\n\r\n                    {/* Comment Button */}\r\n                    <Tooltip title=\"Comments\">\r\n                      <IconButton\r\n                        size=\"small\"\r\n                        onClick={handleOpenComments}\r\n                        disabled={loadingComments}\r\n                        sx={{\r\n                          color: mainYellowColor,\r\n                          '&:hover': {\r\n                            bgcolor: 'rgba(255, 193, 7, 0.08)'\r\n                          }\r\n                        }}\r\n                      >\r\n                        {loadingComments ? (\r\n                          <CircularProgress size={16} sx={{ color: mainYellowColor }} />\r\n                        ) : (\r\n                          <Iconify icon=\"material-symbols:comment-outline\" width={16} height={16} />\r\n                        )}\r\n                      </IconButton>\r\n                    </Tooltip>\r\n\r\n                    {/* Assign Member Button */}\r\n                    <Tooltip title=\"Assign members\">\r\n                      <IconButton\r\n                        size=\"small\"\r\n                        onClick={() => setAssignMemberDialogOpen(true)}\r\n                        sx={{\r\n                          color: localTask.assignees?.length > 0 ? mainYellowColor : '#888',\r\n                          '&:hover': {\r\n                            color: mainYellowColor,\r\n                            bgcolor: 'rgba(255, 193, 7, 0.08)'\r\n                          }\r\n                        }}\r\n                      >\r\n                        <Iconify\r\n                          icon=\"mdi:account-multiple-plus\"\r\n                          width={16}\r\n                          height={16}\r\n                        />\r\n                      </IconButton>\r\n                    </Tooltip>\r\n\r\n                    {/* Add subtask button */}\r\n                    <Tooltip title=\"Add subtask\">\r\n                      <IconButton\r\n                        size=\"small\"\r\n                        onClick={handleAddSubtaskClick}\r\n                        sx={{\r\n                          color: mainYellowColor,\r\n                          '&:hover': {\r\n                            bgcolor: 'rgba(255, 193, 7, 0.08)'\r\n                          }\r\n                        }}\r\n                      >\r\n                        <Iconify icon=\"material-symbols:add-task\" width={16} height={16} />\r\n                      </IconButton>\r\n                    </Tooltip>\r\n\r\n                    {/* Edit Task Button */}\r\n                    <Tooltip title=\"Edit task\">\r\n                      <IconButton\r\n                        size=\"small\"\r\n                        onClick={handleTaskEditClick}\r\n                        sx={{\r\n                          color: '#666',\r\n                          '&:hover': {\r\n                            bgcolor: 'rgba(0, 0, 0, 0.04)'\r\n                          }\r\n                        }}\r\n                      >\r\n                        <Iconify icon=\"eva:edit-fill\" width={16} height={16} />\r\n                      </IconButton>\r\n                    </Tooltip>\r\n\r\n                    {/* Delete task button */}\r\n                    <Tooltip title=\"Delete task\">\r\n                      <IconButton\r\n                        size=\"small\"\r\n                        onClick={handleDeleteTaskClick}\r\n                        sx={{\r\n                          color: '#F44336',\r\n                          '&:hover': {\r\n                            bgcolor: 'rgba(244, 67, 54, 0.08)'\r\n                          }\r\n                        }}\r\n                      >\r\n                        <Iconify icon=\"material-symbols:delete-outline\" width={16} height={16} />\r\n                      </IconButton>\r\n                    </Tooltip>\r\n                  </Box>\r\n\r\n                  {/* Display task progress */}\r\n                  <Box\r\n                    sx={{\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      bgcolor: statusConfig.color + '20',\r\n                      px: 1,\r\n                      py: 0.5,\r\n                      borderRadius: '4px',\r\n                    }}\r\n                  >\r\n                    <Typography\r\n                      variant=\"caption\"\r\n                      sx={{\r\n                        fontWeight: 600,\r\n                        color: statusConfig.color,\r\n                        fontFamily: '\"Recursive Variable\", sans-serif'\r\n                      }}\r\n                    >\r\n                      {taskProgress}%\r\n                    </Typography>\r\n                  </Box>\r\n                </Box>\r\n              </Box>\r\n\r\n              {/* Due date display below task name */}\r\n              {hasDueDates && (\r\n                <Box\r\n                  sx={{\r\n                    display: 'flex',\r\n                    alignItems: 'center',\r\n                    backgroundColor: `${mainYellowColor}08`,\r\n                    borderRadius: '4px',\r\n                    py: 0.5,\r\n                    px: 0.75,\r\n                    width: 'fit-content'\r\n                  }}\r\n                >\r\n                  <Iconify\r\n                    icon=\"material-symbols:calendar-month\"\r\n                    width={14}\r\n                    height={14}\r\n                    color={mainYellowColor} />\r\n                  <Typography\r\n                    variant=\"caption\"\r\n                    sx={{\r\n                      fontFamily: '\"Recursive Variable\", sans-serif',\r\n                      fontSize: '0.75rem',\r\n                      color: '#555',\r\n                      fontWeight: 500,\r\n                      lineHeight: 1,\r\n                      ml: 0.5\r\n                    }}>\r\n                    {formatDate(localTask.start_date)} ~ {formatDate(localTask.end_date)}\r\n                  </Typography>\r\n                </Box>\r\n              )}\r\n            </Box>\r\n          )}\r\n        </Box>\r\n\r\n        <Box\r\n          sx={{\r\n            pl: 4,\r\n            pr: 1,\r\n            pt: 0.5,\r\n            pb: 0.5,\r\n            borderLeft: `1px dashed ${statusConfig.color}`,\r\n            ml: 1.5,\r\n            mt: 0.5,\r\n            display: (hasSubtasks || isAddingSubtask) && showSubtasks ? 'block' : 'none'\r\n          }}\r\n        >\r\n          {task.subtasks && task.subtasks.map((subtask, index) => (\r\n            <SubtaskItem\r\n              key={index}\r\n              subtask={subtask}\r\n              index={index}\r\n              totalSubtasks={task.subtasks.length}\r\n              taskSlug={task.slug}\r\n              calculateSubtaskProgress={calculateSubtaskProgress}\r\n              getSubtaskStatus={getSubtaskStatus}\r\n              onUpdateSubtask={onUpdateSubtask}\r\n              onDeleteSubtask={onDeleteSubtask}\r\n              onAssignMembers={onAssignMembers}\r\n              invitedUsers={invitedUsers}\r\n              planOwner={planOwner}\r\n            />\r\n          ))}\r\n\r\n          {/* New subtask input field */}\r\n          {isAddingSubtask && (\r\n            <Box\r\n              sx={{\r\n                display: 'flex',\r\n                alignItems: 'center',\r\n                py: 0.5,\r\n                borderBottom: '1px dotted #f0f0f0',\r\n                backgroundColor: '#f9f9f9',\r\n                borderRadius: '4px',\r\n                px: 1\r\n              }}\r\n            >\r\n              <Checkbox\r\n                disabled\r\n                size=\"small\"\r\n                sx={{\r\n                  p: 0.5,\r\n                  mr: 0.5,\r\n                  color: '#CCCCCC'\r\n                }}\r\n                icon={<Iconify icon=\"material-symbols:check-box-outline-blank\" width={18} height={18} />}\r\n              />\r\n\r\n              <TextField\r\n                inputRef={newSubtaskInputRef}\r\n                value={newSubtaskName}\r\n                onChange={(e) => setNewSubtaskName(e.target.value)}\r\n                onKeyDown={handleNewSubtaskKeyPress}\r\n                placeholder=\"Enter new subtask name...\"\r\n                variant=\"standard\"\r\n                fullWidth\r\n                autoFocus\r\n                sx={{\r\n                  fontFamily: '\"Recursive Variable\", sans-serif',\r\n                  '& .MuiInputBase-input': {\r\n                    fontSize: '0.85rem',\r\n                    fontFamily: '\"Recursive Variable\", sans-serif',\r\n                    padding: '4px 0'\r\n                  },\r\n                  '& .MuiInput-underline:before': {\r\n                    borderBottomColor: 'rgba(0, 0, 0, 0.1)'\r\n                  }\r\n                }}\r\n              />\r\n\r\n              <Box sx={{ display: 'flex', ml: 1 }}>\r\n                <IconButton size=\"small\" onClick={handleSaveNewSubtask} sx={{ p: 0.5 }}>\r\n                  <Iconify icon=\"material-symbols:check\" width={18} height={18} color=\"#4CAF50\" />\r\n                </IconButton>\r\n\r\n                <IconButton size=\"small\" onClick={() => setIsAddingSubtask(false)} sx={{ p: 0.5, ml: 0.5 }}>\r\n                  <Iconify icon=\"material-symbols:close\" width={18} height={18} color=\"#F44336\" />\r\n                </IconButton>\r\n              </Box>\r\n            </Box>\r\n          )}\r\n        </Box>\r\n      </Box>\r\n\r\n      {/* Comment Dialog */}\r\n      <CommentDialog\r\n        open={commentDialogOpen}\r\n        onClose={() => setCommentDialogOpen(false)}\r\n        comments={comments}\r\n        onAddComment={handleAddComment}\r\n        onUpdateComment={handleUpdateComment}\r\n        onDeleteComment={handleDeleteComment}\r\n        loading={loadingComments}\r\n        targetName={task.name}\r\n        targetType=\"task\"\r\n      />\r\n\r\n      {/* Due Date Dialog */}\r\n      <DueDateDialog\r\n        open={dueDateDialogOpen}\r\n        onClose={() => setDueDateDialogOpen(false)}\r\n        task={localTask}\r\n        onUpdateDueDate={handleUpdateDueDate}\r\n        isUpdating={updatingDueDate}\r\n      />\r\n\r\n      {/* Assign Member Dialog */}\r\n      <AssignMemberDialog\r\n        open={assignMemberDialogOpen}\r\n        onClose={() => setAssignMemberDialogOpen(false)}\r\n        task={localTask}\r\n        invitedUsers={invitedUsers}\r\n        planOwner={planOwner}\r\n        onAssignMembers={handleAssignMembers}\r\n      />\r\n    </>\r\n  );\r\n};\r\n\r\n// Component for subtask\r\nconst SubtaskItem = ({\r\n  subtask,\r\n  index,\r\n  totalSubtasks,\r\n  taskSlug,\r\n  calculateSubtaskProgress,\r\n  getSubtaskStatus,\r\n  onUpdateSubtask,\r\n  onDeleteSubtask,\r\n  onAssignMembers,\r\n  invitedUsers,\r\n  planOwner\r\n}) => {\r\n  const [isEditing, setIsEditing] = useState(false);\r\n  const [subtaskName, setSubtaskName] = useState(subtask.name);\r\n  const [isChecked, setIsChecked] = useState(subtask.status === STATUS.COMPLETED || subtask.progress === 100);\r\n  const subtaskInputRef = useRef(null);\r\n\r\n  // Calculate subtask progress and status\r\n  const subtaskProgress = calculateSubtaskProgress ? calculateSubtaskProgress(subtask) : 0;\r\n  const subtaskStatus = getSubtaskStatus ? getSubtaskStatus(subtask) : (subtask.status || STATUS.NOT_STARTED);\r\n  const subtaskStatusConfig = STATUS_CONFIG[subtaskStatus] || STATUS_CONFIG[STATUS.NOT_STARTED];\r\n\r\n  // Update isChecked when subtask changes from outside\r\n  useEffect(() => {\r\n    setIsChecked(subtask.status === STATUS.COMPLETED || subtask.progress === 100);\r\n  }, [subtask.status, subtask.progress]);\r\n\r\n  // Handle edit mode for subtask\r\n  const handleSubtaskEditClick = () => {\r\n    setIsEditing(true);\r\n  };\r\n\r\n  // Handle save changes for subtask\r\n  const handleSubtaskSave = () => {\r\n    // Trim the name to remove leading/trailing whitespace\r\n    const trimmedName = subtaskName.trim();\r\n\r\n    // Only update if the name has actually changed (after trimming)\r\n    if (trimmedName !== '' && trimmedName !== subtask.name) {\r\n      if (onUpdateSubtask) {\r\n        onUpdateSubtask({ ...subtask, name: trimmedName, task: taskSlug });\r\n      }\r\n    } else {\r\n      // Reset to original if empty or unchanged\r\n      setSubtaskName(subtask.name);\r\n    }\r\n\r\n    setIsEditing(false);\r\n  };\r\n\r\n  // Handle key press events for subtask\r\n  const handleSubtaskKeyPress = (e) => {\r\n    if (e.key === 'Enter') {\r\n      handleSubtaskSave();\r\n    } else if (e.key === 'Escape') {\r\n      setSubtaskName(subtask.name);\r\n      setIsEditing(false);\r\n    }\r\n  };\r\n\r\n  // Handle status toggle\r\n  const handleStatusToggle = () => {\r\n    // Update UI immediately to provide user feedback\r\n    const newCheckedState = !isChecked;\r\n    setIsChecked(newCheckedState);\r\n\r\n    const newStatus = newCheckedState ? STATUS.COMPLETED : STATUS.NOT_STARTED;\r\n    const newProgress = newCheckedState ? 100 : 0;\r\n\r\n    if (onUpdateSubtask) {\r\n      // Ensure to send both status and progress, even when progress = 0\r\n      const updatedData = {\r\n        ...subtask,\r\n        status: newStatus,\r\n        progress: newProgress,\r\n        task: taskSlug\r\n      };\r\n\r\n      console.log('Sending subtask update:', updatedData);\r\n      onUpdateSubtask(updatedData);\r\n    }\r\n  };\r\n\r\n  // Handle delete subtask\r\n  const handleDeleteSubtaskClick = () => {\r\n    if (window.confirm(`Are you sure you want to delete this subtask?`)) {\r\n      onDeleteSubtask(subtask, taskSlug);\r\n    }\r\n  };\r\n\r\n  // Focus input when editing starts\r\n  useEffect(() => {\r\n    if (isEditing && subtaskInputRef.current) {\r\n      subtaskInputRef.current.focus();\r\n    }\r\n  }, [isEditing]);\r\n\r\n  return (\r\n    <Box\r\n      sx={{\r\n        display: 'flex',\r\n        alignItems: 'center',\r\n        py: 0.5,\r\n        borderBottom: index < totalSubtasks - 1 ? '1px dotted #f0f0f0' : 'none'\r\n      }}\r\n    >\r\n      <Checkbox\r\n        checked={isChecked}\r\n        onChange={handleStatusToggle}\r\n        size=\"small\"\r\n        sx={{\r\n          p: 0.5,\r\n          mr: 0.5,\r\n          color: '#CCCCCC',\r\n          '&.Mui-checked': {\r\n            color: '#4CAF50',\r\n          }\r\n        }}\r\n        icon={<Iconify icon=\"material-symbols:check-box-outline-blank\" width={18} height={18} />}\r\n        checkedIcon={<Iconify icon=\"material-symbols:check-box\" width={18} height={18} />}\r\n      />\r\n\r\n      {isEditing ? (\r\n        <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>\r\n          <TextField\r\n            inputRef={subtaskInputRef}\r\n            value={subtaskName}\r\n            onChange={(e) => setSubtaskName(e.target.value)}\r\n            onKeyDown={handleSubtaskKeyPress}\r\n            onBlur={handleSubtaskSave}\r\n            variant=\"standard\"\r\n            fullWidth\r\n            autoFocus\r\n            sx={{\r\n              fontFamily: '\"Recursive Variable\", sans-serif',\r\n              '& .MuiInputBase-input': {\r\n                fontSize: '0.85rem',\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n              }\r\n            }}\r\n          />\r\n          <IconButton size=\"small\" onClick={handleSubtaskSave} sx={{ ml: 1 }}>\r\n            <Iconify icon=\"material-symbols:check\" width={14} height={14} color=\"#4CAF50\" />\r\n          </IconButton>\r\n        </Box>\r\n      ) : (\r\n        <Typography\r\n          variant=\"body2\"\r\n          sx={{\r\n            flexGrow: 1,\r\n            fontSize: '0.85rem',\r\n            fontFamily: '\"Recursive Variable\", sans-serif',\r\n            color: isChecked ? '#4CAF50' : '#555',\r\n            fontWeight: isChecked ? 500 : 400,\r\n            cursor: 'pointer',\r\n            display: 'flex',\r\n            alignItems: 'center',\r\n            '&:hover': {\r\n              '& .subtask-edit-icon': {\r\n                opacity: 1,\r\n              }\r\n            }\r\n          }}\r\n          onClick={handleSubtaskEditClick}\r\n        >\r\n          {subtaskName}\r\n          <Tooltip title=\"Edit subtask name\">\r\n            <IconButton\r\n              size=\"small\"\r\n              className=\"subtask-edit-icon\"\r\n              sx={{\r\n                ml: 0.5,\r\n                opacity: 0,\r\n                transition: 'opacity 0.2s',\r\n                padding: '1px'\r\n              }}\r\n            >\r\n              <Iconify icon=\"material-symbols:edit-outline\" width={12} height={12} color=\"#666\" />\r\n            </IconButton>\r\n          </Tooltip>\r\n        </Typography>\r\n      )}\r\n\r\n      <Box sx={{ display: 'flex', alignItems: 'center' }}>\r\n        {subtaskProgress !== null && subtaskProgress !== undefined && (\r\n          <Typography\r\n            variant=\"caption\"\r\n            sx={{\r\n              fontSize: '0.75rem',\r\n              color: subtaskStatusConfig.color,\r\n              ml: 1,\r\n              mr: 1,\r\n              fontFamily: '\"Recursive Variable\", sans-serif',\r\n              fontWeight: 600\r\n            }}\r\n          >\r\n            {subtaskProgress}%\r\n          </Typography>\r\n        )}\r\n\r\n        {/* Delete subtask button */}\r\n        <Tooltip title=\"Delete subtask\">\r\n          <IconButton\r\n            size=\"small\"\r\n            onClick={handleDeleteSubtaskClick}\r\n            sx={{\r\n              p: 0.5,\r\n              color: '#F44336',\r\n              '&:hover': {\r\n                backgroundColor: '#FFEBEE'\r\n              }\r\n            }}\r\n          >\r\n            <Iconify icon=\"material-symbols:delete-outline\" width={14} height={14} />\r\n          </IconButton>\r\n        </Tooltip>\r\n      </Box>\r\n    </Box>\r\n  );\r\n};\r\n\r\n// Format date for display\r\nfunction formatDate(dateString) {\r\n  if (!dateString) return '';\r\n\r\n  try {\r\n    const date = new Date(dateString);\r\n    if (isNaN(date.getTime())) return '';\r\n\r\n    // Only display day and month, not year\r\n    const options = { month: 'short', day: 'numeric' };\r\n    return date.toLocaleDateString('en-US', options);\r\n  } catch (error) {\r\n    return '';\r\n  }\r\n}\r\n\r\nexport default MilestoneCard;\r\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,cAAc,EACdC,MAAM,EACNC,OAAO,EACPC,SAAS,EACTC,UAAU,EACVC,OAAO,EACPC,QAAQ,EACRC,gBAAgB,EAChBC,MAAM,EACNC,WAAW,EACXC,QAAQ,QACH,eAAe;AACtB,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,MAAM,EAAEC,aAAa,QAAQ,sBAAsB;AAC5D,OAAOC,MAAM,MAAM,uBAAuB;AAC1C,OAAOC,aAAa,MAAM,0BAA0B;AACpD,OAAOC,aAAa,MAAM,0BAA0B;AACpD,OAAOC,kBAAkB,MAAM,+BAA+B;AAC9D,SAASC,WAAW,EAAEC,UAAU,EAAEC,aAAa,EAAEC,aAAa,EAAEC,UAAU,EAAEC,mBAAmB,QAAQ,gBAAgB;AACvH,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvC,MAAMC,aAAa,GAAGC,IAAA,IAoBhB;EAAAC,EAAA;EAAA,IAAAC,gBAAA,EAAAC,iBAAA,EAAAC,iBAAA;EAAA,IApBiB;IACrBC,SAAS;IACTC,OAAO,GAAG,KAAK;IACfC,YAAY,GAAG,KAAK;IACpBC,0BAA0B;IAC1BC,kBAAkB;IAClBC,qBAAqB;IACrBC,aAAa;IACbC,wBAAwB;IACxBC,gBAAgB;IAChBC,iBAAiB;IACjBC,YAAY;IACZC,eAAe;IACfC,SAAS;IACTC,YAAY;IACZC,YAAY;IACZC,eAAe;IACfC,eAAe;IACfC,YAAY;IACZC;EACF,CAAC,GAAAvB,IAAA;EACC,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgE,aAAa,EAAEC,gBAAgB,CAAC,GAAGjE,QAAQ,CAAC2C,SAAS,CAACuB,IAAI,CAAC;EAClE,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqE,WAAW,EAAEC,cAAc,CAAC,GAAGtE,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuE,UAAU,EAAEC,aAAa,CAAC,GAAGxE,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;EACpD,MAAMyE,QAAQ,GAAGxE,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMyE,eAAe,GAAGzE,MAAM,CAAC,IAAI,CAAC;;EAEpC;EACA,MAAM0E,QAAQ,GAAG7B,0BAA0B,GAAGA,0BAA0B,CAACH,SAAS,CAAC,GAAG,CAAC;;EAEvF;EACA,MAAMiC,eAAe,GAAG7B,kBAAkB,GAAGA,kBAAkB,CAACJ,SAAS,CAAC,GAAGvB,MAAM,CAACyD,WAAW;EAC/F,MAAMC,YAAY,GAAGzD,aAAa,CAACuD,eAAe,CAAC,IAAIvD,aAAa,CAACD,MAAM,CAACyD,WAAW,CAAC;;EAExF;EACA,MAAME,cAAc,GAAGpC,SAAS,CAACqC,WAAW,IAAIrC,SAAS,CAACqC,WAAW,CAACC,IAAI,CAAC,CAAC,CAACC,MAAM,GAAG,CAAC;;EAEvF;EACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5BpB,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;;EAED;EACA,MAAMqB,UAAU,GAAGA,CAAA,KAAM;IACvB;IACA,MAAMC,WAAW,GAAGrB,aAAa,CAACiB,IAAI,CAAC,CAAC;;IAExC;IACA,IAAII,WAAW,KAAK,EAAE,IAAIA,WAAW,KAAK1C,SAAS,CAACuB,IAAI,EAAE;MACxD,IAAId,iBAAiB,EAAE;QACrBA,iBAAiB,CAAC;UAAE,GAAGT,SAAS;UAAEuB,IAAI,EAAEmB;QAAY,CAAC,CAAC;MACxD;IACF,CAAC,MAAM;MACL;MACApB,gBAAgB,CAACtB,SAAS,CAACuB,IAAI,CAAC;IAClC;IAEAH,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;;EAED;EACA,MAAMuB,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,EAAE;MACrBJ,UAAU,CAAC,CAAC;IACd,CAAC,MAAM,IAAIG,CAAC,CAACC,GAAG,KAAK,QAAQ,EAAE;MAC7BvB,gBAAgB,CAACtB,SAAS,CAACuB,IAAI,CAAC;MAChCH,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAM0B,kBAAkB,GAAGA,CAAA,KAAM;IAC/BrB,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMsB,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAML,WAAW,GAAGhB,WAAW,CAACY,IAAI,CAAC,CAAC;IACtC,IAAII,WAAW,IAAI9B,SAAS,EAAE;MAC5B,MAAMoC,OAAO,GAAG;QACdzB,IAAI,EAAEmB,WAAW;QACjB1C,SAAS,EAAEA,SAAS,CAACiD,EAAE;QACvBC,MAAM,EAAEzE,MAAM,CAACyD,WAAW;QAC1BF,QAAQ,EAAE;MACZ,CAAC;MACDpB,SAAS,CAACoC,OAAO,CAAC;MAClBrB,cAAc,CAAC,EAAE,CAAC;IACpB;IACAF,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;;EAED;EACA,MAAM0B,qBAAqB,GAAIP,CAAC,IAAK;IACnC,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,EAAE;MACrBE,iBAAiB,CAAC,CAAC;IACrB,CAAC,MAAM,IAAIH,CAAC,CAACC,GAAG,KAAK,QAAQ,EAAE;MAC7BlB,cAAc,CAAC,EAAE,CAAC;MAClBF,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACAlE,SAAS,CAAC,MAAM;IACd,IAAI4D,SAAS,IAAIW,QAAQ,CAACsB,OAAO,EAAE;MACjCtB,QAAQ,CAACsB,OAAO,CAACC,KAAK,CAAC,CAAC;IAC1B;EACF,CAAC,EAAE,CAAClC,SAAS,CAAC,CAAC;;EAEf;EACA5D,SAAS,CAAC,MAAM;IACd,IAAIiE,YAAY,IAAIO,eAAe,CAACqB,OAAO,EAAE;MAC3CrB,eAAe,CAACqB,OAAO,CAACC,KAAK,CAAC,CAAC;IACjC;EACF,CAAC,EAAE,CAAC7B,YAAY,CAAC,CAAC;EAElB,oBACEjC,OAAA,CAAC7B,KAAK;IACJ4F,SAAS,EAAE,CAAE;IACbC,SAAS,EAAE5E,MAAM,CAAC6E,aAAc;IAChCC,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAExBpE,OAAA,CAAC/B,GAAG;MAAC+F,SAAS,EAAE5E,MAAM,CAACiF,eAAgB;MAAAD,QAAA,gBACrCpE,OAAA,CAAC/B,GAAG;QAACiG,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,IAAI,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBAE1DpE,OAAA,CAACvB,UAAU;UACTgG,IAAI,EAAC,OAAO;UACZC,OAAO,EAAEA,CAAA,KAAMpC,aAAa,CAAC,CAACD,UAAU,CAAE;UAC1C6B,EAAE,EAAE;YACFS,EAAE,EAAE,CAAC;YACLC,KAAK,EAAE,MAAM;YACb,SAAS,EAAE;cACTC,eAAe,EAAE;YACnB;UACF,CAAE;UAAAT,QAAA,eAEFpE,OAAA,CAAChB,OAAO;YACN8F,IAAI,EAAEzC,UAAU,GAAG,8BAA8B,GAAG,8BAA+B;YACnF0C,KAAK,EAAE,EAAG;YACVC,MAAM,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC,EAEZxD,SAAS,gBACR5B,OAAA,CAAC/B,GAAG;UAACiG,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEQ,KAAK,EAAE;UAAO,CAAE;UAAAX,QAAA,gBAChEpE,OAAA,CAAChB,OAAO;YAAC8F,IAAI,EAAC,uBAAuB;YAACC,KAAK,EAAE,EAAG;YAACC,MAAM,EAAE,EAAG;YAACJ,KAAK,EAAE3F,eAAgB;YAACiF,EAAE,EAAE;cAAES,EAAE,EAAE;YAAE;UAAE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtGpF,OAAA,CAACxB,SAAS;YACR+D,QAAQ,EAAEA,QAAS;YACnB8C,KAAK,EAAEvD,aAAc;YACrBwD,QAAQ,EAAGjC,CAAC,IAAKtB,gBAAgB,CAACsB,CAAC,CAACkC,MAAM,CAACF,KAAK,CAAE;YAClDG,SAAS,EAAEpC,cAAe;YAC1BqC,MAAM,EAAEvC,UAAW;YACnBwC,OAAO,EAAC,UAAU;YAClBC,SAAS;YACTC,SAAS;YACT1B,EAAE,EAAE;cACF2B,UAAU,EAAE,kCAAkC;cAC9CC,UAAU,EAAE,GAAG;cACfC,QAAQ,EAAE,QAAQ;cAClB,uBAAuB,EAAE;gBACvBD,UAAU,EAAE,GAAG;gBACfC,QAAQ,EAAE,QAAQ;gBAClBF,UAAU,EAAE;cACd;YACF;UAAE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACFpF,OAAA,CAACvB,UAAU;YAACgG,IAAI,EAAC,OAAO;YAACC,OAAO,EAAExB,UAAW;YAACgB,EAAE,EAAE;cAAE8B,EAAE,EAAE;YAAE,CAAE;YAAA5B,QAAA,eAC1DpE,OAAA,CAAChB,OAAO;cAAC8F,IAAI,EAAC,wBAAwB;cAACC,KAAK,EAAE,EAAG;cAACC,MAAM,EAAE,EAAG;cAACJ,KAAK,EAAC;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,gBAENpF,OAAA,CAAC9B,UAAU;UACTwH,OAAO,EAAC,IAAI;UACZ1B,SAAS,EAAE5E,MAAM,CAAC6G,cAAe;UACjC/B,EAAE,EAAE;YACF2B,UAAU,EAAE,kCAAkC;YAC9CvB,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpB2B,MAAM,EAAE,SAAS;YACjB1B,IAAI,EAAE,CAAC;YACP,SAAS,EAAE;cACT,cAAc,EAAE;gBACd2B,OAAO,EAAE;cACX;YACF;UACF,CAAE;UACFzB,OAAO,EAAEzB,eAAgB;UAAAmB,QAAA,gBAEzBpE,OAAA,CAAChB,OAAO;YAAC8F,IAAI,EAAC,uBAAuB;YAACC,KAAK,EAAE,EAAG;YAACC,MAAM,EAAE,EAAG;YAACJ,KAAK,EAAE3F;UAAgB;YAAAgG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACtFtD,aAAa,eACd9B,OAAA,CAACtB,OAAO;YAAC0H,KAAK,EAAC,qBAAqB;YAAAhC,QAAA,eAClCpE,OAAA,CAACvB,UAAU;cACTgG,IAAI,EAAC,OAAO;cACZT,SAAS,EAAC,WAAW;cACrBE,EAAE,EAAE;gBACF8B,EAAE,EAAE,CAAC;gBACLG,OAAO,EAAE,CAAC;gBACVE,UAAU,EAAE,cAAc;gBAC1BlC,OAAO,EAAE;cACX,CAAE;cAAAC,QAAA,eAEFpE,OAAA,CAAChB,OAAO;gBAAC8F,IAAI,EAAC,+BAA+B;gBAACC,KAAK,EAAE,EAAG;gBAACC,MAAM,EAAE,EAAG;gBAACJ,KAAK,EAAC;cAAM;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENpF,OAAA,CAAC5B,IAAI;QACH0G,IAAI,eAAE9E,OAAA,CAAChB,OAAO;UAAC8F,IAAI,EAAElC,YAAY,CAACkC,IAAK;UAACC,KAAK,EAAE,EAAG;UAACC,MAAM,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAClEkB,KAAK,EAAE1D,YAAY,CAAC0D,KAAM;QAC1B7B,IAAI,EAAC,OAAO;QACZP,EAAE,EAAE;UACFW,eAAe,EAAE,GAAGjC,YAAY,CAACgC,KAAK,IAAI;UAC1CA,KAAK,EAAEhC,YAAY,CAACgC,KAAK;UACzBkB,UAAU,EAAE,GAAG;UACfS,YAAY,EAAE,KAAK;UACnBV,UAAU,EAAE;QACd;MAAE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,EAGL,CAAC1E,OAAO,IAAImC,cAAc,iBACzB7C,OAAA,CAAC/B,GAAG;MAACiG,EAAE,EAAE;QAAEsC,EAAE,EAAE,CAAC;QAAEC,EAAE,EAAE;MAAI,CAAE;MAAArC,QAAA,eAC1BpE,OAAA,CAAC/B,GAAG;QACFiG,EAAE,EAAE;UACFW,eAAe,EAAE,SAAS;UAC1B6B,CAAC,EAAE,GAAG;UACNH,YAAY,EAAE,KAAK;UACnBI,MAAM,EAAE;QACV,CAAE;QAAAvC,QAAA,eAEFpE,OAAA,CAAC9B,UAAU;UACTwH,OAAO,EAAC,OAAO;UACfxB,EAAE,EAAE;YACFU,KAAK,EAAE,MAAM;YACbiB,UAAU,EAAE,kCAAkC;YAC9Ce,UAAU,EAAE,UAAU;YACtBC,UAAU,EAAE,GAAG;YACff,UAAU,EAAE;UACd,CAAE;UAAA1B,QAAA,EAED3D,SAAS,CAACqC;QAAW;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN,eAEDpF,OAAA,CAACzB,OAAO;MAAC2F,EAAE,EAAE;QAAE4C,EAAE,EAAE,CAAC;QAAEX,OAAO,EAAE;MAAI;IAAE;MAAAlB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAExCpF,OAAA,CAAC/B,GAAG;MAACiG,EAAE,EAAE;QAAEsC,EAAE,EAAE;MAAE,CAAE;MAAApC,QAAA,gBACjBpE,OAAA,CAAC/B,GAAG;QAACiG,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEyC,cAAc,EAAE,eAAe;UAAExC,UAAU,EAAE,QAAQ;UAAEiC,EAAE,EAAE;QAAI,CAAE;QAAApC,QAAA,gBAC3FpE,OAAA,CAAC9B,UAAU;UAACwH,OAAO,EAAC,OAAO;UAACxB,EAAE,EAAE;YAAE4B,UAAU,EAAE,GAAG;YAAED,UAAU,EAAE,kCAAkC;YAAEjB,KAAK,EAAE;UAAO,CAAE;UAAAR,QAAA,EAAC;QAEpH;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eACbpF,OAAA,CAAC9B,UAAU;UAACwH,OAAO,EAAC,OAAO;UAACxB,EAAE,EAAE;YAAE4B,UAAU,EAAE,GAAG;YAAED,UAAU,EAAE,kCAAkC;YAAEjB,KAAK,EAAE;UAAO,CAAE;UAAAR,QAAA,GAChH3B,QAAQ,EAAC,GACZ;QAAA;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,eACNpF,OAAA,CAAC3B,cAAc;QACbqH,OAAO,EAAC,aAAa;QACrBL,KAAK,EAAE5C,QAAS;QAChByB,EAAE,EAAE;UACFc,MAAM,EAAE,CAAC;UACTuB,YAAY,EAAE,CAAC;UACf1B,eAAe,EAAE,SAAS;UAC1B,0BAA0B,EAAE;YAC1BA,eAAe,EAAEjC,YAAY,CAACgC;UAChC;QACF;MAAE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAENpF,OAAA,CAAC/B,GAAG;MAACiG,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEyC,cAAc,EAAE,eAAe;QAAExC,UAAU,EAAE,QAAQ;QAAEiC,EAAE,EAAE;MAAE,CAAE;MAAApC,QAAA,EACxF3D,SAAS,CAACuG,kBAAkB,iBAC3BhH,OAAA,CAAC/B,GAAG;QAACiG,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAE0C,GAAG,EAAE;QAAE,CAAE;QAAA7C,QAAA,gBACzDpE,OAAA,CAAChB,OAAO;UAAC8F,IAAI,EAAC,wBAAwB;UAACC,KAAK,EAAE,EAAG;UAACC,MAAM,EAAE,EAAG;UAACJ,KAAK,EAAC;QAAM;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC7EpF,OAAA,CAAC9B,UAAU;UAACwH,OAAO,EAAC,OAAO;UAACxB,EAAE,EAAE;YAAE2B,UAAU,EAAE,kCAAkC;YAAEjB,KAAK,EAAE,MAAM;YAAEkB,UAAU,EAAE;UAAI,CAAE;UAAA1B,QAAA,EAChH3D,SAAS,CAACuG;QAAkB;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNpF,OAAA,CAAC/B,GAAG;MAACiG,EAAE,EAAE;QAAEsC,EAAE,EAAE;MAAE,CAAE;MAAApC,QAAA,gBACjBpE,OAAA,CAAC/B,GAAG;QACFiG,EAAE,EAAE;UACFI,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBwC,cAAc,EAAE,eAAe;UAC/BP,EAAE,EAAE;QACN,CAAE;QAAApC,QAAA,gBAEFpE,OAAA,CAAC/B,GAAG;UAACiG,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAE0C,GAAG,EAAE;UAAE,CAAE;UAAA7C,QAAA,gBACzDpE,OAAA,CAAChB,OAAO;YACN8F,IAAI,EAAC,4BAA4B;YACjCC,KAAK,EAAE,EAAG;YACVC,MAAM,EAAE,EAAG;YACXd,EAAE,EAAE;cAAEU,KAAK,EAAE;YAAO;UAAE;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eACFpF,OAAA,CAAC9B,UAAU;YACTwH,OAAO,EAAC,OAAO;YACfxB,EAAE,EAAE;cACF4B,UAAU,EAAE,GAAG;cACflB,KAAK,EAAE,MAAM;cACbiB,UAAU,EAAE,kCAAkC;cAC9CE,QAAQ,EAAE;YACZ,CAAE;YAAA3B,QAAA,EACH;UAED;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eAGNpF,OAAA,CAACtB,OAAO;UAAC0H,KAAK,EAAC,cAAc;UAAAhC,QAAA,eAC3BpE,OAAA,CAACvB,UAAU;YACTgG,IAAI,EAAC,OAAO;YACZC,OAAO,EAAEnB,kBAAmB;YAC5BW,EAAE,EAAE;cACFU,KAAK,EAAE3F,eAAe;cACtB,SAAS,EAAE;gBACT4F,eAAe,EAAE,GAAG5F,eAAe;cACrC;YACF,CAAE;YAAAmF,QAAA,eAEFpE,OAAA,CAAChB,OAAO;cAAC8F,IAAI,EAAC,2BAA2B;cAACC,KAAK,EAAE,EAAG;cAACC,MAAM,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CAAC,EAEL1E,OAAO,gBACNV,OAAA,CAAC/B,GAAG;QAAC+F,SAAS,EAAE5E,MAAM,CAAC8H,QAAS;QAAA9C,QAAA,IAAA9D,gBAAA,GAC7BG,SAAS,CAAC0G,KAAK,cAAA7G,gBAAA,uBAAfA,gBAAA,CAAiB8G,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC5CvH,OAAA,CAACwH,QAAQ;UAEPF,IAAI,EAAEA,IAAK;UACX3G,YAAY,EAAEA,YAAa;UAC3BD,OAAO,EAAE,IAAK;UACdI,qBAAqB,EAAEA,qBAAsB;UAC7CC,aAAa,EAAEA,aAAc;UAC7BC,wBAAwB,EAAEA,wBAAyB;UACnDC,gBAAgB,EAAEA,gBAAiB;UACnCE,YAAY,EAAEA,YAAa;UAC3BC,eAAe,EAAEA,eAAgB;UACjCE,YAAY,EAAEA,YAAa;UAC3BC,YAAY,EAAEA,YAAa;UAC3BC,eAAe,EAAEA,eAAgB;UACjCC,eAAe,EAAEA,eAAgB;UACjCC,YAAY,EAAEA,YAAa;UAC3BC,SAAS,EAAEA;QAAU,GAfhB4F,KAAK;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgBX,CACF,CAAC,EAED,EAAA7E,iBAAA,GAAAE,SAAS,CAAC0G,KAAK,cAAA5G,iBAAA,uBAAfA,iBAAA,CAAiByC,MAAM,IAAG,CAAC,iBAC1BhD,OAAA,CAAC1B,MAAM;UACLoH,OAAO,EAAC,MAAM;UACdjB,IAAI,EAAC,OAAO;UACZP,EAAE,EAAE;YACFU,KAAK,EAAE3F,eAAe;YACtB6G,UAAU,EAAE,GAAG;YACf2B,aAAa,EAAE,MAAM;YACrBf,CAAC,EAAE,CAAC;YACJD,EAAE,EAAE,CAAC;YACLZ,UAAU,EAAE;UACd,CAAE;UAAAzB,QAAA,GACH,IACG,EAAC3D,SAAS,CAAC0G,KAAK,CAACnE,MAAM,GAAG,CAAC,EAAC,aAChC;QAAA;UAAAiC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,gBAENpF,OAAA,CAAC/B,GAAG;QAAC+F,SAAS,EAAE5E,MAAM,CAAC8H,QAAS;QAAA9C,QAAA,IAAA5D,iBAAA,GAC7BC,SAAS,CAAC0G,KAAK,cAAA3G,iBAAA,uBAAfA,iBAAA,CAAiB6G,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAChCvH,OAAA,CAACwH,QAAQ;UAEPF,IAAI,EAAEA,IAAK;UACX3G,YAAY,EAAEA,YAAa;UAC3BD,OAAO,EAAE,KAAM;UACfI,qBAAqB,EAAEA,qBAAsB;UAC7CC,aAAa,EAAEA,aAAc;UAC7BC,wBAAwB,EAAEA,wBAAyB;UACnDC,gBAAgB,EAAEA,gBAAiB;UACnCE,YAAY,EAAEA,YAAa;UAC3BC,eAAe,EAAEA,eAAgB;UACjCE,YAAY,EAAEA,YAAa;UAC3BC,YAAY,EAAEA,YAAa;UAC3BC,eAAe,EAAEA,eAAgB;UACjCC,eAAe,EAAEA,eAAgB;UACjCC,YAAY,EAAEA,YAAa;UAC3BC,SAAS,EAAEA;QAAU,GAfhB4F,KAAK;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAgBX,CACF,CAAC,EAGDnD,YAAY,iBACXjC,OAAA,CAAC/B,GAAG;UACF+F,SAAS,EAAE5E,MAAM,CAACsI,QAAS;UAC3BxD,EAAE,EAAE;YACFyD,QAAQ,EAAE,UAAU;YACpBlB,EAAE,EAAE,CAAC;YACLnC,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBM,eAAe,EAAE,SAAS;YAC1B0B,YAAY,EAAE,KAAK;YACnBpC,OAAO,EAAE,UAAU;YACnBwC,MAAM,EAAE;UACV,CAAE;UAAAvC,QAAA,gBAEFpE,OAAA,CAAC/B,GAAG;YACFiG,EAAE,EAAE;cACFa,KAAK,EAAE,EAAE;cACTC,MAAM,EAAE,EAAE;cACVuB,YAAY,EAAE,KAAK;cACnB1B,eAAe,EAAE,SAAS;cAC1BF,EAAE,EAAE,GAAG;cACPiD,UAAU,EAAE;YACd;UAAE;YAAA3C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEFpF,OAAA,CAACxB,SAAS;YACR+D,QAAQ,EAAEC,eAAgB;YAC1B6C,KAAK,EAAElD,WAAY;YACnBmD,QAAQ,EAAGjC,CAAC,IAAKjB,cAAc,CAACiB,CAAC,CAACkC,MAAM,CAACF,KAAK,CAAE;YAChDG,SAAS,EAAE5B,qBAAsB;YACjCiE,WAAW,EAAC,wBAAwB;YACpCnC,OAAO,EAAC,UAAU;YAClBC,SAAS;YACTC,SAAS;YACT1B,EAAE,EAAE;cACF2B,UAAU,EAAE,kCAAkC;cAC9C,uBAAuB,EAAE;gBACvBE,QAAQ,EAAE,QAAQ;gBAClBF,UAAU,EAAE,kCAAkC;gBAC9C1B,OAAO,EAAE;cACX,CAAC;cACD,8BAA8B,EAAE;gBAC9B2D,iBAAiB,EAAE;cACrB;YACF;UAAE;YAAA7C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEFpF,OAAA,CAAC/B,GAAG;YAACiG,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEyB,EAAE,EAAE;YAAO,CAAE;YAAA5B,QAAA,gBAC7DpE,OAAA,CAACvB,UAAU;cAACgG,IAAI,EAAC,OAAO;cAACC,OAAO,EAAElB,iBAAkB;cAACU,EAAE,EAAE;gBAAE8B,EAAE,EAAE;cAAE,CAAE;cAAA5B,QAAA,eACjEpE,OAAA,CAAChB,OAAO;gBAAC8F,IAAI,EAAC,wBAAwB;gBAACC,KAAK,EAAE,EAAG;gBAACC,MAAM,EAAE,EAAG;gBAACJ,KAAK,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC,eAEbpF,OAAA,CAACvB,UAAU;cAACgG,IAAI,EAAC,OAAO;cAACC,OAAO,EAAEA,CAAA,KAAMxC,eAAe,CAAC,KAAK,CAAE;cAACgC,EAAE,EAAE;gBAAE8B,EAAE,EAAE;cAAI,CAAE;cAAA5B,QAAA,eAC9EpE,OAAA,CAAChB,OAAO;gBAAC8F,IAAI,EAAC,wBAAwB;gBAACC,KAAK,EAAE,EAAG;gBAACC,MAAM,EAAE,EAAG;gBAACJ,KAAK,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACD,CAAC;AAEZ,CAAC;AACD;AAAA/E,EAAA,CA7cMF,aAAa;AAAA4H,EAAA,GAAb5H,aAAa;AA8cnB,MAAMqH,QAAQ,GAAGQ,KAAA,IAgBX;EAAAC,GAAA;EAAA,IAAAC,oBAAA;EAAA,IAhBY;IAChBZ,IAAI;IACJ3G,YAAY,GAAG,KAAK;IACpBD,OAAO,GAAG,KAAK;IACfI,qBAAqB;IACrBC,aAAa;IACbC,wBAAwB;IACxBC,gBAAgB;IAChBE,YAAY;IACZC,eAAe;IACfE,YAAY;IACZC,YAAY;IACZC,eAAe;IACfC,eAAe;IACfC,YAAY;IACZC;EACF,CAAC,GAAAqG,KAAA;EACC,MAAM,CAACpG,SAAS,EAAEC,YAAY,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACqK,QAAQ,EAAEC,WAAW,CAAC,GAAGtK,QAAQ,CAACwJ,IAAI,CAACtF,IAAI,CAAC;EACnD,MAAMqG,YAAY,GAAGtK,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM,CAACuK,eAAe,EAAEC,kBAAkB,CAAC,GAAGzK,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAAC0K,cAAc,EAAEC,iBAAiB,CAAC,GAAG3K,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM4K,kBAAkB,GAAG3K,MAAM,CAAC,IAAI,CAAC;;EAEvC;EACA,MAAM,CAAC4K,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9K,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC+K,QAAQ,EAAEC,WAAW,CAAC,GAAGhL,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACiL,eAAe,EAAEC,kBAAkB,CAAC,GAAGlL,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAACmL,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGpL,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACqL,eAAe,EAAEC,kBAAkB,CAAC,GAAGtL,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAACuL,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGxL,QAAQ,CAAC,KAAK,CAAC;;EAE3E;EACA,MAAM,CAACyL,SAAS,EAAEC,YAAY,CAAC,GAAG1L,QAAQ,CAACwJ,IAAI,CAAC;;EAEhD;EACAtJ,SAAS,CAAC,MAAM;IACdwL,YAAY,CAAClC,IAAI,CAAC;EACpB,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;EAEV,MAAMmC,kBAAkB,GAAIpG,CAAC,IAAK;IAChCA,CAAC,CAACqG,eAAe,CAAC,CAAC;IACnBd,oBAAoB,CAAC,IAAI,CAAC;IAC1BI,kBAAkB,CAAC,IAAI,CAAC;IACxBxJ,WAAW,CAAC8H,IAAI,CAAC5D,EAAE,CAAC,CACjBiG,IAAI,CAACC,QAAQ,IAAI;MAAA,IAAAC,cAAA,EAAAC,eAAA;MAChBC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEJ,QAAQ,CAACK,IAAI,CAAC;MAC5C;MACA,IAAIC,YAAY,GAAG,EAAE;MACrB,IAAIC,KAAK,CAACC,OAAO,CAACR,QAAQ,CAACK,IAAI,CAAC,EAAE;QAChCC,YAAY,GAAGN,QAAQ,CAACK,IAAI;MAC9B,CAAC,MAAM,IAAI,CAAAJ,cAAA,GAAAD,QAAQ,CAACK,IAAI,cAAAJ,cAAA,eAAbA,cAAA,CAAeI,IAAI,IAAIE,KAAK,CAACC,OAAO,CAACR,QAAQ,CAACK,IAAI,CAACA,IAAI,CAAC,EAAE;QACnEC,YAAY,GAAGN,QAAQ,CAACK,IAAI,CAACA,IAAI;MACnC,CAAC,MAAM,IAAI,CAAAH,eAAA,GAAAF,QAAQ,CAACK,IAAI,cAAAH,eAAA,eAAbA,eAAA,CAAejB,QAAQ,IAAIsB,KAAK,CAACC,OAAO,CAACR,QAAQ,CAACK,IAAI,CAACpB,QAAQ,CAAC,EAAE;QAC3EqB,YAAY,GAAGN,QAAQ,CAACK,IAAI,CAACpB,QAAQ;MACvC;MACAC,WAAW,CAACoB,YAAY,CAAC;IAC3B,CAAC,CAAC,CACDG,KAAK,CAACC,KAAK,IAAI;MACdP,OAAO,CAACO,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDxK,KAAK,CAACwK,KAAK,CAAC,yBAAyB,CAAC;IACxC,CAAC,CAAC,CACDC,OAAO,CAAC,MAAM;MACbvB,kBAAkB,CAAC,KAAK,CAAC;IAC3B,CAAC,CAAC;EACN,CAAC;EAED,MAAMwB,gBAAgB,GAAG,MAAOC,OAAO,IAAK;IAC1C,IAAI;MAAA,IAAAC,eAAA;MACF,MAAMd,QAAQ,GAAG,MAAMnK,UAAU,CAAC6H,IAAI,CAAC5D,EAAE,EAAE+G,OAAO,CAAC;MACnDV,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEJ,QAAQ,CAAC;;MAE9C;MACA,IAAIe,UAAU;MACd,KAAAD,eAAA,GAAId,QAAQ,CAACK,IAAI,cAAAS,eAAA,eAAbA,eAAA,CAAeT,IAAI,EAAE;QACvBU,UAAU,GAAGf,QAAQ,CAACK,IAAI,CAACA,IAAI;MACjC,CAAC,MAAM;QACL;QACA,MAAMW,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC;QAClEL,UAAU,GAAG;UACXjH,EAAE,EAAEuH,IAAI,CAACC,GAAG,CAAC,CAAC;UAAE;UAChBT,OAAO,EAAEA,OAAO;UAChBU,IAAI,EAAE;YACJzH,EAAE,EAAEkH,WAAW,CAAClH,EAAE;YAClB0H,UAAU,EAAER,WAAW,CAACQ,UAAU;YAClCC,SAAS,EAAET,WAAW,CAACS,SAAS;YAChCC,MAAM,EAAEV,WAAW,CAACU;UACtB,CAAC;UACDC,UAAU,EAAE,IAAIN,IAAI,CAAC,CAAC,CAACO,cAAc,CAAC,OAAO,EAAE;YAC7CC,IAAI,EAAE,SAAS;YACfC,MAAM,EAAE,SAAS;YACjBC,GAAG,EAAE,SAAS;YACdC,KAAK,EAAE;UACT,CAAC,CAAC,CAACC,OAAO,CAAC,GAAG,EAAE,EAAE;QACpB,CAAC;MACH;;MAEA;MACA/C,WAAW,CAACgD,YAAY,IAAI,CAAC,GAAGA,YAAY,EAAEnB,UAAU,CAAC,CAAC;;MAE1D;MACAoB,eAAe,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CxK,KAAK,CAACwK,KAAK,CAAC,uBAAuB,CAAC;IACtC;EACF,CAAC;EAED,MAAM0B,mBAAmB,GAAG,MAAAA,CAAOC,SAAS,EAAExB,OAAO,KAAK;IACxD,IAAI;MACF;MACA,MAAMyB,kBAAkB,GAAGrD,QAAQ,CAACsD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC1I,EAAE,KAAKuI,SAAS,CAAC;MACjE,IAAIC,kBAAkB,EAAE;QACtB,MAAMG,eAAe,GAAGxD,QAAQ,CAACxB,GAAG,CAAC+E,CAAC,IACpCA,CAAC,CAAC1I,EAAE,KAAKuI,SAAS,GAAG;UAAE,GAAGG,CAAC;UAAE3B,OAAO,EAAEA;QAAQ,CAAC,GAAG2B,CACpD,CAAC;QACDtD,WAAW,CAACuD,eAAe,CAAC;MAC9B;;MAEA;MACA,MAAMzC,QAAQ,GAAG,MAAMlK,aAAa,CAACuM,SAAS,EAAExB,OAAO,CAAC;MACxDV,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEJ,QAAQ,CAAC;MAEjD9J,KAAK,CAACwM,OAAO,CAAC,8BAA8B,CAAC;;MAE7C;MACAP,eAAe,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CxK,KAAK,CAACwK,KAAK,CAAC,0BAA0B,CAAC;MACvC;MACAyB,eAAe,CAAC,CAAC;IACnB;EACF,CAAC;EAED,MAAMQ,mBAAmB,GAAG,MAAON,SAAS,IAAK;IAC/C,IAAI;MACF;MACAnD,WAAW,CAACgD,YAAY,IAAIA,YAAY,CAACU,MAAM,CAACJ,CAAC,IAAIA,CAAC,CAAC1I,EAAE,KAAKuI,SAAS,CAAC,CAAC;;MAEzE;MACA,MAAMtM,aAAa,CAACsM,SAAS,CAAC;MAC9BlC,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAE3ClK,KAAK,CAACwM,OAAO,CAAC,8BAA8B,CAAC;;MAE7C;MACAP,eAAe,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CxK,KAAK,CAACwK,KAAK,CAAC,0BAA0B,CAAC;MACvC;MACAyB,eAAe,CAAC,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMA,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MAAA,IAAAU,eAAA,EAAAC,eAAA;MACF,MAAM9C,QAAQ,GAAG,MAAMpK,WAAW,CAAC8H,IAAI,CAAC5D,EAAE,CAAC;MAC3CqG,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEJ,QAAQ,CAACK,IAAI,CAAC;;MAEjD;MACA,IAAIC,YAAY,GAAG,EAAE;MACrB,IAAIC,KAAK,CAACC,OAAO,CAACR,QAAQ,CAACK,IAAI,CAAC,EAAE;QAChCC,YAAY,GAAGN,QAAQ,CAACK,IAAI;MAC9B,CAAC,MAAM,IAAI,CAAAwC,eAAA,GAAA7C,QAAQ,CAACK,IAAI,cAAAwC,eAAA,eAAbA,eAAA,CAAexC,IAAI,IAAIE,KAAK,CAACC,OAAO,CAACR,QAAQ,CAACK,IAAI,CAACA,IAAI,CAAC,EAAE;QACnEC,YAAY,GAAGN,QAAQ,CAACK,IAAI,CAACA,IAAI;MACnC,CAAC,MAAM,IAAI,CAAAyC,eAAA,GAAA9C,QAAQ,CAACK,IAAI,cAAAyC,eAAA,eAAbA,eAAA,CAAe7D,QAAQ,IAAIsB,KAAK,CAACC,OAAO,CAACR,QAAQ,CAACK,IAAI,CAACpB,QAAQ,CAAC,EAAE;QAC3EqB,YAAY,GAAGN,QAAQ,CAACK,IAAI,CAACpB,QAAQ;MACvC;MAEAC,WAAW,CAACoB,YAAY,CAAC;IAC3B,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF,CAAC;;EAED;EACA,MAAMqC,YAAY,GAAG7L,qBAAqB,GAAGA,qBAAqB,CAACwG,IAAI,CAAC,GAAG,CAAC;;EAE5E;EACA,MAAMsF,UAAU,GAAG7L,aAAa,GAAGA,aAAa,CAACuG,IAAI,CAAC,GAAIA,IAAI,CAAC3D,MAAM,IAAIzE,MAAM,CAACyD,WAAY;EAC5F,MAAMC,YAAY,GAAGzD,aAAa,CAACyN,UAAU,CAAC,IAAIzN,aAAa,CAACD,MAAM,CAACyD,WAAW,CAAC;;EAEnF;EACA,MAAMkK,WAAW,GAAGvF,IAAI,CAACwF,QAAQ,IAAIxF,IAAI,CAACwF,QAAQ,CAAC9J,MAAM,GAAG,CAAC;;EAE7D;EACA,MAAM+J,WAAW,GAAGH,UAAU,KAAK1N,MAAM,CAAC8N,SAAS;;EAEnD;EACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChCpL,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;;EAED;EACA,MAAMqL,cAAc,GAAGA,CAAA,KAAM;IAC3B;IACA,MAAM/J,WAAW,GAAGgF,QAAQ,CAACpF,IAAI,CAAC,CAAC;;IAEnC;IACA,IAAII,WAAW,KAAK,EAAE,IAAIA,WAAW,KAAKmE,IAAI,CAACtF,IAAI,EAAE;MACnD,IAAIb,YAAY,EAAE;QAChBA,YAAY,CAAC;UAAE,GAAGmG,IAAI;UAAEtF,IAAI,EAAEmB;QAAY,CAAC,CAAC;MAC9C;IACF,CAAC,MAAM;MACL;MACAiF,WAAW,CAACd,IAAI,CAACtF,IAAI,CAAC;IACxB;IAEAH,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;;EAED;EACA,MAAMsL,kBAAkB,GAAI9J,CAAC,IAAK;IAChC,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,EAAE;MACrB4J,cAAc,CAAC,CAAC;IAClB,CAAC,MAAM,IAAI7J,CAAC,CAACC,GAAG,KAAK,QAAQ,EAAE;MAC7B8E,WAAW,CAACd,IAAI,CAACtF,IAAI,CAAC;MACtBH,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMuL,qBAAqB,GAAGA,CAAA,KAAM;IAClC7E,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAM8E,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI9L,YAAY,EAAE;MAChBA,YAAY,CAAC+F,IAAI,CAAC;IACpB;EACF,CAAC;;EAED;EACA,MAAMgG,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMnK,WAAW,GAAGqF,cAAc,CAACzF,IAAI,CAAC,CAAC;IACzC,IAAII,WAAW,IAAI7B,YAAY,EAAE;MAC/B,MAAMiM,UAAU,GAAG;QACjBvL,IAAI,EAAEmB,WAAW;QACjBmE,IAAI,EAAEA,IAAI,CAACkG,IAAI;QACf7J,MAAM,EAAEzE,MAAM,CAACyD,WAAW;QAC1BF,QAAQ,EAAE;MACZ,CAAC;MACDnB,YAAY,CAACiM,UAAU,CAAC;MACxB9E,iBAAiB,CAAC,EAAE,CAAC;IACvB;IACAF,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMkF,wBAAwB,GAAIpK,CAAC,IAAK;IACtC,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,EAAE;MACrBgK,oBAAoB,CAAC,CAAC;IACxB,CAAC,MAAM,IAAIjK,CAAC,CAACC,GAAG,KAAK,QAAQ,EAAE;MAC7BmF,iBAAiB,CAAC,EAAE,CAAC;MACrBF,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACAvK,SAAS,CAAC,MAAM;IACd,IAAI4D,SAAS,IAAIyG,YAAY,CAACxE,OAAO,EAAE;MACrCwE,YAAY,CAACxE,OAAO,CAACC,KAAK,CAAC,CAAC;IAC9B;EACF,CAAC,EAAE,CAAClC,SAAS,CAAC,CAAC;;EAEf;EACA5D,SAAS,CAAC,MAAM;IACd,IAAIsK,eAAe,IAAII,kBAAkB,CAAC7E,OAAO,EAAE;MACjD6E,kBAAkB,CAAC7E,OAAO,CAACC,KAAK,CAAC,CAAC;IACpC;EACF,CAAC,EAAE,CAACwE,eAAe,CAAC,CAAC;;EAErB;EACA,MAAMoF,WAAW,GAAGnE,SAAS,CAACoE,UAAU,IAAIpE,SAAS,CAACqE,QAAQ;EAE9D,MAAMC,uBAAuB,GAAIxK,CAAC,IAAK;IACrCA,CAAC,CAACqG,eAAe,CAAC,CAAC;IACnBR,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAM4E,mBAAmB,GAAG,MAAOC,WAAW,IAAK;IACjD3E,kBAAkB,CAAC,IAAI,CAAC;IACxB,IAAI;MACF,MAAM4E,YAAY,GAAG;QACnB,GAAGzE,SAAS;QACZoE,UAAU,EAAEI,WAAW,CAACJ,UAAU;QAClCC,QAAQ,EAAEG,WAAW,CAACH,QAAQ;QAC9BnL,QAAQ,EAAE8G,SAAS,CAAC9G,QAAQ,IAAI,CAAC;QACjCkB,MAAM,EAAE4F,SAAS,CAAC5F,MAAM,IAAI;MAC9B,CAAC;;MAED;MACA6F,YAAY,CAACwE,YAAY,CAAC;;MAE1B;MACA,MAAMpE,QAAQ,GAAG,MAAMhK,UAAU,CAACoO,YAAY,CAAC;;MAE/C;MACA,IAAIpE,QAAQ,IAAIA,QAAQ,CAACK,IAAI,EAAE;QAC7BT,YAAY,CAACI,QAAQ,CAACK,IAAI,CAAC;MAC7B;;MAEA;MACA,IAAI9I,YAAY,EAAE;QAChBA,YAAY,CAAC6M,YAAY,CAAC;MAC5B;MAEAlO,KAAK,CAACwM,OAAO,CAAC,+BAA+B,CAAC;IAChD,CAAC,CAAC,OAAOhC,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDxK,KAAK,CAACwK,KAAK,CAAC,2BAA2B,CAAC;;MAExC;MACAd,YAAY,CAAClC,IAAI,CAAC;IACpB,CAAC,SAAS;MACR8B,kBAAkB,CAAC,KAAK,CAAC;MACzBF,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,MAAM+E,mBAAmB,GAAG,MAAOC,eAAe,IAAK;IACrD,IAAI;MACF;MACA,MAAMC,UAAU,GAAGD,eAAe,CAAC7G,GAAG,CAAC3D,EAAE,IAAI0K,MAAM,CAAC1K,EAAE,CAAC,CAAC;MACxD;MACA,MAAMkG,QAAQ,GAAG,MAAM/J,mBAAmB,CAAC0J,SAAS,CAACiE,IAAI,EAAEW,UAAU,CAAC;MACtE;MACA,MAAMJ,WAAW,GAAG;QAClB,GAAGxE,SAAS;QACZ8E,SAAS,EAAEzE,QAAQ,CAACyE,SAAS,IAAIF,UAAU,CAAC9G,GAAG,CAAC3D,EAAE,KAAK;UACrDA,EAAE,EAAEA,EAAE;UACN0H,UAAU,EAAE,EAAE;UACdC,SAAS,EAAE,EAAE;UACbiD,KAAK,EAAE;QACT,CAAC,CAAC;MACJ,CAAC;;MAED;MACA9E,YAAY,CAACuE,WAAW,CAAC;MACzB,IAAI5M,YAAY,EAAE;QAChBA,YAAY,CAAC4M,WAAW,CAAC;MAC3B;MAEAzE,yBAAyB,CAAC,KAAK,CAAC;MAChCxJ,KAAK,CAACwM,OAAO,CAAC,+BAA+B,CAAC;IAChD,CAAC,CAAC,OAAOhC,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDxK,KAAK,CAACwK,KAAK,CAAC,0BAA0B,CAAC;IACzC;EACF,CAAC;EAED,oBACEtK,OAAA,CAAAE,SAAA;IAAAkE,QAAA,gBACEpE,OAAA,CAAC/B,GAAG;MAAC+F,SAAS,EAAE5E,MAAM,CAACmP,iBAAkB;MAAAnK,QAAA,gBACvCpE,OAAA,CAAC/B,GAAG;QACF+F,SAAS,EAAE5E,MAAM,CAACsI,QAAS;QAC3BxD,EAAE,EAAE;UAAEyD,QAAQ,EAAE;QAAW,CAAE;QAAAvD,QAAA,gBAC7BpE,OAAA,CAAC/B,GAAG;UACFiG,EAAE,EAAE;YACFa,KAAK,EAAE,EAAE;YACTC,MAAM,EAAE,EAAE;YACVuB,YAAY,EAAE,KAAK;YACnB1B,eAAe,EAAEjC,YAAY,CAACgC,KAAK;YACnCD,EAAE,EAAE,GAAG;YACPiD,UAAU,EAAE;UACd;QAAE;UAAA3C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAEDxD,SAAS,gBACR5B,OAAA,CAAC/B,GAAG;UAACiG,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEiK,QAAQ,EAAE;UAAE,CAAE;UAAApK,QAAA,gBAC9DpE,OAAA,CAACxB,SAAS;YACR+D,QAAQ,EAAE8F,YAAa;YACvBhD,KAAK,EAAE8C,QAAS;YAChB7C,QAAQ,EAAGjC,CAAC,IAAK+E,WAAW,CAAC/E,CAAC,CAACkC,MAAM,CAACF,KAAK,CAAE;YAC7CG,SAAS,EAAE2H,kBAAmB;YAC9B1H,MAAM,EAAEyH,cAAe;YACvBxH,OAAO,EAAC,UAAU;YAClBC,SAAS;YACTC,SAAS;YACT1B,EAAE,EAAE;cACF2B,UAAU,EAAE,kCAAkC;cAC9C,uBAAuB,EAAE;gBACvBE,QAAQ,EAAE,QAAQ;gBAClBF,UAAU,EAAE;cACd;YACF;UAAE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACFpF,OAAA,CAACvB,UAAU;YAACgG,IAAI,EAAC,OAAO;YAACC,OAAO,EAAEwI,cAAe;YAAChJ,EAAE,EAAE;cAAE8B,EAAE,EAAE;YAAE,CAAE;YAAA5B,QAAA,eAC9DpE,OAAA,CAAChB,OAAO;cAAC8F,IAAI,EAAC,wBAAwB;cAACC,KAAK,EAAE,EAAG;cAACC,MAAM,EAAE,EAAG;cAACJ,KAAK,EAAC;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,gBAENpF,OAAA,CAAC/B,GAAG;UAACiG,EAAE,EAAE;YAAEsK,QAAQ,EAAE,CAAC;YAAElK,OAAO,EAAE,MAAM;YAAEmK,aAAa,EAAE,QAAQ;YAAExH,GAAG,EAAE;UAAI,CAAE;UAAA7C,QAAA,gBAC3EpE,OAAA,CAAC/B,GAAG;YAACiG,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAEwC,cAAc,EAAE,eAAe;cAAEhC,KAAK,EAAE;YAAO,CAAE;YAAAX,QAAA,gBACjGpE,OAAA,CAAC/B,GAAG;cAACiG,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAE0C,GAAG,EAAE,CAAC;gBAAEuH,QAAQ,EAAE;cAAE,CAAE;cAAApK,QAAA,eACtEpE,OAAA,CAAC9B,UAAU;gBACTwH,OAAO,EAAC,OAAO;gBACf1B,SAAS,EAAE5E,MAAM,CAAC+I,QAAS;gBAC3BjE,EAAE,EAAE;kBACF2B,UAAU,EAAE,kCAAkC;kBAC9CK,MAAM,EAAE,SAAS;kBACjB5B,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBK,KAAK,EAAEmI,WAAW,GAAG,SAAS,GAAG,SAAS;kBAC1CjH,UAAU,EAAEiH,WAAW,GAAG,GAAG,GAAG,GAAG;kBACnCyB,QAAQ,EAAE,CAAC;kBACX,SAAS,EAAE;oBACT,mBAAmB,EAAE;sBACnBrI,OAAO,EAAE;oBACX;kBACF;gBACF,CAAE;gBACFzB,OAAO,EAAEuI,mBAAoB;gBAAA7I,QAAA,GAC5B+D,QAAQ,eACTnI,OAAA,CAACtB,OAAO;kBAAC0H,KAAK,EAAC,gBAAgB;kBAAAhC,QAAA,eAC7BpE,OAAA,CAACvB,UAAU;oBACTgG,IAAI,EAAC,OAAO;oBACZT,SAAS,EAAC,gBAAgB;oBAC1BE,EAAE,EAAE;sBACF8B,EAAE,EAAE,GAAG;sBACPG,OAAO,EAAE,CAAC;sBACVE,UAAU,EAAE,cAAc;sBAC1BlC,OAAO,EAAE;oBACX,CAAE;oBAAAC,QAAA,eAEFpE,OAAA,CAAChB,OAAO;sBAAC8F,IAAI,EAAC,+BAA+B;sBAACC,KAAK,EAAE,EAAG;sBAACC,MAAM,EAAE,EAAG;sBAACJ,KAAK,EAAC;oBAAM;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAENpF,OAAA,CAAC/B,GAAG;cAACiG,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAE0C,GAAG,EAAE;cAAE,CAAE;cAAA7C,QAAA,GAExDmF,SAAS,CAAC8E,SAAS,IAAI9E,SAAS,CAAC8E,SAAS,CAACrL,MAAM,GAAG,CAAC,iBACpDhD,OAAA,CAAClB,WAAW;gBACV4P,GAAG,EAAE,CAAE;gBACPxK,EAAE,EAAE;kBACF,mBAAmB,EAAE;oBACnBa,KAAK,EAAE,EAAE;oBACTC,MAAM,EAAE,EAAE;oBACVe,QAAQ,EAAE,SAAS;oBACnBY,MAAM,EAAE;kBACV;gBACF,CAAE;gBAAAvC,QAAA,EAEDmF,SAAS,CAAC8E,SAAS,CAAChH,GAAG,CAAC,CAACsH,QAAQ,EAAEpH,KAAK,kBACvCvH,OAAA,CAACtB,OAAO;kBAEN0H,KAAK,EAAEuI,QAAQ,CAACvD,UAAU,IAAIuD,QAAQ,CAACtD,SAAS,GAC5C,GAAGsD,QAAQ,CAACvD,UAAU,IAAIuD,QAAQ,CAACtD,SAAS,EAAE,GAC9CsD,QAAQ,CAACL,KAAM;kBACnBM,KAAK;kBAAAxK,QAAA,eAELpE,OAAA,CAACnB,MAAM;oBACLgQ,GAAG,EAAEF,QAAQ,CAACrD,MAAO;oBACrBwD,GAAG,EAAEH,QAAQ,CAACvD,UAAU,IAAIuD,QAAQ,CAACL,KAAM;oBAC3CpK,EAAE,EAAE;sBACF6K,OAAO,EAAE9P;oBACX,CAAE;oBAAAmF,QAAA,EAEDuK,QAAQ,CAACvD,UAAU,GAChBuD,QAAQ,CAACvD,UAAU,CAAC4D,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAC3CN,QAAQ,CAACL,KAAK,GACZK,QAAQ,CAACL,KAAK,CAACU,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GACtC;kBAAE;oBAAAhK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBAAC,GAlBJuJ,QAAQ,CAACjL,EAAE;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAmBT,CACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CACd,eAGDpF,OAAA,CAAC/B,GAAG;gBACFiG,EAAE,EAAE;kBACFI,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBoC,MAAM,EAAE,gBAAgB;kBACxBJ,YAAY,EAAE,KAAK;kBACnBpC,OAAO,EAAE,SAAS;kBAClB+K,UAAU,EAAE;gBACd,CAAE;gBAAA9K,QAAA,gBAGFpE,OAAA,CAACtB,OAAO;kBAAC0H,KAAK,EAAEsH,WAAW,GAAG,eAAe,GAAG,cAAe;kBAAAtJ,QAAA,eAC7DpE,OAAA,CAACvB,UAAU;oBACTgG,IAAI,EAAC,OAAO;oBACZC,OAAO,EAAEmJ,uBAAwB;oBACjC3J,EAAE,EAAE;sBACFU,KAAK,EAAE8I,WAAW,GAAGzO,eAAe,GAAG,MAAM;sBAC7C,SAAS,EAAE;wBACT2F,KAAK,EAAE3F,eAAe;wBACtB8P,OAAO,EAAE;sBACX;oBACF,CAAE;oBAAA3K,QAAA,eAEFpE,OAAA,CAAChB,OAAO;sBACN8F,IAAI,EAAE4I,WAAW,GAAG,iCAAiC,GAAG,kCAAmC;sBAC3F3I,KAAK,EAAE,EAAG;sBACVC,MAAM,EAAE;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAGVpF,OAAA,CAACtB,OAAO;kBAAC0H,KAAK,EAAC,UAAU;kBAAAhC,QAAA,eACvBpE,OAAA,CAACvB,UAAU;oBACTgG,IAAI,EAAC,OAAO;oBACZC,OAAO,EAAE+E,kBAAmB;oBAC5B0F,QAAQ,EAAEpG,eAAgB;oBAC1B7E,EAAE,EAAE;sBACFU,KAAK,EAAE3F,eAAe;sBACtB,SAAS,EAAE;wBACT8P,OAAO,EAAE;sBACX;oBACF,CAAE;oBAAA3K,QAAA,EAED2E,eAAe,gBACd/I,OAAA,CAACpB,gBAAgB;sBAAC6F,IAAI,EAAE,EAAG;sBAACP,EAAE,EAAE;wBAAEU,KAAK,EAAE3F;sBAAgB;oBAAE;sBAAAgG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,gBAE9DpF,OAAA,CAAChB,OAAO;sBAAC8F,IAAI,EAAC,kCAAkC;sBAACC,KAAK,EAAE,EAAG;sBAACC,MAAM,EAAE;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAC1E;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACS;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAGVpF,OAAA,CAACtB,OAAO;kBAAC0H,KAAK,EAAC,gBAAgB;kBAAAhC,QAAA,eAC7BpE,OAAA,CAACvB,UAAU;oBACTgG,IAAI,EAAC,OAAO;oBACZC,OAAO,EAAEA,CAAA,KAAM4E,yBAAyB,CAAC,IAAI,CAAE;oBAC/CpF,EAAE,EAAE;sBACFU,KAAK,EAAE,EAAAsD,oBAAA,GAAAqB,SAAS,CAAC8E,SAAS,cAAAnG,oBAAA,uBAAnBA,oBAAA,CAAqBlF,MAAM,IAAG,CAAC,GAAG/D,eAAe,GAAG,MAAM;sBACjE,SAAS,EAAE;wBACT2F,KAAK,EAAE3F,eAAe;wBACtB8P,OAAO,EAAE;sBACX;oBACF,CAAE;oBAAA3K,QAAA,eAEFpE,OAAA,CAAChB,OAAO;sBACN8F,IAAI,EAAC,2BAA2B;sBAChCC,KAAK,EAAE,EAAG;sBACVC,MAAM,EAAE;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACZ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACQ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAGVpF,OAAA,CAACtB,OAAO;kBAAC0H,KAAK,EAAC,aAAa;kBAAAhC,QAAA,eAC1BpE,OAAA,CAACvB,UAAU;oBACTgG,IAAI,EAAC,OAAO;oBACZC,OAAO,EAAE0I,qBAAsB;oBAC/BlJ,EAAE,EAAE;sBACFU,KAAK,EAAE3F,eAAe;sBACtB,SAAS,EAAE;wBACT8P,OAAO,EAAE;sBACX;oBACF,CAAE;oBAAA3K,QAAA,eAEFpE,OAAA,CAAChB,OAAO;sBAAC8F,IAAI,EAAC,2BAA2B;sBAACC,KAAK,EAAE,EAAG;sBAACC,MAAM,EAAE;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzD;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAGVpF,OAAA,CAACtB,OAAO;kBAAC0H,KAAK,EAAC,WAAW;kBAAAhC,QAAA,eACxBpE,OAAA,CAACvB,UAAU;oBACTgG,IAAI,EAAC,OAAO;oBACZC,OAAO,EAAEuI,mBAAoB;oBAC7B/I,EAAE,EAAE;sBACFU,KAAK,EAAE,MAAM;sBACb,SAAS,EAAE;wBACTmK,OAAO,EAAE;sBACX;oBACF,CAAE;oBAAA3K,QAAA,eAEFpE,OAAA,CAAChB,OAAO;sBAAC8F,IAAI,EAAC,eAAe;sBAACC,KAAK,EAAE,EAAG;sBAACC,MAAM,EAAE;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7C;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC,eAGVpF,OAAA,CAACtB,OAAO;kBAAC0H,KAAK,EAAC,aAAa;kBAAAhC,QAAA,eAC1BpE,OAAA,CAACvB,UAAU;oBACTgG,IAAI,EAAC,OAAO;oBACZC,OAAO,EAAE2I,qBAAsB;oBAC/BnJ,EAAE,EAAE;sBACFU,KAAK,EAAE,SAAS;sBAChB,SAAS,EAAE;wBACTmK,OAAO,EAAE;sBACX;oBACF,CAAE;oBAAA3K,QAAA,eAEFpE,OAAA,CAAChB,OAAO;sBAAC8F,IAAI,EAAC,iCAAiC;sBAACC,KAAK,EAAE,EAAG;sBAACC,MAAM,EAAE;oBAAG;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,eAGNpF,OAAA,CAAC/B,GAAG;gBACFiG,EAAE,EAAE;kBACFI,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBwK,OAAO,EAAEnM,YAAY,CAACgC,KAAK,GAAG,IAAI;kBAClCwK,EAAE,EAAE,CAAC;kBACLC,EAAE,EAAE,GAAG;kBACP9I,YAAY,EAAE;gBAChB,CAAE;gBAAAnC,QAAA,eAEFpE,OAAA,CAAC9B,UAAU;kBACTwH,OAAO,EAAC,SAAS;kBACjBxB,EAAE,EAAE;oBACF4B,UAAU,EAAE,GAAG;oBACflB,KAAK,EAAEhC,YAAY,CAACgC,KAAK;oBACzBiB,UAAU,EAAE;kBACd,CAAE;kBAAAzB,QAAA,GAEDuI,YAAY,EAAC,GAChB;gBAAA;kBAAA1H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLsI,WAAW,iBACV1N,OAAA,CAAC/B,GAAG;YACFiG,EAAE,EAAE;cACFI,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBM,eAAe,EAAE,GAAG5F,eAAe,IAAI;cACvCsH,YAAY,EAAE,KAAK;cACnB8I,EAAE,EAAE,GAAG;cACPD,EAAE,EAAE,IAAI;cACRrK,KAAK,EAAE;YACT,CAAE;YAAAX,QAAA,gBAEFpE,OAAA,CAAChB,OAAO;cACN8F,IAAI,EAAC,iCAAiC;cACtCC,KAAK,EAAE,EAAG;cACVC,MAAM,EAAE,EAAG;cACXJ,KAAK,EAAE3F;YAAgB;cAAAgG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5BpF,OAAA,CAAC9B,UAAU;cACTwH,OAAO,EAAC,SAAS;cACjBxB,EAAE,EAAE;gBACF2B,UAAU,EAAE,kCAAkC;gBAC9CE,QAAQ,EAAE,SAAS;gBACnBnB,KAAK,EAAE,MAAM;gBACbkB,UAAU,EAAE,GAAG;gBACfe,UAAU,EAAE,CAAC;gBACbb,EAAE,EAAE;cACN,CAAE;cAAA5B,QAAA,GACDkL,UAAU,CAAC/F,SAAS,CAACoE,UAAU,CAAC,EAAC,KAAG,EAAC2B,UAAU,CAAC/F,SAAS,CAACqE,QAAQ,CAAC;YAAA;cAAA3I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAENpF,OAAA,CAAC/B,GAAG;QACFiG,EAAE,EAAE;UACFqL,EAAE,EAAE,CAAC;UACLC,EAAE,EAAE,CAAC;UACLC,EAAE,EAAE,GAAG;UACPC,EAAE,EAAE,GAAG;UACPC,UAAU,EAAE,cAAc/M,YAAY,CAACgC,KAAK,EAAE;UAC9CoB,EAAE,EAAE,GAAG;UACPS,EAAE,EAAE,GAAG;UACPnC,OAAO,EAAE,CAACuI,WAAW,IAAIvE,eAAe,KAAK3H,YAAY,GAAG,OAAO,GAAG;QACxE,CAAE;QAAAyD,QAAA,GAEDkD,IAAI,CAACwF,QAAQ,IAAIxF,IAAI,CAACwF,QAAQ,CAACzF,GAAG,CAAC,CAACuI,OAAO,EAAErI,KAAK,kBACjDvH,OAAA,CAAC6P,WAAW;UAEVD,OAAO,EAAEA,OAAQ;UACjBrI,KAAK,EAAEA,KAAM;UACbuI,aAAa,EAAExI,IAAI,CAACwF,QAAQ,CAAC9J,MAAO;UACpC+M,QAAQ,EAAEzI,IAAI,CAACkG,IAAK;UACpBxM,wBAAwB,EAAEA,wBAAyB;UACnDC,gBAAgB,EAAEA,gBAAiB;UACnCG,eAAe,EAAEA,eAAgB;UACjCI,eAAe,EAAEA,eAAgB;UACjCC,eAAe,EAAEA,eAAgB;UACjCC,YAAY,EAAEA,YAAa;UAC3BC,SAAS,EAAEA;QAAU,GAXhB4F,KAAK;UAAAtC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAYX,CACF,CAAC,EAGDkD,eAAe,iBACdtI,OAAA,CAAC/B,GAAG;UACFiG,EAAE,EAAE;YACFI,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpB8K,EAAE,EAAE,GAAG;YACPW,YAAY,EAAE,oBAAoB;YAClCnL,eAAe,EAAE,SAAS;YAC1B0B,YAAY,EAAE,KAAK;YACnB6I,EAAE,EAAE;UACN,CAAE;UAAAhL,QAAA,gBAEFpE,OAAA,CAACrB,QAAQ;YACPwQ,QAAQ;YACR1K,IAAI,EAAC,OAAO;YACZP,EAAE,EAAE;cACFwC,CAAC,EAAE,GAAG;cACN/B,EAAE,EAAE,GAAG;cACPC,KAAK,EAAE;YACT,CAAE;YACFE,IAAI,eAAE9E,OAAA,CAAChB,OAAO;cAAC8F,IAAI,EAAC,0CAA0C;cAACC,KAAK,EAAE,EAAG;cAACC,MAAM,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1F,CAAC,eAEFpF,OAAA,CAACxB,SAAS;YACR+D,QAAQ,EAAEmG,kBAAmB;YAC7BrD,KAAK,EAAEmD,cAAe;YACtBlD,QAAQ,EAAGjC,CAAC,IAAKoF,iBAAiB,CAACpF,CAAC,CAACkC,MAAM,CAACF,KAAK,CAAE;YACnDG,SAAS,EAAEiI,wBAAyB;YACpC5F,WAAW,EAAC,2BAA2B;YACvCnC,OAAO,EAAC,UAAU;YAClBC,SAAS;YACTC,SAAS;YACT1B,EAAE,EAAE;cACF2B,UAAU,EAAE,kCAAkC;cAC9C,uBAAuB,EAAE;gBACvBE,QAAQ,EAAE,SAAS;gBACnBF,UAAU,EAAE,kCAAkC;gBAC9C1B,OAAO,EAAE;cACX,CAAC;cACD,8BAA8B,EAAE;gBAC9B2D,iBAAiB,EAAE;cACrB;YACF;UAAE;YAAA7C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEFpF,OAAA,CAAC/B,GAAG;YAACiG,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAE0B,EAAE,EAAE;YAAE,CAAE;YAAA5B,QAAA,gBAClCpE,OAAA,CAACvB,UAAU;cAACgG,IAAI,EAAC,OAAO;cAACC,OAAO,EAAE4I,oBAAqB;cAACpJ,EAAE,EAAE;gBAAEwC,CAAC,EAAE;cAAI,CAAE;cAAAtC,QAAA,eACrEpE,OAAA,CAAChB,OAAO;gBAAC8F,IAAI,EAAC,wBAAwB;gBAACC,KAAK,EAAE,EAAG;gBAACC,MAAM,EAAE,EAAG;gBAACJ,KAAK,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC,eAEbpF,OAAA,CAACvB,UAAU;cAACgG,IAAI,EAAC,OAAO;cAACC,OAAO,EAAEA,CAAA,KAAM6D,kBAAkB,CAAC,KAAK,CAAE;cAACrE,EAAE,EAAE;gBAAEwC,CAAC,EAAE,GAAG;gBAAEV,EAAE,EAAE;cAAI,CAAE;cAAA5B,QAAA,eACzFpE,OAAA,CAAChB,OAAO;gBAAC8F,IAAI,EAAC,wBAAwB;gBAACC,KAAK,EAAE,EAAG;gBAACC,MAAM,EAAE,EAAG;gBAACJ,KAAK,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpF,OAAA,CAACX,aAAa;MACZ4Q,IAAI,EAAEtH,iBAAkB;MACxBuH,OAAO,EAAEA,CAAA,KAAMtH,oBAAoB,CAAC,KAAK,CAAE;MAC3CC,QAAQ,EAAEA,QAAS;MACnBsH,YAAY,EAAE3F,gBAAiB;MAC/B4F,eAAe,EAAEpE,mBAAoB;MACrCqE,eAAe,EAAE9D,mBAAoB;MACrC+D,OAAO,EAAEvH,eAAgB;MACzBwH,UAAU,EAAEjJ,IAAI,CAACtF,IAAK;MACtBwO,UAAU,EAAC;IAAM;MAAAvL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,eAGFpF,OAAA,CAACV,aAAa;MACZ2Q,IAAI,EAAEhH,iBAAkB;MACxBiH,OAAO,EAAEA,CAAA,KAAMhH,oBAAoB,CAAC,KAAK,CAAE;MAC3C5B,IAAI,EAAEiC,SAAU;MAChBkH,eAAe,EAAE3C,mBAAoB;MACrC4C,UAAU,EAAEvH;IAAgB;MAAAlE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC,eAGFpF,OAAA,CAACT,kBAAkB;MACjB0Q,IAAI,EAAE5G,sBAAuB;MAC7B6G,OAAO,EAAEA,CAAA,KAAM5G,yBAAyB,CAAC,KAAK,CAAE;MAChDhC,IAAI,EAAEiC,SAAU;MAChB7H,YAAY,EAAEA,YAAa;MAC3BC,SAAS,EAAEA,SAAU;MACrBF,eAAe,EAAEwM;IAAoB;MAAAhJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC;EAAA,eACF,CAAC;AAEP,CAAC;;AAED;AAAA6C,GAAA,CApxBMT,QAAQ;AAAAmJ,GAAA,GAARnJ,QAAQ;AAqxBd,MAAMqI,WAAW,GAAGe,KAAA,IAYd;EAAAC,GAAA;EAAA,IAZe;IACnBjB,OAAO;IACPrI,KAAK;IACLuI,aAAa;IACbC,QAAQ;IACR/O,wBAAwB;IACxBC,gBAAgB;IAChBG,eAAe;IACfI,eAAe;IACfC,eAAe;IACfC,YAAY;IACZC;EACF,CAAC,GAAAiP,KAAA;EACC,MAAM,CAAChP,SAAS,EAAEC,YAAY,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgT,WAAW,EAAEC,cAAc,CAAC,GAAGjT,QAAQ,CAAC8R,OAAO,CAAC5N,IAAI,CAAC;EAC5D,MAAM,CAACgP,SAAS,EAAEC,YAAY,CAAC,GAAGnT,QAAQ,CAAC8R,OAAO,CAACjM,MAAM,KAAKzE,MAAM,CAAC8N,SAAS,IAAI4C,OAAO,CAACnN,QAAQ,KAAK,GAAG,CAAC;EAC3G,MAAMyO,eAAe,GAAGnT,MAAM,CAAC,IAAI,CAAC;;EAEpC;EACA,MAAMoT,eAAe,GAAGnQ,wBAAwB,GAAGA,wBAAwB,CAAC4O,OAAO,CAAC,GAAG,CAAC;EACxF,MAAMwB,aAAa,GAAGnQ,gBAAgB,GAAGA,gBAAgB,CAAC2O,OAAO,CAAC,GAAIA,OAAO,CAACjM,MAAM,IAAIzE,MAAM,CAACyD,WAAY;EAC3G,MAAM0O,mBAAmB,GAAGlS,aAAa,CAACiS,aAAa,CAAC,IAAIjS,aAAa,CAACD,MAAM,CAACyD,WAAW,CAAC;;EAE7F;EACA3E,SAAS,CAAC,MAAM;IACdiT,YAAY,CAACrB,OAAO,CAACjM,MAAM,KAAKzE,MAAM,CAAC8N,SAAS,IAAI4C,OAAO,CAACnN,QAAQ,KAAK,GAAG,CAAC;EAC/E,CAAC,EAAE,CAACmN,OAAO,CAACjM,MAAM,EAAEiM,OAAO,CAACnN,QAAQ,CAAC,CAAC;;EAEtC;EACA,MAAM6O,sBAAsB,GAAGA,CAAA,KAAM;IACnCzP,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;;EAED;EACA,MAAM0P,iBAAiB,GAAGA,CAAA,KAAM;IAC9B;IACA,MAAMpO,WAAW,GAAG2N,WAAW,CAAC/N,IAAI,CAAC,CAAC;;IAEtC;IACA,IAAII,WAAW,KAAK,EAAE,IAAIA,WAAW,KAAKyM,OAAO,CAAC5N,IAAI,EAAE;MACtD,IAAIZ,eAAe,EAAE;QACnBA,eAAe,CAAC;UAAE,GAAGwO,OAAO;UAAE5N,IAAI,EAAEmB,WAAW;UAAEmE,IAAI,EAAEyI;QAAS,CAAC,CAAC;MACpE;IACF,CAAC,MAAM;MACL;MACAgB,cAAc,CAACnB,OAAO,CAAC5N,IAAI,CAAC;IAC9B;IAEAH,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;;EAED;EACA,MAAM2P,qBAAqB,GAAInO,CAAC,IAAK;IACnC,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,EAAE;MACrBiO,iBAAiB,CAAC,CAAC;IACrB,CAAC,MAAM,IAAIlO,CAAC,CAACC,GAAG,KAAK,QAAQ,EAAE;MAC7ByN,cAAc,CAACnB,OAAO,CAAC5N,IAAI,CAAC;MAC5BH,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAM4P,kBAAkB,GAAGA,CAAA,KAAM;IAC/B;IACA,MAAMC,eAAe,GAAG,CAACV,SAAS;IAClCC,YAAY,CAACS,eAAe,CAAC;IAE7B,MAAMC,SAAS,GAAGD,eAAe,GAAGxS,MAAM,CAAC8N,SAAS,GAAG9N,MAAM,CAACyD,WAAW;IACzE,MAAMiP,WAAW,GAAGF,eAAe,GAAG,GAAG,GAAG,CAAC;IAE7C,IAAItQ,eAAe,EAAE;MACnB;MACA,MAAMyQ,WAAW,GAAG;QAClB,GAAGjC,OAAO;QACVjM,MAAM,EAAEgO,SAAS;QACjBlP,QAAQ,EAAEmP,WAAW;QACrBtK,IAAI,EAAEyI;MACR,CAAC;MAEDhG,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE6H,WAAW,CAAC;MACnDzQ,eAAe,CAACyQ,WAAW,CAAC;IAC9B;EACF,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAIC,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;MACnExQ,eAAe,CAACoO,OAAO,EAAEG,QAAQ,CAAC;IACpC;EACF,CAAC;;EAED;EACA/R,SAAS,CAAC,MAAM;IACd,IAAI4D,SAAS,IAAIsP,eAAe,CAACrN,OAAO,EAAE;MACxCqN,eAAe,CAACrN,OAAO,CAACC,KAAK,CAAC,CAAC;IACjC;EACF,CAAC,EAAE,CAAClC,SAAS,CAAC,CAAC;EAEf,oBACE5B,OAAA,CAAC/B,GAAG;IACFiG,EAAE,EAAE;MACFI,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpB8K,EAAE,EAAE,GAAG;MACPW,YAAY,EAAEzI,KAAK,GAAGuI,aAAa,GAAG,CAAC,GAAG,oBAAoB,GAAG;IACnE,CAAE;IAAA1L,QAAA,gBAEFpE,OAAA,CAACrB,QAAQ;MACPsT,OAAO,EAAEjB,SAAU;MACnB1L,QAAQ,EAAEmM,kBAAmB;MAC7BhN,IAAI,EAAC,OAAO;MACZP,EAAE,EAAE;QACFwC,CAAC,EAAE,GAAG;QACN/B,EAAE,EAAE,GAAG;QACPC,KAAK,EAAE,SAAS;QAChB,eAAe,EAAE;UACfA,KAAK,EAAE;QACT;MACF,CAAE;MACFE,IAAI,eAAE9E,OAAA,CAAChB,OAAO;QAAC8F,IAAI,EAAC,0CAA0C;QAACC,KAAK,EAAE,EAAG;QAACC,MAAM,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MACzF8M,WAAW,eAAElS,OAAA,CAAChB,OAAO;QAAC8F,IAAI,EAAC,4BAA4B;QAACC,KAAK,EAAE,EAAG;QAACC,MAAM,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnF,CAAC,EAEDxD,SAAS,gBACR5B,OAAA,CAAC/B,GAAG;MAACiG,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEiK,QAAQ,EAAE;MAAE,CAAE;MAAApK,QAAA,gBAC9DpE,OAAA,CAACxB,SAAS;QACR+D,QAAQ,EAAE2O,eAAgB;QAC1B7L,KAAK,EAAEyL,WAAY;QACnBxL,QAAQ,EAAGjC,CAAC,IAAK0N,cAAc,CAAC1N,CAAC,CAACkC,MAAM,CAACF,KAAK,CAAE;QAChDG,SAAS,EAAEgM,qBAAsB;QACjC/L,MAAM,EAAE8L,iBAAkB;QAC1B7L,OAAO,EAAC,UAAU;QAClBC,SAAS;QACTC,SAAS;QACT1B,EAAE,EAAE;UACF2B,UAAU,EAAE,kCAAkC;UAC9C,uBAAuB,EAAE;YACvBE,QAAQ,EAAE,SAAS;YACnBF,UAAU,EAAE;UACd;QACF;MAAE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACFpF,OAAA,CAACvB,UAAU;QAACgG,IAAI,EAAC,OAAO;QAACC,OAAO,EAAE6M,iBAAkB;QAACrN,EAAE,EAAE;UAAE8B,EAAE,EAAE;QAAE,CAAE;QAAA5B,QAAA,eACjEpE,OAAA,CAAChB,OAAO;UAAC8F,IAAI,EAAC,wBAAwB;UAACC,KAAK,EAAE,EAAG;UAACC,MAAM,EAAE,EAAG;UAACJ,KAAK,EAAC;QAAS;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,gBAENpF,OAAA,CAAC9B,UAAU;MACTwH,OAAO,EAAC,OAAO;MACfxB,EAAE,EAAE;QACFsK,QAAQ,EAAE,CAAC;QACXzI,QAAQ,EAAE,SAAS;QACnBF,UAAU,EAAE,kCAAkC;QAC9CjB,KAAK,EAAEoM,SAAS,GAAG,SAAS,GAAG,MAAM;QACrClL,UAAU,EAAEkL,SAAS,GAAG,GAAG,GAAG,GAAG;QACjC9K,MAAM,EAAE,SAAS;QACjB5B,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpB,SAAS,EAAE;UACT,sBAAsB,EAAE;YACtB4B,OAAO,EAAE;UACX;QACF;MACF,CAAE;MACFzB,OAAO,EAAE4M,sBAAuB;MAAAlN,QAAA,GAE/B0M,WAAW,eACZ9Q,OAAA,CAACtB,OAAO;QAAC0H,KAAK,EAAC,mBAAmB;QAAAhC,QAAA,eAChCpE,OAAA,CAACvB,UAAU;UACTgG,IAAI,EAAC,OAAO;UACZT,SAAS,EAAC,mBAAmB;UAC7BE,EAAE,EAAE;YACF8B,EAAE,EAAE,GAAG;YACPG,OAAO,EAAE,CAAC;YACVE,UAAU,EAAE,cAAc;YAC1BlC,OAAO,EAAE;UACX,CAAE;UAAAC,QAAA,eAEFpE,OAAA,CAAChB,OAAO;YAAC8F,IAAI,EAAC,+BAA+B;YAACC,KAAK,EAAE,EAAG;YAACC,MAAM,EAAE,EAAG;YAACJ,KAAK,EAAC;UAAM;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CACb,eAEDpF,OAAA,CAAC/B,GAAG;MAACiG,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAH,QAAA,GAChD+M,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAKgB,SAAS,iBACxDnS,OAAA,CAAC9B,UAAU;QACTwH,OAAO,EAAC,SAAS;QACjBxB,EAAE,EAAE;UACF6B,QAAQ,EAAE,SAAS;UACnBnB,KAAK,EAAEyM,mBAAmB,CAACzM,KAAK;UAChCoB,EAAE,EAAE,CAAC;UACLrB,EAAE,EAAE,CAAC;UACLkB,UAAU,EAAE,kCAAkC;UAC9CC,UAAU,EAAE;QACd,CAAE;QAAA1B,QAAA,GAED+M,eAAe,EAAC,GACnB;MAAA;QAAAlM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CACb,eAGDpF,OAAA,CAACtB,OAAO;QAAC0H,KAAK,EAAC,gBAAgB;QAAAhC,QAAA,eAC7BpE,OAAA,CAACvB,UAAU;UACTgG,IAAI,EAAC,OAAO;UACZC,OAAO,EAAEoN,wBAAyB;UAClC5N,EAAE,EAAE;YACFwC,CAAC,EAAE,GAAG;YACN9B,KAAK,EAAE,SAAS;YAChB,SAAS,EAAE;cACTC,eAAe,EAAE;YACnB;UACF,CAAE;UAAAT,QAAA,eAEFpE,OAAA,CAAChB,OAAO;YAAC8F,IAAI,EAAC,iCAAiC;YAACC,KAAK,EAAE,EAAG;YAACC,MAAM,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAyL,GAAA,CA9NMhB,WAAW;AAAAuC,GAAA,GAAXvC,WAAW;AA+NjB,SAASP,UAAUA,CAAC+C,UAAU,EAAE;EAC9B,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;EAE1B,IAAI;IACF,MAAMC,IAAI,GAAG,IAAIrH,IAAI,CAACoH,UAAU,CAAC;IACjC,IAAIE,KAAK,CAACD,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE;;IAEpC;IACA,MAAMC,OAAO,GAAG;MAAE7G,KAAK,EAAE,OAAO;MAAED,GAAG,EAAE;IAAU,CAAC;IAClD,OAAO2G,IAAI,CAACI,kBAAkB,CAAC,OAAO,EAAED,OAAO,CAAC;EAClD,CAAC,CAAC,OAAOnI,KAAK,EAAE;IACd,OAAO,EAAE;EACX;AACF;AAEA,eAAenK,aAAa;AAAC,IAAA4H,EAAA,EAAA4I,GAAA,EAAAyB,GAAA;AAAAO,YAAA,CAAA5K,EAAA;AAAA4K,YAAA,CAAAhC,GAAA;AAAAgC,YAAA,CAAAP,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}