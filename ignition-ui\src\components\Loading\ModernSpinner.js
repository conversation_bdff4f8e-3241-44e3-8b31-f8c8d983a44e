import React from 'react';
import { Box } from '@mui/material';
import styles from './ModernSpinner.module.scss';

/**
 * Modern animated spinner component with multiple animation layers
 * @param {Object} props - Component props
 * @param {boolean} props.fromCreatePlan - Flag to determine if component is called from CreatePlan page
 * @param {string} props.size - Size of the spinner ('small', 'medium', 'large')
 * @param {string} props.variant - Spinner variant ('orbital', 'pulse', 'ripple', 'dots', 'galaxy')
 * @param {string} props.status - Current status for dynamic animations ('creating', 'processing', 'finalizing')
 * @returns {JSX.Element} - ModernSpinner component
 */
const ModernSpinner = ({
  fromCreatePlan = false,
  size = 'large',
  variant = 'orbital',
  status = 'creating'
}) => {
  
  const renderOrbitalSpinner = () => (
    <Box className={`${styles.spinnerContainer} ${styles[size]} ${styles[status]}`}>
      {/* Outer rotating ring */}
      <Box className={styles.outerRing}>
        <Box className={styles.outerOrb}></Box>
      </Box>

      {/* Middle rotating ring */}
      <Box className={styles.middleRing}>
        <Box className={styles.middleOrb}></Box>
      </Box>

      {/* Inner rotating ring */}
      <Box className={styles.innerRing}>
        <Box className={styles.innerOrb}></Box>
      </Box>

      {/* Center pulsing core */}
      <Box className={styles.centerCore}>
        <Box className={styles.coreInner}></Box>
      </Box>

      {/* Status indicator particles */}
      <Box className={styles.particle1}></Box>
      <Box className={styles.particle2}></Box>
      <Box className={styles.particle3}></Box>
    </Box>
  );

  const renderGalaxySpinner = () => (
    <Box className={`${styles.galaxyContainer} ${styles[size]}`}>
      {/* Galaxy arms */}
      <Box className={styles.galaxyArm1}></Box>
      <Box className={styles.galaxyArm2}></Box>
      <Box className={styles.galaxyArm3}></Box>

      {/* Central black hole */}
      <Box className={styles.galaxyCore}></Box>

      {/* Orbiting stars */}
      <Box className={styles.star1}></Box>
      <Box className={styles.star2}></Box>
      <Box className={styles.star3}></Box>
      <Box className={styles.star4}></Box>
    </Box>
  );

  const renderPulseSpinner = () => (
    <Box className={`${styles.pulseContainer} ${styles[size]}`}>
      <Box className={styles.pulseRing1}></Box>
      <Box className={styles.pulseRing2}></Box>
      <Box className={styles.pulseRing3}></Box>
      <Box className={styles.pulseCenter}></Box>
    </Box>
  );

  const renderRippleSpinner = () => (
    <Box className={`${styles.rippleContainer} ${styles[size]}`}>
      <Box className={styles.ripple}></Box>
      <Box className={styles.ripple}></Box>
      <Box className={styles.ripple}></Box>
    </Box>
  );

  const renderDotsSpinner = () => (
    <Box className={`${styles.dotsContainer} ${styles[size]}`}>
      <Box className={styles.dot1}></Box>
      <Box className={styles.dot2}></Box>
      <Box className={styles.dot3}></Box>
      <Box className={styles.dot4}></Box>
      <Box className={styles.dot5}></Box>
    </Box>
  );

  const renderSpinner = () => {
    switch (variant) {
      case 'pulse':
        return renderPulseSpinner();
      case 'ripple':
        return renderRippleSpinner();
      case 'dots':
        return renderDotsSpinner();
      case 'galaxy':
        return renderGalaxySpinner();
      case 'orbital':
      default:
        return renderOrbitalSpinner();
    }
  };

  return (
    <Box 
      className={styles.loadingWrapper}
      sx={{ 
        ...(!fromCreatePlan && { minHeight: '90vh' }),
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'column'
      }}
    >
      {renderSpinner()}
    </Box>
  );
};

export default ModernSpinner;
