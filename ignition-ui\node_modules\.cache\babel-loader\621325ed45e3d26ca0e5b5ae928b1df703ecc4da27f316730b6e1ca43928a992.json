{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getOptionUtilityClass(slot) {\n  return generateUtilityClass('MuiOption', slot);\n}\nconst optionClasses = generateUtilityClasses('MuiOption', ['root', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'focusVisible', 'disabled', 'selected', 'highlighted', 'variantPlain', 'variantSoft', 'variantOutlined', 'variantSolid']);\nexport default optionClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getOptionUtilityClass", "slot", "optionClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Option/optionClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getOptionUtilityClass(slot) {\n  return generateUtilityClass('MuiOption', slot);\n}\nconst optionClasses = generateUtilityClasses('MuiOption', ['root', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'focusVisible', 'disabled', 'selected', 'highlighted', 'variantPlain', 'variantSoft', 'variantOutlined', 'variantSolid']);\nexport default optionClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,qBAAqBA,CAACC,IAAI,EAAE;EAC1C,OAAOH,oBAAoB,CAAC,WAAW,EAAEG,IAAI,CAAC;AAChD;AACA,MAAMC,aAAa,GAAGH,sBAAsB,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,UAAU,EAAE,UAAU,EAAE,aAAa,EAAE,cAAc,EAAE,aAAa,EAAE,iBAAiB,EAAE,cAAc,CAAC,CAAC;AAC3R,eAAeG,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}