{"ast": null, "code": "'use client';\n\nexport { useTabsList } from './useTabsList';\nexport * from './useTabsList.types';\nexport * from './TabsListProvider';", "map": {"version": 3, "names": ["useTabsList"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/useTabsList/index.js"], "sourcesContent": ["'use client';\n\nexport { useTabsList } from './useTabsList';\nexport * from './useTabsList.types';\nexport * from './TabsListProvider';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,WAAW,QAAQ,eAAe;AAC3C,cAAc,qBAAqB;AACnC,cAAc,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}