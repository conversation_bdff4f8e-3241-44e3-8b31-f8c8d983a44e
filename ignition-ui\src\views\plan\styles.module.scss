.planDetailContainer {
  .noInviteUser {
    color: #333;
    font-size: 1rem;
    font-family: 'Recursive Variable';
    font-style: italic;
    margin-left: 5px;
  }

  .headerPlan {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;

    >h1 {
      font-family: 'Recursive Variable' !important;
      font-weight: bold !important;
      font-size: 3rem !important;
      margin-left: 10px;
      color: #333 !important;
      width: calc(100vw - 60px);
    }
  }

  .bodyPlan {
    display: flex;
    align-items: flex-start;

    >p {
      font-family: 'Recursive Variable' !important;
      font-weight: bold !important;
      font-size: 1.25rem !important;
      margin-left: 20px;
      color: #333 !important;
      width: 99%;
    }
  }

  .deleteActionBtn,
  .editActionBtn,
  .inviteActionBtn {
    display: flex;
    align-items: flex-end;
    font-size: 1.125rem;
    font-family: 'Recursive Variable';
    margin-top: 20px;
    color: red;
    font-weight: bold;
    text-transform: capitalize;
    border: 1px solid red;
    padding: 5px;
    height: 40px;


    &:hover {
      text-decoration: underline;
    }

    >span {
      margin-right: 5px;
    }
  }

  .editActionBtn,
  .inviteActionBtn {
    color: #333;
    margin-right: 10px;
    border: 1px solid #333;
    align-items: center;
  }

  .inviteActionBtn {
    margin-top: 10px;
    margin-right: 0;
    display: flex;
    justify-content: flex-end;
  }
}

.noteDialogContainer {
  padding: 24px;
}

.noteTitle {
  font-family: 'Recursive Variable' !important;
  font-weight: bold;
  margin-bottom: 24px;
  color: #333 !important;
}

.subtaskInputContainer {
  margin-top: 10px;
  margin-bottom: 10px;
}

.invitedUser {
  font-family: 'Recursive Variable' !important;
  color: #333 !important;

  >span {
    margin-left: 5px;
    padding-top: 2px;
  }
}

.mainCreateContainer {
  padding: 24px;
  min-height: calc(100vh - 65px);
  font-family: 'Recursive Variable', sans-serif;
  background-color: #f9f9f9;

  * {
    font-family: 'Recursive Variable', sans-serif;
  }

  .boxWrapper {
    display: flex;
    flex-direction: column;
    gap: 24px;
    max-width: 800px;
    margin: 24px auto;
    padding-bottom: 40px;
  }

  .paperContent {
    padding: 32px 40px;
    width: 100%;
    background-color: white;
    border-radius: 20px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    }
  }

  .formSection {
    padding: 0 10px;
  }

  .paperTitle {
    font-family: 'Recursive Variable';
    color: #333;
    margin-bottom: 16px;
    font-size: 2.4rem;
    font-weight: 700;
    line-height: 1.2;
    background: linear-gradient(45deg, #333 30%, #F0A500 90%);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    text-shadow: 0 2px 4px rgba(0,0,0,0.05);
  }

  .paperBodyContent {
    font-family: 'Recursive Variable';
    color: #555;
    font-size: 1.1rem;
    line-height: 1.6;
  }

  .boxInputContent {
    width: 100%;
    margin-bottom: 24px;

    &:last-child {
      margin-bottom: 0;
    }

    .titleInput {
      font-family: 'Recursive Variable';
      font-size: 1.25rem;
      font-weight: 600;
      color: #333;
      margin-bottom: 16px;
    }
  }

  .genPlanBtn {
    height: 60px;
    font-family: 'Recursive Variable';
    font-size: 1.2rem;
    font-weight: 700;
    color: white;
    background-color: #F0A500;
    border-radius: 16px;
    text-transform: none;
    margin-top: 24px;
    transition: all 0.3s ease;

    >span {
      margin-right: 12px;
      display: flex;
      align-items: center;
    }

    &:hover {
      background-color: darken(#F0A500, 10%);
      transform: translateY(-2px);
    }
  }

  .loadingBox {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    width: 100%;
    height: 100vh;
    background-color: rgba(255, 255, 255, 0.95);
    position: fixed;
    left: 0;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(5px);
  }

  .loadingContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 40px;
  }

  .spinnerContainer {
    width: 300px;
    height: 300px;
    display: flex;
    justify-content: center;
    align-items: center;
    order: 2;
    margin-top: 20px;
  }

  .loadingCard {
    text-align: center;
    max-width: 500px;
    padding: 32px;
    background-color: white;
    border-radius: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    order: 1;
  }

  .titleGenerating {
    font-family: 'Recursive Variable' !important;
    color: #333;
    font-size: 1.6rem;
    font-weight: 600;
    margin-bottom: 16px;
    text-align: center;
    animation: pulse 2s infinite ease-in-out;
  }

  .gotoDetailPageBtn,
  .reRunGenBtn {
    font-family: 'Recursive Variable';
    color: white;
    background-color: #F0A500;
    text-transform: none;
    margin: 0 8px;
    font-size: 1.1rem;
    font-weight: 600;
    padding: 12px 28px;
    border-radius: 12px;
    min-width: 180px;
    transition: all 0.3s ease;

    &:hover {
      background-color: darken(#F0A500, 10%);
      transform: translateY(-2px);
    }
  }

  .reRunGenBtn {
    background-color: #333;

    &:hover {
      background-color: #4f4f4f;
    }
  }
}

.commonStyle {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.invitationWrapper {
  width: 700px;
  height: 90vh;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  margin: -80px auto 0;
  font-family: 'Recursive Variable';

  .titleInvitation {
    font-family: 'Recursive Variable';
    font-size: 1.5rem;
    color: #333;
    padding-top: 10px;
  }

  .applyBtn,
  .rejectBtn {
    font-family: 'Recursive Variable';
    font-weight: bold;
    background-color: #333;
    color: white;
    border-radius: 5px;
    margin-top: 20px;

    &:hover {
      background-color: #333;
    }
  }

  .rejectBtn {
    background-color: red;
    margin-left: 5px;

    &:hover {
      background-color: red;
    }
  }
}

.addBtnBase {
  font-family: 'Recursive Variable' !important;
  font-size: 1.125rem !important;
  background-color: #333 !important;
  color: #F0A500 !important;
  font-weight: bold;

  &:hover {
    background-color: #333 !important;
    color: #F0A500;
  }

  >span {
    margin-right: 5px;
  }
}

.mainPage {
  padding: 24px;
  min-height: calc(100vh - 65px);
}

.noDataPlanMain {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;

  .title {
    font-family: 'Recursive Variable';
    margin-top: 20px;
    color: #333;
    font-size: 1.4rem;
  }

  .subTitle {
    font-family: 'Recursive Variable';
    margin-top: 10px;
    color: #333;
    font-size: 1.125rem;
  }
}


.invitedPlanLabel {
  font-family: 'Recursive Variable';
  font-size: 1.125rem;
  font-style: italic;
  background-color: #FF6347;
  color: white;
  padding: 5px 10px;
  border-radius: 5px;
}

@keyframes loadingProgress {
  0% {
    left: -30%;
    width: 30%;
  }
  50% {
    width: 30%;
  }
  70% {
    width: 70%;
  }
  100% {
    left: 100%;
    width: 30%;
  }
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}