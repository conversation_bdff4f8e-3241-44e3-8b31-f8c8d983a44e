{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { unstable_useId as useId } from '@mui/utils';\nimport { ListContext } from '../useList';\n\n/**\n * Stabilizes the ListContext value for the MenuItem component, so it doesn't change when sibling items update.\n *\n * Demos:\n *\n * - [Menu](https://mui.com/base-ui/react-menu/#hooks)\n *\n * API:\n *\n * - [useMenuItemContextStabilizer API](https://mui.com/base-ui/react-menu/hooks-api/#use-menu-item-context-stabilizer)\n *\n * @param id - The id of the MenuItem. If undefined, it will be generated with useId.\n * @returns The stable ListContext value and the id of the MenuItem.\n */\nexport function useMenuItemContextStabilizer(id) {\n  const listContext = React.useContext(ListContext);\n  if (!listContext) {\n    throw new Error('MenuItem: ListContext was not found.');\n  }\n  const itemId = useId(id);\n  const {\n    getItemState,\n    dispatch\n  } = listContext;\n  let itemState;\n  if (itemId != null) {\n    itemState = getItemState(itemId);\n  } else {\n    itemState = {\n      focusable: true,\n      highlighted: false,\n      selected: false\n    };\n  }\n  const {\n    highlighted,\n    selected,\n    focusable\n  } = itemState;\n\n  // The local version of getItemState can be only called with the current Option's value.\n  // It doesn't make much sense to render an Option depending on other Options' state anyway.\n  const localGetItemState = React.useCallback(itemValue => {\n    if (itemValue !== itemId) {\n      throw new Error(['Base UI MenuItem: Tried to access the state of another MenuItem.', `itemValue: ${itemValue} | id: ${itemId}`, 'This is unsupported when the MenuItem uses the MenuItemContextStabilizer as a performance optimization.'].join('/n'));\n    }\n    return {\n      highlighted,\n      selected,\n      focusable\n    };\n  }, [highlighted, selected, focusable, itemId]);\n\n  // Create a local (per MenuItem) instance of the ListContext that changes only when\n  // the getItemState's return value changes.\n  // This makes MenuItems re-render only when their state actually change, not when any MenuItem's state changes.\n  const localContextValue = React.useMemo(() => ({\n    dispatch,\n    getItemState: localGetItemState\n  }), [dispatch, localGetItemState]);\n  return {\n    contextValue: localContextValue,\n    id: itemId\n  };\n}", "map": {"version": 3, "names": ["React", "unstable_useId", "useId", "ListContext", "useMenuItemContextStabilizer", "id", "listContext", "useContext", "Error", "itemId", "getItemState", "dispatch", "itemState", "focusable", "highlighted", "selected", "localGetItemState", "useCallback", "itemValue", "join", "localContextValue", "useMemo", "contextValue"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/useMenuItem/useMenuItemContextStabilizer.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { unstable_useId as useId } from '@mui/utils';\nimport { ListContext } from '../useList';\n\n/**\n * Stabilizes the ListContext value for the MenuItem component, so it doesn't change when sibling items update.\n *\n * Demos:\n *\n * - [Menu](https://mui.com/base-ui/react-menu/#hooks)\n *\n * API:\n *\n * - [useMenuItemContextStabilizer API](https://mui.com/base-ui/react-menu/hooks-api/#use-menu-item-context-stabilizer)\n *\n * @param id - The id of the MenuItem. If undefined, it will be generated with useId.\n * @returns The stable ListContext value and the id of the MenuItem.\n */\nexport function useMenuItemContextStabilizer(id) {\n  const listContext = React.useContext(ListContext);\n  if (!listContext) {\n    throw new Error('MenuItem: ListContext was not found.');\n  }\n  const itemId = useId(id);\n  const {\n    getItemState,\n    dispatch\n  } = listContext;\n  let itemState;\n  if (itemId != null) {\n    itemState = getItemState(itemId);\n  } else {\n    itemState = {\n      focusable: true,\n      highlighted: false,\n      selected: false\n    };\n  }\n  const {\n    highlighted,\n    selected,\n    focusable\n  } = itemState;\n\n  // The local version of getItemState can be only called with the current Option's value.\n  // It doesn't make much sense to render an Option depending on other Options' state anyway.\n  const localGetItemState = React.useCallback(itemValue => {\n    if (itemValue !== itemId) {\n      throw new Error(['Base UI MenuItem: Tried to access the state of another MenuItem.', `itemValue: ${itemValue} | id: ${itemId}`, 'This is unsupported when the MenuItem uses the MenuItemContextStabilizer as a performance optimization.'].join('/n'));\n    }\n    return {\n      highlighted,\n      selected,\n      focusable\n    };\n  }, [highlighted, selected, focusable, itemId]);\n\n  // Create a local (per MenuItem) instance of the ListContext that changes only when\n  // the getItemState's return value changes.\n  // This makes MenuItems re-render only when their state actually change, not when any MenuItem's state changes.\n  const localContextValue = React.useMemo(() => ({\n    dispatch,\n    getItemState: localGetItemState\n  }), [dispatch, localGetItemState]);\n  return {\n    contextValue: localContextValue,\n    id: itemId\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,IAAIC,KAAK,QAAQ,YAAY;AACpD,SAASC,WAAW,QAAQ,YAAY;;AAExC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,4BAA4BA,CAACC,EAAE,EAAE;EAC/C,MAAMC,WAAW,GAAGN,KAAK,CAACO,UAAU,CAACJ,WAAW,CAAC;EACjD,IAAI,CAACG,WAAW,EAAE;IAChB,MAAM,IAAIE,KAAK,CAAC,sCAAsC,CAAC;EACzD;EACA,MAAMC,MAAM,GAAGP,KAAK,CAACG,EAAE,CAAC;EACxB,MAAM;IACJK,YAAY;IACZC;EACF,CAAC,GAAGL,WAAW;EACf,IAAIM,SAAS;EACb,IAAIH,MAAM,IAAI,IAAI,EAAE;IAClBG,SAAS,GAAGF,YAAY,CAACD,MAAM,CAAC;EAClC,CAAC,MAAM;IACLG,SAAS,GAAG;MACVC,SAAS,EAAE,IAAI;MACfC,WAAW,EAAE,KAAK;MAClBC,QAAQ,EAAE;IACZ,CAAC;EACH;EACA,MAAM;IACJD,WAAW;IACXC,QAAQ;IACRF;EACF,CAAC,GAAGD,SAAS;;EAEb;EACA;EACA,MAAMI,iBAAiB,GAAGhB,KAAK,CAACiB,WAAW,CAACC,SAAS,IAAI;IACvD,IAAIA,SAAS,KAAKT,MAAM,EAAE;MACxB,MAAM,IAAID,KAAK,CAAC,CAAC,kEAAkE,EAAE,cAAcU,SAAS,UAAUT,MAAM,EAAE,EAAE,yGAAyG,CAAC,CAACU,IAAI,CAAC,IAAI,CAAC,CAAC;IACxP;IACA,OAAO;MACLL,WAAW;MACXC,QAAQ;MACRF;IACF,CAAC;EACH,CAAC,EAAE,CAACC,WAAW,EAAEC,QAAQ,EAAEF,SAAS,EAAEJ,MAAM,CAAC,CAAC;;EAE9C;EACA;EACA;EACA,MAAMW,iBAAiB,GAAGpB,KAAK,CAACqB,OAAO,CAAC,OAAO;IAC7CV,QAAQ;IACRD,YAAY,EAAEM;EAChB,CAAC,CAAC,EAAE,CAACL,QAAQ,EAAEK,iBAAiB,CAAC,CAAC;EAClC,OAAO;IACLM,YAAY,EAAEF,iBAAiB;IAC/Bf,EAAE,EAAEI;EACN,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}