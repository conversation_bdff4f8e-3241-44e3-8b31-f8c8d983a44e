{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getTabListUtilityClass(slot) {\n  return generateUtilityClass('MuiTabList', slot);\n}\nconst tabListClasses = generateUtilityClasses('MuiTabList', ['root', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid', 'sizeSm', 'sizeMd', 'sizeLg']);\nexport default tabListClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getTabListUtilityClass", "slot", "tabListClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/TabList/tabListClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getTabListUtilityClass(slot) {\n  return generateUtilityClass('MuiTabList', slot);\n}\nconst tabListClasses = generateUtilityClasses('MuiTabList', ['root', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid', 'sizeSm', 'sizeMd', 'sizeLg']);\nexport default tabListClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,sBAAsBA,CAACC,IAAI,EAAE;EAC3C,OAAOH,oBAAoB,CAAC,YAAY,EAAEG,IAAI,CAAC;AACjD;AACA,MAAMC,cAAc,GAAGH,sBAAsB,CAAC,YAAY,EAAE,CAAC,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,iBAAiB,EAAE,aAAa,EAAE,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;AACpQ,eAAeG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}