import axios from 'axios';
import { useState } from "react";
import {
  InputAdornment,
  Button,
  Typography,
  Box,
  Container,
  Card,
  CardContent,
  FormControl,
  CircularProgress
} from "@mui/material";
import { iconPrimaryColor, APIURL, mainYellowColor } from "helpers/constants";
import Iconify from 'components/Iconify/index';
import InputBase from 'components/Input/InputBase';
import styles from './styles.module.scss';


const ForgotPassword = () => {
  const [email, setEmail] = useState('');
  const [errors, setErrors] = useState({});
  const [isSuccess, setIsSuccess] = useState(false);
  const [loading, setLoading] = useState(false);

  const handleChange = (keyword, value) => {
    if (typeof keyword === 'string') {
      setEmail(value);
    } else {
      setEmail(keyword);
    }
  };

  const handleResetPassword = async () => {
    if (!email) {
      setErrors({ email: ["This field is required!"] });
      return;
    }

    setLoading(true);
    try {
      await axios.post(`${APIURL}/api/user/forgot-password`, { email },
        { headers: { 'Content-Type': 'application/json' } }
      );
      setIsSuccess(true);
    } catch (error) {
      console.error("error:: ", error);
      setErrors(error.response?.data || {});
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (event) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      handleResetPassword();
    }
  };

  return (
    <Container maxWidth="md" className={styles.loginContainer}>
      <Box sx={{ width: '100%', maxWidth: { md: '66.66%' }, mx: 'auto' }}>
        {!isSuccess ? (
          <Card className={styles.loginCard}>
            <Box className={styles.cardHeader}>
              <Box className={styles.headerContent}>
                <Iconify icon="mdi:fire" width={60} height={60} className={styles.logo} />
                <Box className={styles.headerTextContainer}>
                  <Typography variant="h4" className={styles.headerTitle}>
                    Ignition
                  </Typography>
                  <Typography variant="body1" className={styles.headerSubtitle}>
                    Forgot Password
                  </Typography>
                </Box>
              </Box>
            </Box>

            <CardContent sx={{ px: { xs: 3, lg: 5 }, pb: { xs: 3, lg: 5 } }}>
              <div className={styles.divider}>
                <span>Enter your email to receive a password reset link</span>
              </div>

              <Box component="form" onKeyPress={handleKeyPress} noValidate>
                <FormControl className={styles.formGroup} fullWidth>
                  <div className={styles.inputField}>
                    <InputBase
                      type="email"
                      handleChange={handleChange}
                      errorText={errors.email}
                      placeholder="Email"
                      value={email}
                      keyword="email"
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <Iconify icon="ic:baseline-email" width={24} color={iconPrimaryColor} />
                          </InputAdornment>
                        ),
                      }}
                    />
                  </div>
                  {errors.email && <div className={styles.errorMessage}>{errors.email[0]}</div>}
                </FormControl>

                <Box display="flex" justifyContent="center" mt={4}>
                  <Button 
                    onClick={handleResetPassword} 
                    variant="contained" 
                    className={styles.submitBtn}
                    disabled={loading}
                  >
                    {loading ? <CircularProgress size={24} color="inherit" /> : 'Reset Password'}
                  </Button>
                </Box>
              </Box>
            </CardContent>
          </Card>
        ) : (
          <Card className={styles.loginCard}>
            <Box className={styles.cardHeader}>
              <Box className={styles.headerContent}>
                <Iconify icon="mdi:fire" width={60} height={60} className={styles.logo} />
                <Box className={styles.headerTextContainer}>
                  <Typography variant="h4" className={styles.headerTitle}>
                    Ignition
                  </Typography>
                  <Typography variant="body1" className={styles.headerSubtitle}>
                    Password Reset Sent
                  </Typography>
                </Box>
              </Box>
            </Box>
            <CardContent sx={{
              pb: { xs: 3, lg: 5 },
              px: { xs: 5, lg: 7 },
              textAlign: 'center'
            }}>
              <Box sx={{ position: 'relative', zIndex: 1 }}>
                <Box sx={{ mb: 5, mt: 2 }}>
                  <Box
                    className={styles.successIconContainer}
                    sx={{
                      mb: 3,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      width: '100px',
                      height: '100px',
                      borderRadius: '50%',
                      backgroundColor: '#4caf50',
                      border: '2px solid #4caf50',
                      boxShadow: '0 0 15px rgba(76, 175, 80, 0.5)',
                      margin: '0 auto'
                    }}
                  >
                    <Iconify
                      icon="mdi:email-check"
                      width={60}
                      height={60}
                      className={styles.successIcon}
                      sx={{
                        color: '#ffffff',
                      }}
                    />
                  </Box>
                </Box>
                <Typography
                  variant="h5"
                  className={styles.successTitle}
                  sx={{
                    mb: 3,
                    fontWeight: 'bold',
                    color: mainYellowColor,
                    fontFamily: "'Recursive Variable', sans-serif"
                  }}
                >
                  Success message
                </Typography>
                <Typography
                  variant="body1"
                  className={styles.successMessage}
                  sx={{
                    fontSize: '1.25rem',
                    color: mainYellowColor,
                    maxWidth: '100%',
                    mx: 'auto',
                    lineHeight: 1.6,
                    fontFamily: "'Recursive Variable', sans-serif"
                  }}
                >
                  Reset password link has been sent to your email.
                </Typography>
              </Box>
            </CardContent>
          </Card>
        )}

        <Box className={styles.linksContainer} sx={{ justifyContent: 'center' }}>
          <a href="/login" className={styles.customeALink}>
            <Iconify icon="mdi:login" width={20} height={20} className={styles.linkIcon} />
            Already have an account? Sign in
          </a>
        </Box>
      </Box>
    </Container>
  );
};

export default ForgotPassword;
