{"ast": null, "code": "var _jsxFileName = \"C:\\\\ignition\\\\ignition-ui\\\\src\\\\views\\\\plan\\\\detail\\\\components\\\\MilestoneCard.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Box, Typography, Paper, Chip, LinearProgress, Button, Divider, TextField, IconButton, Tooltip, Checkbox, CircularProgress, Avatar, AvatarGroup, Collapse } from '@mui/material';\nimport Iconify from 'components/Iconify/index';\nimport { mainYellowColor } from \"helpers/constants\";\nimport { STATUS, STATUS_CONFIG } from '../hooks/usePlanData';\nimport styles from '../styles.module.scss';\nimport CommentDialog from '../dialogs/CommentDialog';\nimport DueDateDialog from '../dialogs/DueDateDialog';\nimport AssignMemberDialog from '../dialogs/AssignMemberDialog';\nimport { getComments, addComment, updateComment, deleteComment, updateTask, assignMembersToTask } from '../../services';\nimport { toast } from 'react-toastify';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst MilestoneCard = _ref => {\n  _s();\n  var _milestone$tasks, _milestone$tasks2, _milestone$tasks3;\n  let {\n    milestone,\n    compact = false,\n    showSubtasks = false,\n    calculateMilestoneProgress,\n    getMilestoneStatus,\n    calculateTaskProgress,\n    getTaskStatus,\n    calculateSubtaskProgress,\n    getSubtaskStatus,\n    onUpdateMilestone,\n    onUpdateTask,\n    onUpdateSubtask,\n    onAddTask,\n    onAddSubtask,\n    onDeleteTask,\n    onDeleteSubtask,\n    onAssignMembers,\n    invitedUsers,\n    planOwner\n  } = _ref;\n  const [isEditing, setIsEditing] = useState(false);\n  const [milestoneName, setMilestoneName] = useState(milestone.name);\n  const [isAddingTask, setIsAddingTask] = useState(false);\n  const [newTaskName, setNewTaskName] = useState('');\n  // Initialize collapse state from localStorage\n  const [isExpanded, setIsExpanded] = useState(() => {\n    const savedState = localStorage.getItem(`milestone_${milestone.id || milestone.name}_expanded`);\n    return savedState !== null ? JSON.parse(savedState) : true; // Default to expanded\n  });\n  const inputRef = useRef(null);\n  const newTaskInputRef = useRef(null);\n\n  // Handle expand/collapse toggle with state persistence\n  const handleToggleExpanded = () => {\n    const newExpandedState = !isExpanded;\n    setIsExpanded(newExpandedState);\n    // Save state to localStorage\n    localStorage.setItem(`milestone_${milestone.id || milestone.name}_expanded`, JSON.stringify(newExpandedState));\n  };\n\n  // Calculate milestone progress\n  const progress = calculateMilestoneProgress ? calculateMilestoneProgress(milestone) : 0;\n\n  // Determine status based on progress\n  const milestoneStatus = getMilestoneStatus ? getMilestoneStatus(milestone) : STATUS.NOT_STARTED;\n  const statusConfig = STATUS_CONFIG[milestoneStatus] || STATUS_CONFIG[STATUS.NOT_STARTED];\n\n  // Check if description exists and is not empty\n  const hasDescription = milestone.description && milestone.description.trim().length > 0;\n\n  // Handle edit mode\n  const handleEditClick = () => {\n    setIsEditing(true);\n  };\n\n  // Handle save changes\n  const handleSave = () => {\n    // Trim the name to remove leading/trailing whitespace\n    const trimmedName = milestoneName.trim();\n\n    // Only update if the name has actually changed (after trimming)\n    if (trimmedName !== '' && trimmedName !== milestone.name) {\n      if (onUpdateMilestone) {\n        onUpdateMilestone({\n          ...milestone,\n          name: trimmedName\n        });\n      }\n    } else {\n      // Reset to original if empty or unchanged\n      setMilestoneName(milestone.name);\n    }\n    setIsEditing(false);\n  };\n\n  // Handle key press events\n  const handleKeyPress = e => {\n    if (e.key === 'Enter') {\n      handleSave();\n    } else if (e.key === 'Escape') {\n      setMilestoneName(milestone.name);\n      setIsEditing(false);\n    }\n  };\n\n  // Handle add task\n  const handleAddTaskClick = () => {\n    setIsAddingTask(true);\n  };\n\n  // Handle save new task\n  const handleSaveNewTask = () => {\n    const trimmedName = newTaskName.trim();\n    if (trimmedName && onAddTask) {\n      const newTask = {\n        name: trimmedName,\n        milestone: milestone.id,\n        status: STATUS.NOT_STARTED,\n        progress: 0\n      };\n      onAddTask(newTask);\n      setNewTaskName('');\n    }\n    setIsAddingTask(false);\n  };\n\n  // Handle key press events for new task\n  const handleNewTaskKeyPress = e => {\n    if (e.key === 'Enter') {\n      handleSaveNewTask();\n    } else if (e.key === 'Escape') {\n      setNewTaskName('');\n      setIsAddingTask(false);\n    }\n  };\n\n  // Focus input when editing starts\n  useEffect(() => {\n    if (isEditing && inputRef.current) {\n      inputRef.current.focus();\n    }\n  }, [isEditing]);\n\n  // Focus input when adding task\n  useEffect(() => {\n    if (isAddingTask && newTaskInputRef.current) {\n      newTaskInputRef.current.focus();\n    }\n  }, [isAddingTask]);\n  return /*#__PURE__*/_jsxDEV(Paper, {\n    elevation: 0,\n    className: styles.milestoneCard,\n    sx: {\n      padding: '16px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      className: styles.milestoneHeader,\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          flex: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          onClick: handleToggleExpanded,\n          sx: {\n            mr: 1,\n            color: '#666',\n            '&:hover': {\n              backgroundColor: 'rgba(0, 0, 0, 0.04)'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(Iconify, {\n            icon: isExpanded ? \"material-symbols:expand-less\" : \"material-symbols:expand-more\",\n            width: 20,\n            height: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this), isEditing ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"material-symbols:flag\",\n            width: 20,\n            height: 20,\n            color: mainYellowColor,\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            inputRef: inputRef,\n            value: milestoneName,\n            onChange: e => setMilestoneName(e.target.value),\n            onKeyDown: handleKeyPress,\n            onBlur: handleSave,\n            variant: \"standard\",\n            fullWidth: true,\n            autoFocus: true,\n            sx: {\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              fontWeight: 600,\n              fontSize: '1.1rem',\n              '& .MuiInputBase-input': {\n                fontWeight: 600,\n                fontSize: '1.1rem',\n                fontFamily: '\"Recursive Variable\", sans-serif'\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            onClick: handleSave,\n            sx: {\n              ml: 1\n            },\n            children: /*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"material-symbols:check\",\n              width: 20,\n              height: 20,\n              color: \"#4CAF50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 186,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          className: styles.milestoneTitle,\n          sx: {\n            fontFamily: '\"Recursive Variable\", sans-serif',\n            display: 'flex',\n            alignItems: 'center',\n            cursor: 'pointer',\n            flex: 1,\n            '&:hover': {\n              '& .edit-icon': {\n                opacity: 1\n              }\n            }\n          },\n          onClick: handleEditClick,\n          children: [/*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"material-symbols:flag\",\n            width: 20,\n            height: 20,\n            color: mainYellowColor\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 15\n          }, this), milestoneName, /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: \"Edit milestone name\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              className: \"edit-icon\",\n              sx: {\n                ml: 1,\n                opacity: 0,\n                transition: 'opacity 0.2s',\n                padding: '2px'\n              },\n              children: /*#__PURE__*/_jsxDEV(Iconify, {\n                icon: \"material-symbols:edit-outline\",\n                width: 16,\n                height: 16,\n                color: \"#666\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 233,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 232,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 165,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Chip, {\n        icon: /*#__PURE__*/_jsxDEV(Iconify, {\n          icon: statusConfig.icon,\n          width: 16,\n          height: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 17\n        }, this),\n        label: statusConfig.label,\n        size: \"small\",\n        sx: {\n          backgroundColor: `${statusConfig.color}20`,\n          color: statusConfig.color,\n          fontWeight: 600,\n          borderRadius: '4px',\n          fontFamily: '\"Recursive Variable\", sans-serif'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 250,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 164,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n      in: isExpanded,\n      timeout: \"auto\",\n      unmountOnExit: true,\n      children: [!compact && hasDescription && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 2,\n          mt: 1.5\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            backgroundColor: '#f9f9f9',\n            p: 1.5,\n            borderRadius: '8px',\n            border: '1px solid #f0f0f0'\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              color: '#333',\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              whiteSpace: 'pre-line',\n              lineHeight: 1.6,\n              fontWeight: 500\n            },\n            children: milestone.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 276,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 268,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 2,\n          opacity: 0.6\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            mb: 0.5\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 600,\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              color: '#333'\n            },\n            children: \"Progress\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontWeight: 700,\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              color: '#333'\n            },\n            children: [progress, \"%\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(LinearProgress, {\n          variant: \"determinate\",\n          value: progress,\n          sx: {\n            height: 6,\n            borderRadius: 3,\n            backgroundColor: '#f0f0f0',\n            '& .MuiLinearProgress-bar': {\n              backgroundColor: statusConfig.color\n            }\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          justifyContent: 'space-between',\n          alignItems: 'center',\n          mb: 1\n        },\n        children: milestone.estimated_duration && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"material-symbols:timer\",\n            width: 16,\n            height: 16,\n            color: \"#333\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            sx: {\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              color: '#333',\n              fontWeight: 500\n            },\n            children: milestone.estimated_duration\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 319,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 317,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mb: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between',\n            mb: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              gap: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"material-symbols:checklist\",\n              width: 16,\n              height: 16,\n              sx: {\n                color: '#333'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 339,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              sx: {\n                fontWeight: 600,\n                color: '#333',\n                fontFamily: '\"Recursive Variable\", sans-serif',\n                fontSize: '0.9rem'\n              },\n              children: \"Tasks\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 338,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: \"Add new task\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: handleAddTaskClick,\n              sx: {\n                color: mainYellowColor,\n                '&:hover': {\n                  backgroundColor: `${mainYellowColor}10`\n                }\n              },\n              children: /*#__PURE__*/_jsxDEV(Iconify, {\n                icon: \"material-symbols:add-task\",\n                width: 18,\n                height: 18\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 370,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 330,\n          columnNumber: 9\n        }, this), compact ? /*#__PURE__*/_jsxDEV(Box, {\n          className: styles.taskList,\n          children: [(_milestone$tasks = milestone.tasks) === null || _milestone$tasks === void 0 ? void 0 : _milestone$tasks.slice(0, 2).map((task, index) => /*#__PURE__*/_jsxDEV(TaskItem, {\n            task: task,\n            showSubtasks: showSubtasks,\n            compact: true,\n            calculateTaskProgress: calculateTaskProgress,\n            getTaskStatus: getTaskStatus,\n            calculateSubtaskProgress: calculateSubtaskProgress,\n            getSubtaskStatus: getSubtaskStatus,\n            onUpdateTask: onUpdateTask,\n            onUpdateSubtask: onUpdateSubtask,\n            onAddSubtask: onAddSubtask,\n            onDeleteTask: onDeleteTask,\n            onDeleteSubtask: onDeleteSubtask,\n            onAssignMembers: onAssignMembers,\n            invitedUsers: invitedUsers,\n            planOwner: planOwner\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 15\n          }, this)), ((_milestone$tasks2 = milestone.tasks) === null || _milestone$tasks2 === void 0 ? void 0 : _milestone$tasks2.length) > 2 && /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"text\",\n            size: \"small\",\n            sx: {\n              color: mainYellowColor,\n              fontWeight: 600,\n              textTransform: 'none',\n              p: 0,\n              mt: 1,\n              fontFamily: '\"Recursive Variable\", sans-serif'\n            },\n            children: [\"+ \", milestone.tasks.length - 2, \" more tasks\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 399,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 376,\n          columnNumber: 11\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          className: styles.taskList,\n          children: [(_milestone$tasks3 = milestone.tasks) === null || _milestone$tasks3 === void 0 ? void 0 : _milestone$tasks3.map((task, index) => /*#__PURE__*/_jsxDEV(TaskItem, {\n            task: task,\n            showSubtasks: showSubtasks,\n            compact: false,\n            calculateTaskProgress: calculateTaskProgress,\n            getTaskStatus: getTaskStatus,\n            calculateSubtaskProgress: calculateSubtaskProgress,\n            getSubtaskStatus: getSubtaskStatus,\n            onUpdateTask: onUpdateTask,\n            onUpdateSubtask: onUpdateSubtask,\n            onAddSubtask: onAddSubtask,\n            onDeleteTask: onDeleteTask,\n            onDeleteSubtask: onDeleteSubtask,\n            onAssignMembers: onAssignMembers,\n            invitedUsers: invitedUsers,\n            planOwner: planOwner\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 15\n          }, this)), isAddingTask && /*#__PURE__*/_jsxDEV(Box, {\n            className: styles.taskItem,\n            sx: {\n              position: 'relative',\n              mt: 1,\n              display: 'flex',\n              alignItems: 'center',\n              backgroundColor: '#f9f9f9',\n              borderRadius: '6px',\n              padding: '8px 12px',\n              border: '1px solid #f0f0f0'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                width: 10,\n                height: 10,\n                borderRadius: '50%',\n                backgroundColor: '#CCCCCC',\n                mr: 1.5,\n                flexShrink: 0\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 453,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(TextField, {\n              inputRef: newTaskInputRef,\n              value: newTaskName,\n              onChange: e => setNewTaskName(e.target.value),\n              onKeyDown: handleNewTaskKeyPress,\n              placeholder: \"Enter new task name...\",\n              variant: \"standard\",\n              fullWidth: true,\n              autoFocus: true,\n              sx: {\n                fontFamily: '\"Recursive Variable\", sans-serif',\n                '& .MuiInputBase-input': {\n                  fontSize: '0.9rem',\n                  fontFamily: '\"Recursive Variable\", sans-serif',\n                  padding: '4px 0'\n                },\n                '& .MuiInput-underline:before': {\n                  borderBottomColor: 'rgba(0, 0, 0, 0.1)'\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 464,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                ml: 'auto'\n              },\n              children: [/*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                onClick: handleSaveNewTask,\n                sx: {\n                  ml: 1\n                },\n                children: /*#__PURE__*/_jsxDEV(Iconify, {\n                  icon: \"material-symbols:check\",\n                  width: 18,\n                  height: 18,\n                  color: \"#4CAF50\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 488,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                onClick: () => setIsAddingTask(false),\n                sx: {\n                  ml: 0.5\n                },\n                children: /*#__PURE__*/_jsxDEV(Iconify, {\n                  icon: \"material-symbols:close\",\n                  width: 18,\n                  height: 18,\n                  color: \"#F44336\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 492,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 159,\n    columnNumber: 5\n  }, this);\n};\n// Component to display task and subtask\n_s(MilestoneCard, \"rFHsnbl/HSEoYuGM6shADMWTqh8=\");\n_c = MilestoneCard;\nconst TaskItem = _ref2 => {\n  _s2();\n  var _localTask$assignees;\n  let {\n    task,\n    showSubtasks = false,\n    compact = false,\n    calculateTaskProgress,\n    getTaskStatus,\n    calculateSubtaskProgress,\n    getSubtaskStatus,\n    onUpdateTask,\n    onUpdateSubtask,\n    onAddSubtask,\n    onDeleteTask,\n    onDeleteSubtask,\n    onAssignMembers,\n    invitedUsers,\n    planOwner\n  } = _ref2;\n  const [isEditing, setIsEditing] = useState(false);\n  const [taskName, setTaskName] = useState(task.name);\n  const taskInputRef = useRef(null);\n  const [isAddingSubtask, setIsAddingSubtask] = useState(false);\n  const [newSubtaskName, setNewSubtaskName] = useState('');\n  const newSubtaskInputRef = useRef(null);\n\n  // Comment functionality\n  const [commentDialogOpen, setCommentDialogOpen] = useState(false);\n  const [comments, setComments] = useState([]);\n  const [loadingComments, setLoadingComments] = useState(false);\n\n  // Enable due date functionality\n  const [dueDateDialogOpen, setDueDateDialogOpen] = useState(false);\n  const [updatingDueDate, setUpdatingDueDate] = useState(false);\n\n  // Add state for assign member dialog\n  const [assignMemberDialogOpen, setAssignMemberDialogOpen] = useState(false);\n\n  // Add state to store current task information\n  const [localTask, setLocalTask] = useState(task);\n\n  // Update localTask when task changes from props\n  useEffect(() => {\n    setLocalTask(task);\n  }, [task]);\n  const handleOpenComments = e => {\n    e.stopPropagation();\n    setCommentDialogOpen(true);\n    setLoadingComments(true);\n    getComments(task.id).then(response => {\n      var _response$data, _response$data2;\n      console.log('Comments data:', response.data);\n      // Check data structure and get correct comments array\n      let commentsData = [];\n      if (Array.isArray(response.data)) {\n        commentsData = response.data;\n      } else if ((_response$data = response.data) !== null && _response$data !== void 0 && _response$data.data && Array.isArray(response.data.data)) {\n        commentsData = response.data.data;\n      } else if ((_response$data2 = response.data) !== null && _response$data2 !== void 0 && _response$data2.comments && Array.isArray(response.data.comments)) {\n        commentsData = response.data.comments;\n      }\n      setComments(commentsData);\n    }).catch(error => {\n      console.error('Error fetching comments:', error);\n      toast.error('Failed to load comments');\n    }).finally(() => {\n      setLoadingComments(false);\n    });\n  };\n  const handleAddComment = async content => {\n    try {\n      var _response$data3;\n      const response = await addComment(task.id, content);\n      console.log('Add comment response:', response);\n\n      // Create new comment from response or create temporary object\n      let newComment;\n      if ((_response$data3 = response.data) !== null && _response$data3 !== void 0 && _response$data3.data) {\n        newComment = response.data.data;\n      } else {\n        // Create temporary object if API does not return new comment\n        const currentUser = JSON.parse(localStorage.getItem('user')) || {};\n        newComment = {\n          id: Date.now(),\n          // Temporary ID\n          content: content,\n          user: {\n            id: currentUser.id,\n            first_name: currentUser.first_name,\n            last_name: currentUser.last_name,\n            avatar: currentUser.avatar\n          },\n          created_at: new Date().toLocaleString('en-US', {\n            hour: '2-digit',\n            minute: '2-digit',\n            day: '2-digit',\n            month: '2-digit'\n          }).replace(',', '')\n        };\n      }\n\n      // Update state\n      setComments(prevComments => [...prevComments, newComment]);\n\n      // Refresh comments to ensure latest data\n      refreshComments();\n    } catch (error) {\n      console.error('Error adding comment:', error);\n      toast.error('Failed to add comment');\n    }\n  };\n  const handleUpdateComment = async (commentId, content) => {\n    try {\n      // Update UI first\n      const updatedCommentTemp = comments.find(c => c.id === commentId);\n      if (updatedCommentTemp) {\n        const updatedComments = comments.map(c => c.id === commentId ? {\n          ...c,\n          content: content\n        } : c);\n        setComments(updatedComments);\n      }\n\n      // Call API\n      const response = await updateComment(commentId, content);\n      console.log('Update comment response:', response);\n      toast.success('Comment updated successfully');\n\n      // Refresh comments to ensure latest data\n      refreshComments();\n    } catch (error) {\n      console.error('Error updating comment:', error);\n      toast.error('Failed to update comment');\n      // Refresh comments to restore original state if there is an error\n      refreshComments();\n    }\n  };\n  const handleDeleteComment = async commentId => {\n    try {\n      // Update UI first\n      setComments(prevComments => prevComments.filter(c => c.id !== commentId));\n\n      // Call API\n      await deleteComment(commentId);\n      console.log('Comment deleted successfully');\n      toast.success('Comment deleted successfully');\n\n      // Refresh comments to ensure latest data\n      refreshComments();\n    } catch (error) {\n      console.error('Error deleting comment:', error);\n      toast.error('Failed to delete comment');\n      // Refresh comments to restore original state if there is an error\n      refreshComments();\n    }\n  };\n\n  // Refresh comments function\n  const refreshComments = async () => {\n    try {\n      var _response$data4, _response$data5;\n      const response = await getComments(task.id);\n      console.log('Refreshed comments:', response.data);\n\n      // Handle returned data\n      let commentsData = [];\n      if (Array.isArray(response.data)) {\n        commentsData = response.data;\n      } else if ((_response$data4 = response.data) !== null && _response$data4 !== void 0 && _response$data4.data && Array.isArray(response.data.data)) {\n        commentsData = response.data.data;\n      } else if ((_response$data5 = response.data) !== null && _response$data5 !== void 0 && _response$data5.comments && Array.isArray(response.data.comments)) {\n        commentsData = response.data.comments;\n      }\n      setComments(commentsData);\n    } catch (error) {\n      console.error('Error refreshing comments:', error);\n    }\n  };\n\n  // Calculate task progress\n  const taskProgress = calculateTaskProgress ? calculateTaskProgress(task) : 0;\n\n  // Determine task status\n  const taskStatus = getTaskStatus ? getTaskStatus(task) : task.status || STATUS.NOT_STARTED;\n  const statusConfig = STATUS_CONFIG[taskStatus] || STATUS_CONFIG[STATUS.NOT_STARTED];\n\n  // Check if task has subtasks\n  const hasSubtasks = task.subtasks && task.subtasks.length > 0;\n\n  // Check if task is completed\n  const isCompleted = taskStatus === STATUS.COMPLETED;\n\n  // Handle edit mode for task\n  const handleTaskEditClick = () => {\n    setIsEditing(true);\n  };\n\n  // Handle save changes for task\n  const handleTaskSave = () => {\n    // Trim the name to remove leading/trailing whitespace\n    const trimmedName = taskName.trim();\n\n    // Only update if the name has actually changed (after trimming)\n    if (trimmedName !== '' && trimmedName !== task.name) {\n      if (onUpdateTask) {\n        onUpdateTask({\n          ...task,\n          name: trimmedName\n        });\n      }\n    } else {\n      // Reset to original if empty or unchanged\n      setTaskName(task.name);\n    }\n    setIsEditing(false);\n  };\n\n  // Handle key press events for task\n  const handleTaskKeyPress = e => {\n    if (e.key === 'Enter') {\n      handleTaskSave();\n    } else if (e.key === 'Escape') {\n      setTaskName(task.name);\n      setIsEditing(false);\n    }\n  };\n\n  // Handle add subtask\n  const handleAddSubtaskClick = () => {\n    setIsAddingSubtask(true);\n  };\n\n  // Handle delete task\n  const handleDeleteTaskClick = () => {\n    if (onDeleteTask) {\n      onDeleteTask(task);\n    }\n  };\n\n  // Handle save new subtask\n  const handleSaveNewSubtask = () => {\n    const trimmedName = newSubtaskName.trim();\n    if (trimmedName && onAddSubtask) {\n      const newSubtask = {\n        name: trimmedName,\n        task: task.slug,\n        status: STATUS.NOT_STARTED,\n        progress: 0\n      };\n      onAddSubtask(newSubtask);\n      setNewSubtaskName('');\n    }\n    setIsAddingSubtask(false);\n  };\n\n  // Handle key press events for new subtask\n  const handleNewSubtaskKeyPress = e => {\n    if (e.key === 'Enter') {\n      handleSaveNewSubtask();\n    } else if (e.key === 'Escape') {\n      setNewSubtaskName('');\n      setIsAddingSubtask(false);\n    }\n  };\n\n  // Focus input when editing starts\n  useEffect(() => {\n    if (isEditing && taskInputRef.current) {\n      taskInputRef.current.focus();\n    }\n  }, [isEditing]);\n\n  // Focus input when adding subtask\n  useEffect(() => {\n    if (isAddingSubtask && newSubtaskInputRef.current) {\n      newSubtaskInputRef.current.focus();\n    }\n  }, [isAddingSubtask]);\n\n  // Check if task has due dates - use localTask instead of task\n  const hasDueDates = localTask.start_date || localTask.end_date;\n  const handleOpenDueDateDialog = e => {\n    e.stopPropagation();\n    setDueDateDialogOpen(true);\n  };\n  const handleUpdateDueDate = async updatedTask => {\n    setUpdatingDueDate(true);\n    try {\n      const taskToUpdate = {\n        ...localTask,\n        start_date: updatedTask.start_date,\n        end_date: updatedTask.end_date,\n        progress: localTask.progress || 0,\n        status: localTask.status || 1\n      };\n\n      // Update local state first\n      setLocalTask(taskToUpdate);\n\n      // Call API\n      const response = await updateTask(taskToUpdate);\n\n      // If API returns data, update local state\n      if (response && response.data) {\n        setLocalTask(response.data);\n      }\n\n      // Update state in parent component if necessary\n      if (onUpdateTask) {\n        onUpdateTask(taskToUpdate);\n      }\n      toast.success('Due date updated successfully');\n    } catch (error) {\n      console.error('Error updating due date:', error);\n      toast.error('Failed to update due date');\n\n      // If there is an error, restore original state\n      setLocalTask(task);\n    } finally {\n      setUpdatingDueDate(false);\n      setDueDateDialogOpen(false); // Close dialog after completion\n    }\n  };\n\n  // Add handler for member assignment updates\n  const handleAssignMembers = async assignedUserIds => {\n    try {\n      // Convert IDs to numbers\n      const numericIds = assignedUserIds.map(id => Number(id));\n      // Call API to assign members\n      const response = await assignMembersToTask(localTask.slug, numericIds);\n      // Create updated task with new assignees\n      const updatedTask = {\n        ...localTask,\n        assignees: response.assignees || numericIds.map(id => ({\n          id: id,\n          first_name: '',\n          last_name: '',\n          email: ''\n        }))\n      };\n\n      // Update both local state and parent state\n      setLocalTask(updatedTask);\n      if (onUpdateTask) {\n        onUpdateTask(updatedTask);\n      }\n      setAssignMemberDialogOpen(false);\n      toast.success('Members assigned successfully');\n    } catch (error) {\n      console.error('Error assigning members:', error);\n      toast.error('Failed to assign members');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      className: styles.taskItemContainer,\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        className: styles.taskItem,\n        sx: {\n          position: 'relative'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: 10,\n            height: 10,\n            borderRadius: '50%',\n            backgroundColor: statusConfig.color,\n            mr: 1.5,\n            flexShrink: 0\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 871,\n          columnNumber: 11\n        }, this), isEditing ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            flexGrow: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            inputRef: taskInputRef,\n            value: taskName,\n            onChange: e => setTaskName(e.target.value),\n            onKeyDown: handleTaskKeyPress,\n            onBlur: handleTaskSave,\n            variant: \"standard\",\n            fullWidth: true,\n            autoFocus: true,\n            sx: {\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              '& .MuiInputBase-input': {\n                fontSize: '0.9rem',\n                fontFamily: '\"Recursive Variable\", sans-serif'\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 884,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n            size: \"small\",\n            onClick: handleTaskSave,\n            sx: {\n              ml: 1\n            },\n            children: /*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"material-symbols:check\",\n              width: 16,\n              height: 16,\n              color: \"#4CAF50\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 902,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 901,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 883,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            flexGrow: 1,\n            display: 'flex',\n            flexDirection: 'column',\n            gap: 0.5\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'space-between',\n              width: '100%'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 2,\n                flexGrow: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Typography, {\n                variant: \"body2\",\n                className: styles.taskName,\n                sx: {\n                  fontFamily: '\"Recursive Variable\", sans-serif',\n                  cursor: 'pointer',\n                  display: 'flex',\n                  alignItems: 'center',\n                  color: isCompleted ? '#4CAF50' : 'inherit',\n                  fontWeight: isCompleted ? 500 : 400,\n                  flexGrow: 1,\n                  '&:hover': {\n                    '& .task-edit-icon': {\n                      opacity: 1\n                    }\n                  }\n                },\n                onClick: handleTaskEditClick,\n                children: [taskName, /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: \"Edit task name\",\n                  children: /*#__PURE__*/_jsxDEV(IconButton, {\n                    size: \"small\",\n                    className: \"task-edit-icon\",\n                    sx: {\n                      ml: 0.5,\n                      opacity: 0,\n                      transition: 'opacity 0.2s',\n                      padding: '2px'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(Iconify, {\n                      icon: \"material-symbols:edit-outline\",\n                      width: 14,\n                      height: 14,\n                      color: \"#666\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 939,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 929,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 928,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 909,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 908,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                display: 'flex',\n                alignItems: 'center',\n                gap: 2\n              },\n              children: [localTask.assignees && localTask.assignees.length > 0 && /*#__PURE__*/_jsxDEV(AvatarGroup, {\n                max: 3,\n                sx: {\n                  '& .MuiAvatar-root': {\n                    width: 24,\n                    height: 24,\n                    fontSize: '0.75rem',\n                    border: '1.5px solid #fff'\n                  }\n                },\n                children: localTask.assignees.map((assignee, index) => /*#__PURE__*/_jsxDEV(Tooltip, {\n                  title: assignee.first_name && assignee.last_name ? `${assignee.first_name} ${assignee.last_name}` : assignee.email,\n                  arrow: true,\n                  children: /*#__PURE__*/_jsxDEV(Avatar, {\n                    src: assignee.avatar,\n                    alt: assignee.first_name || assignee.email,\n                    sx: {\n                      bgcolor: mainYellowColor\n                    },\n                    children: assignee.first_name ? assignee.first_name.charAt(0).toUpperCase() : assignee.email ? assignee.email.charAt(0).toUpperCase() : ''\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 967,\n                    columnNumber: 27\n                  }, this)\n                }, assignee.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 960,\n                  columnNumber: 25\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 948,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                className: \"task-toolbar\",\n                sx: {\n                  position: 'relative',\n                  display: 'flex',\n                  alignItems: 'center',\n                  '&:hover .toolbar-expanded': {\n                    opacity: 1,\n                    visibility: 'visible',\n                    transform: 'translateX(0)'\n                  },\n                  '&:hover .toolbar-dots': {\n                    opacity: 0,\n                    visibility: 'hidden'\n                  }\n                },\n                children: [/*#__PURE__*/_jsxDEV(Box, {\n                  className: \"toolbar-dots\",\n                  sx: {\n                    opacity: 1,\n                    visibility: 'visible',\n                    transition: 'all 0.2s ease',\n                    display: 'flex',\n                    alignItems: 'center',\n                    cursor: 'pointer',\n                    padding: '4px 8px',\n                    borderRadius: '6px',\n                    '&:hover': {\n                      backgroundColor: 'rgba(0, 0, 0, 0.04)'\n                    }\n                  },\n                  children: /*#__PURE__*/_jsxDEV(Iconify, {\n                    icon: \"material-symbols:more-horiz\",\n                    width: 20,\n                    height: 20,\n                    color: \"#666\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1020,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1004,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Box, {\n                  className: \"toolbar-expanded\",\n                  sx: {\n                    position: 'absolute',\n                    right: 0,\n                    top: 0,\n                    opacity: 0,\n                    visibility: 'hidden',\n                    transform: 'translateX(10px)',\n                    transition: 'all 0.2s ease',\n                    display: 'flex',\n                    alignItems: 'center',\n                    border: '1px solid #eee',\n                    borderRadius: '8px',\n                    padding: '2px 4px',\n                    background: '#fff',\n                    boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\n                    zIndex: 10\n                  },\n                  children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: hasDueDates ? \"Edit due date\" : \"Set due date\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: handleOpenDueDateDialog,\n                      sx: {\n                        color: hasDueDates ? mainYellowColor : '#888',\n                        '&:hover': {\n                          color: mainYellowColor,\n                          bgcolor: 'rgba(255, 193, 7, 0.08)'\n                        }\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Iconify, {\n                        icon: hasDueDates ? \"material-symbols:calendar-month\" : \"material-symbols:calendar-add-on\",\n                        width: 16,\n                        height: 16\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1057,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1046,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1045,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Comments\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: handleOpenComments,\n                      disabled: loadingComments,\n                      sx: {\n                        color: mainYellowColor,\n                        '&:hover': {\n                          bgcolor: 'rgba(255, 193, 7, 0.08)'\n                        }\n                      },\n                      children: loadingComments ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                        size: 16,\n                        sx: {\n                          color: mainYellowColor\n                        }\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1079,\n                        columnNumber: 29\n                      }, this) : /*#__PURE__*/_jsxDEV(Iconify, {\n                        icon: \"material-symbols:comment-outline\",\n                        width: 16,\n                        height: 16\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1081,\n                        columnNumber: 29\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1067,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1066,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Assign members\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: () => setAssignMemberDialogOpen(true),\n                      sx: {\n                        color: ((_localTask$assignees = localTask.assignees) === null || _localTask$assignees === void 0 ? void 0 : _localTask$assignees.length) > 0 ? mainYellowColor : '#888',\n                        '&:hover': {\n                          color: mainYellowColor,\n                          bgcolor: 'rgba(255, 193, 7, 0.08)'\n                        }\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Iconify, {\n                        icon: \"mdi:account-multiple-plus\",\n                        width: 16,\n                        height: 16\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1099,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1088,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1087,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Add subtask\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: handleAddSubtaskClick,\n                      sx: {\n                        color: mainYellowColor,\n                        '&:hover': {\n                          bgcolor: 'rgba(255, 193, 7, 0.08)'\n                        }\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Iconify, {\n                        icon: \"material-symbols:add-task\",\n                        width: 16,\n                        height: 16\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1119,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1109,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1108,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Edit task\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: handleTaskEditClick,\n                      sx: {\n                        color: '#666',\n                        '&:hover': {\n                          bgcolor: 'rgba(0, 0, 0, 0.04)'\n                        }\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Iconify, {\n                        icon: \"eva:edit-fill\",\n                        width: 16,\n                        height: 16\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1135,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1125,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1124,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n                    title: \"Delete task\",\n                    children: /*#__PURE__*/_jsxDEV(IconButton, {\n                      size: \"small\",\n                      onClick: handleDeleteTaskClick,\n                      sx: {\n                        color: '#F44336',\n                        '&:hover': {\n                          bgcolor: 'rgba(244, 67, 54, 0.08)'\n                        }\n                      },\n                      children: /*#__PURE__*/_jsxDEV(Iconify, {\n                        icon: \"material-symbols:delete-outline\",\n                        width: 16,\n                        height: 16\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 1151,\n                        columnNumber: 27\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 1141,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 1140,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1024,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 986,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  display: 'flex',\n                  alignItems: 'center',\n                  bgcolor: statusConfig.color + '20',\n                  px: 1,\n                  py: 0.5,\n                  borderRadius: '4px'\n                },\n                children: /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"caption\",\n                  sx: {\n                    fontWeight: 600,\n                    color: statusConfig.color,\n                    fontFamily: '\"Recursive Variable\", sans-serif'\n                  },\n                  children: [taskProgress, \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 1168,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1158,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 945,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 907,\n            columnNumber: 15\n          }, this), hasDueDates && /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center',\n              backgroundColor: `${mainYellowColor}08`,\n              borderRadius: '4px',\n              py: 0.5,\n              px: 0.75,\n              width: 'fit-content'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"material-symbols:calendar-month\",\n              width: 14,\n              height: 14,\n              color: mainYellowColor\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1195,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              sx: {\n                fontFamily: '\"Recursive Variable\", sans-serif',\n                fontSize: '0.75rem',\n                color: '#555',\n                fontWeight: 500,\n                lineHeight: 1,\n                ml: 0.5\n              },\n              children: [formatDate(localTask.start_date), \" ~ \", formatDate(localTask.end_date)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 1200,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1184,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 906,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 868,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          pl: 4,\n          pr: 1,\n          pt: 0.5,\n          pb: 0.5,\n          borderLeft: `1px dashed ${statusConfig.color}`,\n          ml: 1.5,\n          mt: 0.5,\n          display: (hasSubtasks || isAddingSubtask) && showSubtasks ? 'block' : 'none'\n        },\n        children: [task.subtasks && task.subtasks.map((subtask, index) => /*#__PURE__*/_jsxDEV(SubtaskItem, {\n          subtask: subtask,\n          index: index,\n          totalSubtasks: task.subtasks.length,\n          taskSlug: task.slug,\n          calculateSubtaskProgress: calculateSubtaskProgress,\n          getSubtaskStatus: getSubtaskStatus,\n          onUpdateSubtask: onUpdateSubtask,\n          onDeleteSubtask: onDeleteSubtask,\n          onAssignMembers: onAssignMembers,\n          invitedUsers: invitedUsers,\n          planOwner: planOwner\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1231,\n          columnNumber: 13\n        }, this)), isAddingSubtask && /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            py: 0.5,\n            borderBottom: '1px dotted #f0f0f0',\n            backgroundColor: '#f9f9f9',\n            borderRadius: '4px',\n            px: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n            disabled: true,\n            size: \"small\",\n            sx: {\n              p: 0.5,\n              mr: 0.5,\n              color: '#CCCCCC'\n            },\n            icon: /*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"material-symbols:check-box-outline-blank\",\n              width: 18,\n              height: 18\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1268,\n              columnNumber: 23\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1260,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(TextField, {\n            inputRef: newSubtaskInputRef,\n            value: newSubtaskName,\n            onChange: e => setNewSubtaskName(e.target.value),\n            onKeyDown: handleNewSubtaskKeyPress,\n            placeholder: \"Enter new subtask name...\",\n            variant: \"standard\",\n            fullWidth: true,\n            autoFocus: true,\n            sx: {\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              '& .MuiInputBase-input': {\n                fontSize: '0.85rem',\n                fontFamily: '\"Recursive Variable\", sans-serif',\n                padding: '4px 0'\n              },\n              '& .MuiInput-underline:before': {\n                borderBottomColor: 'rgba(0, 0, 0, 0.1)'\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1271,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              ml: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: handleSaveNewSubtask,\n              sx: {\n                p: 0.5\n              },\n              children: /*#__PURE__*/_jsxDEV(Iconify, {\n                icon: \"material-symbols:check\",\n                width: 18,\n                height: 18,\n                color: \"#4CAF50\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1295,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1294,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n              size: \"small\",\n              onClick: () => setIsAddingSubtask(false),\n              sx: {\n                p: 0.5,\n                ml: 0.5\n              },\n              children: /*#__PURE__*/_jsxDEV(Iconify, {\n                icon: \"material-symbols:close\",\n                width: 18,\n                height: 18,\n                color: \"#F44336\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 1299,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 1298,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 1293,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 1249,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 1218,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 867,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CommentDialog, {\n      open: commentDialogOpen,\n      onClose: () => setCommentDialogOpen(false),\n      comments: comments,\n      onAddComment: handleAddComment,\n      onUpdateComment: handleUpdateComment,\n      onDeleteComment: handleDeleteComment,\n      loading: loadingComments,\n      targetName: task.name,\n      targetType: \"task\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1308,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DueDateDialog, {\n      open: dueDateDialogOpen,\n      onClose: () => setDueDateDialogOpen(false),\n      task: localTask,\n      onUpdateDueDate: handleUpdateDueDate,\n      isUpdating: updatingDueDate\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1321,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AssignMemberDialog, {\n      open: assignMemberDialogOpen,\n      onClose: () => setAssignMemberDialogOpen(false),\n      task: localTask,\n      invitedUsers: invitedUsers,\n      planOwner: planOwner,\n      onAssignMembers: handleAssignMembers\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1330,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// Component for subtask\n_s2(TaskItem, \"dnh9c1rYa0G+f59syNTxEgU3FYU=\");\n_c2 = TaskItem;\nconst SubtaskItem = _ref3 => {\n  _s3();\n  let {\n    subtask,\n    index,\n    totalSubtasks,\n    taskSlug,\n    calculateSubtaskProgress,\n    getSubtaskStatus,\n    onUpdateSubtask,\n    onDeleteSubtask,\n    onAssignMembers,\n    invitedUsers,\n    planOwner\n  } = _ref3;\n  const [isEditing, setIsEditing] = useState(false);\n  const [subtaskName, setSubtaskName] = useState(subtask.name);\n  const [isChecked, setIsChecked] = useState(subtask.status === STATUS.COMPLETED || subtask.progress === 100);\n  const subtaskInputRef = useRef(null);\n\n  // Calculate subtask progress and status\n  const subtaskProgress = calculateSubtaskProgress ? calculateSubtaskProgress(subtask) : 0;\n  const subtaskStatus = getSubtaskStatus ? getSubtaskStatus(subtask) : subtask.status || STATUS.NOT_STARTED;\n  const subtaskStatusConfig = STATUS_CONFIG[subtaskStatus] || STATUS_CONFIG[STATUS.NOT_STARTED];\n\n  // Update isChecked when subtask changes from outside\n  useEffect(() => {\n    setIsChecked(subtask.status === STATUS.COMPLETED || subtask.progress === 100);\n  }, [subtask.status, subtask.progress]);\n\n  // Handle edit mode for subtask\n  const handleSubtaskEditClick = () => {\n    setIsEditing(true);\n  };\n\n  // Handle save changes for subtask\n  const handleSubtaskSave = () => {\n    // Trim the name to remove leading/trailing whitespace\n    const trimmedName = subtaskName.trim();\n\n    // Only update if the name has actually changed (after trimming)\n    if (trimmedName !== '' && trimmedName !== subtask.name) {\n      if (onUpdateSubtask) {\n        onUpdateSubtask({\n          ...subtask,\n          name: trimmedName,\n          task: taskSlug\n        });\n      }\n    } else {\n      // Reset to original if empty or unchanged\n      setSubtaskName(subtask.name);\n    }\n    setIsEditing(false);\n  };\n\n  // Handle key press events for subtask\n  const handleSubtaskKeyPress = e => {\n    if (e.key === 'Enter') {\n      handleSubtaskSave();\n    } else if (e.key === 'Escape') {\n      setSubtaskName(subtask.name);\n      setIsEditing(false);\n    }\n  };\n\n  // Handle status toggle\n  const handleStatusToggle = () => {\n    // Update UI immediately to provide user feedback\n    const newCheckedState = !isChecked;\n    setIsChecked(newCheckedState);\n    const newStatus = newCheckedState ? STATUS.COMPLETED : STATUS.NOT_STARTED;\n    const newProgress = newCheckedState ? 100 : 0;\n    if (onUpdateSubtask) {\n      // Ensure to send both status and progress, even when progress = 0\n      const updatedData = {\n        ...subtask,\n        status: newStatus,\n        progress: newProgress,\n        task: taskSlug\n      };\n      console.log('Sending subtask update:', updatedData);\n      onUpdateSubtask(updatedData);\n    }\n  };\n\n  // Handle delete subtask\n  const handleDeleteSubtaskClick = () => {\n    if (window.confirm(`Are you sure you want to delete this subtask?`)) {\n      onDeleteSubtask(subtask, taskSlug);\n    }\n  };\n\n  // Focus input when editing starts\n  useEffect(() => {\n    if (isEditing && subtaskInputRef.current) {\n      subtaskInputRef.current.focus();\n    }\n  }, [isEditing]);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex',\n      alignItems: 'center',\n      py: 0.5,\n      borderBottom: index < totalSubtasks - 1 ? '1px dotted #f0f0f0' : 'none'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Checkbox, {\n      checked: isChecked,\n      onChange: handleStatusToggle,\n      size: \"small\",\n      sx: {\n        p: 0.5,\n        mr: 0.5,\n        color: '#CCCCCC',\n        '&.Mui-checked': {\n          color: '#4CAF50'\n        }\n      },\n      icon: /*#__PURE__*/_jsxDEV(Iconify, {\n        icon: \"material-symbols:check-box-outline-blank\",\n        width: 18,\n        height: 18\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1462,\n        columnNumber: 15\n      }, this),\n      checkedIcon: /*#__PURE__*/_jsxDEV(Iconify, {\n        icon: \"material-symbols:check-box\",\n        width: 18,\n        height: 18\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1463,\n        columnNumber: 22\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1450,\n      columnNumber: 7\n    }, this), isEditing ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center',\n        flexGrow: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(TextField, {\n        inputRef: subtaskInputRef,\n        value: subtaskName,\n        onChange: e => setSubtaskName(e.target.value),\n        onKeyDown: handleSubtaskKeyPress,\n        onBlur: handleSubtaskSave,\n        variant: \"standard\",\n        fullWidth: true,\n        autoFocus: true,\n        sx: {\n          fontFamily: '\"Recursive Variable\", sans-serif',\n          '& .MuiInputBase-input': {\n            fontSize: '0.85rem',\n            fontFamily: '\"Recursive Variable\", sans-serif'\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1468,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n        size: \"small\",\n        onClick: handleSubtaskSave,\n        sx: {\n          ml: 1\n        },\n        children: /*#__PURE__*/_jsxDEV(Iconify, {\n          icon: \"material-symbols:check\",\n          width: 14,\n          height: 14,\n          color: \"#4CAF50\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1486,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1485,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1467,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body2\",\n      sx: {\n        flexGrow: 1,\n        fontSize: '0.85rem',\n        fontFamily: '\"Recursive Variable\", sans-serif',\n        color: isChecked ? '#4CAF50' : '#555',\n        fontWeight: isChecked ? 500 : 400,\n        cursor: 'pointer',\n        display: 'flex',\n        alignItems: 'center',\n        '&:hover': {\n          '& .subtask-edit-icon': {\n            opacity: 1\n          }\n        }\n      },\n      onClick: handleSubtaskEditClick,\n      children: [subtaskName, /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"Edit subtask name\",\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          className: \"subtask-edit-icon\",\n          sx: {\n            ml: 0.5,\n            opacity: 0,\n            transition: 'opacity 0.2s',\n            padding: '1px'\n          },\n          children: /*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"material-symbols:edit-outline\",\n            width: 12,\n            height: 12,\n            color: \"#666\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1521,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1511,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1510,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 1490,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"Delete subtask\",\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          onClick: handleDeleteSubtaskClick,\n          sx: {\n            p: 0.5,\n            color: '#F44336',\n            '&:hover': {\n              backgroundColor: '#FFEBEE'\n            }\n          },\n          children: /*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"material-symbols:delete-outline\",\n            width: 14,\n            height: 14\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 1541,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 1530,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 1529,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 1527,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 1442,\n    columnNumber: 5\n  }, this);\n};\n\n// Format date for display\n_s3(SubtaskItem, \"95kGDfgernN1QkX5iwg2z2cf2rU=\");\n_c3 = SubtaskItem;\nfunction formatDate(dateString) {\n  if (!dateString) return '';\n  try {\n    const date = new Date(dateString);\n    if (isNaN(date.getTime())) return '';\n\n    // Only display day and month, not year\n    const options = {\n      month: 'short',\n      day: 'numeric'\n    };\n    return date.toLocaleDateString('en-US', options);\n  } catch (error) {\n    return '';\n  }\n}\nexport default MilestoneCard;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"MilestoneCard\");\n$RefreshReg$(_c2, \"TaskItem\");\n$RefreshReg$(_c3, \"SubtaskItem\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Box", "Typography", "Paper", "Chip", "LinearProgress", "<PERSON><PERSON>", "Divider", "TextField", "IconButton", "<PERSON><PERSON><PERSON>", "Checkbox", "CircularProgress", "Avatar", "AvatarGroup", "Collapse", "Iconify", "mainYellowColor", "STATUS", "STATUS_CONFIG", "styles", "CommentDialog", "DueDateDialog", "AssignMemberDialog", "getComments", "addComment", "updateComment", "deleteComment", "updateTask", "assignMembersToTask", "toast", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "MilestoneCard", "_ref", "_s", "_milestone$tasks", "_milestone$tasks2", "_milestone$tasks3", "milestone", "compact", "showSubtasks", "calculateMilestoneProgress", "getMilestoneStatus", "calculateTaskProgress", "getTaskStatus", "calculateSubtaskProgress", "getSubtaskStatus", "onUpdateMilestone", "onUpdateTask", "onUpdateSubtask", "onAddTask", "onAddSubtask", "onDeleteTask", "onDeleteSubtask", "onAssignMembers", "invitedUsers", "<PERSON><PERSON><PERSON><PERSON>", "isEditing", "setIsEditing", "milestoneName", "setMilestoneName", "name", "isAddingTask", "setIsAddingTask", "newTaskName", "setNewTaskName", "isExpanded", "setIsExpanded", "savedState", "localStorage", "getItem", "id", "JSON", "parse", "inputRef", "newTaskInputRef", "handleToggleExpanded", "newExpandedState", "setItem", "stringify", "progress", "milestoneStatus", "NOT_STARTED", "statusConfig", "hasDescription", "description", "trim", "length", "handleEditClick", "handleSave", "trimmedName", "handleKeyPress", "e", "key", "handleAddTaskClick", "handleSaveNewTask", "newTask", "status", "handleNewTaskKeyPress", "current", "focus", "elevation", "className", "milestoneCard", "sx", "padding", "children", "<PERSON><PERSON><PERSON><PERSON>", "display", "alignItems", "flex", "size", "onClick", "mr", "color", "backgroundColor", "icon", "width", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "value", "onChange", "target", "onKeyDown", "onBlur", "variant", "fullWidth", "autoFocus", "fontFamily", "fontWeight", "fontSize", "ml", "milestoneTitle", "cursor", "opacity", "title", "transition", "label", "borderRadius", "in", "timeout", "unmountOnExit", "mb", "mt", "p", "border", "whiteSpace", "lineHeight", "my", "justifyContent", "estimated_duration", "gap", "taskList", "tasks", "slice", "map", "task", "index", "TaskItem", "textTransform", "taskItem", "position", "flexShrink", "placeholder", "borderBottomColor", "_c", "_ref2", "_s2", "_localTask$assignees", "taskName", "setTaskName", "taskInputRef", "isAddingSubtask", "setIsAddingSubtask", "newSubtaskName", "setNewSubtaskName", "newSubtaskInputRef", "commentDialogOpen", "setCommentDialogOpen", "comments", "setComments", "loadingComments", "setLoadingComments", "dueDateDialogOpen", "setDueDateDialogOpen", "updatingDueDate", "setUpdatingDueDate", "assignMemberDialogOpen", "setAssignMemberDialogOpen", "localTask", "setLocalTask", "handleOpenComments", "stopPropagation", "then", "response", "_response$data", "_response$data2", "console", "log", "data", "commentsData", "Array", "isArray", "catch", "error", "finally", "handleAddComment", "content", "_response$data3", "newComment", "currentUser", "Date", "now", "user", "first_name", "last_name", "avatar", "created_at", "toLocaleString", "hour", "minute", "day", "month", "replace", "prevComments", "refreshComments", "handleUpdateComment", "commentId", "updatedCommentTemp", "find", "c", "updatedComments", "success", "handleDeleteComment", "filter", "_response$data4", "_response$data5", "taskProgress", "taskStatus", "hasSubtasks", "subtasks", "isCompleted", "COMPLETED", "handleTaskEditClick", "handleTaskSave", "handleTaskKeyPress", "handleAddSubtaskClick", "handleDeleteTaskClick", "handleSaveNewSubtask", "newSubtask", "slug", "handleNewSubtaskKeyPress", "hasDueDates", "start_date", "end_date", "handleOpenDueDateDialog", "handleUpdateDueDate", "updatedTask", "taskToUpdate", "handleAssignMembers", "assignedUserIds", "numericIds", "Number", "assignees", "email", "taskItemContainer", "flexGrow", "flexDirection", "max", "assignee", "arrow", "src", "alt", "bgcolor", "char<PERSON>t", "toUpperCase", "visibility", "transform", "right", "top", "background", "boxShadow", "zIndex", "disabled", "px", "py", "formatDate", "pl", "pr", "pt", "pb", "borderLeft", "subtask", "SubtaskItem", "totalSubtasks", "taskSlug", "borderBottom", "open", "onClose", "onAddComment", "onUpdateComment", "onDeleteComment", "loading", "targetName", "targetType", "onUpdateDueDate", "isUpdating", "_c2", "_ref3", "_s3", "subtaskName", "setSubtaskName", "isChecked", "setIsChecked", "subtaskInputRef", "subtaskProgress", "subtaskStatus", "subtaskStatusConfig", "handleSubtaskEditClick", "handleSubtaskSave", "handleSubtaskKeyPress", "handleStatusToggle", "newCheckedState", "newStatus", "newProgress", "updatedData", "handleDeleteSubtaskClick", "window", "confirm", "checked", "checkedIcon", "_c3", "dateString", "date", "isNaN", "getTime", "options", "toLocaleDateString", "$RefreshReg$"], "sources": ["C:/ignition/ignition-ui/src/views/plan/detail/components/MilestoneCard.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Paper,\r\n  Chip,\r\n  LinearProgress,\r\n  Button,\r\n  Divider,\r\n  TextField,\r\n  IconButton,\r\n  Tooltip,\r\n  Checkbox,\r\n  CircularProgress,\r\n  Avatar,\r\n  AvatarGroup,\r\n  Collapse\r\n} from '@mui/material';\r\nimport Iconify from 'components/Iconify/index';\r\nimport { mainYellowColor } from \"helpers/constants\";\r\nimport { STATUS, STATUS_CONFIG } from '../hooks/usePlanData';\r\nimport styles from '../styles.module.scss';\r\nimport CommentDialog from '../dialogs/CommentDialog';\r\nimport DueDateDialog from '../dialogs/DueDateDialog';\r\nimport AssignMemberDialog from '../dialogs/AssignMemberDialog';\r\nimport { getComments, addComment, updateComment, deleteComment, updateTask, assignMembersToTask } from '../../services';\r\nimport { toast } from 'react-toastify';\r\n\r\nconst MilestoneCard = ({\r\n  milestone,\r\n  compact = false,\r\n  showSubtasks = false,\r\n  calculateMilestoneProgress,\r\n  getMilestoneStatus,\r\n  calculateTaskProgress,\r\n  getTaskStatus,\r\n  calculateSubtaskProgress,\r\n  getSubtaskStatus,\r\n  onUpdateMilestone,\r\n  onUpdateTask,\r\n  onUpdateSubtask,\r\n  onAddTask,\r\n  onAddSubtask,\r\n  onDeleteTask,\r\n  onDeleteSubtask,\r\n  onAssignMembers,\r\n  invitedUsers,\r\n  planOwner\r\n}) => {\r\n  const [isEditing, setIsEditing] = useState(false);\r\n  const [milestoneName, setMilestoneName] = useState(milestone.name);\r\n  const [isAddingTask, setIsAddingTask] = useState(false);\r\n  const [newTaskName, setNewTaskName] = useState('');\r\n  // Initialize collapse state from localStorage\r\n  const [isExpanded, setIsExpanded] = useState(() => {\r\n    const savedState = localStorage.getItem(`milestone_${milestone.id || milestone.name}_expanded`);\r\n    return savedState !== null ? JSON.parse(savedState) : true; // Default to expanded\r\n  });\r\n  const inputRef = useRef(null);\r\n  const newTaskInputRef = useRef(null);\r\n\r\n  // Handle expand/collapse toggle with state persistence\r\n  const handleToggleExpanded = () => {\r\n    const newExpandedState = !isExpanded;\r\n    setIsExpanded(newExpandedState);\r\n    // Save state to localStorage\r\n    localStorage.setItem(`milestone_${milestone.id || milestone.name}_expanded`, JSON.stringify(newExpandedState));\r\n  };\r\n\r\n  // Calculate milestone progress\r\n  const progress = calculateMilestoneProgress ? calculateMilestoneProgress(milestone) : 0;\r\n\r\n  // Determine status based on progress\r\n  const milestoneStatus = getMilestoneStatus ? getMilestoneStatus(milestone) : STATUS.NOT_STARTED;\r\n  const statusConfig = STATUS_CONFIG[milestoneStatus] || STATUS_CONFIG[STATUS.NOT_STARTED];\r\n\r\n  // Check if description exists and is not empty\r\n  const hasDescription = milestone.description && milestone.description.trim().length > 0;\r\n\r\n  // Handle edit mode\r\n  const handleEditClick = () => {\r\n    setIsEditing(true);\r\n  };\r\n\r\n  // Handle save changes\r\n  const handleSave = () => {\r\n    // Trim the name to remove leading/trailing whitespace\r\n    const trimmedName = milestoneName.trim();\r\n\r\n    // Only update if the name has actually changed (after trimming)\r\n    if (trimmedName !== '' && trimmedName !== milestone.name) {\r\n      if (onUpdateMilestone) {\r\n        onUpdateMilestone({ ...milestone, name: trimmedName });\r\n      }\r\n    } else {\r\n      // Reset to original if empty or unchanged\r\n      setMilestoneName(milestone.name);\r\n    }\r\n\r\n    setIsEditing(false);\r\n  };\r\n\r\n  // Handle key press events\r\n  const handleKeyPress = (e) => {\r\n    if (e.key === 'Enter') {\r\n      handleSave();\r\n    } else if (e.key === 'Escape') {\r\n      setMilestoneName(milestone.name);\r\n      setIsEditing(false);\r\n    }\r\n  };\r\n\r\n  // Handle add task\r\n  const handleAddTaskClick = () => {\r\n    setIsAddingTask(true);\r\n  };\r\n\r\n  // Handle save new task\r\n  const handleSaveNewTask = () => {\r\n    const trimmedName = newTaskName.trim();\r\n    if (trimmedName && onAddTask) {\r\n      const newTask = {\r\n        name: trimmedName,\r\n        milestone: milestone.id,\r\n        status: STATUS.NOT_STARTED,\r\n        progress: 0\r\n      };\r\n      onAddTask(newTask);\r\n      setNewTaskName('');\r\n    }\r\n    setIsAddingTask(false);\r\n  };\r\n\r\n  // Handle key press events for new task\r\n  const handleNewTaskKeyPress = (e) => {\r\n    if (e.key === 'Enter') {\r\n      handleSaveNewTask();\r\n    } else if (e.key === 'Escape') {\r\n      setNewTaskName('');\r\n      setIsAddingTask(false);\r\n    }\r\n  };\r\n\r\n  // Focus input when editing starts\r\n  useEffect(() => {\r\n    if (isEditing && inputRef.current) {\r\n      inputRef.current.focus();\r\n    }\r\n  }, [isEditing]);\r\n\r\n  // Focus input when adding task\r\n  useEffect(() => {\r\n    if (isAddingTask && newTaskInputRef.current) {\r\n      newTaskInputRef.current.focus();\r\n    }\r\n  }, [isAddingTask]);\r\n\r\n  return (\r\n    <Paper\r\n      elevation={0}\r\n      className={styles.milestoneCard}\r\n      sx={{ padding: '16px' }}\r\n    >\r\n      <Box className={styles.milestoneHeader}>\r\n        <Box sx={{ display: 'flex', alignItems: 'center', flex: 1 }}>\r\n          {/* Expand/Collapse Button */}\r\n          <IconButton\r\n            size=\"small\"\r\n            onClick={handleToggleExpanded}\r\n            sx={{\r\n              mr: 1,\r\n              color: '#666',\r\n              '&:hover': {\r\n                backgroundColor: 'rgba(0, 0, 0, 0.04)'\r\n              }\r\n            }}\r\n          >\r\n            <Iconify\r\n              icon={isExpanded ? \"material-symbols:expand-less\" : \"material-symbols:expand-more\"}\r\n              width={20}\r\n              height={20}\r\n            />\r\n          </IconButton>\r\n\r\n          {isEditing ? (\r\n            <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>\r\n              <Iconify icon=\"material-symbols:flag\" width={20} height={20} color={mainYellowColor} sx={{ mr: 1 }} />\r\n              <TextField\r\n                inputRef={inputRef}\r\n                value={milestoneName}\r\n                onChange={(e) => setMilestoneName(e.target.value)}\r\n                onKeyDown={handleKeyPress}\r\n                onBlur={handleSave}\r\n                variant=\"standard\"\r\n                fullWidth\r\n                autoFocus\r\n                sx={{\r\n                  fontFamily: '\"Recursive Variable\", sans-serif',\r\n                  fontWeight: 600,\r\n                  fontSize: '1.1rem',\r\n                  '& .MuiInputBase-input': {\r\n                    fontWeight: 600,\r\n                    fontSize: '1.1rem',\r\n                    fontFamily: '\"Recursive Variable\", sans-serif',\r\n                  }\r\n                }}\r\n              />\r\n              <IconButton size=\"small\" onClick={handleSave} sx={{ ml: 1 }}>\r\n                <Iconify icon=\"material-symbols:check\" width={20} height={20} color=\"#4CAF50\" />\r\n              </IconButton>\r\n            </Box>\r\n          ) : (\r\n            <Typography\r\n              variant=\"h6\"\r\n              className={styles.milestoneTitle}\r\n              sx={{\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                display: 'flex',\r\n                alignItems: 'center',\r\n                cursor: 'pointer',\r\n                flex: 1,\r\n                '&:hover': {\r\n                  '& .edit-icon': {\r\n                    opacity: 1,\r\n                  }\r\n                }\r\n              }}\r\n              onClick={handleEditClick}\r\n            >\r\n              <Iconify icon=\"material-symbols:flag\" width={20} height={20} color={mainYellowColor} />\r\n              {milestoneName}\r\n              <Tooltip title=\"Edit milestone name\">\r\n                <IconButton\r\n                  size=\"small\"\r\n                  className=\"edit-icon\"\r\n                  sx={{\r\n                    ml: 1,\r\n                    opacity: 0,\r\n                    transition: 'opacity 0.2s',\r\n                    padding: '2px'\r\n                  }}\r\n                >\r\n                  <Iconify icon=\"material-symbols:edit-outline\" width={16} height={16} color=\"#666\" />\r\n                </IconButton>\r\n              </Tooltip>\r\n            </Typography>\r\n          )}\r\n        </Box>\r\n\r\n        <Chip\r\n          icon={<Iconify icon={statusConfig.icon} width={16} height={16} />}\r\n          label={statusConfig.label}\r\n          size=\"small\"\r\n          sx={{\r\n            backgroundColor: `${statusConfig.color}20`,\r\n            color: statusConfig.color,\r\n            fontWeight: 600,\r\n            borderRadius: '4px',\r\n            fontFamily: '\"Recursive Variable\", sans-serif'\r\n          }}\r\n        />\r\n      </Box>\r\n\r\n      <Collapse in={isExpanded} timeout=\"auto\" unmountOnExit>\r\n        {/* Description Section */}\r\n        {!compact && hasDescription && (\r\n          <Box sx={{ mb: 2, mt: 1.5 }}>\r\n            <Box\r\n              sx={{\r\n                backgroundColor: '#f9f9f9',\r\n                p: 1.5,\r\n                borderRadius: '8px',\r\n                border: '1px solid #f0f0f0'\r\n              }}\r\n            >\r\n              <Typography\r\n                variant=\"body2\"\r\n                sx={{\r\n                  color: '#333',\r\n                  fontFamily: '\"Recursive Variable\", sans-serif',\r\n                  whiteSpace: 'pre-line',\r\n                  lineHeight: 1.6,\r\n                  fontWeight: 500\r\n                }}\r\n              >\r\n                {milestone.description}\r\n              </Typography>\r\n            </Box>\r\n          </Box>\r\n        )}\r\n\r\n        <Divider sx={{ my: 2, opacity: 0.6 }} />\r\n\r\n      <Box sx={{ mb: 2 }}>\r\n        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>\r\n          <Typography variant=\"body2\" sx={{ fontWeight: 600, fontFamily: '\"Recursive Variable\", sans-serif', color: '#333' }}>\r\n            Progress\r\n          </Typography>\r\n          <Typography variant=\"body2\" sx={{ fontWeight: 700, fontFamily: '\"Recursive Variable\", sans-serif', color: '#333' }}>\r\n            {progress}%\r\n          </Typography>\r\n        </Box>\r\n        <LinearProgress\r\n          variant=\"determinate\"\r\n          value={progress}\r\n          sx={{\r\n            height: 6,\r\n            borderRadius: 3,\r\n            backgroundColor: '#f0f0f0',\r\n            '& .MuiLinearProgress-bar': {\r\n              backgroundColor: statusConfig.color\r\n            }\r\n          }}\r\n        />\r\n      </Box>\r\n\r\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>\r\n        {milestone.estimated_duration && (\r\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n            <Iconify icon=\"material-symbols:timer\" width={16} height={16} color=\"#333\" />\r\n            <Typography variant=\"body2\" sx={{ fontFamily: '\"Recursive Variable\", sans-serif', color: '#333', fontWeight: 500 }}>\r\n              {milestone.estimated_duration}\r\n            </Typography>\r\n          </Box>\r\n        )}\r\n      </Box>\r\n\r\n      {/* Tasks Section */}\r\n      <Box sx={{ mb: 1 }}>\r\n        <Box\r\n          sx={{\r\n            display: 'flex',\r\n            alignItems: 'center',\r\n            justifyContent: 'space-between',\r\n            mb: 1\r\n          }}\r\n        >\r\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n            <Iconify\r\n              icon=\"material-symbols:checklist\"\r\n              width={16}\r\n              height={16}\r\n              sx={{ color: '#333' }}\r\n            />\r\n            <Typography\r\n              variant=\"body2\"\r\n              sx={{\r\n                fontWeight: 600,\r\n                color: '#333',\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                fontSize: '0.9rem'\r\n              }}\r\n            >\r\n              Tasks\r\n            </Typography>\r\n          </Box>\r\n\r\n          {/* Add task button */}\r\n          <Tooltip title=\"Add new task\">\r\n            <IconButton\r\n              size=\"small\"\r\n              onClick={handleAddTaskClick}\r\n              sx={{\r\n                color: mainYellowColor,\r\n                '&:hover': {\r\n                  backgroundColor: `${mainYellowColor}10`\r\n                }\r\n              }}\r\n            >\r\n              <Iconify icon=\"material-symbols:add-task\" width={18} height={18} />\r\n            </IconButton>\r\n          </Tooltip>\r\n        </Box>\r\n\r\n        {compact ? (\r\n          <Box className={styles.taskList}>\r\n            {milestone.tasks?.slice(0, 2).map((task, index) => (\r\n              <TaskItem\r\n                key={index}\r\n                task={task}\r\n                showSubtasks={showSubtasks}\r\n                compact={true}\r\n                calculateTaskProgress={calculateTaskProgress}\r\n                getTaskStatus={getTaskStatus}\r\n                calculateSubtaskProgress={calculateSubtaskProgress}\r\n                getSubtaskStatus={getSubtaskStatus}\r\n                onUpdateTask={onUpdateTask}\r\n                onUpdateSubtask={onUpdateSubtask}\r\n                onAddSubtask={onAddSubtask}\r\n                onDeleteTask={onDeleteTask}\r\n                onDeleteSubtask={onDeleteSubtask}\r\n                onAssignMembers={onAssignMembers}\r\n                invitedUsers={invitedUsers}\r\n                planOwner={planOwner}\r\n              />\r\n            ))}\r\n\r\n            {milestone.tasks?.length > 2 && (\r\n              <Button\r\n                variant=\"text\"\r\n                size=\"small\"\r\n                sx={{\r\n                  color: mainYellowColor,\r\n                  fontWeight: 600,\r\n                  textTransform: 'none',\r\n                  p: 0,\r\n                  mt: 1,\r\n                  fontFamily: '\"Recursive Variable\", sans-serif'\r\n                }}\r\n              >\r\n                + {milestone.tasks.length - 2} more tasks\r\n              </Button>\r\n            )}\r\n          </Box>\r\n        ) : (\r\n          <Box className={styles.taskList}>\r\n            {milestone.tasks?.map((task, index) => (\r\n              <TaskItem\r\n                key={index}\r\n                task={task}\r\n                showSubtasks={showSubtasks}\r\n                compact={false}\r\n                calculateTaskProgress={calculateTaskProgress}\r\n                getTaskStatus={getTaskStatus}\r\n                calculateSubtaskProgress={calculateSubtaskProgress}\r\n                getSubtaskStatus={getSubtaskStatus}\r\n                onUpdateTask={onUpdateTask}\r\n                onUpdateSubtask={onUpdateSubtask}\r\n                onAddSubtask={onAddSubtask}\r\n                onDeleteTask={onDeleteTask}\r\n                onDeleteSubtask={onDeleteSubtask}\r\n                onAssignMembers={onAssignMembers}\r\n                invitedUsers={invitedUsers}\r\n                planOwner={planOwner}\r\n              />\r\n            ))}\r\n\r\n            {/* New task input field */}\r\n            {isAddingTask && (\r\n              <Box\r\n                className={styles.taskItem}\r\n                sx={{\r\n                  position: 'relative',\r\n                  mt: 1,\r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  backgroundColor: '#f9f9f9',\r\n                  borderRadius: '6px',\r\n                  padding: '8px 12px',\r\n                  border: '1px solid #f0f0f0'\r\n                }}\r\n              >\r\n                <Box\r\n                  sx={{\r\n                    width: 10,\r\n                    height: 10,\r\n                    borderRadius: '50%',\r\n                    backgroundColor: '#CCCCCC',\r\n                    mr: 1.5,\r\n                    flexShrink: 0\r\n                  }}\r\n                />\r\n\r\n                <TextField\r\n                  inputRef={newTaskInputRef}\r\n                  value={newTaskName}\r\n                  onChange={(e) => setNewTaskName(e.target.value)}\r\n                  onKeyDown={handleNewTaskKeyPress}\r\n                  placeholder=\"Enter new task name...\"\r\n                  variant=\"standard\"\r\n                  fullWidth\r\n                  autoFocus\r\n                  sx={{\r\n                    fontFamily: '\"Recursive Variable\", sans-serif',\r\n                    '& .MuiInputBase-input': {\r\n                      fontSize: '0.9rem',\r\n                      fontFamily: '\"Recursive Variable\", sans-serif',\r\n                      padding: '4px 0'\r\n                    },\r\n                    '& .MuiInput-underline:before': {\r\n                      borderBottomColor: 'rgba(0, 0, 0, 0.1)'\r\n                    }\r\n                  }}\r\n                />\r\n\r\n                <Box sx={{ display: 'flex', alignItems: 'center', ml: 'auto' }}>\r\n                  <IconButton size=\"small\" onClick={handleSaveNewTask} sx={{ ml: 1 }}>\r\n                    <Iconify icon=\"material-symbols:check\" width={18} height={18} color=\"#4CAF50\" />\r\n                  </IconButton>\r\n\r\n                  <IconButton size=\"small\" onClick={() => setIsAddingTask(false)} sx={{ ml: 0.5 }}>\r\n                    <Iconify icon=\"material-symbols:close\" width={18} height={18} color=\"#F44336\" />\r\n                  </IconButton>\r\n                </Box>\r\n              </Box>\r\n            )}\r\n          </Box>\r\n        )}\r\n      </Box>\r\n      </Collapse>\r\n    </Paper>\r\n  );\r\n};\r\n// Component to display task and subtask\r\nconst TaskItem = ({\r\n  task,\r\n  showSubtasks = false,\r\n  compact = false,\r\n  calculateTaskProgress,\r\n  getTaskStatus,\r\n  calculateSubtaskProgress,\r\n  getSubtaskStatus,\r\n  onUpdateTask,\r\n  onUpdateSubtask,\r\n  onAddSubtask,\r\n  onDeleteTask,\r\n  onDeleteSubtask,\r\n  onAssignMembers,\r\n  invitedUsers,\r\n  planOwner\r\n}) => {\r\n  const [isEditing, setIsEditing] = useState(false);\r\n  const [taskName, setTaskName] = useState(task.name);\r\n  const taskInputRef = useRef(null);\r\n  const [isAddingSubtask, setIsAddingSubtask] = useState(false);\r\n  const [newSubtaskName, setNewSubtaskName] = useState('');\r\n  const newSubtaskInputRef = useRef(null);\r\n\r\n  // Comment functionality\r\n  const [commentDialogOpen, setCommentDialogOpen] = useState(false);\r\n  const [comments, setComments] = useState([]);\r\n  const [loadingComments, setLoadingComments] = useState(false);\r\n\r\n  // Enable due date functionality\r\n  const [dueDateDialogOpen, setDueDateDialogOpen] = useState(false);\r\n  const [updatingDueDate, setUpdatingDueDate] = useState(false);\r\n\r\n  // Add state for assign member dialog\r\n  const [assignMemberDialogOpen, setAssignMemberDialogOpen] = useState(false);\r\n\r\n  // Add state to store current task information\r\n  const [localTask, setLocalTask] = useState(task);\r\n\r\n  // Update localTask when task changes from props\r\n  useEffect(() => {\r\n    setLocalTask(task);\r\n  }, [task]);\r\n\r\n  const handleOpenComments = (e) => {\r\n    e.stopPropagation();\r\n    setCommentDialogOpen(true);\r\n    setLoadingComments(true);\r\n    getComments(task.id)\r\n      .then(response => {\r\n        console.log('Comments data:', response.data);\r\n        // Check data structure and get correct comments array\r\n        let commentsData = [];\r\n        if (Array.isArray(response.data)) {\r\n          commentsData = response.data;\r\n        } else if (response.data?.data && Array.isArray(response.data.data)) {\r\n          commentsData = response.data.data;\r\n        } else if (response.data?.comments && Array.isArray(response.data.comments)) {\r\n          commentsData = response.data.comments;\r\n        }\r\n        setComments(commentsData);\r\n      })\r\n      .catch(error => {\r\n        console.error('Error fetching comments:', error);\r\n        toast.error('Failed to load comments');\r\n      })\r\n      .finally(() => {\r\n        setLoadingComments(false);\r\n      });\r\n  };\r\n\r\n  const handleAddComment = async (content) => {\r\n    try {\r\n      const response = await addComment(task.id, content);\r\n      console.log('Add comment response:', response);\r\n\r\n      // Create new comment from response or create temporary object\r\n      let newComment;\r\n      if (response.data?.data) {\r\n        newComment = response.data.data;\r\n      } else {\r\n        // Create temporary object if API does not return new comment\r\n        const currentUser = JSON.parse(localStorage.getItem('user')) || {};\r\n        newComment = {\r\n          id: Date.now(), // Temporary ID\r\n          content: content,\r\n          user: {\r\n            id: currentUser.id,\r\n            first_name: currentUser.first_name,\r\n            last_name: currentUser.last_name,\r\n            avatar: currentUser.avatar\r\n          },\r\n          created_at: new Date().toLocaleString('en-US', {\r\n            hour: '2-digit',\r\n            minute: '2-digit',\r\n            day: '2-digit',\r\n            month: '2-digit'\r\n          }).replace(',', '')\r\n        };\r\n      }\r\n\r\n      // Update state\r\n      setComments(prevComments => [...prevComments, newComment]);\r\n\r\n      // Refresh comments to ensure latest data\r\n      refreshComments();\r\n    } catch (error) {\r\n      console.error('Error adding comment:', error);\r\n      toast.error('Failed to add comment');\r\n    }\r\n  };\r\n\r\n  const handleUpdateComment = async (commentId, content) => {\r\n    try {\r\n      // Update UI first\r\n      const updatedCommentTemp = comments.find(c => c.id === commentId);\r\n      if (updatedCommentTemp) {\r\n        const updatedComments = comments.map(c =>\r\n          c.id === commentId ? { ...c, content: content } : c\r\n        );\r\n        setComments(updatedComments);\r\n      }\r\n\r\n      // Call API\r\n      const response = await updateComment(commentId, content);\r\n      console.log('Update comment response:', response);\r\n\r\n      toast.success('Comment updated successfully');\r\n\r\n      // Refresh comments to ensure latest data\r\n      refreshComments();\r\n    } catch (error) {\r\n      console.error('Error updating comment:', error);\r\n      toast.error('Failed to update comment');\r\n      // Refresh comments to restore original state if there is an error\r\n      refreshComments();\r\n    }\r\n  };\r\n\r\n  const handleDeleteComment = async (commentId) => {\r\n    try {\r\n      // Update UI first\r\n      setComments(prevComments => prevComments.filter(c => c.id !== commentId));\r\n\r\n      // Call API\r\n      await deleteComment(commentId);\r\n      console.log('Comment deleted successfully');\r\n\r\n      toast.success('Comment deleted successfully');\r\n\r\n      // Refresh comments to ensure latest data\r\n      refreshComments();\r\n    } catch (error) {\r\n      console.error('Error deleting comment:', error);\r\n      toast.error('Failed to delete comment');\r\n      // Refresh comments to restore original state if there is an error\r\n      refreshComments();\r\n    }\r\n  };\r\n\r\n  // Refresh comments function\r\n  const refreshComments = async () => {\r\n    try {\r\n      const response = await getComments(task.id);\r\n      console.log('Refreshed comments:', response.data);\r\n\r\n      // Handle returned data\r\n      let commentsData = [];\r\n      if (Array.isArray(response.data)) {\r\n        commentsData = response.data;\r\n      } else if (response.data?.data && Array.isArray(response.data.data)) {\r\n        commentsData = response.data.data;\r\n      } else if (response.data?.comments && Array.isArray(response.data.comments)) {\r\n        commentsData = response.data.comments;\r\n      }\r\n\r\n      setComments(commentsData);\r\n    } catch (error) {\r\n      console.error('Error refreshing comments:', error);\r\n    }\r\n  };\r\n\r\n  // Calculate task progress\r\n  const taskProgress = calculateTaskProgress ? calculateTaskProgress(task) : 0;\r\n\r\n  // Determine task status\r\n  const taskStatus = getTaskStatus ? getTaskStatus(task) : (task.status || STATUS.NOT_STARTED);\r\n  const statusConfig = STATUS_CONFIG[taskStatus] || STATUS_CONFIG[STATUS.NOT_STARTED];\r\n\r\n  // Check if task has subtasks\r\n  const hasSubtasks = task.subtasks && task.subtasks.length > 0;\r\n\r\n  // Check if task is completed\r\n  const isCompleted = taskStatus === STATUS.COMPLETED;\r\n\r\n  // Handle edit mode for task\r\n  const handleTaskEditClick = () => {\r\n    setIsEditing(true);\r\n  };\r\n\r\n  // Handle save changes for task\r\n  const handleTaskSave = () => {\r\n    // Trim the name to remove leading/trailing whitespace\r\n    const trimmedName = taskName.trim();\r\n\r\n    // Only update if the name has actually changed (after trimming)\r\n    if (trimmedName !== '' && trimmedName !== task.name) {\r\n      if (onUpdateTask) {\r\n        onUpdateTask({ ...task, name: trimmedName });\r\n      }\r\n    } else {\r\n      // Reset to original if empty or unchanged\r\n      setTaskName(task.name);\r\n    }\r\n\r\n    setIsEditing(false);\r\n  };\r\n\r\n  // Handle key press events for task\r\n  const handleTaskKeyPress = (e) => {\r\n    if (e.key === 'Enter') {\r\n      handleTaskSave();\r\n    } else if (e.key === 'Escape') {\r\n      setTaskName(task.name);\r\n      setIsEditing(false);\r\n    }\r\n  };\r\n\r\n  // Handle add subtask\r\n  const handleAddSubtaskClick = () => {\r\n    setIsAddingSubtask(true);\r\n  };\r\n\r\n  // Handle delete task\r\n  const handleDeleteTaskClick = () => {\r\n    if (onDeleteTask) {\r\n      onDeleteTask(task);\r\n    }\r\n  };\r\n\r\n  // Handle save new subtask\r\n  const handleSaveNewSubtask = () => {\r\n    const trimmedName = newSubtaskName.trim();\r\n    if (trimmedName && onAddSubtask) {\r\n      const newSubtask = {\r\n        name: trimmedName,\r\n        task: task.slug,\r\n        status: STATUS.NOT_STARTED,\r\n        progress: 0\r\n      };\r\n      onAddSubtask(newSubtask);\r\n      setNewSubtaskName('');\r\n    }\r\n    setIsAddingSubtask(false);\r\n  };\r\n\r\n  // Handle key press events for new subtask\r\n  const handleNewSubtaskKeyPress = (e) => {\r\n    if (e.key === 'Enter') {\r\n      handleSaveNewSubtask();\r\n    } else if (e.key === 'Escape') {\r\n      setNewSubtaskName('');\r\n      setIsAddingSubtask(false);\r\n    }\r\n  };\r\n\r\n  // Focus input when editing starts\r\n  useEffect(() => {\r\n    if (isEditing && taskInputRef.current) {\r\n      taskInputRef.current.focus();\r\n    }\r\n  }, [isEditing]);\r\n\r\n  // Focus input when adding subtask\r\n  useEffect(() => {\r\n    if (isAddingSubtask && newSubtaskInputRef.current) {\r\n      newSubtaskInputRef.current.focus();\r\n    }\r\n  }, [isAddingSubtask]);\r\n\r\n  // Check if task has due dates - use localTask instead of task\r\n  const hasDueDates = localTask.start_date || localTask.end_date;\r\n\r\n  const handleOpenDueDateDialog = (e) => {\r\n    e.stopPropagation();\r\n    setDueDateDialogOpen(true);\r\n  };\r\n\r\n  const handleUpdateDueDate = async (updatedTask) => {\r\n    setUpdatingDueDate(true);\r\n    try {\r\n      const taskToUpdate = {\r\n        ...localTask,\r\n        start_date: updatedTask.start_date,\r\n        end_date: updatedTask.end_date,\r\n        progress: localTask.progress || 0,\r\n        status: localTask.status || 1\r\n      };\r\n\r\n      // Update local state first\r\n      setLocalTask(taskToUpdate);\r\n\r\n      // Call API\r\n      const response = await updateTask(taskToUpdate);\r\n\r\n      // If API returns data, update local state\r\n      if (response && response.data) {\r\n        setLocalTask(response.data);\r\n      }\r\n\r\n      // Update state in parent component if necessary\r\n      if (onUpdateTask) {\r\n        onUpdateTask(taskToUpdate);\r\n      }\r\n\r\n      toast.success('Due date updated successfully');\r\n    } catch (error) {\r\n      console.error('Error updating due date:', error);\r\n      toast.error('Failed to update due date');\r\n\r\n      // If there is an error, restore original state\r\n      setLocalTask(task);\r\n    } finally {\r\n      setUpdatingDueDate(false);\r\n      setDueDateDialogOpen(false); // Close dialog after completion\r\n    }\r\n  };\r\n\r\n  // Add handler for member assignment updates\r\n  const handleAssignMembers = async (assignedUserIds) => {\r\n    try {\r\n      // Convert IDs to numbers\r\n      const numericIds = assignedUserIds.map(id => Number(id));\r\n      // Call API to assign members\r\n      const response = await assignMembersToTask(localTask.slug, numericIds);\r\n      // Create updated task with new assignees\r\n      const updatedTask = {\r\n        ...localTask,\r\n        assignees: response.assignees || numericIds.map(id => ({\r\n          id: id,\r\n          first_name: '',\r\n          last_name: '',\r\n          email: ''\r\n        }))\r\n      };\r\n\r\n      // Update both local state and parent state\r\n      setLocalTask(updatedTask);\r\n      if (onUpdateTask) {\r\n        onUpdateTask(updatedTask);\r\n      }\r\n\r\n      setAssignMemberDialogOpen(false);\r\n      toast.success('Members assigned successfully');\r\n    } catch (error) {\r\n      console.error('Error assigning members:', error);\r\n      toast.error('Failed to assign members');\r\n    }\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Box className={styles.taskItemContainer}>\r\n        <Box\r\n          className={styles.taskItem}\r\n          sx={{ position: 'relative' }}>\r\n          <Box\r\n            sx={{\r\n              width: 10,\r\n              height: 10,\r\n              borderRadius: '50%',\r\n              backgroundColor: statusConfig.color,\r\n              mr: 1.5,\r\n              flexShrink: 0\r\n            }}\r\n          />\r\n\r\n          {isEditing ? (\r\n            <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>\r\n              <TextField\r\n                inputRef={taskInputRef}\r\n                value={taskName}\r\n                onChange={(e) => setTaskName(e.target.value)}\r\n                onKeyDown={handleTaskKeyPress}\r\n                onBlur={handleTaskSave}\r\n                variant=\"standard\"\r\n                fullWidth\r\n                autoFocus\r\n                sx={{\r\n                  fontFamily: '\"Recursive Variable\", sans-serif',\r\n                  '& .MuiInputBase-input': {\r\n                    fontSize: '0.9rem',\r\n                    fontFamily: '\"Recursive Variable\", sans-serif',\r\n                  }\r\n                }}\r\n              />\r\n              <IconButton size=\"small\" onClick={handleTaskSave} sx={{ ml: 1 }}>\r\n                <Iconify icon=\"material-symbols:check\" width={16} height={16} color=\"#4CAF50\" />\r\n              </IconButton>\r\n            </Box>\r\n          ) : (\r\n            <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', gap: 0.5 }}>\r\n              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>\r\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexGrow: 1 }}>\r\n                  <Typography\r\n                    variant=\"body2\"\r\n                    className={styles.taskName}\r\n                    sx={{\r\n                      fontFamily: '\"Recursive Variable\", sans-serif',\r\n                      cursor: 'pointer',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      color: isCompleted ? '#4CAF50' : 'inherit',\r\n                      fontWeight: isCompleted ? 500 : 400,\r\n                      flexGrow: 1,\r\n                      '&:hover': {\r\n                        '& .task-edit-icon': {\r\n                          opacity: 1,\r\n                        }\r\n                      }\r\n                    }}\r\n                    onClick={handleTaskEditClick}>\r\n                    {taskName}\r\n                    <Tooltip title=\"Edit task name\">\r\n                      <IconButton\r\n                        size=\"small\"\r\n                        className=\"task-edit-icon\"\r\n                        sx={{\r\n                          ml: 0.5,\r\n                          opacity: 0,\r\n                          transition: 'opacity 0.2s',\r\n                          padding: '2px'\r\n                        }}\r\n                      >\r\n                        <Iconify icon=\"material-symbols:edit-outline\" width={14} height={14} color=\"#666\" />\r\n                      </IconButton>\r\n                    </Tooltip>\r\n                  </Typography>\r\n                </Box>\r\n\r\n                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>\r\n                  {/* Display assigned members avatars */}\r\n                  {localTask.assignees && localTask.assignees.length > 0 && (\r\n                    <AvatarGroup\r\n                      max={3}\r\n                      sx={{\r\n                        '& .MuiAvatar-root': {\r\n                          width: 24,\r\n                          height: 24,\r\n                          fontSize: '0.75rem',\r\n                          border: '1.5px solid #fff'\r\n                        }\r\n                      }}\r\n                    >\r\n                      {localTask.assignees.map((assignee, index) => (\r\n                        <Tooltip\r\n                          key={assignee.id}\r\n                          title={assignee.first_name && assignee.last_name\r\n                            ? `${assignee.first_name} ${assignee.last_name}`\r\n                            : assignee.email}\r\n                          arrow\r\n                        >\r\n                          <Avatar\r\n                            src={assignee.avatar}\r\n                            alt={assignee.first_name || assignee.email}\r\n                            sx={{\r\n                              bgcolor: mainYellowColor,\r\n                            }}\r\n                          >\r\n                            {assignee.first_name\r\n                              ? assignee.first_name.charAt(0).toUpperCase()\r\n                              : assignee.email\r\n                                ? assignee.email.charAt(0).toUpperCase()\r\n                                : ''}\r\n                          </Avatar>\r\n                        </Tooltip>\r\n                      ))}\r\n                    </AvatarGroup>\r\n                  )}\r\n\r\n                  {/* Hover-expandable toolbar */}\r\n                  <Box\r\n                    className=\"task-toolbar\"\r\n                    sx={{\r\n                      position: 'relative',\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      '&:hover .toolbar-expanded': {\r\n                        opacity: 1,\r\n                        visibility: 'visible',\r\n                        transform: 'translateX(0)',\r\n                      },\r\n                      '&:hover .toolbar-dots': {\r\n                        opacity: 0,\r\n                        visibility: 'hidden',\r\n                      }\r\n                    }}\r\n                  >\r\n                    {/* Three dots button - visible by default */}\r\n                    <Box\r\n                      className=\"toolbar-dots\"\r\n                      sx={{\r\n                        opacity: 1,\r\n                        visibility: 'visible',\r\n                        transition: 'all 0.2s ease',\r\n                        display: 'flex',\r\n                        alignItems: 'center',\r\n                        cursor: 'pointer',\r\n                        padding: '4px 8px',\r\n                        borderRadius: '6px',\r\n                        '&:hover': {\r\n                          backgroundColor: 'rgba(0, 0, 0, 0.04)'\r\n                        }\r\n                      }}\r\n                    >\r\n                      <Iconify icon=\"material-symbols:more-horiz\" width={20} height={20} color=\"#666\" />\r\n                    </Box>\r\n\r\n                    {/* Expanded toolbar - hidden by default */}\r\n                    <Box\r\n                      className=\"toolbar-expanded\"\r\n                      sx={{\r\n                        position: 'absolute',\r\n                        right: 0,\r\n                        top: 0,\r\n                        opacity: 0,\r\n                        visibility: 'hidden',\r\n                        transform: 'translateX(10px)',\r\n                        transition: 'all 0.2s ease',\r\n                        display: 'flex',\r\n                        alignItems: 'center',\r\n                        border: '1px solid #eee',\r\n                        borderRadius: '8px',\r\n                        padding: '2px 4px',\r\n                        background: '#fff',\r\n                        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',\r\n                        zIndex: 10\r\n                      }}\r\n                    >\r\n                      {/* Due Date Button */}\r\n                      <Tooltip title={hasDueDates ? \"Edit due date\" : \"Set due date\"}>\r\n                        <IconButton\r\n                          size=\"small\"\r\n                          onClick={handleOpenDueDateDialog}\r\n                          sx={{\r\n                            color: hasDueDates ? mainYellowColor : '#888',\r\n                            '&:hover': {\r\n                              color: mainYellowColor,\r\n                              bgcolor: 'rgba(255, 193, 7, 0.08)'\r\n                            }\r\n                          }}\r\n                        >\r\n                          <Iconify\r\n                            icon={hasDueDates ? \"material-symbols:calendar-month\" : \"material-symbols:calendar-add-on\"}\r\n                            width={16}\r\n                            height={16}\r\n                          />\r\n                        </IconButton>\r\n                      </Tooltip>\r\n\r\n                      {/* Comment Button */}\r\n                      <Tooltip title=\"Comments\">\r\n                        <IconButton\r\n                          size=\"small\"\r\n                          onClick={handleOpenComments}\r\n                          disabled={loadingComments}\r\n                          sx={{\r\n                            color: mainYellowColor,\r\n                            '&:hover': {\r\n                              bgcolor: 'rgba(255, 193, 7, 0.08)'\r\n                            }\r\n                          }}\r\n                        >\r\n                          {loadingComments ? (\r\n                            <CircularProgress size={16} sx={{ color: mainYellowColor }} />\r\n                          ) : (\r\n                            <Iconify icon=\"material-symbols:comment-outline\" width={16} height={16} />\r\n                          )}\r\n                        </IconButton>\r\n                      </Tooltip>\r\n\r\n                      {/* Assign Member Button */}\r\n                      <Tooltip title=\"Assign members\">\r\n                        <IconButton\r\n                          size=\"small\"\r\n                          onClick={() => setAssignMemberDialogOpen(true)}\r\n                          sx={{\r\n                            color: localTask.assignees?.length > 0 ? mainYellowColor : '#888',\r\n                            '&:hover': {\r\n                              color: mainYellowColor,\r\n                              bgcolor: 'rgba(255, 193, 7, 0.08)'\r\n                            }\r\n                          }}\r\n                        >\r\n                          <Iconify\r\n                            icon=\"mdi:account-multiple-plus\"\r\n                            width={16}\r\n                            height={16}\r\n                          />\r\n                        </IconButton>\r\n                      </Tooltip>\r\n\r\n                      {/* Add subtask button */}\r\n                      <Tooltip title=\"Add subtask\">\r\n                        <IconButton\r\n                          size=\"small\"\r\n                          onClick={handleAddSubtaskClick}\r\n                          sx={{\r\n                            color: mainYellowColor,\r\n                            '&:hover': {\r\n                              bgcolor: 'rgba(255, 193, 7, 0.08)'\r\n                            }\r\n                          }}\r\n                        >\r\n                          <Iconify icon=\"material-symbols:add-task\" width={16} height={16} />\r\n                        </IconButton>\r\n                      </Tooltip>\r\n\r\n                      {/* Edit Task Button */}\r\n                      <Tooltip title=\"Edit task\">\r\n                        <IconButton\r\n                          size=\"small\"\r\n                          onClick={handleTaskEditClick}\r\n                          sx={{\r\n                            color: '#666',\r\n                            '&:hover': {\r\n                              bgcolor: 'rgba(0, 0, 0, 0.04)'\r\n                            }\r\n                          }}\r\n                        >\r\n                          <Iconify icon=\"eva:edit-fill\" width={16} height={16} />\r\n                        </IconButton>\r\n                      </Tooltip>\r\n\r\n                      {/* Delete task button */}\r\n                      <Tooltip title=\"Delete task\">\r\n                        <IconButton\r\n                          size=\"small\"\r\n                          onClick={handleDeleteTaskClick}\r\n                          sx={{\r\n                            color: '#F44336',\r\n                            '&:hover': {\r\n                              bgcolor: 'rgba(244, 67, 54, 0.08)'\r\n                            }\r\n                          }}\r\n                        >\r\n                          <Iconify icon=\"material-symbols:delete-outline\" width={16} height={16} />\r\n                        </IconButton>\r\n                      </Tooltip>\r\n                    </Box>\r\n                  </Box>\r\n\r\n                  {/* Display task progress */}\r\n                  <Box\r\n                    sx={{\r\n                      display: 'flex',\r\n                      alignItems: 'center',\r\n                      bgcolor: statusConfig.color + '20',\r\n                      px: 1,\r\n                      py: 0.5,\r\n                      borderRadius: '4px',\r\n                    }}\r\n                  >\r\n                    <Typography\r\n                      variant=\"caption\"\r\n                      sx={{\r\n                        fontWeight: 600,\r\n                        color: statusConfig.color,\r\n                        fontFamily: '\"Recursive Variable\", sans-serif'\r\n                      }}\r\n                    >\r\n                      {taskProgress}%\r\n                    </Typography>\r\n                  </Box>\r\n                </Box>\r\n              </Box>\r\n\r\n              {/* Due date display below task name */}\r\n              {hasDueDates && (\r\n                <Box\r\n                  sx={{\r\n                    display: 'flex',\r\n                    alignItems: 'center',\r\n                    backgroundColor: `${mainYellowColor}08`,\r\n                    borderRadius: '4px',\r\n                    py: 0.5,\r\n                    px: 0.75,\r\n                    width: 'fit-content'\r\n                  }}\r\n                >\r\n                  <Iconify\r\n                    icon=\"material-symbols:calendar-month\"\r\n                    width={14}\r\n                    height={14}\r\n                    color={mainYellowColor} />\r\n                  <Typography\r\n                    variant=\"caption\"\r\n                    sx={{\r\n                      fontFamily: '\"Recursive Variable\", sans-serif',\r\n                      fontSize: '0.75rem',\r\n                      color: '#555',\r\n                      fontWeight: 500,\r\n                      lineHeight: 1,\r\n                      ml: 0.5\r\n                    }}>\r\n                    {formatDate(localTask.start_date)} ~ {formatDate(localTask.end_date)}\r\n                  </Typography>\r\n                </Box>\r\n              )}\r\n            </Box>\r\n          )}\r\n        </Box>\r\n\r\n        <Box\r\n          sx={{\r\n            pl: 4,\r\n            pr: 1,\r\n            pt: 0.5,\r\n            pb: 0.5,\r\n            borderLeft: `1px dashed ${statusConfig.color}`,\r\n            ml: 1.5,\r\n            mt: 0.5,\r\n            display: (hasSubtasks || isAddingSubtask) && showSubtasks ? 'block' : 'none'\r\n          }}\r\n        >\r\n          {task.subtasks && task.subtasks.map((subtask, index) => (\r\n            <SubtaskItem\r\n              key={index}\r\n              subtask={subtask}\r\n              index={index}\r\n              totalSubtasks={task.subtasks.length}\r\n              taskSlug={task.slug}\r\n              calculateSubtaskProgress={calculateSubtaskProgress}\r\n              getSubtaskStatus={getSubtaskStatus}\r\n              onUpdateSubtask={onUpdateSubtask}\r\n              onDeleteSubtask={onDeleteSubtask}\r\n              onAssignMembers={onAssignMembers}\r\n              invitedUsers={invitedUsers}\r\n              planOwner={planOwner}\r\n            />\r\n          ))}\r\n\r\n          {/* New subtask input field */}\r\n          {isAddingSubtask && (\r\n            <Box\r\n              sx={{\r\n                display: 'flex',\r\n                alignItems: 'center',\r\n                py: 0.5,\r\n                borderBottom: '1px dotted #f0f0f0',\r\n                backgroundColor: '#f9f9f9',\r\n                borderRadius: '4px',\r\n                px: 1\r\n              }}\r\n            >\r\n              <Checkbox\r\n                disabled\r\n                size=\"small\"\r\n                sx={{\r\n                  p: 0.5,\r\n                  mr: 0.5,\r\n                  color: '#CCCCCC'\r\n                }}\r\n                icon={<Iconify icon=\"material-symbols:check-box-outline-blank\" width={18} height={18} />}\r\n              />\r\n\r\n              <TextField\r\n                inputRef={newSubtaskInputRef}\r\n                value={newSubtaskName}\r\n                onChange={(e) => setNewSubtaskName(e.target.value)}\r\n                onKeyDown={handleNewSubtaskKeyPress}\r\n                placeholder=\"Enter new subtask name...\"\r\n                variant=\"standard\"\r\n                fullWidth\r\n                autoFocus\r\n                sx={{\r\n                  fontFamily: '\"Recursive Variable\", sans-serif',\r\n                  '& .MuiInputBase-input': {\r\n                    fontSize: '0.85rem',\r\n                    fontFamily: '\"Recursive Variable\", sans-serif',\r\n                    padding: '4px 0'\r\n                  },\r\n                  '& .MuiInput-underline:before': {\r\n                    borderBottomColor: 'rgba(0, 0, 0, 0.1)'\r\n                  }\r\n                }}\r\n              />\r\n\r\n              <Box sx={{ display: 'flex', ml: 1 }}>\r\n                <IconButton size=\"small\" onClick={handleSaveNewSubtask} sx={{ p: 0.5 }}>\r\n                  <Iconify icon=\"material-symbols:check\" width={18} height={18} color=\"#4CAF50\" />\r\n                </IconButton>\r\n\r\n                <IconButton size=\"small\" onClick={() => setIsAddingSubtask(false)} sx={{ p: 0.5, ml: 0.5 }}>\r\n                  <Iconify icon=\"material-symbols:close\" width={18} height={18} color=\"#F44336\" />\r\n                </IconButton>\r\n              </Box>\r\n            </Box>\r\n          )}\r\n        </Box>\r\n      </Box>\r\n\r\n      {/* Comment Dialog */}\r\n      <CommentDialog\r\n        open={commentDialogOpen}\r\n        onClose={() => setCommentDialogOpen(false)}\r\n        comments={comments}\r\n        onAddComment={handleAddComment}\r\n        onUpdateComment={handleUpdateComment}\r\n        onDeleteComment={handleDeleteComment}\r\n        loading={loadingComments}\r\n        targetName={task.name}\r\n        targetType=\"task\"\r\n      />\r\n\r\n      {/* Due Date Dialog */}\r\n      <DueDateDialog\r\n        open={dueDateDialogOpen}\r\n        onClose={() => setDueDateDialogOpen(false)}\r\n        task={localTask}\r\n        onUpdateDueDate={handleUpdateDueDate}\r\n        isUpdating={updatingDueDate}\r\n      />\r\n\r\n      {/* Assign Member Dialog */}\r\n      <AssignMemberDialog\r\n        open={assignMemberDialogOpen}\r\n        onClose={() => setAssignMemberDialogOpen(false)}\r\n        task={localTask}\r\n        invitedUsers={invitedUsers}\r\n        planOwner={planOwner}\r\n        onAssignMembers={handleAssignMembers}\r\n      />\r\n    </>\r\n  );\r\n};\r\n\r\n// Component for subtask\r\nconst SubtaskItem = ({\r\n  subtask,\r\n  index,\r\n  totalSubtasks,\r\n  taskSlug,\r\n  calculateSubtaskProgress,\r\n  getSubtaskStatus,\r\n  onUpdateSubtask,\r\n  onDeleteSubtask,\r\n  onAssignMembers,\r\n  invitedUsers,\r\n  planOwner\r\n}) => {\r\n  const [isEditing, setIsEditing] = useState(false);\r\n  const [subtaskName, setSubtaskName] = useState(subtask.name);\r\n  const [isChecked, setIsChecked] = useState(subtask.status === STATUS.COMPLETED || subtask.progress === 100);\r\n  const subtaskInputRef = useRef(null);\r\n\r\n  // Calculate subtask progress and status\r\n  const subtaskProgress = calculateSubtaskProgress ? calculateSubtaskProgress(subtask) : 0;\r\n  const subtaskStatus = getSubtaskStatus ? getSubtaskStatus(subtask) : (subtask.status || STATUS.NOT_STARTED);\r\n  const subtaskStatusConfig = STATUS_CONFIG[subtaskStatus] || STATUS_CONFIG[STATUS.NOT_STARTED];\r\n\r\n  // Update isChecked when subtask changes from outside\r\n  useEffect(() => {\r\n    setIsChecked(subtask.status === STATUS.COMPLETED || subtask.progress === 100);\r\n  }, [subtask.status, subtask.progress]);\r\n\r\n  // Handle edit mode for subtask\r\n  const handleSubtaskEditClick = () => {\r\n    setIsEditing(true);\r\n  };\r\n\r\n  // Handle save changes for subtask\r\n  const handleSubtaskSave = () => {\r\n    // Trim the name to remove leading/trailing whitespace\r\n    const trimmedName = subtaskName.trim();\r\n\r\n    // Only update if the name has actually changed (after trimming)\r\n    if (trimmedName !== '' && trimmedName !== subtask.name) {\r\n      if (onUpdateSubtask) {\r\n        onUpdateSubtask({ ...subtask, name: trimmedName, task: taskSlug });\r\n      }\r\n    } else {\r\n      // Reset to original if empty or unchanged\r\n      setSubtaskName(subtask.name);\r\n    }\r\n\r\n    setIsEditing(false);\r\n  };\r\n\r\n  // Handle key press events for subtask\r\n  const handleSubtaskKeyPress = (e) => {\r\n    if (e.key === 'Enter') {\r\n      handleSubtaskSave();\r\n    } else if (e.key === 'Escape') {\r\n      setSubtaskName(subtask.name);\r\n      setIsEditing(false);\r\n    }\r\n  };\r\n\r\n  // Handle status toggle\r\n  const handleStatusToggle = () => {\r\n    // Update UI immediately to provide user feedback\r\n    const newCheckedState = !isChecked;\r\n    setIsChecked(newCheckedState);\r\n\r\n    const newStatus = newCheckedState ? STATUS.COMPLETED : STATUS.NOT_STARTED;\r\n    const newProgress = newCheckedState ? 100 : 0;\r\n\r\n    if (onUpdateSubtask) {\r\n      // Ensure to send both status and progress, even when progress = 0\r\n      const updatedData = {\r\n        ...subtask,\r\n        status: newStatus,\r\n        progress: newProgress,\r\n        task: taskSlug\r\n      };\r\n\r\n      console.log('Sending subtask update:', updatedData);\r\n      onUpdateSubtask(updatedData);\r\n    }\r\n  };\r\n\r\n  // Handle delete subtask\r\n  const handleDeleteSubtaskClick = () => {\r\n    if (window.confirm(`Are you sure you want to delete this subtask?`)) {\r\n      onDeleteSubtask(subtask, taskSlug);\r\n    }\r\n  };\r\n\r\n  // Focus input when editing starts\r\n  useEffect(() => {\r\n    if (isEditing && subtaskInputRef.current) {\r\n      subtaskInputRef.current.focus();\r\n    }\r\n  }, [isEditing]);\r\n\r\n  return (\r\n    <Box\r\n      sx={{\r\n        display: 'flex',\r\n        alignItems: 'center',\r\n        py: 0.5,\r\n        borderBottom: index < totalSubtasks - 1 ? '1px dotted #f0f0f0' : 'none'\r\n      }}\r\n    >\r\n      <Checkbox\r\n        checked={isChecked}\r\n        onChange={handleStatusToggle}\r\n        size=\"small\"\r\n        sx={{\r\n          p: 0.5,\r\n          mr: 0.5,\r\n          color: '#CCCCCC',\r\n          '&.Mui-checked': {\r\n            color: '#4CAF50',\r\n          }\r\n        }}\r\n        icon={<Iconify icon=\"material-symbols:check-box-outline-blank\" width={18} height={18} />}\r\n        checkedIcon={<Iconify icon=\"material-symbols:check-box\" width={18} height={18} />}\r\n      />\r\n\r\n      {isEditing ? (\r\n        <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>\r\n          <TextField\r\n            inputRef={subtaskInputRef}\r\n            value={subtaskName}\r\n            onChange={(e) => setSubtaskName(e.target.value)}\r\n            onKeyDown={handleSubtaskKeyPress}\r\n            onBlur={handleSubtaskSave}\r\n            variant=\"standard\"\r\n            fullWidth\r\n            autoFocus\r\n            sx={{\r\n              fontFamily: '\"Recursive Variable\", sans-serif',\r\n              '& .MuiInputBase-input': {\r\n                fontSize: '0.85rem',\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n              }\r\n            }}\r\n          />\r\n          <IconButton size=\"small\" onClick={handleSubtaskSave} sx={{ ml: 1 }}>\r\n            <Iconify icon=\"material-symbols:check\" width={14} height={14} color=\"#4CAF50\" />\r\n          </IconButton>\r\n        </Box>\r\n      ) : (\r\n        <Typography\r\n          variant=\"body2\"\r\n          sx={{\r\n            flexGrow: 1,\r\n            fontSize: '0.85rem',\r\n            fontFamily: '\"Recursive Variable\", sans-serif',\r\n            color: isChecked ? '#4CAF50' : '#555',\r\n            fontWeight: isChecked ? 500 : 400,\r\n            cursor: 'pointer',\r\n            display: 'flex',\r\n            alignItems: 'center',\r\n            '&:hover': {\r\n              '& .subtask-edit-icon': {\r\n                opacity: 1,\r\n              }\r\n            }\r\n          }}\r\n          onClick={handleSubtaskEditClick}\r\n        >\r\n          {subtaskName}\r\n          <Tooltip title=\"Edit subtask name\">\r\n            <IconButton\r\n              size=\"small\"\r\n              className=\"subtask-edit-icon\"\r\n              sx={{\r\n                ml: 0.5,\r\n                opacity: 0,\r\n                transition: 'opacity 0.2s',\r\n                padding: '1px'\r\n              }}\r\n            >\r\n              <Iconify icon=\"material-symbols:edit-outline\" width={12} height={12} color=\"#666\" />\r\n            </IconButton>\r\n          </Tooltip>\r\n        </Typography>\r\n      )}\r\n\r\n      <Box sx={{ display: 'flex', alignItems: 'center' }}>\r\n        {/* Delete subtask button */}\r\n        <Tooltip title=\"Delete subtask\">\r\n          <IconButton\r\n            size=\"small\"\r\n            onClick={handleDeleteSubtaskClick}\r\n            sx={{\r\n              p: 0.5,\r\n              color: '#F44336',\r\n              '&:hover': {\r\n                backgroundColor: '#FFEBEE'\r\n              }\r\n            }}\r\n          >\r\n            <Iconify icon=\"material-symbols:delete-outline\" width={14} height={14} />\r\n          </IconButton>\r\n        </Tooltip>\r\n      </Box>\r\n    </Box>\r\n  );\r\n};\r\n\r\n// Format date for display\r\nfunction formatDate(dateString) {\r\n  if (!dateString) return '';\r\n\r\n  try {\r\n    const date = new Date(dateString);\r\n    if (isNaN(date.getTime())) return '';\r\n\r\n    // Only display day and month, not year\r\n    const options = { month: 'short', day: 'numeric' };\r\n    return date.toLocaleDateString('en-US', options);\r\n  } catch (error) {\r\n    return '';\r\n  }\r\n}\r\n\r\nexport default MilestoneCard;\r\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SACEC,GAAG,EACHC,UAAU,EACVC,KAAK,EACLC,IAAI,EACJC,cAAc,EACdC,MAAM,EACNC,OAAO,EACPC,SAAS,EACTC,UAAU,EACVC,OAAO,EACPC,QAAQ,EACRC,gBAAgB,EAChBC,MAAM,EACNC,WAAW,EACXC,QAAQ,QACH,eAAe;AACtB,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,MAAM,EAAEC,aAAa,QAAQ,sBAAsB;AAC5D,OAAOC,MAAM,MAAM,uBAAuB;AAC1C,OAAOC,aAAa,MAAM,0BAA0B;AACpD,OAAOC,aAAa,MAAM,0BAA0B;AACpD,OAAOC,kBAAkB,MAAM,+BAA+B;AAC9D,SAASC,WAAW,EAAEC,UAAU,EAAEC,aAAa,EAAEC,aAAa,EAAEC,UAAU,EAAEC,mBAAmB,QAAQ,gBAAgB;AACvH,SAASC,KAAK,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEvC,MAAMC,aAAa,GAAGC,IAAA,IAoBhB;EAAAC,EAAA;EAAA,IAAAC,gBAAA,EAAAC,iBAAA,EAAAC,iBAAA;EAAA,IApBiB;IACrBC,SAAS;IACTC,OAAO,GAAG,KAAK;IACfC,YAAY,GAAG,KAAK;IACpBC,0BAA0B;IAC1BC,kBAAkB;IAClBC,qBAAqB;IACrBC,aAAa;IACbC,wBAAwB;IACxBC,gBAAgB;IAChBC,iBAAiB;IACjBC,YAAY;IACZC,eAAe;IACfC,SAAS;IACTC,YAAY;IACZC,YAAY;IACZC,eAAe;IACfC,eAAe;IACfC,YAAY;IACZC;EACF,CAAC,GAAAvB,IAAA;EACC,MAAM,CAACwB,SAAS,EAAEC,YAAY,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACgE,aAAa,EAAEC,gBAAgB,CAAC,GAAGjE,QAAQ,CAAC2C,SAAS,CAACuB,IAAI,CAAC;EAClE,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGpE,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACqE,WAAW,EAAEC,cAAc,CAAC,GAAGtE,QAAQ,CAAC,EAAE,CAAC;EAClD;EACA,MAAM,CAACuE,UAAU,EAAEC,aAAa,CAAC,GAAGxE,QAAQ,CAAC,MAAM;IACjD,MAAMyE,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,aAAahC,SAAS,CAACiC,EAAE,IAAIjC,SAAS,CAACuB,IAAI,WAAW,CAAC;IAC/F,OAAOO,UAAU,KAAK,IAAI,GAAGI,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC,GAAG,IAAI,CAAC,CAAC;EAC9D,CAAC,CAAC;EACF,MAAMM,QAAQ,GAAG9E,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAM+E,eAAe,GAAG/E,MAAM,CAAC,IAAI,CAAC;;EAEpC;EACA,MAAMgF,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMC,gBAAgB,GAAG,CAACX,UAAU;IACpCC,aAAa,CAACU,gBAAgB,CAAC;IAC/B;IACAR,YAAY,CAACS,OAAO,CAAC,aAAaxC,SAAS,CAACiC,EAAE,IAAIjC,SAAS,CAACuB,IAAI,WAAW,EAAEW,IAAI,CAACO,SAAS,CAACF,gBAAgB,CAAC,CAAC;EAChH,CAAC;;EAED;EACA,MAAMG,QAAQ,GAAGvC,0BAA0B,GAAGA,0BAA0B,CAACH,SAAS,CAAC,GAAG,CAAC;;EAEvF;EACA,MAAM2C,eAAe,GAAGvC,kBAAkB,GAAGA,kBAAkB,CAACJ,SAAS,CAAC,GAAGvB,MAAM,CAACmE,WAAW;EAC/F,MAAMC,YAAY,GAAGnE,aAAa,CAACiE,eAAe,CAAC,IAAIjE,aAAa,CAACD,MAAM,CAACmE,WAAW,CAAC;;EAExF;EACA,MAAME,cAAc,GAAG9C,SAAS,CAAC+C,WAAW,IAAI/C,SAAS,CAAC+C,WAAW,CAACC,IAAI,CAAC,CAAC,CAACC,MAAM,GAAG,CAAC;;EAEvF;EACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC5B9B,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;;EAED;EACA,MAAM+B,UAAU,GAAGA,CAAA,KAAM;IACvB;IACA,MAAMC,WAAW,GAAG/B,aAAa,CAAC2B,IAAI,CAAC,CAAC;;IAExC;IACA,IAAII,WAAW,KAAK,EAAE,IAAIA,WAAW,KAAKpD,SAAS,CAACuB,IAAI,EAAE;MACxD,IAAId,iBAAiB,EAAE;QACrBA,iBAAiB,CAAC;UAAE,GAAGT,SAAS;UAAEuB,IAAI,EAAE6B;QAAY,CAAC,CAAC;MACxD;IACF,CAAC,MAAM;MACL;MACA9B,gBAAgB,CAACtB,SAAS,CAACuB,IAAI,CAAC;IAClC;IAEAH,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;;EAED;EACA,MAAMiC,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,EAAE;MACrBJ,UAAU,CAAC,CAAC;IACd,CAAC,MAAM,IAAIG,CAAC,CAACC,GAAG,KAAK,QAAQ,EAAE;MAC7BjC,gBAAgB,CAACtB,SAAS,CAACuB,IAAI,CAAC;MAChCH,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAMoC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B/B,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;;EAED;EACA,MAAMgC,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,MAAML,WAAW,GAAG1B,WAAW,CAACsB,IAAI,CAAC,CAAC;IACtC,IAAII,WAAW,IAAIxC,SAAS,EAAE;MAC5B,MAAM8C,OAAO,GAAG;QACdnC,IAAI,EAAE6B,WAAW;QACjBpD,SAAS,EAAEA,SAAS,CAACiC,EAAE;QACvB0B,MAAM,EAAElF,MAAM,CAACmE,WAAW;QAC1BF,QAAQ,EAAE;MACZ,CAAC;MACD9B,SAAS,CAAC8C,OAAO,CAAC;MAClB/B,cAAc,CAAC,EAAE,CAAC;IACpB;IACAF,eAAe,CAAC,KAAK,CAAC;EACxB,CAAC;;EAED;EACA,MAAMmC,qBAAqB,GAAIN,CAAC,IAAK;IACnC,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,EAAE;MACrBE,iBAAiB,CAAC,CAAC;IACrB,CAAC,MAAM,IAAIH,CAAC,CAACC,GAAG,KAAK,QAAQ,EAAE;MAC7B5B,cAAc,CAAC,EAAE,CAAC;MAClBF,eAAe,CAAC,KAAK,CAAC;IACxB;EACF,CAAC;;EAED;EACAlE,SAAS,CAAC,MAAM;IACd,IAAI4D,SAAS,IAAIiB,QAAQ,CAACyB,OAAO,EAAE;MACjCzB,QAAQ,CAACyB,OAAO,CAACC,KAAK,CAAC,CAAC;IAC1B;EACF,CAAC,EAAE,CAAC3C,SAAS,CAAC,CAAC;;EAEf;EACA5D,SAAS,CAAC,MAAM;IACd,IAAIiE,YAAY,IAAIa,eAAe,CAACwB,OAAO,EAAE;MAC3CxB,eAAe,CAACwB,OAAO,CAACC,KAAK,CAAC,CAAC;IACjC;EACF,CAAC,EAAE,CAACtC,YAAY,CAAC,CAAC;EAElB,oBACEjC,OAAA,CAAC7B,KAAK;IACJqG,SAAS,EAAE,CAAE;IACbC,SAAS,EAAErF,MAAM,CAACsF,aAAc;IAChCC,EAAE,EAAE;MAAEC,OAAO,EAAE;IAAO,CAAE;IAAAC,QAAA,gBAExB7E,OAAA,CAAC/B,GAAG;MAACwG,SAAS,EAAErF,MAAM,CAAC0F,eAAgB;MAAAD,QAAA,gBACrC7E,OAAA,CAAC/B,GAAG;QAAC0G,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,IAAI,EAAE;QAAE,CAAE;QAAAJ,QAAA,gBAE1D7E,OAAA,CAACvB,UAAU;UACTyG,IAAI,EAAC,OAAO;UACZC,OAAO,EAAEpC,oBAAqB;UAC9B4B,EAAE,EAAE;YACFS,EAAE,EAAE,CAAC;YACLC,KAAK,EAAE,MAAM;YACb,SAAS,EAAE;cACTC,eAAe,EAAE;YACnB;UACF,CAAE;UAAAT,QAAA,eAEF7E,OAAA,CAAChB,OAAO;YACNuG,IAAI,EAAElD,UAAU,GAAG,8BAA8B,GAAG,8BAA+B;YACnFmD,KAAK,EAAE,EAAG;YACVC,MAAM,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC,EAEZjE,SAAS,gBACR5B,OAAA,CAAC/B,GAAG;UAAC0G,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEQ,KAAK,EAAE;UAAO,CAAE;UAAAX,QAAA,gBAChE7E,OAAA,CAAChB,OAAO;YAACuG,IAAI,EAAC,uBAAuB;YAACC,KAAK,EAAE,EAAG;YAACC,MAAM,EAAE,EAAG;YAACJ,KAAK,EAAEpG,eAAgB;YAAC0F,EAAE,EAAE;cAAES,EAAE,EAAE;YAAE;UAAE;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACtG7F,OAAA,CAACxB,SAAS;YACRqE,QAAQ,EAAEA,QAAS;YACnBiD,KAAK,EAAEhE,aAAc;YACrBiE,QAAQ,EAAGhC,CAAC,IAAKhC,gBAAgB,CAACgC,CAAC,CAACiC,MAAM,CAACF,KAAK,CAAE;YAClDG,SAAS,EAAEnC,cAAe;YAC1BoC,MAAM,EAAEtC,UAAW;YACnBuC,OAAO,EAAC,UAAU;YAClBC,SAAS;YACTC,SAAS;YACT1B,EAAE,EAAE;cACF2B,UAAU,EAAE,kCAAkC;cAC9CC,UAAU,EAAE,GAAG;cACfC,QAAQ,EAAE,QAAQ;cAClB,uBAAuB,EAAE;gBACvBD,UAAU,EAAE,GAAG;gBACfC,QAAQ,EAAE,QAAQ;gBAClBF,UAAU,EAAE;cACd;YACF;UAAE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACF7F,OAAA,CAACvB,UAAU;YAACyG,IAAI,EAAC,OAAO;YAACC,OAAO,EAAEvB,UAAW;YAACe,EAAE,EAAE;cAAE8B,EAAE,EAAE;YAAE,CAAE;YAAA5B,QAAA,eAC1D7E,OAAA,CAAChB,OAAO;cAACuG,IAAI,EAAC,wBAAwB;cAACC,KAAK,EAAE,EAAG;cAACC,MAAM,EAAE,EAAG;cAACJ,KAAK,EAAC;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,gBAEN7F,OAAA,CAAC9B,UAAU;UACTiI,OAAO,EAAC,IAAI;UACZ1B,SAAS,EAAErF,MAAM,CAACsH,cAAe;UACjC/B,EAAE,EAAE;YACF2B,UAAU,EAAE,kCAAkC;YAC9CvB,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpB2B,MAAM,EAAE,SAAS;YACjB1B,IAAI,EAAE,CAAC;YACP,SAAS,EAAE;cACT,cAAc,EAAE;gBACd2B,OAAO,EAAE;cACX;YACF;UACF,CAAE;UACFzB,OAAO,EAAExB,eAAgB;UAAAkB,QAAA,gBAEzB7E,OAAA,CAAChB,OAAO;YAACuG,IAAI,EAAC,uBAAuB;YAACC,KAAK,EAAE,EAAG;YAACC,MAAM,EAAE,EAAG;YAACJ,KAAK,EAAEpG;UAAgB;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACtF/D,aAAa,eACd9B,OAAA,CAACtB,OAAO;YAACmI,KAAK,EAAC,qBAAqB;YAAAhC,QAAA,eAClC7E,OAAA,CAACvB,UAAU;cACTyG,IAAI,EAAC,OAAO;cACZT,SAAS,EAAC,WAAW;cACrBE,EAAE,EAAE;gBACF8B,EAAE,EAAE,CAAC;gBACLG,OAAO,EAAE,CAAC;gBACVE,UAAU,EAAE,cAAc;gBAC1BlC,OAAO,EAAE;cACX,CAAE;cAAAC,QAAA,eAEF7E,OAAA,CAAChB,OAAO;gBAACuG,IAAI,EAAC,+BAA+B;gBAACC,KAAK,EAAE,EAAG;gBAACC,MAAM,EAAE,EAAG;gBAACJ,KAAK,EAAC;cAAM;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CACb;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN7F,OAAA,CAAC5B,IAAI;QACHmH,IAAI,eAAEvF,OAAA,CAAChB,OAAO;UAACuG,IAAI,EAAEjC,YAAY,CAACiC,IAAK;UAACC,KAAK,EAAE,EAAG;UAACC,MAAM,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAClEkB,KAAK,EAAEzD,YAAY,CAACyD,KAAM;QAC1B7B,IAAI,EAAC,OAAO;QACZP,EAAE,EAAE;UACFW,eAAe,EAAE,GAAGhC,YAAY,CAAC+B,KAAK,IAAI;UAC1CA,KAAK,EAAE/B,YAAY,CAAC+B,KAAK;UACzBkB,UAAU,EAAE,GAAG;UACfS,YAAY,EAAE,KAAK;UACnBV,UAAU,EAAE;QACd;MAAE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAEN7F,OAAA,CAACjB,QAAQ;MAACkI,EAAE,EAAE5E,UAAW;MAAC6E,OAAO,EAAC,MAAM;MAACC,aAAa;MAAAtC,QAAA,GAEnD,CAACnE,OAAO,IAAI6C,cAAc,iBACzBvD,OAAA,CAAC/B,GAAG;QAAC0G,EAAE,EAAE;UAAEyC,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAI,CAAE;QAAAxC,QAAA,eAC1B7E,OAAA,CAAC/B,GAAG;UACF0G,EAAE,EAAE;YACFW,eAAe,EAAE,SAAS;YAC1BgC,CAAC,EAAE,GAAG;YACNN,YAAY,EAAE,KAAK;YACnBO,MAAM,EAAE;UACV,CAAE;UAAA1C,QAAA,eAEF7E,OAAA,CAAC9B,UAAU;YACTiI,OAAO,EAAC,OAAO;YACfxB,EAAE,EAAE;cACFU,KAAK,EAAE,MAAM;cACbiB,UAAU,EAAE,kCAAkC;cAC9CkB,UAAU,EAAE,UAAU;cACtBC,UAAU,EAAE,GAAG;cACflB,UAAU,EAAE;YACd,CAAE;YAAA1B,QAAA,EAEDpE,SAAS,CAAC+C;UAAW;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAED7F,OAAA,CAACzB,OAAO;QAACoG,EAAE,EAAE;UAAE+C,EAAE,EAAE,CAAC;UAAEd,OAAO,EAAE;QAAI;MAAE;QAAAlB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE1C7F,OAAA,CAAC/B,GAAG;QAAC0G,EAAE,EAAE;UAAEyC,EAAE,EAAE;QAAE,CAAE;QAAAvC,QAAA,gBACjB7E,OAAA,CAAC/B,GAAG;UAAC0G,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAE4C,cAAc,EAAE,eAAe;YAAE3C,UAAU,EAAE,QAAQ;YAAEoC,EAAE,EAAE;UAAI,CAAE;UAAAvC,QAAA,gBAC3F7E,OAAA,CAAC9B,UAAU;YAACiI,OAAO,EAAC,OAAO;YAACxB,EAAE,EAAE;cAAE4B,UAAU,EAAE,GAAG;cAAED,UAAU,EAAE,kCAAkC;cAAEjB,KAAK,EAAE;YAAO,CAAE;YAAAR,QAAA,EAAC;UAEpH;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACb7F,OAAA,CAAC9B,UAAU;YAACiI,OAAO,EAAC,OAAO;YAACxB,EAAE,EAAE;cAAE4B,UAAU,EAAE,GAAG;cAAED,UAAU,EAAE,kCAAkC;cAAEjB,KAAK,EAAE;YAAO,CAAE;YAAAR,QAAA,GAChH1B,QAAQ,EAAC,GACZ;UAAA;YAAAuC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,eACN7F,OAAA,CAAC3B,cAAc;UACb8H,OAAO,EAAC,aAAa;UACrBL,KAAK,EAAE3C,QAAS;UAChBwB,EAAE,EAAE;YACFc,MAAM,EAAE,CAAC;YACTuB,YAAY,EAAE,CAAC;YACf1B,eAAe,EAAE,SAAS;YAC1B,0BAA0B,EAAE;cAC1BA,eAAe,EAAEhC,YAAY,CAAC+B;YAChC;UACF;QAAE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAEN7F,OAAA,CAAC/B,GAAG;QAAC0G,EAAE,EAAE;UAAEI,OAAO,EAAE,MAAM;UAAE4C,cAAc,EAAE,eAAe;UAAE3C,UAAU,EAAE,QAAQ;UAAEoC,EAAE,EAAE;QAAE,CAAE;QAAAvC,QAAA,EACxFpE,SAAS,CAACmH,kBAAkB,iBAC3B5H,OAAA,CAAC/B,GAAG;UAAC0G,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAE6C,GAAG,EAAE;UAAE,CAAE;UAAAhD,QAAA,gBACzD7E,OAAA,CAAChB,OAAO;YAACuG,IAAI,EAAC,wBAAwB;YAACC,KAAK,EAAE,EAAG;YAACC,MAAM,EAAE,EAAG;YAACJ,KAAK,EAAC;UAAM;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC7E7F,OAAA,CAAC9B,UAAU;YAACiI,OAAO,EAAC,OAAO;YAACxB,EAAE,EAAE;cAAE2B,UAAU,EAAE,kCAAkC;cAAEjB,KAAK,EAAE,MAAM;cAAEkB,UAAU,EAAE;YAAI,CAAE;YAAA1B,QAAA,EAChHpE,SAAS,CAACmH;UAAkB;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN7F,OAAA,CAAC/B,GAAG;QAAC0G,EAAE,EAAE;UAAEyC,EAAE,EAAE;QAAE,CAAE;QAAAvC,QAAA,gBACjB7E,OAAA,CAAC/B,GAAG;UACF0G,EAAE,EAAE;YACFI,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpB2C,cAAc,EAAE,eAAe;YAC/BP,EAAE,EAAE;UACN,CAAE;UAAAvC,QAAA,gBAEF7E,OAAA,CAAC/B,GAAG;YAAC0G,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAE6C,GAAG,EAAE;YAAE,CAAE;YAAAhD,QAAA,gBACzD7E,OAAA,CAAChB,OAAO;cACNuG,IAAI,EAAC,4BAA4B;cACjCC,KAAK,EAAE,EAAG;cACVC,MAAM,EAAE,EAAG;cACXd,EAAE,EAAE;gBAAEU,KAAK,EAAE;cAAO;YAAE;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC,eACF7F,OAAA,CAAC9B,UAAU;cACTiI,OAAO,EAAC,OAAO;cACfxB,EAAE,EAAE;gBACF4B,UAAU,EAAE,GAAG;gBACflB,KAAK,EAAE,MAAM;gBACbiB,UAAU,EAAE,kCAAkC;gBAC9CE,QAAQ,EAAE;cACZ,CAAE;cAAA3B,QAAA,EACH;YAED;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAGN7F,OAAA,CAACtB,OAAO;YAACmI,KAAK,EAAC,cAAc;YAAAhC,QAAA,eAC3B7E,OAAA,CAACvB,UAAU;cACTyG,IAAI,EAAC,OAAO;cACZC,OAAO,EAAElB,kBAAmB;cAC5BU,EAAE,EAAE;gBACFU,KAAK,EAAEpG,eAAe;gBACtB,SAAS,EAAE;kBACTqG,eAAe,EAAE,GAAGrG,eAAe;gBACrC;cACF,CAAE;cAAA4F,QAAA,eAEF7E,OAAA,CAAChB,OAAO;gBAACuG,IAAI,EAAC,2BAA2B;gBAACC,KAAK,EAAE,EAAG;gBAACC,MAAM,EAAE;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,EAELnF,OAAO,gBACNV,OAAA,CAAC/B,GAAG;UAACwG,SAAS,EAAErF,MAAM,CAAC0I,QAAS;UAAAjD,QAAA,IAAAvE,gBAAA,GAC7BG,SAAS,CAACsH,KAAK,cAAAzH,gBAAA,uBAAfA,gBAAA,CAAiB0H,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC5CnI,OAAA,CAACoI,QAAQ;YAEPF,IAAI,EAAEA,IAAK;YACXvH,YAAY,EAAEA,YAAa;YAC3BD,OAAO,EAAE,IAAK;YACdI,qBAAqB,EAAEA,qBAAsB;YAC7CC,aAAa,EAAEA,aAAc;YAC7BC,wBAAwB,EAAEA,wBAAyB;YACnDC,gBAAgB,EAAEA,gBAAiB;YACnCE,YAAY,EAAEA,YAAa;YAC3BC,eAAe,EAAEA,eAAgB;YACjCE,YAAY,EAAEA,YAAa;YAC3BC,YAAY,EAAEA,YAAa;YAC3BC,eAAe,EAAEA,eAAgB;YACjCC,eAAe,EAAEA,eAAgB;YACjCC,YAAY,EAAEA,YAAa;YAC3BC,SAAS,EAAEA;UAAU,GAfhBwG,KAAK;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgBX,CACF,CAAC,EAED,EAAAtF,iBAAA,GAAAE,SAAS,CAACsH,KAAK,cAAAxH,iBAAA,uBAAfA,iBAAA,CAAiBmD,MAAM,IAAG,CAAC,iBAC1B1D,OAAA,CAAC1B,MAAM;YACL6H,OAAO,EAAC,MAAM;YACdjB,IAAI,EAAC,OAAO;YACZP,EAAE,EAAE;cACFU,KAAK,EAAEpG,eAAe;cACtBsH,UAAU,EAAE,GAAG;cACf8B,aAAa,EAAE,MAAM;cACrBf,CAAC,EAAE,CAAC;cACJD,EAAE,EAAE,CAAC;cACLf,UAAU,EAAE;YACd,CAAE;YAAAzB,QAAA,GACH,IACG,EAACpE,SAAS,CAACsH,KAAK,CAACrE,MAAM,GAAG,CAAC,EAAC,aAChC;UAAA;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,gBAEN7F,OAAA,CAAC/B,GAAG;UAACwG,SAAS,EAAErF,MAAM,CAAC0I,QAAS;UAAAjD,QAAA,IAAArE,iBAAA,GAC7BC,SAAS,CAACsH,KAAK,cAAAvH,iBAAA,uBAAfA,iBAAA,CAAiByH,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAChCnI,OAAA,CAACoI,QAAQ;YAEPF,IAAI,EAAEA,IAAK;YACXvH,YAAY,EAAEA,YAAa;YAC3BD,OAAO,EAAE,KAAM;YACfI,qBAAqB,EAAEA,qBAAsB;YAC7CC,aAAa,EAAEA,aAAc;YAC7BC,wBAAwB,EAAEA,wBAAyB;YACnDC,gBAAgB,EAAEA,gBAAiB;YACnCE,YAAY,EAAEA,YAAa;YAC3BC,eAAe,EAAEA,eAAgB;YACjCE,YAAY,EAAEA,YAAa;YAC3BC,YAAY,EAAEA,YAAa;YAC3BC,eAAe,EAAEA,eAAgB;YACjCC,eAAe,EAAEA,eAAgB;YACjCC,YAAY,EAAEA,YAAa;YAC3BC,SAAS,EAAEA;UAAU,GAfhBwG,KAAK;YAAAzC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgBX,CACF,CAAC,EAGD5D,YAAY,iBACXjC,OAAA,CAAC/B,GAAG;YACFwG,SAAS,EAAErF,MAAM,CAACkJ,QAAS;YAC3B3D,EAAE,EAAE;cACF4D,QAAQ,EAAE,UAAU;cACpBlB,EAAE,EAAE,CAAC;cACLtC,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBM,eAAe,EAAE,SAAS;cAC1B0B,YAAY,EAAE,KAAK;cACnBpC,OAAO,EAAE,UAAU;cACnB2C,MAAM,EAAE;YACV,CAAE;YAAA1C,QAAA,gBAEF7E,OAAA,CAAC/B,GAAG;cACF0G,EAAE,EAAE;gBACFa,KAAK,EAAE,EAAE;gBACTC,MAAM,EAAE,EAAE;gBACVuB,YAAY,EAAE,KAAK;gBACnB1B,eAAe,EAAE,SAAS;gBAC1BF,EAAE,EAAE,GAAG;gBACPoD,UAAU,EAAE;cACd;YAAE;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEF7F,OAAA,CAACxB,SAAS;cACRqE,QAAQ,EAAEC,eAAgB;cAC1BgD,KAAK,EAAE3D,WAAY;cACnB4D,QAAQ,EAAGhC,CAAC,IAAK3B,cAAc,CAAC2B,CAAC,CAACiC,MAAM,CAACF,KAAK,CAAE;cAChDG,SAAS,EAAE5B,qBAAsB;cACjCoE,WAAW,EAAC,wBAAwB;cACpCtC,OAAO,EAAC,UAAU;cAClBC,SAAS;cACTC,SAAS;cACT1B,EAAE,EAAE;gBACF2B,UAAU,EAAE,kCAAkC;gBAC9C,uBAAuB,EAAE;kBACvBE,QAAQ,EAAE,QAAQ;kBAClBF,UAAU,EAAE,kCAAkC;kBAC9C1B,OAAO,EAAE;gBACX,CAAC;gBACD,8BAA8B,EAAE;kBAC9B8D,iBAAiB,EAAE;gBACrB;cACF;YAAE;cAAAhD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAEF7F,OAAA,CAAC/B,GAAG;cAAC0G,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAEyB,EAAE,EAAE;cAAO,CAAE;cAAA5B,QAAA,gBAC7D7E,OAAA,CAACvB,UAAU;gBAACyG,IAAI,EAAC,OAAO;gBAACC,OAAO,EAAEjB,iBAAkB;gBAACS,EAAE,EAAE;kBAAE8B,EAAE,EAAE;gBAAE,CAAE;gBAAA5B,QAAA,eACjE7E,OAAA,CAAChB,OAAO;kBAACuG,IAAI,EAAC,wBAAwB;kBAACC,KAAK,EAAE,EAAG;kBAACC,MAAM,EAAE,EAAG;kBAACJ,KAAK,EAAC;gBAAS;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtE,CAAC,eAEb7F,OAAA,CAACvB,UAAU;gBAACyG,IAAI,EAAC,OAAO;gBAACC,OAAO,EAAEA,CAAA,KAAMjD,eAAe,CAAC,KAAK,CAAE;gBAACyC,EAAE,EAAE;kBAAE8B,EAAE,EAAE;gBAAI,CAAE;gBAAA5B,QAAA,eAC9E7E,OAAA,CAAChB,OAAO;kBAACuG,IAAI,EAAC,wBAAwB;kBAACC,KAAK,EAAE,EAAG;kBAACC,MAAM,EAAE,EAAG;kBAACJ,KAAK,EAAC;gBAAS;kBAAAK,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtE,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEZ,CAAC;AACD;AAAAxF,EAAA,CA3dMF,aAAa;AAAAwI,EAAA,GAAbxI,aAAa;AA4dnB,MAAMiI,QAAQ,GAAGQ,KAAA,IAgBX;EAAAC,GAAA;EAAA,IAAAC,oBAAA;EAAA,IAhBY;IAChBZ,IAAI;IACJvH,YAAY,GAAG,KAAK;IACpBD,OAAO,GAAG,KAAK;IACfI,qBAAqB;IACrBC,aAAa;IACbC,wBAAwB;IACxBC,gBAAgB;IAChBE,YAAY;IACZC,eAAe;IACfE,YAAY;IACZC,YAAY;IACZC,eAAe;IACfC,eAAe;IACfC,YAAY;IACZC;EACF,CAAC,GAAAiH,KAAA;EACC,MAAM,CAAChH,SAAS,EAAEC,YAAY,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACiL,QAAQ,EAAEC,WAAW,CAAC,GAAGlL,QAAQ,CAACoK,IAAI,CAAClG,IAAI,CAAC;EACnD,MAAMiH,YAAY,GAAGlL,MAAM,CAAC,IAAI,CAAC;EACjC,MAAM,CAACmL,eAAe,EAAEC,kBAAkB,CAAC,GAAGrL,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACsL,cAAc,EAAEC,iBAAiB,CAAC,GAAGvL,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAMwL,kBAAkB,GAAGvL,MAAM,CAAC,IAAI,CAAC;;EAEvC;EACA,MAAM,CAACwL,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG1L,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC2L,QAAQ,EAAEC,WAAW,CAAC,GAAG5L,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC6L,eAAe,EAAEC,kBAAkB,CAAC,GAAG9L,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAAC+L,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGhM,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACiM,eAAe,EAAEC,kBAAkB,CAAC,GAAGlM,QAAQ,CAAC,KAAK,CAAC;;EAE7D;EACA,MAAM,CAACmM,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGpM,QAAQ,CAAC,KAAK,CAAC;;EAE3E;EACA,MAAM,CAACqM,SAAS,EAAEC,YAAY,CAAC,GAAGtM,QAAQ,CAACoK,IAAI,CAAC;;EAEhD;EACAlK,SAAS,CAAC,MAAM;IACdoM,YAAY,CAAClC,IAAI,CAAC;EACpB,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;EAEV,MAAMmC,kBAAkB,GAAItG,CAAC,IAAK;IAChCA,CAAC,CAACuG,eAAe,CAAC,CAAC;IACnBd,oBAAoB,CAAC,IAAI,CAAC;IAC1BI,kBAAkB,CAAC,IAAI,CAAC;IACxBpK,WAAW,CAAC0I,IAAI,CAACxF,EAAE,CAAC,CACjB6H,IAAI,CAACC,QAAQ,IAAI;MAAA,IAAAC,cAAA,EAAAC,eAAA;MAChBC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEJ,QAAQ,CAACK,IAAI,CAAC;MAC5C;MACA,IAAIC,YAAY,GAAG,EAAE;MACrB,IAAIC,KAAK,CAACC,OAAO,CAACR,QAAQ,CAACK,IAAI,CAAC,EAAE;QAChCC,YAAY,GAAGN,QAAQ,CAACK,IAAI;MAC9B,CAAC,MAAM,IAAI,CAAAJ,cAAA,GAAAD,QAAQ,CAACK,IAAI,cAAAJ,cAAA,eAAbA,cAAA,CAAeI,IAAI,IAAIE,KAAK,CAACC,OAAO,CAACR,QAAQ,CAACK,IAAI,CAACA,IAAI,CAAC,EAAE;QACnEC,YAAY,GAAGN,QAAQ,CAACK,IAAI,CAACA,IAAI;MACnC,CAAC,MAAM,IAAI,CAAAH,eAAA,GAAAF,QAAQ,CAACK,IAAI,cAAAH,eAAA,eAAbA,eAAA,CAAejB,QAAQ,IAAIsB,KAAK,CAACC,OAAO,CAACR,QAAQ,CAACK,IAAI,CAACpB,QAAQ,CAAC,EAAE;QAC3EqB,YAAY,GAAGN,QAAQ,CAACK,IAAI,CAACpB,QAAQ;MACvC;MACAC,WAAW,CAACoB,YAAY,CAAC;IAC3B,CAAC,CAAC,CACDG,KAAK,CAACC,KAAK,IAAI;MACdP,OAAO,CAACO,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDpL,KAAK,CAACoL,KAAK,CAAC,yBAAyB,CAAC;IACxC,CAAC,CAAC,CACDC,OAAO,CAAC,MAAM;MACbvB,kBAAkB,CAAC,KAAK,CAAC;IAC3B,CAAC,CAAC;EACN,CAAC;EAED,MAAMwB,gBAAgB,GAAG,MAAOC,OAAO,IAAK;IAC1C,IAAI;MAAA,IAAAC,eAAA;MACF,MAAMd,QAAQ,GAAG,MAAM/K,UAAU,CAACyI,IAAI,CAACxF,EAAE,EAAE2I,OAAO,CAAC;MACnDV,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEJ,QAAQ,CAAC;;MAE9C;MACA,IAAIe,UAAU;MACd,KAAAD,eAAA,GAAId,QAAQ,CAACK,IAAI,cAAAS,eAAA,eAAbA,eAAA,CAAeT,IAAI,EAAE;QACvBU,UAAU,GAAGf,QAAQ,CAACK,IAAI,CAACA,IAAI;MACjC,CAAC,MAAM;QACL;QACA,MAAMW,WAAW,GAAG7I,IAAI,CAACC,KAAK,CAACJ,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC;QAClE8I,UAAU,GAAG;UACX7I,EAAE,EAAE+I,IAAI,CAACC,GAAG,CAAC,CAAC;UAAE;UAChBL,OAAO,EAAEA,OAAO;UAChBM,IAAI,EAAE;YACJjJ,EAAE,EAAE8I,WAAW,CAAC9I,EAAE;YAClBkJ,UAAU,EAAEJ,WAAW,CAACI,UAAU;YAClCC,SAAS,EAAEL,WAAW,CAACK,SAAS;YAChCC,MAAM,EAAEN,WAAW,CAACM;UACtB,CAAC;UACDC,UAAU,EAAE,IAAIN,IAAI,CAAC,CAAC,CAACO,cAAc,CAAC,OAAO,EAAE;YAC7CC,IAAI,EAAE,SAAS;YACfC,MAAM,EAAE,SAAS;YACjBC,GAAG,EAAE,SAAS;YACdC,KAAK,EAAE;UACT,CAAC,CAAC,CAACC,OAAO,CAAC,GAAG,EAAE,EAAE;QACpB,CAAC;MACH;;MAEA;MACA3C,WAAW,CAAC4C,YAAY,IAAI,CAAC,GAAGA,YAAY,EAAEf,UAAU,CAAC,CAAC;;MAE1D;MACAgB,eAAe,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CpL,KAAK,CAACoL,KAAK,CAAC,uBAAuB,CAAC;IACtC;EACF,CAAC;EAED,MAAMsB,mBAAmB,GAAG,MAAAA,CAAOC,SAAS,EAAEpB,OAAO,KAAK;IACxD,IAAI;MACF;MACA,MAAMqB,kBAAkB,GAAGjD,QAAQ,CAACkD,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAClK,EAAE,KAAK+J,SAAS,CAAC;MACjE,IAAIC,kBAAkB,EAAE;QACtB,MAAMG,eAAe,GAAGpD,QAAQ,CAACxB,GAAG,CAAC2E,CAAC,IACpCA,CAAC,CAAClK,EAAE,KAAK+J,SAAS,GAAG;UAAE,GAAGG,CAAC;UAAEvB,OAAO,EAAEA;QAAQ,CAAC,GAAGuB,CACpD,CAAC;QACDlD,WAAW,CAACmD,eAAe,CAAC;MAC9B;;MAEA;MACA,MAAMrC,QAAQ,GAAG,MAAM9K,aAAa,CAAC+M,SAAS,EAAEpB,OAAO,CAAC;MACxDV,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEJ,QAAQ,CAAC;MAEjD1K,KAAK,CAACgN,OAAO,CAAC,8BAA8B,CAAC;;MAE7C;MACAP,eAAe,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CpL,KAAK,CAACoL,KAAK,CAAC,0BAA0B,CAAC;MACvC;MACAqB,eAAe,CAAC,CAAC;IACnB;EACF,CAAC;EAED,MAAMQ,mBAAmB,GAAG,MAAON,SAAS,IAAK;IAC/C,IAAI;MACF;MACA/C,WAAW,CAAC4C,YAAY,IAAIA,YAAY,CAACU,MAAM,CAACJ,CAAC,IAAIA,CAAC,CAAClK,EAAE,KAAK+J,SAAS,CAAC,CAAC;;MAEzE;MACA,MAAM9M,aAAa,CAAC8M,SAAS,CAAC;MAC9B9B,OAAO,CAACC,GAAG,CAAC,8BAA8B,CAAC;MAE3C9K,KAAK,CAACgN,OAAO,CAAC,8BAA8B,CAAC;;MAE7C;MACAP,eAAe,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOrB,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CpL,KAAK,CAACoL,KAAK,CAAC,0BAA0B,CAAC;MACvC;MACAqB,eAAe,CAAC,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMA,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MAAA,IAAAU,eAAA,EAAAC,eAAA;MACF,MAAM1C,QAAQ,GAAG,MAAMhL,WAAW,CAAC0I,IAAI,CAACxF,EAAE,CAAC;MAC3CiI,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEJ,QAAQ,CAACK,IAAI,CAAC;;MAEjD;MACA,IAAIC,YAAY,GAAG,EAAE;MACrB,IAAIC,KAAK,CAACC,OAAO,CAACR,QAAQ,CAACK,IAAI,CAAC,EAAE;QAChCC,YAAY,GAAGN,QAAQ,CAACK,IAAI;MAC9B,CAAC,MAAM,IAAI,CAAAoC,eAAA,GAAAzC,QAAQ,CAACK,IAAI,cAAAoC,eAAA,eAAbA,eAAA,CAAepC,IAAI,IAAIE,KAAK,CAACC,OAAO,CAACR,QAAQ,CAACK,IAAI,CAACA,IAAI,CAAC,EAAE;QACnEC,YAAY,GAAGN,QAAQ,CAACK,IAAI,CAACA,IAAI;MACnC,CAAC,MAAM,IAAI,CAAAqC,eAAA,GAAA1C,QAAQ,CAACK,IAAI,cAAAqC,eAAA,eAAbA,eAAA,CAAezD,QAAQ,IAAIsB,KAAK,CAACC,OAAO,CAACR,QAAQ,CAACK,IAAI,CAACpB,QAAQ,CAAC,EAAE;QAC3EqB,YAAY,GAAGN,QAAQ,CAACK,IAAI,CAACpB,QAAQ;MACvC;MAEAC,WAAW,CAACoB,YAAY,CAAC;IAC3B,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IACpD;EACF,CAAC;;EAED;EACA,MAAMiC,YAAY,GAAGrM,qBAAqB,GAAGA,qBAAqB,CAACoH,IAAI,CAAC,GAAG,CAAC;;EAE5E;EACA,MAAMkF,UAAU,GAAGrM,aAAa,GAAGA,aAAa,CAACmH,IAAI,CAAC,GAAIA,IAAI,CAAC9D,MAAM,IAAIlF,MAAM,CAACmE,WAAY;EAC5F,MAAMC,YAAY,GAAGnE,aAAa,CAACiO,UAAU,CAAC,IAAIjO,aAAa,CAACD,MAAM,CAACmE,WAAW,CAAC;;EAEnF;EACA,MAAMgK,WAAW,GAAGnF,IAAI,CAACoF,QAAQ,IAAIpF,IAAI,CAACoF,QAAQ,CAAC5J,MAAM,GAAG,CAAC;;EAE7D;EACA,MAAM6J,WAAW,GAAGH,UAAU,KAAKlO,MAAM,CAACsO,SAAS;;EAEnD;EACA,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChC5L,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;;EAED;EACA,MAAM6L,cAAc,GAAGA,CAAA,KAAM;IAC3B;IACA,MAAM7J,WAAW,GAAGkF,QAAQ,CAACtF,IAAI,CAAC,CAAC;;IAEnC;IACA,IAAII,WAAW,KAAK,EAAE,IAAIA,WAAW,KAAKqE,IAAI,CAAClG,IAAI,EAAE;MACnD,IAAIb,YAAY,EAAE;QAChBA,YAAY,CAAC;UAAE,GAAG+G,IAAI;UAAElG,IAAI,EAAE6B;QAAY,CAAC,CAAC;MAC9C;IACF,CAAC,MAAM;MACL;MACAmF,WAAW,CAACd,IAAI,CAAClG,IAAI,CAAC;IACxB;IAEAH,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;;EAED;EACA,MAAM8L,kBAAkB,GAAI5J,CAAC,IAAK;IAChC,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,EAAE;MACrB0J,cAAc,CAAC,CAAC;IAClB,CAAC,MAAM,IAAI3J,CAAC,CAACC,GAAG,KAAK,QAAQ,EAAE;MAC7BgF,WAAW,CAACd,IAAI,CAAClG,IAAI,CAAC;MACtBH,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAM+L,qBAAqB,GAAGA,CAAA,KAAM;IAClCzE,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAM0E,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAItM,YAAY,EAAE;MAChBA,YAAY,CAAC2G,IAAI,CAAC;IACpB;EACF,CAAC;;EAED;EACA,MAAM4F,oBAAoB,GAAGA,CAAA,KAAM;IACjC,MAAMjK,WAAW,GAAGuF,cAAc,CAAC3F,IAAI,CAAC,CAAC;IACzC,IAAII,WAAW,IAAIvC,YAAY,EAAE;MAC/B,MAAMyM,UAAU,GAAG;QACjB/L,IAAI,EAAE6B,WAAW;QACjBqE,IAAI,EAAEA,IAAI,CAAC8F,IAAI;QACf5J,MAAM,EAAElF,MAAM,CAACmE,WAAW;QAC1BF,QAAQ,EAAE;MACZ,CAAC;MACD7B,YAAY,CAACyM,UAAU,CAAC;MACxB1E,iBAAiB,CAAC,EAAE,CAAC;IACvB;IACAF,kBAAkB,CAAC,KAAK,CAAC;EAC3B,CAAC;;EAED;EACA,MAAM8E,wBAAwB,GAAIlK,CAAC,IAAK;IACtC,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,EAAE;MACrB8J,oBAAoB,CAAC,CAAC;IACxB,CAAC,MAAM,IAAI/J,CAAC,CAACC,GAAG,KAAK,QAAQ,EAAE;MAC7BqF,iBAAiB,CAAC,EAAE,CAAC;MACrBF,kBAAkB,CAAC,KAAK,CAAC;IAC3B;EACF,CAAC;;EAED;EACAnL,SAAS,CAAC,MAAM;IACd,IAAI4D,SAAS,IAAIqH,YAAY,CAAC3E,OAAO,EAAE;MACrC2E,YAAY,CAAC3E,OAAO,CAACC,KAAK,CAAC,CAAC;IAC9B;EACF,CAAC,EAAE,CAAC3C,SAAS,CAAC,CAAC;;EAEf;EACA5D,SAAS,CAAC,MAAM;IACd,IAAIkL,eAAe,IAAII,kBAAkB,CAAChF,OAAO,EAAE;MACjDgF,kBAAkB,CAAChF,OAAO,CAACC,KAAK,CAAC,CAAC;IACpC;EACF,CAAC,EAAE,CAAC2E,eAAe,CAAC,CAAC;;EAErB;EACA,MAAMgF,WAAW,GAAG/D,SAAS,CAACgE,UAAU,IAAIhE,SAAS,CAACiE,QAAQ;EAE9D,MAAMC,uBAAuB,GAAItK,CAAC,IAAK;IACrCA,CAAC,CAACuG,eAAe,CAAC,CAAC;IACnBR,oBAAoB,CAAC,IAAI,CAAC;EAC5B,CAAC;EAED,MAAMwE,mBAAmB,GAAG,MAAOC,WAAW,IAAK;IACjDvE,kBAAkB,CAAC,IAAI,CAAC;IACxB,IAAI;MACF,MAAMwE,YAAY,GAAG;QACnB,GAAGrE,SAAS;QACZgE,UAAU,EAAEI,WAAW,CAACJ,UAAU;QAClCC,QAAQ,EAAEG,WAAW,CAACH,QAAQ;QAC9BjL,QAAQ,EAAEgH,SAAS,CAAChH,QAAQ,IAAI,CAAC;QACjCiB,MAAM,EAAE+F,SAAS,CAAC/F,MAAM,IAAI;MAC9B,CAAC;;MAED;MACAgG,YAAY,CAACoE,YAAY,CAAC;;MAE1B;MACA,MAAMhE,QAAQ,GAAG,MAAM5K,UAAU,CAAC4O,YAAY,CAAC;;MAE/C;MACA,IAAIhE,QAAQ,IAAIA,QAAQ,CAACK,IAAI,EAAE;QAC7BT,YAAY,CAACI,QAAQ,CAACK,IAAI,CAAC;MAC7B;;MAEA;MACA,IAAI1J,YAAY,EAAE;QAChBA,YAAY,CAACqN,YAAY,CAAC;MAC5B;MAEA1O,KAAK,CAACgN,OAAO,CAAC,+BAA+B,CAAC;IAChD,CAAC,CAAC,OAAO5B,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDpL,KAAK,CAACoL,KAAK,CAAC,2BAA2B,CAAC;;MAExC;MACAd,YAAY,CAAClC,IAAI,CAAC;IACpB,CAAC,SAAS;MACR8B,kBAAkB,CAAC,KAAK,CAAC;MACzBF,oBAAoB,CAAC,KAAK,CAAC,CAAC,CAAC;IAC/B;EACF,CAAC;;EAED;EACA,MAAM2E,mBAAmB,GAAG,MAAOC,eAAe,IAAK;IACrD,IAAI;MACF;MACA,MAAMC,UAAU,GAAGD,eAAe,CAACzG,GAAG,CAACvF,EAAE,IAAIkM,MAAM,CAAClM,EAAE,CAAC,CAAC;MACxD;MACA,MAAM8H,QAAQ,GAAG,MAAM3K,mBAAmB,CAACsK,SAAS,CAAC6D,IAAI,EAAEW,UAAU,CAAC;MACtE;MACA,MAAMJ,WAAW,GAAG;QAClB,GAAGpE,SAAS;QACZ0E,SAAS,EAAErE,QAAQ,CAACqE,SAAS,IAAIF,UAAU,CAAC1G,GAAG,CAACvF,EAAE,KAAK;UACrDA,EAAE,EAAEA,EAAE;UACNkJ,UAAU,EAAE,EAAE;UACdC,SAAS,EAAE,EAAE;UACbiD,KAAK,EAAE;QACT,CAAC,CAAC;MACJ,CAAC;;MAED;MACA1E,YAAY,CAACmE,WAAW,CAAC;MACzB,IAAIpN,YAAY,EAAE;QAChBA,YAAY,CAACoN,WAAW,CAAC;MAC3B;MAEArE,yBAAyB,CAAC,KAAK,CAAC;MAChCpK,KAAK,CAACgN,OAAO,CAAC,+BAA+B,CAAC;IAChD,CAAC,CAAC,OAAO5B,KAAK,EAAE;MACdP,OAAO,CAACO,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChDpL,KAAK,CAACoL,KAAK,CAAC,0BAA0B,CAAC;IACzC;EACF,CAAC;EAED,oBACElL,OAAA,CAAAE,SAAA;IAAA2E,QAAA,gBACE7E,OAAA,CAAC/B,GAAG;MAACwG,SAAS,EAAErF,MAAM,CAAC2P,iBAAkB;MAAAlK,QAAA,gBACvC7E,OAAA,CAAC/B,GAAG;QACFwG,SAAS,EAAErF,MAAM,CAACkJ,QAAS;QAC3B3D,EAAE,EAAE;UAAE4D,QAAQ,EAAE;QAAW,CAAE;QAAA1D,QAAA,gBAC7B7E,OAAA,CAAC/B,GAAG;UACF0G,EAAE,EAAE;YACFa,KAAK,EAAE,EAAE;YACTC,MAAM,EAAE,EAAE;YACVuB,YAAY,EAAE,KAAK;YACnB1B,eAAe,EAAEhC,YAAY,CAAC+B,KAAK;YACnCD,EAAE,EAAE,GAAG;YACPoD,UAAU,EAAE;UACd;QAAE;UAAA9C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAEDjE,SAAS,gBACR5B,OAAA,CAAC/B,GAAG;UAAC0G,EAAE,EAAE;YAAEI,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEgK,QAAQ,EAAE;UAAE,CAAE;UAAAnK,QAAA,gBAC9D7E,OAAA,CAACxB,SAAS;YACRqE,QAAQ,EAAEoG,YAAa;YACvBnD,KAAK,EAAEiD,QAAS;YAChBhD,QAAQ,EAAGhC,CAAC,IAAKiF,WAAW,CAACjF,CAAC,CAACiC,MAAM,CAACF,KAAK,CAAE;YAC7CG,SAAS,EAAE0H,kBAAmB;YAC9BzH,MAAM,EAAEwH,cAAe;YACvBvH,OAAO,EAAC,UAAU;YAClBC,SAAS;YACTC,SAAS;YACT1B,EAAE,EAAE;cACF2B,UAAU,EAAE,kCAAkC;cAC9C,uBAAuB,EAAE;gBACvBE,QAAQ,EAAE,QAAQ;gBAClBF,UAAU,EAAE;cACd;YACF;UAAE;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACF7F,OAAA,CAACvB,UAAU;YAACyG,IAAI,EAAC,OAAO;YAACC,OAAO,EAAEuI,cAAe;YAAC/I,EAAE,EAAE;cAAE8B,EAAE,EAAE;YAAE,CAAE;YAAA5B,QAAA,eAC9D7E,OAAA,CAAChB,OAAO;cAACuG,IAAI,EAAC,wBAAwB;cAACC,KAAK,EAAE,EAAG;cAACC,MAAM,EAAE,EAAG;cAACJ,KAAK,EAAC;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC,gBAEN7F,OAAA,CAAC/B,GAAG;UAAC0G,EAAE,EAAE;YAAEqK,QAAQ,EAAE,CAAC;YAAEjK,OAAO,EAAE,MAAM;YAAEkK,aAAa,EAAE,QAAQ;YAAEpH,GAAG,EAAE;UAAI,CAAE;UAAAhD,QAAA,gBAC3E7E,OAAA,CAAC/B,GAAG;YAAC0G,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE,QAAQ;cAAE2C,cAAc,EAAE,eAAe;cAAEnC,KAAK,EAAE;YAAO,CAAE;YAAAX,QAAA,gBACjG7E,OAAA,CAAC/B,GAAG;cAAC0G,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAE6C,GAAG,EAAE,CAAC;gBAAEmH,QAAQ,EAAE;cAAE,CAAE;cAAAnK,QAAA,eACtE7E,OAAA,CAAC9B,UAAU;gBACTiI,OAAO,EAAC,OAAO;gBACf1B,SAAS,EAAErF,MAAM,CAAC2J,QAAS;gBAC3BpE,EAAE,EAAE;kBACF2B,UAAU,EAAE,kCAAkC;kBAC9CK,MAAM,EAAE,SAAS;kBACjB5B,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBK,KAAK,EAAEkI,WAAW,GAAG,SAAS,GAAG,SAAS;kBAC1ChH,UAAU,EAAEgH,WAAW,GAAG,GAAG,GAAG,GAAG;kBACnCyB,QAAQ,EAAE,CAAC;kBACX,SAAS,EAAE;oBACT,mBAAmB,EAAE;sBACnBpI,OAAO,EAAE;oBACX;kBACF;gBACF,CAAE;gBACFzB,OAAO,EAAEsI,mBAAoB;gBAAA5I,QAAA,GAC5BkE,QAAQ,eACT/I,OAAA,CAACtB,OAAO;kBAACmI,KAAK,EAAC,gBAAgB;kBAAAhC,QAAA,eAC7B7E,OAAA,CAACvB,UAAU;oBACTyG,IAAI,EAAC,OAAO;oBACZT,SAAS,EAAC,gBAAgB;oBAC1BE,EAAE,EAAE;sBACF8B,EAAE,EAAE,GAAG;sBACPG,OAAO,EAAE,CAAC;sBACVE,UAAU,EAAE,cAAc;sBAC1BlC,OAAO,EAAE;oBACX,CAAE;oBAAAC,QAAA,eAEF7E,OAAA,CAAChB,OAAO;sBAACuG,IAAI,EAAC,+BAA+B;sBAACC,KAAK,EAAE,EAAG;sBAACC,MAAM,EAAE,EAAG;sBAACJ,KAAK,EAAC;oBAAM;sBAAAK,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1E;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACA;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV,CAAC,eAEN7F,OAAA,CAAC/B,GAAG;cAAC0G,EAAE,EAAE;gBAAEI,OAAO,EAAE,MAAM;gBAAEC,UAAU,EAAE,QAAQ;gBAAE6C,GAAG,EAAE;cAAE,CAAE;cAAAhD,QAAA,GAExDsF,SAAS,CAAC0E,SAAS,IAAI1E,SAAS,CAAC0E,SAAS,CAACnL,MAAM,GAAG,CAAC,iBACpD1D,OAAA,CAAClB,WAAW;gBACVoQ,GAAG,EAAE,CAAE;gBACPvK,EAAE,EAAE;kBACF,mBAAmB,EAAE;oBACnBa,KAAK,EAAE,EAAE;oBACTC,MAAM,EAAE,EAAE;oBACVe,QAAQ,EAAE,SAAS;oBACnBe,MAAM,EAAE;kBACV;gBACF,CAAE;gBAAA1C,QAAA,EAEDsF,SAAS,CAAC0E,SAAS,CAAC5G,GAAG,CAAC,CAACkH,QAAQ,EAAEhH,KAAK,kBACvCnI,OAAA,CAACtB,OAAO;kBAENmI,KAAK,EAAEsI,QAAQ,CAACvD,UAAU,IAAIuD,QAAQ,CAACtD,SAAS,GAC5C,GAAGsD,QAAQ,CAACvD,UAAU,IAAIuD,QAAQ,CAACtD,SAAS,EAAE,GAC9CsD,QAAQ,CAACL,KAAM;kBACnBM,KAAK;kBAAAvK,QAAA,eAEL7E,OAAA,CAACnB,MAAM;oBACLwQ,GAAG,EAAEF,QAAQ,CAACrD,MAAO;oBACrBwD,GAAG,EAAEH,QAAQ,CAACvD,UAAU,IAAIuD,QAAQ,CAACL,KAAM;oBAC3CnK,EAAE,EAAE;sBACF4K,OAAO,EAAEtQ;oBACX,CAAE;oBAAA4F,QAAA,EAEDsK,QAAQ,CAACvD,UAAU,GAChBuD,QAAQ,CAACvD,UAAU,CAAC4D,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAC3CN,QAAQ,CAACL,KAAK,GACZK,QAAQ,CAACL,KAAK,CAACU,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GACtC;kBAAE;oBAAA/J,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACF;gBAAC,GAlBJsJ,QAAQ,CAACzM,EAAE;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAmBT,CACV;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS,CACd,eAGD7F,OAAA,CAAC/B,GAAG;gBACFwG,SAAS,EAAC,cAAc;gBACxBE,EAAE,EAAE;kBACF4D,QAAQ,EAAE,UAAU;kBACpBxD,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpB,2BAA2B,EAAE;oBAC3B4B,OAAO,EAAE,CAAC;oBACV8I,UAAU,EAAE,SAAS;oBACrBC,SAAS,EAAE;kBACb,CAAC;kBACD,uBAAuB,EAAE;oBACvB/I,OAAO,EAAE,CAAC;oBACV8I,UAAU,EAAE;kBACd;gBACF,CAAE;gBAAA7K,QAAA,gBAGF7E,OAAA,CAAC/B,GAAG;kBACFwG,SAAS,EAAC,cAAc;kBACxBE,EAAE,EAAE;oBACFiC,OAAO,EAAE,CAAC;oBACV8I,UAAU,EAAE,SAAS;oBACrB5I,UAAU,EAAE,eAAe;oBAC3B/B,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpB2B,MAAM,EAAE,SAAS;oBACjB/B,OAAO,EAAE,SAAS;oBAClBoC,YAAY,EAAE,KAAK;oBACnB,SAAS,EAAE;sBACT1B,eAAe,EAAE;oBACnB;kBACF,CAAE;kBAAAT,QAAA,eAEF7E,OAAA,CAAChB,OAAO;oBAACuG,IAAI,EAAC,6BAA6B;oBAACC,KAAK,EAAE,EAAG;oBAACC,MAAM,EAAE,EAAG;oBAACJ,KAAK,EAAC;kBAAM;oBAAAK,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/E,CAAC,eAGN7F,OAAA,CAAC/B,GAAG;kBACFwG,SAAS,EAAC,kBAAkB;kBAC5BE,EAAE,EAAE;oBACF4D,QAAQ,EAAE,UAAU;oBACpBqH,KAAK,EAAE,CAAC;oBACRC,GAAG,EAAE,CAAC;oBACNjJ,OAAO,EAAE,CAAC;oBACV8I,UAAU,EAAE,QAAQ;oBACpBC,SAAS,EAAE,kBAAkB;oBAC7B7I,UAAU,EAAE,eAAe;oBAC3B/B,OAAO,EAAE,MAAM;oBACfC,UAAU,EAAE,QAAQ;oBACpBuC,MAAM,EAAE,gBAAgB;oBACxBP,YAAY,EAAE,KAAK;oBACnBpC,OAAO,EAAE,SAAS;oBAClBkL,UAAU,EAAE,MAAM;oBAClBC,SAAS,EAAE,2BAA2B;oBACtCC,MAAM,EAAE;kBACV,CAAE;kBAAAnL,QAAA,gBAGF7E,OAAA,CAACtB,OAAO;oBAACmI,KAAK,EAAEqH,WAAW,GAAG,eAAe,GAAG,cAAe;oBAAArJ,QAAA,eAC7D7E,OAAA,CAACvB,UAAU;sBACTyG,IAAI,EAAC,OAAO;sBACZC,OAAO,EAAEkJ,uBAAwB;sBACjC1J,EAAE,EAAE;wBACFU,KAAK,EAAE6I,WAAW,GAAGjP,eAAe,GAAG,MAAM;wBAC7C,SAAS,EAAE;0BACToG,KAAK,EAAEpG,eAAe;0BACtBsQ,OAAO,EAAE;wBACX;sBACF,CAAE;sBAAA1K,QAAA,eAEF7E,OAAA,CAAChB,OAAO;wBACNuG,IAAI,EAAE2I,WAAW,GAAG,iCAAiC,GAAG,kCAAmC;wBAC3F1I,KAAK,EAAE,EAAG;wBACVC,MAAM,EAAE;sBAAG;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eAGV7F,OAAA,CAACtB,OAAO;oBAACmI,KAAK,EAAC,UAAU;oBAAAhC,QAAA,eACvB7E,OAAA,CAACvB,UAAU;sBACTyG,IAAI,EAAC,OAAO;sBACZC,OAAO,EAAEkF,kBAAmB;sBAC5B4F,QAAQ,EAAEtG,eAAgB;sBAC1BhF,EAAE,EAAE;wBACFU,KAAK,EAAEpG,eAAe;wBACtB,SAAS,EAAE;0BACTsQ,OAAO,EAAE;wBACX;sBACF,CAAE;sBAAA1K,QAAA,EAED8E,eAAe,gBACd3J,OAAA,CAACpB,gBAAgB;wBAACsG,IAAI,EAAE,EAAG;wBAACP,EAAE,EAAE;0BAAEU,KAAK,EAAEpG;wBAAgB;sBAAE;wBAAAyG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,gBAE9D7F,OAAA,CAAChB,OAAO;wBAACuG,IAAI,EAAC,kCAAkC;wBAACC,KAAK,EAAE,EAAG;wBAACC,MAAM,EAAE;sBAAG;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAC1E;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACS;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eAGV7F,OAAA,CAACtB,OAAO;oBAACmI,KAAK,EAAC,gBAAgB;oBAAAhC,QAAA,eAC7B7E,OAAA,CAACvB,UAAU;sBACTyG,IAAI,EAAC,OAAO;sBACZC,OAAO,EAAEA,CAAA,KAAM+E,yBAAyB,CAAC,IAAI,CAAE;sBAC/CvF,EAAE,EAAE;wBACFU,KAAK,EAAE,EAAAyD,oBAAA,GAAAqB,SAAS,CAAC0E,SAAS,cAAA/F,oBAAA,uBAAnBA,oBAAA,CAAqBpF,MAAM,IAAG,CAAC,GAAGzE,eAAe,GAAG,MAAM;wBACjE,SAAS,EAAE;0BACToG,KAAK,EAAEpG,eAAe;0BACtBsQ,OAAO,EAAE;wBACX;sBACF,CAAE;sBAAA1K,QAAA,eAEF7E,OAAA,CAAChB,OAAO;wBACNuG,IAAI,EAAC,2BAA2B;wBAChCC,KAAK,EAAE,EAAG;wBACVC,MAAM,EAAE;sBAAG;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACZ;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACQ;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eAGV7F,OAAA,CAACtB,OAAO;oBAACmI,KAAK,EAAC,aAAa;oBAAAhC,QAAA,eAC1B7E,OAAA,CAACvB,UAAU;sBACTyG,IAAI,EAAC,OAAO;sBACZC,OAAO,EAAEyI,qBAAsB;sBAC/BjJ,EAAE,EAAE;wBACFU,KAAK,EAAEpG,eAAe;wBACtB,SAAS,EAAE;0BACTsQ,OAAO,EAAE;wBACX;sBACF,CAAE;sBAAA1K,QAAA,eAEF7E,OAAA,CAAChB,OAAO;wBAACuG,IAAI,EAAC,2BAA2B;wBAACC,KAAK,EAAE,EAAG;wBAACC,MAAM,EAAE;sBAAG;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eAGV7F,OAAA,CAACtB,OAAO;oBAACmI,KAAK,EAAC,WAAW;oBAAAhC,QAAA,eACxB7E,OAAA,CAACvB,UAAU;sBACTyG,IAAI,EAAC,OAAO;sBACZC,OAAO,EAAEsI,mBAAoB;sBAC7B9I,EAAE,EAAE;wBACFU,KAAK,EAAE,MAAM;wBACb,SAAS,EAAE;0BACTkK,OAAO,EAAE;wBACX;sBACF,CAAE;sBAAA1K,QAAA,eAEF7E,OAAA,CAAChB,OAAO;wBAACuG,IAAI,EAAC,eAAe;wBAACC,KAAK,EAAE,EAAG;wBAACC,MAAM,EAAE;sBAAG;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7C;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eAGV7F,OAAA,CAACtB,OAAO;oBAACmI,KAAK,EAAC,aAAa;oBAAAhC,QAAA,eAC1B7E,OAAA,CAACvB,UAAU;sBACTyG,IAAI,EAAC,OAAO;sBACZC,OAAO,EAAE0I,qBAAsB;sBAC/BlJ,EAAE,EAAE;wBACFU,KAAK,EAAE,SAAS;wBAChB,SAAS,EAAE;0BACTkK,OAAO,EAAE;wBACX;sBACF,CAAE;sBAAA1K,QAAA,eAEF7E,OAAA,CAAChB,OAAO;wBAACuG,IAAI,EAAC,iCAAiC;wBAACC,KAAK,EAAE,EAAG;wBAACC,MAAM,EAAE;sBAAG;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/D;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACP,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAGN7F,OAAA,CAAC/B,GAAG;gBACF0G,EAAE,EAAE;kBACFI,OAAO,EAAE,MAAM;kBACfC,UAAU,EAAE,QAAQ;kBACpBuK,OAAO,EAAEjM,YAAY,CAAC+B,KAAK,GAAG,IAAI;kBAClC6K,EAAE,EAAE,CAAC;kBACLC,EAAE,EAAE,GAAG;kBACPnJ,YAAY,EAAE;gBAChB,CAAE;gBAAAnC,QAAA,eAEF7E,OAAA,CAAC9B,UAAU;kBACTiI,OAAO,EAAC,SAAS;kBACjBxB,EAAE,EAAE;oBACF4B,UAAU,EAAE,GAAG;oBACflB,KAAK,EAAE/B,YAAY,CAAC+B,KAAK;oBACzBiB,UAAU,EAAE;kBACd,CAAE;kBAAAzB,QAAA,GAEDsI,YAAY,EAAC,GAChB;gBAAA;kBAAAzH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAGLqI,WAAW,iBACVlO,OAAA,CAAC/B,GAAG;YACF0G,EAAE,EAAE;cACFI,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBM,eAAe,EAAE,GAAGrG,eAAe,IAAI;cACvC+H,YAAY,EAAE,KAAK;cACnBmJ,EAAE,EAAE,GAAG;cACPD,EAAE,EAAE,IAAI;cACR1K,KAAK,EAAE;YACT,CAAE;YAAAX,QAAA,gBAEF7E,OAAA,CAAChB,OAAO;cACNuG,IAAI,EAAC,iCAAiC;cACtCC,KAAK,EAAE,EAAG;cACVC,MAAM,EAAE,EAAG;cACXJ,KAAK,EAAEpG;YAAgB;cAAAyG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5B7F,OAAA,CAAC9B,UAAU;cACTiI,OAAO,EAAC,SAAS;cACjBxB,EAAE,EAAE;gBACF2B,UAAU,EAAE,kCAAkC;gBAC9CE,QAAQ,EAAE,SAAS;gBACnBnB,KAAK,EAAE,MAAM;gBACbkB,UAAU,EAAE,GAAG;gBACfkB,UAAU,EAAE,CAAC;gBACbhB,EAAE,EAAE;cACN,CAAE;cAAA5B,QAAA,GACDuL,UAAU,CAACjG,SAAS,CAACgE,UAAU,CAAC,EAAC,KAAG,EAACiC,UAAU,CAACjG,SAAS,CAACiE,QAAQ,CAAC;YAAA;cAAA1I,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAEN7F,OAAA,CAAC/B,GAAG;QACF0G,EAAE,EAAE;UACF0L,EAAE,EAAE,CAAC;UACLC,EAAE,EAAE,CAAC;UACLC,EAAE,EAAE,GAAG;UACPC,EAAE,EAAE,GAAG;UACPC,UAAU,EAAE,cAAcnN,YAAY,CAAC+B,KAAK,EAAE;UAC9CoB,EAAE,EAAE,GAAG;UACPY,EAAE,EAAE,GAAG;UACPtC,OAAO,EAAE,CAACsI,WAAW,IAAInE,eAAe,KAAKvI,YAAY,GAAG,OAAO,GAAG;QACxE,CAAE;QAAAkE,QAAA,GAEDqD,IAAI,CAACoF,QAAQ,IAAIpF,IAAI,CAACoF,QAAQ,CAACrF,GAAG,CAAC,CAACyI,OAAO,EAAEvI,KAAK,kBACjDnI,OAAA,CAAC2Q,WAAW;UAEVD,OAAO,EAAEA,OAAQ;UACjBvI,KAAK,EAAEA,KAAM;UACbyI,aAAa,EAAE1I,IAAI,CAACoF,QAAQ,CAAC5J,MAAO;UACpCmN,QAAQ,EAAE3I,IAAI,CAAC8F,IAAK;UACpBhN,wBAAwB,EAAEA,wBAAyB;UACnDC,gBAAgB,EAAEA,gBAAiB;UACnCG,eAAe,EAAEA,eAAgB;UACjCI,eAAe,EAAEA,eAAgB;UACjCC,eAAe,EAAEA,eAAgB;UACjCC,YAAY,EAAEA,YAAa;UAC3BC,SAAS,EAAEA;QAAU,GAXhBwG,KAAK;UAAAzC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAYX,CACF,CAAC,EAGDqD,eAAe,iBACdlJ,OAAA,CAAC/B,GAAG;UACF0G,EAAE,EAAE;YACFI,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBmL,EAAE,EAAE,GAAG;YACPW,YAAY,EAAE,oBAAoB;YAClCxL,eAAe,EAAE,SAAS;YAC1B0B,YAAY,EAAE,KAAK;YACnBkJ,EAAE,EAAE;UACN,CAAE;UAAArL,QAAA,gBAEF7E,OAAA,CAACrB,QAAQ;YACPsR,QAAQ;YACR/K,IAAI,EAAC,OAAO;YACZP,EAAE,EAAE;cACF2C,CAAC,EAAE,GAAG;cACNlC,EAAE,EAAE,GAAG;cACPC,KAAK,EAAE;YACT,CAAE;YACFE,IAAI,eAAEvF,OAAA,CAAChB,OAAO;cAACuG,IAAI,EAAC,0CAA0C;cAACC,KAAK,EAAE,EAAG;cAACC,MAAM,EAAE;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1F,CAAC,eAEF7F,OAAA,CAACxB,SAAS;YACRqE,QAAQ,EAAEyG,kBAAmB;YAC7BxD,KAAK,EAAEsD,cAAe;YACtBrD,QAAQ,EAAGhC,CAAC,IAAKsF,iBAAiB,CAACtF,CAAC,CAACiC,MAAM,CAACF,KAAK,CAAE;YACnDG,SAAS,EAAEgI,wBAAyB;YACpCxF,WAAW,EAAC,2BAA2B;YACvCtC,OAAO,EAAC,UAAU;YAClBC,SAAS;YACTC,SAAS;YACT1B,EAAE,EAAE;cACF2B,UAAU,EAAE,kCAAkC;cAC9C,uBAAuB,EAAE;gBACvBE,QAAQ,EAAE,SAAS;gBACnBF,UAAU,EAAE,kCAAkC;gBAC9C1B,OAAO,EAAE;cACX,CAAC;cACD,8BAA8B,EAAE;gBAC9B8D,iBAAiB,EAAE;cACrB;YACF;UAAE;YAAAhD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEF7F,OAAA,CAAC/B,GAAG;YAAC0G,EAAE,EAAE;cAAEI,OAAO,EAAE,MAAM;cAAE0B,EAAE,EAAE;YAAE,CAAE;YAAA5B,QAAA,gBAClC7E,OAAA,CAACvB,UAAU;cAACyG,IAAI,EAAC,OAAO;cAACC,OAAO,EAAE2I,oBAAqB;cAACnJ,EAAE,EAAE;gBAAE2C,CAAC,EAAE;cAAI,CAAE;cAAAzC,QAAA,eACrE7E,OAAA,CAAChB,OAAO;gBAACuG,IAAI,EAAC,wBAAwB;gBAACC,KAAK,EAAE,EAAG;gBAACC,MAAM,EAAE,EAAG;gBAACJ,KAAK,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC,eAEb7F,OAAA,CAACvB,UAAU;cAACyG,IAAI,EAAC,OAAO;cAACC,OAAO,EAAEA,CAAA,KAAMgE,kBAAkB,CAAC,KAAK,CAAE;cAACxE,EAAE,EAAE;gBAAE2C,CAAC,EAAE,GAAG;gBAAEb,EAAE,EAAE;cAAI,CAAE;cAAA5B,QAAA,eACzF7E,OAAA,CAAChB,OAAO;gBAACuG,IAAI,EAAC,wBAAwB;gBAACC,KAAK,EAAE,EAAG;gBAACC,MAAM,EAAE,EAAG;gBAACJ,KAAK,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7F,OAAA,CAACX,aAAa;MACZ0R,IAAI,EAAExH,iBAAkB;MACxByH,OAAO,EAAEA,CAAA,KAAMxH,oBAAoB,CAAC,KAAK,CAAE;MAC3CC,QAAQ,EAAEA,QAAS;MACnBwH,YAAY,EAAE7F,gBAAiB;MAC/B8F,eAAe,EAAE1E,mBAAoB;MACrC2E,eAAe,EAAEpE,mBAAoB;MACrCqE,OAAO,EAAEzH,eAAgB;MACzB0H,UAAU,EAAEnJ,IAAI,CAAClG,IAAK;MACtBsP,UAAU,EAAC;IAAM;MAAA5L,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CAAC,eAGF7F,OAAA,CAACV,aAAa;MACZyR,IAAI,EAAElH,iBAAkB;MACxBmH,OAAO,EAAEA,CAAA,KAAMlH,oBAAoB,CAAC,KAAK,CAAE;MAC3C5B,IAAI,EAAEiC,SAAU;MAChBoH,eAAe,EAAEjD,mBAAoB;MACrCkD,UAAU,EAAEzH;IAAgB;MAAArE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7B,CAAC,eAGF7F,OAAA,CAACT,kBAAkB;MACjBwR,IAAI,EAAE9G,sBAAuB;MAC7B+G,OAAO,EAAEA,CAAA,KAAM9G,yBAAyB,CAAC,KAAK,CAAE;MAChDhC,IAAI,EAAEiC,SAAU;MAChBzI,YAAY,EAAEA,YAAa;MAC3BC,SAAS,EAAEA,SAAU;MACrBF,eAAe,EAAEgN;IAAoB;MAAA/I,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC;EAAA,eACF,CAAC;AAEP,CAAC;;AAED;AAAAgD,GAAA,CAr0BMT,QAAQ;AAAAqJ,GAAA,GAARrJ,QAAQ;AAs0Bd,MAAMuI,WAAW,GAAGe,KAAA,IAYd;EAAAC,GAAA;EAAA,IAZe;IACnBjB,OAAO;IACPvI,KAAK;IACLyI,aAAa;IACbC,QAAQ;IACR7P,wBAAwB;IACxBC,gBAAgB;IAChBG,eAAe;IACfI,eAAe;IACfC,eAAe;IACfC,YAAY;IACZC;EACF,CAAC,GAAA+P,KAAA;EACC,MAAM,CAAC9P,SAAS,EAAEC,YAAY,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC8T,WAAW,EAAEC,cAAc,CAAC,GAAG/T,QAAQ,CAAC4S,OAAO,CAAC1O,IAAI,CAAC;EAC5D,MAAM,CAAC8P,SAAS,EAAEC,YAAY,CAAC,GAAGjU,QAAQ,CAAC4S,OAAO,CAACtM,MAAM,KAAKlF,MAAM,CAACsO,SAAS,IAAIkD,OAAO,CAACvN,QAAQ,KAAK,GAAG,CAAC;EAC3G,MAAM6O,eAAe,GAAGjU,MAAM,CAAC,IAAI,CAAC;;EAEpC;EACA,MAAMkU,eAAe,GAAGjR,wBAAwB,GAAGA,wBAAwB,CAAC0P,OAAO,CAAC,GAAG,CAAC;EACxF,MAAMwB,aAAa,GAAGjR,gBAAgB,GAAGA,gBAAgB,CAACyP,OAAO,CAAC,GAAIA,OAAO,CAACtM,MAAM,IAAIlF,MAAM,CAACmE,WAAY;EAC3G,MAAM8O,mBAAmB,GAAGhT,aAAa,CAAC+S,aAAa,CAAC,IAAI/S,aAAa,CAACD,MAAM,CAACmE,WAAW,CAAC;;EAE7F;EACArF,SAAS,CAAC,MAAM;IACd+T,YAAY,CAACrB,OAAO,CAACtM,MAAM,KAAKlF,MAAM,CAACsO,SAAS,IAAIkD,OAAO,CAACvN,QAAQ,KAAK,GAAG,CAAC;EAC/E,CAAC,EAAE,CAACuN,OAAO,CAACtM,MAAM,EAAEsM,OAAO,CAACvN,QAAQ,CAAC,CAAC;;EAEtC;EACA,MAAMiP,sBAAsB,GAAGA,CAAA,KAAM;IACnCvQ,YAAY,CAAC,IAAI,CAAC;EACpB,CAAC;;EAED;EACA,MAAMwQ,iBAAiB,GAAGA,CAAA,KAAM;IAC9B;IACA,MAAMxO,WAAW,GAAG+N,WAAW,CAACnO,IAAI,CAAC,CAAC;;IAEtC;IACA,IAAII,WAAW,KAAK,EAAE,IAAIA,WAAW,KAAK6M,OAAO,CAAC1O,IAAI,EAAE;MACtD,IAAIZ,eAAe,EAAE;QACnBA,eAAe,CAAC;UAAE,GAAGsP,OAAO;UAAE1O,IAAI,EAAE6B,WAAW;UAAEqE,IAAI,EAAE2I;QAAS,CAAC,CAAC;MACpE;IACF,CAAC,MAAM;MACL;MACAgB,cAAc,CAACnB,OAAO,CAAC1O,IAAI,CAAC;IAC9B;IAEAH,YAAY,CAAC,KAAK,CAAC;EACrB,CAAC;;EAED;EACA,MAAMyQ,qBAAqB,GAAIvO,CAAC,IAAK;IACnC,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,EAAE;MACrBqO,iBAAiB,CAAC,CAAC;IACrB,CAAC,MAAM,IAAItO,CAAC,CAACC,GAAG,KAAK,QAAQ,EAAE;MAC7B6N,cAAc,CAACnB,OAAO,CAAC1O,IAAI,CAAC;MAC5BH,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;;EAED;EACA,MAAM0Q,kBAAkB,GAAGA,CAAA,KAAM;IAC/B;IACA,MAAMC,eAAe,GAAG,CAACV,SAAS;IAClCC,YAAY,CAACS,eAAe,CAAC;IAE7B,MAAMC,SAAS,GAAGD,eAAe,GAAGtT,MAAM,CAACsO,SAAS,GAAGtO,MAAM,CAACmE,WAAW;IACzE,MAAMqP,WAAW,GAAGF,eAAe,GAAG,GAAG,GAAG,CAAC;IAE7C,IAAIpR,eAAe,EAAE;MACnB;MACA,MAAMuR,WAAW,GAAG;QAClB,GAAGjC,OAAO;QACVtM,MAAM,EAAEqO,SAAS;QACjBtP,QAAQ,EAAEuP,WAAW;QACrBxK,IAAI,EAAE2I;MACR,CAAC;MAEDlG,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAE+H,WAAW,CAAC;MACnDvR,eAAe,CAACuR,WAAW,CAAC;IAC9B;EACF,CAAC;;EAED;EACA,MAAMC,wBAAwB,GAAGA,CAAA,KAAM;IACrC,IAAIC,MAAM,CAACC,OAAO,CAAC,+CAA+C,CAAC,EAAE;MACnEtR,eAAe,CAACkP,OAAO,EAAEG,QAAQ,CAAC;IACpC;EACF,CAAC;;EAED;EACA7S,SAAS,CAAC,MAAM;IACd,IAAI4D,SAAS,IAAIoQ,eAAe,CAAC1N,OAAO,EAAE;MACxC0N,eAAe,CAAC1N,OAAO,CAACC,KAAK,CAAC,CAAC;IACjC;EACF,CAAC,EAAE,CAAC3C,SAAS,CAAC,CAAC;EAEf,oBACE5B,OAAA,CAAC/B,GAAG;IACF0G,EAAE,EAAE;MACFI,OAAO,EAAE,MAAM;MACfC,UAAU,EAAE,QAAQ;MACpBmL,EAAE,EAAE,GAAG;MACPW,YAAY,EAAE3I,KAAK,GAAGyI,aAAa,GAAG,CAAC,GAAG,oBAAoB,GAAG;IACnE,CAAE;IAAA/L,QAAA,gBAEF7E,OAAA,CAACrB,QAAQ;MACPoU,OAAO,EAAEjB,SAAU;MACnB/L,QAAQ,EAAEwM,kBAAmB;MAC7BrN,IAAI,EAAC,OAAO;MACZP,EAAE,EAAE;QACF2C,CAAC,EAAE,GAAG;QACNlC,EAAE,EAAE,GAAG;QACPC,KAAK,EAAE,SAAS;QAChB,eAAe,EAAE;UACfA,KAAK,EAAE;QACT;MACF,CAAE;MACFE,IAAI,eAAEvF,OAAA,CAAChB,OAAO;QAACuG,IAAI,EAAC,0CAA0C;QAACC,KAAK,EAAE,EAAG;QAACC,MAAM,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAE;MACzFmN,WAAW,eAAEhT,OAAA,CAAChB,OAAO;QAACuG,IAAI,EAAC,4BAA4B;QAACC,KAAK,EAAE,EAAG;QAACC,MAAM,EAAE;MAAG;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAE;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnF,CAAC,EAEDjE,SAAS,gBACR5B,OAAA,CAAC/B,GAAG;MAAC0G,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEgK,QAAQ,EAAE;MAAE,CAAE;MAAAnK,QAAA,gBAC9D7E,OAAA,CAACxB,SAAS;QACRqE,QAAQ,EAAEmP,eAAgB;QAC1BlM,KAAK,EAAE8L,WAAY;QACnB7L,QAAQ,EAAGhC,CAAC,IAAK8N,cAAc,CAAC9N,CAAC,CAACiC,MAAM,CAACF,KAAK,CAAE;QAChDG,SAAS,EAAEqM,qBAAsB;QACjCpM,MAAM,EAAEmM,iBAAkB;QAC1BlM,OAAO,EAAC,UAAU;QAClBC,SAAS;QACTC,SAAS;QACT1B,EAAE,EAAE;UACF2B,UAAU,EAAE,kCAAkC;UAC9C,uBAAuB,EAAE;YACvBE,QAAQ,EAAE,SAAS;YACnBF,UAAU,EAAE;UACd;QACF;MAAE;QAAAZ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACF7F,OAAA,CAACvB,UAAU;QAACyG,IAAI,EAAC,OAAO;QAACC,OAAO,EAAEkN,iBAAkB;QAAC1N,EAAE,EAAE;UAAE8B,EAAE,EAAE;QAAE,CAAE;QAAA5B,QAAA,eACjE7E,OAAA,CAAChB,OAAO;UAACuG,IAAI,EAAC,wBAAwB;UAACC,KAAK,EAAE,EAAG;UAACC,MAAM,EAAE,EAAG;UAACJ,KAAK,EAAC;QAAS;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,gBAEN7F,OAAA,CAAC9B,UAAU;MACTiI,OAAO,EAAC,OAAO;MACfxB,EAAE,EAAE;QACFqK,QAAQ,EAAE,CAAC;QACXxI,QAAQ,EAAE,SAAS;QACnBF,UAAU,EAAE,kCAAkC;QAC9CjB,KAAK,EAAEyM,SAAS,GAAG,SAAS,GAAG,MAAM;QACrCvL,UAAU,EAAEuL,SAAS,GAAG,GAAG,GAAG,GAAG;QACjCnL,MAAM,EAAE,SAAS;QACjB5B,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpB,SAAS,EAAE;UACT,sBAAsB,EAAE;YACtB4B,OAAO,EAAE;UACX;QACF;MACF,CAAE;MACFzB,OAAO,EAAEiN,sBAAuB;MAAAvN,QAAA,GAE/B+M,WAAW,eACZ5R,OAAA,CAACtB,OAAO;QAACmI,KAAK,EAAC,mBAAmB;QAAAhC,QAAA,eAChC7E,OAAA,CAACvB,UAAU;UACTyG,IAAI,EAAC,OAAO;UACZT,SAAS,EAAC,mBAAmB;UAC7BE,EAAE,EAAE;YACF8B,EAAE,EAAE,GAAG;YACPG,OAAO,EAAE,CAAC;YACVE,UAAU,EAAE,cAAc;YAC1BlC,OAAO,EAAE;UACX,CAAE;UAAAC,QAAA,eAEF7E,OAAA,CAAChB,OAAO;YAACuG,IAAI,EAAC,+BAA+B;YAACC,KAAK,EAAE,EAAG;YAACC,MAAM,EAAE,EAAG;YAACJ,KAAK,EAAC;UAAM;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CACb,eAED7F,OAAA,CAAC/B,GAAG;MAAC0G,EAAE,EAAE;QAAEI,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE;MAAS,CAAE;MAAAH,QAAA,eAEjD7E,OAAA,CAACtB,OAAO;QAACmI,KAAK,EAAC,gBAAgB;QAAAhC,QAAA,eAC7B7E,OAAA,CAACvB,UAAU;UACTyG,IAAI,EAAC,OAAO;UACZC,OAAO,EAAEyN,wBAAyB;UAClCjO,EAAE,EAAE;YACF2C,CAAC,EAAE,GAAG;YACNjC,KAAK,EAAE,SAAS;YAChB,SAAS,EAAE;cACTC,eAAe,EAAE;YACnB;UACF,CAAE;UAAAT,QAAA,eAEF7E,OAAA,CAAChB,OAAO;YAACuG,IAAI,EAAC,iCAAiC;YAACC,KAAK,EAAE,EAAG;YAACC,MAAM,EAAE;UAAG;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAA8L,GAAA,CA9MMhB,WAAW;AAAAsC,GAAA,GAAXtC,WAAW;AA+MjB,SAASP,UAAUA,CAAC8C,UAAU,EAAE;EAC9B,IAAI,CAACA,UAAU,EAAE,OAAO,EAAE;EAE1B,IAAI;IACF,MAAMC,IAAI,GAAG,IAAI1H,IAAI,CAACyH,UAAU,CAAC;IACjC,IAAIE,KAAK,CAACD,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE;;IAEpC;IACA,MAAMC,OAAO,GAAG;MAAElH,KAAK,EAAE,OAAO;MAAED,GAAG,EAAE;IAAU,CAAC;IAClD,OAAOgH,IAAI,CAACI,kBAAkB,CAAC,OAAO,EAAED,OAAO,CAAC;EAClD,CAAC,CAAC,OAAOpI,KAAK,EAAE;IACd,OAAO,EAAE;EACX;AACF;AAEA,eAAe/K,aAAa;AAAC,IAAAwI,EAAA,EAAA8I,GAAA,EAAAwB,GAAA;AAAAO,YAAA,CAAA7K,EAAA;AAAA6K,YAAA,CAAA/B,GAAA;AAAA+B,YAAA,CAAAP,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}