{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useForkRef as useForkRef, unstable_useId as useId } from '@mui/utils';\nimport { extractEventHandlers } from '../utils/extractEventHandlers';\nimport { useListItem } from '../useList';\nimport { useCompoundItem } from '../useCompound';\nimport { useButton } from '../useButton';\nimport { combineHooksSlotProps } from '../utils/combineHooksSlotProps';\n/**\n *\n * Demos:\n *\n * - [Select](https://mui.com/base-ui/react-select/#hooks)\n *\n * API:\n *\n * - [useOption API](https://mui.com/base-ui/react-select/hooks-api/#use-option)\n */\nexport function useOption(params) {\n  const {\n    value,\n    label,\n    disabled,\n    rootRef: optionRefParam,\n    id: idParam\n  } = params;\n  const {\n    getRootProps: getListItemProps,\n    highlighted,\n    selected\n  } = useListItem({\n    item: value\n  });\n  const {\n    getRootProps: getButtonProps,\n    rootRef: buttonRefHandler\n  } = useButton({\n    disabled,\n    focusableWhenDisabled: true\n  });\n  const id = useId(idParam);\n  const optionRef = React.useRef(null);\n  const selectOption = React.useMemo(() => ({\n    disabled,\n    label,\n    value,\n    ref: optionRef,\n    id\n  }), [disabled, label, value, id]);\n  const {\n    index\n  } = useCompoundItem(value, selectOption);\n  const handleRef = useForkRef(optionRefParam, optionRef, buttonRefHandler);\n  const createHandleKeyDown = otherHandlers => event => {\n    var _otherHandlers$onKeyD;\n    (_otherHandlers$onKeyD = otherHandlers.onKeyDown) == null || _otherHandlers$onKeyD.call(otherHandlers, event);\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    if ([' ', 'Enter'].includes(event.key)) {\n      event.defaultMuiPrevented = true; // prevent listbox onKeyDown\n    }\n  };\n  const getOwnHandlers = function () {\n    let otherHandlers = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    return {\n      onKeyDown: createHandleKeyDown(otherHandlers)\n    };\n  };\n  return {\n    getRootProps: function () {\n      let externalProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n      const externalEventHandlers = extractEventHandlers(externalProps);\n      const getCombinedRootProps = combineHooksSlotProps(getListItemProps, combineHooksSlotProps(getButtonProps, getOwnHandlers));\n      return _extends({}, externalProps, externalEventHandlers, getCombinedRootProps(externalEventHandlers), {\n        id,\n        ref: handleRef,\n        role: 'option',\n        'aria-selected': selected\n      });\n    },\n    highlighted,\n    index,\n    selected,\n    rootRef: handleRef\n  };\n}", "map": {"version": 3, "names": ["_extends", "React", "unstable_useForkRef", "useForkRef", "unstable_useId", "useId", "extractEventHandlers", "useListItem", "useCompoundItem", "useButton", "combineHooksSlotProps", "useOption", "params", "value", "label", "disabled", "rootRef", "optionRefParam", "id", "idParam", "getRootProps", "getListItemProps", "highlighted", "selected", "item", "getButtonProps", "buttonRefHandler", "focusableWhenDisabled", "optionRef", "useRef", "selectOption", "useMemo", "ref", "index", "handleRef", "createHandleKeyDown", "otherHandlers", "event", "_otherHandlers$onKeyD", "onKeyDown", "call", "defaultMuiPrevented", "includes", "key", "getOwnHandlers", "arguments", "length", "undefined", "externalProps", "externalEventHandlers", "getCombinedRootProps", "role"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/useOption/useOption.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useForkRef as useForkRef, unstable_useId as useId } from '@mui/utils';\nimport { extractEventHandlers } from '../utils/extractEventHandlers';\nimport { useListItem } from '../useList';\nimport { useCompoundItem } from '../useCompound';\nimport { useButton } from '../useButton';\nimport { combineHooksSlotProps } from '../utils/combineHooksSlotProps';\n/**\n *\n * Demos:\n *\n * - [Select](https://mui.com/base-ui/react-select/#hooks)\n *\n * API:\n *\n * - [useOption API](https://mui.com/base-ui/react-select/hooks-api/#use-option)\n */\nexport function useOption(params) {\n  const {\n    value,\n    label,\n    disabled,\n    rootRef: optionRefParam,\n    id: idParam\n  } = params;\n  const {\n    getRootProps: getListItemProps,\n    highlighted,\n    selected\n  } = useListItem({\n    item: value\n  });\n  const {\n    getRootProps: getButtonProps,\n    rootRef: buttonRefHandler\n  } = useButton({\n    disabled,\n    focusableWhenDisabled: true\n  });\n  const id = useId(idParam);\n  const optionRef = React.useRef(null);\n  const selectOption = React.useMemo(() => ({\n    disabled,\n    label,\n    value,\n    ref: optionRef,\n    id\n  }), [disabled, label, value, id]);\n  const {\n    index\n  } = useCompoundItem(value, selectOption);\n  const handleRef = useForkRef(optionRefParam, optionRef, buttonRefHandler);\n  const createHandleKeyDown = otherHandlers => event => {\n    var _otherHandlers$onKeyD;\n    (_otherHandlers$onKeyD = otherHandlers.onKeyDown) == null || _otherHandlers$onKeyD.call(otherHandlers, event);\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    if ([' ', 'Enter'].includes(event.key)) {\n      event.defaultMuiPrevented = true; // prevent listbox onKeyDown\n    }\n  };\n  const getOwnHandlers = (otherHandlers = {}) => ({\n    onKeyDown: createHandleKeyDown(otherHandlers)\n  });\n  return {\n    getRootProps: (externalProps = {}) => {\n      const externalEventHandlers = extractEventHandlers(externalProps);\n      const getCombinedRootProps = combineHooksSlotProps(getListItemProps, combineHooksSlotProps(getButtonProps, getOwnHandlers));\n      return _extends({}, externalProps, externalEventHandlers, getCombinedRootProps(externalEventHandlers), {\n        id,\n        ref: handleRef,\n        role: 'option',\n        'aria-selected': selected\n      });\n    },\n    highlighted,\n    index,\n    selected,\n    rootRef: handleRef\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,mBAAmB,IAAIC,UAAU,EAAEC,cAAc,IAAIC,KAAK,QAAQ,YAAY;AACvF,SAASC,oBAAoB,QAAQ,+BAA+B;AACpE,SAASC,WAAW,QAAQ,YAAY;AACxC,SAASC,eAAe,QAAQ,gBAAgB;AAChD,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,SAASA,CAACC,MAAM,EAAE;EAChC,MAAM;IACJC,KAAK;IACLC,KAAK;IACLC,QAAQ;IACRC,OAAO,EAAEC,cAAc;IACvBC,EAAE,EAAEC;EACN,CAAC,GAAGP,MAAM;EACV,MAAM;IACJQ,YAAY,EAAEC,gBAAgB;IAC9BC,WAAW;IACXC;EACF,CAAC,GAAGhB,WAAW,CAAC;IACdiB,IAAI,EAAEX;EACR,CAAC,CAAC;EACF,MAAM;IACJO,YAAY,EAAEK,cAAc;IAC5BT,OAAO,EAAEU;EACX,CAAC,GAAGjB,SAAS,CAAC;IACZM,QAAQ;IACRY,qBAAqB,EAAE;EACzB,CAAC,CAAC;EACF,MAAMT,EAAE,GAAGb,KAAK,CAACc,OAAO,CAAC;EACzB,MAAMS,SAAS,GAAG3B,KAAK,CAAC4B,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMC,YAAY,GAAG7B,KAAK,CAAC8B,OAAO,CAAC,OAAO;IACxChB,QAAQ;IACRD,KAAK;IACLD,KAAK;IACLmB,GAAG,EAAEJ,SAAS;IACdV;EACF,CAAC,CAAC,EAAE,CAACH,QAAQ,EAAED,KAAK,EAAED,KAAK,EAAEK,EAAE,CAAC,CAAC;EACjC,MAAM;IACJe;EACF,CAAC,GAAGzB,eAAe,CAACK,KAAK,EAAEiB,YAAY,CAAC;EACxC,MAAMI,SAAS,GAAG/B,UAAU,CAACc,cAAc,EAAEW,SAAS,EAAEF,gBAAgB,CAAC;EACzE,MAAMS,mBAAmB,GAAGC,aAAa,IAAIC,KAAK,IAAI;IACpD,IAAIC,qBAAqB;IACzB,CAACA,qBAAqB,GAAGF,aAAa,CAACG,SAAS,KAAK,IAAI,IAAID,qBAAqB,CAACE,IAAI,CAACJ,aAAa,EAAEC,KAAK,CAAC;IAC7G,IAAIA,KAAK,CAACI,mBAAmB,EAAE;MAC7B;IACF;IACA,IAAI,CAAC,GAAG,EAAE,OAAO,CAAC,CAACC,QAAQ,CAACL,KAAK,CAACM,GAAG,CAAC,EAAE;MACtCN,KAAK,CAACI,mBAAmB,GAAG,IAAI,CAAC,CAAC;IACpC;EACF,CAAC;EACD,MAAMG,cAAc,GAAG,SAAAA,CAAA;IAAA,IAACR,aAAa,GAAAS,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;IAAA,OAAM;MAC9CN,SAAS,EAAEJ,mBAAmB,CAACC,aAAa;IAC9C,CAAC;EAAA,CAAC;EACF,OAAO;IACLhB,YAAY,EAAE,SAAAA,CAAA,EAAwB;MAAA,IAAvB4B,aAAa,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;MAC/B,MAAMI,qBAAqB,GAAG3C,oBAAoB,CAAC0C,aAAa,CAAC;MACjE,MAAME,oBAAoB,GAAGxC,qBAAqB,CAACW,gBAAgB,EAAEX,qBAAqB,CAACe,cAAc,EAAEmB,cAAc,CAAC,CAAC;MAC3H,OAAO5C,QAAQ,CAAC,CAAC,CAAC,EAAEgD,aAAa,EAAEC,qBAAqB,EAAEC,oBAAoB,CAACD,qBAAqB,CAAC,EAAE;QACrG/B,EAAE;QACFc,GAAG,EAAEE,SAAS;QACdiB,IAAI,EAAE,QAAQ;QACd,eAAe,EAAE5B;MACnB,CAAC,CAAC;IACJ,CAAC;IACDD,WAAW;IACXW,KAAK;IACLV,QAAQ;IACRP,OAAO,EAAEkB;EACX,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}