{"ast": null, "code": "import * as React from 'react';\nconst ModalDialogSizeContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== 'production') {\n  ModalDialogSizeContext.displayName = 'ModalDialogSizeContext';\n}\nexport default ModalDialogSizeContext;", "map": {"version": 3, "names": ["React", "ModalDialogSizeContext", "createContext", "undefined", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/ModalDialog/ModalDialogSizeContext.js"], "sourcesContent": ["import * as React from 'react';\nconst ModalDialogSizeContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== 'production') {\n  ModalDialogSizeContext.displayName = 'ModalDialogSizeContext';\n}\nexport default ModalDialogSizeContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,sBAAsB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAACC,SAAS,CAAC;AAC1E,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCL,sBAAsB,CAACM,WAAW,GAAG,wBAAwB;AAC/D;AACA,eAAeN,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}