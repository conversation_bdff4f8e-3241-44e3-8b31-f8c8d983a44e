{"ast": null, "code": "import * as React from 'react';\nconst SizeTabsContext = /*#__PURE__*/React.createContext('md');\nif (process.env.NODE_ENV !== 'production') {\n  SizeTabsContext.displayName = 'SizeTabsContext';\n}\nexport default SizeTabsContext;", "map": {"version": 3, "names": ["React", "SizeTabsContext", "createContext", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Tabs/SizeTabsContext.js"], "sourcesContent": ["import * as React from 'react';\nconst SizeTabsContext = /*#__PURE__*/React.createContext('md');\nif (process.env.NODE_ENV !== 'production') {\n  SizeTabsContext.displayName = 'SizeTabsContext';\n}\nexport default SizeTabsContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,eAAe,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AAC9D,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,eAAe,CAACK,WAAW,GAAG,iBAAiB;AACjD;AACA,eAAeL,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}