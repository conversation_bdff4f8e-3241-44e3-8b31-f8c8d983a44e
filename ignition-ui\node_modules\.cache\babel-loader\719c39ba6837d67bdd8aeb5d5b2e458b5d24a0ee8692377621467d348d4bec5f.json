{"ast": null, "code": "import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nconst COMPONENT_NAME = 'MenuItem';\nexport function getMenuItemUtilityClass(slot) {\n  return generateUtilityClass(COMPONENT_NAME, slot);\n}\nexport const menuItemClasses = generateUtilityClasses(COMPONENT_NAME, ['root', 'disabled', 'focusVisible']);", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "COMPONENT_NAME", "getMenuItemUtilityClass", "slot", "menuItemClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/MenuItem/menuItemClasses.js"], "sourcesContent": ["import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nconst COMPONENT_NAME = 'MenuItem';\nexport function getMenuItemUtilityClass(slot) {\n  return generateUtilityClass(COMPONENT_NAME, slot);\n}\nexport const menuItemClasses = generateUtilityClasses(COMPONENT_NAME, ['root', 'disabled', 'focusVisible']);"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,MAAMC,cAAc,GAAG,UAAU;AACjC,OAAO,SAASC,uBAAuBA,CAACC,IAAI,EAAE;EAC5C,OAAOJ,oBAAoB,CAACE,cAAc,EAAEE,IAAI,CAAC;AACnD;AACA,OAAO,MAAMC,eAAe,GAAGJ,sBAAsB,CAACC,cAAc,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,cAAc,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}