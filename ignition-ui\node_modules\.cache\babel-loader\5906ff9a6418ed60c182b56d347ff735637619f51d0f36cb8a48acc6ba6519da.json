{"ast": null, "code": "export { Modal } from './Modal';\nexport * from './Modal.types';\nexport * from './modalClasses';", "map": {"version": 3, "names": ["Modal"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/Modal/index.js"], "sourcesContent": ["export { Modal } from './Modal';\nexport * from './Modal.types';\nexport * from './modalClasses';"], "mappings": "AAAA,SAASA,KAAK,QAAQ,SAAS;AAC/B,cAAc,eAAe;AAC7B,cAAc,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}