{"ast": null, "code": "'use strict';\n\nvar implementation = require('./implementation');\nmodule.exports = function getPolyfill() {\n  return typeof Object.is === 'function' ? Object.is : implementation;\n};", "map": {"version": 3, "names": ["implementation", "require", "module", "exports", "getPolyfill", "Object", "is"], "sources": ["C:/ignition/ignition-ui/node_modules/object-is/polyfill.js"], "sourcesContent": ["'use strict';\n\nvar implementation = require('./implementation');\n\nmodule.exports = function getPolyfill() {\n\treturn typeof Object.is === 'function' ? Object.is : implementation;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,cAAc,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAEhDC,MAAM,CAACC,OAAO,GAAG,SAASC,WAAWA,CAAA,EAAG;EACvC,OAAO,OAAOC,MAAM,CAACC,EAAE,KAAK,UAAU,GAAGD,MAAM,CAACC,EAAE,GAAGN,cAAc;AACpE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}