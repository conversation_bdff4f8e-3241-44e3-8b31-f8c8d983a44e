{"ast": null, "code": "const colors = {\n  grey: {\n    50: '#FBFCFE',\n    100: '#F0F4F8',\n    200: '#DDE7EE',\n    300: '#CDD7E1',\n    400: '#9FA6AD',\n    500: '#636B74',\n    600: '#555E68',\n    700: '#32383E',\n    800: '#171A1C',\n    900: '#0B0D0E'\n  },\n  blue: {\n    50: '#EDF5FD',\n    100: '#E3EFFB',\n    200: '#C7DFF7',\n    300: '#97C3F0',\n    400: '#4393E4',\n    500: '#0B6BCB',\n    600: '#185EA5',\n    700: '#12467B',\n    800: '#0A2744',\n    900: '#051423'\n  },\n  yellow: {\n    50: '#FEFAF6',\n    100: '#FDF0E1',\n    200: '#FCE1C2',\n    300: '#F3C896',\n    400: '#EA9A3E',\n    500: '#9A5B13',\n    600: '#72430D',\n    700: '#492B08',\n    800: '#2E1B05',\n    900: '#1D1002'\n  },\n  red: {\n    50: '#FEF6F6',\n    100: '#FCE4E4',\n    200: '#F7C5C5',\n    300: '#F09898',\n    400: '#E47474',\n    500: '#C41C1C',\n    600: '#A51818',\n    700: '#7D1212',\n    800: '#430A0A',\n    900: '#240505'\n  },\n  green: {\n    50: '#F6FEF6',\n    100: '#E3FBE3',\n    200: '#C7F7C7',\n    300: '#A1E8A1',\n    400: '#51BC51',\n    500: '#1F7A1F',\n    600: '#136C13',\n    700: '#0A470A',\n    800: '#042F04',\n    900: '#021D02'\n  }\n};\nexport default colors;", "map": {"version": 3, "names": ["colors", "grey", "blue", "yellow", "red", "green"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/colors/colors.js"], "sourcesContent": ["const colors = {\n  grey: {\n    50: '#FBFCFE',\n    100: '#F0F4F8',\n    200: '#DDE7EE',\n    300: '#CDD7E1',\n    400: '#9FA6AD',\n    500: '#636B74',\n    600: '#555E68',\n    700: '#32383E',\n    800: '#171A1C',\n    900: '#0B0D0E'\n  },\n  blue: {\n    50: '#EDF5FD',\n    100: '#E3EFFB',\n    200: '#C7DFF7',\n    300: '#97C3F0',\n    400: '#4393E4',\n    500: '#0B6BCB',\n    600: '#185EA5',\n    700: '#12467B',\n    800: '#0A2744',\n    900: '#051423'\n  },\n  yellow: {\n    50: '#FEFAF6',\n    100: '#FDF0E1',\n    200: '#FCE1C2',\n    300: '#F3C896',\n    400: '#EA9A3E',\n    500: '#9A5B13',\n    600: '#72430D',\n    700: '#492B08',\n    800: '#2E1B05',\n    900: '#1D1002'\n  },\n  red: {\n    50: '#FEF6F6',\n    100: '#FCE4E4',\n    200: '#F7C5C5',\n    300: '#F09898',\n    400: '#E47474',\n    500: '#C41C1C',\n    600: '#A51818',\n    700: '#7D1212',\n    800: '#430A0A',\n    900: '#240505'\n  },\n  green: {\n    50: '#F6FEF6',\n    100: '#E3FBE3',\n    200: '#C7F7C7',\n    300: '#A1E8A1',\n    400: '#51BC51',\n    500: '#1F7A1F',\n    600: '#136C13',\n    700: '#0A470A',\n    800: '#042F04',\n    900: '#021D02'\n  }\n};\nexport default colors;"], "mappings": "AAAA,MAAMA,MAAM,GAAG;EACbC,IAAI,EAAE;IACJ,EAAE,EAAE,SAAS;IACb,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE;EACP,CAAC;EACDC,IAAI,EAAE;IACJ,EAAE,EAAE,SAAS;IACb,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE;EACP,CAAC;EACDC,MAAM,EAAE;IACN,EAAE,EAAE,SAAS;IACb,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE;EACP,CAAC;EACDC,GAAG,EAAE;IACH,EAAE,EAAE,SAAS;IACb,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE;EACP,CAAC;EACDC,KAAK,EAAE;IACL,EAAE,EAAE,SAAS;IACb,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE,SAAS;IACd,GAAG,EAAE;EACP;AACF,CAAC;AACD,eAAeL,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}