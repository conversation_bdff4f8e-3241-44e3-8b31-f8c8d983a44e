{"ast": null, "code": "var _jsxFileName = \"C:\\\\ignition\\\\ignition-ui\\\\src\\\\layouts\\\\Admin.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useCallback } from \"react\";\nimport { useLocation, Route, Routes, Navigate } from \"react-router-dom\";\nimport MobileHeader from \"components/Sidebar/MobileHeader\";\nimport AdminFooter from \"components/Footers/AdminFooter\";\nimport SidebarComponent from \"components/Sidebar/SidebarComponent\";\nimport RightSidebar from \"components/Sidebar/RightSidebar\";\nimport { ADMIN_PAGE_KEY, SIDEBAR_COLLAPSED_LEFT_PX, SIDEBAR_EXPANED_LEFT_PX } from \"helpers/constants\";\nimport { getRoutes } from \"helpers/auth\";\nimport routes from \"routes/index\";\n\n//--------------------------------------------------------------------------------------------------\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AdminLayout = () => {\n  _s();\n  const mainContent = React.useRef(null);\n  const location = useLocation();\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(true);\n  const [isSmartphone, setIsSmartphone] = useState(window.innerWidth <= 768);\n  const [rightSidebarOpen, setRightSidebarOpen] = useState(true);\n\n  // Kiểm tra xem có nên hiển thị right sidebar hay không dựa trên đường dẫn hiện tại\n  const shouldShowRightSidebar = useCallback(() => {\n    const path = location.pathname;\n    console.log(\"Current path:\", path);\n\n    // Danh sách các đường dẫn cần ẩn right sidebar\n    const hiddenPaths = [\n    // Trang Calendar\n    '/my/tasks/calendar', '/d/my/tasks/calendar',\n    // Trang Profile\n    '/profile', '/d/profile',\n    // Plan Creation Page\n    '/plan/create', '/d/plan/create'\n\n    // Không ẩn ở trang Home (index.js)\n    // Removed todo table paths since we removed the todo list page\n    ];\n\n    // Kiểm tra xem đường dẫn hiện tại có nằm trong danh sách cần ẩn không\n    const shouldHide = hiddenPaths.some(hiddenPath => {\n      if (hiddenPath.endsWith('/')) {\n        return path === hiddenPath || path === hiddenPath.slice(0, -1);\n      }\n      return path === hiddenPath || path === hiddenPath + '/';\n    });\n    console.log(\"Should hide right sidebar:\", shouldHide);\n    return !shouldHide;\n  }, [location.pathname]);\n  useEffect(() => {\n    document.documentElement.scrollTop = 0;\n    document.scrollingElement.scrollTop = 0;\n    mainContent.current.scrollTop = 0;\n  }, [location]);\n  useEffect(() => {\n    const handleResize = () => {\n      const newIsSmartphone = window.innerWidth <= 768;\n      setIsSmartphone(newIsSmartphone);\n      if (newIsSmartphone) {\n        setRightSidebarOpen(false);\n      } else {\n        setRightSidebarOpen(shouldShowRightSidebar());\n      }\n    };\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, [location, shouldShowRightSidebar]);\n\n  // Cập nhật trạng thái right sidebar khi đường dẫn thay đổi\n  useEffect(() => {\n    if (!isSmartphone) {\n      const show = shouldShowRightSidebar();\n      console.log(\"Setting rightSidebarOpen to:\", show);\n      setRightSidebarOpen(show);\n    }\n  }, [location, isSmartphone, shouldShowRightSidebar]);\n  const toggleRightSidebar = () => {\n    setRightSidebarOpen(!rightSidebarOpen);\n  };\n\n  // Tính toán margin và width cho main content\n  const leftMargin = isSmartphone ? '0' : sidebarCollapsed ? SIDEBAR_COLLAPSED_LEFT_PX : SIDEBAR_EXPANED_LEFT_PX;\n  const rightMargin = isSmartphone ? '0' : rightSidebarOpen && shouldShowRightSidebar() ? '300px' : '0';\n  const mainContentStyle = {\n    marginLeft: leftMargin,\n    marginRight: rightMargin,\n    transition: 'margin-left 0.3s ease, margin-right 0.3s ease',\n    width: isSmartphone ? '100%' : `calc(100% - ${parseInt(leftMargin) + parseInt(rightMargin)}px)`\n  };\n\n  // Log để debug\n  console.log(\"rightSidebarOpen:\", rightSidebarOpen);\n  console.log(\"shouldShowRightSidebar:\", shouldShowRightSidebar());\n  console.log(\"rightMargin:\", rightMargin);\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [isSmartphone ? /*#__PURE__*/_jsxDEV(MobileHeader, {\n      onCollapseChange: setSidebarCollapsed,\n      logo: {\n        innerLink: \"/d/\",\n        imgSrc: require(\"../assets/main_logo.png\"),\n        imgAlt: \"Logo\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 24\n    }, this) : /*#__PURE__*/_jsxDEV(SidebarComponent, {\n      onCollapseChange: setSidebarCollapsed,\n      logo: {\n        innerLink: \"/d\",\n        imgSrc: require(sidebarCollapsed ? \"../assets/fire_logo.png\" : \"../assets/main_logo.png\"),\n        imgAlt: \"Logo\"\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 116,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"main-content\",\n      ref: mainContent,\n      style: mainContentStyle,\n      children: [/*#__PURE__*/_jsxDEV(Routes, {\n        children: [getRoutes(routes, ADMIN_PAGE_KEY), /*#__PURE__*/_jsxDEV(Route, {\n          path: \"*\",\n          element: /*#__PURE__*/_jsxDEV(Navigate, {\n            to: \"/d/\",\n            replace: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 126,\n            columnNumber: 36\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AdminFooter, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 7\n    }, this), shouldShowRightSidebar() && /*#__PURE__*/_jsxDEV(RightSidebar, {\n      isOpen: rightSidebarOpen,\n      onToggle: toggleRightSidebar\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true);\n};\n_s(AdminLayout, \"vbxWC4MtFQ+mlnDbEbqcYpld/VU=\", false, function () {\n  return [useLocation];\n});\n_c = AdminLayout;\nexport default AdminLayout;\nvar _c;\n$RefreshReg$(_c, \"AdminLayout\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useCallback", "useLocation", "Route", "Routes", "Navigate", "MobileHeader", "<PERSON><PERSON><PERSON><PERSON>er", "SidebarComponent", "RightSidebar", "ADMIN_PAGE_KEY", "SIDEBAR_COLLAPSED_LEFT_PX", "SIDEBAR_EXPANED_LEFT_PX", "getRoutes", "routes", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AdminLayout", "_s", "mainContent", "useRef", "location", "sidebarCollapsed", "setSidebarCollapsed", "isSmartphone", "setIsSmartphone", "window", "innerWidth", "rightSidebarOpen", "setRightSidebarOpen", "shouldShowRightSidebar", "path", "pathname", "console", "log", "hiddenPaths", "shouldHide", "some", "<PERSON><PERSON><PERSON>", "endsWith", "slice", "document", "documentElement", "scrollTop", "scrollingElement", "current", "handleResize", "newIsSmartphone", "addEventListener", "removeEventListener", "show", "toggleRightSidebar", "leftMargin", "<PERSON><PERSON><PERSON><PERSON>", "mainContentStyle", "marginLeft", "marginRight", "transition", "width", "parseInt", "children", "onCollapseChange", "logo", "innerLink", "imgSrc", "require", "imgAlt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "ref", "style", "element", "to", "replace", "isOpen", "onToggle", "_c", "$RefreshReg$"], "sources": ["C:/ignition/ignition-ui/src/layouts/Admin.js"], "sourcesContent": ["import React, { useEffect, useState, useCallback } from \"react\";\r\nimport { useLocation, Route, Routes, Navigate } from \"react-router-dom\";\r\nimport MobileHeader from \"components/Sidebar/MobileHeader\";\r\nimport AdminFooter from \"components/Footers/AdminFooter\";\r\nimport SidebarComponent from \"components/Sidebar/SidebarComponent\";\r\nimport RightSidebar from \"components/Sidebar/RightSidebar\";\r\nimport { ADMIN_PAGE_KEY, SIDEBAR_COLLAPSED_LEFT_PX, SIDEBAR_EXPANED_LEFT_PX } from \"helpers/constants\";\r\nimport { getRoutes } from \"helpers/auth\";\r\nimport routes from \"routes/index\";\r\n\r\n//--------------------------------------------------------------------------------------------------\r\n\r\nconst AdminLayout = () => {\r\n  const mainContent = React.useRef(null);\r\n  const location = useLocation();\r\n  const [sidebarCollapsed, setSidebarCollapsed] = useState(true);\r\n  const [isSmartphone, setIsSmartphone] = useState(window.innerWidth <= 768);\r\n  const [rightSidebarOpen, setRightSidebarOpen] = useState(true);\r\n\r\n  // Kiểm tra xem có nên hiển thị right sidebar hay không dựa trên đường dẫn hiện tại\r\n  const shouldShowRightSidebar = useCallback(() => {\r\n    const path = location.pathname;\r\n    console.log(\"Current path:\", path);\r\n    \r\n    // Danh sách các đường dẫn cần ẩn right sidebar\r\n    const hiddenPaths = [\r\n      // Trang Calendar\r\n      '/my/tasks/calendar',\r\n      '/d/my/tasks/calendar',\r\n\r\n      // Trang Profile\r\n      '/profile',\r\n      '/d/profile',\r\n\r\n      // Plan Creation Page\r\n      '/plan/create',\r\n      '/d/plan/create'\r\n\r\n      // Không ẩn ở trang Home (index.js)\r\n      // Removed todo table paths since we removed the todo list page\r\n    ];\r\n    \r\n    // Kiểm tra xem đường dẫn hiện tại có nằm trong danh sách cần ẩn không\r\n    const shouldHide = hiddenPaths.some(hiddenPath => {\r\n      if (hiddenPath.endsWith('/')) {\r\n        return path === hiddenPath || path === hiddenPath.slice(0, -1);\r\n      }\r\n      return path === hiddenPath || path === hiddenPath + '/';\r\n    });\r\n    \r\n    console.log(\"Should hide right sidebar:\", shouldHide);\r\n    return !shouldHide;\r\n  }, [location.pathname]);\r\n\r\n  useEffect(() => {\r\n    document.documentElement.scrollTop = 0;\r\n    document.scrollingElement.scrollTop = 0;\r\n    mainContent.current.scrollTop = 0;\r\n  }, [location]);\r\n\r\n  useEffect(() => {\r\n    const handleResize = () => {\r\n      const newIsSmartphone = window.innerWidth <= 768;\r\n      setIsSmartphone(newIsSmartphone);\r\n      \r\n      if (newIsSmartphone) {\r\n        setRightSidebarOpen(false);\r\n      } else {\r\n        setRightSidebarOpen(shouldShowRightSidebar());\r\n      }\r\n    };\r\n    \r\n    window.addEventListener('resize', handleResize);\r\n    return () => window.removeEventListener('resize', handleResize);\r\n  }, [location, shouldShowRightSidebar]);\r\n\r\n  // Cập nhật trạng thái right sidebar khi đường dẫn thay đổi\r\n  useEffect(() => {\r\n    if (!isSmartphone) {\r\n      const show = shouldShowRightSidebar();\r\n      console.log(\"Setting rightSidebarOpen to:\", show);\r\n      setRightSidebarOpen(show);\r\n    }\r\n  }, [location, isSmartphone, shouldShowRightSidebar]);\r\n\r\n  const toggleRightSidebar = () => {\r\n    setRightSidebarOpen(!rightSidebarOpen);\r\n  };\r\n\r\n  // Tính toán margin và width cho main content\r\n  const leftMargin = isSmartphone ? '0' : sidebarCollapsed ? SIDEBAR_COLLAPSED_LEFT_PX : SIDEBAR_EXPANED_LEFT_PX;\r\n  const rightMargin = isSmartphone ? '0' : (rightSidebarOpen && shouldShowRightSidebar()) ? '300px' : '0';\r\n  \r\n  const mainContentStyle = {\r\n    marginLeft: leftMargin,\r\n    marginRight: rightMargin,\r\n    transition: 'margin-left 0.3s ease, margin-right 0.3s ease',\r\n    width: isSmartphone ? '100%' : `calc(100% - ${parseInt(leftMargin) + parseInt(rightMargin)}px)`,\r\n  };\r\n\r\n  // Log để debug\r\n  console.log(\"rightSidebarOpen:\", rightSidebarOpen);\r\n  console.log(\"shouldShowRightSidebar:\", shouldShowRightSidebar());\r\n  console.log(\"rightMargin:\", rightMargin);\r\n\r\n  return (\r\n    <>\r\n      {isSmartphone ? (<MobileHeader\r\n        onCollapseChange={setSidebarCollapsed}\r\n        logo={{\r\n          innerLink: \"/d/\",\r\n          imgSrc: require(\"../assets/main_logo.png\"),\r\n          imgAlt: \"Logo\",\r\n        }} />\r\n      ) : (\r\n        <SidebarComponent onCollapseChange={setSidebarCollapsed}\r\n          logo={{\r\n            innerLink: \"/d\",\r\n            imgSrc: require(sidebarCollapsed ? \"../assets/fire_logo.png\" : \"../assets/main_logo.png\"),\r\n            imgAlt: \"Logo\",\r\n          }} />\r\n      )}\r\n      <div className=\"main-content\" ref={mainContent} style={mainContentStyle}>\r\n        <Routes>\r\n          {getRoutes(routes, ADMIN_PAGE_KEY)}\r\n          <Route path=\"*\" element={<Navigate to=\"/d/\" replace />} />\r\n        </Routes>\r\n        <AdminFooter />\r\n      </div>\r\n      \r\n      {/* Right Sidebar for Today's Tasks - Chỉ hiển thị ở các trang được chỉ định */}\r\n      {shouldShowRightSidebar() && (\r\n        <RightSidebar isOpen={rightSidebarOpen} onToggle={toggleRightSidebar} />\r\n      )}\r\n    </>\r\n  );\r\n};\r\n\r\nexport default AdminLayout;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AAC/D,SAASC,WAAW,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,kBAAkB;AACvE,OAAOC,YAAY,MAAM,iCAAiC;AAC1D,OAAOC,WAAW,MAAM,gCAAgC;AACxD,OAAOC,gBAAgB,MAAM,qCAAqC;AAClE,OAAOC,YAAY,MAAM,iCAAiC;AAC1D,SAASC,cAAc,EAAEC,yBAAyB,EAAEC,uBAAuB,QAAQ,mBAAmB;AACtG,SAASC,SAAS,QAAQ,cAAc;AACxC,OAAOC,MAAM,MAAM,cAAc;;AAEjC;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEA,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,WAAW,GAAGvB,KAAK,CAACwB,MAAM,CAAC,IAAI,CAAC;EACtC,MAAMC,QAAQ,GAAGrB,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACsB,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGzB,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC4B,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;EAC1E,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/B,QAAQ,CAAC,IAAI,CAAC;;EAE9D;EACA,MAAMgC,sBAAsB,GAAG/B,WAAW,CAAC,MAAM;IAC/C,MAAMgC,IAAI,GAAGV,QAAQ,CAACW,QAAQ;IAC9BC,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEH,IAAI,CAAC;;IAElC;IACA,MAAMI,WAAW,GAAG;IAClB;IACA,oBAAoB,EACpB,sBAAsB;IAEtB;IACA,UAAU,EACV,YAAY;IAEZ;IACA,cAAc,EACd;;IAEA;IACA;IAAA,CACD;;IAED;IACA,MAAMC,UAAU,GAAGD,WAAW,CAACE,IAAI,CAACC,UAAU,IAAI;MAChD,IAAIA,UAAU,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;QAC5B,OAAOR,IAAI,KAAKO,UAAU,IAAIP,IAAI,KAAKO,UAAU,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;MAChE;MACA,OAAOT,IAAI,KAAKO,UAAU,IAAIP,IAAI,KAAKO,UAAU,GAAG,GAAG;IACzD,CAAC,CAAC;IAEFL,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAEE,UAAU,CAAC;IACrD,OAAO,CAACA,UAAU;EACpB,CAAC,EAAE,CAACf,QAAQ,CAACW,QAAQ,CAAC,CAAC;EAEvBnC,SAAS,CAAC,MAAM;IACd4C,QAAQ,CAACC,eAAe,CAACC,SAAS,GAAG,CAAC;IACtCF,QAAQ,CAACG,gBAAgB,CAACD,SAAS,GAAG,CAAC;IACvCxB,WAAW,CAAC0B,OAAO,CAACF,SAAS,GAAG,CAAC;EACnC,CAAC,EAAE,CAACtB,QAAQ,CAAC,CAAC;EAEdxB,SAAS,CAAC,MAAM;IACd,MAAMiD,YAAY,GAAGA,CAAA,KAAM;MACzB,MAAMC,eAAe,GAAGrB,MAAM,CAACC,UAAU,IAAI,GAAG;MAChDF,eAAe,CAACsB,eAAe,CAAC;MAEhC,IAAIA,eAAe,EAAE;QACnBlB,mBAAmB,CAAC,KAAK,CAAC;MAC5B,CAAC,MAAM;QACLA,mBAAmB,CAACC,sBAAsB,CAAC,CAAC,CAAC;MAC/C;IACF,CAAC;IAEDJ,MAAM,CAACsB,gBAAgB,CAAC,QAAQ,EAAEF,YAAY,CAAC;IAC/C,OAAO,MAAMpB,MAAM,CAACuB,mBAAmB,CAAC,QAAQ,EAAEH,YAAY,CAAC;EACjE,CAAC,EAAE,CAACzB,QAAQ,EAAES,sBAAsB,CAAC,CAAC;;EAEtC;EACAjC,SAAS,CAAC,MAAM;IACd,IAAI,CAAC2B,YAAY,EAAE;MACjB,MAAM0B,IAAI,GAAGpB,sBAAsB,CAAC,CAAC;MACrCG,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEgB,IAAI,CAAC;MACjDrB,mBAAmB,CAACqB,IAAI,CAAC;IAC3B;EACF,CAAC,EAAE,CAAC7B,QAAQ,EAAEG,YAAY,EAAEM,sBAAsB,CAAC,CAAC;EAEpD,MAAMqB,kBAAkB,GAAGA,CAAA,KAAM;IAC/BtB,mBAAmB,CAAC,CAACD,gBAAgB,CAAC;EACxC,CAAC;;EAED;EACA,MAAMwB,UAAU,GAAG5B,YAAY,GAAG,GAAG,GAAGF,gBAAgB,GAAGb,yBAAyB,GAAGC,uBAAuB;EAC9G,MAAM2C,WAAW,GAAG7B,YAAY,GAAG,GAAG,GAAII,gBAAgB,IAAIE,sBAAsB,CAAC,CAAC,GAAI,OAAO,GAAG,GAAG;EAEvG,MAAMwB,gBAAgB,GAAG;IACvBC,UAAU,EAAEH,UAAU;IACtBI,WAAW,EAAEH,WAAW;IACxBI,UAAU,EAAE,+CAA+C;IAC3DC,KAAK,EAAElC,YAAY,GAAG,MAAM,GAAG,eAAemC,QAAQ,CAACP,UAAU,CAAC,GAAGO,QAAQ,CAACN,WAAW,CAAC;EAC5F,CAAC;;EAED;EACApB,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEN,gBAAgB,CAAC;EAClDK,OAAO,CAACC,GAAG,CAAC,yBAAyB,EAAEJ,sBAAsB,CAAC,CAAC,CAAC;EAChEG,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEmB,WAAW,CAAC;EAExC,oBACEvC,OAAA,CAAAE,SAAA;IAAA4C,QAAA,GACGpC,YAAY,gBAAIV,OAAA,CAACV,YAAY;MAC5ByD,gBAAgB,EAAEtC,mBAAoB;MACtCuC,IAAI,EAAE;QACJC,SAAS,EAAE,KAAK;QAChBC,MAAM,EAAEC,OAAO,CAAC,yBAAyB,CAAC;QAC1CC,MAAM,EAAE;MACV;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,gBAELxD,OAAA,CAACR,gBAAgB;MAACuD,gBAAgB,EAAEtC,mBAAoB;MACtDuC,IAAI,EAAE;QACJC,SAAS,EAAE,IAAI;QACfC,MAAM,EAAEC,OAAO,CAAC3C,gBAAgB,GAAG,yBAAyB,GAAG,yBAAyB,CAAC;QACzF4C,MAAM,EAAE;MACV;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CACP,eACDxD,OAAA;MAAKyD,SAAS,EAAC,cAAc;MAACC,GAAG,EAAErD,WAAY;MAACsD,KAAK,EAAEnB,gBAAiB;MAAAM,QAAA,gBACtE9C,OAAA,CAACZ,MAAM;QAAA0D,QAAA,GACJjD,SAAS,CAACC,MAAM,EAAEJ,cAAc,CAAC,eAClCM,OAAA,CAACb,KAAK;UAAC8B,IAAI,EAAC,GAAG;UAAC2C,OAAO,eAAE5D,OAAA,CAACX,QAAQ;YAACwE,EAAE,EAAC,KAAK;YAACC,OAAO;UAAA;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC,eACTxD,OAAA,CAACT,WAAW;QAAA8D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACZ,CAAC,EAGLxC,sBAAsB,CAAC,CAAC,iBACvBhB,OAAA,CAACP,YAAY;MAACsE,MAAM,EAAEjD,gBAAiB;MAACkD,QAAQ,EAAE3B;IAAmB;MAAAgB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CACxE;EAAA,eACD,CAAC;AAEP,CAAC;AAACpD,EAAA,CA5HID,WAAW;EAAA,QAEEjB,WAAW;AAAA;AAAA+E,EAAA,GAFxB9D,WAAW;AA8HjB,eAAeA,WAAW;AAAC,IAAA8D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}