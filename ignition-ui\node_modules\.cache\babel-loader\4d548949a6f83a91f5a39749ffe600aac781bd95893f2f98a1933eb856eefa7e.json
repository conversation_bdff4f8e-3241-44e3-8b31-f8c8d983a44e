{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"color\", \"onClick\", \"disabled\", \"size\", \"variant\", \"startDecorator\", \"endDecorator\", \"component\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport { useButton } from '@mui/base/useButton';\nimport { unstable_capitalize as capitalize, unstable_useId as useId } from '@mui/utils';\nimport { useThemeProps } from '../styles';\nimport styled from '../styles/styled';\nimport { VariantColorProvider } from '../styles/variantColorInheritance';\nimport { resolveSxValue } from '../styles/styleUtils';\nimport chipClasses, { getChipUtilityClass } from './chipClasses';\nimport ChipContext from './ChipContext';\nimport useSlot from '../utils/useSlot';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    disabled,\n    size,\n    color,\n    clickable,\n    variant,\n    focusVisible\n  } = ownerState;\n  const slots = {\n    root: ['root', disabled && 'disabled', color && `color${capitalize(color)}`, size && `size${capitalize(size)}`, variant && `variant${capitalize(variant)}`, clickable && 'clickable'],\n    action: ['action', disabled && 'disabled', focusVisible && 'focusVisible'],\n    label: ['label', size && `label${capitalize(size)}`],\n    startDecorator: ['startDecorator'],\n    endDecorator: ['endDecorator']\n  };\n  return composeClasses(slots, getChipUtilityClass, {});\n};\nconst ChipRoot = styled('div', {\n  name: 'JoyChip',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(_ref => {\n  let {\n    theme,\n    ownerState\n  } = _ref;\n  var _theme$variants, _theme$variants2, _theme$variants3;\n  const variantStyle = (_theme$variants = theme.variants[ownerState.variant]) == null ? void 0 : _theme$variants[ownerState.color];\n  const {\n    borderRadius\n  } = resolveSxValue({\n    theme,\n    ownerState\n  }, ['borderRadius']);\n  return [_extends({\n    // for controlling chip delete margin offset\n    '--Chip-decoratorChildOffset': 'min(calc(var(--Chip-paddingInline) - (var(--_Chip-minHeight) - 2 * var(--variant-borderWidth, 0px) - var(--Chip-decoratorChildHeight)) / 2), var(--Chip-paddingInline))',\n    '--Chip-decoratorChildRadius': 'max(var(--_Chip-radius) - var(--variant-borderWidth, 0px) - var(--_Chip-paddingBlock), min(var(--_Chip-paddingBlock) + var(--variant-borderWidth, 0px), var(--_Chip-radius) / 2))',\n    '--Chip-deleteRadius': 'var(--Chip-decoratorChildRadius)',\n    '--Chip-deleteSize': 'var(--Chip-decoratorChildHeight)',\n    '--Avatar-radius': 'var(--Chip-decoratorChildRadius)',\n    '--Avatar-size': 'var(--Chip-decoratorChildHeight)',\n    '--Icon-margin': 'initial',\n    // reset the icon's margin.\n    '--Icon-color': 'currentColor',\n    '--unstable_actionRadius': 'var(--_Chip-radius)'\n  }, ownerState.size === 'sm' && {\n    '--Chip-paddingInline': '0.375rem',\n    '--Chip-decoratorChildHeight': 'calc(var(--_Chip-minHeight) - 2 * var(--variant-borderWidth))',\n    '--Icon-fontSize': theme.vars.fontSize.sm,\n    '--_Chip-minHeight': 'var(--Chip-minHeight, 1.25rem)',\n    // 20px\n    gap: '3px'\n  }, ownerState.size === 'md' && {\n    '--Chip-paddingInline': '0.5rem',\n    '--Chip-decoratorChildHeight': 'calc(var(--_Chip-minHeight) - 0.25rem - 2 * var(--variant-borderWidth))',\n    '--Icon-fontSize': theme.vars.fontSize.md,\n    '--_Chip-minHeight': 'var(--Chip-minHeight, 1.5rem)',\n    // 26px\n    gap: '0.25rem'\n  }, ownerState.size === 'lg' && {\n    '--Chip-paddingInline': '0.75rem',\n    '--Chip-decoratorChildHeight': 'calc(var(--_Chip-minHeight) - 0.375rem - 2 * var(--variant-borderWidth))',\n    '--Icon-fontSize': theme.vars.fontSize.lg,\n    '--_Chip-minHeight': 'var(--Chip-minHeight, 1.75rem)',\n    // 28px\n    gap: '0.375rem'\n  }, {\n    '--_Chip-radius': 'var(--Chip-radius, 1.5rem)',\n    '--_Chip-paddingBlock': 'max((var(--_Chip-minHeight) - 2 * var(--variant-borderWidth, 0px) - var(--Chip-decoratorChildHeight)) / 2, 0px)',\n    minHeight: 'var(--_Chip-minHeight)',\n    maxWidth: 'max-content',\n    // to prevent Chip from stretching to full width when used with flexbox\n    paddingInline: 'var(--Chip-paddingInline)',\n    borderRadius: 'var(--_Chip-radius)',\n    position: 'relative',\n    display: 'inline-flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    whiteSpace: 'nowrap',\n    textDecoration: 'none',\n    verticalAlign: 'middle',\n    boxSizing: 'border-box'\n  }, theme.typography[`body-${{\n    sm: 'xs',\n    md: 'sm',\n    lg: 'md'\n  }[ownerState.size]}`], {\n    fontWeight: theme.vars.fontWeight.md,\n    [`&.${chipClasses.disabled}`]: {\n      color: (_theme$variants2 = theme.variants[`${ownerState.variant}Disabled`]) == null || (_theme$variants2 = _theme$variants2[ownerState.color]) == null ? void 0 : _theme$variants2.color\n    }\n  }), ...(!ownerState.clickable ? [_extends({\n    backgroundColor: theme.vars.palette.background.surface\n  }, variantStyle, {\n    [`&.${chipClasses.disabled}`]: (_theme$variants3 = theme.variants[`${ownerState.variant}Disabled`]) == null ? void 0 : _theme$variants3[ownerState.color]\n  })] : [{\n    '--variant-borderWidth': '0px',\n    color: variantStyle == null ? void 0 : variantStyle.color\n  }]), borderRadius !== undefined && {\n    '--_Chip-radius': borderRadius\n  }];\n});\nconst ChipLabel = styled('span', {\n  name: 'JoyChip',\n  slot: 'Label',\n  overridesResolver: (props, styles) => styles.label\n})(_ref2 => {\n  let {\n    ownerState\n  } = _ref2;\n  return _extends({\n    display: 'inline-block',\n    overflow: 'hidden',\n    textOverflow: 'ellipsis',\n    order: 1,\n    minInlineSize: 0,\n    flexGrow: 1\n  }, ownerState.clickable && {\n    zIndex: 1,\n    pointerEvents: 'none'\n  });\n});\nconst ChipAction = styled('button', {\n  name: 'JoyChip',\n  slot: 'Action',\n  overridesResolver: (props, styles) => styles.action\n})(_ref3 => {\n  let {\n    theme,\n    ownerState\n  } = _ref3;\n  var _theme$variants4, _theme$variants5, _theme$variants6, _theme$variants7;\n  return [{\n    '--Icon-color': ownerState.color !== 'neutral' || ownerState.variant === 'solid' ? 'currentColor' : theme.vars.palette.text.icon,\n    position: 'absolute',\n    zIndex: 0,\n    top: 0,\n    left: 0,\n    bottom: 0,\n    right: 0,\n    width: '100%',\n    // To fix Firefox issue (https://github.com/mui/material-ui/issues/36877)\n    border: 'none',\n    cursor: 'pointer',\n    padding: 'initial',\n    margin: 'initial',\n    backgroundColor: 'initial',\n    textDecoration: 'none',\n    borderRadius: 'inherit',\n    [theme.focus.selector]: theme.focus.default\n  }, _extends({\n    backgroundColor: theme.vars.palette.background.surface\n  }, (_theme$variants4 = theme.variants[ownerState.variant]) == null ? void 0 : _theme$variants4[ownerState.color]), {\n    '&:hover': {\n      '@media (hover: hover)': (_theme$variants5 = theme.variants[`${ownerState.variant}Hover`]) == null ? void 0 : _theme$variants5[ownerState.color]\n    }\n  }, {\n    '&:active': (_theme$variants6 = theme.variants[`${ownerState.variant}Active`]) == null ? void 0 : _theme$variants6[ownerState.color]\n  }, {\n    [`&.${chipClasses.disabled}`]: (_theme$variants7 = theme.variants[`${ownerState.variant}Disabled`]) == null ? void 0 : _theme$variants7[ownerState.color]\n  }];\n});\nconst ChipStartDecorator = styled('span', {\n  name: 'JoyChip',\n  slot: 'StartDecorator',\n  overridesResolver: (props, styles) => styles.startDecorator\n})({\n  '--Avatar-marginInlineStart': 'calc(var(--Chip-decoratorChildOffset) * -1)',\n  '--IconButton-margin': '0 calc(-1 * var(--Chip-paddingInline) / 3) 0 calc(var(--Chip-decoratorChildOffset) * -1)',\n  '--Icon-margin': '0 0 0 calc(var(--Chip-paddingInline) / -4)',\n  display: 'inherit',\n  // set zIndex to 1 with order to stay on top of other controls, for example Checkbox, Radio\n  order: 0,\n  zIndex: 1,\n  pointerEvents: 'none'\n});\nconst ChipEndDecorator = styled('span', {\n  name: 'JoyChip',\n  slot: 'EndDecorator',\n  overridesResolver: (props, styles) => styles.endDecorator\n})({\n  '--IconButton-margin': '0 calc(var(--Chip-decoratorChildOffset) * -1) 0 calc(-1 * var(--Chip-paddingInline) / 3)',\n  '--Icon-margin': '0 calc(var(--Chip-paddingInline) / -4) 0 0',\n  display: 'inherit',\n  // set zIndex to 1 with order to stay on top of other controls, for example Checkbox, Radio\n  order: 2,\n  zIndex: 1,\n  pointerEvents: 'none'\n});\n\n/**\n * Chips represent complex entities in small blocks, such as a contact.\n *\n * Demos:\n *\n * - [Chip](https://mui.com/joy-ui/react-chip/)\n *\n * API:\n *\n * - [Chip API](https://mui.com/joy-ui/api/chip/)\n */\nconst Chip = /*#__PURE__*/React.forwardRef(function Chip(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyChip'\n  });\n  const {\n      children,\n      className,\n      color = 'neutral',\n      onClick,\n      disabled = false,\n      size = 'md',\n      variant = 'soft',\n      startDecorator,\n      endDecorator,\n      component,\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const clickable = !!onClick || !!slotProps.action;\n  const ownerState = _extends({}, props, {\n    disabled,\n    size,\n    color,\n    variant,\n    clickable,\n    focusVisible: false\n  });\n  const resolvedActionProps = typeof slotProps.action === 'function' ? slotProps.action(ownerState) : slotProps.action;\n  const actionRef = React.useRef(null);\n  const {\n    focusVisible,\n    getRootProps\n  } = useButton(_extends({}, resolvedActionProps, {\n    disabled,\n    rootRef: actionRef\n  }));\n  ownerState.focusVisible = focusVisible;\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    className: clsx(classes.root, className),\n    elementType: ChipRoot,\n    externalForwardedProps,\n    ownerState\n  });\n  const [SlotLabel, labelProps] = useSlot('label', {\n    className: classes.label,\n    elementType: ChipLabel,\n    externalForwardedProps,\n    ownerState\n  });\n\n  // @ts-ignore internal logic.\n  const id = useId(labelProps.id);\n  const [SlotAction, actionProps] = useSlot('action', {\n    className: classes.action,\n    elementType: ChipAction,\n    externalForwardedProps,\n    ownerState,\n    getSlotProps: getRootProps,\n    additionalProps: {\n      'aria-labelledby': id,\n      as: resolvedActionProps == null ? void 0 : resolvedActionProps.component,\n      onClick\n    }\n  });\n  const [SlotStartDecorator, startDecoratorProps] = useSlot('startDecorator', {\n    className: classes.startDecorator,\n    elementType: ChipStartDecorator,\n    externalForwardedProps,\n    ownerState\n  });\n  const [SlotEndDecorator, endDecoratorProps] = useSlot('endDecorator', {\n    className: classes.endDecorator,\n    elementType: ChipEndDecorator,\n    externalForwardedProps,\n    ownerState\n  });\n  const chipContextValue = React.useMemo(() => ({\n    disabled\n  }), [disabled]);\n  return /*#__PURE__*/_jsx(ChipContext.Provider, {\n    value: chipContextValue,\n    children: /*#__PURE__*/_jsx(VariantColorProvider, {\n      variant: variant,\n      color: color,\n      children: /*#__PURE__*/_jsxs(SlotRoot, _extends({}, rootProps, {\n        children: [clickable && /*#__PURE__*/_jsx(SlotAction, _extends({}, actionProps)), /*#__PURE__*/_jsx(SlotLabel, _extends({}, labelProps, {\n          id: id,\n          children: children\n        })), startDecorator && /*#__PURE__*/_jsx(SlotStartDecorator, _extends({}, startDecoratorProps, {\n          children: startDecorator\n        })), endDecorator && /*#__PURE__*/_jsx(SlotEndDecorator, _extends({}, endDecoratorProps, {\n          children: endDecorator\n        }))]\n      }))\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Chip.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component. It supports those theme colors that make sense for this component.\n   * @default 'neutral'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['danger', 'neutral', 'primary', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * Element placed after the children.\n   */\n  endDecorator: PropTypes.node,\n  /**\n   * Element action click handler.\n   */\n  onClick: PropTypes.func,\n  /**\n   * The size of the component.\n   * It accepts theme values between 'sm' and 'lg'.\n   * @default 'md'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['lg', 'md', 'sm']), PropTypes.string]),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    action: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    endDecorator: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    label: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    startDecorator: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    action: PropTypes.elementType,\n    endDecorator: PropTypes.elementType,\n    label: PropTypes.elementType,\n    root: PropTypes.elementType,\n    startDecorator: PropTypes.elementType\n  }),\n  /**\n   * Element placed before the children.\n   */\n  startDecorator: PropTypes.node,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The [global variant](https://mui.com/joy-ui/main-features/global-variants/) to use.\n   * @default 'soft'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['outlined', 'plain', 'soft', 'solid']), PropTypes.string])\n} : void 0;\nexport default Chip;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "clsx", "PropTypes", "unstable_composeClasses", "composeClasses", "useButton", "unstable_capitalize", "capitalize", "unstable_useId", "useId", "useThemeProps", "styled", "VariantColorProvider", "resolveSxValue", "chipClasses", "getChipUtilityClass", "ChipContext", "useSlot", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "disabled", "size", "color", "clickable", "variant", "focusVisible", "slots", "root", "action", "label", "startDecorator", "endDecorator", "ChipRoot", "name", "slot", "overridesResolver", "props", "styles", "_ref", "theme", "_theme$variants", "_theme$variants2", "_theme$variants3", "variantStyle", "variants", "borderRadius", "vars", "fontSize", "sm", "gap", "md", "lg", "minHeight", "max<PERSON><PERSON><PERSON>", "paddingInline", "position", "display", "alignItems", "justifyContent", "whiteSpace", "textDecoration", "verticalAlign", "boxSizing", "typography", "fontWeight", "backgroundColor", "palette", "background", "surface", "undefined", "ChipLabel", "_ref2", "overflow", "textOverflow", "order", "minInlineSize", "flexGrow", "zIndex", "pointerEvents", "ChipAction", "_ref3", "_theme$variants4", "_theme$variants5", "_theme$variants6", "_theme$variants7", "text", "icon", "top", "left", "bottom", "right", "width", "border", "cursor", "padding", "margin", "focus", "selector", "default", "ChipStartDecorator", "ChipEndDecorator", "Chip", "forwardRef", "inProps", "ref", "children", "className", "onClick", "component", "slotProps", "other", "resolvedActionProps", "actionRef", "useRef", "getRootProps", "rootRef", "classes", "externalForwardedProps", "SlotRoot", "rootProps", "elementType", "SlotLabel", "labelProps", "id", "SlotAction", "actionProps", "getSlotProps", "additionalProps", "as", "SlotStartDecorator", "startDecoratorProps", "SlotEndDecorator", "endDecoratorProps", "chipContextValue", "useMemo", "Provider", "value", "process", "env", "NODE_ENV", "propTypes", "node", "string", "oneOfType", "oneOf", "bool", "func", "shape", "object", "sx", "arrayOf"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Chip/Chip.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"color\", \"onClick\", \"disabled\", \"size\", \"variant\", \"startDecorator\", \"endDecorator\", \"component\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport { useButton } from '@mui/base/useButton';\nimport { unstable_capitalize as capitalize, unstable_useId as useId } from '@mui/utils';\nimport { useThemeProps } from '../styles';\nimport styled from '../styles/styled';\nimport { VariantColorProvider } from '../styles/variantColorInheritance';\nimport { resolveSxValue } from '../styles/styleUtils';\nimport chipClasses, { getChipUtilityClass } from './chipClasses';\nimport ChipContext from './ChipContext';\nimport useSlot from '../utils/useSlot';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    disabled,\n    size,\n    color,\n    clickable,\n    variant,\n    focusVisible\n  } = ownerState;\n  const slots = {\n    root: ['root', disabled && 'disabled', color && `color${capitalize(color)}`, size && `size${capitalize(size)}`, variant && `variant${capitalize(variant)}`, clickable && 'clickable'],\n    action: ['action', disabled && 'disabled', focusVisible && 'focusVisible'],\n    label: ['label', size && `label${capitalize(size)}`],\n    startDecorator: ['startDecorator'],\n    endDecorator: ['endDecorator']\n  };\n  return composeClasses(slots, getChipUtilityClass, {});\n};\nconst ChipRoot = styled('div', {\n  name: 'JoyChip',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(({\n  theme,\n  ownerState\n}) => {\n  var _theme$variants, _theme$variants2, _theme$variants3;\n  const variantStyle = (_theme$variants = theme.variants[ownerState.variant]) == null ? void 0 : _theme$variants[ownerState.color];\n  const {\n    borderRadius\n  } = resolveSxValue({\n    theme,\n    ownerState\n  }, ['borderRadius']);\n  return [_extends({\n    // for controlling chip delete margin offset\n    '--Chip-decoratorChildOffset': 'min(calc(var(--Chip-paddingInline) - (var(--_Chip-minHeight) - 2 * var(--variant-borderWidth, 0px) - var(--Chip-decoratorChildHeight)) / 2), var(--Chip-paddingInline))',\n    '--Chip-decoratorChildRadius': 'max(var(--_Chip-radius) - var(--variant-borderWidth, 0px) - var(--_Chip-paddingBlock), min(var(--_Chip-paddingBlock) + var(--variant-borderWidth, 0px), var(--_Chip-radius) / 2))',\n    '--Chip-deleteRadius': 'var(--Chip-decoratorChildRadius)',\n    '--Chip-deleteSize': 'var(--Chip-decoratorChildHeight)',\n    '--Avatar-radius': 'var(--Chip-decoratorChildRadius)',\n    '--Avatar-size': 'var(--Chip-decoratorChildHeight)',\n    '--Icon-margin': 'initial',\n    // reset the icon's margin.\n    '--Icon-color': 'currentColor',\n    '--unstable_actionRadius': 'var(--_Chip-radius)'\n  }, ownerState.size === 'sm' && {\n    '--Chip-paddingInline': '0.375rem',\n    '--Chip-decoratorChildHeight': 'calc(var(--_Chip-minHeight) - 2 * var(--variant-borderWidth))',\n    '--Icon-fontSize': theme.vars.fontSize.sm,\n    '--_Chip-minHeight': 'var(--Chip-minHeight, 1.25rem)',\n    // 20px\n    gap: '3px'\n  }, ownerState.size === 'md' && {\n    '--Chip-paddingInline': '0.5rem',\n    '--Chip-decoratorChildHeight': 'calc(var(--_Chip-minHeight) - 0.25rem - 2 * var(--variant-borderWidth))',\n    '--Icon-fontSize': theme.vars.fontSize.md,\n    '--_Chip-minHeight': 'var(--Chip-minHeight, 1.5rem)',\n    // 26px\n    gap: '0.25rem'\n  }, ownerState.size === 'lg' && {\n    '--Chip-paddingInline': '0.75rem',\n    '--Chip-decoratorChildHeight': 'calc(var(--_Chip-minHeight) - 0.375rem - 2 * var(--variant-borderWidth))',\n    '--Icon-fontSize': theme.vars.fontSize.lg,\n    '--_Chip-minHeight': 'var(--Chip-minHeight, 1.75rem)',\n    // 28px\n    gap: '0.375rem'\n  }, {\n    '--_Chip-radius': 'var(--Chip-radius, 1.5rem)',\n    '--_Chip-paddingBlock': 'max((var(--_Chip-minHeight) - 2 * var(--variant-borderWidth, 0px) - var(--Chip-decoratorChildHeight)) / 2, 0px)',\n    minHeight: 'var(--_Chip-minHeight)',\n    maxWidth: 'max-content',\n    // to prevent Chip from stretching to full width when used with flexbox\n    paddingInline: 'var(--Chip-paddingInline)',\n    borderRadius: 'var(--_Chip-radius)',\n    position: 'relative',\n    display: 'inline-flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    whiteSpace: 'nowrap',\n    textDecoration: 'none',\n    verticalAlign: 'middle',\n    boxSizing: 'border-box'\n  }, theme.typography[`body-${{\n    sm: 'xs',\n    md: 'sm',\n    lg: 'md'\n  }[ownerState.size]}`], {\n    fontWeight: theme.vars.fontWeight.md,\n    [`&.${chipClasses.disabled}`]: {\n      color: (_theme$variants2 = theme.variants[`${ownerState.variant}Disabled`]) == null || (_theme$variants2 = _theme$variants2[ownerState.color]) == null ? void 0 : _theme$variants2.color\n    }\n  }), ...(!ownerState.clickable ? [_extends({\n    backgroundColor: theme.vars.palette.background.surface\n  }, variantStyle, {\n    [`&.${chipClasses.disabled}`]: (_theme$variants3 = theme.variants[`${ownerState.variant}Disabled`]) == null ? void 0 : _theme$variants3[ownerState.color]\n  })] : [{\n    '--variant-borderWidth': '0px',\n    color: variantStyle == null ? void 0 : variantStyle.color\n  }]), borderRadius !== undefined && {\n    '--_Chip-radius': borderRadius\n  }];\n});\nconst ChipLabel = styled('span', {\n  name: 'JoyChip',\n  slot: 'Label',\n  overridesResolver: (props, styles) => styles.label\n})(({\n  ownerState\n}) => _extends({\n  display: 'inline-block',\n  overflow: 'hidden',\n  textOverflow: 'ellipsis',\n  order: 1,\n  minInlineSize: 0,\n  flexGrow: 1\n}, ownerState.clickable && {\n  zIndex: 1,\n  pointerEvents: 'none'\n}));\nconst ChipAction = styled('button', {\n  name: 'JoyChip',\n  slot: 'Action',\n  overridesResolver: (props, styles) => styles.action\n})(({\n  theme,\n  ownerState\n}) => {\n  var _theme$variants4, _theme$variants5, _theme$variants6, _theme$variants7;\n  return [{\n    '--Icon-color': ownerState.color !== 'neutral' || ownerState.variant === 'solid' ? 'currentColor' : theme.vars.palette.text.icon,\n    position: 'absolute',\n    zIndex: 0,\n    top: 0,\n    left: 0,\n    bottom: 0,\n    right: 0,\n    width: '100%',\n    // To fix Firefox issue (https://github.com/mui/material-ui/issues/36877)\n    border: 'none',\n    cursor: 'pointer',\n    padding: 'initial',\n    margin: 'initial',\n    backgroundColor: 'initial',\n    textDecoration: 'none',\n    borderRadius: 'inherit',\n    [theme.focus.selector]: theme.focus.default\n  }, _extends({\n    backgroundColor: theme.vars.palette.background.surface\n  }, (_theme$variants4 = theme.variants[ownerState.variant]) == null ? void 0 : _theme$variants4[ownerState.color]), {\n    '&:hover': {\n      '@media (hover: hover)': (_theme$variants5 = theme.variants[`${ownerState.variant}Hover`]) == null ? void 0 : _theme$variants5[ownerState.color]\n    }\n  }, {\n    '&:active': (_theme$variants6 = theme.variants[`${ownerState.variant}Active`]) == null ? void 0 : _theme$variants6[ownerState.color]\n  }, {\n    [`&.${chipClasses.disabled}`]: (_theme$variants7 = theme.variants[`${ownerState.variant}Disabled`]) == null ? void 0 : _theme$variants7[ownerState.color]\n  }];\n});\nconst ChipStartDecorator = styled('span', {\n  name: 'JoyChip',\n  slot: 'StartDecorator',\n  overridesResolver: (props, styles) => styles.startDecorator\n})({\n  '--Avatar-marginInlineStart': 'calc(var(--Chip-decoratorChildOffset) * -1)',\n  '--IconButton-margin': '0 calc(-1 * var(--Chip-paddingInline) / 3) 0 calc(var(--Chip-decoratorChildOffset) * -1)',\n  '--Icon-margin': '0 0 0 calc(var(--Chip-paddingInline) / -4)',\n  display: 'inherit',\n  // set zIndex to 1 with order to stay on top of other controls, for example Checkbox, Radio\n  order: 0,\n  zIndex: 1,\n  pointerEvents: 'none'\n});\nconst ChipEndDecorator = styled('span', {\n  name: 'JoyChip',\n  slot: 'EndDecorator',\n  overridesResolver: (props, styles) => styles.endDecorator\n})({\n  '--IconButton-margin': '0 calc(var(--Chip-decoratorChildOffset) * -1) 0 calc(-1 * var(--Chip-paddingInline) / 3)',\n  '--Icon-margin': '0 calc(var(--Chip-paddingInline) / -4) 0 0',\n  display: 'inherit',\n  // set zIndex to 1 with order to stay on top of other controls, for example Checkbox, Radio\n  order: 2,\n  zIndex: 1,\n  pointerEvents: 'none'\n});\n\n/**\n * Chips represent complex entities in small blocks, such as a contact.\n *\n * Demos:\n *\n * - [Chip](https://mui.com/joy-ui/react-chip/)\n *\n * API:\n *\n * - [Chip API](https://mui.com/joy-ui/api/chip/)\n */\nconst Chip = /*#__PURE__*/React.forwardRef(function Chip(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyChip'\n  });\n  const {\n      children,\n      className,\n      color = 'neutral',\n      onClick,\n      disabled = false,\n      size = 'md',\n      variant = 'soft',\n      startDecorator,\n      endDecorator,\n      component,\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const clickable = !!onClick || !!slotProps.action;\n  const ownerState = _extends({}, props, {\n    disabled,\n    size,\n    color,\n    variant,\n    clickable,\n    focusVisible: false\n  });\n  const resolvedActionProps = typeof slotProps.action === 'function' ? slotProps.action(ownerState) : slotProps.action;\n  const actionRef = React.useRef(null);\n  const {\n    focusVisible,\n    getRootProps\n  } = useButton(_extends({}, resolvedActionProps, {\n    disabled,\n    rootRef: actionRef\n  }));\n  ownerState.focusVisible = focusVisible;\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    className: clsx(classes.root, className),\n    elementType: ChipRoot,\n    externalForwardedProps,\n    ownerState\n  });\n  const [SlotLabel, labelProps] = useSlot('label', {\n    className: classes.label,\n    elementType: ChipLabel,\n    externalForwardedProps,\n    ownerState\n  });\n\n  // @ts-ignore internal logic.\n  const id = useId(labelProps.id);\n  const [SlotAction, actionProps] = useSlot('action', {\n    className: classes.action,\n    elementType: ChipAction,\n    externalForwardedProps,\n    ownerState,\n    getSlotProps: getRootProps,\n    additionalProps: {\n      'aria-labelledby': id,\n      as: resolvedActionProps == null ? void 0 : resolvedActionProps.component,\n      onClick\n    }\n  });\n  const [SlotStartDecorator, startDecoratorProps] = useSlot('startDecorator', {\n    className: classes.startDecorator,\n    elementType: ChipStartDecorator,\n    externalForwardedProps,\n    ownerState\n  });\n  const [SlotEndDecorator, endDecoratorProps] = useSlot('endDecorator', {\n    className: classes.endDecorator,\n    elementType: ChipEndDecorator,\n    externalForwardedProps,\n    ownerState\n  });\n  const chipContextValue = React.useMemo(() => ({\n    disabled\n  }), [disabled]);\n  return /*#__PURE__*/_jsx(ChipContext.Provider, {\n    value: chipContextValue,\n    children: /*#__PURE__*/_jsx(VariantColorProvider, {\n      variant: variant,\n      color: color,\n      children: /*#__PURE__*/_jsxs(SlotRoot, _extends({}, rootProps, {\n        children: [clickable && /*#__PURE__*/_jsx(SlotAction, _extends({}, actionProps)), /*#__PURE__*/_jsx(SlotLabel, _extends({}, labelProps, {\n          id: id,\n          children: children\n        })), startDecorator && /*#__PURE__*/_jsx(SlotStartDecorator, _extends({}, startDecoratorProps, {\n          children: startDecorator\n        })), endDecorator && /*#__PURE__*/_jsx(SlotEndDecorator, _extends({}, endDecoratorProps, {\n          children: endDecorator\n        }))]\n      }))\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? Chip.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component. It supports those theme colors that make sense for this component.\n   * @default 'neutral'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['danger', 'neutral', 'primary', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the component is disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * Element placed after the children.\n   */\n  endDecorator: PropTypes.node,\n  /**\n   * Element action click handler.\n   */\n  onClick: PropTypes.func,\n  /**\n   * The size of the component.\n   * It accepts theme values between 'sm' and 'lg'.\n   * @default 'md'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['lg', 'md', 'sm']), PropTypes.string]),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    action: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    endDecorator: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    label: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    startDecorator: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    action: PropTypes.elementType,\n    endDecorator: PropTypes.elementType,\n    label: PropTypes.elementType,\n    root: PropTypes.elementType,\n    startDecorator: PropTypes.elementType\n  }),\n  /**\n   * Element placed before the children.\n   */\n  startDecorator: PropTypes.node,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The [global variant](https://mui.com/joy-ui/main-features/global-variants/) to use.\n   * @default 'soft'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['outlined', 'plain', 'soft', 'solid']), PropTypes.string])\n} : void 0;\nexport default Chip;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,SAAS,EAAE,gBAAgB,EAAE,cAAc,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,CAAC;AACnK,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,0BAA0B;AACpF,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,mBAAmB,IAAIC,UAAU,EAAEC,cAAc,IAAIC,KAAK,QAAQ,YAAY;AACvF,SAASC,aAAa,QAAQ,WAAW;AACzC,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,oBAAoB,QAAQ,mCAAmC;AACxE,SAASC,cAAc,QAAQ,sBAAsB;AACrD,OAAOC,WAAW,IAAIC,mBAAmB,QAAQ,eAAe;AAChE,OAAOC,WAAW,MAAM,eAAe;AACvC,OAAOC,OAAO,MAAM,kBAAkB;AACtC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,QAAQ;IACRC,IAAI;IACJC,KAAK;IACLC,SAAS;IACTC,OAAO;IACPC;EACF,CAAC,GAAGN,UAAU;EACd,MAAMO,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEP,QAAQ,IAAI,UAAU,EAAEE,KAAK,IAAI,QAAQnB,UAAU,CAACmB,KAAK,CAAC,EAAE,EAAED,IAAI,IAAI,OAAOlB,UAAU,CAACkB,IAAI,CAAC,EAAE,EAAEG,OAAO,IAAI,UAAUrB,UAAU,CAACqB,OAAO,CAAC,EAAE,EAAED,SAAS,IAAI,WAAW,CAAC;IACrLK,MAAM,EAAE,CAAC,QAAQ,EAAER,QAAQ,IAAI,UAAU,EAAEK,YAAY,IAAI,cAAc,CAAC;IAC1EI,KAAK,EAAE,CAAC,OAAO,EAAER,IAAI,IAAI,QAAQlB,UAAU,CAACkB,IAAI,CAAC,EAAE,CAAC;IACpDS,cAAc,EAAE,CAAC,gBAAgB,CAAC;IAClCC,YAAY,EAAE,CAAC,cAAc;EAC/B,CAAC;EACD,OAAO/B,cAAc,CAAC0B,KAAK,EAAEf,mBAAmB,EAAE,CAAC,CAAC,CAAC;AACvD,CAAC;AACD,MAAMqB,QAAQ,GAAGzB,MAAM,CAAC,KAAK,EAAE;EAC7B0B,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACV;AAC/C,CAAC,CAAC,CAACW,IAAA,IAGG;EAAA,IAHF;IACFC,KAAK;IACLpB;EACF,CAAC,GAAAmB,IAAA;EACC,IAAIE,eAAe,EAAEC,gBAAgB,EAAEC,gBAAgB;EACvD,MAAMC,YAAY,GAAG,CAACH,eAAe,GAAGD,KAAK,CAACK,QAAQ,CAACzB,UAAU,CAACK,OAAO,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGgB,eAAe,CAACrB,UAAU,CAACG,KAAK,CAAC;EAChI,MAAM;IACJuB;EACF,CAAC,GAAGpC,cAAc,CAAC;IACjB8B,KAAK;IACLpB;EACF,CAAC,EAAE,CAAC,cAAc,CAAC,CAAC;EACpB,OAAO,CAACzB,QAAQ,CAAC;IACf;IACA,6BAA6B,EAAE,yKAAyK;IACxM,6BAA6B,EAAE,mLAAmL;IAClN,qBAAqB,EAAE,kCAAkC;IACzD,mBAAmB,EAAE,kCAAkC;IACvD,iBAAiB,EAAE,kCAAkC;IACrD,eAAe,EAAE,kCAAkC;IACnD,eAAe,EAAE,SAAS;IAC1B;IACA,cAAc,EAAE,cAAc;IAC9B,yBAAyB,EAAE;EAC7B,CAAC,EAAEyB,UAAU,CAACE,IAAI,KAAK,IAAI,IAAI;IAC7B,sBAAsB,EAAE,UAAU;IAClC,6BAA6B,EAAE,+DAA+D;IAC9F,iBAAiB,EAAEkB,KAAK,CAACO,IAAI,CAACC,QAAQ,CAACC,EAAE;IACzC,mBAAmB,EAAE,gCAAgC;IACrD;IACAC,GAAG,EAAE;EACP,CAAC,EAAE9B,UAAU,CAACE,IAAI,KAAK,IAAI,IAAI;IAC7B,sBAAsB,EAAE,QAAQ;IAChC,6BAA6B,EAAE,yEAAyE;IACxG,iBAAiB,EAAEkB,KAAK,CAACO,IAAI,CAACC,QAAQ,CAACG,EAAE;IACzC,mBAAmB,EAAE,+BAA+B;IACpD;IACAD,GAAG,EAAE;EACP,CAAC,EAAE9B,UAAU,CAACE,IAAI,KAAK,IAAI,IAAI;IAC7B,sBAAsB,EAAE,SAAS;IACjC,6BAA6B,EAAE,0EAA0E;IACzG,iBAAiB,EAAEkB,KAAK,CAACO,IAAI,CAACC,QAAQ,CAACI,EAAE;IACzC,mBAAmB,EAAE,gCAAgC;IACrD;IACAF,GAAG,EAAE;EACP,CAAC,EAAE;IACD,gBAAgB,EAAE,4BAA4B;IAC9C,sBAAsB,EAAE,iHAAiH;IACzIG,SAAS,EAAE,wBAAwB;IACnCC,QAAQ,EAAE,aAAa;IACvB;IACAC,aAAa,EAAE,2BAA2B;IAC1CT,YAAY,EAAE,qBAAqB;IACnCU,QAAQ,EAAE,UAAU;IACpBC,OAAO,EAAE,aAAa;IACtBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,MAAM;IACtBC,aAAa,EAAE,QAAQ;IACvBC,SAAS,EAAE;EACb,CAAC,EAAEvB,KAAK,CAACwB,UAAU,CAAC,QAAQ;IAC1Bf,EAAE,EAAE,IAAI;IACRE,EAAE,EAAE,IAAI;IACRC,EAAE,EAAE;EACN,CAAC,CAAChC,UAAU,CAACE,IAAI,CAAC,EAAE,CAAC,EAAE;IACrB2C,UAAU,EAAEzB,KAAK,CAACO,IAAI,CAACkB,UAAU,CAACd,EAAE;IACpC,CAAC,KAAKxC,WAAW,CAACU,QAAQ,EAAE,GAAG;MAC7BE,KAAK,EAAE,CAACmB,gBAAgB,GAAGF,KAAK,CAACK,QAAQ,CAAC,GAAGzB,UAAU,CAACK,OAAO,UAAU,CAAC,KAAK,IAAI,IAAI,CAACiB,gBAAgB,GAAGA,gBAAgB,CAACtB,UAAU,CAACG,KAAK,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGmB,gBAAgB,CAACnB;IACrL;EACF,CAAC,CAAC,EAAE,IAAI,CAACH,UAAU,CAACI,SAAS,GAAG,CAAC7B,QAAQ,CAAC;IACxCuE,eAAe,EAAE1B,KAAK,CAACO,IAAI,CAACoB,OAAO,CAACC,UAAU,CAACC;EACjD,CAAC,EAAEzB,YAAY,EAAE;IACf,CAAC,KAAKjC,WAAW,CAACU,QAAQ,EAAE,GAAG,CAACsB,gBAAgB,GAAGH,KAAK,CAACK,QAAQ,CAAC,GAAGzB,UAAU,CAACK,OAAO,UAAU,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGkB,gBAAgB,CAACvB,UAAU,CAACG,KAAK;EAC1J,CAAC,CAAC,CAAC,GAAG,CAAC;IACL,uBAAuB,EAAE,KAAK;IAC9BA,KAAK,EAAEqB,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACrB;EACtD,CAAC,CAAC,CAAC,EAAEuB,YAAY,KAAKwB,SAAS,IAAI;IACjC,gBAAgB,EAAExB;EACpB,CAAC,CAAC;AACJ,CAAC,CAAC;AACF,MAAMyB,SAAS,GAAG/D,MAAM,CAAC,MAAM,EAAE;EAC/B0B,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,OAAO;EACbC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACR;AAC/C,CAAC,CAAC,CAAC0C,KAAA;EAAA,IAAC;IACFpD;EACF,CAAC,GAAAoD,KAAA;EAAA,OAAK7E,QAAQ,CAAC;IACb8D,OAAO,EAAE,cAAc;IACvBgB,QAAQ,EAAE,QAAQ;IAClBC,YAAY,EAAE,UAAU;IACxBC,KAAK,EAAE,CAAC;IACRC,aAAa,EAAE,CAAC;IAChBC,QAAQ,EAAE;EACZ,CAAC,EAAEzD,UAAU,CAACI,SAAS,IAAI;IACzBsD,MAAM,EAAE,CAAC;IACTC,aAAa,EAAE;EACjB,CAAC,CAAC;AAAA,EAAC;AACH,MAAMC,UAAU,GAAGxE,MAAM,CAAC,QAAQ,EAAE;EAClC0B,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,QAAQ;EACdC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACT;AAC/C,CAAC,CAAC,CAACoD,KAAA,IAGG;EAAA,IAHF;IACFzC,KAAK;IACLpB;EACF,CAAC,GAAA6D,KAAA;EACC,IAAIC,gBAAgB,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,gBAAgB;EAC1E,OAAO,CAAC;IACN,cAAc,EAAEjE,UAAU,CAACG,KAAK,KAAK,SAAS,IAAIH,UAAU,CAACK,OAAO,KAAK,OAAO,GAAG,cAAc,GAAGe,KAAK,CAACO,IAAI,CAACoB,OAAO,CAACmB,IAAI,CAACC,IAAI;IAChI/B,QAAQ,EAAE,UAAU;IACpBsB,MAAM,EAAE,CAAC;IACTU,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE,CAAC;IACRC,KAAK,EAAE,MAAM;IACb;IACAC,MAAM,EAAE,MAAM;IACdC,MAAM,EAAE,SAAS;IACjBC,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,SAAS;IACjB9B,eAAe,EAAE,SAAS;IAC1BL,cAAc,EAAE,MAAM;IACtBf,YAAY,EAAE,SAAS;IACvB,CAACN,KAAK,CAACyD,KAAK,CAACC,QAAQ,GAAG1D,KAAK,CAACyD,KAAK,CAACE;EACtC,CAAC,EAAExG,QAAQ,CAAC;IACVuE,eAAe,EAAE1B,KAAK,CAACO,IAAI,CAACoB,OAAO,CAACC,UAAU,CAACC;EACjD,CAAC,EAAE,CAACa,gBAAgB,GAAG1C,KAAK,CAACK,QAAQ,CAACzB,UAAU,CAACK,OAAO,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGyD,gBAAgB,CAAC9D,UAAU,CAACG,KAAK,CAAC,CAAC,EAAE;IACjH,SAAS,EAAE;MACT,uBAAuB,EAAE,CAAC4D,gBAAgB,GAAG3C,KAAK,CAACK,QAAQ,CAAC,GAAGzB,UAAU,CAACK,OAAO,OAAO,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG0D,gBAAgB,CAAC/D,UAAU,CAACG,KAAK;IACjJ;EACF,CAAC,EAAE;IACD,UAAU,EAAE,CAAC6D,gBAAgB,GAAG5C,KAAK,CAACK,QAAQ,CAAC,GAAGzB,UAAU,CAACK,OAAO,QAAQ,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG2D,gBAAgB,CAAChE,UAAU,CAACG,KAAK;EACrI,CAAC,EAAE;IACD,CAAC,KAAKZ,WAAW,CAACU,QAAQ,EAAE,GAAG,CAACgE,gBAAgB,GAAG7C,KAAK,CAACK,QAAQ,CAAC,GAAGzB,UAAU,CAACK,OAAO,UAAU,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG4D,gBAAgB,CAACjE,UAAU,CAACG,KAAK;EAC1J,CAAC,CAAC;AACJ,CAAC,CAAC;AACF,MAAM6E,kBAAkB,GAAG5F,MAAM,CAAC,MAAM,EAAE;EACxC0B,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,gBAAgB;EACtBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACP;AAC/C,CAAC,CAAC,CAAC;EACD,4BAA4B,EAAE,6CAA6C;EAC3E,qBAAqB,EAAE,0FAA0F;EACjH,eAAe,EAAE,4CAA4C;EAC7D0B,OAAO,EAAE,SAAS;EAClB;EACAkB,KAAK,EAAE,CAAC;EACRG,MAAM,EAAE,CAAC;EACTC,aAAa,EAAE;AACjB,CAAC,CAAC;AACF,MAAMsB,gBAAgB,GAAG7F,MAAM,CAAC,MAAM,EAAE;EACtC0B,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,cAAc;EACpBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC/C,CAAC,CAAC,CAAC;EACD,qBAAqB,EAAE,0FAA0F;EACjH,eAAe,EAAE,4CAA4C;EAC7DyB,OAAO,EAAE,SAAS;EAClB;EACAkB,KAAK,EAAE,CAAC;EACRG,MAAM,EAAE,CAAC;EACTC,aAAa,EAAE;AACjB,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuB,IAAI,GAAG,aAAazG,KAAK,CAAC0G,UAAU,CAAC,SAASD,IAAIA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACrE,MAAMpE,KAAK,GAAG9B,aAAa,CAAC;IAC1B8B,KAAK,EAAEmE,OAAO;IACdtE,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFwE,QAAQ;MACRC,SAAS;MACTpF,KAAK,GAAG,SAAS;MACjBqF,OAAO;MACPvF,QAAQ,GAAG,KAAK;MAChBC,IAAI,GAAG,IAAI;MACXG,OAAO,GAAG,MAAM;MAChBM,cAAc;MACdC,YAAY;MACZ6E,SAAS;MACTlF,KAAK,GAAG,CAAC,CAAC;MACVmF,SAAS,GAAG,CAAC;IACf,CAAC,GAAGzE,KAAK;IACT0E,KAAK,GAAGrH,6BAA6B,CAAC2C,KAAK,EAAEzC,SAAS,CAAC;EACzD,MAAM4B,SAAS,GAAG,CAAC,CAACoF,OAAO,IAAI,CAAC,CAACE,SAAS,CAACjF,MAAM;EACjD,MAAMT,UAAU,GAAGzB,QAAQ,CAAC,CAAC,CAAC,EAAE0C,KAAK,EAAE;IACrChB,QAAQ;IACRC,IAAI;IACJC,KAAK;IACLE,OAAO;IACPD,SAAS;IACTE,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,MAAMsF,mBAAmB,GAAG,OAAOF,SAAS,CAACjF,MAAM,KAAK,UAAU,GAAGiF,SAAS,CAACjF,MAAM,CAACT,UAAU,CAAC,GAAG0F,SAAS,CAACjF,MAAM;EACpH,MAAMoF,SAAS,GAAGpH,KAAK,CAACqH,MAAM,CAAC,IAAI,CAAC;EACpC,MAAM;IACJxF,YAAY;IACZyF;EACF,CAAC,GAAGjH,SAAS,CAACP,QAAQ,CAAC,CAAC,CAAC,EAAEqH,mBAAmB,EAAE;IAC9C3F,QAAQ;IACR+F,OAAO,EAAEH;EACX,CAAC,CAAC,CAAC;EACH7F,UAAU,CAACM,YAAY,GAAGA,YAAY;EACtC,MAAM2F,OAAO,GAAGlG,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMkG,sBAAsB,GAAG3H,QAAQ,CAAC,CAAC,CAAC,EAAEoH,KAAK,EAAE;IACjDF,SAAS;IACTlF,KAAK;IACLmF;EACF,CAAC,CAAC;EACF,MAAM,CAACS,QAAQ,EAAEC,SAAS,CAAC,GAAG1G,OAAO,CAAC,MAAM,EAAE;IAC5C2F,GAAG;IACHE,SAAS,EAAE7G,IAAI,CAACuH,OAAO,CAACzF,IAAI,EAAE+E,SAAS,CAAC;IACxCc,WAAW,EAAExF,QAAQ;IACrBqF,sBAAsB;IACtBlG;EACF,CAAC,CAAC;EACF,MAAM,CAACsG,SAAS,EAAEC,UAAU,CAAC,GAAG7G,OAAO,CAAC,OAAO,EAAE;IAC/C6F,SAAS,EAAEU,OAAO,CAACvF,KAAK;IACxB2F,WAAW,EAAElD,SAAS;IACtB+C,sBAAsB;IACtBlG;EACF,CAAC,CAAC;;EAEF;EACA,MAAMwG,EAAE,GAAGtH,KAAK,CAACqH,UAAU,CAACC,EAAE,CAAC;EAC/B,MAAM,CAACC,UAAU,EAAEC,WAAW,CAAC,GAAGhH,OAAO,CAAC,QAAQ,EAAE;IAClD6F,SAAS,EAAEU,OAAO,CAACxF,MAAM;IACzB4F,WAAW,EAAEzC,UAAU;IACvBsC,sBAAsB;IACtBlG,UAAU;IACV2G,YAAY,EAAEZ,YAAY;IAC1Ba,eAAe,EAAE;MACf,iBAAiB,EAAEJ,EAAE;MACrBK,EAAE,EAAEjB,mBAAmB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAACH,SAAS;MACxED;IACF;EACF,CAAC,CAAC;EACF,MAAM,CAACsB,kBAAkB,EAAEC,mBAAmB,CAAC,GAAGrH,OAAO,CAAC,gBAAgB,EAAE;IAC1E6F,SAAS,EAAEU,OAAO,CAACtF,cAAc;IACjC0F,WAAW,EAAErB,kBAAkB;IAC/BkB,sBAAsB;IACtBlG;EACF,CAAC,CAAC;EACF,MAAM,CAACgH,gBAAgB,EAAEC,iBAAiB,CAAC,GAAGvH,OAAO,CAAC,cAAc,EAAE;IACpE6F,SAAS,EAAEU,OAAO,CAACrF,YAAY;IAC/ByF,WAAW,EAAEpB,gBAAgB;IAC7BiB,sBAAsB;IACtBlG;EACF,CAAC,CAAC;EACF,MAAMkH,gBAAgB,GAAGzI,KAAK,CAAC0I,OAAO,CAAC,OAAO;IAC5ClH;EACF,CAAC,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EACf,OAAO,aAAaL,IAAI,CAACH,WAAW,CAAC2H,QAAQ,EAAE;IAC7CC,KAAK,EAAEH,gBAAgB;IACvB5B,QAAQ,EAAE,aAAa1F,IAAI,CAACP,oBAAoB,EAAE;MAChDgB,OAAO,EAAEA,OAAO;MAChBF,KAAK,EAAEA,KAAK;MACZmF,QAAQ,EAAE,aAAaxF,KAAK,CAACqG,QAAQ,EAAE5H,QAAQ,CAAC,CAAC,CAAC,EAAE6H,SAAS,EAAE;QAC7Dd,QAAQ,EAAE,CAAClF,SAAS,IAAI,aAAaR,IAAI,CAAC6G,UAAU,EAAElI,QAAQ,CAAC,CAAC,CAAC,EAAEmI,WAAW,CAAC,CAAC,EAAE,aAAa9G,IAAI,CAAC0G,SAAS,EAAE/H,QAAQ,CAAC,CAAC,CAAC,EAAEgI,UAAU,EAAE;UACtIC,EAAE,EAAEA,EAAE;UACNlB,QAAQ,EAAEA;QACZ,CAAC,CAAC,CAAC,EAAE3E,cAAc,IAAI,aAAaf,IAAI,CAACkH,kBAAkB,EAAEvI,QAAQ,CAAC,CAAC,CAAC,EAAEwI,mBAAmB,EAAE;UAC7FzB,QAAQ,EAAE3E;QACZ,CAAC,CAAC,CAAC,EAAEC,YAAY,IAAI,aAAahB,IAAI,CAACoH,gBAAgB,EAAEzI,QAAQ,CAAC,CAAC,CAAC,EAAE0I,iBAAiB,EAAE;UACvF3B,QAAQ,EAAE1E;QACZ,CAAC,CAAC,CAAC;MACL,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACF0G,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGtC,IAAI,CAACuC,SAAS,CAAC,yBAAyB;EAC9E;EACA;EACA;EACA;EACA;AACF;AACA;EACEnC,QAAQ,EAAE3G,SAAS,CAAC+I,IAAI;EACxB;AACF;AACA;EACEnC,SAAS,EAAE5G,SAAS,CAACgJ,MAAM;EAC3B;AACF;AACA;AACA;EACExH,KAAK,EAAExB,SAAS,CAAC,sCAAsCiJ,SAAS,CAAC,CAACjJ,SAAS,CAACkJ,KAAK,CAAC,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,EAAElJ,SAAS,CAACgJ,MAAM,CAAC,CAAC;EAC7J;AACF;AACA;AACA;EACElC,SAAS,EAAE9G,SAAS,CAAC0H,WAAW;EAChC;AACF;AACA;AACA;EACEpG,QAAQ,EAAEtB,SAAS,CAACmJ,IAAI;EACxB;AACF;AACA;EACElH,YAAY,EAAEjC,SAAS,CAAC+I,IAAI;EAC5B;AACF;AACA;EACElC,OAAO,EAAE7G,SAAS,CAACoJ,IAAI;EACvB;AACF;AACA;AACA;AACA;EACE7H,IAAI,EAAEvB,SAAS,CAAC,sCAAsCiJ,SAAS,CAAC,CAACjJ,SAAS,CAACkJ,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAElJ,SAAS,CAACgJ,MAAM,CAAC,CAAC;EACxH;AACF;AACA;AACA;EACEjC,SAAS,EAAE/G,SAAS,CAACqJ,KAAK,CAAC;IACzBvH,MAAM,EAAE9B,SAAS,CAACiJ,SAAS,CAAC,CAACjJ,SAAS,CAACoJ,IAAI,EAAEpJ,SAAS,CAACsJ,MAAM,CAAC,CAAC;IAC/DrH,YAAY,EAAEjC,SAAS,CAACiJ,SAAS,CAAC,CAACjJ,SAAS,CAACoJ,IAAI,EAAEpJ,SAAS,CAACsJ,MAAM,CAAC,CAAC;IACrEvH,KAAK,EAAE/B,SAAS,CAACiJ,SAAS,CAAC,CAACjJ,SAAS,CAACoJ,IAAI,EAAEpJ,SAAS,CAACsJ,MAAM,CAAC,CAAC;IAC9DzH,IAAI,EAAE7B,SAAS,CAACiJ,SAAS,CAAC,CAACjJ,SAAS,CAACoJ,IAAI,EAAEpJ,SAAS,CAACsJ,MAAM,CAAC,CAAC;IAC7DtH,cAAc,EAAEhC,SAAS,CAACiJ,SAAS,CAAC,CAACjJ,SAAS,CAACoJ,IAAI,EAAEpJ,SAAS,CAACsJ,MAAM,CAAC;EACxE,CAAC,CAAC;EACF;AACF;AACA;AACA;EACE1H,KAAK,EAAE5B,SAAS,CAACqJ,KAAK,CAAC;IACrBvH,MAAM,EAAE9B,SAAS,CAAC0H,WAAW;IAC7BzF,YAAY,EAAEjC,SAAS,CAAC0H,WAAW;IACnC3F,KAAK,EAAE/B,SAAS,CAAC0H,WAAW;IAC5B7F,IAAI,EAAE7B,SAAS,CAAC0H,WAAW;IAC3B1F,cAAc,EAAEhC,SAAS,CAAC0H;EAC5B,CAAC,CAAC;EACF;AACF;AACA;EACE1F,cAAc,EAAEhC,SAAS,CAAC+I,IAAI;EAC9B;AACF;AACA;EACEQ,EAAE,EAAEvJ,SAAS,CAACiJ,SAAS,CAAC,CAACjJ,SAAS,CAACwJ,OAAO,CAACxJ,SAAS,CAACiJ,SAAS,CAAC,CAACjJ,SAAS,CAACoJ,IAAI,EAAEpJ,SAAS,CAACsJ,MAAM,EAAEtJ,SAAS,CAACmJ,IAAI,CAAC,CAAC,CAAC,EAAEnJ,SAAS,CAACoJ,IAAI,EAAEpJ,SAAS,CAACsJ,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;EACE5H,OAAO,EAAE1B,SAAS,CAAC,sCAAsCiJ,SAAS,CAAC,CAACjJ,SAAS,CAACkJ,KAAK,CAAC,CAAC,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,EAAElJ,SAAS,CAACgJ,MAAM,CAAC;AAChJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAezC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}