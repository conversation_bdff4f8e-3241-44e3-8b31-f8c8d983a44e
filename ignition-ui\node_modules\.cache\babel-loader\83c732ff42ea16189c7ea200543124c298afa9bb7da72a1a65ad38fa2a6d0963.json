{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"disabled\", \"component\", \"selected\", \"color\", \"orientation\", \"variant\", \"slots\", \"slotProps\", \"id\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_capitalize as capitalize } from '@mui/utils';\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport { useMenuItem, useMenuItemContextStabilizer } from '@mui/base/useMenuItem';\nimport { ListContext } from '@mui/base/useList';\nimport { StyledListItemButton } from '../ListItemButton/ListItemButton';\nimport { styled, useThemeProps } from '../styles';\nimport { useVariantColor } from '../styles/variantColorInheritance';\nimport { getMenuItemUtilityClass } from './menuItemClasses';\nimport RowListContext from '../List/RowListContext';\nimport ListItemButtonOrientationContext from '../ListItemButton/ListItemButtonOrientationContext';\nimport useSlot from '../utils/useSlot';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    focusVisible,\n    disabled,\n    selected,\n    color,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', focusVisible && 'focusVisible', disabled && 'disabled', selected && 'selected', color && `color${capitalize(color)}`, variant && `variant${capitalize(variant)}`]\n  };\n  const composedClasses = composeClasses(slots, getMenuItemUtilityClass, {});\n  return composedClasses;\n};\nconst MenuItemRoot = styled(StyledListItemButton, {\n  name: 'JoyMenuItem',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({});\nconst MenuItem = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(function MenuItem(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyMenuItem'\n  });\n  const row = React.useContext(RowListContext);\n  const {\n      children,\n      disabled: disabledProp = false,\n      component = 'li',\n      selected = false,\n      color: colorProp = 'neutral',\n      orientation = 'horizontal',\n      variant: variantProp = 'plain',\n      slots = {},\n      slotProps = {},\n      id\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    variant = variantProp,\n    color = colorProp\n  } = useVariantColor(inProps.variant, inProps.color);\n  const {\n    getRootProps,\n    disabled,\n    focusVisible\n  } = useMenuItem({\n    id,\n    disabled: disabledProp,\n    rootRef: ref\n  });\n  const ownerState = _extends({}, props, {\n    component,\n    color,\n    disabled,\n    focusVisible,\n    orientation,\n    selected,\n    row,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    elementType: MenuItemRoot,\n    getSlotProps: getRootProps,\n    externalForwardedProps,\n    className: classes.root,\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(ListItemButtonOrientationContext.Provider, {\n    value: orientation,\n    children: /*#__PURE__*/_jsx(SlotRoot, _extends({}, rootProps, {\n      children: children\n    }))\n  });\n}));\n\n/**\n *\n * Demos:\n *\n * - [Menu](https://mui.com/joy-ui/react-menu/)\n *\n * API:\n *\n * - [MenuItem API](https://mui.com/joy-ui/api/menu-item/)\n * - inherits [ListItemButton API](https://mui.com/joy-ui/api/list-item-button/)\n */\nconst StableMenuItem = /*#__PURE__*/React.forwardRef(function StableMenuItem(props, ref) {\n  // This wrapper component is used as a performance optimization.\n  // `useMenuItemContextStabilizer` ensures that the context value\n  // is stable across renders, so that the actual MenuItem re-renders\n  // only when it needs to.\n  const {\n    contextValue,\n    id\n  } = useMenuItemContextStabilizer(props.id);\n  return /*#__PURE__*/_jsx(ListContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(MenuItem, _extends({}, props, {\n      id: id,\n      ref: ref\n    }))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? StableMenuItem.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  id: PropTypes.string\n} : void 0;\nexport default StableMenuItem;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "unstable_capitalize", "capitalize", "unstable_composeClasses", "composeClasses", "useMenuItem", "useMenuItemContextStabilizer", "ListContext", "StyledListItemButton", "styled", "useThemeProps", "useVariantColor", "getMenuItemUtilityClass", "RowListContext", "ListItemButtonOrientationContext", "useSlot", "jsx", "_jsx", "useUtilityClasses", "ownerState", "focusVisible", "disabled", "selected", "color", "variant", "slots", "root", "composedClasses", "MenuItemRoot", "name", "slot", "overridesResolver", "props", "styles", "MenuItem", "memo", "forwardRef", "inProps", "ref", "row", "useContext", "children", "disabledProp", "component", "colorProp", "orientation", "variantProp", "slotProps", "id", "other", "getRootProps", "rootRef", "classes", "externalForwardedProps", "SlotRoot", "rootProps", "elementType", "getSlotProps", "className", "Provider", "value", "StableMenuItem", "contextValue", "process", "env", "NODE_ENV", "propTypes", "node", "string"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/MenuItem/MenuItem.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"disabled\", \"component\", \"selected\", \"color\", \"orientation\", \"variant\", \"slots\", \"slotProps\", \"id\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_capitalize as capitalize } from '@mui/utils';\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport { useMenuItem, useMenuItemContextStabilizer } from '@mui/base/useMenuItem';\nimport { ListContext } from '@mui/base/useList';\nimport { StyledListItemButton } from '../ListItemButton/ListItemButton';\nimport { styled, useThemeProps } from '../styles';\nimport { useVariantColor } from '../styles/variantColorInheritance';\nimport { getMenuItemUtilityClass } from './menuItemClasses';\nimport RowListContext from '../List/RowListContext';\nimport ListItemButtonOrientationContext from '../ListItemButton/ListItemButtonOrientationContext';\nimport useSlot from '../utils/useSlot';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    focusVisible,\n    disabled,\n    selected,\n    color,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', focusVisible && 'focusVisible', disabled && 'disabled', selected && 'selected', color && `color${capitalize(color)}`, variant && `variant${capitalize(variant)}`]\n  };\n  const composedClasses = composeClasses(slots, getMenuItemUtilityClass, {});\n  return composedClasses;\n};\nconst MenuItemRoot = styled(StyledListItemButton, {\n  name: 'JoyMenuItem',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({});\nconst MenuItem = /*#__PURE__*/React.memo( /*#__PURE__*/React.forwardRef(function MenuItem(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyMenuItem'\n  });\n  const row = React.useContext(RowListContext);\n  const {\n      children,\n      disabled: disabledProp = false,\n      component = 'li',\n      selected = false,\n      color: colorProp = 'neutral',\n      orientation = 'horizontal',\n      variant: variantProp = 'plain',\n      slots = {},\n      slotProps = {},\n      id\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    variant = variantProp,\n    color = colorProp\n  } = useVariantColor(inProps.variant, inProps.color);\n  const {\n    getRootProps,\n    disabled,\n    focusVisible\n  } = useMenuItem({\n    id,\n    disabled: disabledProp,\n    rootRef: ref\n  });\n  const ownerState = _extends({}, props, {\n    component,\n    color,\n    disabled,\n    focusVisible,\n    orientation,\n    selected,\n    row,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    elementType: MenuItemRoot,\n    getSlotProps: getRootProps,\n    externalForwardedProps,\n    className: classes.root,\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(ListItemButtonOrientationContext.Provider, {\n    value: orientation,\n    children: /*#__PURE__*/_jsx(SlotRoot, _extends({}, rootProps, {\n      children: children\n    }))\n  });\n}));\n\n/**\n *\n * Demos:\n *\n * - [Menu](https://mui.com/joy-ui/react-menu/)\n *\n * API:\n *\n * - [MenuItem API](https://mui.com/joy-ui/api/menu-item/)\n * - inherits [ListItemButton API](https://mui.com/joy-ui/api/list-item-button/)\n */\nconst StableMenuItem = /*#__PURE__*/React.forwardRef(function StableMenuItem(props, ref) {\n  // This wrapper component is used as a performance optimization.\n  // `useMenuItemContextStabilizer` ensures that the context value\n  // is stable across renders, so that the actual MenuItem re-renders\n  // only when it needs to.\n  const {\n    contextValue,\n    id\n  } = useMenuItemContextStabilizer(props.id);\n  return /*#__PURE__*/_jsx(ListContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(MenuItem, _extends({}, props, {\n      id: id,\n      ref: ref\n    }))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? StableMenuItem.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  id: PropTypes.string\n} : void 0;\nexport default StableMenuItem;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,aAAa,EAAE,SAAS,EAAE,OAAO,EAAE,WAAW,EAAE,IAAI,CAAC;AAClI,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,mBAAmB,IAAIC,UAAU,QAAQ,YAAY;AAC9D,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,0BAA0B;AACpF,SAASC,WAAW,EAAEC,4BAA4B,QAAQ,uBAAuB;AACjF,SAASC,WAAW,QAAQ,mBAAmB;AAC/C,SAASC,oBAAoB,QAAQ,kCAAkC;AACvE,SAASC,MAAM,EAAEC,aAAa,QAAQ,WAAW;AACjD,SAASC,eAAe,QAAQ,mCAAmC;AACnE,SAASC,uBAAuB,QAAQ,mBAAmB;AAC3D,OAAOC,cAAc,MAAM,wBAAwB;AACnD,OAAOC,gCAAgC,MAAM,oDAAoD;AACjG,OAAOC,OAAO,MAAM,kBAAkB;AACtC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,YAAY;IACZC,QAAQ;IACRC,QAAQ;IACRC,KAAK;IACLC;EACF,CAAC,GAAGL,UAAU;EACd,MAAMM,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEN,YAAY,IAAI,cAAc,EAAEC,QAAQ,IAAI,UAAU,EAAEC,QAAQ,IAAI,UAAU,EAAEC,KAAK,IAAI,QAAQrB,UAAU,CAACqB,KAAK,CAAC,EAAE,EAAEC,OAAO,IAAI,UAAUtB,UAAU,CAACsB,OAAO,CAAC,EAAE;EACjL,CAAC;EACD,MAAMG,eAAe,GAAGvB,cAAc,CAACqB,KAAK,EAAEb,uBAAuB,EAAE,CAAC,CAAC,CAAC;EAC1E,OAAOe,eAAe;AACxB,CAAC;AACD,MAAMC,YAAY,GAAGnB,MAAM,CAACD,oBAAoB,EAAE;EAChDqB,IAAI,EAAE,aAAa;EACnBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACP;AAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACN,MAAMQ,QAAQ,GAAG,aAAanC,KAAK,CAACoC,IAAI,CAAE,aAAapC,KAAK,CAACqC,UAAU,CAAC,SAASF,QAAQA,CAACG,OAAO,EAAEC,GAAG,EAAE;EACtG,MAAMN,KAAK,GAAGtB,aAAa,CAAC;IAC1BsB,KAAK,EAAEK,OAAO;IACdR,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAMU,GAAG,GAAGxC,KAAK,CAACyC,UAAU,CAAC3B,cAAc,CAAC;EAC5C,MAAM;MACF4B,QAAQ;MACRpB,QAAQ,EAAEqB,YAAY,GAAG,KAAK;MAC9BC,SAAS,GAAG,IAAI;MAChBrB,QAAQ,GAAG,KAAK;MAChBC,KAAK,EAAEqB,SAAS,GAAG,SAAS;MAC5BC,WAAW,GAAG,YAAY;MAC1BrB,OAAO,EAAEsB,WAAW,GAAG,OAAO;MAC9BrB,KAAK,GAAG,CAAC,CAAC;MACVsB,SAAS,GAAG,CAAC,CAAC;MACdC;IACF,CAAC,GAAGhB,KAAK;IACTiB,KAAK,GAAGpD,6BAA6B,CAACmC,KAAK,EAAElC,SAAS,CAAC;EACzD,MAAM;IACJ0B,OAAO,GAAGsB,WAAW;IACrBvB,KAAK,GAAGqB;EACV,CAAC,GAAGjC,eAAe,CAAC0B,OAAO,CAACb,OAAO,EAAEa,OAAO,CAACd,KAAK,CAAC;EACnD,MAAM;IACJ2B,YAAY;IACZ7B,QAAQ;IACRD;EACF,CAAC,GAAGf,WAAW,CAAC;IACd2C,EAAE;IACF3B,QAAQ,EAAEqB,YAAY;IACtBS,OAAO,EAAEb;EACX,CAAC,CAAC;EACF,MAAMnB,UAAU,GAAGvB,QAAQ,CAAC,CAAC,CAAC,EAAEoC,KAAK,EAAE;IACrCW,SAAS;IACTpB,KAAK;IACLF,QAAQ;IACRD,YAAY;IACZyB,WAAW;IACXvB,QAAQ;IACRiB,GAAG;IACHf;EACF,CAAC,CAAC;EACF,MAAM4B,OAAO,GAAGlC,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMkC,sBAAsB,GAAGzD,QAAQ,CAAC,CAAC,CAAC,EAAEqD,KAAK,EAAE;IACjDN,SAAS;IACTlB,KAAK;IACLsB;EACF,CAAC,CAAC;EACF,MAAM,CAACO,QAAQ,EAAEC,SAAS,CAAC,GAAGxC,OAAO,CAAC,MAAM,EAAE;IAC5CuB,GAAG;IACHkB,WAAW,EAAE5B,YAAY;IACzB6B,YAAY,EAAEP,YAAY;IAC1BG,sBAAsB;IACtBK,SAAS,EAAEN,OAAO,CAAC1B,IAAI;IACvBP;EACF,CAAC,CAAC;EACF,OAAO,aAAaF,IAAI,CAACH,gCAAgC,CAAC6C,QAAQ,EAAE;IAClEC,KAAK,EAAEf,WAAW;IAClBJ,QAAQ,EAAE,aAAaxB,IAAI,CAACqC,QAAQ,EAAE1D,QAAQ,CAAC,CAAC,CAAC,EAAE2D,SAAS,EAAE;MAC5Dd,QAAQ,EAAEA;IACZ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC,CAAC;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoB,cAAc,GAAG,aAAa9D,KAAK,CAACqC,UAAU,CAAC,SAASyB,cAAcA,CAAC7B,KAAK,EAAEM,GAAG,EAAE;EACvF;EACA;EACA;EACA;EACA,MAAM;IACJwB,YAAY;IACZd;EACF,CAAC,GAAG1C,4BAA4B,CAAC0B,KAAK,CAACgB,EAAE,CAAC;EAC1C,OAAO,aAAa/B,IAAI,CAACV,WAAW,CAACoD,QAAQ,EAAE;IAC7CC,KAAK,EAAEE,YAAY;IACnBrB,QAAQ,EAAE,aAAaxB,IAAI,CAACiB,QAAQ,EAAEtC,QAAQ,CAAC,CAAC,CAAC,EAAEoC,KAAK,EAAE;MACxDgB,EAAE,EAAEA,EAAE;MACNV,GAAG,EAAEA;IACP,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFyB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGJ,cAAc,CAACK,SAAS,CAAC,yBAAyB;EACxF;EACA;EACA;EACA;EACA;AACF;AACA;EACEzB,QAAQ,EAAEzC,SAAS,CAACmE,IAAI;EACxBnB,EAAE,EAAEhD,SAAS,CAACoE;AAChB,CAAC,GAAG,KAAK,CAAC;AACV,eAAeP,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}