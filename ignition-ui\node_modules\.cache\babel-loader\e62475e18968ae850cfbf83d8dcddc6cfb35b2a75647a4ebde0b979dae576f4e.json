{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"alt\", \"color\", \"size\", \"variant\", \"src\", \"srcSet\", \"children\", \"component\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport { unstable_capitalize as capitalize } from '@mui/utils';\nimport { useThemeProps } from '../styles';\nimport useSlot from '../utils/useSlot';\nimport styled from '../styles/styled';\nimport Person from '../internal/svg-icons/Person';\nimport { getAvatarUtilityClass } from './avatarClasses';\nimport { AvatarGroupContext } from '../AvatarGroup/AvatarGroup';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    size,\n    variant,\n    color,\n    src,\n    srcSet\n  } = ownerState;\n  const slots = {\n    root: ['root', variant && `variant${capitalize(variant)}`, color && `color${capitalize(color)}`, size && `size${capitalize(size)}`],\n    img: [(src || srcSet) && 'img'],\n    fallback: ['fallback']\n  };\n  return composeClasses(slots, getAvatarUtilityClass, {});\n};\nconst AvatarRoot = styled('div', {\n  name: 'JoyAvatar',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(_ref => {\n  let {\n    theme,\n    ownerState\n  } = _ref;\n  var _theme$variants;\n  return _extends({\n    '--Icon-color': ownerState.color !== 'neutral' || ownerState.variant === 'solid' ? 'currentColor' : theme.vars.palette.text.icon\n  }, theme.typography[`title-${ownerState.size}`], ownerState.size === 'sm' && {\n    width: `var(--Avatar-size, 2rem)`,\n    height: `var(--Avatar-size, 2rem)`,\n    fontSize: `calc(var(--Avatar-size, 2rem) * 0.4375)` // default as 14px\n  }, ownerState.size === 'md' && {\n    width: `var(--Avatar-size, 2.5rem)`,\n    height: `var(--Avatar-size, 2.5rem)`,\n    fontSize: `calc(var(--Avatar-size, 2.5rem) * 0.4)` // default as 16px\n  }, ownerState.size === 'lg' && {\n    width: `var(--Avatar-size, 3rem)`,\n    height: `var(--Avatar-size, 3rem)`,\n    fontSize: `calc(var(--Avatar-size, 3rem) * 0.375)` // default as 18px\n  }, {\n    marginInlineStart: 'var(--Avatar-marginInlineStart)',\n    boxShadow: `var(--Avatar-ring)`,\n    position: 'relative',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    flexShrink: 0,\n    lineHeight: 1,\n    overflow: 'hidden',\n    borderRadius: 'var(--Avatar-radius, 50%)',\n    userSelect: 'none'\n  }, (_theme$variants = theme.variants[ownerState.variant]) == null ? void 0 : _theme$variants[ownerState.color]);\n});\nconst AvatarImg = styled('img', {\n  name: 'JoyAvatar',\n  slot: 'Img',\n  overridesResolver: (props, styles) => styles.img\n})({\n  width: '100%',\n  height: '100%',\n  textAlign: 'center',\n  // Handle non-square image. The property isn't supported by IE11.\n  objectFit: 'cover',\n  // Hide alt text.\n  color: 'transparent',\n  // Hide the image broken icon, only works on Chrome.\n  textIndent: 10000\n});\nconst AvatarFallback = styled(Person, {\n  name: 'JoyAvatar',\n  slot: 'Fallback',\n  overridesResolver: (props, styles) => styles.fallback\n})({\n  width: '64%',\n  height: '64%'\n});\nfunction useLoaded(_ref2) {\n  let {\n    crossOrigin,\n    referrerPolicy,\n    src,\n    srcSet\n  } = _ref2;\n  const [loaded, setLoaded] = React.useState(false);\n  React.useEffect(() => {\n    if (!src && !srcSet) {\n      return undefined;\n    }\n    setLoaded(false);\n    let active = true;\n    const image = new Image();\n    image.onload = () => {\n      if (!active) {\n        return;\n      }\n      setLoaded('loaded');\n    };\n    image.onerror = () => {\n      if (!active) {\n        return;\n      }\n      setLoaded('error');\n    };\n    image.crossOrigin = crossOrigin;\n    image.referrerPolicy = referrerPolicy;\n    if (src) {\n      image.src = src;\n    }\n    if (srcSet) {\n      image.srcset = srcSet;\n    }\n    return () => {\n      active = false;\n    };\n  }, [crossOrigin, referrerPolicy, src, srcSet]);\n  return loaded;\n}\n/**\n *\n * Demos:\n *\n * - [Avatar](https://mui.com/joy-ui/react-avatar/)\n * - [Skeleton](https://mui.com/joy-ui/react-skeleton/)\n *\n * API:\n *\n * - [Avatar API](https://mui.com/joy-ui/api/avatar/)\n */\nconst Avatar = /*#__PURE__*/React.forwardRef(function Avatar(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyAvatar'\n  });\n  const groupContext = React.useContext(AvatarGroupContext);\n  const {\n      alt,\n      color: colorProp = 'neutral',\n      size: sizeProp = 'md',\n      variant: variantProp = 'soft',\n      src,\n      srcSet,\n      children: childrenProp,\n      component,\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const variant = inProps.variant || (groupContext == null ? void 0 : groupContext.variant) || variantProp;\n  const color = inProps.color || (groupContext == null ? void 0 : groupContext.color) || colorProp;\n  const size = inProps.size || (groupContext == null ? void 0 : groupContext.size) || sizeProp;\n  let children = null;\n  const ownerState = _extends({}, props, {\n    color,\n    size,\n    variant,\n    grouped: !!groupContext\n  });\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    className: classes.root,\n    elementType: AvatarRoot,\n    externalForwardedProps,\n    ownerState\n  });\n  const [SlotImg, imageProps] = useSlot('img', {\n    additionalProps: {\n      alt,\n      src,\n      srcSet\n    },\n    className: classes.img,\n    elementType: AvatarImg,\n    externalForwardedProps,\n    ownerState\n  });\n  const [SlotFallback, fallbackProps] = useSlot('fallback', {\n    className: classes.fallback,\n    elementType: AvatarFallback,\n    externalForwardedProps,\n    ownerState\n  });\n\n  // Use a hook instead of onError on the img element to support server-side rendering.\n  const loaded = useLoaded(_extends({}, imageProps, {\n    src,\n    srcSet\n  }));\n  const hasImg = src || srcSet;\n  const hasImgNotFailing = hasImg && loaded !== 'error';\n  if (hasImgNotFailing) {\n    children = /*#__PURE__*/_jsx(SlotImg, _extends({}, imageProps));\n  } else if (childrenProp != null) {\n    children = childrenProp;\n  } else if (alt) {\n    children = alt[0];\n  } else {\n    children = /*#__PURE__*/_jsx(SlotFallback, _extends({}, fallbackProps));\n  }\n  return /*#__PURE__*/_jsx(SlotRoot, _extends({}, rootProps, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Avatar.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Used in combination with `src` or `srcSet` to\n   * provide an alt attribute for the rendered `img` element.\n   */\n  alt: PropTypes.string,\n  /**\n   * Used to render icon or text elements inside the Avatar if `src` is not set.\n   * This can be an element, or just a string.\n   */\n  children: PropTypes.node,\n  /**\n   * The color of the component. It supports those theme colors that make sense for this component.\n   * @default 'neutral'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['danger', 'neutral', 'primary', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The size of the component.\n   * It accepts theme values between 'sm' and 'lg'.\n   * @default 'md'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['lg', 'md', 'sm']), PropTypes.string]),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    fallback: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    img: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    fallback: PropTypes.elementType,\n    img: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * The `src` attribute for the `img` element.\n   */\n  src: PropTypes.string,\n  /**\n   * The `srcSet` attribute for the `img` element.\n   * Use this attribute for responsive image display.\n   */\n  srcSet: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The [global variant](https://mui.com/joy-ui/main-features/global-variants/) to use.\n   * @default 'soft'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['outlined', 'plain', 'soft', 'solid']), PropTypes.string])\n} : void 0;\nexport default Avatar;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "unstable_composeClasses", "composeClasses", "unstable_capitalize", "capitalize", "useThemeProps", "useSlot", "styled", "Person", "getAvatarUtilityClass", "AvatarGroupContext", "jsx", "_jsx", "useUtilityClasses", "ownerState", "size", "variant", "color", "src", "srcSet", "slots", "root", "img", "fallback", "AvatarRoot", "name", "slot", "overridesResolver", "props", "styles", "_ref", "theme", "_theme$variants", "vars", "palette", "text", "icon", "typography", "width", "height", "fontSize", "marginInlineStart", "boxShadow", "position", "display", "alignItems", "justifyContent", "flexShrink", "lineHeight", "overflow", "borderRadius", "userSelect", "variants", "AvatarImg", "textAlign", "objectFit", "textIndent", "AvatarFallback", "useLoaded", "_ref2", "crossOrigin", "referrerPolicy", "loaded", "setLoaded", "useState", "useEffect", "undefined", "active", "image", "Image", "onload", "onerror", "srcset", "Avatar", "forwardRef", "inProps", "ref", "groupContext", "useContext", "alt", "colorProp", "sizeProp", "variantProp", "children", "childrenProp", "component", "slotProps", "other", "grouped", "classes", "externalForwardedProps", "SlotRoot", "rootProps", "className", "elementType", "SlotImg", "imageProps", "additionalProps", "SlotFallback", "fallbackProps", "hasImg", "hasImgNotFailing", "process", "env", "NODE_ENV", "propTypes", "string", "node", "oneOfType", "oneOf", "shape", "func", "object", "sx", "arrayOf", "bool"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Avatar/Avatar.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"alt\", \"color\", \"size\", \"variant\", \"src\", \"srcSet\", \"children\", \"component\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport { unstable_capitalize as capitalize } from '@mui/utils';\nimport { useThemeProps } from '../styles';\nimport useSlot from '../utils/useSlot';\nimport styled from '../styles/styled';\nimport Person from '../internal/svg-icons/Person';\nimport { getAvatarUtilityClass } from './avatarClasses';\nimport { AvatarGroupContext } from '../AvatarGroup/AvatarGroup';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    size,\n    variant,\n    color,\n    src,\n    srcSet\n  } = ownerState;\n  const slots = {\n    root: ['root', variant && `variant${capitalize(variant)}`, color && `color${capitalize(color)}`, size && `size${capitalize(size)}`],\n    img: [(src || srcSet) && 'img'],\n    fallback: ['fallback']\n  };\n  return composeClasses(slots, getAvatarUtilityClass, {});\n};\nconst AvatarRoot = styled('div', {\n  name: 'JoyAvatar',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(({\n  theme,\n  ownerState\n}) => {\n  var _theme$variants;\n  return _extends({\n    '--Icon-color': ownerState.color !== 'neutral' || ownerState.variant === 'solid' ? 'currentColor' : theme.vars.palette.text.icon\n  }, theme.typography[`title-${ownerState.size}`], ownerState.size === 'sm' && {\n    width: `var(--Avatar-size, 2rem)`,\n    height: `var(--Avatar-size, 2rem)`,\n    fontSize: `calc(var(--Avatar-size, 2rem) * 0.4375)` // default as 14px\n  }, ownerState.size === 'md' && {\n    width: `var(--Avatar-size, 2.5rem)`,\n    height: `var(--Avatar-size, 2.5rem)`,\n    fontSize: `calc(var(--Avatar-size, 2.5rem) * 0.4)` // default as 16px\n  }, ownerState.size === 'lg' && {\n    width: `var(--Avatar-size, 3rem)`,\n    height: `var(--Avatar-size, 3rem)`,\n    fontSize: `calc(var(--Avatar-size, 3rem) * 0.375)` // default as 18px\n  }, {\n    marginInlineStart: 'var(--Avatar-marginInlineStart)',\n    boxShadow: `var(--Avatar-ring)`,\n    position: 'relative',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    flexShrink: 0,\n    lineHeight: 1,\n    overflow: 'hidden',\n    borderRadius: 'var(--Avatar-radius, 50%)',\n    userSelect: 'none'\n  }, (_theme$variants = theme.variants[ownerState.variant]) == null ? void 0 : _theme$variants[ownerState.color]);\n});\nconst AvatarImg = styled('img', {\n  name: 'JoyAvatar',\n  slot: 'Img',\n  overridesResolver: (props, styles) => styles.img\n})({\n  width: '100%',\n  height: '100%',\n  textAlign: 'center',\n  // Handle non-square image. The property isn't supported by IE11.\n  objectFit: 'cover',\n  // Hide alt text.\n  color: 'transparent',\n  // Hide the image broken icon, only works on Chrome.\n  textIndent: 10000\n});\nconst AvatarFallback = styled(Person, {\n  name: 'JoyAvatar',\n  slot: 'Fallback',\n  overridesResolver: (props, styles) => styles.fallback\n})({\n  width: '64%',\n  height: '64%'\n});\nfunction useLoaded({\n  crossOrigin,\n  referrerPolicy,\n  src,\n  srcSet\n}) {\n  const [loaded, setLoaded] = React.useState(false);\n  React.useEffect(() => {\n    if (!src && !srcSet) {\n      return undefined;\n    }\n    setLoaded(false);\n    let active = true;\n    const image = new Image();\n    image.onload = () => {\n      if (!active) {\n        return;\n      }\n      setLoaded('loaded');\n    };\n    image.onerror = () => {\n      if (!active) {\n        return;\n      }\n      setLoaded('error');\n    };\n    image.crossOrigin = crossOrigin;\n    image.referrerPolicy = referrerPolicy;\n    if (src) {\n      image.src = src;\n    }\n    if (srcSet) {\n      image.srcset = srcSet;\n    }\n    return () => {\n      active = false;\n    };\n  }, [crossOrigin, referrerPolicy, src, srcSet]);\n  return loaded;\n}\n/**\n *\n * Demos:\n *\n * - [Avatar](https://mui.com/joy-ui/react-avatar/)\n * - [Skeleton](https://mui.com/joy-ui/react-skeleton/)\n *\n * API:\n *\n * - [Avatar API](https://mui.com/joy-ui/api/avatar/)\n */\nconst Avatar = /*#__PURE__*/React.forwardRef(function Avatar(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyAvatar'\n  });\n  const groupContext = React.useContext(AvatarGroupContext);\n  const {\n      alt,\n      color: colorProp = 'neutral',\n      size: sizeProp = 'md',\n      variant: variantProp = 'soft',\n      src,\n      srcSet,\n      children: childrenProp,\n      component,\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const variant = inProps.variant || (groupContext == null ? void 0 : groupContext.variant) || variantProp;\n  const color = inProps.color || (groupContext == null ? void 0 : groupContext.color) || colorProp;\n  const size = inProps.size || (groupContext == null ? void 0 : groupContext.size) || sizeProp;\n  let children = null;\n  const ownerState = _extends({}, props, {\n    color,\n    size,\n    variant,\n    grouped: !!groupContext\n  });\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    className: classes.root,\n    elementType: AvatarRoot,\n    externalForwardedProps,\n    ownerState\n  });\n  const [SlotImg, imageProps] = useSlot('img', {\n    additionalProps: {\n      alt,\n      src,\n      srcSet\n    },\n    className: classes.img,\n    elementType: AvatarImg,\n    externalForwardedProps,\n    ownerState\n  });\n  const [SlotFallback, fallbackProps] = useSlot('fallback', {\n    className: classes.fallback,\n    elementType: AvatarFallback,\n    externalForwardedProps,\n    ownerState\n  });\n\n  // Use a hook instead of onError on the img element to support server-side rendering.\n  const loaded = useLoaded(_extends({}, imageProps, {\n    src,\n    srcSet\n  }));\n  const hasImg = src || srcSet;\n  const hasImgNotFailing = hasImg && loaded !== 'error';\n  if (hasImgNotFailing) {\n    children = /*#__PURE__*/_jsx(SlotImg, _extends({}, imageProps));\n  } else if (childrenProp != null) {\n    children = childrenProp;\n  } else if (alt) {\n    children = alt[0];\n  } else {\n    children = /*#__PURE__*/_jsx(SlotFallback, _extends({}, fallbackProps));\n  }\n  return /*#__PURE__*/_jsx(SlotRoot, _extends({}, rootProps, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Avatar.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Used in combination with `src` or `srcSet` to\n   * provide an alt attribute for the rendered `img` element.\n   */\n  alt: PropTypes.string,\n  /**\n   * Used to render icon or text elements inside the Avatar if `src` is not set.\n   * This can be an element, or just a string.\n   */\n  children: PropTypes.node,\n  /**\n   * The color of the component. It supports those theme colors that make sense for this component.\n   * @default 'neutral'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['danger', 'neutral', 'primary', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The size of the component.\n   * It accepts theme values between 'sm' and 'lg'.\n   * @default 'md'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['lg', 'md', 'sm']), PropTypes.string]),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    fallback: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    img: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    fallback: PropTypes.elementType,\n    img: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * The `src` attribute for the `img` element.\n   */\n  src: PropTypes.string,\n  /**\n   * The `srcSet` attribute for the `img` element.\n   * Use this attribute for responsive image display.\n   */\n  srcSet: PropTypes.string,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The [global variant](https://mui.com/joy-ui/main-features/global-variants/) to use.\n   * @default 'soft'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['outlined', 'plain', 'soft', 'solid']), PropTypes.string])\n} : void 0;\nexport default Avatar;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,CAAC;AACrH,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,WAAW;AACrE,SAASC,mBAAmB,IAAIC,UAAU,QAAQ,YAAY;AAC9D,SAASC,aAAa,QAAQ,WAAW;AACzC,OAAOC,OAAO,MAAM,kBAAkB;AACtC,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,MAAM,MAAM,8BAA8B;AACjD,SAASC,qBAAqB,QAAQ,iBAAiB;AACvD,SAASC,kBAAkB,QAAQ,4BAA4B;AAC/D,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,IAAI;IACJC,OAAO;IACPC,KAAK;IACLC,GAAG;IACHC;EACF,CAAC,GAAGL,UAAU;EACd,MAAMM,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEL,OAAO,IAAI,UAAUZ,UAAU,CAACY,OAAO,CAAC,EAAE,EAAEC,KAAK,IAAI,QAAQb,UAAU,CAACa,KAAK,CAAC,EAAE,EAAEF,IAAI,IAAI,OAAOX,UAAU,CAACW,IAAI,CAAC,EAAE,CAAC;IACnIO,GAAG,EAAE,CAAC,CAACJ,GAAG,IAAIC,MAAM,KAAK,KAAK,CAAC;IAC/BI,QAAQ,EAAE,CAAC,UAAU;EACvB,CAAC;EACD,OAAOrB,cAAc,CAACkB,KAAK,EAAEX,qBAAqB,EAAE,CAAC,CAAC,CAAC;AACzD,CAAC;AACD,MAAMe,UAAU,GAAGjB,MAAM,CAAC,KAAK,EAAE;EAC/BkB,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACR;AAC/C,CAAC,CAAC,CAACS,IAAA,IAGG;EAAA,IAHF;IACFC,KAAK;IACLjB;EACF,CAAC,GAAAgB,IAAA;EACC,IAAIE,eAAe;EACnB,OAAOnC,QAAQ,CAAC;IACd,cAAc,EAAEiB,UAAU,CAACG,KAAK,KAAK,SAAS,IAAIH,UAAU,CAACE,OAAO,KAAK,OAAO,GAAG,cAAc,GAAGe,KAAK,CAACE,IAAI,CAACC,OAAO,CAACC,IAAI,CAACC;EAC9H,CAAC,EAAEL,KAAK,CAACM,UAAU,CAAC,SAASvB,UAAU,CAACC,IAAI,EAAE,CAAC,EAAED,UAAU,CAACC,IAAI,KAAK,IAAI,IAAI;IAC3EuB,KAAK,EAAE,0BAA0B;IACjCC,MAAM,EAAE,0BAA0B;IAClCC,QAAQ,EAAE,yCAAyC,CAAC;EACtD,CAAC,EAAE1B,UAAU,CAACC,IAAI,KAAK,IAAI,IAAI;IAC7BuB,KAAK,EAAE,4BAA4B;IACnCC,MAAM,EAAE,4BAA4B;IACpCC,QAAQ,EAAE,wCAAwC,CAAC;EACrD,CAAC,EAAE1B,UAAU,CAACC,IAAI,KAAK,IAAI,IAAI;IAC7BuB,KAAK,EAAE,0BAA0B;IACjCC,MAAM,EAAE,0BAA0B;IAClCC,QAAQ,EAAE,wCAAwC,CAAC;EACrD,CAAC,EAAE;IACDC,iBAAiB,EAAE,iCAAiC;IACpDC,SAAS,EAAE,oBAAoB;IAC/BC,QAAQ,EAAE,UAAU;IACpBC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,CAAC;IACbC,UAAU,EAAE,CAAC;IACbC,QAAQ,EAAE,QAAQ;IAClBC,YAAY,EAAE,2BAA2B;IACzCC,UAAU,EAAE;EACd,CAAC,EAAE,CAACnB,eAAe,GAAGD,KAAK,CAACqB,QAAQ,CAACtC,UAAU,CAACE,OAAO,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGgB,eAAe,CAAClB,UAAU,CAACG,KAAK,CAAC,CAAC;AACjH,CAAC,CAAC;AACF,MAAMoC,SAAS,GAAG9C,MAAM,CAAC,KAAK,EAAE;EAC9BkB,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,KAAK;EACXC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACP;AAC/C,CAAC,CAAC,CAAC;EACDgB,KAAK,EAAE,MAAM;EACbC,MAAM,EAAE,MAAM;EACde,SAAS,EAAE,QAAQ;EACnB;EACAC,SAAS,EAAE,OAAO;EAClB;EACAtC,KAAK,EAAE,aAAa;EACpB;EACAuC,UAAU,EAAE;AACd,CAAC,CAAC;AACF,MAAMC,cAAc,GAAGlD,MAAM,CAACC,MAAM,EAAE;EACpCiB,IAAI,EAAE,WAAW;EACjBC,IAAI,EAAE,UAAU;EAChBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC/C,CAAC,CAAC,CAAC;EACDe,KAAK,EAAE,KAAK;EACZC,MAAM,EAAE;AACV,CAAC,CAAC;AACF,SAASmB,SAASA,CAAAC,KAAA,EAKf;EAAA,IALgB;IACjBC,WAAW;IACXC,cAAc;IACd3C,GAAG;IACHC;EACF,CAAC,GAAAwC,KAAA;EACC,MAAM,CAACG,MAAM,EAAEC,SAAS,CAAC,GAAGhE,KAAK,CAACiE,QAAQ,CAAC,KAAK,CAAC;EACjDjE,KAAK,CAACkE,SAAS,CAAC,MAAM;IACpB,IAAI,CAAC/C,GAAG,IAAI,CAACC,MAAM,EAAE;MACnB,OAAO+C,SAAS;IAClB;IACAH,SAAS,CAAC,KAAK,CAAC;IAChB,IAAII,MAAM,GAAG,IAAI;IACjB,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAAC,CAAC;IACzBD,KAAK,CAACE,MAAM,GAAG,MAAM;MACnB,IAAI,CAACH,MAAM,EAAE;QACX;MACF;MACAJ,SAAS,CAAC,QAAQ,CAAC;IACrB,CAAC;IACDK,KAAK,CAACG,OAAO,GAAG,MAAM;MACpB,IAAI,CAACJ,MAAM,EAAE;QACX;MACF;MACAJ,SAAS,CAAC,OAAO,CAAC;IACpB,CAAC;IACDK,KAAK,CAACR,WAAW,GAAGA,WAAW;IAC/BQ,KAAK,CAACP,cAAc,GAAGA,cAAc;IACrC,IAAI3C,GAAG,EAAE;MACPkD,KAAK,CAAClD,GAAG,GAAGA,GAAG;IACjB;IACA,IAAIC,MAAM,EAAE;MACViD,KAAK,CAACI,MAAM,GAAGrD,MAAM;IACvB;IACA,OAAO,MAAM;MACXgD,MAAM,GAAG,KAAK;IAChB,CAAC;EACH,CAAC,EAAE,CAACP,WAAW,EAAEC,cAAc,EAAE3C,GAAG,EAAEC,MAAM,CAAC,CAAC;EAC9C,OAAO2C,MAAM;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMW,MAAM,GAAG,aAAa1E,KAAK,CAAC2E,UAAU,CAAC,SAASD,MAAMA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACzE,MAAMhD,KAAK,GAAGvB,aAAa,CAAC;IAC1BuB,KAAK,EAAE+C,OAAO;IACdlD,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAMoD,YAAY,GAAG9E,KAAK,CAAC+E,UAAU,CAACpE,kBAAkB,CAAC;EACzD,MAAM;MACFqE,GAAG;MACH9D,KAAK,EAAE+D,SAAS,GAAG,SAAS;MAC5BjE,IAAI,EAAEkE,QAAQ,GAAG,IAAI;MACrBjE,OAAO,EAAEkE,WAAW,GAAG,MAAM;MAC7BhE,GAAG;MACHC,MAAM;MACNgE,QAAQ,EAAEC,YAAY;MACtBC,SAAS;MACTjE,KAAK,GAAG,CAAC,CAAC;MACVkE,SAAS,GAAG,CAAC;IACf,CAAC,GAAG1D,KAAK;IACT2D,KAAK,GAAG3F,6BAA6B,CAACgC,KAAK,EAAE9B,SAAS,CAAC;EACzD,MAAMkB,OAAO,GAAG2D,OAAO,CAAC3D,OAAO,KAAK6D,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC7D,OAAO,CAAC,IAAIkE,WAAW;EACxG,MAAMjE,KAAK,GAAG0D,OAAO,CAAC1D,KAAK,KAAK4D,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC5D,KAAK,CAAC,IAAI+D,SAAS;EAChG,MAAMjE,IAAI,GAAG4D,OAAO,CAAC5D,IAAI,KAAK8D,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,YAAY,CAAC9D,IAAI,CAAC,IAAIkE,QAAQ;EAC5F,IAAIE,QAAQ,GAAG,IAAI;EACnB,MAAMrE,UAAU,GAAGjB,QAAQ,CAAC,CAAC,CAAC,EAAE+B,KAAK,EAAE;IACrCX,KAAK;IACLF,IAAI;IACJC,OAAO;IACPwE,OAAO,EAAE,CAAC,CAACX;EACb,CAAC,CAAC;EACF,MAAMY,OAAO,GAAG5E,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAM4E,sBAAsB,GAAG7F,QAAQ,CAAC,CAAC,CAAC,EAAE0F,KAAK,EAAE;IACjDF,SAAS;IACTjE,KAAK;IACLkE;EACF,CAAC,CAAC;EACF,MAAM,CAACK,QAAQ,EAAEC,SAAS,CAAC,GAAGtF,OAAO,CAAC,MAAM,EAAE;IAC5CsE,GAAG;IACHiB,SAAS,EAAEJ,OAAO,CAACpE,IAAI;IACvByE,WAAW,EAAEtE,UAAU;IACvBkE,sBAAsB;IACtB5E;EACF,CAAC,CAAC;EACF,MAAM,CAACiF,OAAO,EAAEC,UAAU,CAAC,GAAG1F,OAAO,CAAC,KAAK,EAAE;IAC3C2F,eAAe,EAAE;MACflB,GAAG;MACH7D,GAAG;MACHC;IACF,CAAC;IACD0E,SAAS,EAAEJ,OAAO,CAACnE,GAAG;IACtBwE,WAAW,EAAEzC,SAAS;IACtBqC,sBAAsB;IACtB5E;EACF,CAAC,CAAC;EACF,MAAM,CAACoF,YAAY,EAAEC,aAAa,CAAC,GAAG7F,OAAO,CAAC,UAAU,EAAE;IACxDuF,SAAS,EAAEJ,OAAO,CAAClE,QAAQ;IAC3BuE,WAAW,EAAErC,cAAc;IAC3BiC,sBAAsB;IACtB5E;EACF,CAAC,CAAC;;EAEF;EACA,MAAMgD,MAAM,GAAGJ,SAAS,CAAC7D,QAAQ,CAAC,CAAC,CAAC,EAAEmG,UAAU,EAAE;IAChD9E,GAAG;IACHC;EACF,CAAC,CAAC,CAAC;EACH,MAAMiF,MAAM,GAAGlF,GAAG,IAAIC,MAAM;EAC5B,MAAMkF,gBAAgB,GAAGD,MAAM,IAAItC,MAAM,KAAK,OAAO;EACrD,IAAIuC,gBAAgB,EAAE;IACpBlB,QAAQ,GAAG,aAAavE,IAAI,CAACmF,OAAO,EAAElG,QAAQ,CAAC,CAAC,CAAC,EAAEmG,UAAU,CAAC,CAAC;EACjE,CAAC,MAAM,IAAIZ,YAAY,IAAI,IAAI,EAAE;IAC/BD,QAAQ,GAAGC,YAAY;EACzB,CAAC,MAAM,IAAIL,GAAG,EAAE;IACdI,QAAQ,GAAGJ,GAAG,CAAC,CAAC,CAAC;EACnB,CAAC,MAAM;IACLI,QAAQ,GAAG,aAAavE,IAAI,CAACsF,YAAY,EAAErG,QAAQ,CAAC,CAAC,CAAC,EAAEsG,aAAa,CAAC,CAAC;EACzE;EACA,OAAO,aAAavF,IAAI,CAAC+E,QAAQ,EAAE9F,QAAQ,CAAC,CAAC,CAAC,EAAE+F,SAAS,EAAE;IACzDT,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFmB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG/B,MAAM,CAACgC,SAAS,CAAC,yBAAyB;EAChF;EACA;EACA;EACA;EACA;AACF;AACA;AACA;EACE1B,GAAG,EAAE/E,SAAS,CAAC0G,MAAM;EACrB;AACF;AACA;AACA;EACEvB,QAAQ,EAAEnF,SAAS,CAAC2G,IAAI;EACxB;AACF;AACA;AACA;EACE1F,KAAK,EAAEjB,SAAS,CAAC,sCAAsC4G,SAAS,CAAC,CAAC5G,SAAS,CAAC6G,KAAK,CAAC,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,EAAE7G,SAAS,CAAC0G,MAAM,CAAC,CAAC;EAC7J;AACF;AACA;AACA;EACErB,SAAS,EAAErF,SAAS,CAAC8F,WAAW;EAChC;AACF;AACA;AACA;AACA;EACE/E,IAAI,EAAEf,SAAS,CAAC,sCAAsC4G,SAAS,CAAC,CAAC5G,SAAS,CAAC6G,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE7G,SAAS,CAAC0G,MAAM,CAAC,CAAC;EACxH;AACF;AACA;AACA;EACEpB,SAAS,EAAEtF,SAAS,CAAC8G,KAAK,CAAC;IACzBvF,QAAQ,EAAEvB,SAAS,CAAC4G,SAAS,CAAC,CAAC5G,SAAS,CAAC+G,IAAI,EAAE/G,SAAS,CAACgH,MAAM,CAAC,CAAC;IACjE1F,GAAG,EAAEtB,SAAS,CAAC4G,SAAS,CAAC,CAAC5G,SAAS,CAAC+G,IAAI,EAAE/G,SAAS,CAACgH,MAAM,CAAC,CAAC;IAC5D3F,IAAI,EAAErB,SAAS,CAAC4G,SAAS,CAAC,CAAC5G,SAAS,CAAC+G,IAAI,EAAE/G,SAAS,CAACgH,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;EACE5F,KAAK,EAAEpB,SAAS,CAAC8G,KAAK,CAAC;IACrBvF,QAAQ,EAAEvB,SAAS,CAAC8F,WAAW;IAC/BxE,GAAG,EAAEtB,SAAS,CAAC8F,WAAW;IAC1BzE,IAAI,EAAErB,SAAS,CAAC8F;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACE5E,GAAG,EAAElB,SAAS,CAAC0G,MAAM;EACrB;AACF;AACA;AACA;EACEvF,MAAM,EAAEnB,SAAS,CAAC0G,MAAM;EACxB;AACF;AACA;EACEO,EAAE,EAAEjH,SAAS,CAAC4G,SAAS,CAAC,CAAC5G,SAAS,CAACkH,OAAO,CAAClH,SAAS,CAAC4G,SAAS,CAAC,CAAC5G,SAAS,CAAC+G,IAAI,EAAE/G,SAAS,CAACgH,MAAM,EAAEhH,SAAS,CAACmH,IAAI,CAAC,CAAC,CAAC,EAAEnH,SAAS,CAAC+G,IAAI,EAAE/G,SAAS,CAACgH,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;EACEhG,OAAO,EAAEhB,SAAS,CAAC,sCAAsC4G,SAAS,CAAC,CAAC5G,SAAS,CAAC6G,KAAK,CAAC,CAAC,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,EAAE7G,SAAS,CAAC0G,MAAM,CAAC;AAChJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAejC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}