{"ast": null, "code": "var _jsxFileName = \"C:\\\\ignition\\\\ignition-ui\\\\src\\\\components\\\\Sidebar\\\\SidebarComponent.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\n/* eslint-disable react-hooks/exhaustive-deps */\nimport React, { useEffect } from 'react';\nimport axios from 'axios';\nimport { NavbarBrand, Navbar } from \"reactstrap\";\nimport { useDispatch, useSelector } from 'react-redux';\nimport { Sidebar, Menu, MenuItem } from 'react-pro-sidebar';\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\nimport { setUser, clearUser } from '../../redux/userSlice';\nimport Iconify from 'components/Iconify/index';\nimport { APIURL } from \"helpers/constants\";\nimport { getHeaders, getRefreshToken, getAccessToken, setCookies, removeCookies } from \"helpers/functions\";\nimport styles from './styles.module.scss';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SidebarComponent = props => {\n  _s();\n  const {\n    logo,\n    onCollapseChange\n  } = props;\n  const [collapsed, setCollapsed] = React.useState(true);\n  const dispatch = useDispatch();\n  const navigate = useNavigate();\n  const location = useLocation();\n  const user = useSelector(state => state.user);\n  useEffect(() => {\n    fetchAccountInfo();\n  }, []);\n  const fetchAccountInfo = async () => {\n    if (!getAccessToken() && !getRefreshToken()) {\n      navigate(\"/login\", {\n        replace: true\n      });\n      return;\n    }\n    try {\n      const response = await axios.get(`${APIURL}/api/user/view-profile`, {\n        headers: getHeaders()\n      });\n      if (response.status === 200) {\n        dispatch(setUser({\n          first_name: response.data.first_name,\n          last_name: response.data.last_name,\n          avatar: response.data.avatar,\n          id: response.data.id\n        }));\n      } else {\n        handleUnauthorized(response.status, getRefreshToken());\n      }\n    } catch (error) {\n      handleFetchError(error, getRefreshToken());\n    }\n  };\n  const handleUnauthorized = async (status, refreshToken) => {\n    if (status === 401 && refreshToken) {\n      try {\n        await refreshTokenFunc(refreshToken);\n        await fetchAccountInfo();\n      } catch {\n        redirectToLogin();\n      }\n    } else {\n      redirectToLogin();\n    }\n  };\n  const handleFetchError = async (error, refreshToken) => {\n    if (refreshToken) {\n      try {\n        await refreshTokenFunc(refreshToken);\n        await fetchAccountInfo();\n      } catch {\n        redirectToLogin();\n      }\n    } else {\n      redirectToLogin();\n    }\n    console.error(error);\n  };\n  const refreshTokenFunc = async refreshToken => {\n    try {\n      const refreshResponse = await axios.post(`${APIURL}/api/user/token-refresh`, {\n        refresh: refreshToken\n      });\n      const newAccessToken = refreshResponse.data.access;\n      setCookies('accessToken', newAccessToken);\n      return newAccessToken;\n    } catch (error) {\n      console.error('Error refreshing token:', error);\n      removeCookies('accessToken');\n      removeCookies('refreshToken');\n      throw error;\n    }\n  };\n  const redirectToLogin = () => {\n    removeCookies('accessToken');\n    removeCookies('refreshToken');\n    navigate(\"/login\", {\n      replace: true\n    });\n  };\n  const handleLogout = () => {\n    removeCookies('accessToken');\n    removeCookies('refreshToken');\n    dispatch(clearUser());\n    navigate('/login', {\n      replace: true\n    });\n  };\n  const handleMouseEnter = () => {\n    setCollapsed(false);\n    onCollapseChange(false);\n  };\n  const handleMouseLeave = () => {\n    setCollapsed(true);\n    onCollapseChange(true);\n  };\n  return /*#__PURE__*/_jsxDEV(Navbar, {\n    className: `navbar-vertical fixed-left ${collapsed ? \"collapsed\" : \"\"} ${styles.customeNavbar}`,\n    expand: \"md\",\n    id: \"sidenav-main\",\n    onMouseEnter: handleMouseEnter,\n    onMouseLeave: handleMouseLeave,\n    style: {\n      padding: 0,\n      width: collapsed ? '70px' : '250px'\n    },\n    children: [logo && /*#__PURE__*/_jsxDEV(NavbarBrand, {\n      className: styles.navbarBrand,\n      children: /*#__PURE__*/_jsxDEV(Link, {\n        to: \"/d/\",\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          alt: logo.imgAlt,\n          className: styles.brandLogo,\n          src: logo.imgSrc\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Sidebar, {\n      collapsed: collapsed,\n      className: styles.sidebarMain,\n      width: \"240px\",\n      collapsedWidth: \"80px\",\n      children: /*#__PURE__*/_jsxDEV(Menu, {\n        className: styles.sidebarMenu,\n        children: [/*#__PURE__*/_jsxDEV(MenuItemLink, {\n          to: \"/d/\",\n          active: location.pathname === \"/d/\" || location.pathname.includes('/d/plan/') && location.pathname !== \"/d/plan/create\",\n          iconName: \"mage:dashboard-check\",\n          text: \"My Plans\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 143,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MenuItemLink, {\n          to: \"/d/plan/create\",\n          active: location.pathname === \"/d/plan/create\",\n          iconName: \"icons8:idea\",\n          text: \"New Idea\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MenuItemLink, {\n          to: \"/d/my/tasks/calendar\",\n          active: location.pathname === \"/d/my/tasks/calendar\",\n          iconName: \"ion:calendar-sharp\",\n          text: \"Tasks Schedule\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 155,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MenuItemLink, {\n          to: \"/d/connect\",\n          active: location.pathname === \"/d/connect\" || location.pathname.includes('/d/other/'),\n          iconName: \"fluent:person-28-filled\",\n          text: \"Connect with Friends\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(MenuItemLink, {\n          to: \"/d/notifications\",\n          active: location.pathname === \"/d/notifications\",\n          iconName: \"ion:notifications-sharp\",\n          text: \"Notifications\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 167,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 142,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 137,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(SidebarFooter, {\n      user: user,\n      collapsed: collapsed,\n      handleLogout: handleLogout\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 176,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 115,\n    columnNumber: 5\n  }, this);\n};\n_s(SidebarComponent, \"h75kBj6TVzXBQOXsNA5FgjVicmc=\", false, function () {\n  return [useDispatch, useNavigate, useLocation, useSelector];\n});\n_c = SidebarComponent;\nconst MenuItemLink = _ref => {\n  let {\n    to,\n    active,\n    iconName,\n    text\n  } = _ref;\n  return /*#__PURE__*/_jsxDEV(MenuItem, {\n    active: active,\n    component: /*#__PURE__*/_jsxDEV(Link, {\n      to: to\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 16\n    }, this),\n    className: `${styles.menuItem} ${active ? styles.activeMenuItem : ''}`,\n    icon: /*#__PURE__*/_jsxDEV(Iconify, {\n      icon: iconName,\n      width: 24,\n      height: 24,\n      className: styles.menuIcon\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this),\n    children: text\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 182,\n    columnNumber: 3\n  }, this);\n};\n_c2 = MenuItemLink;\nconst SidebarFooter = _ref2 => {\n  _s2();\n  let {\n    user,\n    collapsed,\n    handleLogout\n  } = _ref2;\n  const location = useLocation();\n  const isProfileActive = location.pathname === '/d/profile/';\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `${styles.sidebarFooter} ${collapsed ? styles.footerCollapsed : ''}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.userInfo,\n      children: [/*#__PURE__*/_jsxDEV(Link, {\n        to: \"/d/profile/\",\n        className: `${styles.avatarWrapper} ${isProfileActive ? styles.avatarActive : ''}`,\n        children: /*#__PURE__*/_jsxDEV(\"img\", {\n          alt: `${user.first_name} ${user.last_name}`,\n          src: user.avatar,\n          className: styles.avatarImage\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 206,\n        columnNumber: 9\n      }, this), !collapsed && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.userDetails,\n        children: /*#__PURE__*/_jsxDEV(Link, {\n          to: \"/d/profile/\",\n          className: styles.userName,\n          children: [user.first_name, \" \", user.last_name]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 215,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 214,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 7\n    }, this), !collapsed && /*#__PURE__*/_jsxDEV(\"button\", {\n      className: styles.logoutButton,\n      onClick: handleLogout,\n      title: \"Logout\",\n      children: /*#__PURE__*/_jsxDEV(Iconify, {\n        icon: \"quill:off\",\n        width: 24,\n        height: 24\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 227,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 222,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 204,\n    columnNumber: 5\n  }, this);\n};\n_s2(SidebarFooter, \"pkHmaVRPskBaU4tMJuJJpV42k1I=\", false, function () {\n  return [useLocation];\n});\n_c3 = SidebarFooter;\nexport default SidebarComponent;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"SidebarComponent\");\n$RefreshReg$(_c2, \"MenuItemLink\");\n$RefreshReg$(_c3, \"SidebarFooter\");", "map": {"version": 3, "names": ["React", "useEffect", "axios", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "useDispatch", "useSelector", "Sidebar", "<PERSON><PERSON>", "MenuItem", "Link", "useNavigate", "useLocation", "setUser", "clearUser", "Iconify", "APIURL", "getHeaders", "getRefreshToken", "getAccessToken", "setCookies", "removeCookies", "styles", "jsxDEV", "_jsxDEV", "SidebarComponent", "props", "_s", "logo", "onCollapseChange", "collapsed", "setCollapsed", "useState", "dispatch", "navigate", "location", "user", "state", "fetchAccountInfo", "replace", "response", "get", "headers", "status", "first_name", "data", "last_name", "avatar", "id", "handleUnauthorized", "error", "handleFetchError", "refreshToken", "refreshTokenFunc", "redirectToLogin", "console", "refreshResponse", "post", "refresh", "newAccessToken", "access", "handleLogout", "handleMouseEnter", "handleMouseLeave", "className", "customeNavbar", "expand", "onMouseEnter", "onMouseLeave", "style", "padding", "width", "children", "navbar<PERSON><PERSON>", "to", "alt", "imgAlt", "brandLogo", "src", "imgSrc", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "sidebarMain", "collapsedWidth", "sidebarMenu", "MenuItemLink", "active", "pathname", "includes", "iconName", "text", "SidebarFooter", "_c", "_ref", "component", "menuItem", "activeMenuItem", "icon", "height", "menuIcon", "_c2", "_ref2", "_s2", "isProfileActive", "sidebarFooter", "footerCollapsed", "userInfo", "avatar<PERSON><PERSON>per", "avatarActive", "avatarImage", "userDetails", "userName", "logoutButton", "onClick", "title", "_c3", "$RefreshReg$"], "sources": ["C:/ignition/ignition-ui/src/components/Sidebar/SidebarComponent.js"], "sourcesContent": ["/* eslint-disable react-hooks/exhaustive-deps */\r\nimport React, { useEffect } from 'react';\r\nimport axios from 'axios';\r\nimport { NavbarBrand, Navbar } from \"reactstrap\";\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { Sidebar, Menu, MenuItem } from 'react-pro-sidebar';\r\nimport { Link, useNavigate, useLocation } from 'react-router-dom';\r\nimport { setUser, clearUser } from '../../redux/userSlice';\r\nimport Iconify from 'components/Iconify/index';\r\nimport { APIURL } from \"helpers/constants\";\r\nimport { getHeaders, getRefreshToken, getAccessToken, setCookies, removeCookies } from \"helpers/functions\";\r\nimport styles from './styles.module.scss'\r\n\r\nconst SidebarComponent = (props) => {\r\n  const { logo, onCollapseChange } = props;\r\n  const [collapsed, setCollapsed] = React.useState(true);\r\n  const dispatch = useDispatch();\r\n  const navigate = useNavigate();\r\n  const location = useLocation();\r\n  const user = useSelector((state) => state.user);\r\n\r\n  useEffect(() => {\r\n    fetchAccountInfo();\r\n  }, []);\r\n\r\n  const fetchAccountInfo = async () => {\r\n    if (!getAccessToken() && !getRefreshToken()) {\r\n      navigate(\"/login\", { replace: true });\r\n      return;\r\n    }\r\n\r\n    try {\r\n      const response = await axios.get(`${APIURL}/api/user/view-profile`, { headers: getHeaders() });\r\n\r\n      if (response.status === 200) {\r\n        dispatch(setUser({\r\n          first_name: response.data.first_name,\r\n          last_name: response.data.last_name,\r\n          avatar: response.data.avatar,\r\n          id: response.data.id,\r\n        }));\r\n      } else {\r\n        handleUnauthorized(response.status, getRefreshToken());\r\n      }\r\n    } catch (error) {\r\n      handleFetchError(error, getRefreshToken());\r\n    }\r\n  };\r\n\r\n  const handleUnauthorized = async (status, refreshToken) => {\r\n    if (status === 401 && refreshToken) {\r\n      try {\r\n        await refreshTokenFunc(refreshToken);\r\n        await fetchAccountInfo();\r\n      } catch {\r\n        redirectToLogin();\r\n      }\r\n    } else {\r\n      redirectToLogin();\r\n    }\r\n  };\r\n\r\n  const handleFetchError = async (error, refreshToken) => {\r\n    if (refreshToken) {\r\n      try {\r\n        await refreshTokenFunc(refreshToken);\r\n        await fetchAccountInfo();\r\n      } catch {\r\n        redirectToLogin();\r\n      }\r\n    } else {\r\n      redirectToLogin();\r\n    }\r\n    console.error(error);\r\n  };\r\n\r\n  const refreshTokenFunc = async (refreshToken) => {\r\n    try {\r\n      const refreshResponse = await axios.post(`${APIURL}/api/user/token-refresh`, { refresh: refreshToken });\r\n      const newAccessToken = refreshResponse.data.access;\r\n      setCookies('accessToken', newAccessToken)\r\n      return newAccessToken;\r\n    } catch (error) {\r\n      console.error('Error refreshing token:', error);\r\n      removeCookies('accessToken');\r\n      removeCookies('refreshToken');\r\n      throw error;\r\n    }\r\n  };\r\n\r\n  const redirectToLogin = () => {\r\n    removeCookies('accessToken');\r\n    removeCookies('refreshToken');\r\n    navigate(\"/login\", { replace: true });\r\n  };\r\n\r\n  const handleLogout = () => {\r\n    removeCookies('accessToken');\r\n    removeCookies('refreshToken');\r\n    dispatch(clearUser());\r\n    navigate('/login', { replace: true });\r\n  };\r\n\r\n  const handleMouseEnter = () => {\r\n    setCollapsed(false);\r\n    onCollapseChange(false);\r\n  };\r\n\r\n  const handleMouseLeave = () => {\r\n    setCollapsed(true);\r\n    onCollapseChange(true);\r\n  };\r\n\r\n  return (\r\n    <Navbar\r\n      className={`navbar-vertical fixed-left ${collapsed ? \"collapsed\" : \"\"} ${styles.customeNavbar}`}\r\n      expand=\"md\"\r\n      id=\"sidenav-main\"\r\n      onMouseEnter={handleMouseEnter}\r\n      onMouseLeave={handleMouseLeave}\r\n      style={{ \r\n        padding: 0,\r\n        width: collapsed ? '70px' : '250px'\r\n      }}>\r\n      {logo && (\r\n        <NavbarBrand className={styles.navbarBrand}>\r\n          <Link to=\"/d/\">\r\n            <img\r\n              alt={logo.imgAlt}\r\n              className={styles.brandLogo}\r\n              src={logo.imgSrc}\r\n            />\r\n          </Link>\r\n        </NavbarBrand>\r\n      )}\r\n\r\n      <Sidebar\r\n        collapsed={collapsed}\r\n        className={styles.sidebarMain}\r\n        width=\"240px\"\r\n        collapsedWidth=\"80px\">\r\n        <Menu className={styles.sidebarMenu}>\r\n          <MenuItemLink\r\n            to=\"/d/\"\r\n            active={location.pathname === \"/d/\" || (location.pathname.includes('/d/plan/') && location.pathname !== \"/d/plan/create\")}\r\n            iconName=\"mage:dashboard-check\"\r\n            text=\"My Plans\"\r\n          />\r\n          <MenuItemLink\r\n            to=\"/d/plan/create\"\r\n            active={location.pathname === \"/d/plan/create\"}\r\n            iconName=\"icons8:idea\"\r\n            text=\"New Idea\"\r\n          />\r\n          <MenuItemLink\r\n            to=\"/d/my/tasks/calendar\"\r\n            active={location.pathname === \"/d/my/tasks/calendar\"}\r\n            iconName=\"ion:calendar-sharp\"\r\n            text=\"Tasks Schedule\"\r\n          />\r\n          <MenuItemLink\r\n            to=\"/d/connect\"\r\n            active={location.pathname === \"/d/connect\" || (location.pathname.includes('/d/other/'))}\r\n            iconName=\"fluent:person-28-filled\"\r\n            text=\"Connect with Friends\"\r\n          />\r\n          <MenuItemLink\r\n            to=\"/d/notifications\"\r\n            active={location.pathname === \"/d/notifications\"}\r\n            iconName=\"ion:notifications-sharp\"\r\n            text=\"Notifications\"\r\n          />\r\n        </Menu>\r\n      </Sidebar>\r\n\r\n      <SidebarFooter user={user} collapsed={collapsed} handleLogout={handleLogout} />\r\n    </Navbar>\r\n  );\r\n};\r\n\r\nconst MenuItemLink = ({ to, active, iconName, text }) => (\r\n  <MenuItem\r\n    active={active}\r\n    component={<Link to={to} />}\r\n    className={`${styles.menuItem} ${active ? styles.activeMenuItem : ''}`}\r\n    icon={\r\n      <Iconify \r\n        icon={iconName} \r\n        width={24} \r\n        height={24}\r\n        className={styles.menuIcon} \r\n      />\r\n    }\r\n  >\r\n    {text}\r\n  </MenuItem>\r\n);\r\n\r\nconst SidebarFooter = ({ user, collapsed, handleLogout }) => {\r\n  const location = useLocation();\r\n  const isProfileActive = location.pathname === '/d/profile/';\r\n\r\n  return (\r\n    <div className={`${styles.sidebarFooter} ${collapsed ? styles.footerCollapsed : ''}`}>\r\n      <div className={styles.userInfo}>\r\n        <Link to=\"/d/profile/\" className={`${styles.avatarWrapper} ${isProfileActive ? styles.avatarActive : ''}`}>\r\n          <img \r\n            alt={`${user.first_name} ${user.last_name}`} \r\n            src={user.avatar} \r\n            className={styles.avatarImage}\r\n          />\r\n        </Link>\r\n        {!collapsed && (\r\n          <div className={styles.userDetails}>\r\n            <Link to=\"/d/profile/\" className={styles.userName}>\r\n              {user.first_name} {user.last_name}\r\n            </Link>\r\n          </div>\r\n        )}\r\n      </div>\r\n      {!collapsed && (\r\n        <button \r\n          className={styles.logoutButton} \r\n          onClick={handleLogout}\r\n          title=\"Logout\"\r\n        >\r\n          <Iconify icon=\"quill:off\" width={24} height={24} />\r\n        </button>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default SidebarComponent;\r\n"], "mappings": ";;;AAAA;AACA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,EAAEC,MAAM,QAAQ,YAAY;AAChD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AACtD,SAASC,OAAO,EAAEC,IAAI,EAAEC,QAAQ,QAAQ,mBAAmB;AAC3D,SAASC,IAAI,EAAEC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AACjE,SAASC,OAAO,EAAEC,SAAS,QAAQ,uBAAuB;AAC1D,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,SAASC,MAAM,QAAQ,mBAAmB;AAC1C,SAASC,UAAU,EAAEC,eAAe,EAAEC,cAAc,EAAEC,UAAU,EAAEC,aAAa,QAAQ,mBAAmB;AAC1G,OAAOC,MAAM,MAAM,sBAAsB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEzC,MAAMC,gBAAgB,GAAIC,KAAK,IAAK;EAAAC,EAAA;EAClC,MAAM;IAAEC,IAAI;IAAEC;EAAiB,CAAC,GAAGH,KAAK;EACxC,MAAM,CAACI,SAAS,EAAEC,YAAY,CAAC,GAAG/B,KAAK,CAACgC,QAAQ,CAAC,IAAI,CAAC;EACtD,MAAMC,QAAQ,GAAG5B,WAAW,CAAC,CAAC;EAC9B,MAAM6B,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAMwB,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAMwB,IAAI,GAAG9B,WAAW,CAAE+B,KAAK,IAAKA,KAAK,CAACD,IAAI,CAAC;EAE/CnC,SAAS,CAAC,MAAM;IACdqC,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI,CAACnB,cAAc,CAAC,CAAC,IAAI,CAACD,eAAe,CAAC,CAAC,EAAE;MAC3CgB,QAAQ,CAAC,QAAQ,EAAE;QAAEK,OAAO,EAAE;MAAK,CAAC,CAAC;MACrC;IACF;IAEA,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMtC,KAAK,CAACuC,GAAG,CAAC,GAAGzB,MAAM,wBAAwB,EAAE;QAAE0B,OAAO,EAAEzB,UAAU,CAAC;MAAE,CAAC,CAAC;MAE9F,IAAIuB,QAAQ,CAACG,MAAM,KAAK,GAAG,EAAE;QAC3BV,QAAQ,CAACpB,OAAO,CAAC;UACf+B,UAAU,EAAEJ,QAAQ,CAACK,IAAI,CAACD,UAAU;UACpCE,SAAS,EAAEN,QAAQ,CAACK,IAAI,CAACC,SAAS;UAClCC,MAAM,EAAEP,QAAQ,CAACK,IAAI,CAACE,MAAM;UAC5BC,EAAE,EAAER,QAAQ,CAACK,IAAI,CAACG;QACpB,CAAC,CAAC,CAAC;MACL,CAAC,MAAM;QACLC,kBAAkB,CAACT,QAAQ,CAACG,MAAM,EAAEzB,eAAe,CAAC,CAAC,CAAC;MACxD;IACF,CAAC,CAAC,OAAOgC,KAAK,EAAE;MACdC,gBAAgB,CAACD,KAAK,EAAEhC,eAAe,CAAC,CAAC,CAAC;IAC5C;EACF,CAAC;EAED,MAAM+B,kBAAkB,GAAG,MAAAA,CAAON,MAAM,EAAES,YAAY,KAAK;IACzD,IAAIT,MAAM,KAAK,GAAG,IAAIS,YAAY,EAAE;MAClC,IAAI;QACF,MAAMC,gBAAgB,CAACD,YAAY,CAAC;QACpC,MAAMd,gBAAgB,CAAC,CAAC;MAC1B,CAAC,CAAC,MAAM;QACNgB,eAAe,CAAC,CAAC;MACnB;IACF,CAAC,MAAM;MACLA,eAAe,CAAC,CAAC;IACnB;EACF,CAAC;EAED,MAAMH,gBAAgB,GAAG,MAAAA,CAAOD,KAAK,EAAEE,YAAY,KAAK;IACtD,IAAIA,YAAY,EAAE;MAChB,IAAI;QACF,MAAMC,gBAAgB,CAACD,YAAY,CAAC;QACpC,MAAMd,gBAAgB,CAAC,CAAC;MAC1B,CAAC,CAAC,MAAM;QACNgB,eAAe,CAAC,CAAC;MACnB;IACF,CAAC,MAAM;MACLA,eAAe,CAAC,CAAC;IACnB;IACAC,OAAO,CAACL,KAAK,CAACA,KAAK,CAAC;EACtB,CAAC;EAED,MAAMG,gBAAgB,GAAG,MAAOD,YAAY,IAAK;IAC/C,IAAI;MACF,MAAMI,eAAe,GAAG,MAAMtD,KAAK,CAACuD,IAAI,CAAC,GAAGzC,MAAM,yBAAyB,EAAE;QAAE0C,OAAO,EAAEN;MAAa,CAAC,CAAC;MACvG,MAAMO,cAAc,GAAGH,eAAe,CAACX,IAAI,CAACe,MAAM;MAClDxC,UAAU,CAAC,aAAa,EAAEuC,cAAc,CAAC;MACzC,OAAOA,cAAc;IACvB,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C7B,aAAa,CAAC,aAAa,CAAC;MAC5BA,aAAa,CAAC,cAAc,CAAC;MAC7B,MAAM6B,KAAK;IACb;EACF,CAAC;EAED,MAAMI,eAAe,GAAGA,CAAA,KAAM;IAC5BjC,aAAa,CAAC,aAAa,CAAC;IAC5BA,aAAa,CAAC,cAAc,CAAC;IAC7Ba,QAAQ,CAAC,QAAQ,EAAE;MAAEK,OAAO,EAAE;IAAK,CAAC,CAAC;EACvC,CAAC;EAED,MAAMsB,YAAY,GAAGA,CAAA,KAAM;IACzBxC,aAAa,CAAC,aAAa,CAAC;IAC5BA,aAAa,CAAC,cAAc,CAAC;IAC7BY,QAAQ,CAACnB,SAAS,CAAC,CAAC,CAAC;IACrBoB,QAAQ,CAAC,QAAQ,EAAE;MAAEK,OAAO,EAAE;IAAK,CAAC,CAAC;EACvC,CAAC;EAED,MAAMuB,gBAAgB,GAAGA,CAAA,KAAM;IAC7B/B,YAAY,CAAC,KAAK,CAAC;IACnBF,gBAAgB,CAAC,KAAK,CAAC;EACzB,CAAC;EAED,MAAMkC,gBAAgB,GAAGA,CAAA,KAAM;IAC7BhC,YAAY,CAAC,IAAI,CAAC;IAClBF,gBAAgB,CAAC,IAAI,CAAC;EACxB,CAAC;EAED,oBACEL,OAAA,CAACpB,MAAM;IACL4D,SAAS,EAAE,8BAA8BlC,SAAS,GAAG,WAAW,GAAG,EAAE,IAAIR,MAAM,CAAC2C,aAAa,EAAG;IAChGC,MAAM,EAAC,IAAI;IACXlB,EAAE,EAAC,cAAc;IACjBmB,YAAY,EAAEL,gBAAiB;IAC/BM,YAAY,EAAEL,gBAAiB;IAC/BM,KAAK,EAAE;MACLC,OAAO,EAAE,CAAC;MACVC,KAAK,EAAEzC,SAAS,GAAG,MAAM,GAAG;IAC9B,CAAE;IAAA0C,QAAA,GACD5C,IAAI,iBACHJ,OAAA,CAACrB,WAAW;MAAC6D,SAAS,EAAE1C,MAAM,CAACmD,WAAY;MAAAD,QAAA,eACzChD,OAAA,CAACd,IAAI;QAACgE,EAAE,EAAC,KAAK;QAAAF,QAAA,eACZhD,OAAA;UACEmD,GAAG,EAAE/C,IAAI,CAACgD,MAAO;UACjBZ,SAAS,EAAE1C,MAAM,CAACuD,SAAU;UAC5BC,GAAG,EAAElD,IAAI,CAACmD;QAAO;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACd,eAED3D,OAAA,CAACjB,OAAO;MACNuB,SAAS,EAAEA,SAAU;MACrBkC,SAAS,EAAE1C,MAAM,CAAC8D,WAAY;MAC9Bb,KAAK,EAAC,OAAO;MACbc,cAAc,EAAC,MAAM;MAAAb,QAAA,eACrBhD,OAAA,CAAChB,IAAI;QAACwD,SAAS,EAAE1C,MAAM,CAACgE,WAAY;QAAAd,QAAA,gBAClChD,OAAA,CAAC+D,YAAY;UACXb,EAAE,EAAC,KAAK;UACRc,MAAM,EAAErD,QAAQ,CAACsD,QAAQ,KAAK,KAAK,IAAKtD,QAAQ,CAACsD,QAAQ,CAACC,QAAQ,CAAC,UAAU,CAAC,IAAIvD,QAAQ,CAACsD,QAAQ,KAAK,gBAAkB;UAC1HE,QAAQ,EAAC,sBAAsB;UAC/BC,IAAI,EAAC;QAAU;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eACF3D,OAAA,CAAC+D,YAAY;UACXb,EAAE,EAAC,gBAAgB;UACnBc,MAAM,EAAErD,QAAQ,CAACsD,QAAQ,KAAK,gBAAiB;UAC/CE,QAAQ,EAAC,aAAa;UACtBC,IAAI,EAAC;QAAU;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eACF3D,OAAA,CAAC+D,YAAY;UACXb,EAAE,EAAC,sBAAsB;UACzBc,MAAM,EAAErD,QAAQ,CAACsD,QAAQ,KAAK,sBAAuB;UACrDE,QAAQ,EAAC,oBAAoB;UAC7BC,IAAI,EAAC;QAAgB;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eACF3D,OAAA,CAAC+D,YAAY;UACXb,EAAE,EAAC,YAAY;UACfc,MAAM,EAAErD,QAAQ,CAACsD,QAAQ,KAAK,YAAY,IAAKtD,QAAQ,CAACsD,QAAQ,CAACC,QAAQ,CAAC,WAAW,CAAG;UACxFC,QAAQ,EAAC,yBAAyB;UAClCC,IAAI,EAAC;QAAsB;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CAAC,eACF3D,OAAA,CAAC+D,YAAY;UACXb,EAAE,EAAC,kBAAkB;UACrBc,MAAM,EAAErD,QAAQ,CAACsD,QAAQ,KAAK,kBAAmB;UACjDE,QAAQ,EAAC,yBAAyB;UAClCC,IAAI,EAAC;QAAe;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAEV3D,OAAA,CAACqE,aAAa;MAACzD,IAAI,EAAEA,IAAK;MAACN,SAAS,EAAEA,SAAU;MAAC+B,YAAY,EAAEA;IAAa;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACzE,CAAC;AAEb,CAAC;AAACxD,EAAA,CArKIF,gBAAgB;EAAA,QAGHpB,WAAW,EACXM,WAAW,EACXC,WAAW,EACfN,WAAW;AAAA;AAAAwF,EAAA,GANpBrE,gBAAgB;AAuKtB,MAAM8D,YAAY,GAAGQ,IAAA;EAAA,IAAC;IAAErB,EAAE;IAAEc,MAAM;IAAEG,QAAQ;IAAEC;EAAK,CAAC,GAAAG,IAAA;EAAA,oBAClDvE,OAAA,CAACf,QAAQ;IACP+E,MAAM,EAAEA,MAAO;IACfQ,SAAS,eAAExE,OAAA,CAACd,IAAI;MAACgE,EAAE,EAAEA;IAAG;MAAAM,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAE;IAC5BnB,SAAS,EAAE,GAAG1C,MAAM,CAAC2E,QAAQ,IAAIT,MAAM,GAAGlE,MAAM,CAAC4E,cAAc,GAAG,EAAE,EAAG;IACvEC,IAAI,eACF3E,OAAA,CAACT,OAAO;MACNoF,IAAI,EAAER,QAAS;MACfpB,KAAK,EAAE,EAAG;MACV6B,MAAM,EAAE,EAAG;MACXpC,SAAS,EAAE1C,MAAM,CAAC+E;IAAS;MAAArB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CACF;IAAAX,QAAA,EAEAoB;EAAI;IAAAZ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAAA,CACZ;AAACmB,GAAA,GAhBIf,YAAY;AAkBlB,MAAMM,aAAa,GAAGU,KAAA,IAAuC;EAAAC,GAAA;EAAA,IAAtC;IAAEpE,IAAI;IAAEN,SAAS;IAAE+B;EAAa,CAAC,GAAA0C,KAAA;EACtD,MAAMpE,QAAQ,GAAGvB,WAAW,CAAC,CAAC;EAC9B,MAAM6F,eAAe,GAAGtE,QAAQ,CAACsD,QAAQ,KAAK,aAAa;EAE3D,oBACEjE,OAAA;IAAKwC,SAAS,EAAE,GAAG1C,MAAM,CAACoF,aAAa,IAAI5E,SAAS,GAAGR,MAAM,CAACqF,eAAe,GAAG,EAAE,EAAG;IAAAnC,QAAA,gBACnFhD,OAAA;MAAKwC,SAAS,EAAE1C,MAAM,CAACsF,QAAS;MAAApC,QAAA,gBAC9BhD,OAAA,CAACd,IAAI;QAACgE,EAAE,EAAC,aAAa;QAACV,SAAS,EAAE,GAAG1C,MAAM,CAACuF,aAAa,IAAIJ,eAAe,GAAGnF,MAAM,CAACwF,YAAY,GAAG,EAAE,EAAG;QAAAtC,QAAA,eACxGhD,OAAA;UACEmD,GAAG,EAAE,GAAGvC,IAAI,CAACQ,UAAU,IAAIR,IAAI,CAACU,SAAS,EAAG;UAC5CgC,GAAG,EAAE1C,IAAI,CAACW,MAAO;UACjBiB,SAAS,EAAE1C,MAAM,CAACyF;QAAY;UAAA/B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EACN,CAACrD,SAAS,iBACTN,OAAA;QAAKwC,SAAS,EAAE1C,MAAM,CAAC0F,WAAY;QAAAxC,QAAA,eACjChD,OAAA,CAACd,IAAI;UAACgE,EAAE,EAAC,aAAa;UAACV,SAAS,EAAE1C,MAAM,CAAC2F,QAAS;UAAAzC,QAAA,GAC/CpC,IAAI,CAACQ,UAAU,EAAC,GAAC,EAACR,IAAI,CAACU,SAAS;QAAA;UAAAkC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,EACL,CAACrD,SAAS,iBACTN,OAAA;MACEwC,SAAS,EAAE1C,MAAM,CAAC4F,YAAa;MAC/BC,OAAO,EAAEtD,YAAa;MACtBuD,KAAK,EAAC,QAAQ;MAAA5C,QAAA,eAEdhD,OAAA,CAACT,OAAO;QAACoF,IAAI,EAAC,WAAW;QAAC5B,KAAK,EAAE,EAAG;QAAC6B,MAAM,EAAE;MAAG;QAAApB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CACT;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACqB,GAAA,CAjCIX,aAAa;EAAA,QACAjF,WAAW;AAAA;AAAAyG,GAAA,GADxBxB,aAAa;AAmCnB,eAAepE,gBAAgB;AAAC,IAAAqE,EAAA,EAAAQ,GAAA,EAAAe,GAAA;AAAAC,YAAA,CAAAxB,EAAA;AAAAwB,YAAA,CAAAhB,GAAA;AAAAgB,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}