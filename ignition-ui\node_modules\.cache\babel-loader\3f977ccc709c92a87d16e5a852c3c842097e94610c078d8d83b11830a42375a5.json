{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getAvatarGroupUtilityClass(slot) {\n  return generateUtilityClass('MuiAvatarGroup', slot);\n}\nconst avatarGroupClasses = generateUtilityClasses('MuiAvatarGroup', ['root']);\nexport default avatarGroupClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getAvatarGroupUtilityClass", "slot", "avatarGroupClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/AvatarGroup/avatarGroupClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getAvatarGroupUtilityClass(slot) {\n  return generateUtilityClass('MuiAvatarGroup', slot);\n}\nconst avatarGroupClasses = generateUtilityClasses('MuiAvatarGroup', ['root']);\nexport default avatarGroupClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,0BAA0BA,CAACC,IAAI,EAAE;EAC/C,OAAOH,oBAAoB,CAAC,gBAAgB,EAAEG,IAAI,CAAC;AACrD;AACA,MAAMC,kBAAkB,GAAGH,sBAAsB,CAAC,gBAAgB,EAAE,CAAC,MAAM,CAAC,CAAC;AAC7E,eAAeG,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}