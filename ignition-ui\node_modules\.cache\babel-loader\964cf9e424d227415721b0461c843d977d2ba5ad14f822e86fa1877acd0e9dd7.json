{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"component\", \"children\", \"orientation\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport { useThemeProps } from '../styles';\nimport styled from '../styles/styled';\nimport { getDialogContentUtilityClass } from './dialogContentClasses';\nimport useSlot from '../utils/useSlot';\nimport ModalDialogVariantColorContext from '../ModalDialog/ModalDialogVariantColorContext';\nimport { StyledCardContentRoot } from '../CardContent/CardContent';\nimport modalDialogClasses from '../ModalDialog/modalDialogClasses';\nimport dialogTitleClasses from '../DialogTitle/dialogTitleClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = () => {\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getDialogContentUtilityClass, {});\n};\nconst DialogContentRoot = styled(StyledCardContentRoot, {\n  name: 'JoyDialogContent',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    color: `var(--DialogContent-color, ${theme.vars.palette.text.tertiary})`,\n    overflow: 'auto',\n    margin: 'var(--unstable_DialogContent-margin)',\n    [`.${modalDialogClasses.root} > .${dialogTitleClasses.root} + &`]: {\n      '--unstable_DialogContent-margin': '-0.375em 0 0 0'\n    }\n  };\n});\n/**\n *\n * Demos:\n *\n * - [Drawer](https://mui.com/joy-ui/react-drawer/)\n * - [Modal](https://mui.com/joy-ui/react-modal/)\n *\n * API:\n *\n * - [DialogContent API](https://mui.com/joy-ui/api/dialog-content/)\n */\nconst DialogContent = /*#__PURE__*/React.forwardRef(function DialogContent(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyDialogContent'\n  });\n  const context = React.useContext(ModalDialogVariantColorContext);\n  const {\n      component = 'div',\n      children,\n      orientation = 'vertical',\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const ownerState = _extends({}, props, {\n    component,\n    orientation\n  });\n  const classes = useUtilityClasses();\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    className: classes.root,\n    elementType: DialogContentRoot,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      id: context == null ? void 0 : context.describedBy\n    }\n  });\n  return /*#__PURE__*/_jsx(SlotRoot, _extends({}, rootProps, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? DialogContent.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Used to render icon or text elements inside the CardContent if `src` is not set.\n   * This can be an element, or just a string.\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The component orientation.\n   * @default 'vertical'\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default DialogContent;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "unstable_composeClasses", "composeClasses", "useThemeProps", "styled", "getDialogContentUtilityClass", "useSlot", "ModalDialogVariantColorContext", "StyledCardContentRoot", "modalDialogClasses", "dialogTitleClasses", "jsx", "_jsx", "useUtilityClasses", "slots", "root", "DialogContentRoot", "name", "slot", "overridesResolver", "props", "styles", "_ref", "theme", "color", "vars", "palette", "text", "tertiary", "overflow", "margin", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "forwardRef", "inProps", "ref", "context", "useContext", "component", "children", "orientation", "slotProps", "other", "externalForwardedProps", "ownerState", "classes", "SlotRoot", "rootProps", "className", "elementType", "additionalProps", "id", "describedBy", "process", "env", "NODE_ENV", "propTypes", "node", "oneOf", "shape", "oneOfType", "func", "object", "sx", "arrayOf", "bool"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/DialogContent/DialogContent.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"component\", \"children\", \"orientation\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport { useThemeProps } from '../styles';\nimport styled from '../styles/styled';\nimport { getDialogContentUtilityClass } from './dialogContentClasses';\nimport useSlot from '../utils/useSlot';\nimport ModalDialogVariantColorContext from '../ModalDialog/ModalDialogVariantColorContext';\nimport { StyledCardContentRoot } from '../CardContent/CardContent';\nimport modalDialogClasses from '../ModalDialog/modalDialogClasses';\nimport dialogTitleClasses from '../DialogTitle/dialogTitleClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = () => {\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getDialogContentUtilityClass, {});\n};\nconst DialogContentRoot = styled(StyledCardContentRoot, {\n  name: 'JoyDialogContent',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(({\n  theme\n}) => ({\n  color: `var(--DialogContent-color, ${theme.vars.palette.text.tertiary})`,\n  overflow: 'auto',\n  margin: 'var(--unstable_DialogContent-margin)',\n  [`.${modalDialogClasses.root} > .${dialogTitleClasses.root} + &`]: {\n    '--unstable_DialogContent-margin': '-0.375em 0 0 0'\n  }\n}));\n/**\n *\n * Demos:\n *\n * - [Drawer](https://mui.com/joy-ui/react-drawer/)\n * - [Modal](https://mui.com/joy-ui/react-modal/)\n *\n * API:\n *\n * - [DialogContent API](https://mui.com/joy-ui/api/dialog-content/)\n */\nconst DialogContent = /*#__PURE__*/React.forwardRef(function DialogContent(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyDialogContent'\n  });\n  const context = React.useContext(ModalDialogVariantColorContext);\n  const {\n      component = 'div',\n      children,\n      orientation = 'vertical',\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const ownerState = _extends({}, props, {\n    component,\n    orientation\n  });\n  const classes = useUtilityClasses();\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    className: classes.root,\n    elementType: DialogContentRoot,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      id: context == null ? void 0 : context.describedBy\n    }\n  });\n  return /*#__PURE__*/_jsx(SlotRoot, _extends({}, rootProps, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? DialogContent.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Used to render icon or text elements inside the CardContent if `src` is not set.\n   * This can be an element, or just a string.\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The component orientation.\n   * @default 'vertical'\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default DialogContent;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,OAAO,EAAE,WAAW,CAAC;AAChF,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,WAAW;AACrE,SAASC,aAAa,QAAQ,WAAW;AACzC,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,4BAA4B,QAAQ,wBAAwB;AACrE,OAAOC,OAAO,MAAM,kBAAkB;AACtC,OAAOC,8BAA8B,MAAM,+CAA+C;AAC1F,SAASC,qBAAqB,QAAQ,4BAA4B;AAClE,OAAOC,kBAAkB,MAAM,mCAAmC;AAClE,OAAOC,kBAAkB,MAAM,mCAAmC;AAClE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAC9B,MAAMC,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,OAAOb,cAAc,CAACY,KAAK,EAAET,4BAA4B,EAAE,CAAC,CAAC,CAAC;AAChE,CAAC;AACD,MAAMW,iBAAiB,GAAGZ,MAAM,CAACI,qBAAqB,EAAE;EACtDS,IAAI,EAAE,kBAAkB;EACxBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC/C,CAAC,CAAC,CAACO,IAAA;EAAA,IAAC;IACFC;EACF,CAAC,GAAAD,IAAA;EAAA,OAAM;IACLE,KAAK,EAAE,8BAA8BD,KAAK,CAACE,IAAI,CAACC,OAAO,CAACC,IAAI,CAACC,QAAQ,GAAG;IACxEC,QAAQ,EAAE,MAAM;IAChBC,MAAM,EAAE,sCAAsC;IAC9C,CAAC,IAAIrB,kBAAkB,CAACM,IAAI,OAAOL,kBAAkB,CAACK,IAAI,MAAM,GAAG;MACjE,iCAAiC,EAAE;IACrC;EACF,CAAC;AAAA,CAAC,CAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgB,aAAa,GAAG,aAAahC,KAAK,CAACiC,UAAU,CAAC,SAASD,aAAaA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACvF,MAAMd,KAAK,GAAGjB,aAAa,CAAC;IAC1BiB,KAAK,EAAEa,OAAO;IACdhB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAMkB,OAAO,GAAGpC,KAAK,CAACqC,UAAU,CAAC7B,8BAA8B,CAAC;EAChE,MAAM;MACF8B,SAAS,GAAG,KAAK;MACjBC,QAAQ;MACRC,WAAW,GAAG,UAAU;MACxBzB,KAAK,GAAG,CAAC,CAAC;MACV0B,SAAS,GAAG,CAAC;IACf,CAAC,GAAGpB,KAAK;IACTqB,KAAK,GAAG5C,6BAA6B,CAACuB,KAAK,EAAEtB,SAAS,CAAC;EACzD,MAAM4C,sBAAsB,GAAG9C,QAAQ,CAAC,CAAC,CAAC,EAAE6C,KAAK,EAAE;IACjDJ,SAAS;IACTvB,KAAK;IACL0B;EACF,CAAC,CAAC;EACF,MAAMG,UAAU,GAAG/C,QAAQ,CAAC,CAAC,CAAC,EAAEwB,KAAK,EAAE;IACrCiB,SAAS;IACTE;EACF,CAAC,CAAC;EACF,MAAMK,OAAO,GAAG/B,iBAAiB,CAAC,CAAC;EACnC,MAAM,CAACgC,QAAQ,EAAEC,SAAS,CAAC,GAAGxC,OAAO,CAAC,MAAM,EAAE;IAC5C4B,GAAG;IACHa,SAAS,EAAEH,OAAO,CAAC7B,IAAI;IACvBiC,WAAW,EAAEhC,iBAAiB;IAC9B0B,sBAAsB;IACtBC,UAAU;IACVM,eAAe,EAAE;MACfC,EAAE,EAAEf,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACgB;IACzC;EACF,CAAC,CAAC;EACF,OAAO,aAAavC,IAAI,CAACiC,QAAQ,EAAEjD,QAAQ,CAAC,CAAC,CAAC,EAAEkD,SAAS,EAAE;IACzDR,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFc,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGvB,aAAa,CAACwB,SAAS,CAAC,yBAAyB;EACvF;EACA;EACA;EACA;EACA;AACF;AACA;AACA;EACEjB,QAAQ,EAAEtC,SAAS,CAACwD,IAAI;EACxB;AACF;AACA;AACA;EACEnB,SAAS,EAAErC,SAAS,CAACgD,WAAW;EAChC;AACF;AACA;AACA;EACET,WAAW,EAAEvC,SAAS,CAACyD,KAAK,CAAC,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;EACxD;AACF;AACA;AACA;EACEjB,SAAS,EAAExC,SAAS,CAAC0D,KAAK,CAAC;IACzB3C,IAAI,EAAEf,SAAS,CAAC2D,SAAS,CAAC,CAAC3D,SAAS,CAAC4D,IAAI,EAAE5D,SAAS,CAAC6D,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;EACE/C,KAAK,EAAEd,SAAS,CAAC0D,KAAK,CAAC;IACrB3C,IAAI,EAAEf,SAAS,CAACgD;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACEc,EAAE,EAAE9D,SAAS,CAAC2D,SAAS,CAAC,CAAC3D,SAAS,CAAC+D,OAAO,CAAC/D,SAAS,CAAC2D,SAAS,CAAC,CAAC3D,SAAS,CAAC4D,IAAI,EAAE5D,SAAS,CAAC6D,MAAM,EAAE7D,SAAS,CAACgE,IAAI,CAAC,CAAC,CAAC,EAAEhE,SAAS,CAAC4D,IAAI,EAAE5D,SAAS,CAAC6D,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAe9B,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}