{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getDialogActionsUtilityClass(slot) {\n  return generateUtilityClass('MuiDialogActions', slot);\n}\nconst dialogActionsClasses = generateUtilityClasses('MuiDialogActions', ['root']);\nexport default dialogActionsClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getDialogActionsUtilityClass", "slot", "dialogActionsClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/DialogActions/dialogActionsClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getDialogActionsUtilityClass(slot) {\n  return generateUtilityClass('MuiDialogActions', slot);\n}\nconst dialogActionsClasses = generateUtilityClasses('MuiDialogActions', ['root']);\nexport default dialogActionsClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,4BAA4BA,CAACC,IAAI,EAAE;EACjD,OAAOH,oBAAoB,CAAC,kBAAkB,EAAEG,IAAI,CAAC;AACvD;AACA,MAAMC,oBAAoB,GAAGH,sBAAsB,CAAC,kBAAkB,EAAE,CAAC,MAAM,CAAC,CAAC;AACjF,eAAeG,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}