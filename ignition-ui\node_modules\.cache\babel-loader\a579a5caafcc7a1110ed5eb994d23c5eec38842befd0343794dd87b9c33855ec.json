{"ast": null, "code": "'use strict';\n\nvar implementation = require('./implementation');\nvar supportsDescriptors = require('define-properties').supportsDescriptors;\nvar $gOPD = Object.getOwnPropertyDescriptor;\nmodule.exports = function getPolyfill() {\n  if (supportsDescriptors && /a/mig.flags === 'gim') {\n    var descriptor = $gOPD(RegExp.prototype, 'flags');\n    if (descriptor && typeof descriptor.get === 'function' && 'dotAll' in RegExp.prototype && 'hasIndices' in RegExp.prototype) {\n      /* eslint getter-return: 0 */\n      var calls = '';\n      var o = {};\n      Object.defineProperty(o, 'hasIndices', {\n        get: function () {\n          calls += 'd';\n        }\n      });\n      Object.defineProperty(o, 'sticky', {\n        get: function () {\n          calls += 'y';\n        }\n      });\n      descriptor.get.call(o);\n      if (calls === 'dy') {\n        return descriptor.get;\n      }\n    }\n  }\n  return implementation;\n};", "map": {"version": 3, "names": ["implementation", "require", "supportsDescriptors", "$gOPD", "Object", "getOwnPropertyDescriptor", "module", "exports", "getPolyfill", "flags", "descriptor", "RegExp", "prototype", "get", "calls", "o", "defineProperty", "call"], "sources": ["C:/ignition/ignition-ui/node_modules/regexp.prototype.flags/polyfill.js"], "sourcesContent": ["'use strict';\n\nvar implementation = require('./implementation');\n\nvar supportsDescriptors = require('define-properties').supportsDescriptors;\nvar $gOPD = Object.getOwnPropertyDescriptor;\n\nmodule.exports = function getPolyfill() {\n\tif (supportsDescriptors && (/a/mig).flags === 'gim') {\n\t\tvar descriptor = $gOPD(RegExp.prototype, 'flags');\n\t\tif (\n\t\t\tdescriptor\n\t\t\t&& typeof descriptor.get === 'function'\n\t\t\t&& 'dotAll' in RegExp.prototype\n\t\t\t&& 'hasIndices' in RegExp.prototype\n\t\t) {\n\t\t\t/* eslint getter-return: 0 */\n\t\t\tvar calls = '';\n\t\t\tvar o = {};\n\t\t\tObject.defineProperty(o, 'hasIndices', {\n\t\t\t\tget: function () {\n\t\t\t\t\tcalls += 'd';\n\t\t\t\t}\n\t\t\t});\n\t\t\tObject.defineProperty(o, 'sticky', {\n\t\t\t\tget: function () {\n\t\t\t\t\tcalls += 'y';\n\t\t\t\t}\n\t\t\t});\n\n\t\t\tdescriptor.get.call(o);\n\n\t\t\tif (calls === 'dy') {\n\t\t\t\treturn descriptor.get;\n\t\t\t}\n\t\t}\n\t}\n\treturn implementation;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,cAAc,GAAGC,OAAO,CAAC,kBAAkB,CAAC;AAEhD,IAAIC,mBAAmB,GAAGD,OAAO,CAAC,mBAAmB,CAAC,CAACC,mBAAmB;AAC1E,IAAIC,KAAK,GAAGC,MAAM,CAACC,wBAAwB;AAE3CC,MAAM,CAACC,OAAO,GAAG,SAASC,WAAWA,CAAA,EAAG;EACvC,IAAIN,mBAAmB,IAAK,MAAM,CAAEO,KAAK,KAAK,KAAK,EAAE;IACpD,IAAIC,UAAU,GAAGP,KAAK,CAACQ,MAAM,CAACC,SAAS,EAAE,OAAO,CAAC;IACjD,IACCF,UAAU,IACP,OAAOA,UAAU,CAACG,GAAG,KAAK,UAAU,IACpC,QAAQ,IAAIF,MAAM,CAACC,SAAS,IAC5B,YAAY,IAAID,MAAM,CAACC,SAAS,EAClC;MACD;MACA,IAAIE,KAAK,GAAG,EAAE;MACd,IAAIC,CAAC,GAAG,CAAC,CAAC;MACVX,MAAM,CAACY,cAAc,CAACD,CAAC,EAAE,YAAY,EAAE;QACtCF,GAAG,EAAE,SAAAA,CAAA,EAAY;UAChBC,KAAK,IAAI,GAAG;QACb;MACD,CAAC,CAAC;MACFV,MAAM,CAACY,cAAc,CAACD,CAAC,EAAE,QAAQ,EAAE;QAClCF,GAAG,EAAE,SAAAA,CAAA,EAAY;UAChBC,KAAK,IAAI,GAAG;QACb;MACD,CAAC,CAAC;MAEFJ,UAAU,CAACG,GAAG,CAACI,IAAI,CAACF,CAAC,CAAC;MAEtB,IAAID,KAAK,KAAK,IAAI,EAAE;QACnB,OAAOJ,UAAU,CAACG,GAAG;MACtB;IACD;EACD;EACA,OAAOb,cAAc;AACtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}