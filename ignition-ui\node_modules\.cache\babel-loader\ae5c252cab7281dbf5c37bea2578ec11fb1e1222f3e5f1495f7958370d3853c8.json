{"ast": null, "code": "var objectKeys = require('object-keys');\nvar isArguments = require('is-arguments');\nvar is = require('object-is');\nvar isRegex = require('is-regex');\nvar flags = require('regexp.prototype.flags');\nvar isDate = require('is-date-object');\nvar getTime = Date.prototype.getTime;\nfunction deepEqual(actual, expected, options) {\n  var opts = options || {};\n\n  // 7.1. All identical values are equivalent, as determined by ===.\n  if (opts.strict ? is(actual, expected) : actual === expected) {\n    return true;\n  }\n\n  // 7.3. Other pairs that do not both pass typeof value == 'object', equivalence is determined by ==.\n  if (!actual || !expected || typeof actual !== 'object' && typeof expected !== 'object') {\n    return opts.strict ? is(actual, expected) : actual == expected;\n  }\n\n  /*\n   * 7.4. For all other Object pairs, including Array objects, equivalence is\n   * determined by having the same number of owned properties (as verified\n   * with Object.prototype.hasOwnProperty.call), the same set of keys\n   * (although not necessarily the same order), equivalent values for every\n   * corresponding key, and an identical 'prototype' property. Note: this\n   * accounts for both named and indexed properties on Arrays.\n   */\n  // eslint-disable-next-line no-use-before-define\n  return objEquiv(actual, expected, opts);\n}\nfunction isUndefinedOrNull(value) {\n  return value === null || value === undefined;\n}\nfunction isBuffer(x) {\n  if (!x || typeof x !== 'object' || typeof x.length !== 'number') {\n    return false;\n  }\n  if (typeof x.copy !== 'function' || typeof x.slice !== 'function') {\n    return false;\n  }\n  if (x.length > 0 && typeof x[0] !== 'number') {\n    return false;\n  }\n  return true;\n}\nfunction objEquiv(a, b, opts) {\n  /* eslint max-statements: [2, 50] */\n  var i, key;\n  if (typeof a !== typeof b) {\n    return false;\n  }\n  if (isUndefinedOrNull(a) || isUndefinedOrNull(b)) {\n    return false;\n  }\n\n  // an identical 'prototype' property.\n  if (a.prototype !== b.prototype) {\n    return false;\n  }\n  if (isArguments(a) !== isArguments(b)) {\n    return false;\n  }\n  var aIsRegex = isRegex(a);\n  var bIsRegex = isRegex(b);\n  if (aIsRegex !== bIsRegex) {\n    return false;\n  }\n  if (aIsRegex || bIsRegex) {\n    return a.source === b.source && flags(a) === flags(b);\n  }\n  if (isDate(a) && isDate(b)) {\n    return getTime.call(a) === getTime.call(b);\n  }\n  var aIsBuffer = isBuffer(a);\n  var bIsBuffer = isBuffer(b);\n  if (aIsBuffer !== bIsBuffer) {\n    return false;\n  }\n  if (aIsBuffer || bIsBuffer) {\n    // && would work too, because both are true or both false here\n    if (a.length !== b.length) {\n      return false;\n    }\n    for (i = 0; i < a.length; i++) {\n      if (a[i] !== b[i]) {\n        return false;\n      }\n    }\n    return true;\n  }\n  if (typeof a !== typeof b) {\n    return false;\n  }\n  try {\n    var ka = objectKeys(a);\n    var kb = objectKeys(b);\n  } catch (e) {\n    // happens when one is a string literal and the other isn't\n    return false;\n  }\n  // having the same number of owned properties (keys incorporates hasOwnProperty)\n  if (ka.length !== kb.length) {\n    return false;\n  }\n\n  // the same set of keys (although not necessarily the same order),\n  ka.sort();\n  kb.sort();\n  // ~~~cheap key test\n  for (i = ka.length - 1; i >= 0; i--) {\n    if (ka[i] != kb[i]) {\n      return false;\n    }\n  }\n  // equivalent values for every corresponding key, and ~~~possibly expensive deep test\n  for (i = ka.length - 1; i >= 0; i--) {\n    key = ka[i];\n    if (!deepEqual(a[key], b[key], opts)) {\n      return false;\n    }\n  }\n  return true;\n}\nmodule.exports = deepEqual;", "map": {"version": 3, "names": ["objectKeys", "require", "isArguments", "is", "isRegex", "flags", "isDate", "getTime", "Date", "prototype", "deepEqual", "actual", "expected", "options", "opts", "strict", "objEquiv", "isUndefinedOrNull", "value", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "x", "length", "copy", "slice", "a", "b", "i", "key", "aIsRegex", "bIsRegex", "source", "call", "aIsBuffer", "b<PERSON>s<PERSON>uffer", "ka", "kb", "e", "sort", "module", "exports"], "sources": ["C:/ignition/ignition-ui/node_modules/deep-equal/index.js"], "sourcesContent": ["var objectKeys = require('object-keys');\nvar isArguments = require('is-arguments');\nvar is = require('object-is');\nvar isRegex = require('is-regex');\nvar flags = require('regexp.prototype.flags');\nvar isDate = require('is-date-object');\n\nvar getTime = Date.prototype.getTime;\n\nfunction deepEqual(actual, expected, options) {\n  var opts = options || {};\n\n  // 7.1. All identical values are equivalent, as determined by ===.\n  if (opts.strict ? is(actual, expected) : actual === expected) {\n    return true;\n  }\n\n  // 7.3. Other pairs that do not both pass typeof value == 'object', equivalence is determined by ==.\n  if (!actual || !expected || (typeof actual !== 'object' && typeof expected !== 'object')) {\n    return opts.strict ? is(actual, expected) : actual == expected;\n  }\n\n  /*\n   * 7.4. For all other Object pairs, including Array objects, equivalence is\n   * determined by having the same number of owned properties (as verified\n   * with Object.prototype.hasOwnProperty.call), the same set of keys\n   * (although not necessarily the same order), equivalent values for every\n   * corresponding key, and an identical 'prototype' property. Note: this\n   * accounts for both named and indexed properties on Arrays.\n   */\n  // eslint-disable-next-line no-use-before-define\n  return objEquiv(actual, expected, opts);\n}\n\nfunction isUndefinedOrNull(value) {\n  return value === null || value === undefined;\n}\n\nfunction isBuffer(x) {\n  if (!x || typeof x !== 'object' || typeof x.length !== 'number') {\n    return false;\n  }\n  if (typeof x.copy !== 'function' || typeof x.slice !== 'function') {\n    return false;\n  }\n  if (x.length > 0 && typeof x[0] !== 'number') {\n    return false;\n  }\n  return true;\n}\n\nfunction objEquiv(a, b, opts) {\n  /* eslint max-statements: [2, 50] */\n  var i, key;\n  if (typeof a !== typeof b) { return false; }\n  if (isUndefinedOrNull(a) || isUndefinedOrNull(b)) { return false; }\n\n  // an identical 'prototype' property.\n  if (a.prototype !== b.prototype) { return false; }\n\n  if (isArguments(a) !== isArguments(b)) { return false; }\n\n  var aIsRegex = isRegex(a);\n  var bIsRegex = isRegex(b);\n  if (aIsRegex !== bIsRegex) { return false; }\n  if (aIsRegex || bIsRegex) {\n    return a.source === b.source && flags(a) === flags(b);\n  }\n\n  if (isDate(a) && isDate(b)) {\n    return getTime.call(a) === getTime.call(b);\n  }\n\n  var aIsBuffer = isBuffer(a);\n  var bIsBuffer = isBuffer(b);\n  if (aIsBuffer !== bIsBuffer) { return false; }\n  if (aIsBuffer || bIsBuffer) { // && would work too, because both are true or both false here\n    if (a.length !== b.length) { return false; }\n    for (i = 0; i < a.length; i++) {\n      if (a[i] !== b[i]) { return false; }\n    }\n    return true;\n  }\n\n  if (typeof a !== typeof b) { return false; }\n\n  try {\n    var ka = objectKeys(a);\n    var kb = objectKeys(b);\n  } catch (e) { // happens when one is a string literal and the other isn't\n    return false;\n  }\n  // having the same number of owned properties (keys incorporates hasOwnProperty)\n  if (ka.length !== kb.length) { return false; }\n\n  // the same set of keys (although not necessarily the same order),\n  ka.sort();\n  kb.sort();\n  // ~~~cheap key test\n  for (i = ka.length - 1; i >= 0; i--) {\n    if (ka[i] != kb[i]) { return false; }\n  }\n  // equivalent values for every corresponding key, and ~~~possibly expensive deep test\n  for (i = ka.length - 1; i >= 0; i--) {\n    key = ka[i];\n    if (!deepEqual(a[key], b[key], opts)) { return false; }\n  }\n\n  return true;\n}\n\nmodule.exports = deepEqual;\n"], "mappings": "AAAA,IAAIA,UAAU,GAAGC,OAAO,CAAC,aAAa,CAAC;AACvC,IAAIC,WAAW,GAAGD,OAAO,CAAC,cAAc,CAAC;AACzC,IAAIE,EAAE,GAAGF,OAAO,CAAC,WAAW,CAAC;AAC7B,IAAIG,OAAO,GAAGH,OAAO,CAAC,UAAU,CAAC;AACjC,IAAII,KAAK,GAAGJ,OAAO,CAAC,wBAAwB,CAAC;AAC7C,IAAIK,MAAM,GAAGL,OAAO,CAAC,gBAAgB,CAAC;AAEtC,IAAIM,OAAO,GAAGC,IAAI,CAACC,SAAS,CAACF,OAAO;AAEpC,SAASG,SAASA,CAACC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EAC5C,IAAIC,IAAI,GAAGD,OAAO,IAAI,CAAC,CAAC;;EAExB;EACA,IAAIC,IAAI,CAACC,MAAM,GAAGZ,EAAE,CAACQ,MAAM,EAAEC,QAAQ,CAAC,GAAGD,MAAM,KAAKC,QAAQ,EAAE;IAC5D,OAAO,IAAI;EACb;;EAEA;EACA,IAAI,CAACD,MAAM,IAAI,CAACC,QAAQ,IAAK,OAAOD,MAAM,KAAK,QAAQ,IAAI,OAAOC,QAAQ,KAAK,QAAS,EAAE;IACxF,OAAOE,IAAI,CAACC,MAAM,GAAGZ,EAAE,CAACQ,MAAM,EAAEC,QAAQ,CAAC,GAAGD,MAAM,IAAIC,QAAQ;EAChE;;EAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA;EACE;EACA,OAAOI,QAAQ,CAACL,MAAM,EAAEC,QAAQ,EAAEE,IAAI,CAAC;AACzC;AAEA,SAASG,iBAAiBA,CAACC,KAAK,EAAE;EAChC,OAAOA,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS;AAC9C;AAEA,SAASC,QAAQA,CAACC,CAAC,EAAE;EACnB,IAAI,CAACA,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,IAAI,OAAOA,CAAC,CAACC,MAAM,KAAK,QAAQ,EAAE;IAC/D,OAAO,KAAK;EACd;EACA,IAAI,OAAOD,CAAC,CAACE,IAAI,KAAK,UAAU,IAAI,OAAOF,CAAC,CAACG,KAAK,KAAK,UAAU,EAAE;IACjE,OAAO,KAAK;EACd;EACA,IAAIH,CAAC,CAACC,MAAM,GAAG,CAAC,IAAI,OAAOD,CAAC,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;IAC5C,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb;AAEA,SAASL,QAAQA,CAACS,CAAC,EAAEC,CAAC,EAAEZ,IAAI,EAAE;EAC5B;EACA,IAAIa,CAAC,EAAEC,GAAG;EACV,IAAI,OAAOH,CAAC,KAAK,OAAOC,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;EAC3C,IAAIT,iBAAiB,CAACQ,CAAC,CAAC,IAAIR,iBAAiB,CAACS,CAAC,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;;EAElE;EACA,IAAID,CAAC,CAAChB,SAAS,KAAKiB,CAAC,CAACjB,SAAS,EAAE;IAAE,OAAO,KAAK;EAAE;EAEjD,IAAIP,WAAW,CAACuB,CAAC,CAAC,KAAKvB,WAAW,CAACwB,CAAC,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;EAEvD,IAAIG,QAAQ,GAAGzB,OAAO,CAACqB,CAAC,CAAC;EACzB,IAAIK,QAAQ,GAAG1B,OAAO,CAACsB,CAAC,CAAC;EACzB,IAAIG,QAAQ,KAAKC,QAAQ,EAAE;IAAE,OAAO,KAAK;EAAE;EAC3C,IAAID,QAAQ,IAAIC,QAAQ,EAAE;IACxB,OAAOL,CAAC,CAACM,MAAM,KAAKL,CAAC,CAACK,MAAM,IAAI1B,KAAK,CAACoB,CAAC,CAAC,KAAKpB,KAAK,CAACqB,CAAC,CAAC;EACvD;EAEA,IAAIpB,MAAM,CAACmB,CAAC,CAAC,IAAInB,MAAM,CAACoB,CAAC,CAAC,EAAE;IAC1B,OAAOnB,OAAO,CAACyB,IAAI,CAACP,CAAC,CAAC,KAAKlB,OAAO,CAACyB,IAAI,CAACN,CAAC,CAAC;EAC5C;EAEA,IAAIO,SAAS,GAAGb,QAAQ,CAACK,CAAC,CAAC;EAC3B,IAAIS,SAAS,GAAGd,QAAQ,CAACM,CAAC,CAAC;EAC3B,IAAIO,SAAS,KAAKC,SAAS,EAAE;IAAE,OAAO,KAAK;EAAE;EAC7C,IAAID,SAAS,IAAIC,SAAS,EAAE;IAAE;IAC5B,IAAIT,CAAC,CAACH,MAAM,KAAKI,CAAC,CAACJ,MAAM,EAAE;MAAE,OAAO,KAAK;IAAE;IAC3C,KAAKK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,CAAC,CAACH,MAAM,EAAEK,CAAC,EAAE,EAAE;MAC7B,IAAIF,CAAC,CAACE,CAAC,CAAC,KAAKD,CAAC,CAACC,CAAC,CAAC,EAAE;QAAE,OAAO,KAAK;MAAE;IACrC;IACA,OAAO,IAAI;EACb;EAEA,IAAI,OAAOF,CAAC,KAAK,OAAOC,CAAC,EAAE;IAAE,OAAO,KAAK;EAAE;EAE3C,IAAI;IACF,IAAIS,EAAE,GAAGnC,UAAU,CAACyB,CAAC,CAAC;IACtB,IAAIW,EAAE,GAAGpC,UAAU,CAAC0B,CAAC,CAAC;EACxB,CAAC,CAAC,OAAOW,CAAC,EAAE;IAAE;IACZ,OAAO,KAAK;EACd;EACA;EACA,IAAIF,EAAE,CAACb,MAAM,KAAKc,EAAE,CAACd,MAAM,EAAE;IAAE,OAAO,KAAK;EAAE;;EAE7C;EACAa,EAAE,CAACG,IAAI,CAAC,CAAC;EACTF,EAAE,CAACE,IAAI,CAAC,CAAC;EACT;EACA,KAAKX,CAAC,GAAGQ,EAAE,CAACb,MAAM,GAAG,CAAC,EAAEK,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACnC,IAAIQ,EAAE,CAACR,CAAC,CAAC,IAAIS,EAAE,CAACT,CAAC,CAAC,EAAE;MAAE,OAAO,KAAK;IAAE;EACtC;EACA;EACA,KAAKA,CAAC,GAAGQ,EAAE,CAACb,MAAM,GAAG,CAAC,EAAEK,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IACnCC,GAAG,GAAGO,EAAE,CAACR,CAAC,CAAC;IACX,IAAI,CAACjB,SAAS,CAACe,CAAC,CAACG,GAAG,CAAC,EAAEF,CAAC,CAACE,GAAG,CAAC,EAAEd,IAAI,CAAC,EAAE;MAAE,OAAO,KAAK;IAAE;EACxD;EAEA,OAAO,IAAI;AACb;AAEAyB,MAAM,CAACC,OAAO,GAAG9B,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}