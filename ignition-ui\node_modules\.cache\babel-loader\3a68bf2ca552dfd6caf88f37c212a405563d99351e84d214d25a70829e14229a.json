{"ast": null, "code": "var _jsxFileName = \"C:\\\\ignition\\\\ignition-ui\\\\src\\\\views\\\\plan\\\\detail\\\\components\\\\ChatbotBar.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Box, TextField, IconButton, Paper, Typography, Chip, Tooltip, Collapse, CircularProgress } from '@mui/material';\nimport Iconify from 'components/Iconify/index';\nimport { mainYellowColor } from \"helpers/constants\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatbotBar = _ref => {\n  _s();\n  let {\n    planInfo,\n    onPlanUpdate,\n    onSwitchToAgent\n  } = _ref;\n  const [isExpanded, setIsExpanded] = useState(false);\n  const [message, setMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [suggestions] = useState([\"Mark task as completed\", \"Add new task to milestone\", \"Update task description\", \"Delete completed tasks\", \"Show project progress\"]);\n  const inputRef = useRef(null);\n\n  // Focus input when expanded\n  useEffect(() => {\n    if (isExpanded && inputRef.current) {\n      inputRef.current.focus();\n    }\n  }, [isExpanded]);\n  const handleToggleExpand = () => {\n    setIsExpanded(!isExpanded);\n  };\n  const handleSendMessage = async () => {\n    if (!message.trim() || isLoading) return;\n    console.log('ChatbotBar - planInfo:', planInfo); // Debug log\n    setIsLoading(true);\n    try {\n      // Validate planInfo before proceeding\n      if (!(planInfo !== null && planInfo !== void 0 && planInfo.id)) {\n        console.error('ChatbotBar - Missing planInfo or planInfo.id:', planInfo);\n        return;\n      }\n\n      // Save conversation to localStorage for Agent tab\n      const conversationHistory = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\n      const newConversation = {\n        id: Date.now(),\n        planId: planInfo.id,\n        planName: planInfo.name,\n        message: message.trim(),\n        timestamp: new Date().toISOString(),\n        response: null // Will be filled by AI response\n      };\n      conversationHistory.push(newConversation);\n      localStorage.setItem('agent_conversations', JSON.stringify(conversationHistory));\n      console.log('ChatbotBar - Conversation saved, switching to agent tab'); // Debug log\n\n      // Switch to Agent tab with the conversation\n      if (onSwitchToAgent) {\n        onSwitchToAgent({\n          message: message.trim(),\n          planInfo: planInfo,\n          conversationId: newConversation.id\n        });\n      } else {\n        console.error('ChatbotBar - onSwitchToAgent callback not provided');\n      }\n    } catch (error) {\n      console.error('Error sending message:', error);\n    } finally {\n      setIsLoading(false);\n      setMessage('');\n      setIsExpanded(false);\n    }\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n  const handleSuggestionClick = suggestion => {\n    setMessage(suggestion);\n    if (inputRef.current) {\n      inputRef.current.focus();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Paper, {\n    elevation: 0,\n    sx: {\n      mb: 2,\n      borderRadius: '12px',\n      border: '1px solid #f0f0f0',\n      overflow: 'hidden',\n      transition: 'all 0.3s ease',\n      backgroundColor: '#fafafa'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        p: 2,\n        cursor: 'pointer',\n        '&:hover': {\n          backgroundColor: '#f5f5f5'\n        }\n      },\n      onClick: handleToggleExpand,\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 1.5\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: 32,\n            height: 32,\n            borderRadius: '50%',\n            backgroundColor: mainYellowColor,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"mdi:robot\",\n            width: 18,\n            height: 18,\n            color: \"#fff\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 142,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              fontWeight: 600,\n              fontSize: '1rem',\n              color: '#333',\n              mb: 0.5\n            },\n            children: \"AI Project Assistant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            sx: {\n              color: '#666',\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              fontSize: '0.8rem'\n            },\n            children: \"Ask questions or make changes to your project\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 157,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 130,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Chip, {\n          label: \"Beta\",\n          size: \"small\",\n          sx: {\n            backgroundColor: `${mainYellowColor}20`,\n            color: mainYellowColor,\n            fontWeight: 600,\n            fontSize: '0.7rem',\n            height: '20px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Iconify, {\n            icon: isExpanded ? \"material-symbols:expand-less\" : \"material-symbols:expand-more\",\n            width: 20,\n            height: 20,\n            color: \"#666\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 183,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n      in: isExpanded,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          px: 2,\n          pb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            sx: {\n              color: '#666',\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              fontSize: '0.8rem',\n              mb: 1,\n              display: 'block'\n            },\n            children: \"Quick suggestions:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 198,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              flexWrap: 'wrap',\n              gap: 1\n            },\n            children: suggestions.map((suggestion, index) => /*#__PURE__*/_jsxDEV(Chip, {\n              label: suggestion,\n              size: \"small\",\n              onClick: () => handleSuggestionClick(suggestion),\n              sx: {\n                backgroundColor: '#fff',\n                border: '1px solid #e0e0e0',\n                cursor: 'pointer',\n                fontSize: '0.75rem',\n                '&:hover': {\n                  backgroundColor: `${mainYellowColor}10`,\n                  borderColor: mainYellowColor\n                }\n              }\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 1,\n            alignItems: 'flex-end'\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            inputRef: inputRef,\n            value: message,\n            onChange: e => setMessage(e.target.value),\n            onKeyPress: handleKeyPress,\n            placeholder: \"Ask me anything about your project or request changes...\",\n            multiline: true,\n            maxRows: 3,\n            fullWidth: true,\n            variant: \"outlined\",\n            disabled: isLoading,\n            sx: {\n              '& .MuiOutlinedInput-root': {\n                borderRadius: '8px',\n                backgroundColor: '#fff',\n                fontFamily: '\"Recursive Variable\", sans-serif',\n                fontSize: '0.9rem'\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: \"Send message\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: handleSendMessage,\n              disabled: !message.trim() || isLoading,\n              sx: {\n                backgroundColor: message.trim() && !isLoading ? mainYellowColor : '#f0f0f0',\n                color: message.trim() && !isLoading ? '#fff' : '#999',\n                '&:hover': {\n                  backgroundColor: message.trim() && !isLoading ? '#E69500' : '#f0f0f0'\n                },\n                mb: 0.5\n              },\n              children: isLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 20,\n                sx: {\n                  color: '#999'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(Iconify, {\n                icon: \"material-symbols:send\",\n                width: 20,\n                height: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 270,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 254,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          sx: {\n            color: '#999',\n            fontFamily: '\"Recursive Variable\", sans-serif',\n            fontSize: '0.75rem',\n            mt: 1,\n            display: 'block'\n          },\n          children: \"\\uD83D\\uDCA1 I can help you mark tasks as complete, add new tasks, update descriptions, and more!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 277,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 195,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 194,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 105,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatbotBar, \"mef8O7yVDwbwWJgg1+xcI0Pp7hs=\");\n_c = ChatbotBar;\nexport default ChatbotBar;\nvar _c;\n$RefreshReg$(_c, \"ChatbotBar\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Box", "TextField", "IconButton", "Paper", "Typography", "Chip", "<PERSON><PERSON><PERSON>", "Collapse", "CircularProgress", "Iconify", "mainYellowColor", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "_s", "planInfo", "onPlanUpdate", "onSwitchToAgent", "isExpanded", "setIsExpanded", "message", "setMessage", "isLoading", "setIsLoading", "suggestions", "inputRef", "current", "focus", "handleToggleExpand", "handleSendMessage", "trim", "console", "log", "id", "error", "conversationHistory", "JSON", "parse", "localStorage", "getItem", "newConversation", "Date", "now", "planId", "planName", "name", "timestamp", "toISOString", "response", "push", "setItem", "stringify", "conversationId", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "handleSuggestionClick", "suggestion", "elevation", "sx", "mb", "borderRadius", "border", "overflow", "transition", "backgroundColor", "children", "display", "alignItems", "justifyContent", "p", "cursor", "onClick", "gap", "width", "height", "icon", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontFamily", "fontWeight", "fontSize", "label", "size", "in", "px", "pb", "flexWrap", "map", "index", "borderColor", "value", "onChange", "target", "onKeyPress", "placeholder", "multiline", "maxRows", "fullWidth", "disabled", "title", "mt", "_c", "$RefreshReg$"], "sources": ["C:/ignition/ignition-ui/src/views/plan/detail/components/ChatbotBar.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport {\n  Box,\n  TextField,\n  IconButton,\n  Paper,\n  Typography,\n  Chip,\n  Tooltip,\n  Collapse,\n  CircularProgress\n} from '@mui/material';\nimport Iconify from 'components/Iconify/index';\nimport { mainYellowColor } from \"helpers/constants\";\n\nconst ChatbotBar = ({ planInfo, onPlanUpdate, onSwitchToAgent }) => {\n  const [isExpanded, setIsExpanded] = useState(false);\n  const [message, setMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [suggestions] = useState([\n    \"Mark task as completed\",\n    \"Add new task to milestone\",\n    \"Update task description\",\n    \"Delete completed tasks\",\n    \"Show project progress\"\n  ]);\n  \n  const inputRef = useRef(null);\n\n  // Focus input when expanded\n  useEffect(() => {\n    if (isExpanded && inputRef.current) {\n      inputRef.current.focus();\n    }\n  }, [isExpanded]);\n\n  const handleToggleExpand = () => {\n    setIsExpanded(!isExpanded);\n  };\n\n  const handleSendMessage = async () => {\n    if (!message.trim() || isLoading) return;\n\n    console.log('ChatbotBar - planInfo:', planInfo); // Debug log\n    setIsLoading(true);\n\n    try {\n      // Validate planInfo before proceeding\n      if (!planInfo?.id) {\n        console.error('ChatbotBar - Missing planInfo or planInfo.id:', planInfo);\n        return;\n      }\n\n      // Save conversation to localStorage for Agent tab\n      const conversationHistory = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\n      const newConversation = {\n        id: Date.now(),\n        planId: planInfo.id,\n        planName: planInfo.name,\n        message: message.trim(),\n        timestamp: new Date().toISOString(),\n        response: null // Will be filled by AI response\n      };\n\n      conversationHistory.push(newConversation);\n      localStorage.setItem('agent_conversations', JSON.stringify(conversationHistory));\n\n      console.log('ChatbotBar - Conversation saved, switching to agent tab'); // Debug log\n\n      // Switch to Agent tab with the conversation\n      if (onSwitchToAgent) {\n        onSwitchToAgent({\n          message: message.trim(),\n          planInfo: planInfo,\n          conversationId: newConversation.id\n        });\n      } else {\n        console.error('ChatbotBar - onSwitchToAgent callback not provided');\n      }\n\n    } catch (error) {\n      console.error('Error sending message:', error);\n    } finally {\n      setIsLoading(false);\n      setMessage('');\n      setIsExpanded(false);\n    }\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  const handleSuggestionClick = (suggestion) => {\n    setMessage(suggestion);\n    if (inputRef.current) {\n      inputRef.current.focus();\n    }\n  };\n\n  return (\n    <Paper\n      elevation={0}\n      sx={{\n        mb: 2,\n        borderRadius: '12px',\n        border: '1px solid #f0f0f0',\n        overflow: 'hidden',\n        transition: 'all 0.3s ease',\n        backgroundColor: '#fafafa'\n      }}\n    >\n      {/* Chatbot Header */}\n      <Box\n        sx={{\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          p: 2,\n          cursor: 'pointer',\n          '&:hover': {\n            backgroundColor: '#f5f5f5'\n          }\n        }}\n        onClick={handleToggleExpand}\n      >\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>\n          <Box\n            sx={{\n              width: 32,\n              height: 32,\n              borderRadius: '50%',\n              backgroundColor: mainYellowColor,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            }}\n          >\n            <Iconify icon=\"mdi:robot\" width={18} height={18} color=\"#fff\" />\n          </Box>\n          <Box>\n            <Typography\n              variant=\"h6\"\n              sx={{\n                fontFamily: '\"Recursive Variable\", sans-serif',\n                fontWeight: 600,\n                fontSize: '1rem',\n                color: '#333',\n                mb: 0.5\n              }}\n            >\n              AI Project Assistant\n            </Typography>\n            <Typography\n              variant=\"caption\"\n              sx={{\n                color: '#666',\n                fontFamily: '\"Recursive Variable\", sans-serif',\n                fontSize: '0.8rem'\n              }}\n            >\n              Ask questions or make changes to your project\n            </Typography>\n          </Box>\n        </Box>\n\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n          <Chip\n            label=\"Beta\"\n            size=\"small\"\n            sx={{\n              backgroundColor: `${mainYellowColor}20`,\n              color: mainYellowColor,\n              fontWeight: 600,\n              fontSize: '0.7rem',\n              height: '20px'\n            }}\n          />\n          <IconButton size=\"small\">\n            <Iconify \n              icon={isExpanded ? \"material-symbols:expand-less\" : \"material-symbols:expand-more\"} \n              width={20} \n              height={20} \n              color=\"#666\"\n            />\n          </IconButton>\n        </Box>\n      </Box>\n\n      {/* Expanded Content */}\n      <Collapse in={isExpanded}>\n        <Box sx={{ px: 2, pb: 2 }}>\n          {/* Quick Suggestions */}\n          <Box sx={{ mb: 2 }}>\n            <Typography\n              variant=\"caption\"\n              sx={{\n                color: '#666',\n                fontFamily: '\"Recursive Variable\", sans-serif',\n                fontSize: '0.8rem',\n                mb: 1,\n                display: 'block'\n              }}\n            >\n              Quick suggestions:\n            </Typography>\n            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>\n              {suggestions.map((suggestion, index) => (\n                <Chip\n                  key={index}\n                  label={suggestion}\n                  size=\"small\"\n                  onClick={() => handleSuggestionClick(suggestion)}\n                  sx={{\n                    backgroundColor: '#fff',\n                    border: '1px solid #e0e0e0',\n                    cursor: 'pointer',\n                    fontSize: '0.75rem',\n                    '&:hover': {\n                      backgroundColor: `${mainYellowColor}10`,\n                      borderColor: mainYellowColor\n                    }\n                  }}\n                />\n              ))}\n            </Box>\n          </Box>\n\n          {/* Input Field */}\n          <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>\n            <TextField\n              inputRef={inputRef}\n              value={message}\n              onChange={(e) => setMessage(e.target.value)}\n              onKeyPress={handleKeyPress}\n              placeholder=\"Ask me anything about your project or request changes...\"\n              multiline\n              maxRows={3}\n              fullWidth\n              variant=\"outlined\"\n              disabled={isLoading}\n              sx={{\n                '& .MuiOutlinedInput-root': {\n                  borderRadius: '8px',\n                  backgroundColor: '#fff',\n                  fontFamily: '\"Recursive Variable\", sans-serif',\n                  fontSize: '0.9rem'\n                }\n              }}\n            />\n            <Tooltip title=\"Send message\">\n              <IconButton\n                onClick={handleSendMessage}\n                disabled={!message.trim() || isLoading}\n                sx={{\n                  backgroundColor: message.trim() && !isLoading ? mainYellowColor : '#f0f0f0',\n                  color: message.trim() && !isLoading ? '#fff' : '#999',\n                  '&:hover': {\n                    backgroundColor: message.trim() && !isLoading ? '#E69500' : '#f0f0f0'\n                  },\n                  mb: 0.5\n                }}\n              >\n                {isLoading ? (\n                  <CircularProgress size={20} sx={{ color: '#999' }} />\n                ) : (\n                  <Iconify icon=\"material-symbols:send\" width={20} height={20} />\n                )}\n              </IconButton>\n            </Tooltip>\n          </Box>\n\n          {/* Helper Text */}\n          <Typography\n            variant=\"caption\"\n            sx={{\n              color: '#999',\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              fontSize: '0.75rem',\n              mt: 1,\n              display: 'block'\n            }}\n          >\n            💡 I can help you mark tasks as complete, add new tasks, update descriptions, and more!\n          </Typography>\n        </Box>\n      </Collapse>\n    </Paper>\n  );\n};\n\nexport default ChatbotBar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SACEC,GAAG,EACHC,SAAS,EACTC,UAAU,EACVC,KAAK,EACLC,UAAU,EACVC,IAAI,EACJC,OAAO,EACPC,QAAQ,EACRC,gBAAgB,QACX,eAAe;AACtB,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,SAASC,eAAe,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,UAAU,GAAGC,IAAA,IAAiD;EAAAC,EAAA;EAAA,IAAhD;IAAEC,QAAQ;IAAEC,YAAY;IAAEC;EAAgB,CAAC,GAAAJ,IAAA;EAC7D,MAAM,CAACK,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAAC4B,WAAW,CAAC,GAAG5B,QAAQ,CAAC,CAC7B,wBAAwB,EACxB,2BAA2B,EAC3B,yBAAyB,EACzB,wBAAwB,EACxB,uBAAuB,CACxB,CAAC;EAEF,MAAM6B,QAAQ,GAAG5B,MAAM,CAAC,IAAI,CAAC;;EAE7B;EACAC,SAAS,CAAC,MAAM;IACd,IAAIoB,UAAU,IAAIO,QAAQ,CAACC,OAAO,EAAE;MAClCD,QAAQ,CAACC,OAAO,CAACC,KAAK,CAAC,CAAC;IAC1B;EACF,CAAC,EAAE,CAACT,UAAU,CAAC,CAAC;EAEhB,MAAMU,kBAAkB,GAAGA,CAAA,KAAM;IAC/BT,aAAa,CAAC,CAACD,UAAU,CAAC;EAC5B,CAAC;EAED,MAAMW,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACT,OAAO,CAACU,IAAI,CAAC,CAAC,IAAIR,SAAS,EAAE;IAElCS,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEjB,QAAQ,CAAC,CAAC,CAAC;IACjDQ,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF;MACA,IAAI,EAACR,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEkB,EAAE,GAAE;QACjBF,OAAO,CAACG,KAAK,CAAC,+CAA+C,EAAEnB,QAAQ,CAAC;QACxE;MACF;;MAEA;MACA,MAAMoB,mBAAmB,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC,IAAI,IAAI,CAAC;MAC3F,MAAMC,eAAe,GAAG;QACtBP,EAAE,EAAEQ,IAAI,CAACC,GAAG,CAAC,CAAC;QACdC,MAAM,EAAE5B,QAAQ,CAACkB,EAAE;QACnBW,QAAQ,EAAE7B,QAAQ,CAAC8B,IAAI;QACvBzB,OAAO,EAAEA,OAAO,CAACU,IAAI,CAAC,CAAC;QACvBgB,SAAS,EAAE,IAAIL,IAAI,CAAC,CAAC,CAACM,WAAW,CAAC,CAAC;QACnCC,QAAQ,EAAE,IAAI,CAAC;MACjB,CAAC;MAEDb,mBAAmB,CAACc,IAAI,CAACT,eAAe,CAAC;MACzCF,YAAY,CAACY,OAAO,CAAC,qBAAqB,EAAEd,IAAI,CAACe,SAAS,CAAChB,mBAAmB,CAAC,CAAC;MAEhFJ,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC,CAAC,CAAC;;MAExE;MACA,IAAIf,eAAe,EAAE;QACnBA,eAAe,CAAC;UACdG,OAAO,EAAEA,OAAO,CAACU,IAAI,CAAC,CAAC;UACvBf,QAAQ,EAAEA,QAAQ;UAClBqC,cAAc,EAAEZ,eAAe,CAACP;QAClC,CAAC,CAAC;MACJ,CAAC,MAAM;QACLF,OAAO,CAACG,KAAK,CAAC,oDAAoD,CAAC;MACrE;IAEF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD,CAAC,SAAS;MACRX,YAAY,CAAC,KAAK,CAAC;MACnBF,UAAU,CAAC,EAAE,CAAC;MACdF,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMkC,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClB5B,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;EAED,MAAM6B,qBAAqB,GAAIC,UAAU,IAAK;IAC5CtC,UAAU,CAACsC,UAAU,CAAC;IACtB,IAAIlC,QAAQ,CAACC,OAAO,EAAE;MACpBD,QAAQ,CAACC,OAAO,CAACC,KAAK,CAAC,CAAC;IAC1B;EACF,CAAC;EAED,oBACEhB,OAAA,CAACT,KAAK;IACJ0D,SAAS,EAAE,CAAE;IACbC,EAAE,EAAE;MACFC,EAAE,EAAE,CAAC;MACLC,YAAY,EAAE,MAAM;MACpBC,MAAM,EAAE,mBAAmB;MAC3BC,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,eAAe;MAC3BC,eAAe,EAAE;IACnB,CAAE;IAAAC,QAAA,gBAGFzD,OAAA,CAACZ,GAAG;MACF8D,EAAE,EAAE;QACFQ,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,eAAe;QAC/BC,CAAC,EAAE,CAAC;QACJC,MAAM,EAAE,SAAS;QACjB,SAAS,EAAE;UACTN,eAAe,EAAE;QACnB;MACF,CAAE;MACFO,OAAO,EAAE9C,kBAAmB;MAAAwC,QAAA,gBAE5BzD,OAAA,CAACZ,GAAG;QAAC8D,EAAE,EAAE;UAAEQ,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEK,GAAG,EAAE;QAAI,CAAE;QAAAP,QAAA,gBAC3DzD,OAAA,CAACZ,GAAG;UACF8D,EAAE,EAAE;YACFe,KAAK,EAAE,EAAE;YACTC,MAAM,EAAE,EAAE;YACVd,YAAY,EAAE,KAAK;YACnBI,eAAe,EAAE1D,eAAe;YAChC4D,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAH,QAAA,eAEFzD,OAAA,CAACH,OAAO;YAACsE,IAAI,EAAC,WAAW;YAACF,KAAK,EAAE,EAAG;YAACC,MAAM,EAAE,EAAG;YAACE,KAAK,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eACNxE,OAAA,CAACZ,GAAG;UAAAqE,QAAA,gBACFzD,OAAA,CAACR,UAAU;YACTiF,OAAO,EAAC,IAAI;YACZvB,EAAE,EAAE;cACFwB,UAAU,EAAE,kCAAkC;cAC9CC,UAAU,EAAE,GAAG;cACfC,QAAQ,EAAE,MAAM;cAChBR,KAAK,EAAE,MAAM;cACbjB,EAAE,EAAE;YACN,CAAE;YAAAM,QAAA,EACH;UAED;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxE,OAAA,CAACR,UAAU;YACTiF,OAAO,EAAC,SAAS;YACjBvB,EAAE,EAAE;cACFkB,KAAK,EAAE,MAAM;cACbM,UAAU,EAAE,kCAAkC;cAC9CE,QAAQ,EAAE;YACZ,CAAE;YAAAnB,QAAA,EACH;UAED;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENxE,OAAA,CAACZ,GAAG;QAAC8D,EAAE,EAAE;UAAEQ,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEK,GAAG,EAAE;QAAE,CAAE;QAAAP,QAAA,gBACzDzD,OAAA,CAACP,IAAI;UACHoF,KAAK,EAAC,MAAM;UACZC,IAAI,EAAC,OAAO;UACZ5B,EAAE,EAAE;YACFM,eAAe,EAAE,GAAG1D,eAAe,IAAI;YACvCsE,KAAK,EAAEtE,eAAe;YACtB6E,UAAU,EAAE,GAAG;YACfC,QAAQ,EAAE,QAAQ;YAClBV,MAAM,EAAE;UACV;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFxE,OAAA,CAACV,UAAU;UAACwF,IAAI,EAAC,OAAO;UAAArB,QAAA,eACtBzD,OAAA,CAACH,OAAO;YACNsE,IAAI,EAAE5D,UAAU,GAAG,8BAA8B,GAAG,8BAA+B;YACnF0D,KAAK,EAAE,EAAG;YACVC,MAAM,EAAE,EAAG;YACXE,KAAK,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNxE,OAAA,CAACL,QAAQ;MAACoF,EAAE,EAAExE,UAAW;MAAAkD,QAAA,eACvBzD,OAAA,CAACZ,GAAG;QAAC8D,EAAE,EAAE;UAAE8B,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAxB,QAAA,gBAExBzD,OAAA,CAACZ,GAAG;UAAC8D,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAM,QAAA,gBACjBzD,OAAA,CAACR,UAAU;YACTiF,OAAO,EAAC,SAAS;YACjBvB,EAAE,EAAE;cACFkB,KAAK,EAAE,MAAM;cACbM,UAAU,EAAE,kCAAkC;cAC9CE,QAAQ,EAAE,QAAQ;cAClBzB,EAAE,EAAE,CAAC;cACLO,OAAO,EAAE;YACX,CAAE;YAAAD,QAAA,EACH;UAED;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbxE,OAAA,CAACZ,GAAG;YAAC8D,EAAE,EAAE;cAAEQ,OAAO,EAAE,MAAM;cAAEwB,QAAQ,EAAE,MAAM;cAAElB,GAAG,EAAE;YAAE,CAAE;YAAAP,QAAA,EACpD5C,WAAW,CAACsE,GAAG,CAAC,CAACnC,UAAU,EAAEoC,KAAK,kBACjCpF,OAAA,CAACP,IAAI;cAEHoF,KAAK,EAAE7B,UAAW;cAClB8B,IAAI,EAAC,OAAO;cACZf,OAAO,EAAEA,CAAA,KAAMhB,qBAAqB,CAACC,UAAU,CAAE;cACjDE,EAAE,EAAE;gBACFM,eAAe,EAAE,MAAM;gBACvBH,MAAM,EAAE,mBAAmB;gBAC3BS,MAAM,EAAE,SAAS;gBACjBc,QAAQ,EAAE,SAAS;gBACnB,SAAS,EAAE;kBACTpB,eAAe,EAAE,GAAG1D,eAAe,IAAI;kBACvCuF,WAAW,EAAEvF;gBACf;cACF;YAAE,GAbGsF,KAAK;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAcX,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNxE,OAAA,CAACZ,GAAG;UAAC8D,EAAE,EAAE;YAAEQ,OAAO,EAAE,MAAM;YAAEM,GAAG,EAAE,CAAC;YAAEL,UAAU,EAAE;UAAW,CAAE;UAAAF,QAAA,gBAC3DzD,OAAA,CAACX,SAAS;YACRyB,QAAQ,EAAEA,QAAS;YACnBwE,KAAK,EAAE7E,OAAQ;YACf8E,QAAQ,EAAG5C,CAAC,IAAKjC,UAAU,CAACiC,CAAC,CAAC6C,MAAM,CAACF,KAAK,CAAE;YAC5CG,UAAU,EAAE/C,cAAe;YAC3BgD,WAAW,EAAC,0DAA0D;YACtEC,SAAS;YACTC,OAAO,EAAE,CAAE;YACXC,SAAS;YACTpB,OAAO,EAAC,UAAU;YAClBqB,QAAQ,EAAEnF,SAAU;YACpBuC,EAAE,EAAE;cACF,0BAA0B,EAAE;gBAC1BE,YAAY,EAAE,KAAK;gBACnBI,eAAe,EAAE,MAAM;gBACvBkB,UAAU,EAAE,kCAAkC;gBAC9CE,QAAQ,EAAE;cACZ;YACF;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACFxE,OAAA,CAACN,OAAO;YAACqG,KAAK,EAAC,cAAc;YAAAtC,QAAA,eAC3BzD,OAAA,CAACV,UAAU;cACTyE,OAAO,EAAE7C,iBAAkB;cAC3B4E,QAAQ,EAAE,CAACrF,OAAO,CAACU,IAAI,CAAC,CAAC,IAAIR,SAAU;cACvCuC,EAAE,EAAE;gBACFM,eAAe,EAAE/C,OAAO,CAACU,IAAI,CAAC,CAAC,IAAI,CAACR,SAAS,GAAGb,eAAe,GAAG,SAAS;gBAC3EsE,KAAK,EAAE3D,OAAO,CAACU,IAAI,CAAC,CAAC,IAAI,CAACR,SAAS,GAAG,MAAM,GAAG,MAAM;gBACrD,SAAS,EAAE;kBACT6C,eAAe,EAAE/C,OAAO,CAACU,IAAI,CAAC,CAAC,IAAI,CAACR,SAAS,GAAG,SAAS,GAAG;gBAC9D,CAAC;gBACDwC,EAAE,EAAE;cACN,CAAE;cAAAM,QAAA,EAED9C,SAAS,gBACRX,OAAA,CAACJ,gBAAgB;gBAACkF,IAAI,EAAE,EAAG;gBAAC5B,EAAE,EAAE;kBAAEkB,KAAK,EAAE;gBAAO;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAErDxE,OAAA,CAACH,OAAO;gBAACsE,IAAI,EAAC,uBAAuB;gBAACF,KAAK,EAAE,EAAG;gBAACC,MAAM,EAAE;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAC/D;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eAGNxE,OAAA,CAACR,UAAU;UACTiF,OAAO,EAAC,SAAS;UACjBvB,EAAE,EAAE;YACFkB,KAAK,EAAE,MAAM;YACbM,UAAU,EAAE,kCAAkC;YAC9CE,QAAQ,EAAE,SAAS;YACnBoB,EAAE,EAAE,CAAC;YACLtC,OAAO,EAAE;UACX,CAAE;UAAAD,QAAA,EACH;QAED;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEZ,CAAC;AAACrE,EAAA,CArRIF,UAAU;AAAAgG,EAAA,GAAVhG,UAAU;AAuRhB,eAAeA,UAAU;AAAC,IAAAgG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}