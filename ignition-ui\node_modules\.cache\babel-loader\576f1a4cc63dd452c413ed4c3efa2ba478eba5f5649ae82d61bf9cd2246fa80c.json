{"ast": null, "code": "var _jsxFileName = \"C:\\\\ignition\\\\ignition-ui\\\\src\\\\views\\\\plan\\\\detail\\\\index.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useParams } from 'react-router-dom';\nimport { Container, Box, Tabs, Tab, CircularProgress, Alert } from '@mui/material';\nimport Iconify from 'components/Iconify/index';\nimport { mainYellowColor } from \"helpers/constants\";\nimport { successSnackbar, errorSnackbar } from 'components/Snackbar/index';\nimport { toast } from 'react-toastify';\n\n// Components\nimport Header from './components/Header';\nimport Description from './components/Description';\nimport Statistics from './components/Statistics';\nimport Progress from './components/Progress';\nimport Members from './components/Members';\nimport MilestoneList from './components/MilestoneList';\nimport TaskList from './components/TaskList';\nimport MilestoneOverview from './components/MilestoneOverview';\n\n// Hooks\nimport usePlanData from './hooks/usePlanData';\nimport useViewMode from './hooks/useViewMode';\n\n// Dialogs\nimport InviteDialog from './dialogs/InviteDialog';\nimport DeleteDialog from './dialogs/DeleteDialog';\nimport OptOutDialog from './dialogs/OptOutDialog';\nimport ConfirmDialog from './dialogs/ConfirmDialog';\n\n// Services\nimport { updateMilestone, updateTask, updateSubtask, addTask, addSubtask, deleteTask, deleteSubtask, assignMembersToTask } from '../services';\n\n// Styles\nimport styles from './styles.module.scss';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst PlanDetail = () => {\n  _s();\n  const {\n    param\n  } = useParams();\n  const [activeTab, setActiveTab] = useState(() => {\n    // Get active tab value from localStorage, default to 'overview' if not found\n    return localStorage.getItem(`plan_${param}_activeTab`) || 'overview';\n  });\n  const [plan, setPlan] = useState(null);\n  const [dialogState, setDialogState] = useState({\n    invite: false,\n    delete: false,\n    optOut: false,\n    deleteTask: false,\n    deleteSubtask: false\n  });\n  const [selectedTaskToDelete] = useState(null);\n  const [selectedSubtaskToDelete] = useState(null);\n\n  // Custom hooks\n  const {\n    planInfo,\n    loading,\n    error,\n    invitedUsers,\n    handleDeletePlan,\n    handleOptOutPlan,\n    handleInviteUser,\n    calculatePlanStats,\n    calculateSubtaskProgress,\n    getSubtaskStatus,\n    calculateTaskProgress,\n    getTaskStatus,\n    calculateMilestoneProgress,\n    getMilestoneStatus\n  } = usePlanData(param);\n  const {\n    viewMode,\n    handleViewModeChange\n  } = useViewMode();\n\n  // Update plan state when planInfo changes\n  useEffect(() => {\n    if (planInfo) {\n      setPlan(planInfo);\n    }\n  }, [planInfo]);\n\n  // Remove active tab from localStorage when component unmounts\n  useEffect(() => {\n    return () => {\n      localStorage.removeItem(`plan_${param}_activeTab`);\n    };\n  }, [param]);\n\n  // Calculate plan statistics\n  const stats = calculatePlanStats ? calculatePlanStats(planInfo) : null;\n\n  // Dialog handlers\n  const openDialog = dialogName => {\n    setDialogState(prev => ({\n      ...prev,\n      [dialogName]: true\n    }));\n  };\n  const closeDialog = dialogName => {\n    setDialogState(prev => ({\n      ...prev,\n      [dialogName]: false\n    }));\n  };\n\n  // Tab change handler\n  const handleTabChange = (event, newValue) => {\n    setActiveTab(newValue);\n    // Save active tab to localStorage\n    localStorage.setItem(`plan_${param}_activeTab`, newValue);\n  };\n\n  // Handle milestone update\n  const handleUpdateMilestone = async updatedMilestone => {\n    try {\n      console.log('Updating milestone:', updatedMilestone);\n      await updateMilestone(updatedMilestone);\n\n      // Update local state\n      const updatedPlan = {\n        ...plan\n      };\n      const milestoneIndex = updatedPlan.milestones.findIndex(m => m.id === updatedMilestone.id);\n      if (milestoneIndex !== -1) {\n        updatedPlan.milestones[milestoneIndex] = {\n          ...updatedPlan.milestones[milestoneIndex],\n          ...updatedMilestone\n        };\n        setPlan(updatedPlan);\n      }\n      successSnackbar('Milestone updated successfully');\n    } catch (error) {\n      console.error('Error updating milestone:', error);\n      errorSnackbar('Failed to update milestone');\n    }\n  };\n\n  // Handle task update\n  const handleUpdateTask = async updatedTask => {\n    try {\n      console.log('Updating task:', updatedTask);\n      await updateTask(updatedTask);\n\n      // Update local state\n      const updatedPlan = {\n        ...plan\n      };\n      const milestoneIndex = updatedPlan.milestones.findIndex(m => m.tasks && m.tasks.some(t => t.id === updatedTask.id));\n      if (milestoneIndex !== -1) {\n        const taskIndex = updatedPlan.milestones[milestoneIndex].tasks.findIndex(t => t.id === updatedTask.id);\n        if (taskIndex !== -1) {\n          updatedPlan.milestones[milestoneIndex].tasks[taskIndex] = {\n            ...updatedPlan.milestones[milestoneIndex].tasks[taskIndex],\n            ...updatedTask\n          };\n          setPlan(updatedPlan);\n        }\n      }\n      successSnackbar('Task updated successfully');\n    } catch (error) {\n      console.error('Error updating task:', error);\n      errorSnackbar('Failed to update task');\n    }\n  };\n\n  // Handle subtask update\n  const handleUpdateSubtask = async updatedSubtask => {\n    try {\n      console.log('Updating subtask:', updatedSubtask);\n\n      // Call API to update subtask\n      await updateSubtask(updatedSubtask);\n\n      // Create a copy of current plan to update\n      const updatedPlan = {\n        ...plan\n      };\n\n      // Find the task containing the subtask\n      let taskFound = false;\n\n      // Update subtask in state\n      for (let i = 0; i < updatedPlan.milestones.length; i++) {\n        const milestone = updatedPlan.milestones[i];\n        if (!milestone.tasks) continue;\n        for (let j = 0; j < milestone.tasks.length; j++) {\n          const task = milestone.tasks[j];\n          if (!task.subtasks) continue;\n\n          // Update subtask in task\n          const subtaskIndex = task.subtasks.findIndex(s => s.id === updatedSubtask.id);\n          if (subtaskIndex !== -1) {\n            // Update subtask\n            task.subtasks[subtaskIndex] = {\n              ...task.subtasks[subtaskIndex],\n              ...updatedSubtask\n            };\n            taskFound = true;\n            break;\n          }\n        }\n        if (taskFound) break;\n      }\n\n      // Update state with new plan\n      setPlan(updatedPlan);\n      successSnackbar('Subtask updated successfully');\n    } catch (error) {\n      console.error('Error updating subtask:', error);\n      errorSnackbar('Failed to update subtask');\n    }\n  };\n\n  // Handle add task\n  const handleAddTask = async newTask => {\n    try {\n      console.log('Adding new task:', newTask);\n      const response = await addTask(newTask);\n      console.log('Add task response:', response);\n\n      // Update local state\n      const updatedPlan = {\n        ...plan\n      };\n\n      // Find milestone to add new task\n      const milestoneIndex = updatedPlan.milestones.findIndex(m => m.id === newTask.milestone);\n      if (milestoneIndex !== -1) {\n        // Add new task to milestone\n        if (!updatedPlan.milestones[milestoneIndex].tasks) {\n          updatedPlan.milestones[milestoneIndex].tasks = [];\n        }\n\n        // Add the new task with data from response\n        const taskToAdd = response.data || {\n          ...newTask,\n          id: Date.now(),\n          // Temporary ID if response doesn't provide one\n          subtasks: []\n        };\n        updatedPlan.milestones[milestoneIndex].tasks.push(taskToAdd);\n        setPlan(updatedPlan);\n      }\n      successSnackbar('Task added successfully');\n    } catch (error) {\n      console.error('Error adding task:', error);\n      errorSnackbar('Failed to add task');\n    }\n  };\n\n  // Handle add subtask\n  const handleAddSubtask = async newSubtask => {\n    try {\n      console.log('Adding new subtask:', newSubtask);\n      const response = await addSubtask(newSubtask);\n      console.log('Add subtask response:', response);\n\n      // Update local state\n      const updatedPlan = {\n        ...plan\n      };\n\n      // Find task to add new subtask\n      let taskFound = false;\n      for (let i = 0; i < updatedPlan.milestones.length; i++) {\n        const milestone = updatedPlan.milestones[i];\n        if (!milestone.tasks) continue;\n        for (let j = 0; j < milestone.tasks.length; j++) {\n          const task = milestone.tasks[j];\n          if (task.slug === newSubtask.task) {\n            // Add new subtask to task\n            if (!task.subtasks) {\n              task.subtasks = [];\n            }\n\n            // Add the new subtask with data from response\n            const subtaskToAdd = response.data || {\n              ...newSubtask,\n              id: Date.now() // Temporary ID if response doesn't provide one\n            };\n            task.subtasks.push(subtaskToAdd);\n            taskFound = true;\n            break;\n          }\n        }\n        if (taskFound) break;\n      }\n      setPlan(updatedPlan);\n      successSnackbar('Subtask added successfully');\n    } catch (error) {\n      console.error('Error adding subtask:', error);\n      errorSnackbar('Failed to add subtask');\n    }\n  };\n\n  // Handle delete task\n  const handleDeleteTask = async taskToDelete => {\n    try {\n      console.log('Deleting task:', taskToDelete);\n      await deleteTask(taskToDelete.slug);\n\n      // Update local state\n      const updatedPlan = {\n        ...plan\n      };\n\n      // Find milestone containing the task to delete\n      const milestoneIndex = updatedPlan.milestones.findIndex(m => m.tasks && m.tasks.some(t => t.id === taskToDelete.id));\n      if (milestoneIndex !== -1) {\n        // Filter out the task to delete\n        updatedPlan.milestones[milestoneIndex].tasks = updatedPlan.milestones[milestoneIndex].tasks.filter(t => t.id !== taskToDelete.id);\n        setPlan(updatedPlan);\n      }\n      successSnackbar('Task deleted successfully');\n    } catch (error) {\n      console.error('Error deleting task:', error);\n      errorSnackbar('Failed to delete task');\n    }\n  };\n\n  // Handle delete subtask\n  const handleDeleteSubtask = async subtaskToDelete => {\n    try {\n      console.log('Deleting subtask:', subtaskToDelete);\n      await deleteSubtask(subtaskToDelete.slug);\n\n      // Update local state\n      const updatedPlan = {\n        ...plan\n      };\n\n      // Find task containing the subtask to delete\n      let taskFound = false;\n      for (let i = 0; i < updatedPlan.milestones.length; i++) {\n        const milestone = updatedPlan.milestones[i];\n        if (!milestone.tasks) continue;\n        for (let j = 0; j < milestone.tasks.length; j++) {\n          const task = milestone.tasks[j];\n          if (!task.subtasks) continue;\n\n          // Filter out the subtask to delete\n          const originalLength = task.subtasks.length;\n          task.subtasks = task.subtasks.filter(s => s.id !== subtaskToDelete.id);\n          if (task.subtasks.length < originalLength) {\n            taskFound = true;\n            break;\n          }\n        }\n        if (taskFound) break;\n      }\n      setPlan(updatedPlan);\n      toast.success('Subtask deleted successfully');\n    } catch (error) {\n      console.error('Error deleting subtask:', error);\n      toast.error('Failed to delete subtask');\n    }\n  };\n\n  // Handle assign members to task\n  const handleAssignMembers = async (taskToAssign, memberIds) => {\n    try {\n      console.log('Assigning members to task:', taskToAssign, memberIds);\n      await assignMembersToTask(taskToAssign.slug, memberIds);\n\n      // Update local state\n      const updatedPlan = {\n        ...plan\n      };\n\n      // Find the task to update\n      let taskFound = false;\n      for (let i = 0; i < updatedPlan.milestones.length; i++) {\n        const milestone = updatedPlan.milestones[i];\n        if (!milestone.tasks) continue;\n        for (let j = 0; j < milestone.tasks.length; j++) {\n          const task = milestone.tasks[j];\n          if (task.id === taskToAssign.id) {\n            // Update assignees\n            // Find user objects for the selected member IDs\n            const assignedMembers = memberIds.map(memberId => {\n              var _planInfo$invited_use;\n              // Check if it's the plan owner\n              if (planInfo.owner && planInfo.owner.id === memberId) {\n                return {\n                  id: planInfo.owner.id,\n                  first_name: planInfo.owner.first_name,\n                  last_name: planInfo.owner.last_name,\n                  email: planInfo.owner.email,\n                  avatar: planInfo.owner.avatar\n                };\n              }\n\n              // Check in invited users\n              const invitedUser = (_planInfo$invited_use = planInfo.invited_users) === null || _planInfo$invited_use === void 0 ? void 0 : _planInfo$invited_use.find(user => user.invited_user_info && user.invited_user_info.id === memberId);\n              if (invitedUser) {\n                return {\n                  id: invitedUser.invited_user_info.id,\n                  first_name: invitedUser.invited_user_info.first_name,\n                  last_name: invitedUser.invited_user_info.last_name,\n                  email: invitedUser.email,\n                  avatar: invitedUser.invited_user_info.avatar\n                };\n              }\n              return null;\n            }).filter(member => member !== null);\n            task.assignees = assignedMembers;\n            taskFound = true;\n            break;\n          }\n        }\n        if (taskFound) break;\n      }\n      setPlan(updatedPlan);\n      toast.success('Members assigned successfully');\n    } catch (error) {\n      console.error('Error assigning members to task:', error);\n      toast.error('Failed to assign members to task');\n    }\n  };\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(Container, {\n      className: styles.container,\n      children: /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"error\",\n        sx: {\n          mt: 4\n        },\n        children: \"An error occurred while loading plan information. Please try again later.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 450,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 449,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(Container, {\n    maxWidth: \"lg\",\n    className: styles.container,\n    sx: {\n      padding: '20px',\n      minHeight: 'calc(100vh - 65px)',\n      fontFamily: '\"Recursive Variable\", sans-serif'\n    },\n    children: [loading ? /*#__PURE__*/_jsxDEV(Box, {\n      className: styles.loadingContainer,\n      children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n        size: 60,\n        sx: {\n          color: mainYellowColor\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 469,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 468,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(Header, {\n        planInfo: planInfo,\n        viewMode: viewMode,\n        onViewModeChange: handleViewModeChange,\n        onOpenInviteDialog: () => openDialog('invite'),\n        onOpenDeleteDialog: () => openDialog('delete'),\n        onOpenOptOutDialog: () => openDialog('optOut')\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 474,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          borderBottom: 1,\n          borderColor: 'divider',\n          mb: 0.5,\n          mt: -1\n        },\n        children: /*#__PURE__*/_jsxDEV(Tabs, {\n          value: activeTab,\n          onChange: handleTabChange,\n          variant: \"scrollable\",\n          scrollButtons: \"auto\",\n          sx: {\n            minHeight: '36px',\n            '& .MuiTab-root': {\n              textTransform: 'none',\n              fontWeight: 600,\n              fontSize: '0.9rem',\n              minWidth: 'auto',\n              minHeight: '36px',\n              px: 2,\n              py: 0.5,\n              fontFamily: '\"Recursive Variable\", sans-serif'\n            },\n            '& .Mui-selected': {\n              color: `${mainYellowColor} !important`\n            },\n            '& .MuiTabs-indicator': {\n              backgroundColor: mainYellowColor,\n              height: '2px'\n            }\n          },\n          children: [/*#__PURE__*/_jsxDEV(Tab, {\n            icon: /*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"material-symbols:dashboard\",\n              width: 16,\n              height: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 512,\n              columnNumber: 23\n            }, this),\n            iconPosition: \"start\",\n            label: \"Overview\",\n            value: \"overview\",\n            sx: {\n              gap: '4px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 511,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            icon: /*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"material-symbols:flag\",\n              width: 16,\n              height: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 519,\n              columnNumber: 23\n            }, this),\n            iconPosition: \"start\",\n            label: \"Milestones\",\n            value: \"milestones\",\n            sx: {\n              gap: '4px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            icon: /*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"material-symbols:task\",\n              width: 16,\n              height: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 526,\n              columnNumber: 23\n            }, this),\n            iconPosition: \"start\",\n            label: \"Tasks\",\n            value: \"tasks\",\n            sx: {\n              gap: '4px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 525,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Tab, {\n            icon: /*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"material-symbols:people\",\n              width: 16,\n              height: 16\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 533,\n              columnNumber: 23\n            }, this),\n            iconPosition: \"start\",\n            label: \"Members\",\n            value: \"members\",\n            sx: {\n              gap: '4px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 484,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        className: styles.tabContent,\n        children: [activeTab === 'overview' && /*#__PURE__*/_jsxDEV(Box, {\n          className: styles.overviewTab,\n          sx: {\n            gap: 0.5\n          },\n          children: [/*#__PURE__*/_jsxDEV(Description, {\n            planInfo: planInfo\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 546,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              flexDirection: {\n                xs: 'column',\n                md: 'row'\n              },\n              gap: 1,\n              mb: 0.5\n            },\n            children: [/*#__PURE__*/_jsxDEV(Statistics, {\n              stats: stats\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 548,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Progress, {\n              stats: stats\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 549,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 547,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Members, {\n            invitedUsers: invitedUsers,\n            planOwner: planInfo === null || planInfo === void 0 ? void 0 : planInfo.user,\n            onInvite: () => openDialog('invite')\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 551,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(MilestoneOverview, {\n            milestones: planInfo === null || planInfo === void 0 ? void 0 : planInfo.milestones,\n            calculateMilestoneProgress: calculateMilestoneProgress,\n            getMilestoneStatus: getMilestoneStatus,\n            calculateTaskProgress: calculateTaskProgress,\n            getTaskStatus: getTaskStatus,\n            calculateSubtaskProgress: calculateSubtaskProgress,\n            getSubtaskStatus: getSubtaskStatus\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 556,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 545,\n          columnNumber: 15\n        }, this), activeTab === 'milestones' && /*#__PURE__*/_jsxDEV(MilestoneList, {\n          milestones: planInfo === null || planInfo === void 0 ? void 0 : planInfo.milestones,\n          viewMode: viewMode,\n          compact: false,\n          showSubtasks: true,\n          calculateMilestoneProgress: calculateMilestoneProgress,\n          getMilestoneStatus: getMilestoneStatus,\n          calculateTaskProgress: calculateTaskProgress,\n          getTaskStatus: getTaskStatus,\n          calculateSubtaskProgress: calculateSubtaskProgress,\n          getSubtaskStatus: getSubtaskStatus,\n          onUpdateMilestone: handleUpdateMilestone,\n          onUpdateTask: handleUpdateTask,\n          onUpdateSubtask: handleUpdateSubtask,\n          onAddTask: handleAddTask,\n          onAddSubtask: handleAddSubtask,\n          onDeleteTask: handleDeleteTask,\n          onDeleteSubtask: handleDeleteSubtask,\n          onAssignMembers: handleAssignMembers,\n          invitedUsers: (planInfo === null || planInfo === void 0 ? void 0 : planInfo.invited_users) || [],\n          planOwner: planInfo === null || planInfo === void 0 ? void 0 : planInfo.owner\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 569,\n          columnNumber: 15\n        }, this), activeTab === 'tasks' && /*#__PURE__*/_jsxDEV(TaskList, {\n          milestones: planInfo === null || planInfo === void 0 ? void 0 : planInfo.milestones,\n          onUpdateTask: handleUpdateTask,\n          onUpdateSubtask: handleUpdateSubtask,\n          onAddSubtask: handleAddSubtask,\n          onDeleteTask: handleDeleteTask,\n          onDeleteSubtask: handleDeleteSubtask\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 594,\n          columnNumber: 15\n        }, this), activeTab === 'members' && /*#__PURE__*/_jsxDEV(Members, {\n          invitedUsers: invitedUsers,\n          planOwner: planInfo === null || planInfo === void 0 ? void 0 : planInfo.user,\n          onInvite: () => openDialog('invite'),\n          expanded: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 605,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 543,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true), /*#__PURE__*/_jsxDEV(InviteDialog, {\n      open: dialogState.invite,\n      onClose: () => closeDialog('invite'),\n      onInvite: handleInviteUser,\n      planInfo: planInfo\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 617,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DeleteDialog, {\n      open: dialogState.delete,\n      onClose: () => closeDialog('delete'),\n      onDelete: handleDeletePlan\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 624,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmDialog, {\n      open: dialogState.deleteTask,\n      onClose: () => setDialogState(prev => ({\n        ...prev,\n        deleteTask: false\n      })),\n      onConfirm: () => handleDeleteTask(selectedTaskToDelete),\n      title: \"Delete Task\",\n      description: `Are you sure you want to delete the task \"${selectedTaskToDelete === null || selectedTaskToDelete === void 0 ? void 0 : selectedTaskToDelete.name}\"? This action cannot be undone.`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 630,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ConfirmDialog, {\n      open: dialogState.deleteSubtask,\n      onClose: () => setDialogState(prev => ({\n        ...prev,\n        deleteSubtask: false\n      })),\n      onConfirm: () => handleDeleteSubtask(selectedSubtaskToDelete),\n      title: \"Delete Subtask\",\n      description: `Are you sure you want to delete the subtask \"${selectedSubtaskToDelete === null || selectedSubtaskToDelete === void 0 ? void 0 : selectedSubtaskToDelete.name}\"? This action cannot be undone.`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 638,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 458,\n    columnNumber: 5\n  }, this);\n};\n_s(PlanDetail, \"2pkZBJAor0T26AdkEXoGB0yssbw=\", false, function () {\n  return [useParams, usePlanData, useViewMode];\n});\n_c = PlanDetail;\nexport default PlanDetail;\nvar _c;\n$RefreshReg$(_c, \"PlanDetail\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useParams", "Container", "Box", "Tabs", "Tab", "CircularProgress", "<PERSON><PERSON>", "Iconify", "mainYellowColor", "successSnackbar", "errorSnackbar", "toast", "Header", "Description", "Statistics", "Progress", "Members", "MilestoneList", "TaskList", "MilestoneOverview", "usePlanData", "useViewMode", "InviteDialog", "DeleteDialog", "OptOutDialog", "ConfirmDialog", "updateMilestone", "updateTask", "updateSubtask", "addTask", "addSubtask", "deleteTask", "deleteSubtask", "assignMembersToTask", "styles", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "PlanDetail", "_s", "param", "activeTab", "setActiveTab", "localStorage", "getItem", "plan", "setPlan", "dialogState", "setDialogState", "invite", "delete", "optOut", "selectedTaskToDelete", "selectedSubtaskToDelete", "planInfo", "loading", "error", "invitedUsers", "handleDeletePlan", "handleOptOutPlan", "handleInviteUser", "calculatePlanStats", "calculateSubtaskProgress", "getSubtaskStatus", "calculateTaskProgress", "getTaskStatus", "calculateMilestoneProgress", "getMilestoneStatus", "viewMode", "handleViewModeChange", "removeItem", "stats", "openDialog", "dialogName", "prev", "closeDialog", "handleTabChange", "event", "newValue", "setItem", "handleUpdateMilestone", "updatedMilestone", "console", "log", "updatedPlan", "milestoneIndex", "milestones", "findIndex", "m", "id", "handleUpdateTask", "updatedTask", "tasks", "some", "t", "taskIndex", "handleUpdateSubtask", "updatedSubtask", "taskFound", "i", "length", "milestone", "j", "task", "subtasks", "subtaskIndex", "s", "handleAddTask", "newTask", "response", "taskToAdd", "data", "Date", "now", "push", "handleAddSubtask", "newSubtask", "slug", "subtaskToAdd", "handleDeleteTask", "taskToDelete", "filter", "handleDeleteSubtask", "subtaskToDelete", "original<PERSON>ength", "success", "handleAssignMembers", "taskToAssign", "memberIds", "assignedMembers", "map", "memberId", "_planInfo$invited_use", "owner", "first_name", "last_name", "email", "avatar", "invitedUser", "invited_users", "find", "user", "invited_user_info", "member", "assignees", "className", "container", "children", "severity", "sx", "mt", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "max<PERSON><PERSON><PERSON>", "padding", "minHeight", "fontFamily", "loadingContainer", "size", "color", "onViewModeChange", "onOpenInviteDialog", "onOpenDeleteDialog", "onOpenOptOutDialog", "borderBottom", "borderColor", "mb", "value", "onChange", "variant", "scrollButtons", "textTransform", "fontWeight", "fontSize", "min<PERSON><PERSON><PERSON>", "px", "py", "backgroundColor", "height", "icon", "width", "iconPosition", "label", "gap", "tab<PERSON>ontent", "overviewTab", "display", "flexDirection", "xs", "md", "<PERSON><PERSON><PERSON><PERSON>", "onInvite", "compact", "showSubtasks", "onUpdateMilestone", "onUpdateTask", "onUpdateSubtask", "onAddTask", "onAddSubtask", "onDeleteTask", "onDeleteSubtask", "onAssignMembers", "expanded", "open", "onClose", "onDelete", "onConfirm", "title", "description", "name", "_c", "$RefreshReg$"], "sources": ["C:/ignition/ignition-ui/src/views/plan/detail/index.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport { useParams } from 'react-router-dom';\r\nimport { Container, Box, Tabs, Tab, CircularProgress, Alert } from '@mui/material';\r\nimport Iconify from 'components/Iconify/index';\r\nimport { mainYellowColor } from \"helpers/constants\";\r\nimport { successSnackbar, errorSnackbar } from 'components/Snackbar/index';\r\nimport { toast } from 'react-toastify';\r\n\r\n// Components\r\nimport Header from './components/Header';\r\nimport Description from './components/Description';\r\nimport Statistics from './components/Statistics';\r\nimport Progress from './components/Progress';\r\nimport Members from './components/Members';\r\nimport MilestoneList from './components/MilestoneList';\r\nimport TaskList from './components/TaskList';\r\nimport MilestoneOverview from './components/MilestoneOverview';\r\n\r\n// Hooks\r\nimport usePlanData from './hooks/usePlanData';\r\nimport useViewMode from './hooks/useViewMode';\r\n\r\n// Dialogs\r\nimport InviteDialog from './dialogs/InviteDialog';\r\nimport DeleteDialog from './dialogs/DeleteDialog';\r\nimport OptOutDialog from './dialogs/OptOutDialog';\r\nimport ConfirmDialog from './dialogs/ConfirmDialog';\r\n\r\n// Services\r\nimport {\r\n  updateMilestone,\r\n  updateTask,\r\n  updateSubtask,\r\n  addTask,\r\n  addSubtask,\r\n  deleteTask,\r\n  deleteSubtask,\r\n  assignMembersToTask\r\n} from '../services';\r\n\r\n// Styles\r\nimport styles from './styles.module.scss';\r\n\r\nconst PlanDetail = () => {\r\n  const { param } = useParams();\r\n  const [activeTab, setActiveTab] = useState(() => {\r\n    // Get active tab value from localStorage, default to 'overview' if not found\r\n    return localStorage.getItem(`plan_${param}_activeTab`) || 'overview';\r\n  });\r\n  const [plan, setPlan] = useState(null);\r\n  const [dialogState, setDialogState] = useState({\r\n    invite: false,\r\n    delete: false,\r\n    optOut: false,\r\n    deleteTask: false,\r\n    deleteSubtask: false\r\n  });\r\n  const [selectedTaskToDelete, ] = useState(null);\r\n  const [selectedSubtaskToDelete, ] = useState(null);\r\n\r\n  // Custom hooks\r\n  const {\r\n    planInfo,\r\n    loading,\r\n    error,\r\n    invitedUsers,\r\n    handleDeletePlan,\r\n    handleOptOutPlan,\r\n    handleInviteUser,\r\n    calculatePlanStats,\r\n    calculateSubtaskProgress,\r\n    getSubtaskStatus,\r\n    calculateTaskProgress,\r\n    getTaskStatus,\r\n    calculateMilestoneProgress,\r\n    getMilestoneStatus\r\n  } = usePlanData(param);\r\n\r\n  const {\r\n    viewMode,\r\n    handleViewModeChange\r\n  } = useViewMode();\r\n\r\n  // Update plan state when planInfo changes\r\n  useEffect(() => {\r\n    if (planInfo) {\r\n      setPlan(planInfo);\r\n    }\r\n  }, [planInfo]);\r\n\r\n  // Remove active tab from localStorage when component unmounts\r\n  useEffect(() => {\r\n    return () => {\r\n      localStorage.removeItem(`plan_${param}_activeTab`);\r\n    };\r\n  }, [param]);\r\n\r\n  // Calculate plan statistics\r\n  const stats = calculatePlanStats ? calculatePlanStats(planInfo) : null;\r\n\r\n  // Dialog handlers\r\n  const openDialog = (dialogName) => {\r\n    setDialogState(prev => ({ ...prev, [dialogName]: true }));\r\n  };\r\n\r\n  const closeDialog = (dialogName) => {\r\n    setDialogState(prev => ({ ...prev, [dialogName]: false }));\r\n  };\r\n\r\n  // Tab change handler\r\n  const handleTabChange = (event, newValue) => {\r\n    setActiveTab(newValue);\r\n    // Save active tab to localStorage\r\n    localStorage.setItem(`plan_${param}_activeTab`, newValue);\r\n  };\r\n\r\n  // Handle milestone update\r\n  const handleUpdateMilestone = async (updatedMilestone) => {\r\n    try {\r\n      console.log('Updating milestone:', updatedMilestone);\r\n      await updateMilestone(updatedMilestone);\r\n\r\n      // Update local state\r\n      const updatedPlan = { ...plan };\r\n      const milestoneIndex = updatedPlan.milestones.findIndex(m => m.id === updatedMilestone.id);\r\n\r\n      if (milestoneIndex !== -1) {\r\n        updatedPlan.milestones[milestoneIndex] = {\r\n          ...updatedPlan.milestones[milestoneIndex],\r\n          ...updatedMilestone\r\n        };\r\n        setPlan(updatedPlan);\r\n      }\r\n\r\n      successSnackbar('Milestone updated successfully');\r\n    } catch (error) {\r\n      console.error('Error updating milestone:', error);\r\n      errorSnackbar('Failed to update milestone');\r\n    }\r\n  };\r\n\r\n  // Handle task update\r\n  const handleUpdateTask = async (updatedTask) => {\r\n    try {\r\n      console.log('Updating task:', updatedTask);\r\n      await updateTask(updatedTask);\r\n\r\n      // Update local state\r\n      const updatedPlan = { ...plan };\r\n      const milestoneIndex = updatedPlan.milestones.findIndex(m =>\r\n        m.tasks && m.tasks.some(t => t.id === updatedTask.id)\r\n      );\r\n\r\n      if (milestoneIndex !== -1) {\r\n        const taskIndex = updatedPlan.milestones[milestoneIndex].tasks.findIndex(t => t.id === updatedTask.id);\r\n\r\n        if (taskIndex !== -1) {\r\n          updatedPlan.milestones[milestoneIndex].tasks[taskIndex] = {\r\n            ...updatedPlan.milestones[milestoneIndex].tasks[taskIndex],\r\n            ...updatedTask\r\n          };\r\n          setPlan(updatedPlan);\r\n        }\r\n      }\r\n\r\n      successSnackbar('Task updated successfully');\r\n    } catch (error) {\r\n      console.error('Error updating task:', error);\r\n      errorSnackbar('Failed to update task');\r\n    }\r\n  };\r\n\r\n  // Handle subtask update\r\n  const handleUpdateSubtask = async (updatedSubtask) => {\r\n    try {\r\n      console.log('Updating subtask:', updatedSubtask);\r\n\r\n      // Call API to update subtask\r\n      await updateSubtask(updatedSubtask);\r\n\r\n      // Create a copy of current plan to update\r\n      const updatedPlan = { ...plan };\r\n\r\n      // Find the task containing the subtask\r\n      let taskFound = false;\r\n\r\n      // Update subtask in state\r\n      for (let i = 0; i < updatedPlan.milestones.length; i++) {\r\n        const milestone = updatedPlan.milestones[i];\r\n        if (!milestone.tasks) continue;\r\n\r\n        for (let j = 0; j < milestone.tasks.length; j++) {\r\n          const task = milestone.tasks[j];\r\n          if (!task.subtasks) continue;\r\n\r\n          // Update subtask in task\r\n          const subtaskIndex = task.subtasks.findIndex(s => s.id === updatedSubtask.id);\r\n          if (subtaskIndex !== -1) {\r\n            // Update subtask\r\n            task.subtasks[subtaskIndex] = {\r\n              ...task.subtasks[subtaskIndex],\r\n              ...updatedSubtask\r\n            };\r\n            taskFound = true;\r\n            break;\r\n          }\r\n        }\r\n\r\n        if (taskFound) break;\r\n      }\r\n\r\n      // Update state with new plan\r\n      setPlan(updatedPlan);\r\n\r\n      successSnackbar('Subtask updated successfully');\r\n    } catch (error) {\r\n      console.error('Error updating subtask:', error);\r\n      errorSnackbar('Failed to update subtask');\r\n    }\r\n  };\r\n\r\n  // Handle add task\r\n  const handleAddTask = async (newTask) => {\r\n    try {\r\n      console.log('Adding new task:', newTask);\r\n      const response = await addTask(newTask);\r\n      console.log('Add task response:', response);\r\n\r\n      // Update local state\r\n      const updatedPlan = { ...plan };\r\n\r\n      // Find milestone to add new task\r\n      const milestoneIndex = updatedPlan.milestones.findIndex(m => m.id === newTask.milestone);\r\n\r\n      if (milestoneIndex !== -1) {\r\n        // Add new task to milestone\r\n        if (!updatedPlan.milestones[milestoneIndex].tasks) {\r\n          updatedPlan.milestones[milestoneIndex].tasks = [];\r\n        }\r\n\r\n        // Add the new task with data from response\r\n        const taskToAdd = response.data || {\r\n          ...newTask,\r\n          id: Date.now(), // Temporary ID if response doesn't provide one\r\n          subtasks: []\r\n        };\r\n\r\n        updatedPlan.milestones[milestoneIndex].tasks.push(taskToAdd);\r\n        setPlan(updatedPlan);\r\n      }\r\n\r\n      successSnackbar('Task added successfully');\r\n    } catch (error) {\r\n      console.error('Error adding task:', error);\r\n      errorSnackbar('Failed to add task');\r\n    }\r\n  };\r\n\r\n  // Handle add subtask\r\n  const handleAddSubtask = async (newSubtask) => {\r\n    try {\r\n      console.log('Adding new subtask:', newSubtask);\r\n      const response = await addSubtask(newSubtask);\r\n      console.log('Add subtask response:', response);\r\n\r\n      // Update local state\r\n      const updatedPlan = { ...plan };\r\n\r\n      // Find task to add new subtask\r\n      let taskFound = false;\r\n\r\n      for (let i = 0; i < updatedPlan.milestones.length; i++) {\r\n        const milestone = updatedPlan.milestones[i];\r\n        if (!milestone.tasks) continue;\r\n\r\n        for (let j = 0; j < milestone.tasks.length; j++) {\r\n          const task = milestone.tasks[j];\r\n\r\n          if (task.slug === newSubtask.task) {\r\n            // Add new subtask to task\r\n            if (!task.subtasks) {\r\n              task.subtasks = [];\r\n            }\r\n\r\n            // Add the new subtask with data from response\r\n            const subtaskToAdd = response.data || {\r\n              ...newSubtask,\r\n              id: Date.now() // Temporary ID if response doesn't provide one\r\n            };\r\n\r\n            task.subtasks.push(subtaskToAdd);\r\n            taskFound = true;\r\n            break;\r\n          }\r\n        }\r\n\r\n        if (taskFound) break;\r\n      }\r\n\r\n      setPlan(updatedPlan);\r\n      successSnackbar('Subtask added successfully');\r\n    } catch (error) {\r\n      console.error('Error adding subtask:', error);\r\n      errorSnackbar('Failed to add subtask');\r\n    }\r\n  };\r\n\r\n  // Handle delete task\r\n  const handleDeleteTask = async (taskToDelete) => {\r\n    try {\r\n      console.log('Deleting task:', taskToDelete);\r\n      await deleteTask(taskToDelete.slug);\r\n\r\n      // Update local state\r\n      const updatedPlan = { ...plan };\r\n\r\n      // Find milestone containing the task to delete\r\n      const milestoneIndex = updatedPlan.milestones.findIndex(m =>\r\n        m.tasks && m.tasks.some(t => t.id === taskToDelete.id)\r\n      );\r\n\r\n      if (milestoneIndex !== -1) {\r\n        // Filter out the task to delete\r\n        updatedPlan.milestones[milestoneIndex].tasks = updatedPlan.milestones[milestoneIndex].tasks.filter(\r\n          t => t.id !== taskToDelete.id\r\n        );\r\n\r\n        setPlan(updatedPlan);\r\n      }\r\n\r\n      successSnackbar('Task deleted successfully');\r\n    } catch (error) {\r\n      console.error('Error deleting task:', error);\r\n      errorSnackbar('Failed to delete task');\r\n    }\r\n  };\r\n\r\n  // Handle delete subtask\r\n  const handleDeleteSubtask = async (subtaskToDelete) => {\r\n    try {\r\n      console.log('Deleting subtask:', subtaskToDelete);\r\n      await deleteSubtask(subtaskToDelete.slug);\r\n\r\n      // Update local state\r\n      const updatedPlan = { ...plan };\r\n\r\n      // Find task containing the subtask to delete\r\n      let taskFound = false;\r\n\r\n      for (let i = 0; i < updatedPlan.milestones.length; i++) {\r\n        const milestone = updatedPlan.milestones[i];\r\n        if (!milestone.tasks) continue;\r\n\r\n        for (let j = 0; j < milestone.tasks.length; j++) {\r\n          const task = milestone.tasks[j];\r\n          if (!task.subtasks) continue;\r\n\r\n          // Filter out the subtask to delete\r\n          const originalLength = task.subtasks.length;\r\n          task.subtasks = task.subtasks.filter(s => s.id !== subtaskToDelete.id);\r\n\r\n          if (task.subtasks.length < originalLength) {\r\n            taskFound = true;\r\n            break;\r\n          }\r\n        }\r\n\r\n        if (taskFound) break;\r\n      }\r\n\r\n      setPlan(updatedPlan);\r\n      toast.success('Subtask deleted successfully');\r\n    } catch (error) {\r\n      console.error('Error deleting subtask:', error);\r\n      toast.error('Failed to delete subtask');\r\n    }\r\n  };\r\n\r\n  // Handle assign members to task\r\n  const handleAssignMembers = async (taskToAssign, memberIds) => {\r\n    try {\r\n      console.log('Assigning members to task:', taskToAssign, memberIds);\r\n      await assignMembersToTask(taskToAssign.slug, memberIds);\r\n\r\n      // Update local state\r\n      const updatedPlan = { ...plan };\r\n\r\n      // Find the task to update\r\n      let taskFound = false;\r\n\r\n      for (let i = 0; i < updatedPlan.milestones.length; i++) {\r\n        const milestone = updatedPlan.milestones[i];\r\n        if (!milestone.tasks) continue;\r\n\r\n        for (let j = 0; j < milestone.tasks.length; j++) {\r\n          const task = milestone.tasks[j];\r\n          if (task.id === taskToAssign.id) {\r\n            // Update assignees\r\n            // Find user objects for the selected member IDs\r\n            const assignedMembers = memberIds.map(memberId => {\r\n              // Check if it's the plan owner\r\n              if (planInfo.owner && planInfo.owner.id === memberId) {\r\n                return {\r\n                  id: planInfo.owner.id,\r\n                  first_name: planInfo.owner.first_name,\r\n                  last_name: planInfo.owner.last_name,\r\n                  email: planInfo.owner.email,\r\n                  avatar: planInfo.owner.avatar\r\n                };\r\n              }\r\n\r\n              // Check in invited users\r\n              const invitedUser = planInfo.invited_users?.find(\r\n                user => user.invited_user_info && user.invited_user_info.id === memberId\r\n              );\r\n\r\n              if (invitedUser) {\r\n                return {\r\n                  id: invitedUser.invited_user_info.id,\r\n                  first_name: invitedUser.invited_user_info.first_name,\r\n                  last_name: invitedUser.invited_user_info.last_name,\r\n                  email: invitedUser.email,\r\n                  avatar: invitedUser.invited_user_info.avatar\r\n                };\r\n              }\r\n\r\n              return null;\r\n            }).filter(member => member !== null);\r\n\r\n            task.assignees = assignedMembers;\r\n            taskFound = true;\r\n            break;\r\n          }\r\n        }\r\n\r\n        if (taskFound) break;\r\n      }\r\n\r\n      setPlan(updatedPlan);\r\n      toast.success('Members assigned successfully');\r\n    } catch (error) {\r\n      console.error('Error assigning members to task:', error);\r\n      toast.error('Failed to assign members to task');\r\n    }\r\n  };\r\n\r\n  if (error) {\r\n    return (\r\n      <Container className={styles.container}>\r\n        <Alert severity=\"error\" sx={{ mt: 4 }}>\r\n          An error occurred while loading plan information. Please try again later.\r\n        </Alert>\r\n      </Container>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <Container\r\n      maxWidth=\"lg\"\r\n      className={styles.container}\r\n      sx={{\r\n        padding: '20px',\r\n        minHeight: 'calc(100vh - 65px)',\r\n        fontFamily: '\"Recursive Variable\", sans-serif'\r\n      }}\r\n    >\r\n      {loading ? (\r\n        <Box className={styles.loadingContainer}>\r\n          <CircularProgress size={60} sx={{ color: mainYellowColor }} />\r\n        </Box>\r\n      ) : (\r\n        <>\r\n          {/* Header Section */}\r\n          <Header\r\n            planInfo={planInfo}\r\n            viewMode={viewMode}\r\n            onViewModeChange={handleViewModeChange}\r\n            onOpenInviteDialog={() => openDialog('invite')}\r\n            onOpenDeleteDialog={() => openDialog('delete')}\r\n            onOpenOptOutDialog={() => openDialog('optOut')}\r\n          />\r\n\r\n          {/* Tabs Navigation */}\r\n          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 0.5, mt: -1 }}>\r\n            <Tabs\r\n              value={activeTab}\r\n              onChange={handleTabChange}\r\n              variant=\"scrollable\"\r\n              scrollButtons=\"auto\"\r\n              sx={{\r\n                minHeight: '36px',\r\n                '& .MuiTab-root': {\r\n                  textTransform: 'none',\r\n                  fontWeight: 600,\r\n                  fontSize: '0.9rem',\r\n                  minWidth: 'auto',\r\n                  minHeight: '36px',\r\n                  px: 2,\r\n                  py: 0.5,\r\n                  fontFamily: '\"Recursive Variable\", sans-serif'\r\n                },\r\n                '& .Mui-selected': {\r\n                  color: `${mainYellowColor} !important`,\r\n                },\r\n                '& .MuiTabs-indicator': {\r\n                  backgroundColor: mainYellowColor,\r\n                  height: '2px'\r\n                }\r\n              }}\r\n            >\r\n              <Tab\r\n                icon={<Iconify icon=\"material-symbols:dashboard\" width={16} height={16} />}\r\n                iconPosition=\"start\"\r\n                label=\"Overview\"\r\n                value=\"overview\"\r\n                sx={{ gap: '4px' }}\r\n              />\r\n              <Tab\r\n                icon={<Iconify icon=\"material-symbols:flag\" width={16} height={16} />}\r\n                iconPosition=\"start\"\r\n                label=\"Milestones\"\r\n                value=\"milestones\"\r\n                sx={{ gap: '4px' }}\r\n              />\r\n              <Tab\r\n                icon={<Iconify icon=\"material-symbols:task\" width={16} height={16} />}\r\n                iconPosition=\"start\"\r\n                label=\"Tasks\"\r\n                value=\"tasks\"\r\n                sx={{ gap: '4px' }}\r\n              />\r\n              <Tab\r\n                icon={<Iconify icon=\"material-symbols:people\" width={16} height={16} />}\r\n                iconPosition=\"start\"\r\n                label=\"Members\"\r\n                value=\"members\"\r\n                sx={{ gap: '4px' }}\r\n              />\r\n            </Tabs>\r\n          </Box>\r\n\r\n          {/* Tab Content */}\r\n          <Box className={styles.tabContent}>\r\n            {activeTab === 'overview' && (\r\n              <Box className={styles.overviewTab} sx={{ gap: 0.5 }}>\r\n                <Description planInfo={planInfo} />\r\n                <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 1, mb: 0.5 }}>\r\n                  <Statistics stats={stats} />\r\n                  <Progress stats={stats} />\r\n                </Box>\r\n                <Members\r\n                  invitedUsers={invitedUsers}\r\n                  planOwner={planInfo?.user}\r\n                  onInvite={() => openDialog('invite')}\r\n                />\r\n                <MilestoneOverview\r\n                  milestones={planInfo?.milestones}\r\n                  calculateMilestoneProgress={calculateMilestoneProgress}\r\n                  getMilestoneStatus={getMilestoneStatus}\r\n                  calculateTaskProgress={calculateTaskProgress}\r\n                  getTaskStatus={getTaskStatus}\r\n                  calculateSubtaskProgress={calculateSubtaskProgress}\r\n                  getSubtaskStatus={getSubtaskStatus}\r\n                />\r\n              </Box>\r\n            )}\r\n\r\n            {activeTab === 'milestones' && (\r\n              <MilestoneList\r\n                milestones={planInfo?.milestones}\r\n                viewMode={viewMode}\r\n                compact={false}\r\n                showSubtasks={true}\r\n                calculateMilestoneProgress={calculateMilestoneProgress}\r\n                getMilestoneStatus={getMilestoneStatus}\r\n                calculateTaskProgress={calculateTaskProgress}\r\n                getTaskStatus={getTaskStatus}\r\n                calculateSubtaskProgress={calculateSubtaskProgress}\r\n                getSubtaskStatus={getSubtaskStatus}\r\n                onUpdateMilestone={handleUpdateMilestone}\r\n                onUpdateTask={handleUpdateTask}\r\n                onUpdateSubtask={handleUpdateSubtask}\r\n                onAddTask={handleAddTask}\r\n                onAddSubtask={handleAddSubtask}\r\n                onDeleteTask={handleDeleteTask}\r\n                onDeleteSubtask={handleDeleteSubtask}\r\n                onAssignMembers={handleAssignMembers}\r\n                invitedUsers={planInfo?.invited_users || []}\r\n                planOwner={planInfo?.owner}\r\n              />\r\n            )}\r\n\r\n            {activeTab === 'tasks' && (\r\n              <TaskList\r\n                milestones={planInfo?.milestones}\r\n                onUpdateTask={handleUpdateTask}\r\n                onUpdateSubtask={handleUpdateSubtask}\r\n                onAddSubtask={handleAddSubtask}\r\n                onDeleteTask={handleDeleteTask}\r\n                onDeleteSubtask={handleDeleteSubtask}\r\n              />\r\n            )}\r\n\r\n            {activeTab === 'members' && (\r\n              <Members\r\n                invitedUsers={invitedUsers}\r\n                planOwner={planInfo?.user}\r\n                onInvite={() => openDialog('invite')}\r\n                expanded={true}\r\n              />\r\n            )}\r\n          </Box>\r\n        </>\r\n      )}\r\n\r\n      {/* Dialogs */}\r\n      <InviteDialog\r\n        open={dialogState.invite}\r\n        onClose={() => closeDialog('invite')}\r\n        onInvite={handleInviteUser}\r\n        planInfo={planInfo}\r\n      />\r\n\r\n      <DeleteDialog\r\n        open={dialogState.delete}\r\n        onClose={() => closeDialog('delete')}\r\n        onDelete={handleDeletePlan}\r\n      />\r\n\r\n      <ConfirmDialog\r\n        open={dialogState.deleteTask}\r\n        onClose={() => setDialogState(prev => ({ ...prev, deleteTask: false }))}\r\n        onConfirm={() => handleDeleteTask(selectedTaskToDelete)}\r\n        title=\"Delete Task\"\r\n        description={`Are you sure you want to delete the task \"${selectedTaskToDelete?.name}\"? This action cannot be undone.`}\r\n      />\r\n\r\n      <ConfirmDialog\r\n        open={dialogState.deleteSubtask}\r\n        onClose={() => setDialogState(prev => ({ ...prev, deleteSubtask: false }))}\r\n        onConfirm={() => handleDeleteSubtask(selectedSubtaskToDelete)}\r\n        title=\"Delete Subtask\"\r\n        description={`Are you sure you want to delete the subtask \"${selectedSubtaskToDelete?.name}\"? This action cannot be undone.`}\r\n      />\r\n    </Container>\r\n  );\r\n};\r\n\r\nexport default PlanDetail;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,kBAAkB;AAC5C,SAASC,SAAS,EAAEC,GAAG,EAAEC,IAAI,EAAEC,GAAG,EAAEC,gBAAgB,EAAEC,KAAK,QAAQ,eAAe;AAClF,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,eAAe,EAAEC,aAAa,QAAQ,2BAA2B;AAC1E,SAASC,KAAK,QAAQ,gBAAgB;;AAEtC;AACA,OAAOC,MAAM,MAAM,qBAAqB;AACxC,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAOC,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,iBAAiB,MAAM,gCAAgC;;AAE9D;AACA,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,WAAW,MAAM,qBAAqB;;AAE7C;AACA,OAAOC,YAAY,MAAM,wBAAwB;AACjD,OAAOC,YAAY,MAAM,wBAAwB;AACjD,OAAOC,YAAY,MAAM,wBAAwB;AACjD,OAAOC,aAAa,MAAM,yBAAyB;;AAEnD;AACA,SACEC,eAAe,EACfC,UAAU,EACVC,aAAa,EACbC,OAAO,EACPC,UAAU,EACVC,UAAU,EACVC,aAAa,EACbC,mBAAmB,QACd,aAAa;;AAEpB;AACA,OAAOC,MAAM,MAAM,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1C,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM;IAAEC;EAAM,CAAC,GAAGzC,SAAS,CAAC,CAAC;EAC7B,MAAM,CAAC0C,SAAS,EAAEC,YAAY,CAAC,GAAG7C,QAAQ,CAAC,MAAM;IAC/C;IACA,OAAO8C,YAAY,CAACC,OAAO,CAAC,QAAQJ,KAAK,YAAY,CAAC,IAAI,UAAU;EACtE,CAAC,CAAC;EACF,MAAM,CAACK,IAAI,EAAEC,OAAO,CAAC,GAAGjD,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAC;IAC7CoD,MAAM,EAAE,KAAK;IACbC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAE,KAAK;IACbrB,UAAU,EAAE,KAAK;IACjBC,aAAa,EAAE;EACjB,CAAC,CAAC;EACF,MAAM,CAACqB,oBAAoB,CAAG,GAAGvD,QAAQ,CAAC,IAAI,CAAC;EAC/C,MAAM,CAACwD,uBAAuB,CAAG,GAAGxD,QAAQ,CAAC,IAAI,CAAC;;EAElD;EACA,MAAM;IACJyD,QAAQ;IACRC,OAAO;IACPC,KAAK;IACLC,YAAY;IACZC,gBAAgB;IAChBC,gBAAgB;IAChBC,gBAAgB;IAChBC,kBAAkB;IAClBC,wBAAwB;IACxBC,gBAAgB;IAChBC,qBAAqB;IACrBC,aAAa;IACbC,0BAA0B;IAC1BC;EACF,CAAC,GAAGhD,WAAW,CAACqB,KAAK,CAAC;EAEtB,MAAM;IACJ4B,QAAQ;IACRC;EACF,CAAC,GAAGjD,WAAW,CAAC,CAAC;;EAEjB;EACAtB,SAAS,CAAC,MAAM;IACd,IAAIwD,QAAQ,EAAE;MACZR,OAAO,CAACQ,QAAQ,CAAC;IACnB;EACF,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;;EAEd;EACAxD,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX6C,YAAY,CAAC2B,UAAU,CAAC,QAAQ9B,KAAK,YAAY,CAAC;IACpD,CAAC;EACH,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;;EAEX;EACA,MAAM+B,KAAK,GAAGV,kBAAkB,GAAGA,kBAAkB,CAACP,QAAQ,CAAC,GAAG,IAAI;;EAEtE;EACA,MAAMkB,UAAU,GAAIC,UAAU,IAAK;IACjCzB,cAAc,CAAC0B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACD,UAAU,GAAG;IAAK,CAAC,CAAC,CAAC;EAC3D,CAAC;EAED,MAAME,WAAW,GAAIF,UAAU,IAAK;IAClCzB,cAAc,CAAC0B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACD,UAAU,GAAG;IAAM,CAAC,CAAC,CAAC;EAC5D,CAAC;;EAED;EACA,MAAMG,eAAe,GAAGA,CAACC,KAAK,EAAEC,QAAQ,KAAK;IAC3CpC,YAAY,CAACoC,QAAQ,CAAC;IACtB;IACAnC,YAAY,CAACoC,OAAO,CAAC,QAAQvC,KAAK,YAAY,EAAEsC,QAAQ,CAAC;EAC3D,CAAC;;EAED;EACA,MAAME,qBAAqB,GAAG,MAAOC,gBAAgB,IAAK;IACxD,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEF,gBAAgB,CAAC;MACpD,MAAMxD,eAAe,CAACwD,gBAAgB,CAAC;;MAEvC;MACA,MAAMG,WAAW,GAAG;QAAE,GAAGvC;MAAK,CAAC;MAC/B,MAAMwC,cAAc,GAAGD,WAAW,CAACE,UAAU,CAACC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKR,gBAAgB,CAACQ,EAAE,CAAC;MAE1F,IAAIJ,cAAc,KAAK,CAAC,CAAC,EAAE;QACzBD,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,GAAG;UACvC,GAAGD,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC;UACzC,GAAGJ;QACL,CAAC;QACDnC,OAAO,CAACsC,WAAW,CAAC;MACtB;MAEA5E,eAAe,CAAC,gCAAgC,CAAC;IACnD,CAAC,CAAC,OAAOgD,KAAK,EAAE;MACd0B,OAAO,CAAC1B,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD/C,aAAa,CAAC,4BAA4B,CAAC;IAC7C;EACF,CAAC;;EAED;EACA,MAAMiF,gBAAgB,GAAG,MAAOC,WAAW,IAAK;IAC9C,IAAI;MACFT,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEQ,WAAW,CAAC;MAC1C,MAAMjE,UAAU,CAACiE,WAAW,CAAC;;MAE7B;MACA,MAAMP,WAAW,GAAG;QAAE,GAAGvC;MAAK,CAAC;MAC/B,MAAMwC,cAAc,GAAGD,WAAW,CAACE,UAAU,CAACC,SAAS,CAACC,CAAC,IACvDA,CAAC,CAACI,KAAK,IAAIJ,CAAC,CAACI,KAAK,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACL,EAAE,KAAKE,WAAW,CAACF,EAAE,CACtD,CAAC;MAED,IAAIJ,cAAc,KAAK,CAAC,CAAC,EAAE;QACzB,MAAMU,SAAS,GAAGX,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CAACO,KAAK,CAACL,SAAS,CAACO,CAAC,IAAIA,CAAC,CAACL,EAAE,KAAKE,WAAW,CAACF,EAAE,CAAC;QAEtG,IAAIM,SAAS,KAAK,CAAC,CAAC,EAAE;UACpBX,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CAACO,KAAK,CAACG,SAAS,CAAC,GAAG;YACxD,GAAGX,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CAACO,KAAK,CAACG,SAAS,CAAC;YAC1D,GAAGJ;UACL,CAAC;UACD7C,OAAO,CAACsC,WAAW,CAAC;QACtB;MACF;MAEA5E,eAAe,CAAC,2BAA2B,CAAC;IAC9C,CAAC,CAAC,OAAOgD,KAAK,EAAE;MACd0B,OAAO,CAAC1B,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C/C,aAAa,CAAC,uBAAuB,CAAC;IACxC;EACF,CAAC;;EAED;EACA,MAAMuF,mBAAmB,GAAG,MAAOC,cAAc,IAAK;IACpD,IAAI;MACFf,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEc,cAAc,CAAC;;MAEhD;MACA,MAAMtE,aAAa,CAACsE,cAAc,CAAC;;MAEnC;MACA,MAAMb,WAAW,GAAG;QAAE,GAAGvC;MAAK,CAAC;;MAE/B;MACA,IAAIqD,SAAS,GAAG,KAAK;;MAErB;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,WAAW,CAACE,UAAU,CAACc,MAAM,EAAED,CAAC,EAAE,EAAE;QACtD,MAAME,SAAS,GAAGjB,WAAW,CAACE,UAAU,CAACa,CAAC,CAAC;QAC3C,IAAI,CAACE,SAAS,CAACT,KAAK,EAAE;QAEtB,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,CAACT,KAAK,CAACQ,MAAM,EAAEE,CAAC,EAAE,EAAE;UAC/C,MAAMC,IAAI,GAAGF,SAAS,CAACT,KAAK,CAACU,CAAC,CAAC;UAC/B,IAAI,CAACC,IAAI,CAACC,QAAQ,EAAE;;UAEpB;UACA,MAAMC,YAAY,GAAGF,IAAI,CAACC,QAAQ,CAACjB,SAAS,CAACmB,CAAC,IAAIA,CAAC,CAACjB,EAAE,KAAKQ,cAAc,CAACR,EAAE,CAAC;UAC7E,IAAIgB,YAAY,KAAK,CAAC,CAAC,EAAE;YACvB;YACAF,IAAI,CAACC,QAAQ,CAACC,YAAY,CAAC,GAAG;cAC5B,GAAGF,IAAI,CAACC,QAAQ,CAACC,YAAY,CAAC;cAC9B,GAAGR;YACL,CAAC;YACDC,SAAS,GAAG,IAAI;YAChB;UACF;QACF;QAEA,IAAIA,SAAS,EAAE;MACjB;;MAEA;MACApD,OAAO,CAACsC,WAAW,CAAC;MAEpB5E,eAAe,CAAC,8BAA8B,CAAC;IACjD,CAAC,CAAC,OAAOgD,KAAK,EAAE;MACd0B,OAAO,CAAC1B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C/C,aAAa,CAAC,0BAA0B,CAAC;IAC3C;EACF,CAAC;;EAED;EACA,MAAMkG,aAAa,GAAG,MAAOC,OAAO,IAAK;IACvC,IAAI;MACF1B,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEyB,OAAO,CAAC;MACxC,MAAMC,QAAQ,GAAG,MAAMjF,OAAO,CAACgF,OAAO,CAAC;MACvC1B,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAE0B,QAAQ,CAAC;;MAE3C;MACA,MAAMzB,WAAW,GAAG;QAAE,GAAGvC;MAAK,CAAC;;MAE/B;MACA,MAAMwC,cAAc,GAAGD,WAAW,CAACE,UAAU,CAACC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,EAAE,KAAKmB,OAAO,CAACP,SAAS,CAAC;MAExF,IAAIhB,cAAc,KAAK,CAAC,CAAC,EAAE;QACzB;QACA,IAAI,CAACD,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CAACO,KAAK,EAAE;UACjDR,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CAACO,KAAK,GAAG,EAAE;QACnD;;QAEA;QACA,MAAMkB,SAAS,GAAGD,QAAQ,CAACE,IAAI,IAAI;UACjC,GAAGH,OAAO;UACVnB,EAAE,EAAEuB,IAAI,CAACC,GAAG,CAAC,CAAC;UAAE;UAChBT,QAAQ,EAAE;QACZ,CAAC;QAEDpB,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CAACO,KAAK,CAACsB,IAAI,CAACJ,SAAS,CAAC;QAC5DhE,OAAO,CAACsC,WAAW,CAAC;MACtB;MAEA5E,eAAe,CAAC,yBAAyB,CAAC;IAC5C,CAAC,CAAC,OAAOgD,KAAK,EAAE;MACd0B,OAAO,CAAC1B,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C/C,aAAa,CAAC,oBAAoB,CAAC;IACrC;EACF,CAAC;;EAED;EACA,MAAM0G,gBAAgB,GAAG,MAAOC,UAAU,IAAK;IAC7C,IAAI;MACFlC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEiC,UAAU,CAAC;MAC9C,MAAMP,QAAQ,GAAG,MAAMhF,UAAU,CAACuF,UAAU,CAAC;MAC7ClC,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAE0B,QAAQ,CAAC;;MAE9C;MACA,MAAMzB,WAAW,GAAG;QAAE,GAAGvC;MAAK,CAAC;;MAE/B;MACA,IAAIqD,SAAS,GAAG,KAAK;MAErB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,WAAW,CAACE,UAAU,CAACc,MAAM,EAAED,CAAC,EAAE,EAAE;QACtD,MAAME,SAAS,GAAGjB,WAAW,CAACE,UAAU,CAACa,CAAC,CAAC;QAC3C,IAAI,CAACE,SAAS,CAACT,KAAK,EAAE;QAEtB,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,CAACT,KAAK,CAACQ,MAAM,EAAEE,CAAC,EAAE,EAAE;UAC/C,MAAMC,IAAI,GAAGF,SAAS,CAACT,KAAK,CAACU,CAAC,CAAC;UAE/B,IAAIC,IAAI,CAACc,IAAI,KAAKD,UAAU,CAACb,IAAI,EAAE;YACjC;YACA,IAAI,CAACA,IAAI,CAACC,QAAQ,EAAE;cAClBD,IAAI,CAACC,QAAQ,GAAG,EAAE;YACpB;;YAEA;YACA,MAAMc,YAAY,GAAGT,QAAQ,CAACE,IAAI,IAAI;cACpC,GAAGK,UAAU;cACb3B,EAAE,EAAEuB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;YACjB,CAAC;YAEDV,IAAI,CAACC,QAAQ,CAACU,IAAI,CAACI,YAAY,CAAC;YAChCpB,SAAS,GAAG,IAAI;YAChB;UACF;QACF;QAEA,IAAIA,SAAS,EAAE;MACjB;MAEApD,OAAO,CAACsC,WAAW,CAAC;MACpB5E,eAAe,CAAC,4BAA4B,CAAC;IAC/C,CAAC,CAAC,OAAOgD,KAAK,EAAE;MACd0B,OAAO,CAAC1B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7C/C,aAAa,CAAC,uBAAuB,CAAC;IACxC;EACF,CAAC;;EAED;EACA,MAAM8G,gBAAgB,GAAG,MAAOC,YAAY,IAAK;IAC/C,IAAI;MACFtC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEqC,YAAY,CAAC;MAC3C,MAAM1F,UAAU,CAAC0F,YAAY,CAACH,IAAI,CAAC;;MAEnC;MACA,MAAMjC,WAAW,GAAG;QAAE,GAAGvC;MAAK,CAAC;;MAE/B;MACA,MAAMwC,cAAc,GAAGD,WAAW,CAACE,UAAU,CAACC,SAAS,CAACC,CAAC,IACvDA,CAAC,CAACI,KAAK,IAAIJ,CAAC,CAACI,KAAK,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACL,EAAE,KAAK+B,YAAY,CAAC/B,EAAE,CACvD,CAAC;MAED,IAAIJ,cAAc,KAAK,CAAC,CAAC,EAAE;QACzB;QACAD,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CAACO,KAAK,GAAGR,WAAW,CAACE,UAAU,CAACD,cAAc,CAAC,CAACO,KAAK,CAAC6B,MAAM,CAChG3B,CAAC,IAAIA,CAAC,CAACL,EAAE,KAAK+B,YAAY,CAAC/B,EAC7B,CAAC;QAED3C,OAAO,CAACsC,WAAW,CAAC;MACtB;MAEA5E,eAAe,CAAC,2BAA2B,CAAC;IAC9C,CAAC,CAAC,OAAOgD,KAAK,EAAE;MACd0B,OAAO,CAAC1B,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C/C,aAAa,CAAC,uBAAuB,CAAC;IACxC;EACF,CAAC;;EAED;EACA,MAAMiH,mBAAmB,GAAG,MAAOC,eAAe,IAAK;IACrD,IAAI;MACFzC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEwC,eAAe,CAAC;MACjD,MAAM5F,aAAa,CAAC4F,eAAe,CAACN,IAAI,CAAC;;MAEzC;MACA,MAAMjC,WAAW,GAAG;QAAE,GAAGvC;MAAK,CAAC;;MAE/B;MACA,IAAIqD,SAAS,GAAG,KAAK;MAErB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,WAAW,CAACE,UAAU,CAACc,MAAM,EAAED,CAAC,EAAE,EAAE;QACtD,MAAME,SAAS,GAAGjB,WAAW,CAACE,UAAU,CAACa,CAAC,CAAC;QAC3C,IAAI,CAACE,SAAS,CAACT,KAAK,EAAE;QAEtB,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,CAACT,KAAK,CAACQ,MAAM,EAAEE,CAAC,EAAE,EAAE;UAC/C,MAAMC,IAAI,GAAGF,SAAS,CAACT,KAAK,CAACU,CAAC,CAAC;UAC/B,IAAI,CAACC,IAAI,CAACC,QAAQ,EAAE;;UAEpB;UACA,MAAMoB,cAAc,GAAGrB,IAAI,CAACC,QAAQ,CAACJ,MAAM;UAC3CG,IAAI,CAACC,QAAQ,GAAGD,IAAI,CAACC,QAAQ,CAACiB,MAAM,CAACf,CAAC,IAAIA,CAAC,CAACjB,EAAE,KAAKkC,eAAe,CAAClC,EAAE,CAAC;UAEtE,IAAIc,IAAI,CAACC,QAAQ,CAACJ,MAAM,GAAGwB,cAAc,EAAE;YACzC1B,SAAS,GAAG,IAAI;YAChB;UACF;QACF;QAEA,IAAIA,SAAS,EAAE;MACjB;MAEApD,OAAO,CAACsC,WAAW,CAAC;MACpB1E,KAAK,CAACmH,OAAO,CAAC,8BAA8B,CAAC;IAC/C,CAAC,CAAC,OAAOrE,KAAK,EAAE;MACd0B,OAAO,CAAC1B,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C9C,KAAK,CAAC8C,KAAK,CAAC,0BAA0B,CAAC;IACzC;EACF,CAAC;;EAED;EACA,MAAMsE,mBAAmB,GAAG,MAAAA,CAAOC,YAAY,EAAEC,SAAS,KAAK;IAC7D,IAAI;MACF9C,OAAO,CAACC,GAAG,CAAC,4BAA4B,EAAE4C,YAAY,EAAEC,SAAS,CAAC;MAClE,MAAMhG,mBAAmB,CAAC+F,YAAY,CAACV,IAAI,EAAEW,SAAS,CAAC;;MAEvD;MACA,MAAM5C,WAAW,GAAG;QAAE,GAAGvC;MAAK,CAAC;;MAE/B;MACA,IAAIqD,SAAS,GAAG,KAAK;MAErB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGf,WAAW,CAACE,UAAU,CAACc,MAAM,EAAED,CAAC,EAAE,EAAE;QACtD,MAAME,SAAS,GAAGjB,WAAW,CAACE,UAAU,CAACa,CAAC,CAAC;QAC3C,IAAI,CAACE,SAAS,CAACT,KAAK,EAAE;QAEtB,KAAK,IAAIU,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,SAAS,CAACT,KAAK,CAACQ,MAAM,EAAEE,CAAC,EAAE,EAAE;UAC/C,MAAMC,IAAI,GAAGF,SAAS,CAACT,KAAK,CAACU,CAAC,CAAC;UAC/B,IAAIC,IAAI,CAACd,EAAE,KAAKsC,YAAY,CAACtC,EAAE,EAAE;YAC/B;YACA;YACA,MAAMwC,eAAe,GAAGD,SAAS,CAACE,GAAG,CAACC,QAAQ,IAAI;cAAA,IAAAC,qBAAA;cAChD;cACA,IAAI9E,QAAQ,CAAC+E,KAAK,IAAI/E,QAAQ,CAAC+E,KAAK,CAAC5C,EAAE,KAAK0C,QAAQ,EAAE;gBACpD,OAAO;kBACL1C,EAAE,EAAEnC,QAAQ,CAAC+E,KAAK,CAAC5C,EAAE;kBACrB6C,UAAU,EAAEhF,QAAQ,CAAC+E,KAAK,CAACC,UAAU;kBACrCC,SAAS,EAAEjF,QAAQ,CAAC+E,KAAK,CAACE,SAAS;kBACnCC,KAAK,EAAElF,QAAQ,CAAC+E,KAAK,CAACG,KAAK;kBAC3BC,MAAM,EAAEnF,QAAQ,CAAC+E,KAAK,CAACI;gBACzB,CAAC;cACH;;cAEA;cACA,MAAMC,WAAW,IAAAN,qBAAA,GAAG9E,QAAQ,CAACqF,aAAa,cAAAP,qBAAA,uBAAtBA,qBAAA,CAAwBQ,IAAI,CAC9CC,IAAI,IAAIA,IAAI,CAACC,iBAAiB,IAAID,IAAI,CAACC,iBAAiB,CAACrD,EAAE,KAAK0C,QAClE,CAAC;cAED,IAAIO,WAAW,EAAE;gBACf,OAAO;kBACLjD,EAAE,EAAEiD,WAAW,CAACI,iBAAiB,CAACrD,EAAE;kBACpC6C,UAAU,EAAEI,WAAW,CAACI,iBAAiB,CAACR,UAAU;kBACpDC,SAAS,EAAEG,WAAW,CAACI,iBAAiB,CAACP,SAAS;kBAClDC,KAAK,EAAEE,WAAW,CAACF,KAAK;kBACxBC,MAAM,EAAEC,WAAW,CAACI,iBAAiB,CAACL;gBACxC,CAAC;cACH;cAEA,OAAO,IAAI;YACb,CAAC,CAAC,CAAChB,MAAM,CAACsB,MAAM,IAAIA,MAAM,KAAK,IAAI,CAAC;YAEpCxC,IAAI,CAACyC,SAAS,GAAGf,eAAe;YAChC/B,SAAS,GAAG,IAAI;YAChB;UACF;QACF;QAEA,IAAIA,SAAS,EAAE;MACjB;MAEApD,OAAO,CAACsC,WAAW,CAAC;MACpB1E,KAAK,CAACmH,OAAO,CAAC,+BAA+B,CAAC;IAChD,CAAC,CAAC,OAAOrE,KAAK,EAAE;MACd0B,OAAO,CAAC1B,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD9C,KAAK,CAAC8C,KAAK,CAAC,kCAAkC,CAAC;IACjD;EACF,CAAC;EAED,IAAIA,KAAK,EAAE;IACT,oBACErB,OAAA,CAACnC,SAAS;MAACiJ,SAAS,EAAEhH,MAAM,CAACiH,SAAU;MAAAC,QAAA,eACrChH,OAAA,CAAC9B,KAAK;QAAC+I,QAAQ,EAAC,OAAO;QAACC,EAAE,EAAE;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAH,QAAA,EAAC;MAEvC;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEhB;EAEA,oBACEvH,OAAA,CAACnC,SAAS;IACR2J,QAAQ,EAAC,IAAI;IACbV,SAAS,EAAEhH,MAAM,CAACiH,SAAU;IAC5BG,EAAE,EAAE;MACFO,OAAO,EAAE,MAAM;MACfC,SAAS,EAAE,oBAAoB;MAC/BC,UAAU,EAAE;IACd,CAAE;IAAAX,QAAA,GAED5F,OAAO,gBACNpB,OAAA,CAAClC,GAAG;MAACgJ,SAAS,EAAEhH,MAAM,CAAC8H,gBAAiB;MAAAZ,QAAA,eACtChH,OAAA,CAAC/B,gBAAgB;QAAC4J,IAAI,EAAE,EAAG;QAACX,EAAE,EAAE;UAAEY,KAAK,EAAE1J;QAAgB;MAAE;QAAAgJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3D,CAAC,gBAENvH,OAAA,CAAAE,SAAA;MAAA8G,QAAA,gBAEEhH,OAAA,CAACxB,MAAM;QACL2C,QAAQ,EAAEA,QAAS;QACnBc,QAAQ,EAAEA,QAAS;QACnB8F,gBAAgB,EAAE7F,oBAAqB;QACvC8F,kBAAkB,EAAEA,CAAA,KAAM3F,UAAU,CAAC,QAAQ,CAAE;QAC/C4F,kBAAkB,EAAEA,CAAA,KAAM5F,UAAU,CAAC,QAAQ,CAAE;QAC/C6F,kBAAkB,EAAEA,CAAA,KAAM7F,UAAU,CAAC,QAAQ;MAAE;QAAA+E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD,CAAC,eAGFvH,OAAA,CAAClC,GAAG;QAACoJ,EAAE,EAAE;UAAEiB,YAAY,EAAE,CAAC;UAAEC,WAAW,EAAE,SAAS;UAAEC,EAAE,EAAE,GAAG;UAAElB,EAAE,EAAE,CAAC;QAAE,CAAE;QAAAH,QAAA,eACpEhH,OAAA,CAACjC,IAAI;UACHuK,KAAK,EAAEhI,SAAU;UACjBiI,QAAQ,EAAE9F,eAAgB;UAC1B+F,OAAO,EAAC,YAAY;UACpBC,aAAa,EAAC,MAAM;UACpBvB,EAAE,EAAE;YACFQ,SAAS,EAAE,MAAM;YACjB,gBAAgB,EAAE;cAChBgB,aAAa,EAAE,MAAM;cACrBC,UAAU,EAAE,GAAG;cACfC,QAAQ,EAAE,QAAQ;cAClBC,QAAQ,EAAE,MAAM;cAChBnB,SAAS,EAAE,MAAM;cACjBoB,EAAE,EAAE,CAAC;cACLC,EAAE,EAAE,GAAG;cACPpB,UAAU,EAAE;YACd,CAAC;YACD,iBAAiB,EAAE;cACjBG,KAAK,EAAE,GAAG1J,eAAe;YAC3B,CAAC;YACD,sBAAsB,EAAE;cACtB4K,eAAe,EAAE5K,eAAe;cAChC6K,MAAM,EAAE;YACV;UACF,CAAE;UAAAjC,QAAA,gBAEFhH,OAAA,CAAChC,GAAG;YACFkL,IAAI,eAAElJ,OAAA,CAAC7B,OAAO;cAAC+K,IAAI,EAAC,4BAA4B;cAACC,KAAK,EAAE,EAAG;cAACF,MAAM,EAAE;YAAG;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3E6B,YAAY,EAAC,OAAO;YACpBC,KAAK,EAAC,UAAU;YAChBf,KAAK,EAAC,UAAU;YAChBpB,EAAE,EAAE;cAAEoC,GAAG,EAAE;YAAM;UAAE;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eACFvH,OAAA,CAAChC,GAAG;YACFkL,IAAI,eAAElJ,OAAA,CAAC7B,OAAO;cAAC+K,IAAI,EAAC,uBAAuB;cAACC,KAAK,EAAE,EAAG;cAACF,MAAM,EAAE;YAAG;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACtE6B,YAAY,EAAC,OAAO;YACpBC,KAAK,EAAC,YAAY;YAClBf,KAAK,EAAC,YAAY;YAClBpB,EAAE,EAAE;cAAEoC,GAAG,EAAE;YAAM;UAAE;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eACFvH,OAAA,CAAChC,GAAG;YACFkL,IAAI,eAAElJ,OAAA,CAAC7B,OAAO;cAAC+K,IAAI,EAAC,uBAAuB;cAACC,KAAK,EAAE,EAAG;cAACF,MAAM,EAAE;YAAG;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACtE6B,YAAY,EAAC,OAAO;YACpBC,KAAK,EAAC,OAAO;YACbf,KAAK,EAAC,OAAO;YACbpB,EAAE,EAAE;cAAEoC,GAAG,EAAE;YAAM;UAAE;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC,eACFvH,OAAA,CAAChC,GAAG;YACFkL,IAAI,eAAElJ,OAAA,CAAC7B,OAAO;cAAC+K,IAAI,EAAC,yBAAyB;cAACC,KAAK,EAAE,EAAG;cAACF,MAAM,EAAE;YAAG;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACxE6B,YAAY,EAAC,OAAO;YACpBC,KAAK,EAAC,SAAS;YACff,KAAK,EAAC,SAAS;YACfpB,EAAE,EAAE;cAAEoC,GAAG,EAAE;YAAM;UAAE;YAAAlC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNvH,OAAA,CAAClC,GAAG;QAACgJ,SAAS,EAAEhH,MAAM,CAACyJ,UAAW;QAAAvC,QAAA,GAC/B1G,SAAS,KAAK,UAAU,iBACvBN,OAAA,CAAClC,GAAG;UAACgJ,SAAS,EAAEhH,MAAM,CAAC0J,WAAY;UAACtC,EAAE,EAAE;YAAEoC,GAAG,EAAE;UAAI,CAAE;UAAAtC,QAAA,gBACnDhH,OAAA,CAACvB,WAAW;YAAC0C,QAAQ,EAAEA;UAAS;YAAAiG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACnCvH,OAAA,CAAClC,GAAG;YAACoJ,EAAE,EAAE;cAAEuC,OAAO,EAAE,MAAM;cAAEC,aAAa,EAAE;gBAAEC,EAAE,EAAE,QAAQ;gBAAEC,EAAE,EAAE;cAAM,CAAC;cAAEN,GAAG,EAAE,CAAC;cAAEjB,EAAE,EAAE;YAAI,CAAE;YAAArB,QAAA,gBACxFhH,OAAA,CAACtB,UAAU;cAAC0D,KAAK,EAAEA;YAAM;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5BvH,OAAA,CAACrB,QAAQ;cAACyD,KAAK,EAAEA;YAAM;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvB,CAAC,eACNvH,OAAA,CAACpB,OAAO;YACN0C,YAAY,EAAEA,YAAa;YAC3BuI,SAAS,EAAE1I,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuF,IAAK;YAC1BoD,QAAQ,EAAEA,CAAA,KAAMzH,UAAU,CAAC,QAAQ;UAAE;YAAA+E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACFvH,OAAA,CAACjB,iBAAiB;YAChBoE,UAAU,EAAEhC,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEgC,UAAW;YACjCpB,0BAA0B,EAAEA,0BAA2B;YACvDC,kBAAkB,EAAEA,kBAAmB;YACvCH,qBAAqB,EAAEA,qBAAsB;YAC7CC,aAAa,EAAEA,aAAc;YAC7BH,wBAAwB,EAAEA,wBAAyB;YACnDC,gBAAgB,EAAEA;UAAiB;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN,EAEAjH,SAAS,KAAK,YAAY,iBACzBN,OAAA,CAACnB,aAAa;UACZsE,UAAU,EAAEhC,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEgC,UAAW;UACjClB,QAAQ,EAAEA,QAAS;UACnB8H,OAAO,EAAE,KAAM;UACfC,YAAY,EAAE,IAAK;UACnBjI,0BAA0B,EAAEA,0BAA2B;UACvDC,kBAAkB,EAAEA,kBAAmB;UACvCH,qBAAqB,EAAEA,qBAAsB;UAC7CC,aAAa,EAAEA,aAAc;UAC7BH,wBAAwB,EAAEA,wBAAyB;UACnDC,gBAAgB,EAAEA,gBAAiB;UACnCqI,iBAAiB,EAAEpH,qBAAsB;UACzCqH,YAAY,EAAE3G,gBAAiB;UAC/B4G,eAAe,EAAEtG,mBAAoB;UACrCuG,SAAS,EAAE5F,aAAc;UACzB6F,YAAY,EAAErF,gBAAiB;UAC/BsF,YAAY,EAAElF,gBAAiB;UAC/BmF,eAAe,EAAEhF,mBAAoB;UACrCiF,eAAe,EAAE7E,mBAAoB;UACrCrE,YAAY,EAAE,CAAAH,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEqF,aAAa,KAAI,EAAG;UAC5CqD,SAAS,EAAE1I,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE+E;QAAM;UAAAkB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5B,CACF,EAEAjH,SAAS,KAAK,OAAO,iBACpBN,OAAA,CAAClB,QAAQ;UACPqE,UAAU,EAAEhC,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEgC,UAAW;UACjC+G,YAAY,EAAE3G,gBAAiB;UAC/B4G,eAAe,EAAEtG,mBAAoB;UACrCwG,YAAY,EAAErF,gBAAiB;UAC/BsF,YAAY,EAAElF,gBAAiB;UAC/BmF,eAAe,EAAEhF;QAAoB;UAAA6B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CACF,EAEAjH,SAAS,KAAK,SAAS,iBACtBN,OAAA,CAACpB,OAAO;UACN0C,YAAY,EAAEA,YAAa;UAC3BuI,SAAS,EAAE1I,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEuF,IAAK;UAC1BoD,QAAQ,EAAEA,CAAA,KAAMzH,UAAU,CAAC,QAAQ,CAAE;UACrCoI,QAAQ,EAAE;QAAK;UAAArD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA,eACN,CACH,eAGDvH,OAAA,CAACd,YAAY;MACXwL,IAAI,EAAE9J,WAAW,CAACE,MAAO;MACzB6J,OAAO,EAAEA,CAAA,KAAMnI,WAAW,CAAC,QAAQ,CAAE;MACrCsH,QAAQ,EAAErI,gBAAiB;MAC3BN,QAAQ,EAAEA;IAAS;MAAAiG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CAAC,eAEFvH,OAAA,CAACb,YAAY;MACXuL,IAAI,EAAE9J,WAAW,CAACG,MAAO;MACzB4J,OAAO,EAAEA,CAAA,KAAMnI,WAAW,CAAC,QAAQ,CAAE;MACrCoI,QAAQ,EAAErJ;IAAiB;MAAA6F,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,eAEFvH,OAAA,CAACX,aAAa;MACZqL,IAAI,EAAE9J,WAAW,CAACjB,UAAW;MAC7BgL,OAAO,EAAEA,CAAA,KAAM9J,cAAc,CAAC0B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE5C,UAAU,EAAE;MAAM,CAAC,CAAC,CAAE;MACxEkL,SAAS,EAAEA,CAAA,KAAMzF,gBAAgB,CAACnE,oBAAoB,CAAE;MACxD6J,KAAK,EAAC,aAAa;MACnBC,WAAW,EAAE,6CAA6C9J,oBAAoB,aAApBA,oBAAoB,uBAApBA,oBAAoB,CAAE+J,IAAI;IAAmC;MAAA5D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxH,CAAC,eAEFvH,OAAA,CAACX,aAAa;MACZqL,IAAI,EAAE9J,WAAW,CAAChB,aAAc;MAChC+K,OAAO,EAAEA,CAAA,KAAM9J,cAAc,CAAC0B,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE3C,aAAa,EAAE;MAAM,CAAC,CAAC,CAAE;MAC3EiL,SAAS,EAAEA,CAAA,KAAMtF,mBAAmB,CAACrE,uBAAuB,CAAE;MAC9D4J,KAAK,EAAC,gBAAgB;MACtBC,WAAW,EAAE,gDAAgD7J,uBAAuB,aAAvBA,uBAAuB,uBAAvBA,uBAAuB,CAAE8J,IAAI;IAAmC;MAAA5D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9H,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEhB,CAAC;AAACnH,EAAA,CA3lBID,UAAU;EAAA,QACIvC,SAAS,EAgCvBoB,WAAW,EAKXC,WAAW;AAAA;AAAAgM,EAAA,GAtCX9K,UAAU;AA6lBhB,eAAeA,UAAU;AAAC,IAAA8K,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}