{"ast": null, "code": "'use strict';\n\nvar define = require('define-data-property');\nvar hasDescriptors = require('has-property-descriptors')();\nvar functionsHaveConfigurableNames = require('functions-have-names').functionsHaveConfigurableNames();\nvar $TypeError = require('es-errors/type');\n\n/** @type {import('.')} */\nmodule.exports = function setFunctionName(fn, name) {\n  if (typeof fn !== 'function') {\n    throw new $TypeError('`fn` is not a function');\n  }\n  var loose = arguments.length > 2 && !!arguments[2];\n  if (!loose || functionsHaveConfigurableNames) {\n    if (hasDescriptors) {\n      define(/** @type {Parameters<define>[0]} */fn, 'name', name, true, true);\n    } else {\n      define(/** @type {Parameters<define>[0]} */fn, 'name', name);\n    }\n  }\n  return fn;\n};", "map": {"version": 3, "names": ["define", "require", "hasDescriptors", "functionsHaveConfigurableNames", "$TypeError", "module", "exports", "setFunctionName", "fn", "name", "loose", "arguments", "length"], "sources": ["C:/ignition/ignition-ui/node_modules/set-function-name/index.js"], "sourcesContent": ["'use strict';\n\nvar define = require('define-data-property');\nvar hasDescriptors = require('has-property-descriptors')();\nvar functionsHaveConfigurableNames = require('functions-have-names').functionsHaveConfigurableNames();\n\nvar $TypeError = require('es-errors/type');\n\n/** @type {import('.')} */\nmodule.exports = function setFunctionName(fn, name) {\n\tif (typeof fn !== 'function') {\n\t\tthrow new $TypeError('`fn` is not a function');\n\t}\n\tvar loose = arguments.length > 2 && !!arguments[2];\n\tif (!loose || functionsHaveConfigurableNames) {\n\t\tif (hasDescriptors) {\n\t\t\tdefine(/** @type {Parameters<define>[0]} */ (fn), 'name', name, true, true);\n\t\t} else {\n\t\t\tdefine(/** @type {Parameters<define>[0]} */ (fn), 'name', name);\n\t\t}\n\t}\n\treturn fn;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,MAAM,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AAC5C,IAAIC,cAAc,GAAGD,OAAO,CAAC,0BAA0B,CAAC,CAAC,CAAC;AAC1D,IAAIE,8BAA8B,GAAGF,OAAO,CAAC,sBAAsB,CAAC,CAACE,8BAA8B,CAAC,CAAC;AAErG,IAAIC,UAAU,GAAGH,OAAO,CAAC,gBAAgB,CAAC;;AAE1C;AACAI,MAAM,CAACC,OAAO,GAAG,SAASC,eAAeA,CAACC,EAAE,EAAEC,IAAI,EAAE;EACnD,IAAI,OAAOD,EAAE,KAAK,UAAU,EAAE;IAC7B,MAAM,IAAIJ,UAAU,CAAC,wBAAwB,CAAC;EAC/C;EACA,IAAIM,KAAK,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAI,CAAC,CAACD,SAAS,CAAC,CAAC,CAAC;EAClD,IAAI,CAACD,KAAK,IAAIP,8BAA8B,EAAE;IAC7C,IAAID,cAAc,EAAE;MACnBF,MAAM,CAAC,oCAAsCQ,EAAE,EAAG,MAAM,EAAEC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAC5E,CAAC,MAAM;MACNT,MAAM,CAAC,oCAAsCQ,EAAE,EAAG,MAAM,EAAEC,IAAI,CAAC;IAChE;EACD;EACA,OAAOD,EAAE;AACV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}