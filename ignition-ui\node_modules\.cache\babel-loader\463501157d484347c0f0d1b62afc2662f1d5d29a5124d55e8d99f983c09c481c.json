{"ast": null, "code": "'use strict';\n\nvar origSymbol = typeof Symbol !== 'undefined' && Symbol;\nvar hasSymbolSham = require('./shams');\n\n/** @type {import('.')} */\nmodule.exports = function hasNativeSymbols() {\n  if (typeof origSymbol !== 'function') {\n    return false;\n  }\n  if (typeof Symbol !== 'function') {\n    return false;\n  }\n  if (typeof origSymbol('foo') !== 'symbol') {\n    return false;\n  }\n  if (typeof Symbol('bar') !== 'symbol') {\n    return false;\n  }\n  return hasSymbolSham();\n};", "map": {"version": 3, "names": ["origSymbol", "Symbol", "hasSymbolSham", "require", "module", "exports", "hasNativeSymbols"], "sources": ["C:/ignition/ignition-ui/node_modules/has-symbols/index.js"], "sourcesContent": ["'use strict';\n\nvar origSymbol = typeof Symbol !== 'undefined' && Symbol;\nvar hasSymbolSham = require('./shams');\n\n/** @type {import('.')} */\nmodule.exports = function hasNativeSymbols() {\n\tif (typeof origSymbol !== 'function') { return false; }\n\tif (typeof Symbol !== 'function') { return false; }\n\tif (typeof origSymbol('foo') !== 'symbol') { return false; }\n\tif (typeof Symbol('bar') !== 'symbol') { return false; }\n\n\treturn hasSymbolSham();\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,UAAU,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM;AACxD,IAAIC,aAAa,GAAGC,OAAO,CAAC,SAAS,CAAC;;AAEtC;AACAC,MAAM,CAACC,OAAO,GAAG,SAASC,gBAAgBA,CAAA,EAAG;EAC5C,IAAI,OAAON,UAAU,KAAK,UAAU,EAAE;IAAE,OAAO,KAAK;EAAE;EACtD,IAAI,OAAOC,MAAM,KAAK,UAAU,EAAE;IAAE,OAAO,KAAK;EAAE;EAClD,IAAI,OAAOD,UAAU,CAAC,KAAK,CAAC,KAAK,QAAQ,EAAE;IAAE,OAAO,KAAK;EAAE;EAC3D,IAAI,OAAOC,MAAM,CAAC,KAAK,CAAC,KAAK,QAAQ,EAAE;IAAE,OAAO,KAAK;EAAE;EAEvD,OAAOC,aAAa,CAAC,CAAC;AACvB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}