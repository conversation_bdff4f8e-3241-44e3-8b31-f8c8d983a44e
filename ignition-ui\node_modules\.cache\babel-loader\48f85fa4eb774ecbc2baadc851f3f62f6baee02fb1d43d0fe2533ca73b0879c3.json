{"ast": null, "code": "import * as React from 'react';\nexport const TransitionContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== 'production') {\n  TransitionContext.displayName = 'TransitionContext';\n}", "map": {"version": 3, "names": ["React", "TransitionContext", "createContext", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/useTransition/TransitionContext.js"], "sourcesContent": ["import * as React from 'react';\nexport const TransitionContext = /*#__PURE__*/React.createContext(null);\nif (process.env.NODE_ENV !== 'production') {\n  TransitionContext.displayName = 'TransitionContext';\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,MAAMC,iBAAiB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,IAAI,CAAC;AACvE,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,iBAAiB,CAACK,WAAW,GAAG,mBAAmB;AACrD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}