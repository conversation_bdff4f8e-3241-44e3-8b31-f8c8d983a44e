{"ast": null, "code": "import { unstable_generateUtilityClass, unstable_generateUtilityClasses } from '@mui/utils';\nexport { unstable_ClassNameGenerator } from '@mui/utils';\nexport const generateUtilityClass = (componentName, slot) => unstable_generateUtilityClass(componentName, slot, 'Mui');\nexport const generateUtilityClasses = (componentName, slots) => unstable_generateUtilityClasses(componentName, slots, 'Mui');", "map": {"version": 3, "names": ["unstable_generateUtilityClass", "unstable_generateUtilityClasses", "unstable_ClassNameGenerator", "generateUtilityClass", "componentName", "slot", "generateUtilityClasses", "slots"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/className/index.js"], "sourcesContent": ["import { unstable_generateUtilityClass, unstable_generateUtilityClasses } from '@mui/utils';\nexport { unstable_ClassNameGenerator } from '@mui/utils';\nexport const generateUtilityClass = (componentName, slot) => unstable_generateUtilityClass(componentName, slot, 'Mui');\nexport const generateUtilityClasses = (componentName, slots) => unstable_generateUtilityClasses(componentName, slots, 'Mui');"], "mappings": "AAAA,SAASA,6BAA6B,EAAEC,+BAA+B,QAAQ,YAAY;AAC3F,SAASC,2BAA2B,QAAQ,YAAY;AACxD,OAAO,MAAMC,oBAAoB,GAAGA,CAACC,aAAa,EAAEC,IAAI,KAAKL,6BAA6B,CAACI,aAAa,EAAEC,IAAI,EAAE,KAAK,CAAC;AACtH,OAAO,MAAMC,sBAAsB,GAAGA,CAACF,aAAa,EAAEG,KAAK,KAAKN,+BAA+B,CAACG,aAAa,EAAEG,KAAK,EAAE,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}