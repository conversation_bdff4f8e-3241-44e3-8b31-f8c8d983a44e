{"ast": null, "code": "'use client';\n\nexport { Tabs } from './Tabs';\nexport * from './TabsContext';\nexport * from './tabsClasses';\nexport * from './Tabs.types';", "map": {"version": 3, "names": ["Tabs"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/Tabs/index.js"], "sourcesContent": ["'use client';\n\nexport { Tabs } from './Tabs';\nexport * from './TabsContext';\nexport * from './tabsClasses';\nexport * from './Tabs.types';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,IAAI,QAAQ,QAAQ;AAC7B,cAAc,eAAe;AAC7B,cAAc,eAAe;AAC7B,cAAc,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}