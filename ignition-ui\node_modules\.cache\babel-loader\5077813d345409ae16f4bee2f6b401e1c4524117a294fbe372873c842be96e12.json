{"ast": null, "code": "var _jsxFileName = \"C:\\\\ignition\\\\ignition-ui\\\\src\\\\components\\\\Loading\\\\SimpleHexagonSpinner.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { Box } from '@mui/material';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SimpleHexagonSpinner = _ref => {\n  _s();\n  let {\n    size = 200\n  } = _ref;\n  const canvasRef = useRef(null);\n  const animationRef = useRef(null);\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    const ctx = canvas.getContext('2d');\n\n    // Set canvas size\n    canvas.width = size;\n    canvas.height = size;\n    const centerX = size / 2;\n    const centerY = size / 2;\n    const hexRadius = size * 0.35;\n\n    // Ball physics\n    let ballX = centerX;\n    let ballY = centerY;\n    let ballVelX = 2;\n    let ballVelY = 0;\n    const ballRadius = 8;\n    const gravity = 0.3;\n    const bounce = 0.8;\n\n    // Hexagon rotation\n    let hexRotation = 0;\n\n    // Create hexagon vertices\n    const getHexagonVertices = rotation => {\n      const vertices = [];\n      for (let i = 0; i < 6; i++) {\n        const angle = Math.PI / 3 * i + rotation;\n        vertices.push({\n          x: centerX + hexRadius * Math.cos(angle),\n          y: centerY + hexRadius * Math.sin(angle)\n        });\n      }\n      return vertices;\n    };\n\n    // Check if point is inside hexagon\n    const isInsideHexagon = (x, y, vertices) => {\n      let inside = true;\n      for (let i = 0; i < 6; i++) {\n        const j = (i + 1) % 6;\n        const v1 = vertices[i];\n        const v2 = vertices[j];\n\n        // Calculate cross product to determine which side of edge the point is on\n        const cross = (v2.x - v1.x) * (y - v1.y) - (v2.y - v1.y) * (x - v1.x);\n        if (cross > 0) {\n          inside = false;\n          break;\n        }\n      }\n      return inside;\n    };\n\n    // Get closest point on hexagon edge\n    const getClosestPointOnEdge = (x, y, vertices) => {\n      let closestPoint = {\n        x,\n        y\n      };\n      let minDistance = Infinity;\n      for (let i = 0; i < 6; i++) {\n        const j = (i + 1) % 6;\n        const v1 = vertices[i];\n        const v2 = vertices[j];\n\n        // Project point onto line segment\n        const A = x - v1.x;\n        const B = y - v1.y;\n        const C = v2.x - v1.x;\n        const D = v2.y - v1.y;\n        const dot = A * C + B * D;\n        const lenSq = C * C + D * D;\n        let param = -1;\n        if (lenSq !== 0) param = dot / lenSq;\n        let xx, yy;\n        if (param < 0) {\n          xx = v1.x;\n          yy = v1.y;\n        } else if (param > 1) {\n          xx = v2.x;\n          yy = v2.y;\n        } else {\n          xx = v1.x + param * C;\n          yy = v1.y + param * D;\n        }\n        const distance = Math.sqrt((x - xx) * (x - xx) + (y - yy) * (y - yy));\n        if (distance < minDistance) {\n          minDistance = distance;\n          closestPoint = {\n            x: xx,\n            y: yy\n          };\n        }\n      }\n      return closestPoint;\n    };\n    const animate = () => {\n      // Clear canvas\n      ctx.clearRect(0, 0, size, size);\n\n      // Update hexagon rotation\n      hexRotation += 0.02;\n\n      // Get current hexagon vertices\n      const vertices = getHexagonVertices(hexRotation);\n\n      // Apply gravity to ball\n      ballVelY += gravity;\n\n      // Update ball position\n      ballX += ballVelX;\n      ballY += ballVelY;\n\n      // Check collision with hexagon\n      if (!isInsideHexagon(ballX, ballY, vertices)) {\n        const closestPoint = getClosestPointOnEdge(ballX, ballY, vertices);\n\n        // Calculate normal vector\n        const normalX = ballX - closestPoint.x;\n        const normalY = ballY - closestPoint.y;\n        const normalLength = Math.sqrt(normalX * normalX + normalY * normalY);\n        if (normalLength > 0) {\n          const unitNormalX = normalX / normalLength;\n          const unitNormalY = normalY / normalLength;\n\n          // Move ball back inside\n          ballX = closestPoint.x + unitNormalX * ballRadius;\n          ballY = closestPoint.y + unitNormalY * ballRadius;\n\n          // Reflect velocity\n          const dotProduct = ballVelX * unitNormalX + ballVelY * unitNormalY;\n          ballVelX = (ballVelX - 2 * dotProduct * unitNormalX) * bounce;\n          ballVelY = (ballVelY - 2 * dotProduct * unitNormalY) * bounce;\n        }\n      }\n\n      // Draw hexagon\n      ctx.beginPath();\n      ctx.moveTo(vertices[0].x, vertices[0].y);\n      for (let i = 1; i < 6; i++) {\n        ctx.lineTo(vertices[i].x, vertices[i].y);\n      }\n      ctx.closePath();\n      ctx.strokeStyle = '#FFFFFF';\n      ctx.lineWidth = 2;\n      ctx.stroke();\n\n      // Draw ball\n      ctx.beginPath();\n      ctx.arc(ballX, ballY, ballRadius, 0, Math.PI * 2);\n      ctx.fillStyle = '#F0A500';\n      ctx.fill();\n      animationRef.current = requestAnimationFrame(animate);\n    };\n    animate();\n    return () => {\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n    };\n  }, [size]);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    sx: {\n      display: 'flex',\n      justifyContent: 'center',\n      alignItems: 'center',\n      width: size,\n      height: size\n    },\n    children: /*#__PURE__*/_jsxDEV(\"canvas\", {\n      ref: canvasRef,\n      style: {\n        backgroundColor: 'transparent'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 177,\n    columnNumber: 5\n  }, this);\n};\n_s(SimpleHexagonSpinner, \"X5bd7Q1XXg1keIMflMhOltk4wyU=\");\n_c = SimpleHexagonSpinner;\nexport default SimpleHexagonSpinner;\nvar _c;\n$RefreshReg$(_c, \"SimpleHexagonSpinner\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "Box", "jsxDEV", "_jsxDEV", "SimpleHexagonSpinner", "_ref", "_s", "size", "canvasRef", "animationRef", "canvas", "current", "ctx", "getContext", "width", "height", "centerX", "centerY", "hexRadius", "ballX", "ballY", "ballVelX", "ballVelY", "ballRadius", "gravity", "bounce", "hexRotation", "getHexagonVertices", "rotation", "vertices", "i", "angle", "Math", "PI", "push", "x", "cos", "y", "sin", "isInsideHexagon", "inside", "j", "v1", "v2", "cross", "getClosestPointOnEdge", "closestPoint", "minDistance", "Infinity", "A", "B", "C", "D", "dot", "lenSq", "param", "xx", "yy", "distance", "sqrt", "animate", "clearRect", "normalX", "normalY", "normalLength", "unitNormalX", "unitNormalY", "dotProduct", "beginPath", "moveTo", "lineTo", "closePath", "strokeStyle", "lineWidth", "stroke", "arc", "fillStyle", "fill", "requestAnimationFrame", "cancelAnimationFrame", "sx", "display", "justifyContent", "alignItems", "children", "ref", "style", "backgroundColor", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/ignition/ignition-ui/src/components/Loading/SimpleHexagonSpinner.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\nimport { Box } from '@mui/material';\n\nconst SimpleHexagonSpinner = ({ size = 200 }) => {\n  const canvasRef = useRef(null);\n  const animationRef = useRef(null);\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    const ctx = canvas.getContext('2d');\n    \n    // Set canvas size\n    canvas.width = size;\n    canvas.height = size;\n    \n    const centerX = size / 2;\n    const centerY = size / 2;\n    const hexRadius = size * 0.35;\n    \n    // Ball physics\n    let ballX = centerX;\n    let ballY = centerY;\n    let ballVelX = 2;\n    let ballVelY = 0;\n    const ballRadius = 8;\n    const gravity = 0.3;\n    const bounce = 0.8;\n    \n    // Hexagon rotation\n    let hexRotation = 0;\n    \n    // Create hexagon vertices\n    const getHexagonVertices = (rotation) => {\n      const vertices = [];\n      for (let i = 0; i < 6; i++) {\n        const angle = (Math.PI / 3) * i + rotation;\n        vertices.push({\n          x: centerX + hexRadius * Math.cos(angle),\n          y: centerY + hexRadius * Math.sin(angle)\n        });\n      }\n      return vertices;\n    };\n    \n    // Check if point is inside hexagon\n    const isInsideHexagon = (x, y, vertices) => {\n      let inside = true;\n      for (let i = 0; i < 6; i++) {\n        const j = (i + 1) % 6;\n        const v1 = vertices[i];\n        const v2 = vertices[j];\n        \n        // Calculate cross product to determine which side of edge the point is on\n        const cross = (v2.x - v1.x) * (y - v1.y) - (v2.y - v1.y) * (x - v1.x);\n        if (cross > 0) {\n          inside = false;\n          break;\n        }\n      }\n      return inside;\n    };\n    \n    // Get closest point on hexagon edge\n    const getClosestPointOnEdge = (x, y, vertices) => {\n      let closestPoint = { x, y };\n      let minDistance = Infinity;\n      \n      for (let i = 0; i < 6; i++) {\n        const j = (i + 1) % 6;\n        const v1 = vertices[i];\n        const v2 = vertices[j];\n        \n        // Project point onto line segment\n        const A = x - v1.x;\n        const B = y - v1.y;\n        const C = v2.x - v1.x;\n        const D = v2.y - v1.y;\n        \n        const dot = A * C + B * D;\n        const lenSq = C * C + D * D;\n        let param = -1;\n        if (lenSq !== 0) param = dot / lenSq;\n        \n        let xx, yy;\n        if (param < 0) {\n          xx = v1.x;\n          yy = v1.y;\n        } else if (param > 1) {\n          xx = v2.x;\n          yy = v2.y;\n        } else {\n          xx = v1.x + param * C;\n          yy = v1.y + param * D;\n        }\n        \n        const distance = Math.sqrt((x - xx) * (x - xx) + (y - yy) * (y - yy));\n        if (distance < minDistance) {\n          minDistance = distance;\n          closestPoint = { x: xx, y: yy };\n        }\n      }\n      \n      return closestPoint;\n    };\n    \n    const animate = () => {\n      // Clear canvas\n      ctx.clearRect(0, 0, size, size);\n      \n      // Update hexagon rotation\n      hexRotation += 0.02;\n      \n      // Get current hexagon vertices\n      const vertices = getHexagonVertices(hexRotation);\n      \n      // Apply gravity to ball\n      ballVelY += gravity;\n      \n      // Update ball position\n      ballX += ballVelX;\n      ballY += ballVelY;\n      \n      // Check collision with hexagon\n      if (!isInsideHexagon(ballX, ballY, vertices)) {\n        const closestPoint = getClosestPointOnEdge(ballX, ballY, vertices);\n        \n        // Calculate normal vector\n        const normalX = ballX - closestPoint.x;\n        const normalY = ballY - closestPoint.y;\n        const normalLength = Math.sqrt(normalX * normalX + normalY * normalY);\n        \n        if (normalLength > 0) {\n          const unitNormalX = normalX / normalLength;\n          const unitNormalY = normalY / normalLength;\n          \n          // Move ball back inside\n          ballX = closestPoint.x + unitNormalX * ballRadius;\n          ballY = closestPoint.y + unitNormalY * ballRadius;\n          \n          // Reflect velocity\n          const dotProduct = ballVelX * unitNormalX + ballVelY * unitNormalY;\n          ballVelX = (ballVelX - 2 * dotProduct * unitNormalX) * bounce;\n          ballVelY = (ballVelY - 2 * dotProduct * unitNormalY) * bounce;\n        }\n      }\n      \n      // Draw hexagon\n      ctx.beginPath();\n      ctx.moveTo(vertices[0].x, vertices[0].y);\n      for (let i = 1; i < 6; i++) {\n        ctx.lineTo(vertices[i].x, vertices[i].y);\n      }\n      ctx.closePath();\n      ctx.strokeStyle = '#FFFFFF';\n      ctx.lineWidth = 2;\n      ctx.stroke();\n      \n      // Draw ball\n      ctx.beginPath();\n      ctx.arc(ballX, ballY, ballRadius, 0, Math.PI * 2);\n      ctx.fillStyle = '#F0A500';\n      ctx.fill();\n      \n      animationRef.current = requestAnimationFrame(animate);\n    };\n    \n    animate();\n    \n    return () => {\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n    };\n  }, [size]);\n\n  return (\n    <Box\n      sx={{\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        width: size,\n        height: size,\n      }}\n    >\n      <canvas\n        ref={canvasRef}\n        style={{\n          backgroundColor: 'transparent',\n        }}\n      />\n    </Box>\n  );\n};\n\nexport default SimpleHexagonSpinner;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,SAASC,GAAG,QAAQ,eAAe;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpC,MAAMC,oBAAoB,GAAGC,IAAA,IAAoB;EAAAC,EAAA;EAAA,IAAnB;IAAEC,IAAI,GAAG;EAAI,CAAC,GAAAF,IAAA;EAC1C,MAAMG,SAAS,GAAGR,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMS,YAAY,GAAGT,MAAM,CAAC,IAAI,CAAC;EAEjCD,SAAS,CAAC,MAAM;IACd,MAAMW,MAAM,GAAGF,SAAS,CAACG,OAAO;IAChC,MAAMC,GAAG,GAAGF,MAAM,CAACG,UAAU,CAAC,IAAI,CAAC;;IAEnC;IACAH,MAAM,CAACI,KAAK,GAAGP,IAAI;IACnBG,MAAM,CAACK,MAAM,GAAGR,IAAI;IAEpB,MAAMS,OAAO,GAAGT,IAAI,GAAG,CAAC;IACxB,MAAMU,OAAO,GAAGV,IAAI,GAAG,CAAC;IACxB,MAAMW,SAAS,GAAGX,IAAI,GAAG,IAAI;;IAE7B;IACA,IAAIY,KAAK,GAAGH,OAAO;IACnB,IAAII,KAAK,GAAGH,OAAO;IACnB,IAAII,QAAQ,GAAG,CAAC;IAChB,IAAIC,QAAQ,GAAG,CAAC;IAChB,MAAMC,UAAU,GAAG,CAAC;IACpB,MAAMC,OAAO,GAAG,GAAG;IACnB,MAAMC,MAAM,GAAG,GAAG;;IAElB;IACA,IAAIC,WAAW,GAAG,CAAC;;IAEnB;IACA,MAAMC,kBAAkB,GAAIC,QAAQ,IAAK;MACvC,MAAMC,QAAQ,GAAG,EAAE;MACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC1B,MAAMC,KAAK,GAAIC,IAAI,CAACC,EAAE,GAAG,CAAC,GAAIH,CAAC,GAAGF,QAAQ;QAC1CC,QAAQ,CAACK,IAAI,CAAC;UACZC,CAAC,EAAEnB,OAAO,GAAGE,SAAS,GAAGc,IAAI,CAACI,GAAG,CAACL,KAAK,CAAC;UACxCM,CAAC,EAAEpB,OAAO,GAAGC,SAAS,GAAGc,IAAI,CAACM,GAAG,CAACP,KAAK;QACzC,CAAC,CAAC;MACJ;MACA,OAAOF,QAAQ;IACjB,CAAC;;IAED;IACA,MAAMU,eAAe,GAAGA,CAACJ,CAAC,EAAEE,CAAC,EAAER,QAAQ,KAAK;MAC1C,IAAIW,MAAM,GAAG,IAAI;MACjB,KAAK,IAAIV,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC1B,MAAMW,CAAC,GAAG,CAACX,CAAC,GAAG,CAAC,IAAI,CAAC;QACrB,MAAMY,EAAE,GAAGb,QAAQ,CAACC,CAAC,CAAC;QACtB,MAAMa,EAAE,GAAGd,QAAQ,CAACY,CAAC,CAAC;;QAEtB;QACA,MAAMG,KAAK,GAAG,CAACD,EAAE,CAACR,CAAC,GAAGO,EAAE,CAACP,CAAC,KAAKE,CAAC,GAAGK,EAAE,CAACL,CAAC,CAAC,GAAG,CAACM,EAAE,CAACN,CAAC,GAAGK,EAAE,CAACL,CAAC,KAAKF,CAAC,GAAGO,EAAE,CAACP,CAAC,CAAC;QACrE,IAAIS,KAAK,GAAG,CAAC,EAAE;UACbJ,MAAM,GAAG,KAAK;UACd;QACF;MACF;MACA,OAAOA,MAAM;IACf,CAAC;;IAED;IACA,MAAMK,qBAAqB,GAAGA,CAACV,CAAC,EAAEE,CAAC,EAAER,QAAQ,KAAK;MAChD,IAAIiB,YAAY,GAAG;QAAEX,CAAC;QAAEE;MAAE,CAAC;MAC3B,IAAIU,WAAW,GAAGC,QAAQ;MAE1B,KAAK,IAAIlB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC1B,MAAMW,CAAC,GAAG,CAACX,CAAC,GAAG,CAAC,IAAI,CAAC;QACrB,MAAMY,EAAE,GAAGb,QAAQ,CAACC,CAAC,CAAC;QACtB,MAAMa,EAAE,GAAGd,QAAQ,CAACY,CAAC,CAAC;;QAEtB;QACA,MAAMQ,CAAC,GAAGd,CAAC,GAAGO,EAAE,CAACP,CAAC;QAClB,MAAMe,CAAC,GAAGb,CAAC,GAAGK,EAAE,CAACL,CAAC;QAClB,MAAMc,CAAC,GAAGR,EAAE,CAACR,CAAC,GAAGO,EAAE,CAACP,CAAC;QACrB,MAAMiB,CAAC,GAAGT,EAAE,CAACN,CAAC,GAAGK,EAAE,CAACL,CAAC;QAErB,MAAMgB,GAAG,GAAGJ,CAAC,GAAGE,CAAC,GAAGD,CAAC,GAAGE,CAAC;QACzB,MAAME,KAAK,GAAGH,CAAC,GAAGA,CAAC,GAAGC,CAAC,GAAGA,CAAC;QAC3B,IAAIG,KAAK,GAAG,CAAC,CAAC;QACd,IAAID,KAAK,KAAK,CAAC,EAAEC,KAAK,GAAGF,GAAG,GAAGC,KAAK;QAEpC,IAAIE,EAAE,EAAEC,EAAE;QACV,IAAIF,KAAK,GAAG,CAAC,EAAE;UACbC,EAAE,GAAGd,EAAE,CAACP,CAAC;UACTsB,EAAE,GAAGf,EAAE,CAACL,CAAC;QACX,CAAC,MAAM,IAAIkB,KAAK,GAAG,CAAC,EAAE;UACpBC,EAAE,GAAGb,EAAE,CAACR,CAAC;UACTsB,EAAE,GAAGd,EAAE,CAACN,CAAC;QACX,CAAC,MAAM;UACLmB,EAAE,GAAGd,EAAE,CAACP,CAAC,GAAGoB,KAAK,GAAGJ,CAAC;UACrBM,EAAE,GAAGf,EAAE,CAACL,CAAC,GAAGkB,KAAK,GAAGH,CAAC;QACvB;QAEA,MAAMM,QAAQ,GAAG1B,IAAI,CAAC2B,IAAI,CAAC,CAACxB,CAAC,GAAGqB,EAAE,KAAKrB,CAAC,GAAGqB,EAAE,CAAC,GAAG,CAACnB,CAAC,GAAGoB,EAAE,KAAKpB,CAAC,GAAGoB,EAAE,CAAC,CAAC;QACrE,IAAIC,QAAQ,GAAGX,WAAW,EAAE;UAC1BA,WAAW,GAAGW,QAAQ;UACtBZ,YAAY,GAAG;YAAEX,CAAC,EAAEqB,EAAE;YAAEnB,CAAC,EAAEoB;UAAG,CAAC;QACjC;MACF;MAEA,OAAOX,YAAY;IACrB,CAAC;IAED,MAAMc,OAAO,GAAGA,CAAA,KAAM;MACpB;MACAhD,GAAG,CAACiD,SAAS,CAAC,CAAC,EAAE,CAAC,EAAEtD,IAAI,EAAEA,IAAI,CAAC;;MAE/B;MACAmB,WAAW,IAAI,IAAI;;MAEnB;MACA,MAAMG,QAAQ,GAAGF,kBAAkB,CAACD,WAAW,CAAC;;MAEhD;MACAJ,QAAQ,IAAIE,OAAO;;MAEnB;MACAL,KAAK,IAAIE,QAAQ;MACjBD,KAAK,IAAIE,QAAQ;;MAEjB;MACA,IAAI,CAACiB,eAAe,CAACpB,KAAK,EAAEC,KAAK,EAAES,QAAQ,CAAC,EAAE;QAC5C,MAAMiB,YAAY,GAAGD,qBAAqB,CAAC1B,KAAK,EAAEC,KAAK,EAAES,QAAQ,CAAC;;QAElE;QACA,MAAMiC,OAAO,GAAG3C,KAAK,GAAG2B,YAAY,CAACX,CAAC;QACtC,MAAM4B,OAAO,GAAG3C,KAAK,GAAG0B,YAAY,CAACT,CAAC;QACtC,MAAM2B,YAAY,GAAGhC,IAAI,CAAC2B,IAAI,CAACG,OAAO,GAAGA,OAAO,GAAGC,OAAO,GAAGA,OAAO,CAAC;QAErE,IAAIC,YAAY,GAAG,CAAC,EAAE;UACpB,MAAMC,WAAW,GAAGH,OAAO,GAAGE,YAAY;UAC1C,MAAME,WAAW,GAAGH,OAAO,GAAGC,YAAY;;UAE1C;UACA7C,KAAK,GAAG2B,YAAY,CAACX,CAAC,GAAG8B,WAAW,GAAG1C,UAAU;UACjDH,KAAK,GAAG0B,YAAY,CAACT,CAAC,GAAG6B,WAAW,GAAG3C,UAAU;;UAEjD;UACA,MAAM4C,UAAU,GAAG9C,QAAQ,GAAG4C,WAAW,GAAG3C,QAAQ,GAAG4C,WAAW;UAClE7C,QAAQ,GAAG,CAACA,QAAQ,GAAG,CAAC,GAAG8C,UAAU,GAAGF,WAAW,IAAIxC,MAAM;UAC7DH,QAAQ,GAAG,CAACA,QAAQ,GAAG,CAAC,GAAG6C,UAAU,GAAGD,WAAW,IAAIzC,MAAM;QAC/D;MACF;;MAEA;MACAb,GAAG,CAACwD,SAAS,CAAC,CAAC;MACfxD,GAAG,CAACyD,MAAM,CAACxC,QAAQ,CAAC,CAAC,CAAC,CAACM,CAAC,EAAEN,QAAQ,CAAC,CAAC,CAAC,CAACQ,CAAC,CAAC;MACxC,KAAK,IAAIP,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC1BlB,GAAG,CAAC0D,MAAM,CAACzC,QAAQ,CAACC,CAAC,CAAC,CAACK,CAAC,EAAEN,QAAQ,CAACC,CAAC,CAAC,CAACO,CAAC,CAAC;MAC1C;MACAzB,GAAG,CAAC2D,SAAS,CAAC,CAAC;MACf3D,GAAG,CAAC4D,WAAW,GAAG,SAAS;MAC3B5D,GAAG,CAAC6D,SAAS,GAAG,CAAC;MACjB7D,GAAG,CAAC8D,MAAM,CAAC,CAAC;;MAEZ;MACA9D,GAAG,CAACwD,SAAS,CAAC,CAAC;MACfxD,GAAG,CAAC+D,GAAG,CAACxD,KAAK,EAAEC,KAAK,EAAEG,UAAU,EAAE,CAAC,EAAES,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC;MACjDrB,GAAG,CAACgE,SAAS,GAAG,SAAS;MACzBhE,GAAG,CAACiE,IAAI,CAAC,CAAC;MAEVpE,YAAY,CAACE,OAAO,GAAGmE,qBAAqB,CAAClB,OAAO,CAAC;IACvD,CAAC;IAEDA,OAAO,CAAC,CAAC;IAET,OAAO,MAAM;MACX,IAAInD,YAAY,CAACE,OAAO,EAAE;QACxBoE,oBAAoB,CAACtE,YAAY,CAACE,OAAO,CAAC;MAC5C;IACF,CAAC;EACH,CAAC,EAAE,CAACJ,IAAI,CAAC,CAAC;EAEV,oBACEJ,OAAA,CAACF,GAAG;IACF+E,EAAE,EAAE;MACFC,OAAO,EAAE,MAAM;MACfC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBrE,KAAK,EAAEP,IAAI;MACXQ,MAAM,EAAER;IACV,CAAE;IAAA6E,QAAA,eAEFjF,OAAA;MACEkF,GAAG,EAAE7E,SAAU;MACf8E,KAAK,EAAE;QACLC,eAAe,EAAE;MACnB;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACrF,EAAA,CA9LIF,oBAAoB;AAAAwF,EAAA,GAApBxF,oBAAoB;AAgM1B,eAAeA,oBAAoB;AAAC,IAAAwF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}