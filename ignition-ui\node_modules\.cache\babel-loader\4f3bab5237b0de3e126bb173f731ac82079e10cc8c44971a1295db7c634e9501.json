{"ast": null, "code": "'use client';\n\nexport * from './useCompoundParent';\nexport * from './useCompoundItem';", "map": {"version": 3, "names": [], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/useCompound/index.js"], "sourcesContent": ["'use client';\n\nexport * from './useCompoundParent';\nexport * from './useCompoundItem';"], "mappings": "AAAA,YAAY;;AAEZ,cAAc,qBAAqB;AACnC,cAAc,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}