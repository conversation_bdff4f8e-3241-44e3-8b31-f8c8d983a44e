import { TextField } from '@mui/material';
import React from 'react';

const CustomDatePicker = ({ value, onChange, label, error, helperText, ...props }) => {
  return (
    <TextField
      type="date"
      value={value ? value.format('YYYY-MM-DD') : ''}
      onChange={(e) => onChange(e.target.value)}
      label={label}
      error={error}
      helperText={helperText}
      InputLabelProps={{
        shrink: true,
      }}
      {...props}
    />
  );
};

export default CustomDatePicker;
