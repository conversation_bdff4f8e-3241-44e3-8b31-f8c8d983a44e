{"ast": null, "code": "/**!\n * @fileOverview Kickass library to create and place poppers near their reference elements.\n * @version 1.16.1\n * @license\n * Copyright (c) 2016 <PERSON> and contributors\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\nvar isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined' && typeof navigator !== 'undefined';\nvar timeoutDuration = function () {\n  var longerTimeoutBrowsers = ['Edge', 'Trident', 'Firefox'];\n  for (var i = 0; i < longerTimeoutBrowsers.length; i += 1) {\n    if (isBrowser && navigator.userAgent.indexOf(longerTimeoutBrowsers[i]) >= 0) {\n      return 1;\n    }\n  }\n  return 0;\n}();\nfunction microtaskDebounce(fn) {\n  var called = false;\n  return function () {\n    if (called) {\n      return;\n    }\n    called = true;\n    window.Promise.resolve().then(function () {\n      called = false;\n      fn();\n    });\n  };\n}\nfunction taskDebounce(fn) {\n  var scheduled = false;\n  return function () {\n    if (!scheduled) {\n      scheduled = true;\n      setTimeout(function () {\n        scheduled = false;\n        fn();\n      }, timeoutDuration);\n    }\n  };\n}\nvar supportsMicroTasks = isBrowser && window.Promise;\n\n/**\n* Create a debounced version of a method, that's asynchronously deferred\n* but called in the minimum time possible.\n*\n* @method\n* @memberof Popper.Utils\n* @argument {Function} fn\n* @returns {Function}\n*/\nvar debounce = supportsMicroTasks ? microtaskDebounce : taskDebounce;\n\n/**\n * Check if the given variable is a function\n * @method\n * @memberof Popper.Utils\n * @argument {Any} functionToCheck - variable to check\n * @returns {Boolean} answer to: is a function?\n */\nfunction isFunction(functionToCheck) {\n  var getType = {};\n  return functionToCheck && getType.toString.call(functionToCheck) === '[object Function]';\n}\n\n/**\n * Get CSS computed property of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Eement} element\n * @argument {String} property\n */\nfunction getStyleComputedProperty(element, property) {\n  if (element.nodeType !== 1) {\n    return [];\n  }\n  // NOTE: 1 DOM access here\n  var window = element.ownerDocument.defaultView;\n  var css = window.getComputedStyle(element, null);\n  return property ? css[property] : css;\n}\n\n/**\n * Returns the parentNode or the host of the element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} parent\n */\nfunction getParentNode(element) {\n  if (element.nodeName === 'HTML') {\n    return element;\n  }\n  return element.parentNode || element.host;\n}\n\n/**\n * Returns the scrolling parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} scroll parent\n */\nfunction getScrollParent(element) {\n  // Return body, `getScroll` will take care to get the correct `scrollTop` from it\n  if (!element) {\n    return document.body;\n  }\n  switch (element.nodeName) {\n    case 'HTML':\n    case 'BODY':\n      return element.ownerDocument.body;\n    case '#document':\n      return element.body;\n  }\n\n  // Firefox want us to check `-x` and `-y` variations as well\n\n  var _getStyleComputedProp = getStyleComputedProperty(element),\n    overflow = _getStyleComputedProp.overflow,\n    overflowX = _getStyleComputedProp.overflowX,\n    overflowY = _getStyleComputedProp.overflowY;\n  if (/(auto|scroll|overlay)/.test(overflow + overflowY + overflowX)) {\n    return element;\n  }\n  return getScrollParent(getParentNode(element));\n}\n\n/**\n * Returns the reference node of the reference object, or the reference object itself.\n * @method\n * @memberof Popper.Utils\n * @param {Element|Object} reference - the reference element (the popper will be relative to this)\n * @returns {Element} parent\n */\nfunction getReferenceNode(reference) {\n  return reference && reference.referenceNode ? reference.referenceNode : reference;\n}\nvar isIE11 = isBrowser && !!(window.MSInputMethodContext && document.documentMode);\nvar isIE10 = isBrowser && /MSIE 10/.test(navigator.userAgent);\n\n/**\n * Determines if the browser is Internet Explorer\n * @method\n * @memberof Popper.Utils\n * @param {Number} version to check\n * @returns {Boolean} isIE\n */\nfunction isIE(version) {\n  if (version === 11) {\n    return isIE11;\n  }\n  if (version === 10) {\n    return isIE10;\n  }\n  return isIE11 || isIE10;\n}\n\n/**\n * Returns the offset parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} offset parent\n */\nfunction getOffsetParent(element) {\n  if (!element) {\n    return document.documentElement;\n  }\n  var noOffsetParent = isIE(10) ? document.body : null;\n\n  // NOTE: 1 DOM access here\n  var offsetParent = element.offsetParent || null;\n  // Skip hidden elements which don't have an offsetParent\n  while (offsetParent === noOffsetParent && element.nextElementSibling) {\n    offsetParent = (element = element.nextElementSibling).offsetParent;\n  }\n  var nodeName = offsetParent && offsetParent.nodeName;\n  if (!nodeName || nodeName === 'BODY' || nodeName === 'HTML') {\n    return element ? element.ownerDocument.documentElement : document.documentElement;\n  }\n\n  // .offsetParent will return the closest TH, TD or TABLE in case\n  // no offsetParent is present, I hate this job...\n  if (['TH', 'TD', 'TABLE'].indexOf(offsetParent.nodeName) !== -1 && getStyleComputedProperty(offsetParent, 'position') === 'static') {\n    return getOffsetParent(offsetParent);\n  }\n  return offsetParent;\n}\nfunction isOffsetContainer(element) {\n  var nodeName = element.nodeName;\n  if (nodeName === 'BODY') {\n    return false;\n  }\n  return nodeName === 'HTML' || getOffsetParent(element.firstElementChild) === element;\n}\n\n/**\n * Finds the root node (document, shadowDOM root) of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} node\n * @returns {Element} root node\n */\nfunction getRoot(node) {\n  if (node.parentNode !== null) {\n    return getRoot(node.parentNode);\n  }\n  return node;\n}\n\n/**\n * Finds the offset parent common to the two provided nodes\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element1\n * @argument {Element} element2\n * @returns {Element} common offset parent\n */\nfunction findCommonOffsetParent(element1, element2) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element1 || !element1.nodeType || !element2 || !element2.nodeType) {\n    return document.documentElement;\n  }\n\n  // Here we make sure to give as \"start\" the element that comes first in the DOM\n  var order = element1.compareDocumentPosition(element2) & Node.DOCUMENT_POSITION_FOLLOWING;\n  var start = order ? element1 : element2;\n  var end = order ? element2 : element1;\n\n  // Get common ancestor container\n  var range = document.createRange();\n  range.setStart(start, 0);\n  range.setEnd(end, 0);\n  var commonAncestorContainer = range.commonAncestorContainer;\n\n  // Both nodes are inside #document\n\n  if (element1 !== commonAncestorContainer && element2 !== commonAncestorContainer || start.contains(end)) {\n    if (isOffsetContainer(commonAncestorContainer)) {\n      return commonAncestorContainer;\n    }\n    return getOffsetParent(commonAncestorContainer);\n  }\n\n  // one of the nodes is inside shadowDOM, find which one\n  var element1root = getRoot(element1);\n  if (element1root.host) {\n    return findCommonOffsetParent(element1root.host, element2);\n  } else {\n    return findCommonOffsetParent(element1, getRoot(element2).host);\n  }\n}\n\n/**\n * Gets the scroll value of the given element in the given side (top and left)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {String} side `top` or `left`\n * @returns {number} amount of scrolled pixels\n */\nfunction getScroll(element) {\n  var side = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 'top';\n  var upperSide = side === 'top' ? 'scrollTop' : 'scrollLeft';\n  var nodeName = element.nodeName;\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    var html = element.ownerDocument.documentElement;\n    var scrollingElement = element.ownerDocument.scrollingElement || html;\n    return scrollingElement[upperSide];\n  }\n  return element[upperSide];\n}\n\n/*\n * Sum or subtract the element scroll values (left and top) from a given rect object\n * @method\n * @memberof Popper.Utils\n * @param {Object} rect - Rect object you want to change\n * @param {HTMLElement} element - The element from the function reads the scroll values\n * @param {Boolean} subtract - set to true if you want to subtract the scroll values\n * @return {Object} rect - The modifier rect object\n */\nfunction includeScroll(rect, element) {\n  var subtract = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  var scrollTop = getScroll(element, 'top');\n  var scrollLeft = getScroll(element, 'left');\n  var modifier = subtract ? -1 : 1;\n  rect.top += scrollTop * modifier;\n  rect.bottom += scrollTop * modifier;\n  rect.left += scrollLeft * modifier;\n  rect.right += scrollLeft * modifier;\n  return rect;\n}\n\n/*\n * Helper to detect borders of a given element\n * @method\n * @memberof Popper.Utils\n * @param {CSSStyleDeclaration} styles\n * Result of `getStyleComputedProperty` on the given element\n * @param {String} axis - `x` or `y`\n * @return {number} borders - The borders size of the given axis\n */\n\nfunction getBordersSize(styles, axis) {\n  var sideA = axis === 'x' ? 'Left' : 'Top';\n  var sideB = sideA === 'Left' ? 'Right' : 'Bottom';\n  return parseFloat(styles['border' + sideA + 'Width']) + parseFloat(styles['border' + sideB + 'Width']);\n}\nfunction getSize(axis, body, html, computedStyle) {\n  return Math.max(body['offset' + axis], body['scroll' + axis], html['client' + axis], html['offset' + axis], html['scroll' + axis], isIE(10) ? parseInt(html['offset' + axis]) + parseInt(computedStyle['margin' + (axis === 'Height' ? 'Top' : 'Left')]) + parseInt(computedStyle['margin' + (axis === 'Height' ? 'Bottom' : 'Right')]) : 0);\n}\nfunction getWindowSizes(document) {\n  var body = document.body;\n  var html = document.documentElement;\n  var computedStyle = isIE(10) && getComputedStyle(html);\n  return {\n    height: getSize('Height', body, html, computedStyle),\n    width: getSize('Width', body, html, computedStyle)\n  };\n}\nvar classCallCheck = function (instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n};\nvar createClass = function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) defineProperties(Constructor, staticProps);\n    return Constructor;\n  };\n}();\nvar defineProperty = function (obj, key, value) {\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n};\nvar _extends = Object.assign || function (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n    for (var key in source) {\n      if (Object.prototype.hasOwnProperty.call(source, key)) {\n        target[key] = source[key];\n      }\n    }\n  }\n  return target;\n};\n\n/**\n * Given element offsets, generate an output similar to getBoundingClientRect\n * @method\n * @memberof Popper.Utils\n * @argument {Object} offsets\n * @returns {Object} ClientRect like output\n */\nfunction getClientRect(offsets) {\n  return _extends({}, offsets, {\n    right: offsets.left + offsets.width,\n    bottom: offsets.top + offsets.height\n  });\n}\n\n/**\n * Get bounding client rect of given element\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} element\n * @return {Object} client rect\n */\nfunction getBoundingClientRect(element) {\n  var rect = {};\n\n  // IE10 10 FIX: Please, don't ask, the element isn't\n  // considered in DOM in some circumstances...\n  // This isn't reproducible in IE10 compatibility mode of IE11\n  try {\n    if (isIE(10)) {\n      rect = element.getBoundingClientRect();\n      var scrollTop = getScroll(element, 'top');\n      var scrollLeft = getScroll(element, 'left');\n      rect.top += scrollTop;\n      rect.left += scrollLeft;\n      rect.bottom += scrollTop;\n      rect.right += scrollLeft;\n    } else {\n      rect = element.getBoundingClientRect();\n    }\n  } catch (e) {}\n  var result = {\n    left: rect.left,\n    top: rect.top,\n    width: rect.right - rect.left,\n    height: rect.bottom - rect.top\n  };\n\n  // subtract scrollbar size from sizes\n  var sizes = element.nodeName === 'HTML' ? getWindowSizes(element.ownerDocument) : {};\n  var width = sizes.width || element.clientWidth || result.width;\n  var height = sizes.height || element.clientHeight || result.height;\n  var horizScrollbar = element.offsetWidth - width;\n  var vertScrollbar = element.offsetHeight - height;\n\n  // if an hypothetical scrollbar is detected, we must be sure it's not a `border`\n  // we make this check conditional for performance reasons\n  if (horizScrollbar || vertScrollbar) {\n    var styles = getStyleComputedProperty(element);\n    horizScrollbar -= getBordersSize(styles, 'x');\n    vertScrollbar -= getBordersSize(styles, 'y');\n    result.width -= horizScrollbar;\n    result.height -= vertScrollbar;\n  }\n  return getClientRect(result);\n}\nfunction getOffsetRectRelativeToArbitraryNode(children, parent) {\n  var fixedPosition = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  var isIE10 = isIE(10);\n  var isHTML = parent.nodeName === 'HTML';\n  var childrenRect = getBoundingClientRect(children);\n  var parentRect = getBoundingClientRect(parent);\n  var scrollParent = getScrollParent(children);\n  var styles = getStyleComputedProperty(parent);\n  var borderTopWidth = parseFloat(styles.borderTopWidth);\n  var borderLeftWidth = parseFloat(styles.borderLeftWidth);\n\n  // In cases where the parent is fixed, we must ignore negative scroll in offset calc\n  if (fixedPosition && isHTML) {\n    parentRect.top = Math.max(parentRect.top, 0);\n    parentRect.left = Math.max(parentRect.left, 0);\n  }\n  var offsets = getClientRect({\n    top: childrenRect.top - parentRect.top - borderTopWidth,\n    left: childrenRect.left - parentRect.left - borderLeftWidth,\n    width: childrenRect.width,\n    height: childrenRect.height\n  });\n  offsets.marginTop = 0;\n  offsets.marginLeft = 0;\n\n  // Subtract margins of documentElement in case it's being used as parent\n  // we do this only on HTML because it's the only element that behaves\n  // differently when margins are applied to it. The margins are included in\n  // the box of the documentElement, in the other cases not.\n  if (!isIE10 && isHTML) {\n    var marginTop = parseFloat(styles.marginTop);\n    var marginLeft = parseFloat(styles.marginLeft);\n    offsets.top -= borderTopWidth - marginTop;\n    offsets.bottom -= borderTopWidth - marginTop;\n    offsets.left -= borderLeftWidth - marginLeft;\n    offsets.right -= borderLeftWidth - marginLeft;\n\n    // Attach marginTop and marginLeft because in some circumstances we may need them\n    offsets.marginTop = marginTop;\n    offsets.marginLeft = marginLeft;\n  }\n  if (isIE10 && !fixedPosition ? parent.contains(scrollParent) : parent === scrollParent && scrollParent.nodeName !== 'BODY') {\n    offsets = includeScroll(offsets, parent);\n  }\n  return offsets;\n}\nfunction getViewportOffsetRectRelativeToArtbitraryNode(element) {\n  var excludeScroll = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  var html = element.ownerDocument.documentElement;\n  var relativeOffset = getOffsetRectRelativeToArbitraryNode(element, html);\n  var width = Math.max(html.clientWidth, window.innerWidth || 0);\n  var height = Math.max(html.clientHeight, window.innerHeight || 0);\n  var scrollTop = !excludeScroll ? getScroll(html) : 0;\n  var scrollLeft = !excludeScroll ? getScroll(html, 'left') : 0;\n  var offset = {\n    top: scrollTop - relativeOffset.top + relativeOffset.marginTop,\n    left: scrollLeft - relativeOffset.left + relativeOffset.marginLeft,\n    width: width,\n    height: height\n  };\n  return getClientRect(offset);\n}\n\n/**\n * Check if the given element is fixed or is inside a fixed parent\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {Element} customContainer\n * @returns {Boolean} answer to \"isFixed?\"\n */\nfunction isFixed(element) {\n  var nodeName = element.nodeName;\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    return false;\n  }\n  if (getStyleComputedProperty(element, 'position') === 'fixed') {\n    return true;\n  }\n  var parentNode = getParentNode(element);\n  if (!parentNode) {\n    return false;\n  }\n  return isFixed(parentNode);\n}\n\n/**\n * Finds the first parent of an element that has a transformed property defined\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} first transformed parent or documentElement\n */\n\nfunction getFixedPositionOffsetParent(element) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element || !element.parentElement || isIE()) {\n    return document.documentElement;\n  }\n  var el = element.parentElement;\n  while (el && getStyleComputedProperty(el, 'transform') === 'none') {\n    el = el.parentElement;\n  }\n  return el || document.documentElement;\n}\n\n/**\n * Computed the boundaries limits and return them\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} popper\n * @param {HTMLElement} reference\n * @param {number} padding\n * @param {HTMLElement} boundariesElement - Element used to define the boundaries\n * @param {Boolean} fixedPosition - Is in fixed position mode\n * @returns {Object} Coordinates of the boundaries\n */\nfunction getBoundaries(popper, reference, padding, boundariesElement) {\n  var fixedPosition = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : false;\n\n  // NOTE: 1 DOM access here\n\n  var boundaries = {\n    top: 0,\n    left: 0\n  };\n  var offsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, getReferenceNode(reference));\n\n  // Handle viewport case\n  if (boundariesElement === 'viewport') {\n    boundaries = getViewportOffsetRectRelativeToArtbitraryNode(offsetParent, fixedPosition);\n  } else {\n    // Handle other cases based on DOM element used as boundaries\n    var boundariesNode = void 0;\n    if (boundariesElement === 'scrollParent') {\n      boundariesNode = getScrollParent(getParentNode(reference));\n      if (boundariesNode.nodeName === 'BODY') {\n        boundariesNode = popper.ownerDocument.documentElement;\n      }\n    } else if (boundariesElement === 'window') {\n      boundariesNode = popper.ownerDocument.documentElement;\n    } else {\n      boundariesNode = boundariesElement;\n    }\n    var offsets = getOffsetRectRelativeToArbitraryNode(boundariesNode, offsetParent, fixedPosition);\n\n    // In case of HTML, we need a different computation\n    if (boundariesNode.nodeName === 'HTML' && !isFixed(offsetParent)) {\n      var _getWindowSizes = getWindowSizes(popper.ownerDocument),\n        height = _getWindowSizes.height,\n        width = _getWindowSizes.width;\n      boundaries.top += offsets.top - offsets.marginTop;\n      boundaries.bottom = height + offsets.top;\n      boundaries.left += offsets.left - offsets.marginLeft;\n      boundaries.right = width + offsets.left;\n    } else {\n      // for all the other DOM elements, this one is good\n      boundaries = offsets;\n    }\n  }\n\n  // Add paddings\n  padding = padding || 0;\n  var isPaddingNumber = typeof padding === 'number';\n  boundaries.left += isPaddingNumber ? padding : padding.left || 0;\n  boundaries.top += isPaddingNumber ? padding : padding.top || 0;\n  boundaries.right -= isPaddingNumber ? padding : padding.right || 0;\n  boundaries.bottom -= isPaddingNumber ? padding : padding.bottom || 0;\n  return boundaries;\n}\nfunction getArea(_ref) {\n  var width = _ref.width,\n    height = _ref.height;\n  return width * height;\n}\n\n/**\n * Utility used to transform the `auto` placement to the placement with more\n * available space.\n * @method\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction computeAutoPlacement(placement, refRect, popper, reference, boundariesElement) {\n  var padding = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : 0;\n  if (placement.indexOf('auto') === -1) {\n    return placement;\n  }\n  var boundaries = getBoundaries(popper, reference, padding, boundariesElement);\n  var rects = {\n    top: {\n      width: boundaries.width,\n      height: refRect.top - boundaries.top\n    },\n    right: {\n      width: boundaries.right - refRect.right,\n      height: boundaries.height\n    },\n    bottom: {\n      width: boundaries.width,\n      height: boundaries.bottom - refRect.bottom\n    },\n    left: {\n      width: refRect.left - boundaries.left,\n      height: boundaries.height\n    }\n  };\n  var sortedAreas = Object.keys(rects).map(function (key) {\n    return _extends({\n      key: key\n    }, rects[key], {\n      area: getArea(rects[key])\n    });\n  }).sort(function (a, b) {\n    return b.area - a.area;\n  });\n  var filteredAreas = sortedAreas.filter(function (_ref2) {\n    var width = _ref2.width,\n      height = _ref2.height;\n    return width >= popper.clientWidth && height >= popper.clientHeight;\n  });\n  var computedPlacement = filteredAreas.length > 0 ? filteredAreas[0].key : sortedAreas[0].key;\n  var variation = placement.split('-')[1];\n  return computedPlacement + (variation ? '-' + variation : '');\n}\n\n/**\n * Get offsets to the reference element\n * @method\n * @memberof Popper.Utils\n * @param {Object} state\n * @param {Element} popper - the popper element\n * @param {Element} reference - the reference element (the popper will be relative to this)\n * @param {Element} fixedPosition - is in fixed position mode\n * @returns {Object} An object containing the offsets which will be applied to the popper\n */\nfunction getReferenceOffsets(state, popper, reference) {\n  var fixedPosition = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : null;\n  var commonOffsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, getReferenceNode(reference));\n  return getOffsetRectRelativeToArbitraryNode(reference, commonOffsetParent, fixedPosition);\n}\n\n/**\n * Get the outer sizes of the given element (offset size + margins)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Object} object containing width and height properties\n */\nfunction getOuterSizes(element) {\n  var window = element.ownerDocument.defaultView;\n  var styles = window.getComputedStyle(element);\n  var x = parseFloat(styles.marginTop || 0) + parseFloat(styles.marginBottom || 0);\n  var y = parseFloat(styles.marginLeft || 0) + parseFloat(styles.marginRight || 0);\n  var result = {\n    width: element.offsetWidth + y,\n    height: element.offsetHeight + x\n  };\n  return result;\n}\n\n/**\n * Get the opposite placement of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement\n * @returns {String} flipped placement\n */\nfunction getOppositePlacement(placement) {\n  var hash = {\n    left: 'right',\n    right: 'left',\n    bottom: 'top',\n    top: 'bottom'\n  };\n  return placement.replace(/left|right|bottom|top/g, function (matched) {\n    return hash[matched];\n  });\n}\n\n/**\n * Get offsets to the popper\n * @method\n * @memberof Popper.Utils\n * @param {Object} position - CSS position the Popper will get applied\n * @param {HTMLElement} popper - the popper element\n * @param {Object} referenceOffsets - the reference offsets (the popper will be relative to this)\n * @param {String} placement - one of the valid placement options\n * @returns {Object} popperOffsets - An object containing the offsets which will be applied to the popper\n */\nfunction getPopperOffsets(popper, referenceOffsets, placement) {\n  placement = placement.split('-')[0];\n\n  // Get popper node sizes\n  var popperRect = getOuterSizes(popper);\n\n  // Add position, width and height to our offsets object\n  var popperOffsets = {\n    width: popperRect.width,\n    height: popperRect.height\n  };\n\n  // depending by the popper placement we have to compute its offsets slightly differently\n  var isHoriz = ['right', 'left'].indexOf(placement) !== -1;\n  var mainSide = isHoriz ? 'top' : 'left';\n  var secondarySide = isHoriz ? 'left' : 'top';\n  var measurement = isHoriz ? 'height' : 'width';\n  var secondaryMeasurement = !isHoriz ? 'height' : 'width';\n  popperOffsets[mainSide] = referenceOffsets[mainSide] + referenceOffsets[measurement] / 2 - popperRect[measurement] / 2;\n  if (placement === secondarySide) {\n    popperOffsets[secondarySide] = referenceOffsets[secondarySide] - popperRect[secondaryMeasurement];\n  } else {\n    popperOffsets[secondarySide] = referenceOffsets[getOppositePlacement(secondarySide)];\n  }\n  return popperOffsets;\n}\n\n/**\n * Mimics the `find` method of Array\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nfunction find(arr, check) {\n  // use native find if supported\n  if (Array.prototype.find) {\n    return arr.find(check);\n  }\n\n  // use `filter` to obtain the same behavior of `find`\n  return arr.filter(check)[0];\n}\n\n/**\n * Return the index of the matching object\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nfunction findIndex(arr, prop, value) {\n  // use native findIndex if supported\n  if (Array.prototype.findIndex) {\n    return arr.findIndex(function (cur) {\n      return cur[prop] === value;\n    });\n  }\n\n  // use `find` + `indexOf` if `findIndex` isn't supported\n  var match = find(arr, function (obj) {\n    return obj[prop] === value;\n  });\n  return arr.indexOf(match);\n}\n\n/**\n * Loop trough the list of modifiers and run them in order,\n * each of them will then edit the data object.\n * @method\n * @memberof Popper.Utils\n * @param {dataObject} data\n * @param {Array} modifiers\n * @param {String} ends - Optional modifier name used as stopper\n * @returns {dataObject}\n */\nfunction runModifiers(modifiers, data, ends) {\n  var modifiersToRun = ends === undefined ? modifiers : modifiers.slice(0, findIndex(modifiers, 'name', ends));\n  modifiersToRun.forEach(function (modifier) {\n    if (modifier['function']) {\n      // eslint-disable-line dot-notation\n      console.warn('`modifier.function` is deprecated, use `modifier.fn`!');\n    }\n    var fn = modifier['function'] || modifier.fn; // eslint-disable-line dot-notation\n    if (modifier.enabled && isFunction(fn)) {\n      // Add properties to offsets to make them a complete clientRect object\n      // we do this before each modifier to make sure the previous one doesn't\n      // mess with these values\n      data.offsets.popper = getClientRect(data.offsets.popper);\n      data.offsets.reference = getClientRect(data.offsets.reference);\n      data = fn(data, modifier);\n    }\n  });\n  return data;\n}\n\n/**\n * Updates the position of the popper, computing the new offsets and applying\n * the new style.<br />\n * Prefer `scheduleUpdate` over `update` because of performance reasons.\n * @method\n * @memberof Popper\n */\nfunction update() {\n  // if popper is destroyed, don't perform any further update\n  if (this.state.isDestroyed) {\n    return;\n  }\n  var data = {\n    instance: this,\n    styles: {},\n    arrowStyles: {},\n    attributes: {},\n    flipped: false,\n    offsets: {}\n  };\n\n  // compute reference element offsets\n  data.offsets.reference = getReferenceOffsets(this.state, this.popper, this.reference, this.options.positionFixed);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  data.placement = computeAutoPlacement(this.options.placement, data.offsets.reference, this.popper, this.reference, this.options.modifiers.flip.boundariesElement, this.options.modifiers.flip.padding);\n\n  // store the computed placement inside `originalPlacement`\n  data.originalPlacement = data.placement;\n  data.positionFixed = this.options.positionFixed;\n\n  // compute the popper offsets\n  data.offsets.popper = getPopperOffsets(this.popper, data.offsets.reference, data.placement);\n  data.offsets.popper.position = this.options.positionFixed ? 'fixed' : 'absolute';\n\n  // run the modifiers\n  data = runModifiers(this.modifiers, data);\n\n  // the first `update` will call `onCreate` callback\n  // the other ones will call `onUpdate` callback\n  if (!this.state.isCreated) {\n    this.state.isCreated = true;\n    this.options.onCreate(data);\n  } else {\n    this.options.onUpdate(data);\n  }\n}\n\n/**\n * Helper used to know if the given modifier is enabled.\n * @method\n * @memberof Popper.Utils\n * @returns {Boolean}\n */\nfunction isModifierEnabled(modifiers, modifierName) {\n  return modifiers.some(function (_ref) {\n    var name = _ref.name,\n      enabled = _ref.enabled;\n    return enabled && name === modifierName;\n  });\n}\n\n/**\n * Get the prefixed supported property name\n * @method\n * @memberof Popper.Utils\n * @argument {String} property (camelCase)\n * @returns {String} prefixed property (camelCase or PascalCase, depending on the vendor prefix)\n */\nfunction getSupportedPropertyName(property) {\n  var prefixes = [false, 'ms', 'Webkit', 'Moz', 'O'];\n  var upperProp = property.charAt(0).toUpperCase() + property.slice(1);\n  for (var i = 0; i < prefixes.length; i++) {\n    var prefix = prefixes[i];\n    var toCheck = prefix ? '' + prefix + upperProp : property;\n    if (typeof document.body.style[toCheck] !== 'undefined') {\n      return toCheck;\n    }\n  }\n  return null;\n}\n\n/**\n * Destroys the popper.\n * @method\n * @memberof Popper\n */\nfunction destroy() {\n  this.state.isDestroyed = true;\n\n  // touch DOM only if `applyStyle` modifier is enabled\n  if (isModifierEnabled(this.modifiers, 'applyStyle')) {\n    this.popper.removeAttribute('x-placement');\n    this.popper.style.position = '';\n    this.popper.style.top = '';\n    this.popper.style.left = '';\n    this.popper.style.right = '';\n    this.popper.style.bottom = '';\n    this.popper.style.willChange = '';\n    this.popper.style[getSupportedPropertyName('transform')] = '';\n  }\n  this.disableEventListeners();\n\n  // remove the popper if user explicitly asked for the deletion on destroy\n  // do not use `remove` because IE11 doesn't support it\n  if (this.options.removeOnDestroy) {\n    this.popper.parentNode.removeChild(this.popper);\n  }\n  return this;\n}\n\n/**\n * Get the window associated with the element\n * @argument {Element} element\n * @returns {Window}\n */\nfunction getWindow(element) {\n  var ownerDocument = element.ownerDocument;\n  return ownerDocument ? ownerDocument.defaultView : window;\n}\nfunction attachToScrollParents(scrollParent, event, callback, scrollParents) {\n  var isBody = scrollParent.nodeName === 'BODY';\n  var target = isBody ? scrollParent.ownerDocument.defaultView : scrollParent;\n  target.addEventListener(event, callback, {\n    passive: true\n  });\n  if (!isBody) {\n    attachToScrollParents(getScrollParent(target.parentNode), event, callback, scrollParents);\n  }\n  scrollParents.push(target);\n}\n\n/**\n * Setup needed event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nfunction setupEventListeners(reference, options, state, updateBound) {\n  // Resize event listener on window\n  state.updateBound = updateBound;\n  getWindow(reference).addEventListener('resize', state.updateBound, {\n    passive: true\n  });\n\n  // Scroll event listener on scroll parents\n  var scrollElement = getScrollParent(reference);\n  attachToScrollParents(scrollElement, 'scroll', state.updateBound, state.scrollParents);\n  state.scrollElement = scrollElement;\n  state.eventsEnabled = true;\n  return state;\n}\n\n/**\n * It will add resize/scroll events and start recalculating\n * position of the popper element when they are triggered.\n * @method\n * @memberof Popper\n */\nfunction enableEventListeners() {\n  if (!this.state.eventsEnabled) {\n    this.state = setupEventListeners(this.reference, this.options, this.state, this.scheduleUpdate);\n  }\n}\n\n/**\n * Remove event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nfunction removeEventListeners(reference, state) {\n  // Remove resize event listener on window\n  getWindow(reference).removeEventListener('resize', state.updateBound);\n\n  // Remove scroll event listener on scroll parents\n  state.scrollParents.forEach(function (target) {\n    target.removeEventListener('scroll', state.updateBound);\n  });\n\n  // Reset state\n  state.updateBound = null;\n  state.scrollParents = [];\n  state.scrollElement = null;\n  state.eventsEnabled = false;\n  return state;\n}\n\n/**\n * It will remove resize/scroll events and won't recalculate popper position\n * when they are triggered. It also won't trigger `onUpdate` callback anymore,\n * unless you call `update` method manually.\n * @method\n * @memberof Popper\n */\nfunction disableEventListeners() {\n  if (this.state.eventsEnabled) {\n    cancelAnimationFrame(this.scheduleUpdate);\n    this.state = removeEventListeners(this.reference, this.state);\n  }\n}\n\n/**\n * Tells if a given input is a number\n * @method\n * @memberof Popper.Utils\n * @param {*} input to check\n * @return {Boolean}\n */\nfunction isNumeric(n) {\n  return n !== '' && !isNaN(parseFloat(n)) && isFinite(n);\n}\n\n/**\n * Set the style to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the style to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nfunction setStyles(element, styles) {\n  Object.keys(styles).forEach(function (prop) {\n    var unit = '';\n    // add unit if the value is numeric and is one of the following\n    if (['width', 'height', 'top', 'right', 'bottom', 'left'].indexOf(prop) !== -1 && isNumeric(styles[prop])) {\n      unit = 'px';\n    }\n    element.style[prop] = styles[prop] + unit;\n  });\n}\n\n/**\n * Set the attributes to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the attributes to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nfunction setAttributes(element, attributes) {\n  Object.keys(attributes).forEach(function (prop) {\n    var value = attributes[prop];\n    if (value !== false) {\n      element.setAttribute(prop, attributes[prop]);\n    } else {\n      element.removeAttribute(prop);\n    }\n  });\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} data.styles - List of style properties - values to apply to popper element\n * @argument {Object} data.attributes - List of attribute properties - values to apply to popper element\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The same data object\n */\nfunction applyStyle(data) {\n  // any property present in `data.styles` will be applied to the popper,\n  // in this way we can make the 3rd party modifiers add custom styles to it\n  // Be aware, modifiers could override the properties defined in the previous\n  // lines of this modifier!\n  setStyles(data.instance.popper, data.styles);\n\n  // any property present in `data.attributes` will be applied to the popper,\n  // they will be set as HTML attributes of the element\n  setAttributes(data.instance.popper, data.attributes);\n\n  // if arrowElement is defined and arrowStyles has some properties\n  if (data.arrowElement && Object.keys(data.arrowStyles).length) {\n    setStyles(data.arrowElement, data.arrowStyles);\n  }\n  return data;\n}\n\n/**\n * Set the x-placement attribute before everything else because it could be used\n * to add margins to the popper margins needs to be calculated to get the\n * correct popper offsets.\n * @method\n * @memberof Popper.modifiers\n * @param {HTMLElement} reference - The reference element used to position the popper\n * @param {HTMLElement} popper - The HTML element used as popper\n * @param {Object} options - Popper.js options\n */\nfunction applyStyleOnLoad(reference, popper, options, modifierOptions, state) {\n  // compute reference element offsets\n  var referenceOffsets = getReferenceOffsets(state, popper, reference, options.positionFixed);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  var placement = computeAutoPlacement(options.placement, referenceOffsets, popper, reference, options.modifiers.flip.boundariesElement, options.modifiers.flip.padding);\n  popper.setAttribute('x-placement', placement);\n\n  // Apply `position` to popper before anything else because\n  // without the position applied we can't guarantee correct computations\n  setStyles(popper, {\n    position: options.positionFixed ? 'fixed' : 'absolute'\n  });\n  return options;\n}\n\n/**\n * @function\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Boolean} shouldRound - If the offsets should be rounded at all\n * @returns {Object} The popper's position offsets rounded\n *\n * The tale of pixel-perfect positioning. It's still not 100% perfect, but as\n * good as it can be within reason.\n * Discussion here: https://github.com/FezVrasta/popper.js/pull/715\n *\n * Low DPI screens cause a popper to be blurry if not using full pixels (Safari\n * as well on High DPI screens).\n *\n * Firefox prefers no rounding for positioning and does not have blurriness on\n * high DPI screens.\n *\n * Only horizontal placement and left/right values need to be considered.\n */\nfunction getRoundedOffsets(data, shouldRound) {\n  var _data$offsets = data.offsets,\n    popper = _data$offsets.popper,\n    reference = _data$offsets.reference;\n  var round = Math.round,\n    floor = Math.floor;\n  var noRound = function noRound(v) {\n    return v;\n  };\n  var referenceWidth = round(reference.width);\n  var popperWidth = round(popper.width);\n  var isVertical = ['left', 'right'].indexOf(data.placement) !== -1;\n  var isVariation = data.placement.indexOf('-') !== -1;\n  var sameWidthParity = referenceWidth % 2 === popperWidth % 2;\n  var bothOddWidth = referenceWidth % 2 === 1 && popperWidth % 2 === 1;\n  var horizontalToInteger = !shouldRound ? noRound : isVertical || isVariation || sameWidthParity ? round : floor;\n  var verticalToInteger = !shouldRound ? noRound : round;\n  return {\n    left: horizontalToInteger(bothOddWidth && !isVariation && shouldRound ? popper.left - 1 : popper.left),\n    top: verticalToInteger(popper.top),\n    bottom: verticalToInteger(popper.bottom),\n    right: horizontalToInteger(popper.right)\n  };\n}\nvar isFirefox = isBrowser && /Firefox/i.test(navigator.userAgent);\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction computeStyle(data, options) {\n  var x = options.x,\n    y = options.y;\n  var popper = data.offsets.popper;\n\n  // Remove this legacy support in Popper.js v2\n\n  var legacyGpuAccelerationOption = find(data.instance.modifiers, function (modifier) {\n    return modifier.name === 'applyStyle';\n  }).gpuAcceleration;\n  if (legacyGpuAccelerationOption !== undefined) {\n    console.warn('WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!');\n  }\n  var gpuAcceleration = legacyGpuAccelerationOption !== undefined ? legacyGpuAccelerationOption : options.gpuAcceleration;\n  var offsetParent = getOffsetParent(data.instance.popper);\n  var offsetParentRect = getBoundingClientRect(offsetParent);\n\n  // Styles\n  var styles = {\n    position: popper.position\n  };\n  var offsets = getRoundedOffsets(data, window.devicePixelRatio < 2 || !isFirefox);\n  var sideA = x === 'bottom' ? 'top' : 'bottom';\n  var sideB = y === 'right' ? 'left' : 'right';\n\n  // if gpuAcceleration is set to `true` and transform is supported,\n  //  we use `translate3d` to apply the position to the popper we\n  // automatically use the supported prefixed version if needed\n  var prefixedProperty = getSupportedPropertyName('transform');\n\n  // now, let's make a step back and look at this code closely (wtf?)\n  // If the content of the popper grows once it's been positioned, it\n  // may happen that the popper gets misplaced because of the new content\n  // overflowing its reference element\n  // To avoid this problem, we provide two options (x and y), which allow\n  // the consumer to define the offset origin.\n  // If we position a popper on top of a reference element, we can set\n  // `x` to `top` to make the popper grow towards its top instead of\n  // its bottom.\n  var left = void 0,\n    top = void 0;\n  if (sideA === 'bottom') {\n    // when offsetParent is <html> the positioning is relative to the bottom of the screen (excluding the scrollbar)\n    // and not the bottom of the html element\n    if (offsetParent.nodeName === 'HTML') {\n      top = -offsetParent.clientHeight + offsets.bottom;\n    } else {\n      top = -offsetParentRect.height + offsets.bottom;\n    }\n  } else {\n    top = offsets.top;\n  }\n  if (sideB === 'right') {\n    if (offsetParent.nodeName === 'HTML') {\n      left = -offsetParent.clientWidth + offsets.right;\n    } else {\n      left = -offsetParentRect.width + offsets.right;\n    }\n  } else {\n    left = offsets.left;\n  }\n  if (gpuAcceleration && prefixedProperty) {\n    styles[prefixedProperty] = 'translate3d(' + left + 'px, ' + top + 'px, 0)';\n    styles[sideA] = 0;\n    styles[sideB] = 0;\n    styles.willChange = 'transform';\n  } else {\n    // othwerise, we use the standard `top`, `left`, `bottom` and `right` properties\n    var invertTop = sideA === 'bottom' ? -1 : 1;\n    var invertLeft = sideB === 'right' ? -1 : 1;\n    styles[sideA] = top * invertTop;\n    styles[sideB] = left * invertLeft;\n    styles.willChange = sideA + ', ' + sideB;\n  }\n\n  // Attributes\n  var attributes = {\n    'x-placement': data.placement\n  };\n\n  // Update `data` attributes, styles and arrowStyles\n  data.attributes = _extends({}, attributes, data.attributes);\n  data.styles = _extends({}, styles, data.styles);\n  data.arrowStyles = _extends({}, data.offsets.arrow, data.arrowStyles);\n  return data;\n}\n\n/**\n * Helper used to know if the given modifier depends from another one.<br />\n * It checks if the needed modifier is listed and enabled.\n * @method\n * @memberof Popper.Utils\n * @param {Array} modifiers - list of modifiers\n * @param {String} requestingName - name of requesting modifier\n * @param {String} requestedName - name of requested modifier\n * @returns {Boolean}\n */\nfunction isModifierRequired(modifiers, requestingName, requestedName) {\n  var requesting = find(modifiers, function (_ref) {\n    var name = _ref.name;\n    return name === requestingName;\n  });\n  var isRequired = !!requesting && modifiers.some(function (modifier) {\n    return modifier.name === requestedName && modifier.enabled && modifier.order < requesting.order;\n  });\n  if (!isRequired) {\n    var _requesting = '`' + requestingName + '`';\n    var requested = '`' + requestedName + '`';\n    console.warn(requested + ' modifier is required by ' + _requesting + ' modifier in order to work, be sure to include it before ' + _requesting + '!');\n  }\n  return isRequired;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction arrow(data, options) {\n  var _data$offsets$arrow;\n\n  // arrow depends on keepTogether in order to work\n  if (!isModifierRequired(data.instance.modifiers, 'arrow', 'keepTogether')) {\n    return data;\n  }\n  var arrowElement = options.element;\n\n  // if arrowElement is a string, suppose it's a CSS selector\n  if (typeof arrowElement === 'string') {\n    arrowElement = data.instance.popper.querySelector(arrowElement);\n\n    // if arrowElement is not found, don't run the modifier\n    if (!arrowElement) {\n      return data;\n    }\n  } else {\n    // if the arrowElement isn't a query selector we must check that the\n    // provided DOM node is child of its popper node\n    if (!data.instance.popper.contains(arrowElement)) {\n      console.warn('WARNING: `arrow.element` must be child of its popper element!');\n      return data;\n    }\n  }\n  var placement = data.placement.split('-')[0];\n  var _data$offsets = data.offsets,\n    popper = _data$offsets.popper,\n    reference = _data$offsets.reference;\n  var isVertical = ['left', 'right'].indexOf(placement) !== -1;\n  var len = isVertical ? 'height' : 'width';\n  var sideCapitalized = isVertical ? 'Top' : 'Left';\n  var side = sideCapitalized.toLowerCase();\n  var altSide = isVertical ? 'left' : 'top';\n  var opSide = isVertical ? 'bottom' : 'right';\n  var arrowElementSize = getOuterSizes(arrowElement)[len];\n\n  //\n  // extends keepTogether behavior making sure the popper and its\n  // reference have enough pixels in conjunction\n  //\n\n  // top/left side\n  if (reference[opSide] - arrowElementSize < popper[side]) {\n    data.offsets.popper[side] -= popper[side] - (reference[opSide] - arrowElementSize);\n  }\n  // bottom/right side\n  if (reference[side] + arrowElementSize > popper[opSide]) {\n    data.offsets.popper[side] += reference[side] + arrowElementSize - popper[opSide];\n  }\n  data.offsets.popper = getClientRect(data.offsets.popper);\n\n  // compute center of the popper\n  var center = reference[side] + reference[len] / 2 - arrowElementSize / 2;\n\n  // Compute the sideValue using the updated popper offsets\n  // take popper margin in account because we don't have this info available\n  var css = getStyleComputedProperty(data.instance.popper);\n  var popperMarginSide = parseFloat(css['margin' + sideCapitalized]);\n  var popperBorderSide = parseFloat(css['border' + sideCapitalized + 'Width']);\n  var sideValue = center - data.offsets.popper[side] - popperMarginSide - popperBorderSide;\n\n  // prevent arrowElement from being placed not contiguously to its popper\n  sideValue = Math.max(Math.min(popper[len] - arrowElementSize, sideValue), 0);\n  data.arrowElement = arrowElement;\n  data.offsets.arrow = (_data$offsets$arrow = {}, defineProperty(_data$offsets$arrow, side, Math.round(sideValue)), defineProperty(_data$offsets$arrow, altSide, ''), _data$offsets$arrow);\n  return data;\n}\n\n/**\n * Get the opposite placement variation of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement variation\n * @returns {String} flipped placement variation\n */\nfunction getOppositeVariation(variation) {\n  if (variation === 'end') {\n    return 'start';\n  } else if (variation === 'start') {\n    return 'end';\n  }\n  return variation;\n}\n\n/**\n * List of accepted placements to use as values of the `placement` option.<br />\n * Valid placements are:\n * - `auto`\n * - `top`\n * - `right`\n * - `bottom`\n * - `left`\n *\n * Each placement can have a variation from this list:\n * - `-start`\n * - `-end`\n *\n * Variations are interpreted easily if you think of them as the left to right\n * written languages. Horizontally (`top` and `bottom`), `start` is left and `end`\n * is right.<br />\n * Vertically (`left` and `right`), `start` is top and `end` is bottom.\n *\n * Some valid examples are:\n * - `top-end` (on top of reference, right aligned)\n * - `right-start` (on right of reference, top aligned)\n * - `bottom` (on bottom, centered)\n * - `auto-end` (on the side with more space available, alignment depends by placement)\n *\n * @static\n * @type {Array}\n * @enum {String}\n * @readonly\n * @method placements\n * @memberof Popper\n */\nvar placements = ['auto-start', 'auto', 'auto-end', 'top-start', 'top', 'top-end', 'right-start', 'right', 'right-end', 'bottom-end', 'bottom', 'bottom-start', 'left-end', 'left', 'left-start'];\n\n// Get rid of `auto` `auto-start` and `auto-end`\nvar validPlacements = placements.slice(3);\n\n/**\n * Given an initial placement, returns all the subsequent placements\n * clockwise (or counter-clockwise).\n *\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement - A valid placement (it accepts variations)\n * @argument {Boolean} counter - Set to true to walk the placements counterclockwise\n * @returns {Array} placements including their variations\n */\nfunction clockwise(placement) {\n  var counter = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  var index = validPlacements.indexOf(placement);\n  var arr = validPlacements.slice(index + 1).concat(validPlacements.slice(0, index));\n  return counter ? arr.reverse() : arr;\n}\nvar BEHAVIORS = {\n  FLIP: 'flip',\n  CLOCKWISE: 'clockwise',\n  COUNTERCLOCKWISE: 'counterclockwise'\n};\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction flip(data, options) {\n  // if `inner` modifier is enabled, we can't use the `flip` modifier\n  if (isModifierEnabled(data.instance.modifiers, 'inner')) {\n    return data;\n  }\n  if (data.flipped && data.placement === data.originalPlacement) {\n    // seems like flip is trying to loop, probably there's not enough space on any of the flippable sides\n    return data;\n  }\n  var boundaries = getBoundaries(data.instance.popper, data.instance.reference, options.padding, options.boundariesElement, data.positionFixed);\n  var placement = data.placement.split('-')[0];\n  var placementOpposite = getOppositePlacement(placement);\n  var variation = data.placement.split('-')[1] || '';\n  var flipOrder = [];\n  switch (options.behavior) {\n    case BEHAVIORS.FLIP:\n      flipOrder = [placement, placementOpposite];\n      break;\n    case BEHAVIORS.CLOCKWISE:\n      flipOrder = clockwise(placement);\n      break;\n    case BEHAVIORS.COUNTERCLOCKWISE:\n      flipOrder = clockwise(placement, true);\n      break;\n    default:\n      flipOrder = options.behavior;\n  }\n  flipOrder.forEach(function (step, index) {\n    if (placement !== step || flipOrder.length === index + 1) {\n      return data;\n    }\n    placement = data.placement.split('-')[0];\n    placementOpposite = getOppositePlacement(placement);\n    var popperOffsets = data.offsets.popper;\n    var refOffsets = data.offsets.reference;\n\n    // using floor because the reference offsets may contain decimals we are not going to consider here\n    var floor = Math.floor;\n    var overlapsRef = placement === 'left' && floor(popperOffsets.right) > floor(refOffsets.left) || placement === 'right' && floor(popperOffsets.left) < floor(refOffsets.right) || placement === 'top' && floor(popperOffsets.bottom) > floor(refOffsets.top) || placement === 'bottom' && floor(popperOffsets.top) < floor(refOffsets.bottom);\n    var overflowsLeft = floor(popperOffsets.left) < floor(boundaries.left);\n    var overflowsRight = floor(popperOffsets.right) > floor(boundaries.right);\n    var overflowsTop = floor(popperOffsets.top) < floor(boundaries.top);\n    var overflowsBottom = floor(popperOffsets.bottom) > floor(boundaries.bottom);\n    var overflowsBoundaries = placement === 'left' && overflowsLeft || placement === 'right' && overflowsRight || placement === 'top' && overflowsTop || placement === 'bottom' && overflowsBottom;\n\n    // flip the variation if required\n    var isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n\n    // flips variation if reference element overflows boundaries\n    var flippedVariationByRef = !!options.flipVariations && (isVertical && variation === 'start' && overflowsLeft || isVertical && variation === 'end' && overflowsRight || !isVertical && variation === 'start' && overflowsTop || !isVertical && variation === 'end' && overflowsBottom);\n\n    // flips variation if popper content overflows boundaries\n    var flippedVariationByContent = !!options.flipVariationsByContent && (isVertical && variation === 'start' && overflowsRight || isVertical && variation === 'end' && overflowsLeft || !isVertical && variation === 'start' && overflowsBottom || !isVertical && variation === 'end' && overflowsTop);\n    var flippedVariation = flippedVariationByRef || flippedVariationByContent;\n    if (overlapsRef || overflowsBoundaries || flippedVariation) {\n      // this boolean to detect any flip loop\n      data.flipped = true;\n      if (overlapsRef || overflowsBoundaries) {\n        placement = flipOrder[index + 1];\n      }\n      if (flippedVariation) {\n        variation = getOppositeVariation(variation);\n      }\n      data.placement = placement + (variation ? '-' + variation : '');\n\n      // this object contains `position`, we want to preserve it along with\n      // any additional property we may add in the future\n      data.offsets.popper = _extends({}, data.offsets.popper, getPopperOffsets(data.instance.popper, data.offsets.reference, data.placement));\n      data = runModifiers(data.instance.modifiers, data, 'flip');\n    }\n  });\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction keepTogether(data) {\n  var _data$offsets = data.offsets,\n    popper = _data$offsets.popper,\n    reference = _data$offsets.reference;\n  var placement = data.placement.split('-')[0];\n  var floor = Math.floor;\n  var isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n  var side = isVertical ? 'right' : 'bottom';\n  var opSide = isVertical ? 'left' : 'top';\n  var measurement = isVertical ? 'width' : 'height';\n  if (popper[side] < floor(reference[opSide])) {\n    data.offsets.popper[opSide] = floor(reference[opSide]) - popper[measurement];\n  }\n  if (popper[opSide] > floor(reference[side])) {\n    data.offsets.popper[opSide] = floor(reference[side]);\n  }\n  return data;\n}\n\n/**\n * Converts a string containing value + unit into a px value number\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} str - Value + unit string\n * @argument {String} measurement - `height` or `width`\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @returns {Number|String}\n * Value in pixels, or original string if no values were extracted\n */\nfunction toValue(str, measurement, popperOffsets, referenceOffsets) {\n  // separate value from unit\n  var split = str.match(/((?:\\-|\\+)?\\d*\\.?\\d*)(.*)/);\n  var value = +split[1];\n  var unit = split[2];\n\n  // If it's not a number it's an operator, I guess\n  if (!value) {\n    return str;\n  }\n  if (unit.indexOf('%') === 0) {\n    var element = void 0;\n    switch (unit) {\n      case '%p':\n        element = popperOffsets;\n        break;\n      case '%':\n      case '%r':\n      default:\n        element = referenceOffsets;\n    }\n    var rect = getClientRect(element);\n    return rect[measurement] / 100 * value;\n  } else if (unit === 'vh' || unit === 'vw') {\n    // if is a vh or vw, we calculate the size based on the viewport\n    var size = void 0;\n    if (unit === 'vh') {\n      size = Math.max(document.documentElement.clientHeight, window.innerHeight || 0);\n    } else {\n      size = Math.max(document.documentElement.clientWidth, window.innerWidth || 0);\n    }\n    return size / 100 * value;\n  } else {\n    // if is an explicit pixel unit, we get rid of the unit and keep the value\n    // if is an implicit unit, it's px, and we return just the value\n    return value;\n  }\n}\n\n/**\n * Parse an `offset` string to extrapolate `x` and `y` numeric offsets.\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} offset\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @argument {String} basePlacement\n * @returns {Array} a two cells array with x and y offsets in numbers\n */\nfunction parseOffset(offset, popperOffsets, referenceOffsets, basePlacement) {\n  var offsets = [0, 0];\n\n  // Use height if placement is left or right and index is 0 otherwise use width\n  // in this way the first offset will use an axis and the second one\n  // will use the other one\n  var useHeight = ['right', 'left'].indexOf(basePlacement) !== -1;\n\n  // Split the offset string to obtain a list of values and operands\n  // The regex addresses values with the plus or minus sign in front (+10, -20, etc)\n  var fragments = offset.split(/(\\+|\\-)/).map(function (frag) {\n    return frag.trim();\n  });\n\n  // Detect if the offset string contains a pair of values or a single one\n  // they could be separated by comma or space\n  var divider = fragments.indexOf(find(fragments, function (frag) {\n    return frag.search(/,|\\s/) !== -1;\n  }));\n  if (fragments[divider] && fragments[divider].indexOf(',') === -1) {\n    console.warn('Offsets separated by white space(s) are deprecated, use a comma (,) instead.');\n  }\n\n  // If divider is found, we divide the list of values and operands to divide\n  // them by ofset X and Y.\n  var splitRegex = /\\s*,\\s*|\\s+/;\n  var ops = divider !== -1 ? [fragments.slice(0, divider).concat([fragments[divider].split(splitRegex)[0]]), [fragments[divider].split(splitRegex)[1]].concat(fragments.slice(divider + 1))] : [fragments];\n\n  // Convert the values with units to absolute pixels to allow our computations\n  ops = ops.map(function (op, index) {\n    // Most of the units rely on the orientation of the popper\n    var measurement = (index === 1 ? !useHeight : useHeight) ? 'height' : 'width';\n    var mergeWithPrevious = false;\n    return op\n    // This aggregates any `+` or `-` sign that aren't considered operators\n    // e.g.: 10 + +5 => [10, +, +5]\n    .reduce(function (a, b) {\n      if (a[a.length - 1] === '' && ['+', '-'].indexOf(b) !== -1) {\n        a[a.length - 1] = b;\n        mergeWithPrevious = true;\n        return a;\n      } else if (mergeWithPrevious) {\n        a[a.length - 1] += b;\n        mergeWithPrevious = false;\n        return a;\n      } else {\n        return a.concat(b);\n      }\n    }, [])\n    // Here we convert the string values into number values (in px)\n    .map(function (str) {\n      return toValue(str, measurement, popperOffsets, referenceOffsets);\n    });\n  });\n\n  // Loop trough the offsets arrays and execute the operations\n  ops.forEach(function (op, index) {\n    op.forEach(function (frag, index2) {\n      if (isNumeric(frag)) {\n        offsets[index] += frag * (op[index2 - 1] === '-' ? -1 : 1);\n      }\n    });\n  });\n  return offsets;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @argument {Number|String} options.offset=0\n * The offset value as described in the modifier description\n * @returns {Object} The data object, properly modified\n */\nfunction offset(data, _ref) {\n  var offset = _ref.offset;\n  var placement = data.placement,\n    _data$offsets = data.offsets,\n    popper = _data$offsets.popper,\n    reference = _data$offsets.reference;\n  var basePlacement = placement.split('-')[0];\n  var offsets = void 0;\n  if (isNumeric(+offset)) {\n    offsets = [+offset, 0];\n  } else {\n    offsets = parseOffset(offset, popper, reference, basePlacement);\n  }\n  if (basePlacement === 'left') {\n    popper.top += offsets[0];\n    popper.left -= offsets[1];\n  } else if (basePlacement === 'right') {\n    popper.top += offsets[0];\n    popper.left += offsets[1];\n  } else if (basePlacement === 'top') {\n    popper.left += offsets[0];\n    popper.top -= offsets[1];\n  } else if (basePlacement === 'bottom') {\n    popper.left += offsets[0];\n    popper.top += offsets[1];\n  }\n  data.popper = popper;\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction preventOverflow(data, options) {\n  var boundariesElement = options.boundariesElement || getOffsetParent(data.instance.popper);\n\n  // If offsetParent is the reference element, we really want to\n  // go one step up and use the next offsetParent as reference to\n  // avoid to make this modifier completely useless and look like broken\n  if (data.instance.reference === boundariesElement) {\n    boundariesElement = getOffsetParent(boundariesElement);\n  }\n\n  // NOTE: DOM access here\n  // resets the popper's position so that the document size can be calculated excluding\n  // the size of the popper element itself\n  var transformProp = getSupportedPropertyName('transform');\n  var popperStyles = data.instance.popper.style; // assignment to help minification\n  var top = popperStyles.top,\n    left = popperStyles.left,\n    transform = popperStyles[transformProp];\n  popperStyles.top = '';\n  popperStyles.left = '';\n  popperStyles[transformProp] = '';\n  var boundaries = getBoundaries(data.instance.popper, data.instance.reference, options.padding, boundariesElement, data.positionFixed);\n\n  // NOTE: DOM access here\n  // restores the original style properties after the offsets have been computed\n  popperStyles.top = top;\n  popperStyles.left = left;\n  popperStyles[transformProp] = transform;\n  options.boundaries = boundaries;\n  var order = options.priority;\n  var popper = data.offsets.popper;\n  var check = {\n    primary: function primary(placement) {\n      var value = popper[placement];\n      if (popper[placement] < boundaries[placement] && !options.escapeWithReference) {\n        value = Math.max(popper[placement], boundaries[placement]);\n      }\n      return defineProperty({}, placement, value);\n    },\n    secondary: function secondary(placement) {\n      var mainSide = placement === 'right' ? 'left' : 'top';\n      var value = popper[mainSide];\n      if (popper[placement] > boundaries[placement] && !options.escapeWithReference) {\n        value = Math.min(popper[mainSide], boundaries[placement] - (placement === 'right' ? popper.width : popper.height));\n      }\n      return defineProperty({}, mainSide, value);\n    }\n  };\n  order.forEach(function (placement) {\n    var side = ['left', 'top'].indexOf(placement) !== -1 ? 'primary' : 'secondary';\n    popper = _extends({}, popper, check[side](placement));\n  });\n  data.offsets.popper = popper;\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction shift(data) {\n  var placement = data.placement;\n  var basePlacement = placement.split('-')[0];\n  var shiftvariation = placement.split('-')[1];\n\n  // if shift shiftvariation is specified, run the modifier\n  if (shiftvariation) {\n    var _data$offsets = data.offsets,\n      reference = _data$offsets.reference,\n      popper = _data$offsets.popper;\n    var isVertical = ['bottom', 'top'].indexOf(basePlacement) !== -1;\n    var side = isVertical ? 'left' : 'top';\n    var measurement = isVertical ? 'width' : 'height';\n    var shiftOffsets = {\n      start: defineProperty({}, side, reference[side]),\n      end: defineProperty({}, side, reference[side] + reference[measurement] - popper[measurement])\n    };\n    data.offsets.popper = _extends({}, popper, shiftOffsets[shiftvariation]);\n  }\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction hide(data) {\n  if (!isModifierRequired(data.instance.modifiers, 'hide', 'preventOverflow')) {\n    return data;\n  }\n  var refRect = data.offsets.reference;\n  var bound = find(data.instance.modifiers, function (modifier) {\n    return modifier.name === 'preventOverflow';\n  }).boundaries;\n  if (refRect.bottom < bound.top || refRect.left > bound.right || refRect.top > bound.bottom || refRect.right < bound.left) {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === true) {\n      return data;\n    }\n    data.hide = true;\n    data.attributes['x-out-of-boundaries'] = '';\n  } else {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === false) {\n      return data;\n    }\n    data.hide = false;\n    data.attributes['x-out-of-boundaries'] = false;\n  }\n  return data;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nfunction inner(data) {\n  var placement = data.placement;\n  var basePlacement = placement.split('-')[0];\n  var _data$offsets = data.offsets,\n    popper = _data$offsets.popper,\n    reference = _data$offsets.reference;\n  var isHoriz = ['left', 'right'].indexOf(basePlacement) !== -1;\n  var subtractLength = ['top', 'left'].indexOf(basePlacement) === -1;\n  popper[isHoriz ? 'left' : 'top'] = reference[basePlacement] - (subtractLength ? popper[isHoriz ? 'width' : 'height'] : 0);\n  data.placement = getOppositePlacement(placement);\n  data.offsets.popper = getClientRect(popper);\n  return data;\n}\n\n/**\n * Modifier function, each modifier can have a function of this type assigned\n * to its `fn` property.<br />\n * These functions will be called on each update, this means that you must\n * make sure they are performant enough to avoid performance bottlenecks.\n *\n * @function ModifierFn\n * @argument {dataObject} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {dataObject} The data object, properly modified\n */\n\n/**\n * Modifiers are plugins used to alter the behavior of your poppers.<br />\n * Popper.js uses a set of 9 modifiers to provide all the basic functionalities\n * needed by the library.\n *\n * Usually you don't want to override the `order`, `fn` and `onLoad` props.\n * All the other properties are configurations that could be tweaked.\n * @namespace modifiers\n */\nvar modifiers = {\n  /**\n   * Modifier used to shift the popper on the start or end of its reference\n   * element.<br />\n   * It will read the variation of the `placement` property.<br />\n   * It can be one either `-end` or `-start`.\n   * @memberof modifiers\n   * @inner\n   */\n  shift: {\n    /** @prop {number} order=100 - Index used to define the order of execution */\n    order: 100,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: shift\n  },\n  /**\n   * The `offset` modifier can shift your popper on both its axis.\n   *\n   * It accepts the following units:\n   * - `px` or unit-less, interpreted as pixels\n   * - `%` or `%r`, percentage relative to the length of the reference element\n   * - `%p`, percentage relative to the length of the popper element\n   * - `vw`, CSS viewport width unit\n   * - `vh`, CSS viewport height unit\n   *\n   * For length is intended the main axis relative to the placement of the popper.<br />\n   * This means that if the placement is `top` or `bottom`, the length will be the\n   * `width`. In case of `left` or `right`, it will be the `height`.\n   *\n   * You can provide a single value (as `Number` or `String`), or a pair of values\n   * as `String` divided by a comma or one (or more) white spaces.<br />\n   * The latter is a deprecated method because it leads to confusion and will be\n   * removed in v2.<br />\n   * Additionally, it accepts additions and subtractions between different units.\n   * Note that multiplications and divisions aren't supported.\n   *\n   * Valid examples are:\n   * ```\n   * 10\n   * '10%'\n   * '10, 10'\n   * '10%, 10'\n   * '10 + 10%'\n   * '10 - 5vh + 3%'\n   * '-10px + 5vh, 5px - 6%'\n   * ```\n   * > **NB**: If you desire to apply offsets to your poppers in a way that may make them overlap\n   * > with their reference element, unfortunately, you will have to disable the `flip` modifier.\n   * > You can read more on this at this [issue](https://github.com/FezVrasta/popper.js/issues/373).\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  offset: {\n    /** @prop {number} order=200 - Index used to define the order of execution */\n    order: 200,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: offset,\n    /** @prop {Number|String} offset=0\n     * The offset value as described in the modifier description\n     */\n    offset: 0\n  },\n  /**\n   * Modifier used to prevent the popper from being positioned outside the boundary.\n   *\n   * A scenario exists where the reference itself is not within the boundaries.<br />\n   * We can say it has \"escaped the boundaries\" — or just \"escaped\".<br />\n   * In this case we need to decide whether the popper should either:\n   *\n   * - detach from the reference and remain \"trapped\" in the boundaries, or\n   * - if it should ignore the boundary and \"escape with its reference\"\n   *\n   * When `escapeWithReference` is set to`true` and reference is completely\n   * outside its boundaries, the popper will overflow (or completely leave)\n   * the boundaries in order to remain attached to the edge of the reference.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  preventOverflow: {\n    /** @prop {number} order=300 - Index used to define the order of execution */\n    order: 300,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: preventOverflow,\n    /**\n     * @prop {Array} [priority=['left','right','top','bottom']]\n     * Popper will try to prevent overflow following these priorities by default,\n     * then, it could overflow on the left and on top of the `boundariesElement`\n     */\n    priority: ['left', 'right', 'top', 'bottom'],\n    /**\n     * @prop {number} padding=5\n     * Amount of pixel used to define a minimum distance between the boundaries\n     * and the popper. This makes sure the popper always has a little padding\n     * between the edges of its container\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='scrollParent'\n     * Boundaries used by the modifier. Can be `scrollParent`, `window`,\n     * `viewport` or any DOM element.\n     */\n    boundariesElement: 'scrollParent'\n  },\n  /**\n   * Modifier used to make sure the reference and its popper stay near each other\n   * without leaving any gap between the two. Especially useful when the arrow is\n   * enabled and you want to ensure that it points to its reference element.\n   * It cares only about the first axis. You can still have poppers with margin\n   * between the popper and its reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  keepTogether: {\n    /** @prop {number} order=400 - Index used to define the order of execution */\n    order: 400,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: keepTogether\n  },\n  /**\n   * This modifier is used to move the `arrowElement` of the popper to make\n   * sure it is positioned between the reference element and its popper element.\n   * It will read the outer size of the `arrowElement` node to detect how many\n   * pixels of conjunction are needed.\n   *\n   * It has no effect if no `arrowElement` is provided.\n   * @memberof modifiers\n   * @inner\n   */\n  arrow: {\n    /** @prop {number} order=500 - Index used to define the order of execution */\n    order: 500,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: arrow,\n    /** @prop {String|HTMLElement} element='[x-arrow]' - Selector or node used as arrow */\n    element: '[x-arrow]'\n  },\n  /**\n   * Modifier used to flip the popper's placement when it starts to overlap its\n   * reference element.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   *\n   * **NOTE:** this modifier will interrupt the current update cycle and will\n   * restart it if it detects the need to flip the placement.\n   * @memberof modifiers\n   * @inner\n   */\n  flip: {\n    /** @prop {number} order=600 - Index used to define the order of execution */\n    order: 600,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: flip,\n    /**\n     * @prop {String|Array} behavior='flip'\n     * The behavior used to change the popper's placement. It can be one of\n     * `flip`, `clockwise`, `counterclockwise` or an array with a list of valid\n     * placements (with optional variations)\n     */\n    behavior: 'flip',\n    /**\n     * @prop {number} padding=5\n     * The popper will flip if it hits the edges of the `boundariesElement`\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='viewport'\n     * The element which will define the boundaries of the popper position.\n     * The popper will never be placed outside of the defined boundaries\n     * (except if `keepTogether` is enabled)\n     */\n    boundariesElement: 'viewport',\n    /**\n     * @prop {Boolean} flipVariations=false\n     * The popper will switch placement variation between `-start` and `-end` when\n     * the reference element overlaps its boundaries.\n     *\n     * The original placement should have a set variation.\n     */\n    flipVariations: false,\n    /**\n     * @prop {Boolean} flipVariationsByContent=false\n     * The popper will switch placement variation between `-start` and `-end` when\n     * the popper element overlaps its reference boundaries.\n     *\n     * The original placement should have a set variation.\n     */\n    flipVariationsByContent: false\n  },\n  /**\n   * Modifier used to make the popper flow toward the inner of the reference element.\n   * By default, when this modifier is disabled, the popper will be placed outside\n   * the reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  inner: {\n    /** @prop {number} order=700 - Index used to define the order of execution */\n    order: 700,\n    /** @prop {Boolean} enabled=false - Whether the modifier is enabled or not */\n    enabled: false,\n    /** @prop {ModifierFn} */\n    fn: inner\n  },\n  /**\n   * Modifier used to hide the popper when its reference element is outside of the\n   * popper boundaries. It will set a `x-out-of-boundaries` attribute which can\n   * be used to hide with a CSS selector the popper when its reference is\n   * out of boundaries.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   * @memberof modifiers\n   * @inner\n   */\n  hide: {\n    /** @prop {number} order=800 - Index used to define the order of execution */\n    order: 800,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: hide\n  },\n  /**\n   * Computes the style that will be applied to the popper element to gets\n   * properly positioned.\n   *\n   * Note that this modifier will not touch the DOM, it just prepares the styles\n   * so that `applyStyle` modifier can apply it. This separation is useful\n   * in case you need to replace `applyStyle` with a custom implementation.\n   *\n   * This modifier has `850` as `order` value to maintain backward compatibility\n   * with previous versions of Popper.js. Expect the modifiers ordering method\n   * to change in future major versions of the library.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  computeStyle: {\n    /** @prop {number} order=850 - Index used to define the order of execution */\n    order: 850,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: computeStyle,\n    /**\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3D transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties\n     */\n    gpuAcceleration: true,\n    /**\n     * @prop {string} [x='bottom']\n     * Where to anchor the X axis (`bottom` or `top`). AKA X offset origin.\n     * Change this if your popper should grow in a direction different from `bottom`\n     */\n    x: 'bottom',\n    /**\n     * @prop {string} [x='left']\n     * Where to anchor the Y axis (`left` or `right`). AKA Y offset origin.\n     * Change this if your popper should grow in a direction different from `right`\n     */\n    y: 'right'\n  },\n  /**\n   * Applies the computed styles to the popper element.\n   *\n   * All the DOM manipulations are limited to this modifier. This is useful in case\n   * you want to integrate Popper.js inside a framework or view library and you\n   * want to delegate all the DOM manipulations to it.\n   *\n   * Note that if you disable this modifier, you must make sure the popper element\n   * has its position set to `absolute` before Popper.js can do its work!\n   *\n   * Just disable this modifier and define your own to achieve the desired effect.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  applyStyle: {\n    /** @prop {number} order=900 - Index used to define the order of execution */\n    order: 900,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: applyStyle,\n    /** @prop {Function} */\n    onLoad: applyStyleOnLoad,\n    /**\n     * @deprecated since version 1.10.0, the property moved to `computeStyle` modifier\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3D transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties\n     */\n    gpuAcceleration: undefined\n  }\n};\n\n/**\n * The `dataObject` is an object containing all the information used by Popper.js.\n * This object is passed to modifiers and to the `onCreate` and `onUpdate` callbacks.\n * @name dataObject\n * @property {Object} data.instance The Popper.js instance\n * @property {String} data.placement Placement applied to popper\n * @property {String} data.originalPlacement Placement originally defined on init\n * @property {Boolean} data.flipped True if popper has been flipped by flip modifier\n * @property {Boolean} data.hide True if the reference element is out of boundaries, useful to know when to hide the popper\n * @property {HTMLElement} data.arrowElement Node used as arrow by arrow modifier\n * @property {Object} data.styles Any CSS property defined here will be applied to the popper. It expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.arrowStyles Any CSS property defined here will be applied to the popper arrow. It expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.boundaries Offsets of the popper boundaries\n * @property {Object} data.offsets The measurements of popper, reference and arrow elements\n * @property {Object} data.offsets.popper `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.reference `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.arrow] `top` and `left` offsets, only one of them will be different from 0\n */\n\n/**\n * Default options provided to Popper.js constructor.<br />\n * These can be overridden using the `options` argument of Popper.js.<br />\n * To override an option, simply pass an object with the same\n * structure of the `options` object, as the 3rd argument. For example:\n * ```\n * new Popper(ref, pop, {\n *   modifiers: {\n *     preventOverflow: { enabled: false }\n *   }\n * })\n * ```\n * @type {Object}\n * @static\n * @memberof Popper\n */\nvar Defaults = {\n  /**\n   * Popper's placement.\n   * @prop {Popper.placements} placement='bottom'\n   */\n  placement: 'bottom',\n  /**\n   * Set this to true if you want popper to position it self in 'fixed' mode\n   * @prop {Boolean} positionFixed=false\n   */\n  positionFixed: false,\n  /**\n   * Whether events (resize, scroll) are initially enabled.\n   * @prop {Boolean} eventsEnabled=true\n   */\n  eventsEnabled: true,\n  /**\n   * Set to true if you want to automatically remove the popper when\n   * you call the `destroy` method.\n   * @prop {Boolean} removeOnDestroy=false\n   */\n  removeOnDestroy: false,\n  /**\n   * Callback called when the popper is created.<br />\n   * By default, it is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onCreate}\n   */\n  onCreate: function onCreate() {},\n  /**\n   * Callback called when the popper is updated. This callback is not called\n   * on the initialization/creation of the popper, but only on subsequent\n   * updates.<br />\n   * By default, it is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onUpdate}\n   */\n  onUpdate: function onUpdate() {},\n  /**\n   * List of modifiers used to modify the offsets before they are applied to the popper.\n   * They provide most of the functionalities of Popper.js.\n   * @prop {modifiers}\n   */\n  modifiers: modifiers\n};\n\n/**\n * @callback onCreate\n * @param {dataObject} data\n */\n\n/**\n * @callback onUpdate\n * @param {dataObject} data\n */\n\n// Utils\n// Methods\nvar Popper = function () {\n  /**\n   * Creates a new Popper.js instance.\n   * @class Popper\n   * @param {Element|referenceObject} reference - The reference element used to position the popper\n   * @param {Element} popper - The HTML / XML element used as the popper\n   * @param {Object} options - Your custom options to override the ones defined in [Defaults](#defaults)\n   * @return {Object} instance - The generated Popper.js instance\n   */\n  function Popper(reference, popper) {\n    var _this = this;\n    var options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    classCallCheck(this, Popper);\n    this.scheduleUpdate = function () {\n      return requestAnimationFrame(_this.update);\n    };\n\n    // make update() debounced, so that it only runs at most once-per-tick\n    this.update = debounce(this.update.bind(this));\n\n    // with {} we create a new object with the options inside it\n    this.options = _extends({}, Popper.Defaults, options);\n\n    // init state\n    this.state = {\n      isDestroyed: false,\n      isCreated: false,\n      scrollParents: []\n    };\n\n    // get reference and popper elements (allow jQuery wrappers)\n    this.reference = reference && reference.jquery ? reference[0] : reference;\n    this.popper = popper && popper.jquery ? popper[0] : popper;\n\n    // Deep merge modifiers options\n    this.options.modifiers = {};\n    Object.keys(_extends({}, Popper.Defaults.modifiers, options.modifiers)).forEach(function (name) {\n      _this.options.modifiers[name] = _extends({}, Popper.Defaults.modifiers[name] || {}, options.modifiers ? options.modifiers[name] : {});\n    });\n\n    // Refactoring modifiers' list (Object => Array)\n    this.modifiers = Object.keys(this.options.modifiers).map(function (name) {\n      return _extends({\n        name: name\n      }, _this.options.modifiers[name]);\n    })\n    // sort the modifiers by order\n    .sort(function (a, b) {\n      return a.order - b.order;\n    });\n\n    // modifiers have the ability to execute arbitrary code when Popper.js get inited\n    // such code is executed in the same order of its modifier\n    // they could add new properties to their options configuration\n    // BE AWARE: don't add options to `options.modifiers.name` but to `modifierOptions`!\n    this.modifiers.forEach(function (modifierOptions) {\n      if (modifierOptions.enabled && isFunction(modifierOptions.onLoad)) {\n        modifierOptions.onLoad(_this.reference, _this.popper, _this.options, modifierOptions, _this.state);\n      }\n    });\n\n    // fire the first update to position the popper in the right place\n    this.update();\n    var eventsEnabled = this.options.eventsEnabled;\n    if (eventsEnabled) {\n      // setup event listeners, they will take care of update the position in specific situations\n      this.enableEventListeners();\n    }\n    this.state.eventsEnabled = eventsEnabled;\n  }\n\n  // We can't use class properties because they don't get listed in the\n  // class prototype and break stuff like Sinon stubs\n\n  createClass(Popper, [{\n    key: 'update',\n    value: function update$$1() {\n      return update.call(this);\n    }\n  }, {\n    key: 'destroy',\n    value: function destroy$$1() {\n      return destroy.call(this);\n    }\n  }, {\n    key: 'enableEventListeners',\n    value: function enableEventListeners$$1() {\n      return enableEventListeners.call(this);\n    }\n  }, {\n    key: 'disableEventListeners',\n    value: function disableEventListeners$$1() {\n      return disableEventListeners.call(this);\n    }\n\n    /**\n     * Schedules an update. It will run on the next UI update available.\n     * @method scheduleUpdate\n     * @memberof Popper\n     */\n\n    /**\n     * Collection of utilities useful when writing custom modifiers.\n     * Starting from version 1.7, this method is available only if you\n     * include `popper-utils.js` before `popper.js`.\n     *\n     * **DEPRECATION**: This way to access PopperUtils is deprecated\n     * and will be removed in v2! Use the PopperUtils module directly instead.\n     * Due to the high instability of the methods contained in Utils, we can't\n     * guarantee them to follow semver. Use them at your own risk!\n     * @static\n     * @private\n     * @type {Object}\n     * @deprecated since version 1.8\n     * @member Utils\n     * @memberof Popper\n     */\n  }]);\n  return Popper;\n}();\n\n/**\n * The `referenceObject` is an object that provides an interface compatible with Popper.js\n * and lets you use it as replacement of a real DOM node.<br />\n * You can use this method to position a popper relatively to a set of coordinates\n * in case you don't have a DOM node to use as reference.\n *\n * ```\n * new Popper(referenceObject, popperNode);\n * ```\n *\n * NB: This feature isn't supported in Internet Explorer 10.\n * @name referenceObject\n * @property {Function} data.getBoundingClientRect\n * A function that returns a set of coordinates compatible with the native `getBoundingClientRect` method.\n * @property {number} data.clientWidth\n * An ES6 getter that will return the width of the virtual reference element.\n * @property {number} data.clientHeight\n * An ES6 getter that will return the height of the virtual reference element.\n */\n\nPopper.Utils = (typeof window !== 'undefined' ? window : global).PopperUtils;\nPopper.placements = placements;\nPopper.Defaults = Defaults;\nexport default Popper;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "window", "document", "navigator", "timeoutDuration", "longerTimeoutBrowsers", "i", "length", "userAgent", "indexOf", "microtaskDebounce", "fn", "called", "Promise", "resolve", "then", "taskDebounce", "scheduled", "supportsMicroTasks", "debounce", "isFunction", "functionToCheck", "getType", "toString", "call", "getStyleComputedProperty", "element", "property", "nodeType", "ownerDocument", "defaultView", "css", "getComputedStyle", "getParentNode", "nodeName", "parentNode", "host", "getScrollParent", "body", "overflow", "_getStyleComputedProp", "overflowX", "overflowY", "test", "getReferenceNode", "reference", "referenceNode", "isIE11", "MSInputMethodContext", "documentMode", "isIE10", "isIE", "version", "getOffsetParent", "documentElement", "noOffsetParent", "offsetParent", "nextElement<PERSON><PERSON>ling", "isOffsetContainer", "first<PERSON><PERSON><PERSON><PERSON><PERSON>", "getRoot", "node", "findCommonOffsetParent", "element1", "element2", "order", "compareDocumentPosition", "Node", "DOCUMENT_POSITION_FOLLOWING", "start", "end", "range", "createRange", "setStart", "setEnd", "commonAncestorContainer", "contains", "element1root", "getScroll", "side", "arguments", "undefined", "upperSide", "html", "scrollingElement", "includeScroll", "rect", "subtract", "scrollTop", "scrollLeft", "modifier", "top", "bottom", "left", "right", "getBordersSize", "styles", "axis", "sideA", "sideB", "parseFloat", "getSize", "computedStyle", "Math", "max", "parseInt", "getWindowSizes", "getClientRect", "offsets", "width", "height", "getBoundingClientRect", "e", "result", "sizes", "clientWidth", "clientHeight", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "offsetWidth", "vertScrollbar", "offsetHeight", "getOffsetRectRelativeToArbitraryNode", "children", "parent", "fixedPosition", "isHTML", "childrenRect", "parentRect", "scrollParent", "borderTopWidth", "borderLeftWidth", "marginTop", "marginLeft", "getViewportOffsetRectRelativeToArtbitraryNode", "excludeScroll", "relativeOffset", "innerWidth", "innerHeight", "offset", "isFixed", "getFixedPositionOffsetParent", "parentElement", "el", "getBoundaries", "popper", "padding", "boundariesElement", "boundaries", "boundariesNode", "_getWindowSizes", "isPaddingNumber", "getArea", "_ref", "computeAutoPlacement", "placement", "refRect", "rects", "sorted<PERSON>reas", "Object", "keys", "map", "key", "sort", "a", "b", "area", "filtered<PERSON><PERSON>s", "filter", "_ref2", "computedPlacement", "variation", "split", "getReferenceOffsets", "state", "commonOffsetParent", "getOuterSizes", "x", "marginBottom", "y", "marginRight", "getOppositePlacement", "hash", "replace", "matched", "getPopperOffsets", "referenceOffsets", "popperRect", "popperOffsets", "isHoriz", "mainSide", "secondarySide", "measurement", "secondaryMeasurement", "find", "arr", "check", "Array", "prototype", "findIndex", "prop", "value", "cur", "match", "obj", "runModifiers", "modifiers", "data", "ends", "modifiersToRun", "slice", "for<PERSON>ach", "warn", "enabled", "update", "isDestroyed", "options", "positionFixed", "flip", "originalPlacement", "position", "isCreated", "onCreate", "onUpdate", "isModifierEnabled", "modifierName", "some", "name", "getSupportedPropertyName", "prefixes", "upperProp", "char<PERSON>t", "toUpperCase", "prefix", "to<PERSON><PERSON><PERSON>", "style", "destroy", "removeAttribute", "<PERSON><PERSON><PERSON><PERSON>", "disableEventListeners", "removeOnDestroy", "<PERSON><PERSON><PERSON><PERSON>", "getWindow", "attachToScrollParents", "event", "callback", "scrollParents", "isBody", "target", "addEventListener", "passive", "push", "setupEventListeners", "updateBound", "scrollElement", "eventsEnabled", "enableEventListeners", "scheduleUpdate", "removeEventListeners", "removeEventListener", "isNumeric", "n", "isNaN", "isFinite", "setStyles", "unit", "setAttributes", "attributes", "setAttribute", "applyStyle", "instance", "arrowElement", "arrowStyles", "applyStyleOnLoad", "modifierOptions", "getRoundedOffsets", "shouldRound", "_data$offsets", "round", "floor", "noRound", "v", "referenceWidth", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "isVertical", "isVariation", "sameWidthParity", "bothOddWidth", "horizontalToInteger", "verticalToInteger", "isFirefox", "computeStyle", "legacyGpuAccelerationOption", "gpuAcceleration", "offsetParentRect", "devicePixelRatio", "prefixedProperty", "invertTop", "invertLeft", "_extends", "arrow", "isModifierRequired", "requestingName", "requested<PERSON><PERSON>", "requesting", "isRequired", "_requesting", "requested", "querySelector", "len", "sideCapitalized", "toLowerCase", "altSide", "opSide", "arrowElementSize", "center", "popperMarginSide", "popperBorderSide", "sideValue", "min", "_data$offsets$arrow", "defineProperty", "getOppositeVariation", "placements", "validPlacements", "clockwise", "counter", "index", "concat", "reverse", "BEHAVIORS", "flipped", "placementOpposite", "flipOrder", "behavior", "FLIP", "CLOCKWISE", "COUNTERCLOCKWISE", "step", "refOffsets", "overlapsRef", "overflowsLeft", "overflowsRight", "overflowsTop", "overflowsBottom", "overflowsBoundaries", "flippedVariationByRef", "flipVariations", "flippedVariationByContent", "flipVariationsByContent", "flippedVariation", "keepTogether", "toValue", "str", "size", "parseOffset", "basePlacement", "useHeight", "fragments", "frag", "trim", "divider", "search", "splitRegex", "ops", "op", "mergeWithPrevious", "reduce", "index2", "preventOverflow", "transformProp", "popperStyles", "transform", "priority", "primary", "escapeWithReference", "secondary", "shift", "shiftvariation", "shiftOffsets", "hide", "bound", "inner", "subtractLength", "De<PERSON>ults", "<PERSON><PERSON>", "requestAnimationFrame", "_this", "bind", "j<PERSON>y", "onLoad", "Utils", "global", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\isBrowser.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\debounce.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\isFunction.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\getStyleComputedProperty.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\getParentNode.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\getScrollParent.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\getReferenceNode.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\isIE.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\getOffsetParent.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\isOffsetContainer.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\getRoot.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\findCommonOffsetParent.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\getScroll.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\includeScroll.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\getBordersSize.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\getWindowSizes.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\getClientRect.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\getBoundingClientRect.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\getOffsetRectRelativeToArbitraryNode.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\getViewportOffsetRectRelativeToArtbitraryNode.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\isFixed.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\getFixedPositionOffsetParent.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\getBoundaries.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\computeAutoPlacement.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\getReferenceOffsets.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\getOuterSizes.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\getOppositePlacement.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\getPopperOffsets.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\find.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\findIndex.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\runModifiers.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\methods\\update.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\isModifierEnabled.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\getSupportedPropertyName.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\methods\\destroy.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\getWindow.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\setupEventListeners.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\methods\\enableEventListeners.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\removeEventListeners.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\methods\\disableEventListeners.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\isNumeric.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\setStyles.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\setAttributes.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\modifiers\\applyStyle.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\getRoundedOffsets.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\modifiers\\computeStyle.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\isModifierRequired.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\modifiers\\arrow.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\getOppositeVariation.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\methods\\placements.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\utils\\clockwise.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\modifiers\\flip.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\modifiers\\keepTogether.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\modifiers\\offset.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\modifiers\\preventOverflow.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\modifiers\\shift.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\modifiers\\hide.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\modifiers\\inner.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\modifiers\\index.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\methods\\defaults.js", "C:\\ignition\\ignition-ui\\node_modules\\react-popper\\node_modules\\popper.js\\src\\index.js"], "sourcesContent": ["export default typeof window !== 'undefined' && typeof document !== 'undefined' && typeof navigator !== 'undefined';\n", "import isBrowser from './isBrowser';\n\nconst timeoutDuration = (function(){\n  const longerTimeoutBrowsers = ['Edge', 'Trident', 'Firefox'];\n  for (let i = 0; i < longerTimeoutBrowsers.length; i += 1) {\n    if (isBrowser && navigator.userAgent.indexOf(longerTimeoutBrowsers[i]) >= 0) {\n      return 1;\n    }\n  }\n  return 0;\n}());\n\nexport function microtaskDebounce(fn) {\n  let called = false\n  return () => {\n    if (called) {\n      return\n    }\n    called = true\n    window.Promise.resolve().then(() => {\n      called = false\n      fn()\n    })\n  }\n}\n\nexport function taskDebounce(fn) {\n  let scheduled = false;\n  return () => {\n    if (!scheduled) {\n      scheduled = true;\n      setTimeout(() => {\n        scheduled = false;\n        fn();\n      }, timeoutDuration);\n    }\n  };\n}\n\nconst supportsMicroTasks = isBrowser && window.Promise\n\n\n/**\n* Create a debounced version of a method, that's asynchronously deferred\n* but called in the minimum time possible.\n*\n* @method\n* @memberof Popper.Utils\n* @argument {Function} fn\n* @returns {Function}\n*/\nexport default (supportsMicroTasks\n  ? microtaskDebounce\n  : taskDebounce);\n", "/**\n * Check if the given variable is a function\n * @method\n * @memberof Popper.Utils\n * @argument {Any} functionToCheck - variable to check\n * @returns {Boolean} answer to: is a function?\n */\nexport default function isFunction(functionToCheck) {\n  const getType = {};\n  return (\n    functionToCheck &&\n    getType.toString.call(functionToCheck) === '[object Function]'\n  );\n}\n", "/**\n * Get CSS computed property of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Eement} element\n * @argument {String} property\n */\nexport default function getStyleComputedProperty(element, property) {\n  if (element.nodeType !== 1) {\n    return [];\n  }\n  // NOTE: 1 DOM access here\n  const window = element.ownerDocument.defaultView;\n  const css = window.getComputedStyle(element, null);\n  return property ? css[property] : css;\n}\n", "/**\n * Returns the parentNode or the host of the element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} parent\n */\nexport default function getParentNode(element) {\n  if (element.nodeName === 'HTML') {\n    return element;\n  }\n  return element.parentNode || element.host;\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getParentNode from './getParentNode';\n\n/**\n * Returns the scrolling parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} scroll parent\n */\nexport default function getScrollParent(element) {\n  // Return body, `getScroll` will take care to get the correct `scrollTop` from it\n  if (!element) {\n    return document.body\n  }\n\n  switch (element.nodeName) {\n    case 'HTML':\n    case 'BODY':\n      return element.ownerDocument.body\n    case '#document':\n      return element.body\n  }\n\n  // Firefox want us to check `-x` and `-y` variations as well\n  const { overflow, overflowX, overflowY } = getStyleComputedProperty(element);\n  if (/(auto|scroll|overlay)/.test(overflow + overflowY + overflowX)) {\n    return element;\n  }\n\n  return getScrollParent(getParentNode(element));\n}\n", "/**\n * Returns the reference node of the reference object, or the reference object itself.\n * @method\n * @memberof Popper.Utils\n * @param {Element|Object} reference - the reference element (the popper will be relative to this)\n * @returns {Element} parent\n */\nexport default function getReferenceNode(reference) {\n  return reference && reference.referenceNode ? reference.referenceNode : reference;\n}\n", "import isBrowser from './isBrowser';\n\nconst isIE11 = isBrowser && !!(window.MSInputMethodContext && document.documentMode);\nconst isIE10 = isBrowser && /MSIE 10/.test(navigator.userAgent);\n\n/**\n * Determines if the browser is Internet Explorer\n * @method\n * @memberof Popper.Utils\n * @param {Number} version to check\n * @returns {Boolean} isIE\n */\nexport default function isIE(version) {\n  if (version === 11) {\n    return isIE11;\n  }\n  if (version === 10) {\n    return isIE10;\n  }\n  return isIE11 || isIE10;\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport isIE from './isIE';\n/**\n * Returns the offset parent of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} offset parent\n */\nexport default function getOffsetParent(element) {\n  if (!element) {\n    return document.documentElement;\n  }\n\n  const noOffsetParent = isIE(10) ? document.body : null;\n\n  // NOTE: 1 DOM access here\n  let offsetParent = element.offsetParent || null;\n  // Skip hidden elements which don't have an offsetParent\n  while (offsetParent === noOffsetParent && element.nextElementSibling) {\n    offsetParent = (element = element.nextElementSibling).offsetParent;\n  }\n\n  const nodeName = offsetParent && offsetParent.nodeName;\n\n  if (!nodeName || nodeName === 'BODY' || nodeName === 'HTML') {\n    return element ? element.ownerDocument.documentElement : document.documentElement;\n  }\n\n  // .offsetParent will return the closest TH, TD or TABLE in case\n  // no offsetParent is present, I hate this job...\n  if (\n    ['TH', 'TD', 'TABLE'].indexOf(offsetParent.nodeName) !== -1 &&\n    getStyleComputedProperty(offsetParent, 'position') === 'static'\n  ) {\n    return getOffsetParent(offsetParent);\n  }\n\n  return offsetParent;\n}\n", "import getOffsetParent from './getOffsetParent';\n\nexport default function isOffsetContainer(element) {\n  const { nodeName } = element;\n  if (nodeName === 'BODY') {\n    return false;\n  }\n  return (\n    nodeName === 'HTML' || getOffsetParent(element.firstElementChild) === element\n  );\n}\n", "/**\n * Finds the root node (document, shadowDOM root) of the given element\n * @method\n * @memberof Popper.Utils\n * @argument {Element} node\n * @returns {Element} root node\n */\nexport default function getRoot(node) {\n  if (node.parentNode !== null) {\n    return getRoot(node.parentNode);\n  }\n\n  return node;\n}\n", "import isOffsetContainer from './isOffsetContainer';\nimport getRoot from './getRoot';\nimport getOffsetParent from './getOffsetParent';\n\n/**\n * Finds the offset parent common to the two provided nodes\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element1\n * @argument {Element} element2\n * @returns {Element} common offset parent\n */\nexport default function findCommonOffsetParent(element1, element2) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n  if (!element1 || !element1.nodeType || !element2 || !element2.nodeType) {\n    return document.documentElement;\n  }\n\n  // Here we make sure to give as \"start\" the element that comes first in the DOM\n  const order =\n    element1.compareDocumentPosition(element2) &\n    Node.DOCUMENT_POSITION_FOLLOWING;\n  const start = order ? element1 : element2;\n  const end = order ? element2 : element1;\n\n  // Get common ancestor container\n  const range = document.createRange();\n  range.setStart(start, 0);\n  range.setEnd(end, 0);\n  const { commonAncestorContainer } = range;\n\n  // Both nodes are inside #document\n  if (\n    (element1 !== commonAncestorContainer &&\n      element2 !== commonAncestorContainer) ||\n    start.contains(end)\n  ) {\n    if (isOffsetContainer(commonAncestorContainer)) {\n      return commonAncestorContainer;\n    }\n\n    return getOffsetParent(commonAncestorContainer);\n  }\n\n  // one of the nodes is inside shadowDOM, find which one\n  const element1root = getRoot(element1);\n  if (element1root.host) {\n    return findCommonOffsetParent(element1root.host, element2);\n  } else {\n    return findCommonOffsetParent(element1, getRoot(element2).host);\n  }\n}\n", "/**\n * Gets the scroll value of the given element in the given side (top and left)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {String} side `top` or `left`\n * @returns {number} amount of scrolled pixels\n */\nexport default function getScroll(element, side = 'top') {\n  const upperSide = side === 'top' ? 'scrollTop' : 'scrollLeft';\n  const nodeName = element.nodeName;\n\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    const html = element.ownerDocument.documentElement;\n    const scrollingElement = element.ownerDocument.scrollingElement || html;\n    return scrollingElement[upperSide];\n  }\n\n  return element[upperSide];\n}\n", "import getScroll from './getScroll';\n\n/*\n * Sum or subtract the element scroll values (left and top) from a given rect object\n * @method\n * @memberof Popper.Utils\n * @param {Object} rect - Rect object you want to change\n * @param {HTMLElement} element - The element from the function reads the scroll values\n * @param {Boolean} subtract - set to true if you want to subtract the scroll values\n * @return {Object} rect - The modifier rect object\n */\nexport default function includeScroll(rect, element, subtract = false) {\n  const scrollTop = getScroll(element, 'top');\n  const scrollLeft = getScroll(element, 'left');\n  const modifier = subtract ? -1 : 1;\n  rect.top += scrollTop * modifier;\n  rect.bottom += scrollTop * modifier;\n  rect.left += scrollLeft * modifier;\n  rect.right += scrollLeft * modifier;\n  return rect;\n}\n", "/*\n * Helper to detect borders of a given element\n * @method\n * @memberof Popper.Utils\n * @param {CSSStyleDeclaration} styles\n * Result of `getStyleComputedProperty` on the given element\n * @param {String} axis - `x` or `y`\n * @return {number} borders - The borders size of the given axis\n */\n\nexport default function getBordersSize(styles, axis) {\n  const sideA = axis === 'x' ? 'Left' : 'Top';\n  const sideB = sideA === 'Left' ? 'Right' : 'Bottom';\n\n  return (\n    parseFloat(styles[`border${sideA}Width`]) +\n    parseFloat(styles[`border${sideB}Width`])\n  );\n}\n", "import isIE from './isIE';\n\nfunction getSize(axis, body, html, computedStyle) {\n  return Math.max(\n    body[`offset${axis}`],\n    body[`scroll${axis}`],\n    html[`client${axis}`],\n    html[`offset${axis}`],\n    html[`scroll${axis}`],\n    isIE(10)\n      ? (parseInt(html[`offset${axis}`]) + \n      parseInt(computedStyle[`margin${axis === 'Height' ? 'Top' : 'Left'}`]) + \n      parseInt(computedStyle[`margin${axis === 'Height' ? 'Bottom' : 'Right'}`]))\n    : 0 \n  );\n}\n\nexport default function getWindowSizes(document) {\n  const body = document.body;\n  const html = document.documentElement;\n  const computedStyle = isIE(10) && getComputedStyle(html);\n\n  return {\n    height: getSize('Height', body, html, computedStyle),\n    width: getSize('Width', body, html, computedStyle),\n  };\n}\n", "/**\n * Given element offsets, generate an output similar to getBoundingClientRect\n * @method\n * @memberof Popper.Utils\n * @argument {Object} offsets\n * @returns {Object} ClientRect like output\n */\nexport default function getClientRect(offsets) {\n  return {\n    ...offsets,\n    right: offsets.left + offsets.width,\n    bottom: offsets.top + offsets.height,\n  };\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getBordersSize from './getBordersSize';\nimport getWindowSizes from './getWindowSizes';\nimport getScroll from './getScroll';\nimport getClientRect from './getClientRect';\nimport isIE from './isIE';\n\n/**\n * Get bounding client rect of given element\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} element\n * @return {Object} client rect\n */\nexport default function getBoundingClientRect(element) {\n  let rect = {};\n\n  // IE10 10 FIX: Please, don't ask, the element isn't\n  // considered in DOM in some circumstances...\n  // This isn't reproducible in IE10 compatibility mode of IE11\n  try {\n    if (isIE(10)) {\n      rect = element.getBoundingClientRect();\n      const scrollTop = getScroll(element, 'top');\n      const scrollLeft = getScroll(element, 'left');\n      rect.top += scrollTop;\n      rect.left += scrollLeft;\n      rect.bottom += scrollTop;\n      rect.right += scrollLeft;\n    }\n    else {\n      rect = element.getBoundingClientRect();\n    }\n  }\n  catch(e){}\n\n  const result = {\n    left: rect.left,\n    top: rect.top,\n    width: rect.right - rect.left,\n    height: rect.bottom - rect.top,\n  };\n\n  // subtract scrollbar size from sizes\n  const sizes = element.nodeName === 'HTML' ? getWindowSizes(element.ownerDocument) : {};\n  const width =\n    sizes.width || element.clientWidth || result.width;\n  const height =\n    sizes.height || element.clientHeight || result.height;\n\n  let horizScrollbar = element.offsetWidth - width;\n  let vertScrollbar = element.offsetHeight - height;\n\n  // if an hypothetical scrollbar is detected, we must be sure it's not a `border`\n  // we make this check conditional for performance reasons\n  if (horizScrollbar || vertScrollbar) {\n    const styles = getStyleComputedProperty(element);\n    horizScrollbar -= getBordersSize(styles, 'x');\n    vertScrollbar -= getBordersSize(styles, 'y');\n\n    result.width -= horizScrollbar;\n    result.height -= vertScrollbar;\n  }\n\n  return getClientRect(result);\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport includeScroll from './includeScroll';\nimport getScrollParent from './getScrollParent';\nimport getBoundingClientRect from './getBoundingClientRect';\nimport runIsIE from './isIE';\nimport getClientRect from './getClientRect';\n\nexport default function getOffsetRectRelativeToArbitraryNode(children, parent, fixedPosition = false) {\n  const isIE10 = runIsIE(10);\n  const isHTML = parent.nodeName === 'HTML';\n  const childrenRect = getBoundingClientRect(children);\n  const parentRect = getBoundingClientRect(parent);\n  const scrollParent = getScrollParent(children);\n\n  const styles = getStyleComputedProperty(parent);\n  const borderTopWidth = parseFloat(styles.borderTopWidth);\n  const borderLeftWidth = parseFloat(styles.borderLeftWidth);\n\n  // In cases where the parent is fixed, we must ignore negative scroll in offset calc\n  if(fixedPosition && isHTML) {\n    parentRect.top = Math.max(parentRect.top, 0);\n    parentRect.left = Math.max(parentRect.left, 0);\n  }\n  let offsets = getClientRect({\n    top: childrenRect.top - parentRect.top - borderTopWidth,\n    left: childrenRect.left - parentRect.left - borderLeftWidth,\n    width: childrenRect.width,\n    height: childrenRect.height,\n  });\n  offsets.marginTop = 0;\n  offsets.marginLeft = 0;\n\n  // Subtract margins of documentElement in case it's being used as parent\n  // we do this only on HTML because it's the only element that behaves\n  // differently when margins are applied to it. The margins are included in\n  // the box of the documentElement, in the other cases not.\n  if (!isIE10 && isHTML) {\n    const marginTop = parseFloat(styles.marginTop);\n    const marginLeft = parseFloat(styles.marginLeft);\n\n    offsets.top -= borderTopWidth - marginTop;\n    offsets.bottom -= borderTopWidth - marginTop;\n    offsets.left -= borderLeftWidth - marginLeft;\n    offsets.right -= borderLeftWidth - marginLeft;\n\n    // Attach marginTop and marginLeft because in some circumstances we may need them\n    offsets.marginTop = marginTop;\n    offsets.marginLeft = marginLeft;\n  }\n\n  if (\n    isIE10 && !fixedPosition\n      ? parent.contains(scrollParent)\n      : parent === scrollParent && scrollParent.nodeName !== 'BODY'\n  ) {\n    offsets = includeScroll(offsets, parent);\n  }\n\n  return offsets;\n}\n", "import getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getScroll from './getScroll';\nimport getClientRect from './getClientRect';\n\nexport default function getViewportOffsetRectRelativeToArtbitraryNode(element, excludeScroll = false) {\n  const html = element.ownerDocument.documentElement;\n  const relativeOffset = getOffsetRectRelativeToArbitraryNode(element, html);\n  const width = Math.max(html.clientWidth, window.innerWidth || 0);\n  const height = Math.max(html.clientHeight, window.innerHeight || 0);\n\n  const scrollTop = !excludeScroll ? getScroll(html) : 0;\n  const scrollLeft = !excludeScroll ? getScroll(html, 'left') : 0;\n\n  const offset = {\n    top: scrollTop - relativeOffset.top + relativeOffset.marginTop,\n    left: scrollLeft - relativeOffset.left + relativeOffset.marginLeft,\n    width,\n    height,\n  };\n\n  return getClientRect(offset);\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport getParentNode from './getParentNode';\n\n/**\n * Check if the given element is fixed or is inside a fixed parent\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @argument {Element} customContainer\n * @returns {<PERSON><PERSON><PERSON>} answer to \"isFixed?\"\n */\nexport default function isFixed(element) {\n  const nodeName = element.nodeName;\n  if (nodeName === 'BODY' || nodeName === 'HTML') {\n    return false;\n  }\n  if (getStyleComputedProperty(element, 'position') === 'fixed') {\n    return true;\n  }\n  const parentNode = getParentNode(element);\n  if (!parentNode) {\n    return false;\n  }\n  return isFixed(parentNode);\n}\n", "import getStyleComputedProperty from './getStyleComputedProperty';\nimport isIE from './isIE';\n/**\n * Finds the first parent of an element that has a transformed property defined\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Element} first transformed parent or documentElement\n */\n\nexport default function getFixedPositionOffsetParent(element) {\n  // This check is needed to avoid errors in case one of the elements isn't defined for any reason\n   if (!element || !element.parentElement || isIE()) {\n    return document.documentElement;\n  }\n  let el = element.parentElement;\n  while (el && getStyleComputedProperty(el, 'transform') === 'none') {\n    el = el.parentElement;\n  }\n  return el || document.documentElement;\n\n}\n", "import getScrollParent from './getScrollParent';\nimport getParentNode from './getParentNode';\nimport getReferenceNode from './getReferenceNode';\nimport findCommonOffsetParent from './findCommonOffsetParent';\nimport getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getViewportOffsetRectRelativeToArtbitraryNode from './getViewportOffsetRectRelativeToArtbitraryNode';\nimport getWindowSizes from './getWindowSizes';\nimport isFixed from './isFixed';\nimport getFixedPositionOffsetParent from './getFixedPositionOffsetParent';\n\n/**\n * Computed the boundaries limits and return them\n * @method\n * @memberof Popper.Utils\n * @param {HTMLElement} popper\n * @param {HTMLElement} reference\n * @param {number} padding\n * @param {HTMLElement} boundariesElement - Element used to define the boundaries\n * @param {Boolean} fixedPosition - Is in fixed position mode\n * @returns {Object} Coordinates of the boundaries\n */\nexport default function getBoundaries(\n  popper,\n  reference,\n  padding,\n  boundariesElement,\n  fixedPosition = false\n) {\n  // NOTE: 1 DOM access here\n\n  let boundaries = { top: 0, left: 0 };\n  const offsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, getReferenceNode(reference));\n\n  // Handle viewport case\n  if (boundariesElement === 'viewport' ) {\n    boundaries = getViewportOffsetRectRelativeToArtbitraryNode(offsetParent, fixedPosition);\n  }\n\n  else {\n    // Handle other cases based on DOM element used as boundaries\n    let boundariesNode;\n    if (boundariesElement === 'scrollParent') {\n      boundariesNode = getScrollParent(getParentNode(reference));\n      if (boundariesNode.nodeName === 'BODY') {\n        boundariesNode = popper.ownerDocument.documentElement;\n      }\n    } else if (boundariesElement === 'window') {\n      boundariesNode = popper.ownerDocument.documentElement;\n    } else {\n      boundariesNode = boundariesElement;\n    }\n\n    const offsets = getOffsetRectRelativeToArbitraryNode(\n      boundariesNode,\n      offsetParent,\n      fixedPosition\n    );\n\n    // In case of HTML, we need a different computation\n    if (boundariesNode.nodeName === 'HTML' && !isFixed(offsetParent)) {\n      const { height, width } = getWindowSizes(popper.ownerDocument);\n      boundaries.top += offsets.top - offsets.marginTop;\n      boundaries.bottom = height + offsets.top;\n      boundaries.left += offsets.left - offsets.marginLeft;\n      boundaries.right = width + offsets.left;\n    } else {\n      // for all the other DOM elements, this one is good\n      boundaries = offsets;\n    }\n  }\n\n  // Add paddings\n  padding = padding || 0;\n  const isPaddingNumber = typeof padding === 'number';\n  boundaries.left += isPaddingNumber ? padding : padding.left || 0; \n  boundaries.top += isPaddingNumber ? padding : padding.top || 0; \n  boundaries.right -= isPaddingNumber ? padding : padding.right || 0; \n  boundaries.bottom -= isPaddingNumber ? padding : padding.bottom || 0; \n\n  return boundaries;\n}\n", "import getBoundaries from '../utils/getBoundaries';\n\nfunction getArea({ width, height }) {\n  return width * height;\n}\n\n/**\n * Utility used to transform the `auto` placement to the placement with more\n * available space.\n * @method\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function computeAutoPlacement(\n  placement,\n  refRect,\n  popper,\n  reference,\n  boundariesElement,\n  padding = 0\n) {\n  if (placement.indexOf('auto') === -1) {\n    return placement;\n  }\n\n  const boundaries = getBoundaries(\n    popper,\n    reference,\n    padding,\n    boundariesElement\n  );\n\n  const rects = {\n    top: {\n      width: boundaries.width,\n      height: refRect.top - boundaries.top,\n    },\n    right: {\n      width: boundaries.right - refRect.right,\n      height: boundaries.height,\n    },\n    bottom: {\n      width: boundaries.width,\n      height: boundaries.bottom - refRect.bottom,\n    },\n    left: {\n      width: refRect.left - boundaries.left,\n      height: boundaries.height,\n    },\n  };\n\n  const sortedAreas = Object.keys(rects)\n    .map(key => ({\n      key,\n      ...rects[key],\n      area: getArea(rects[key]),\n    }))\n    .sort((a, b) => b.area - a.area);\n\n  const filteredAreas = sortedAreas.filter(\n    ({ width, height }) =>\n      width >= popper.clientWidth && height >= popper.clientHeight\n  );\n\n  const computedPlacement = filteredAreas.length > 0\n    ? filteredAreas[0].key\n    : sortedAreas[0].key;\n\n  const variation = placement.split('-')[1];\n\n  return computedPlacement + (variation ? `-${variation}` : '');\n}\n", "import findCommonOffsetParent from './findCommonOffsetParent';\nimport getOffsetRectRelativeToArbitraryNode from './getOffsetRectRelativeToArbitraryNode';\nimport getFixedPositionOffsetParent from './getFixedPositionOffsetParent';\nimport getReferenceNode from './getReferenceNode';\n\n/**\n * Get offsets to the reference element\n * @method\n * @memberof Popper.Utils\n * @param {Object} state\n * @param {Element} popper - the popper element\n * @param {Element} reference - the reference element (the popper will be relative to this)\n * @param {Element} fixedPosition - is in fixed position mode\n * @returns {Object} An object containing the offsets which will be applied to the popper\n */\nexport default function getReferenceOffsets(state, popper, reference, fixedPosition = null) {\n  const commonOffsetParent = fixedPosition ? getFixedPositionOffsetParent(popper) : findCommonOffsetParent(popper, getReferenceNode(reference));\n  return getOffsetRectRelativeToArbitraryNode(reference, commonOffsetParent, fixedPosition);\n}\n", "/**\n * Get the outer sizes of the given element (offset size + margins)\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element\n * @returns {Object} object containing width and height properties\n */\nexport default function getOuterSizes(element) {\n  const window = element.ownerDocument.defaultView;\n  const styles = window.getComputedStyle(element);\n  const x = parseFloat(styles.marginTop || 0) + parseFloat(styles.marginBottom || 0);\n  const y = parseFloat(styles.marginLeft || 0) + parseFloat(styles.marginRight || 0);\n  const result = {\n    width: element.offsetWidth + y,\n    height: element.offsetHeight + x,\n  };\n  return result;\n}\n", "/**\n * Get the opposite placement of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement\n * @returns {String} flipped placement\n */\nexport default function getOppositePlacement(placement) {\n  const hash = { left: 'right', right: 'left', bottom: 'top', top: 'bottom' };\n  return placement.replace(/left|right|bottom|top/g, matched => hash[matched]);\n}\n", "import getOuterSizes from './getOuterSizes';\nimport getOppositePlacement from './getOppositePlacement';\n\n/**\n * Get offsets to the popper\n * @method\n * @memberof Popper.Utils\n * @param {Object} position - CSS position the Popper will get applied\n * @param {HTMLElement} popper - the popper element\n * @param {Object} referenceOffsets - the reference offsets (the popper will be relative to this)\n * @param {String} placement - one of the valid placement options\n * @returns {Object} popperOffsets - An object containing the offsets which will be applied to the popper\n */\nexport default function getPopperOffsets(popper, referenceOffsets, placement) {\n  placement = placement.split('-')[0];\n\n  // Get popper node sizes\n  const popperRect = getOuterSizes(popper);\n\n  // Add position, width and height to our offsets object\n  const popperOffsets = {\n    width: popperRect.width,\n    height: popperRect.height,\n  };\n\n  // depending by the popper placement we have to compute its offsets slightly differently\n  const isHoriz = ['right', 'left'].indexOf(placement) !== -1;\n  const mainSide = isHoriz ? 'top' : 'left';\n  const secondarySide = isHoriz ? 'left' : 'top';\n  const measurement = isHoriz ? 'height' : 'width';\n  const secondaryMeasurement = !isHoriz ? 'height' : 'width';\n\n  popperOffsets[mainSide] =\n    referenceOffsets[mainSide] +\n    referenceOffsets[measurement] / 2 -\n    popperRect[measurement] / 2;\n  if (placement === secondarySide) {\n    popperOffsets[secondarySide] =\n      referenceOffsets[secondarySide] - popperRect[secondaryMeasurement];\n  } else {\n    popperOffsets[secondarySide] =\n      referenceOffsets[getOppositePlacement(secondarySide)];\n  }\n\n  return popperOffsets;\n}\n", "/**\n * Mimics the `find` method of Array\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nexport default function find(arr, check) {\n  // use native find if supported\n  if (Array.prototype.find) {\n    return arr.find(check);\n  }\n\n  // use `filter` to obtain the same behavior of `find`\n  return arr.filter(check)[0];\n}\n", "import find from './find';\n\n/**\n * Return the index of the matching object\n * @method\n * @memberof Popper.Utils\n * @argument {Array} arr\n * @argument prop\n * @argument value\n * @returns index or -1\n */\nexport default function findIndex(arr, prop, value) {\n  // use native findIndex if supported\n  if (Array.prototype.findIndex) {\n    return arr.findIndex(cur => cur[prop] === value);\n  }\n\n  // use `find` + `indexOf` if `findIndex` isn't supported\n  const match = find(arr, obj => obj[prop] === value);\n  return arr.indexOf(match);\n}\n", "import isFunction from './isFunction';\nimport findIndex from './findIndex';\nimport getClientRect from '../utils/getClientRect';\n\n/**\n * Loop trough the list of modifiers and run them in order,\n * each of them will then edit the data object.\n * @method\n * @memberof Popper.Utils\n * @param {dataObject} data\n * @param {Array} modifiers\n * @param {String} ends - Optional modifier name used as stopper\n * @returns {dataObject}\n */\nexport default function runModifiers(modifiers, data, ends) {\n  const modifiersToRun = ends === undefined\n    ? modifiers\n    : modifiers.slice(0, findIndex(modifiers, 'name', ends));\n\n  modifiersToRun.forEach(modifier => {\n    if (modifier['function']) { // eslint-disable-line dot-notation\n      console.warn('`modifier.function` is deprecated, use `modifier.fn`!');\n    }\n    const fn = modifier['function'] || modifier.fn; // eslint-disable-line dot-notation\n    if (modifier.enabled && isFunction(fn)) {\n      // Add properties to offsets to make them a complete clientRect object\n      // we do this before each modifier to make sure the previous one doesn't\n      // mess with these values\n      data.offsets.popper = getClientRect(data.offsets.popper);\n      data.offsets.reference = getClientRect(data.offsets.reference);\n\n      data = fn(data, modifier);\n    }\n  });\n\n  return data;\n}\n", "import computeAutoPlacement from '../utils/computeAutoPlacement';\nimport getReferenceOffsets from '../utils/getReferenceOffsets';\nimport getPopperOffsets from '../utils/getPopperOffsets';\nimport runModifiers from '../utils/runModifiers';\n\n/**\n * Updates the position of the popper, computing the new offsets and applying\n * the new style.<br />\n * Prefer `scheduleUpdate` over `update` because of performance reasons.\n * @method\n * @memberof Popper\n */\nexport default function update() {\n  // if popper is destroyed, don't perform any further update\n  if (this.state.isDestroyed) {\n    return;\n  }\n\n  let data = {\n    instance: this,\n    styles: {},\n    arrowStyles: {},\n    attributes: {},\n    flipped: false,\n    offsets: {},\n  };\n\n  // compute reference element offsets\n  data.offsets.reference = getReferenceOffsets(\n    this.state,\n    this.popper,\n    this.reference,\n    this.options.positionFixed\n  );\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  data.placement = computeAutoPlacement(\n    this.options.placement,\n    data.offsets.reference,\n    this.popper,\n    this.reference,\n    this.options.modifiers.flip.boundariesElement,\n    this.options.modifiers.flip.padding\n  );\n\n  // store the computed placement inside `originalPlacement`\n  data.originalPlacement = data.placement;\n\n  data.positionFixed = this.options.positionFixed;\n\n  // compute the popper offsets\n  data.offsets.popper = getPopperOffsets(\n    this.popper,\n    data.offsets.reference,\n    data.placement\n  );\n\n  data.offsets.popper.position = this.options.positionFixed\n    ? 'fixed'\n    : 'absolute';\n\n  // run the modifiers\n  data = runModifiers(this.modifiers, data);\n\n  // the first `update` will call `onCreate` callback\n  // the other ones will call `onUpdate` callback\n  if (!this.state.isCreated) {\n    this.state.isCreated = true;\n    this.options.onCreate(data);\n  } else {\n    this.options.onUpdate(data);\n  }\n}\n", "/**\n * Helper used to know if the given modifier is enabled.\n * @method\n * @memberof Popper.Utils\n * @returns {Boolean}\n */\nexport default function isModifierEnabled(modifiers, modifierName) {\n  return modifiers.some(\n    ({ name, enabled }) => enabled && name === modifierName\n  );\n}\n", "/**\n * Get the prefixed supported property name\n * @method\n * @memberof Popper.Utils\n * @argument {String} property (camelCase)\n * @returns {String} prefixed property (camelCase or PascalCase, depending on the vendor prefix)\n */\nexport default function getSupportedPropertyName(property) {\n  const prefixes = [false, 'ms', 'Webkit', 'Moz', 'O'];\n  const upperProp = property.charAt(0).toUpperCase() + property.slice(1);\n\n  for (let i = 0; i < prefixes.length; i++) {\n    const prefix = prefixes[i];\n    const toCheck = prefix ? `${prefix}${upperProp}` : property;\n    if (typeof document.body.style[toCheck] !== 'undefined') {\n      return toCheck;\n    }\n  }\n  return null;\n}\n", "import isModifierEnabled from '../utils/isModifierEnabled';\nimport getSupportedPropertyName from '../utils/getSupportedPropertyName';\n\n/**\n * Destroys the popper.\n * @method\n * @memberof Popper\n */\nexport default function destroy() {\n  this.state.isDestroyed = true;\n\n  // touch DOM only if `applyStyle` modifier is enabled\n  if (isModifierEnabled(this.modifiers, 'applyStyle')) {\n    this.popper.removeAttribute('x-placement');\n    this.popper.style.position = '';\n    this.popper.style.top = '';\n    this.popper.style.left = '';\n    this.popper.style.right = '';\n    this.popper.style.bottom = '';\n    this.popper.style.willChange = '';\n    this.popper.style[getSupportedPropertyName('transform')] = '';\n  }\n\n  this.disableEventListeners();\n\n  // remove the popper if user explicitly asked for the deletion on destroy\n  // do not use `remove` because IE11 doesn't support it\n  if (this.options.removeOnDestroy) {\n    this.popper.parentNode.removeChild(this.popper);\n  }\n  return this;\n}\n", "/**\n * Get the window associated with the element\n * @argument {Element} element\n * @returns {Window}\n */\nexport default function getWindow(element) {\n  const ownerDocument = element.ownerDocument;\n  return ownerDocument ? ownerDocument.defaultView : window;\n}\n", "import getScrollParent from './getScrollParent';\nimport getWindow from './getWindow';\n\nfunction attachToScrollParents(scrollParent, event, callback, scrollParents) {\n  const isBody = scrollParent.nodeName === 'BODY';\n  const target = isBody ? scrollParent.ownerDocument.defaultView : scrollParent;\n  target.addEventListener(event, callback, { passive: true });\n\n  if (!isBody) {\n    attachToScrollParents(\n      getScrollParent(target.parentNode),\n      event,\n      callback,\n      scrollParents\n    );\n  }\n  scrollParents.push(target);\n}\n\n/**\n * Setup needed event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nexport default function setupEventListeners(\n  reference,\n  options,\n  state,\n  updateBound\n) {\n  // Resize event listener on window\n  state.updateBound = updateBound;\n  getWindow(reference).addEventListener('resize', state.updateBound, { passive: true });\n\n  // Scroll event listener on scroll parents\n  const scrollElement = getScrollParent(reference);\n  attachToScrollParents(\n    scrollElement,\n    'scroll',\n    state.updateBound,\n    state.scrollParents\n  );\n  state.scrollElement = scrollElement;\n  state.eventsEnabled = true;\n\n  return state;\n}\n", "import setupEventListeners from '../utils/setupEventListeners';\n\n/**\n * It will add resize/scroll events and start recalculating\n * position of the popper element when they are triggered.\n * @method\n * @memberof Popper\n */\nexport default function enableEventListeners() {\n  if (!this.state.eventsEnabled) {\n    this.state = setupEventListeners(\n      this.reference,\n      this.options,\n      this.state,\n      this.scheduleUpdate\n    );\n  }\n}\n", "import getWindow from './getWindow';\n\n/**\n * Remove event listeners used to update the popper position\n * @method\n * @memberof Popper.Utils\n * @private\n */\nexport default function removeEventListeners(reference, state) {\n  // Remove resize event listener on window\n  getWindow(reference).removeEventListener('resize', state.updateBound);\n\n  // Remove scroll event listener on scroll parents\n  state.scrollParents.forEach(target => {\n    target.removeEventListener('scroll', state.updateBound);\n  });\n\n  // Reset state\n  state.updateBound = null;\n  state.scrollParents = [];\n  state.scrollElement = null;\n  state.eventsEnabled = false;\n  return state;\n}\n", "import removeEventListeners from '../utils/removeEventListeners';\n\n/**\n * It will remove resize/scroll events and won't recalculate popper position\n * when they are triggered. It also won't trigger `onUpdate` callback anymore,\n * unless you call `update` method manually.\n * @method\n * @memberof Popper\n */\nexport default function disableEventListeners() {\n  if (this.state.eventsEnabled) {\n    cancelAnimationFrame(this.scheduleUpdate);\n    this.state = removeEventListeners(this.reference, this.state);\n  }\n}\n", "/**\n * Tells if a given input is a number\n * @method\n * @memberof Popper.Utils\n * @param {*} input to check\n * @return {Boolean}\n */\nexport default function isNumeric(n) {\n  return n !== '' && !isNaN(parseFloat(n)) && isFinite(n);\n}\n", "import isNumeric from './isNumeric';\n\n/**\n * Set the style to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the style to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nexport default function setStyles(element, styles) {\n  Object.keys(styles).forEach(prop => {\n    let unit = '';\n    // add unit if the value is numeric and is one of the following\n    if (\n      ['width', 'height', 'top', 'right', 'bottom', 'left'].indexOf(prop) !==\n        -1 &&\n      isNumeric(styles[prop])\n    ) {\n      unit = 'px';\n    }\n    element.style[prop] = styles[prop] + unit;\n  });\n}\n", "/**\n * Set the attributes to the given popper\n * @method\n * @memberof Popper.Utils\n * @argument {Element} element - Element to apply the attributes to\n * @argument {Object} styles\n * Object with a list of properties and values which will be applied to the element\n */\nexport default function setAttributes(element, attributes) {\n  Object.keys(attributes).forEach(function(prop) {\n    const value = attributes[prop];\n    if (value !== false) {\n      element.setAttribute(prop, attributes[prop]);\n    } else {\n      element.removeAttribute(prop);\n    }\n  });\n}\n", "import setStyles from '../utils/setStyles';\nimport setAttributes from '../utils/setAttributes';\nimport getReferenceOffsets from '../utils/getReferenceOffsets';\nimport computeAutoPlacement from '../utils/computeAutoPlacement';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} data.styles - List of style properties - values to apply to popper element\n * @argument {Object} data.attributes - List of attribute properties - values to apply to popper element\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The same data object\n */\nexport default function applyStyle(data) {\n  // any property present in `data.styles` will be applied to the popper,\n  // in this way we can make the 3rd party modifiers add custom styles to it\n  // Be aware, modifiers could override the properties defined in the previous\n  // lines of this modifier!\n  setStyles(data.instance.popper, data.styles);\n\n  // any property present in `data.attributes` will be applied to the popper,\n  // they will be set as HTML attributes of the element\n  setAttributes(data.instance.popper, data.attributes);\n\n  // if arrowElement is defined and arrowStyles has some properties\n  if (data.arrowElement && Object.keys(data.arrowStyles).length) {\n    setStyles(data.arrowElement, data.arrowStyles);\n  }\n\n  return data;\n}\n\n/**\n * Set the x-placement attribute before everything else because it could be used\n * to add margins to the popper margins needs to be calculated to get the\n * correct popper offsets.\n * @method\n * @memberof Popper.modifiers\n * @param {HTMLElement} reference - The reference element used to position the popper\n * @param {HTMLElement} popper - The HTML element used as popper\n * @param {Object} options - Popper.js options\n */\nexport function applyStyleOnLoad(\n  reference,\n  popper,\n  options,\n  modifierOptions,\n  state\n) {\n  // compute reference element offsets\n  const referenceOffsets = getReferenceOffsets(state, popper, reference, options.positionFixed);\n\n  // compute auto placement, store placement inside the data object,\n  // modifiers will be able to edit `placement` if needed\n  // and refer to originalPlacement to know the original value\n  const placement = computeAutoPlacement(\n    options.placement,\n    referenceOffsets,\n    popper,\n    reference,\n    options.modifiers.flip.boundariesElement,\n    options.modifiers.flip.padding\n  );\n\n  popper.setAttribute('x-placement', placement);\n\n  // Apply `position` to popper before anything else because\n  // without the position applied we can't guarantee correct computations\n  setStyles(popper, { position: options.positionFixed ? 'fixed' : 'absolute' });\n\n  return options;\n}\n", "/**\n * @function\n * @memberof Popper.Utils\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Boolean} shouldRound - If the offsets should be rounded at all\n * @returns {Object} The popper's position offsets rounded\n *\n * The tale of pixel-perfect positioning. It's still not 100% perfect, but as\n * good as it can be within reason.\n * Discussion here: https://github.com/FezVrasta/popper.js/pull/715\n *\n * Low DPI screens cause a popper to be blurry if not using full pixels (Safari\n * as well on High DPI screens).\n *\n * Firefox prefers no rounding for positioning and does not have blurriness on\n * high DPI screens.\n *\n * Only horizontal placement and left/right values need to be considered.\n */\nexport default function getRoundedOffsets(data, shouldRound) {\n  const { popper, reference } = data.offsets;\n  const { round, floor } = Math;\n  const noRound = v => v;\n  \n  const referenceWidth = round(reference.width);\n  const popperWidth = round(popper.width);\n  \n  const isVertical = ['left', 'right'].indexOf(data.placement) !== -1;\n  const isVariation = data.placement.indexOf('-') !== -1;\n  const sameWidthParity = referenceWidth % 2 === popperWidth % 2;\n  const bothOddWidth = referenceWidth % 2 === 1 && popperWidth % 2 === 1;\n\n  const horizontalToInteger = !shouldRound\n    ? noRound\n    : isVertical || isVariation || sameWidthParity\n    ? round\n    : floor;\n  const verticalToInteger = !shouldRound ? noRound : round;\n\n  return {\n    left: horizontalToInteger(\n      bothOddWidth && !isVariation && shouldRound\n        ? popper.left - 1\n        : popper.left\n    ),\n    top: verticalToInteger(popper.top),\n    bottom: verticalToInteger(popper.bottom),\n    right: horizontalToInteger(popper.right),\n  };\n}\n", "import getSupportedPropertyName from '../utils/getSupportedPropertyName';\nimport find from '../utils/find';\nimport getOffsetParent from '../utils/getOffsetParent';\nimport getBoundingClientRect from '../utils/getBoundingClientRect';\nimport getRoundedOffsets from '../utils/getRoundedOffsets';\nimport isBrowser from '../utils/isBrowser';\n\nconst isFirefox = isBrowser && /Firefox/i.test(navigator.userAgent);\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function computeStyle(data, options) {\n  const { x, y } = options;\n  const { popper } = data.offsets;\n\n  // Remove this legacy support in Popper.js v2\n  const legacyGpuAccelerationOption = find(\n    data.instance.modifiers,\n    modifier => modifier.name === 'applyStyle'\n  ).gpuAcceleration;\n  if (legacyGpuAccelerationOption !== undefined) {\n    console.warn(\n      'WARNING: `gpuAcceleration` option moved to `computeStyle` modifier and will not be supported in future versions of Popper.js!'\n    );\n  }\n  const gpuAcceleration =\n    legacyGpuAccelerationOption !== undefined\n      ? legacyGpuAccelerationOption\n      : options.gpuAcceleration;\n\n  const offsetParent = getOffsetParent(data.instance.popper);\n  const offsetParentRect = getBoundingClientRect(offsetParent);\n\n  // Styles\n  const styles = {\n    position: popper.position,\n  };\n\n  const offsets = getRoundedOffsets(\n    data,\n    window.devicePixelRatio < 2 || !isFirefox\n  );\n\n  const sideA = x === 'bottom' ? 'top' : 'bottom';\n  const sideB = y === 'right' ? 'left' : 'right';\n\n  // if gpuAcceleration is set to `true` and transform is supported,\n  //  we use `translate3d` to apply the position to the popper we\n  // automatically use the supported prefixed version if needed\n  const prefixedProperty = getSupportedPropertyName('transform');\n\n  // now, let's make a step back and look at this code closely (wtf?)\n  // If the content of the popper grows once it's been positioned, it\n  // may happen that the popper gets misplaced because of the new content\n  // overflowing its reference element\n  // To avoid this problem, we provide two options (x and y), which allow\n  // the consumer to define the offset origin.\n  // If we position a popper on top of a reference element, we can set\n  // `x` to `top` to make the popper grow towards its top instead of\n  // its bottom.\n  let left, top;\n  if (sideA === 'bottom') {\n    // when offsetParent is <html> the positioning is relative to the bottom of the screen (excluding the scrollbar)\n    // and not the bottom of the html element\n    if (offsetParent.nodeName === 'HTML') {\n      top = -offsetParent.clientHeight + offsets.bottom;\n    } else {\n      top = -offsetParentRect.height + offsets.bottom;\n    }\n  } else {\n    top = offsets.top;\n  }\n  if (sideB === 'right') {\n    if (offsetParent.nodeName === 'HTML') {\n      left = -offsetParent.clientWidth + offsets.right;\n    } else {\n      left = -offsetParentRect.width + offsets.right;\n    }\n  } else {\n    left = offsets.left;\n  }\n  if (gpuAcceleration && prefixedProperty) {\n    styles[prefixedProperty] = `translate3d(${left}px, ${top}px, 0)`;\n    styles[sideA] = 0;\n    styles[sideB] = 0;\n    styles.willChange = 'transform';\n  } else {\n    // othwerise, we use the standard `top`, `left`, `bottom` and `right` properties\n    const invertTop = sideA === 'bottom' ? -1 : 1;\n    const invertLeft = sideB === 'right' ? -1 : 1;\n    styles[sideA] = top * invertTop;\n    styles[sideB] = left * invertLeft;\n    styles.willChange = `${sideA}, ${sideB}`;\n  }\n\n  // Attributes\n  const attributes = {\n    'x-placement': data.placement,\n  };\n\n  // Update `data` attributes, styles and arrowStyles\n  data.attributes = { ...attributes, ...data.attributes };\n  data.styles = { ...styles, ...data.styles };\n  data.arrowStyles = { ...data.offsets.arrow, ...data.arrowStyles };\n\n  return data;\n}\n", "import find from './find';\n\n/**\n * Helper used to know if the given modifier depends from another one.<br />\n * It checks if the needed modifier is listed and enabled.\n * @method\n * @memberof Popper.Utils\n * @param {Array} modifiers - list of modifiers\n * @param {String} requestingName - name of requesting modifier\n * @param {String} requestedName - name of requested modifier\n * @returns {Boolean}\n */\nexport default function isModifierRequired(\n  modifiers,\n  requestingName,\n  requestedName\n) {\n  const requesting = find(modifiers, ({ name }) => name === requestingName);\n\n  const isRequired =\n    !!requesting &&\n    modifiers.some(modifier => {\n      return (\n        modifier.name === requestedName &&\n        modifier.enabled &&\n        modifier.order < requesting.order\n      );\n    });\n\n  if (!isRequired) {\n    const requesting = `\\`${requestingName}\\``;\n    const requested = `\\`${requestedName}\\``;\n    console.warn(\n      `${requested} modifier is required by ${requesting} modifier in order to work, be sure to include it before ${requesting}!`\n    );\n  }\n  return isRequired;\n}\n", "import getClientRect from '../utils/getClientRect';\nimport getOuterSizes from '../utils/getOuterSizes';\nimport isModifierRequired from '../utils/isModifierRequired';\nimport getStyleComputedProperty from '../utils/getStyleComputedProperty';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function arrow(data, options) {\n  // arrow depends on keepTogether in order to work\n  if (!isModifierRequired(data.instance.modifiers, 'arrow', 'keepTogether')) {\n    return data;\n  }\n\n  let arrowElement = options.element;\n\n  // if arrowElement is a string, suppose it's a CSS selector\n  if (typeof arrowElement === 'string') {\n    arrowElement = data.instance.popper.querySelector(arrowElement);\n\n    // if arrowElement is not found, don't run the modifier\n    if (!arrowElement) {\n      return data;\n    }\n  } else {\n    // if the arrowElement isn't a query selector we must check that the\n    // provided DOM node is child of its popper node\n    if (!data.instance.popper.contains(arrowElement)) {\n      console.warn(\n        'WARNING: `arrow.element` must be child of its popper element!'\n      );\n      return data;\n    }\n  }\n\n  const placement = data.placement.split('-')[0];\n  const { popper, reference } = data.offsets;\n  const isVertical = ['left', 'right'].indexOf(placement) !== -1;\n\n  const len = isVertical ? 'height' : 'width';\n  const sideCapitalized = isVertical ? 'Top' : 'Left';\n  const side = sideCapitalized.toLowerCase();\n  const altSide = isVertical ? 'left' : 'top';\n  const opSide = isVertical ? 'bottom' : 'right';\n  const arrowElementSize = getOuterSizes(arrowElement)[len];\n\n  //\n  // extends keepTogether behavior making sure the popper and its\n  // reference have enough pixels in conjunction\n  //\n\n  // top/left side\n  if (reference[opSide] - arrowElementSize < popper[side]) {\n    data.offsets.popper[side] -=\n      popper[side] - (reference[opSide] - arrowElementSize);\n  }\n  // bottom/right side\n  if (reference[side] + arrowElementSize > popper[opSide]) {\n    data.offsets.popper[side] +=\n      reference[side] + arrowElementSize - popper[opSide];\n  }\n  data.offsets.popper = getClientRect(data.offsets.popper);\n\n  // compute center of the popper\n  const center = reference[side] + reference[len] / 2 - arrowElementSize / 2;\n\n  // Compute the sideValue using the updated popper offsets\n  // take popper margin in account because we don't have this info available\n  const css = getStyleComputedProperty(data.instance.popper);\n  const popperMarginSide = parseFloat(css[`margin${sideCapitalized}`]);\n  const popperBorderSide = parseFloat(css[`border${sideCapitalized}Width`]);\n  let sideValue =\n    center - data.offsets.popper[side] - popperMarginSide - popperBorderSide;\n\n  // prevent arrowElement from being placed not contiguously to its popper\n  sideValue = Math.max(Math.min(popper[len] - arrowElementSize, sideValue), 0);\n\n  data.arrowElement = arrowElement;\n  data.offsets.arrow = {\n    [side]: Math.round(sideValue),\n    [altSide]: '', // make sure to unset any eventual altSide value from the DOM node\n  };\n\n  return data;\n}\n", "/**\n * Get the opposite placement variation of the given one\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement variation\n * @returns {String} flipped placement variation\n */\nexport default function getOppositeVariation(variation) {\n  if (variation === 'end') {\n    return 'start';\n  } else if (variation === 'start') {\n    return 'end';\n  }\n  return variation;\n}\n", "/**\n * List of accepted placements to use as values of the `placement` option.<br />\n * Valid placements are:\n * - `auto`\n * - `top`\n * - `right`\n * - `bottom`\n * - `left`\n *\n * Each placement can have a variation from this list:\n * - `-start`\n * - `-end`\n *\n * Variations are interpreted easily if you think of them as the left to right\n * written languages. Horizontally (`top` and `bottom`), `start` is left and `end`\n * is right.<br />\n * Vertically (`left` and `right`), `start` is top and `end` is bottom.\n *\n * Some valid examples are:\n * - `top-end` (on top of reference, right aligned)\n * - `right-start` (on right of reference, top aligned)\n * - `bottom` (on bottom, centered)\n * - `auto-end` (on the side with more space available, alignment depends by placement)\n *\n * @static\n * @type {Array}\n * @enum {String}\n * @readonly\n * @method placements\n * @memberof Popper\n */\nexport default [\n  'auto-start',\n  'auto',\n  'auto-end',\n  'top-start',\n  'top',\n  'top-end',\n  'right-start',\n  'right',\n  'right-end',\n  'bottom-end',\n  'bottom',\n  'bottom-start',\n  'left-end',\n  'left',\n  'left-start',\n];\n", "import placements from '../methods/placements';\n\n// Get rid of `auto` `auto-start` and `auto-end`\nconst validPlacements = placements.slice(3);\n\n/**\n * Given an initial placement, returns all the subsequent placements\n * clockwise (or counter-clockwise).\n *\n * @method\n * @memberof Popper.Utils\n * @argument {String} placement - A valid placement (it accepts variations)\n * @argument {Boolean} counter - Set to true to walk the placements counterclockwise\n * @returns {Array} placements including their variations\n */\nexport default function clockwise(placement, counter = false) {\n  const index = validPlacements.indexOf(placement);\n  const arr = validPlacements\n    .slice(index + 1)\n    .concat(validPlacements.slice(0, index));\n  return counter ? arr.reverse() : arr;\n}\n", "import getOppositePlacement from '../utils/getOppositePlacement';\nimport getOppositeVariation from '../utils/getOppositeVariation';\nimport getPopperOffsets from '../utils/getPopperOffsets';\nimport runModifiers from '../utils/runModifiers';\nimport getBoundaries from '../utils/getBoundaries';\nimport isModifierEnabled from '../utils/isModifierEnabled';\nimport clockwise from '../utils/clockwise';\n\nconst BEHAVIORS = {\n  FLIP: 'flip',\n  CLOCKWISE: 'clockwise',\n  COUNTERCLOCKWISE: 'counterclockwise',\n};\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function flip(data, options) {\n  // if `inner` modifier is enabled, we can't use the `flip` modifier\n  if (isModifierEnabled(data.instance.modifiers, 'inner')) {\n    return data;\n  }\n\n  if (data.flipped && data.placement === data.originalPlacement) {\n    // seems like flip is trying to loop, probably there's not enough space on any of the flippable sides\n    return data;\n  }\n\n  const boundaries = getBoundaries(\n    data.instance.popper,\n    data.instance.reference,\n    options.padding,\n    options.boundariesElement,\n    data.positionFixed\n  );\n\n  let placement = data.placement.split('-')[0];\n  let placementOpposite = getOppositePlacement(placement);\n  let variation = data.placement.split('-')[1] || '';\n\n  let flipOrder = [];\n\n  switch (options.behavior) {\n    case BEHAVIORS.FLIP:\n      flipOrder = [placement, placementOpposite];\n      break;\n    case BEHAVIORS.CLOCKWISE:\n      flipOrder = clockwise(placement);\n      break;\n    case BEHAVIORS.COUNTERCLOCKWISE:\n      flipOrder = clockwise(placement, true);\n      break;\n    default:\n      flipOrder = options.behavior;\n  }\n\n  flipOrder.forEach((step, index) => {\n    if (placement !== step || flipOrder.length === index + 1) {\n      return data;\n    }\n\n    placement = data.placement.split('-')[0];\n    placementOpposite = getOppositePlacement(placement);\n\n    const popperOffsets = data.offsets.popper;\n    const refOffsets = data.offsets.reference;\n\n    // using floor because the reference offsets may contain decimals we are not going to consider here\n    const floor = Math.floor;\n    const overlapsRef =\n      (placement === 'left' &&\n        floor(popperOffsets.right) > floor(refOffsets.left)) ||\n      (placement === 'right' &&\n        floor(popperOffsets.left) < floor(refOffsets.right)) ||\n      (placement === 'top' &&\n        floor(popperOffsets.bottom) > floor(refOffsets.top)) ||\n      (placement === 'bottom' &&\n        floor(popperOffsets.top) < floor(refOffsets.bottom));\n\n    const overflowsLeft = floor(popperOffsets.left) < floor(boundaries.left);\n    const overflowsRight = floor(popperOffsets.right) > floor(boundaries.right);\n    const overflowsTop = floor(popperOffsets.top) < floor(boundaries.top);\n    const overflowsBottom =\n      floor(popperOffsets.bottom) > floor(boundaries.bottom);\n\n    const overflowsBoundaries =\n      (placement === 'left' && overflowsLeft) ||\n      (placement === 'right' && overflowsRight) ||\n      (placement === 'top' && overflowsTop) ||\n      (placement === 'bottom' && overflowsBottom);\n\n    // flip the variation if required\n    const isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n\n    // flips variation if reference element overflows boundaries\n    const flippedVariationByRef =\n      !!options.flipVariations &&\n      ((isVertical && variation === 'start' && overflowsLeft) ||\n        (isVertical && variation === 'end' && overflowsRight) ||\n        (!isVertical && variation === 'start' && overflowsTop) ||\n        (!isVertical && variation === 'end' && overflowsBottom));\n\n    // flips variation if popper content overflows boundaries\n    const flippedVariationByContent =\n      !!options.flipVariationsByContent &&\n      ((isVertical && variation === 'start' && overflowsRight) ||\n        (isVertical && variation === 'end' && overflowsLeft) ||\n        (!isVertical && variation === 'start' && overflowsBottom) ||\n        (!isVertical && variation === 'end' && overflowsTop));\n\n    const flippedVariation = flippedVariationByRef || flippedVariationByContent;\n\n    if (overlapsRef || overflowsBoundaries || flippedVariation) {\n      // this boolean to detect any flip loop\n      data.flipped = true;\n\n      if (overlapsRef || overflowsBoundaries) {\n        placement = flipOrder[index + 1];\n      }\n\n      if (flippedVariation) {\n        variation = getOppositeVariation(variation);\n      }\n\n      data.placement = placement + (variation ? '-' + variation : '');\n\n      // this object contains `position`, we want to preserve it along with\n      // any additional property we may add in the future\n      data.offsets.popper = {\n        ...data.offsets.popper,\n        ...getPopperOffsets(\n          data.instance.popper,\n          data.offsets.reference,\n          data.placement\n        ),\n      };\n\n      data = runModifiers(data.instance.modifiers, data, 'flip');\n    }\n  });\n  return data;\n}\n", "/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function keepTogether(data) {\n  const { popper, reference } = data.offsets;\n  const placement = data.placement.split('-')[0];\n  const floor = Math.floor;\n  const isVertical = ['top', 'bottom'].indexOf(placement) !== -1;\n  const side = isVertical ? 'right' : 'bottom';\n  const opSide = isVertical ? 'left' : 'top';\n  const measurement = isVertical ? 'width' : 'height';\n\n  if (popper[side] < floor(reference[opSide])) {\n    data.offsets.popper[opSide] =\n      floor(reference[opSide]) - popper[measurement];\n  }\n  if (popper[opSide] > floor(reference[side])) {\n    data.offsets.popper[opSide] = floor(reference[side]);\n  }\n\n  return data;\n}\n", "import isNumeric from '../utils/isNumeric';\nimport getClientRect from '../utils/getClientRect';\nimport find from '../utils/find';\n\n/**\n * Converts a string containing value + unit into a px value number\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} str - Value + unit string\n * @argument {String} measurement - `height` or `width`\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @returns {Number|String}\n * Value in pixels, or original string if no values were extracted\n */\nexport function toValue(str, measurement, popperOffsets, referenceOffsets) {\n  // separate value from unit\n  const split = str.match(/((?:\\-|\\+)?\\d*\\.?\\d*)(.*)/);\n  const value = +split[1];\n  const unit = split[2];\n\n  // If it's not a number it's an operator, I guess\n  if (!value) {\n    return str;\n  }\n\n  if (unit.indexOf('%') === 0) {\n    let element;\n    switch (unit) {\n      case '%p':\n        element = popperOffsets;\n        break;\n      case '%':\n      case '%r':\n      default:\n        element = referenceOffsets;\n    }\n\n    const rect = getClientRect(element);\n    return rect[measurement] / 100 * value;\n  } else if (unit === 'vh' || unit === 'vw') {\n    // if is a vh or vw, we calculate the size based on the viewport\n    let size;\n    if (unit === 'vh') {\n      size = Math.max(\n        document.documentElement.clientHeight,\n        window.innerHeight || 0\n      );\n    } else {\n      size = Math.max(\n        document.documentElement.clientWidth,\n        window.innerWidth || 0\n      );\n    }\n    return size / 100 * value;\n  } else {\n    // if is an explicit pixel unit, we get rid of the unit and keep the value\n    // if is an implicit unit, it's px, and we return just the value\n    return value;\n  }\n}\n\n/**\n * Parse an `offset` string to extrapolate `x` and `y` numeric offsets.\n * @function\n * @memberof {modifiers~offset}\n * @private\n * @argument {String} offset\n * @argument {Object} popperOffsets\n * @argument {Object} referenceOffsets\n * @argument {String} basePlacement\n * @returns {Array} a two cells array with x and y offsets in numbers\n */\nexport function parseOffset(\n  offset,\n  popperOffsets,\n  referenceOffsets,\n  basePlacement\n) {\n  const offsets = [0, 0];\n\n  // Use height if placement is left or right and index is 0 otherwise use width\n  // in this way the first offset will use an axis and the second one\n  // will use the other one\n  const useHeight = ['right', 'left'].indexOf(basePlacement) !== -1;\n\n  // Split the offset string to obtain a list of values and operands\n  // The regex addresses values with the plus or minus sign in front (+10, -20, etc)\n  const fragments = offset.split(/(\\+|\\-)/).map(frag => frag.trim());\n\n  // Detect if the offset string contains a pair of values or a single one\n  // they could be separated by comma or space\n  const divider = fragments.indexOf(\n    find(fragments, frag => frag.search(/,|\\s/) !== -1)\n  );\n\n  if (fragments[divider] && fragments[divider].indexOf(',') === -1) {\n    console.warn(\n      'Offsets separated by white space(s) are deprecated, use a comma (,) instead.'\n    );\n  }\n\n  // If divider is found, we divide the list of values and operands to divide\n  // them by ofset X and Y.\n  const splitRegex = /\\s*,\\s*|\\s+/;\n  let ops = divider !== -1\n    ? [\n        fragments\n          .slice(0, divider)\n          .concat([fragments[divider].split(splitRegex)[0]]),\n        [fragments[divider].split(splitRegex)[1]].concat(\n          fragments.slice(divider + 1)\n        ),\n      ]\n    : [fragments];\n\n  // Convert the values with units to absolute pixels to allow our computations\n  ops = ops.map((op, index) => {\n    // Most of the units rely on the orientation of the popper\n    const measurement = (index === 1 ? !useHeight : useHeight)\n      ? 'height'\n      : 'width';\n    let mergeWithPrevious = false;\n    return (\n      op\n        // This aggregates any `+` or `-` sign that aren't considered operators\n        // e.g.: 10 + +5 => [10, +, +5]\n        .reduce((a, b) => {\n          if (a[a.length - 1] === '' && ['+', '-'].indexOf(b) !== -1) {\n            a[a.length - 1] = b;\n            mergeWithPrevious = true;\n            return a;\n          } else if (mergeWithPrevious) {\n            a[a.length - 1] += b;\n            mergeWithPrevious = false;\n            return a;\n          } else {\n            return a.concat(b);\n          }\n        }, [])\n        // Here we convert the string values into number values (in px)\n        .map(str => toValue(str, measurement, popperOffsets, referenceOffsets))\n    );\n  });\n\n  // Loop trough the offsets arrays and execute the operations\n  ops.forEach((op, index) => {\n    op.forEach((frag, index2) => {\n      if (isNumeric(frag)) {\n        offsets[index] += frag * (op[index2 - 1] === '-' ? -1 : 1);\n      }\n    });\n  });\n  return offsets;\n}\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @argument {Number|String} options.offset=0\n * The offset value as described in the modifier description\n * @returns {Object} The data object, properly modified\n */\nexport default function offset(data, { offset }) {\n  const { placement, offsets: { popper, reference } } = data;\n  const basePlacement = placement.split('-')[0];\n\n  let offsets;\n  if (isNumeric(+offset)) {\n    offsets = [+offset, 0];\n  } else {\n    offsets = parseOffset(offset, popper, reference, basePlacement);\n  }\n\n  if (basePlacement === 'left') {\n    popper.top += offsets[0];\n    popper.left -= offsets[1];\n  } else if (basePlacement === 'right') {\n    popper.top += offsets[0];\n    popper.left += offsets[1];\n  } else if (basePlacement === 'top') {\n    popper.left += offsets[0];\n    popper.top -= offsets[1];\n  } else if (basePlacement === 'bottom') {\n    popper.left += offsets[0];\n    popper.top += offsets[1];\n  }\n\n  data.popper = popper;\n  return data;\n}\n", "import getOffsetParent from '../utils/getOffsetParent';\nimport getBoundaries from '../utils/getBoundaries';\nimport getSupportedPropertyName from '../utils/getSupportedPropertyName';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function preventOverflow(data, options) {\n  let boundariesElement =\n    options.boundariesElement || getOffsetParent(data.instance.popper);\n\n  // If offsetParent is the reference element, we really want to\n  // go one step up and use the next offsetParent as reference to\n  // avoid to make this modifier completely useless and look like broken\n  if (data.instance.reference === boundariesElement) {\n    boundariesElement = getOffsetParent(boundariesElement);\n  }\n\n  // NOTE: DOM access here\n  // resets the popper's position so that the document size can be calculated excluding\n  // the size of the popper element itself\n  const transformProp = getSupportedPropertyName('transform');\n  const popperStyles = data.instance.popper.style; // assignment to help minification\n  const { top, left, [transformProp]: transform } = popperStyles;\n  popperStyles.top = '';\n  popperStyles.left = '';\n  popperStyles[transformProp] = '';\n\n  const boundaries = getBoundaries(\n    data.instance.popper,\n    data.instance.reference,\n    options.padding,\n    boundariesElement,\n    data.positionFixed\n  );\n\n  // NOTE: DOM access here\n  // restores the original style properties after the offsets have been computed\n  popperStyles.top = top;\n  popperStyles.left = left;\n  popperStyles[transformProp] = transform;\n\n  options.boundaries = boundaries;\n\n  const order = options.priority;\n  let popper = data.offsets.popper;\n\n  const check = {\n    primary(placement) {\n      let value = popper[placement];\n      if (\n        popper[placement] < boundaries[placement] &&\n        !options.escapeWithReference\n      ) {\n        value = Math.max(popper[placement], boundaries[placement]);\n      }\n      return { [placement]: value };\n    },\n    secondary(placement) {\n      const mainSide = placement === 'right' ? 'left' : 'top';\n      let value = popper[mainSide];\n      if (\n        popper[placement] > boundaries[placement] &&\n        !options.escapeWithReference\n      ) {\n        value = Math.min(\n          popper[mainSide],\n          boundaries[placement] -\n            (placement === 'right' ? popper.width : popper.height)\n        );\n      }\n      return { [mainSide]: value };\n    },\n  };\n\n  order.forEach(placement => {\n    const side =\n      ['left', 'top'].indexOf(placement) !== -1 ? 'primary' : 'secondary';\n    popper = { ...popper, ...check[side](placement) };\n  });\n\n  data.offsets.popper = popper;\n\n  return data;\n}\n", "/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function shift(data) {\n  const placement = data.placement;\n  const basePlacement = placement.split('-')[0];\n  const shiftvariation = placement.split('-')[1];\n\n  // if shift shiftvariation is specified, run the modifier\n  if (shiftvariation) {\n    const { reference, popper } = data.offsets;\n    const isVertical = ['bottom', 'top'].indexOf(basePlacement) !== -1;\n    const side = isVertical ? 'left' : 'top';\n    const measurement = isVertical ? 'width' : 'height';\n\n    const shiftOffsets = {\n      start: { [side]: reference[side] },\n      end: {\n        [side]: reference[side] + reference[measurement] - popper[measurement],\n      },\n    };\n\n    data.offsets.popper = { ...popper, ...shiftOffsets[shiftvariation] };\n  }\n\n  return data;\n}\n", "import isModifierRequired from '../utils/isModifierRequired';\nimport find from '../utils/find';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by update method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function hide(data) {\n  if (!isModifierRequired(data.instance.modifiers, 'hide', 'preventOverflow')) {\n    return data;\n  }\n\n  const refRect = data.offsets.reference;\n  const bound = find(\n    data.instance.modifiers,\n    modifier => modifier.name === 'preventOverflow'\n  ).boundaries;\n\n  if (\n    refRect.bottom < bound.top ||\n    refRect.left > bound.right ||\n    refRect.top > bound.bottom ||\n    refRect.right < bound.left\n  ) {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === true) {\n      return data;\n    }\n\n    data.hide = true;\n    data.attributes['x-out-of-boundaries'] = '';\n  } else {\n    // Avoid unnecessary DOM access if visibility hasn't changed\n    if (data.hide === false) {\n      return data;\n    }\n\n    data.hide = false;\n    data.attributes['x-out-of-boundaries'] = false;\n  }\n\n  return data;\n}\n", "import getClientRect from '../utils/getClientRect';\nimport getOppositePlacement from '../utils/getOppositePlacement';\n\n/**\n * @function\n * @memberof Modifiers\n * @argument {Object} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {Object} The data object, properly modified\n */\nexport default function inner(data) {\n  const placement = data.placement;\n  const basePlacement = placement.split('-')[0];\n  const { popper, reference } = data.offsets;\n  const isHoriz = ['left', 'right'].indexOf(basePlacement) !== -1;\n\n  const subtractLength = ['top', 'left'].indexOf(basePlacement) === -1;\n\n  popper[isHoriz ? 'left' : 'top'] =\n    reference[basePlacement] -\n    (subtractLength ? popper[isHoriz ? 'width' : 'height'] : 0);\n\n  data.placement = getOppositePlacement(placement);\n  data.offsets.popper = getClientRect(popper);\n\n  return data;\n}\n", "import applyStyle, { applyStyleOnLoad } from './applyStyle';\nimport computeStyle from './computeStyle';\nimport arrow from './arrow';\nimport flip from './flip';\nimport keepTogether from './keepTogether';\nimport offset from './offset';\nimport preventOverflow from './preventOverflow';\nimport shift from './shift';\nimport hide from './hide';\nimport inner from './inner';\n\n/**\n * Modifier function, each modifier can have a function of this type assigned\n * to its `fn` property.<br />\n * These functions will be called on each update, this means that you must\n * make sure they are performant enough to avoid performance bottlenecks.\n *\n * @function ModifierFn\n * @argument {dataObject} data - The data object generated by `update` method\n * @argument {Object} options - Modifiers configuration and options\n * @returns {dataObject} The data object, properly modified\n */\n\n/**\n * Modifiers are plugins used to alter the behavior of your poppers.<br />\n * Popper.js uses a set of 9 modifiers to provide all the basic functionalities\n * needed by the library.\n *\n * Usually you don't want to override the `order`, `fn` and `onLoad` props.\n * All the other properties are configurations that could be tweaked.\n * @namespace modifiers\n */\nexport default {\n  /**\n   * Modifier used to shift the popper on the start or end of its reference\n   * element.<br />\n   * It will read the variation of the `placement` property.<br />\n   * It can be one either `-end` or `-start`.\n   * @memberof modifiers\n   * @inner\n   */\n  shift: {\n    /** @prop {number} order=100 - Index used to define the order of execution */\n    order: 100,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: shift,\n  },\n\n  /**\n   * The `offset` modifier can shift your popper on both its axis.\n   *\n   * It accepts the following units:\n   * - `px` or unit-less, interpreted as pixels\n   * - `%` or `%r`, percentage relative to the length of the reference element\n   * - `%p`, percentage relative to the length of the popper element\n   * - `vw`, CSS viewport width unit\n   * - `vh`, CSS viewport height unit\n   *\n   * For length is intended the main axis relative to the placement of the popper.<br />\n   * This means that if the placement is `top` or `bottom`, the length will be the\n   * `width`. In case of `left` or `right`, it will be the `height`.\n   *\n   * You can provide a single value (as `Number` or `String`), or a pair of values\n   * as `String` divided by a comma or one (or more) white spaces.<br />\n   * The latter is a deprecated method because it leads to confusion and will be\n   * removed in v2.<br />\n   * Additionally, it accepts additions and subtractions between different units.\n   * Note that multiplications and divisions aren't supported.\n   *\n   * Valid examples are:\n   * ```\n   * 10\n   * '10%'\n   * '10, 10'\n   * '10%, 10'\n   * '10 + 10%'\n   * '10 - 5vh + 3%'\n   * '-10px + 5vh, 5px - 6%'\n   * ```\n   * > **NB**: If you desire to apply offsets to your poppers in a way that may make them overlap\n   * > with their reference element, unfortunately, you will have to disable the `flip` modifier.\n   * > You can read more on this at this [issue](https://github.com/FezVrasta/popper.js/issues/373).\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  offset: {\n    /** @prop {number} order=200 - Index used to define the order of execution */\n    order: 200,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: offset,\n    /** @prop {Number|String} offset=0\n     * The offset value as described in the modifier description\n     */\n    offset: 0,\n  },\n\n  /**\n   * Modifier used to prevent the popper from being positioned outside the boundary.\n   *\n   * A scenario exists where the reference itself is not within the boundaries.<br />\n   * We can say it has \"escaped the boundaries\" — or just \"escaped\".<br />\n   * In this case we need to decide whether the popper should either:\n   *\n   * - detach from the reference and remain \"trapped\" in the boundaries, or\n   * - if it should ignore the boundary and \"escape with its reference\"\n   *\n   * When `escapeWithReference` is set to`true` and reference is completely\n   * outside its boundaries, the popper will overflow (or completely leave)\n   * the boundaries in order to remain attached to the edge of the reference.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  preventOverflow: {\n    /** @prop {number} order=300 - Index used to define the order of execution */\n    order: 300,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: preventOverflow,\n    /**\n     * @prop {Array} [priority=['left','right','top','bottom']]\n     * Popper will try to prevent overflow following these priorities by default,\n     * then, it could overflow on the left and on top of the `boundariesElement`\n     */\n    priority: ['left', 'right', 'top', 'bottom'],\n    /**\n     * @prop {number} padding=5\n     * Amount of pixel used to define a minimum distance between the boundaries\n     * and the popper. This makes sure the popper always has a little padding\n     * between the edges of its container\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='scrollParent'\n     * Boundaries used by the modifier. Can be `scrollParent`, `window`,\n     * `viewport` or any DOM element.\n     */\n    boundariesElement: 'scrollParent',\n  },\n\n  /**\n   * Modifier used to make sure the reference and its popper stay near each other\n   * without leaving any gap between the two. Especially useful when the arrow is\n   * enabled and you want to ensure that it points to its reference element.\n   * It cares only about the first axis. You can still have poppers with margin\n   * between the popper and its reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  keepTogether: {\n    /** @prop {number} order=400 - Index used to define the order of execution */\n    order: 400,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: keepTogether,\n  },\n\n  /**\n   * This modifier is used to move the `arrowElement` of the popper to make\n   * sure it is positioned between the reference element and its popper element.\n   * It will read the outer size of the `arrowElement` node to detect how many\n   * pixels of conjunction are needed.\n   *\n   * It has no effect if no `arrowElement` is provided.\n   * @memberof modifiers\n   * @inner\n   */\n  arrow: {\n    /** @prop {number} order=500 - Index used to define the order of execution */\n    order: 500,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: arrow,\n    /** @prop {String|HTMLElement} element='[x-arrow]' - Selector or node used as arrow */\n    element: '[x-arrow]',\n  },\n\n  /**\n   * Modifier used to flip the popper's placement when it starts to overlap its\n   * reference element.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   *\n   * **NOTE:** this modifier will interrupt the current update cycle and will\n   * restart it if it detects the need to flip the placement.\n   * @memberof modifiers\n   * @inner\n   */\n  flip: {\n    /** @prop {number} order=600 - Index used to define the order of execution */\n    order: 600,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: flip,\n    /**\n     * @prop {String|Array} behavior='flip'\n     * The behavior used to change the popper's placement. It can be one of\n     * `flip`, `clockwise`, `counterclockwise` or an array with a list of valid\n     * placements (with optional variations)\n     */\n    behavior: 'flip',\n    /**\n     * @prop {number} padding=5\n     * The popper will flip if it hits the edges of the `boundariesElement`\n     */\n    padding: 5,\n    /**\n     * @prop {String|HTMLElement} boundariesElement='viewport'\n     * The element which will define the boundaries of the popper position.\n     * The popper will never be placed outside of the defined boundaries\n     * (except if `keepTogether` is enabled)\n     */\n    boundariesElement: 'viewport',\n    /**\n     * @prop {Boolean} flipVariations=false\n     * The popper will switch placement variation between `-start` and `-end` when\n     * the reference element overlaps its boundaries.\n     *\n     * The original placement should have a set variation.\n     */\n    flipVariations: false,\n    /**\n     * @prop {Boolean} flipVariationsByContent=false\n     * The popper will switch placement variation between `-start` and `-end` when\n     * the popper element overlaps its reference boundaries.\n     *\n     * The original placement should have a set variation.\n     */\n    flipVariationsByContent: false,\n  },\n\n  /**\n   * Modifier used to make the popper flow toward the inner of the reference element.\n   * By default, when this modifier is disabled, the popper will be placed outside\n   * the reference element.\n   * @memberof modifiers\n   * @inner\n   */\n  inner: {\n    /** @prop {number} order=700 - Index used to define the order of execution */\n    order: 700,\n    /** @prop {Boolean} enabled=false - Whether the modifier is enabled or not */\n    enabled: false,\n    /** @prop {ModifierFn} */\n    fn: inner,\n  },\n\n  /**\n   * Modifier used to hide the popper when its reference element is outside of the\n   * popper boundaries. It will set a `x-out-of-boundaries` attribute which can\n   * be used to hide with a CSS selector the popper when its reference is\n   * out of boundaries.\n   *\n   * Requires the `preventOverflow` modifier before it in order to work.\n   * @memberof modifiers\n   * @inner\n   */\n  hide: {\n    /** @prop {number} order=800 - Index used to define the order of execution */\n    order: 800,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: hide,\n  },\n\n  /**\n   * Computes the style that will be applied to the popper element to gets\n   * properly positioned.\n   *\n   * Note that this modifier will not touch the DOM, it just prepares the styles\n   * so that `applyStyle` modifier can apply it. This separation is useful\n   * in case you need to replace `applyStyle` with a custom implementation.\n   *\n   * This modifier has `850` as `order` value to maintain backward compatibility\n   * with previous versions of Popper.js. Expect the modifiers ordering method\n   * to change in future major versions of the library.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  computeStyle: {\n    /** @prop {number} order=850 - Index used to define the order of execution */\n    order: 850,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: computeStyle,\n    /**\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3D transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties\n     */\n    gpuAcceleration: true,\n    /**\n     * @prop {string} [x='bottom']\n     * Where to anchor the X axis (`bottom` or `top`). AKA X offset origin.\n     * Change this if your popper should grow in a direction different from `bottom`\n     */\n    x: 'bottom',\n    /**\n     * @prop {string} [x='left']\n     * Where to anchor the Y axis (`left` or `right`). AKA Y offset origin.\n     * Change this if your popper should grow in a direction different from `right`\n     */\n    y: 'right',\n  },\n\n  /**\n   * Applies the computed styles to the popper element.\n   *\n   * All the DOM manipulations are limited to this modifier. This is useful in case\n   * you want to integrate Popper.js inside a framework or view library and you\n   * want to delegate all the DOM manipulations to it.\n   *\n   * Note that if you disable this modifier, you must make sure the popper element\n   * has its position set to `absolute` before Popper.js can do its work!\n   *\n   * Just disable this modifier and define your own to achieve the desired effect.\n   *\n   * @memberof modifiers\n   * @inner\n   */\n  applyStyle: {\n    /** @prop {number} order=900 - Index used to define the order of execution */\n    order: 900,\n    /** @prop {Boolean} enabled=true - Whether the modifier is enabled or not */\n    enabled: true,\n    /** @prop {ModifierFn} */\n    fn: applyStyle,\n    /** @prop {Function} */\n    onLoad: applyStyleOnLoad,\n    /**\n     * @deprecated since version 1.10.0, the property moved to `computeStyle` modifier\n     * @prop {Boolean} gpuAcceleration=true\n     * If true, it uses the CSS 3D transformation to position the popper.\n     * Otherwise, it will use the `top` and `left` properties\n     */\n    gpuAcceleration: undefined,\n  },\n};\n\n/**\n * The `dataObject` is an object containing all the information used by Popper.js.\n * This object is passed to modifiers and to the `onCreate` and `onUpdate` callbacks.\n * @name dataObject\n * @property {Object} data.instance The Popper.js instance\n * @property {String} data.placement Placement applied to popper\n * @property {String} data.originalPlacement Placement originally defined on init\n * @property {Boolean} data.flipped True if popper has been flipped by flip modifier\n * @property {Boolean} data.hide True if the reference element is out of boundaries, useful to know when to hide the popper\n * @property {HTMLElement} data.arrowElement Node used as arrow by arrow modifier\n * @property {Object} data.styles Any CSS property defined here will be applied to the popper. It expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.arrowStyles Any CSS property defined here will be applied to the popper arrow. It expects the JavaScript nomenclature (eg. `marginBottom`)\n * @property {Object} data.boundaries Offsets of the popper boundaries\n * @property {Object} data.offsets The measurements of popper, reference and arrow elements\n * @property {Object} data.offsets.popper `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.reference `top`, `left`, `width`, `height` values\n * @property {Object} data.offsets.arrow] `top` and `left` offsets, only one of them will be different from 0\n */\n", "import modifiers from '../modifiers/index';\n\n/**\n * Default options provided to Popper.js constructor.<br />\n * These can be overridden using the `options` argument of Popper.js.<br />\n * To override an option, simply pass an object with the same\n * structure of the `options` object, as the 3rd argument. For example:\n * ```\n * new Popper(ref, pop, {\n *   modifiers: {\n *     preventOverflow: { enabled: false }\n *   }\n * })\n * ```\n * @type {Object}\n * @static\n * @memberof Popper\n */\nexport default {\n  /**\n   * Popper's placement.\n   * @prop {Popper.placements} placement='bottom'\n   */\n  placement: 'bottom',\n\n  /**\n   * Set this to true if you want popper to position it self in 'fixed' mode\n   * @prop {Boolean} positionFixed=false\n   */\n  positionFixed: false,\n\n  /**\n   * Whether events (resize, scroll) are initially enabled.\n   * @prop {Boolean} eventsEnabled=true\n   */\n  eventsEnabled: true,\n\n  /**\n   * Set to true if you want to automatically remove the popper when\n   * you call the `destroy` method.\n   * @prop {Boolean} removeOnDestroy=false\n   */\n  removeOnDestroy: false,\n\n  /**\n   * Callback called when the popper is created.<br />\n   * By default, it is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onCreate}\n   */\n  onCreate: () => {},\n\n  /**\n   * Callback called when the popper is updated. This callback is not called\n   * on the initialization/creation of the popper, but only on subsequent\n   * updates.<br />\n   * By default, it is set to no-op.<br />\n   * Access Popper.js instance with `data.instance`.\n   * @prop {onUpdate}\n   */\n  onUpdate: () => {},\n\n  /**\n   * List of modifiers used to modify the offsets before they are applied to the popper.\n   * They provide most of the functionalities of Popper.js.\n   * @prop {modifiers}\n   */\n  modifiers,\n};\n\n/**\n * @callback onCreate\n * @param {dataObject} data\n */\n\n/**\n * @callback onUpdate\n * @param {dataObject} data\n */\n", "// Utils\nimport debounce from './utils/debounce';\nimport isFunction from './utils/isFunction';\n\n// Methods\nimport update from './methods/update';\nimport destroy from './methods/destroy';\nimport enableEventListeners from './methods/enableEventListeners';\nimport disableEventListeners from './methods/disableEventListeners';\nimport Defaults from './methods/defaults';\nimport placements from './methods/placements';\n\nexport default class Popper {\n  /**\n   * Creates a new Popper.js instance.\n   * @class Popper\n   * @param {Element|referenceObject} reference - The reference element used to position the popper\n   * @param {Element} popper - The HTML / XML element used as the popper\n   * @param {Object} options - Your custom options to override the ones defined in [Defaults](#defaults)\n   * @return {Object} instance - The generated Popper.js instance\n   */\n  constructor(reference, popper, options = {}) {\n    // make update() debounced, so that it only runs at most once-per-tick\n    this.update = debounce(this.update.bind(this));\n\n    // with {} we create a new object with the options inside it\n    this.options = { ...Popper.Defaults, ...options };\n\n    // init state\n    this.state = {\n      isDestroyed: false,\n      isCreated: false,\n      scrollParents: [],\n    };\n\n    // get reference and popper elements (allow jQuery wrappers)\n    this.reference = reference && reference.jquery ? reference[0] : reference;\n    this.popper = popper && popper.jquery ? popper[0] : popper;\n\n    // Deep merge modifiers options\n    this.options.modifiers = {};\n    Object.keys({\n      ...Popper.Defaults.modifiers,\n      ...options.modifiers,\n    }).forEach(name => {\n      this.options.modifiers[name] = {\n        // If it's a built-in modifier, use it as base\n        ...(Popper.Defaults.modifiers[name] || {}),\n        // If there are custom options, override and merge with default ones\n        ...(options.modifiers ? options.modifiers[name] : {}),\n      };\n    });\n\n    // Refactoring modifiers' list (Object => Array)\n    this.modifiers = Object.keys(this.options.modifiers)\n      .map(name => ({\n        name,\n        ...this.options.modifiers[name],\n      }))\n      // sort the modifiers by order\n      .sort((a, b) => a.order - b.order);\n\n    // modifiers have the ability to execute arbitrary code when Popper.js get inited\n    // such code is executed in the same order of its modifier\n    // they could add new properties to their options configuration\n    // BE AWARE: don't add options to `options.modifiers.name` but to `modifierOptions`!\n    this.modifiers.forEach(modifierOptions => {\n      if (modifierOptions.enabled && isFunction(modifierOptions.onLoad)) {\n        modifierOptions.onLoad(\n          this.reference,\n          this.popper,\n          this.options,\n          modifierOptions,\n          this.state\n        );\n      }\n    });\n\n    // fire the first update to position the popper in the right place\n    this.update();\n\n    const eventsEnabled = this.options.eventsEnabled;\n    if (eventsEnabled) {\n      // setup event listeners, they will take care of update the position in specific situations\n      this.enableEventListeners();\n    }\n\n    this.state.eventsEnabled = eventsEnabled;\n  }\n\n  // We can't use class properties because they don't get listed in the\n  // class prototype and break stuff like Sinon stubs\n  update() {\n    return update.call(this);\n  }\n  destroy() {\n    return destroy.call(this);\n  }\n  enableEventListeners() {\n    return enableEventListeners.call(this);\n  }\n  disableEventListeners() {\n    return disableEventListeners.call(this);\n  }\n\n  /**\n   * Schedules an update. It will run on the next UI update available.\n   * @method scheduleUpdate\n   * @memberof Popper\n   */\n  scheduleUpdate = () => requestAnimationFrame(this.update);\n\n  /**\n   * Collection of utilities useful when writing custom modifiers.\n   * Starting from version 1.7, this method is available only if you\n   * include `popper-utils.js` before `popper.js`.\n   *\n   * **DEPRECATION**: This way to access PopperUtils is deprecated\n   * and will be removed in v2! Use the PopperUtils module directly instead.\n   * Due to the high instability of the methods contained in Utils, we can't\n   * guarantee them to follow semver. Use them at your own risk!\n   * @static\n   * @private\n   * @type {Object}\n   * @deprecated since version 1.8\n   * @member Utils\n   * @memberof Popper\n   */\n  static Utils = (typeof window !== 'undefined' ? window : global).PopperUtils;\n\n  static placements = placements;\n\n  static Defaults = Defaults;\n}\n\n/**\n * The `referenceObject` is an object that provides an interface compatible with Popper.js\n * and lets you use it as replacement of a real DOM node.<br />\n * You can use this method to position a popper relatively to a set of coordinates\n * in case you don't have a DOM node to use as reference.\n *\n * ```\n * new Popper(referenceObject, popperNode);\n * ```\n *\n * NB: This feature isn't supported in Internet Explorer 10.\n * @name referenceObject\n * @property {Function} data.getBoundingClientRect\n * A function that returns a set of coordinates compatible with the native `getBoundingClientRect` method.\n * @property {number} data.clientWidth\n * An ES6 getter that will return the width of the virtual reference element.\n * @property {number} data.clientHeight\n * An ES6 getter that will return the height of the virtual reference element.\n */\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,SAAA,GAAe,OAAOC,MAAP,KAAkB,WAAlB,IAAiC,OAAOC,QAAP,KAAoB,WAArD,IAAoE,OAAOC,SAAP,KAAqB,WAAxG;ACEA,IAAMC,eAAA,GAAmB,YAAU;MAC3BC,qBAAA,GAAwB,CAAC,MAAD,EAAS,SAAT,EAAoB,SAApB,CAA9B;OACK,IAAIC,CAAA,GAAI,CAAb,EAAgBA,CAAA,GAAID,qBAAA,CAAsBE,MAA1C,EAAkDD,CAAA,IAAK,CAAvD,EAA0D;QACpDN,SAAA,IAAaG,SAAA,CAAUK,SAAV,CAAoBC,OAApB,CAA4BJ,qBAAA,CAAsBC,CAAtB,CAA5B,KAAyD,CAA1E,EAA6E;aACpE,CAAP;;;SAGG,CAAP;CAPuB,EAAzB;AAUA,SAAgBI,iBAATA,CAA2BC,EAA3B,EAA+B;MAChCC,MAAA,GAAS,KAAb;SACO,YAAM;QACPA,MAAJ,EAAY;;;aAGH,IAAT;WACOC,OAAP,CAAeC,OAAf,GAAyBC,IAAzB,CAA8B,YAAM;eACzB,KAAT;;KADF;GALF;;AAYF,SAAgBC,YAATA,CAAsBL,EAAtB,EAA0B;MAC3BM,SAAA,GAAY,KAAhB;SACO,YAAM;QACP,CAACA,SAAL,EAAgB;kBACF,IAAZ;iBACW,YAAM;oBACH,KAAZ;;OADF,EAGGb,eAHH;;GAHJ;;AAWF,IAAMc,kBAAA,GAAqBlB,SAAA,IAAaC,MAAA,CAAOY,OAA/C;;;;;;;;;;;AAYA,IAAAM,QAAA,GAAgBD,kBAAA,GACZR,iBADY,GAEZM,YAFJ;;ACnDA;;;;;;;AAOA,SAAwBI,UAATA,CAAoBC,eAApB,EAAqC;MAC5CC,OAAA,GAAU,EAAhB;SAEED,eAAA,IACAC,OAAA,CAAQC,QAAR,CAAiBC,IAAjB,CAAsBH,eAAtB,MAA2C,mBAF7C;;;ACTF;;;;;;;AAOA,SAAwBI,wBAATA,CAAkCC,OAAlC,EAA2CC,QAA3C,EAAqD;MAC9DD,OAAA,CAAQE,QAAR,KAAqB,CAAzB,EAA4B;WACnB,EAAP;;;MAGI3B,MAAA,GAASyB,OAAA,CAAQG,aAAR,CAAsBC,WAArC;MACMC,GAAA,GAAM9B,MAAA,CAAO+B,gBAAP,CAAwBN,OAAxB,EAAiC,IAAjC,CAAZ;SACOC,QAAA,GAAWI,GAAA,CAAIJ,QAAJ,CAAX,GAA2BI,GAAlC;;;ACdF;;;;;;;AAOA,SAAwBE,aAATA,CAAuBP,OAAvB,EAAgC;MACzCA,OAAA,CAAQQ,QAAR,KAAqB,MAAzB,EAAiC;WACxBR,OAAP;;SAEKA,OAAA,CAAQS,UAAR,IAAsBT,OAAA,CAAQU,IAArC;;;ACRF;;;;;;;AAOA,SAAwBC,eAATA,CAAyBX,OAAzB,EAAkC;;MAE3C,CAACA,OAAL,EAAc;WACLxB,QAAA,CAASoC,IAAhB;;UAGMZ,OAAA,CAAQQ,QAAhB;SACO,MAAL;SACK,MAAL;aACSR,OAAA,CAAQG,aAAR,CAAsBS,IAA7B;SACG,WAAL;aACSZ,OAAA,CAAQY,IAAf;;;;;8BAIuCb,wBAAA,CAAyBC,OAAzB,CAfI;IAevCa,QAfuC,GAAAC,qBAAA,CAevCD,QAfuC;IAe7BE,SAf6B,GAAAD,qBAAA,CAe7BC,SAf6B;IAelBC,SAfkB,GAAAF,qBAAA,CAelBE,SAfkB;MAgB3C,wBAAwBC,IAAxB,CAA6BJ,QAAA,GAAWG,SAAX,GAAuBD,SAApD,CAAJ,EAAoE;WAC3Df,OAAP;;SAGKW,eAAA,CAAgBJ,aAAA,CAAcP,OAAd,CAAhB,CAAP;;;AC9BF;;;;;;;AAOA,SAAwBkB,gBAATA,CAA0BC,SAA1B,EAAqC;SAC3CA,SAAA,IAAaA,SAAA,CAAUC,aAAvB,GAAuCD,SAAA,CAAUC,aAAjD,GAAiED,SAAxE;;ACNF,IAAME,MAAA,GAAS/C,SAAA,IAAa,CAAC,EAAEC,MAAA,CAAO+C,oBAAP,IAA+B9C,QAAA,CAAS+C,YAA1C,CAA7B;AACA,IAAMC,MAAA,GAASlD,SAAA,IAAa,UAAU2C,IAAV,CAAexC,SAAA,CAAUK,SAAzB,CAA5B;;;;;;;;;AASA,SAAwB2C,IAATA,CAAcC,OAAd,EAAuB;MAChCA,OAAA,KAAY,EAAhB,EAAoB;WACXL,MAAP;;MAEEK,OAAA,KAAY,EAAhB,EAAoB;WACXF,MAAP;;SAEKH,MAAA,IAAUG,MAAjB;;;ACjBF;;;;;;;AAOA,SAAwBG,eAATA,CAAyB3B,OAAzB,EAAkC;MAC3C,CAACA,OAAL,EAAc;WACLxB,QAAA,CAASoD,eAAhB;;MAGIC,cAAA,GAAiBJ,IAAA,CAAK,EAAL,IAAWjD,QAAA,CAASoC,IAApB,GAA2B,IAAlD;;;MAGIkB,YAAA,GAAe9B,OAAA,CAAQ8B,YAAR,IAAwB,IAA3C;;SAEOA,YAAA,KAAiBD,cAAjB,IAAmC7B,OAAA,CAAQ+B,kBAAlD,EAAsE;mBACrD,CAAC/B,OAAA,GAAUA,OAAA,CAAQ+B,kBAAnB,EAAuCD,YAAtD;;MAGItB,QAAA,GAAWsB,YAAA,IAAgBA,YAAA,CAAatB,QAA9C;MAEI,CAACA,QAAD,IAAaA,QAAA,KAAa,MAA1B,IAAoCA,QAAA,KAAa,MAArD,EAA6D;WACpDR,OAAA,GAAUA,OAAA,CAAQG,aAAR,CAAsByB,eAAhC,GAAkDpD,QAAA,CAASoD,eAAlE;;;;;MAMA,CAAC,IAAD,EAAO,IAAP,EAAa,OAAb,EAAsB7C,OAAtB,CAA8B+C,YAAA,CAAatB,QAA3C,MAAyD,CAAC,CAA1D,IACAT,wBAAA,CAAyB+B,YAAzB,EAAuC,UAAvC,MAAuD,QAFzD,EAGE;WACOH,eAAA,CAAgBG,YAAhB,CAAP;;SAGKA,YAAP;;ACpCa,SAASE,iBAATA,CAA2BhC,OAA3B,EAAoC;MACzCQ,QADyC,GAC5BR,OAD4B,CACzCQ,QADyC;MAE7CA,QAAA,KAAa,MAAjB,EAAyB;WAChB,KAAP;;SAGAA,QAAA,KAAa,MAAb,IAAuBmB,eAAA,CAAgB3B,OAAA,CAAQiC,iBAAxB,MAA+CjC,OADxE;;;ACPF;;;;;;;AAOA,SAAwBkC,OAATA,CAAiBC,IAAjB,EAAuB;MAChCA,IAAA,CAAK1B,UAAL,KAAoB,IAAxB,EAA8B;WACrByB,OAAA,CAAQC,IAAA,CAAK1B,UAAb,CAAP;;SAGK0B,IAAP;;;ACRF;;;;;;;;AAQA,SAAwBC,sBAATA,CAAgCC,QAAhC,EAA0CC,QAA1C,EAAoD;;MAE7D,CAACD,QAAD,IAAa,CAACA,QAAA,CAASnC,QAAvB,IAAmC,CAACoC,QAApC,IAAgD,CAACA,QAAA,CAASpC,QAA9D,EAAwE;WAC/D1B,QAAA,CAASoD,eAAhB;;;;MAIIW,KAAA,GACJF,QAAA,CAASG,uBAAT,CAAiCF,QAAjC,IACAG,IAAA,CAAKC,2BAFP;MAGMC,KAAA,GAAQJ,KAAA,GAAQF,QAAR,GAAmBC,QAAjC;MACMM,GAAA,GAAML,KAAA,GAAQD,QAAR,GAAmBD,QAA/B;;;MAGMQ,KAAA,GAAQrE,QAAA,CAASsE,WAAT,EAAd;QACMC,QAAN,CAAeJ,KAAf,EAAsB,CAAtB;QACMK,MAAN,CAAaJ,GAAb,EAAkB,CAAlB;MACQK,uBAjByD,GAiB7BJ,KAjB6B,CAiBzDI,uBAjByD;;;;MAqB9DZ,QAAA,KAAaY,uBAAb,IACCX,QAAA,KAAaW,uBADf,IAEAN,KAAA,CAAMO,QAAN,CAAeN,GAAf,CAHF,EAIE;QACIZ,iBAAA,CAAkBiB,uBAAlB,CAAJ,EAAgD;aACvCA,uBAAP;;WAGKtB,eAAA,CAAgBsB,uBAAhB,CAAP;;;;MAIIE,YAAA,GAAejB,OAAA,CAAQG,QAAR,CAArB;MACIc,YAAA,CAAazC,IAAjB,EAAuB;WACd0B,sBAAA,CAAuBe,YAAA,CAAazC,IAApC,EAA0C4B,QAA1C,CAAP;GADF,MAEO;WACEF,sBAAA,CAAuBC,QAAvB,EAAiCH,OAAA,CAAQI,QAAR,EAAkB5B,IAAnD,CAAP;;;;ACjDJ;;;;;;;;AAQA,SAAwB0C,SAATA,CAAmBpD,OAAnB,EAA0C;MAAdqD,IAAc,GAAAC,SAAA,CAAAzE,MAAA,QAAAyE,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAP,KAAO;MACjDE,SAAA,GAAYH,IAAA,KAAS,KAAT,GAAiB,WAAjB,GAA+B,YAAjD;MACM7C,QAAA,GAAWR,OAAA,CAAQQ,QAAzB;MAEIA,QAAA,KAAa,MAAb,IAAuBA,QAAA,KAAa,MAAxC,EAAgD;QACxCiD,IAAA,GAAOzD,OAAA,CAAQG,aAAR,CAAsByB,eAAnC;QACM8B,gBAAA,GAAmB1D,OAAA,CAAQG,aAAR,CAAsBuD,gBAAtB,IAA0CD,IAAnE;WACOC,gBAAA,CAAiBF,SAAjB,CAAP;;SAGKxD,OAAA,CAAQwD,SAAR,CAAP;;;AChBF;;;;;;;;;AASA,SAAwBG,aAATA,CAAuBC,IAAvB,EAA6B5D,OAA7B,EAAwD;MAAlB6D,QAAkB,GAAAP,SAAA,CAAAzE,MAAA,QAAAyE,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAP,KAAO;MAC/DQ,SAAA,GAAYV,SAAA,CAAUpD,OAAV,EAAmB,KAAnB,CAAlB;MACM+D,UAAA,GAAaX,SAAA,CAAUpD,OAAV,EAAmB,MAAnB,CAAnB;MACMgE,QAAA,GAAWH,QAAA,GAAW,CAAC,CAAZ,GAAgB,CAAjC;OACKI,GAAL,IAAYH,SAAA,GAAYE,QAAxB;OACKE,MAAL,IAAeJ,SAAA,GAAYE,QAA3B;OACKG,IAAL,IAAaJ,UAAA,GAAaC,QAA1B;OACKI,KAAL,IAAcL,UAAA,GAAaC,QAA3B;SACOJ,IAAP;;;ACnBF;;;;;;;;;;AAUA,SAAwBS,cAATA,CAAwBC,MAAxB,EAAgCC,IAAhC,EAAsC;MAC7CC,KAAA,GAAQD,IAAA,KAAS,GAAT,GAAe,MAAf,GAAwB,KAAtC;MACME,KAAA,GAAQD,KAAA,KAAU,MAAV,GAAmB,OAAnB,GAA6B,QAA3C;SAGEE,UAAA,CAAWJ,MAAA,YAAgBE,KAAhB,WAAX,IACAE,UAAA,CAAWJ,MAAA,YAAgBG,KAAhB,WAAX,CAFF;;ACZF,SAASE,OAATA,CAAiBJ,IAAjB,EAAuB3D,IAAvB,EAA6B6C,IAA7B,EAAmCmB,aAAnC,EAAkD;SACzCC,IAAA,CAAKC,GAAL,CACLlE,IAAA,YAAc2D,IAAd,CADK,EAEL3D,IAAA,YAAc2D,IAAd,CAFK,EAGLd,IAAA,YAAcc,IAAd,CAHK,EAILd,IAAA,YAAcc,IAAd,CAJK,EAKLd,IAAA,YAAcc,IAAd,CALK,EAML9C,IAAA,CAAK,EAAL,IACKsD,QAAA,CAAStB,IAAA,YAAcc,IAAd,CAAT,IACHQ,QAAA,CAASH,aAAA,aAAuBL,IAAA,KAAS,QAAT,GAAoB,KAApB,GAA4B,MAAnD,EAAT,CADG,GAEHQ,QAAA,CAASH,aAAA,aAAuBL,IAAA,KAAS,QAAT,GAAoB,QAApB,GAA+B,OAAtD,EAAT,CAHF,GAIE,CAVG,CAAP;;AAcF,SAAwBS,cAATA,CAAwBxG,QAAxB,EAAkC;MACzCoC,IAAA,GAAOpC,QAAA,CAASoC,IAAtB;MACM6C,IAAA,GAAOjF,QAAA,CAASoD,eAAtB;MACMgD,aAAA,GAAgBnD,IAAA,CAAK,EAAL,KAAYnB,gBAAA,CAAiBmD,IAAjB,CAAlC;SAEO;YACGkB,OAAA,CAAQ,QAAR,EAAkB/D,IAAlB,EAAwB6C,IAAxB,EAA8BmB,aAA9B,CADH;WAEED,OAAA,CAAQ,OAAR,EAAiB/D,IAAjB,EAAuB6C,IAAvB,EAA6BmB,aAA7B;GAFT;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACtBF;;;;;;;AAOA,SAAwBK,aAATA,CAAuBC,OAAvB,EAAgC;sBAExCA,OADL;WAESA,OAAA,CAAQf,IAAR,GAAee,OAAA,CAAQC,KAFhC;YAGUD,OAAA,CAAQjB,GAAR,GAAciB,OAAA,CAAQE;;;;ACJlC;;;;;;;AAOA,SAAwBC,qBAATA,CAA+BrF,OAA/B,EAAwC;MACjD4D,IAAA,GAAO,EAAX;;;;;MAKI;QACEnC,IAAA,CAAK,EAAL,CAAJ,EAAc;aACLzB,OAAA,CAAQqF,qBAAR,EAAP;UACMvB,SAAA,GAAYV,SAAA,CAAUpD,OAAV,EAAmB,KAAnB,CAAlB;UACM+D,UAAA,GAAaX,SAAA,CAAUpD,OAAV,EAAmB,MAAnB,CAAnB;WACKiE,GAAL,IAAYH,SAAZ;WACKK,IAAL,IAAaJ,UAAb;WACKG,MAAL,IAAeJ,SAAf;WACKM,KAAL,IAAcL,UAAd;KAPF,MASK;aACI/D,OAAA,CAAQqF,qBAAR,EAAP;;GAXJ,CAcA,OAAMC,CAAN,EAAQ;MAEFC,MAAA,GAAS;UACP3B,IAAA,CAAKO,IADE;SAERP,IAAA,CAAKK,GAFG;WAGNL,IAAA,CAAKQ,KAAL,GAAaR,IAAA,CAAKO,IAHZ;YAILP,IAAA,CAAKM,MAAL,GAAcN,IAAA,CAAKK;GAJ7B;;;MAQMuB,KAAA,GAAQxF,OAAA,CAAQQ,QAAR,KAAqB,MAArB,GAA8BwE,cAAA,CAAehF,OAAA,CAAQG,aAAvB,CAA9B,GAAsE,EAApF;MACMgF,KAAA,GACJK,KAAA,CAAML,KAAN,IAAenF,OAAA,CAAQyF,WAAvB,IAAsCF,MAAA,CAAOJ,KAD/C;MAEMC,MAAA,GACJI,KAAA,CAAMJ,MAAN,IAAgBpF,OAAA,CAAQ0F,YAAxB,IAAwCH,MAAA,CAAOH,MADjD;MAGIO,cAAA,GAAiB3F,OAAA,CAAQ4F,WAAR,GAAsBT,KAA3C;MACIU,aAAA,GAAgB7F,OAAA,CAAQ8F,YAAR,GAAuBV,MAA3C;;;;MAIIO,cAAA,IAAkBE,aAAtB,EAAqC;QAC7BvB,MAAA,GAASvE,wBAAA,CAAyBC,OAAzB,CAAf;sBACkBqE,cAAA,CAAeC,MAAf,EAAuB,GAAvB,CAAlB;qBACiBD,cAAA,CAAeC,MAAf,EAAuB,GAAvB,CAAjB;WAEOa,KAAP,IAAgBQ,cAAhB;WACOP,MAAP,IAAiBS,aAAjB;;SAGKZ,aAAA,CAAcM,MAAd,CAAP;;ACzDa,SAASQ,oCAATA,CAA8CC,QAA9C,EAAwDC,MAAxD,EAAuF;MAAvBC,aAAuB,GAAA5C,SAAA,CAAAzE,MAAA,QAAAyE,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAP,KAAO;MAC9F9B,MAAA,GAASC,IAAA,CAAQ,EAAR,CAAf;MACM0E,MAAA,GAASF,MAAA,CAAOzF,QAAP,KAAoB,MAAnC;MACM4F,YAAA,GAAef,qBAAA,CAAsBW,QAAtB,CAArB;MACMK,UAAA,GAAahB,qBAAA,CAAsBY,MAAtB,CAAnB;MACMK,YAAA,GAAe3F,eAAA,CAAgBqF,QAAhB,CAArB;MAEM1B,MAAA,GAASvE,wBAAA,CAAyBkG,MAAzB,CAAf;MACMM,cAAA,GAAiB7B,UAAA,CAAWJ,MAAA,CAAOiC,cAAlB,CAAvB;MACMC,eAAA,GAAkB9B,UAAA,CAAWJ,MAAA,CAAOkC,eAAlB,CAAxB;;;MAGGN,aAAA,IAAiBC,MAApB,EAA4B;eACflC,GAAX,GAAiBY,IAAA,CAAKC,GAAL,CAASuB,UAAA,CAAWpC,GAApB,EAAyB,CAAzB,CAAjB;eACWE,IAAX,GAAkBU,IAAA,CAAKC,GAAL,CAASuB,UAAA,CAAWlC,IAApB,EAA0B,CAA1B,CAAlB;;MAEEe,OAAA,GAAUD,aAAA,CAAc;SACrBmB,YAAA,CAAanC,GAAb,GAAmBoC,UAAA,CAAWpC,GAA9B,GAAoCsC,cADf;UAEpBH,YAAA,CAAajC,IAAb,GAAoBkC,UAAA,CAAWlC,IAA/B,GAAsCqC,eAFlB;WAGnBJ,YAAA,CAAajB,KAHM;YAIlBiB,YAAA,CAAahB;GAJT,CAAd;UAMQqB,SAAR,GAAoB,CAApB;UACQC,UAAR,GAAqB,CAArB;;;;;;MAMI,CAAClF,MAAD,IAAW2E,MAAf,EAAuB;QACfM,SAAA,GAAY/B,UAAA,CAAWJ,MAAA,CAAOmC,SAAlB,CAAlB;QACMC,UAAA,GAAahC,UAAA,CAAWJ,MAAA,CAAOoC,UAAlB,CAAnB;YAEQzC,GAAR,IAAesC,cAAA,GAAiBE,SAAhC;YACQvC,MAAR,IAAkBqC,cAAA,GAAiBE,SAAnC;YACQtC,IAAR,IAAgBqC,eAAA,GAAkBE,UAAlC;YACQtC,KAAR,IAAiBoC,eAAA,GAAkBE,UAAnC;;;YAGQD,SAAR,GAAoBA,SAApB;YACQC,UAAR,GAAqBA,UAArB;;MAIAlF,MAAA,IAAU,CAAC0E,aAAX,GACID,MAAA,CAAO/C,QAAP,CAAgBoD,YAAhB,CADJ,GAEIL,MAAA,KAAWK,YAAX,IAA2BA,YAAA,CAAa9F,QAAb,KAA0B,MAH3D,EAIE;cACUmD,aAAA,CAAcuB,OAAd,EAAuBe,MAAvB,CAAV;;SAGKf,OAAP;;ACtDa,SAASyB,6CAATA,CAAuD3G,OAAvD,EAAuF;MAAvB4G,aAAuB,GAAAtD,SAAA,CAAAzE,MAAA,QAAAyE,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAP,KAAO;MAC9FG,IAAA,GAAOzD,OAAA,CAAQG,aAAR,CAAsByB,eAAnC;MACMiF,cAAA,GAAiBd,oCAAA,CAAqC/F,OAArC,EAA8CyD,IAA9C,CAAvB;MACM0B,KAAA,GAAQN,IAAA,CAAKC,GAAL,CAASrB,IAAA,CAAKgC,WAAd,EAA2BlH,MAAA,CAAOuI,UAAP,IAAqB,CAAhD,CAAd;MACM1B,MAAA,GAASP,IAAA,CAAKC,GAAL,CAASrB,IAAA,CAAKiC,YAAd,EAA4BnH,MAAA,CAAOwI,WAAP,IAAsB,CAAlD,CAAf;MAEMjD,SAAA,GAAY,CAAC8C,aAAD,GAAiBxD,SAAA,CAAUK,IAAV,CAAjB,GAAmC,CAArD;MACMM,UAAA,GAAa,CAAC6C,aAAD,GAAiBxD,SAAA,CAAUK,IAAV,EAAgB,MAAhB,CAAjB,GAA2C,CAA9D;MAEMuD,MAAA,GAAS;SACRlD,SAAA,GAAY+C,cAAA,CAAe5C,GAA3B,GAAiC4C,cAAA,CAAeJ,SADxC;UAEP1C,UAAA,GAAa8C,cAAA,CAAe1C,IAA5B,GAAmC0C,cAAA,CAAeH,UAF3C;gBAAA;;GAAf;SAOOzB,aAAA,CAAc+B,MAAd,CAAP;;;ACjBF;;;;;;;;AAQA,SAAwBC,OAATA,CAAiBjH,OAAjB,EAA0B;MACjCQ,QAAA,GAAWR,OAAA,CAAQQ,QAAzB;MACIA,QAAA,KAAa,MAAb,IAAuBA,QAAA,KAAa,MAAxC,EAAgD;WACvC,KAAP;;MAEET,wBAAA,CAAyBC,OAAzB,EAAkC,UAAlC,MAAkD,OAAtD,EAA+D;WACtD,IAAP;;MAEIS,UAAA,GAAaF,aAAA,CAAcP,OAAd,CAAnB;MACI,CAACS,UAAL,EAAiB;WACR,KAAP;;SAEKwG,OAAA,CAAQxG,UAAR,CAAP;;;ACrBF;;;;;;;;AAQA,SAAwByG,4BAATA,CAAsClH,OAAtC,EAA+C;;MAEvD,CAACA,OAAD,IAAY,CAACA,OAAA,CAAQmH,aAArB,IAAsC1F,IAAA,EAA1C,EAAkD;WAC1CjD,QAAA,CAASoD,eAAhB;;MAEEwF,EAAA,GAAKpH,OAAA,CAAQmH,aAAjB;SACOC,EAAA,IAAMrH,wBAAA,CAAyBqH,EAAzB,EAA6B,WAA7B,MAA8C,MAA3D,EAAmE;SAC5DA,EAAA,CAAGD,aAAR;;SAEKC,EAAA,IAAM5I,QAAA,CAASoD,eAAtB;;;ACTF;;;;;;;;;;;AAWA,SAAwByF,aAATA,CACbC,MADa,EAEbnG,SAFa,EAGboG,OAHa,EAIbC,iBAJa,EAMb;MADAtB,aACA,GAAA5C,SAAA,CAAAzE,MAAA,QAAAyE,SAAA,QAAAC,SAAA,GAAAD,SAAA,MADgB,KAChB;;;;MAGImE,UAAA,GAAa;IAAExD,GAAA,EAAK,CAAP;IAAUE,IAAA,EAAM;EAAhB,CAAjB;MACMrC,YAAA,GAAeoE,aAAA,GAAgBgB,4BAAA,CAA6BI,MAA7B,CAAhB,GAAuDlF,sBAAA,CAAuBkF,MAAvB,EAA+BpG,gBAAA,CAAiBC,SAAjB,CAA/B,CAA5E;;;MAGIqG,iBAAA,KAAsB,UAA1B,EAAuC;iBACxBb,6CAAA,CAA8C7E,YAA9C,EAA4DoE,aAA5D,CAAb;GADF,MAIK;;QAECwB,cAAA,SAAJ;QACIF,iBAAA,KAAsB,cAA1B,EAA0C;uBACvB7G,eAAA,CAAgBJ,aAAA,CAAcY,SAAd,CAAhB,CAAjB;UACIuG,cAAA,CAAelH,QAAf,KAA4B,MAAhC,EAAwC;yBACrB8G,MAAA,CAAOnH,aAAP,CAAqByB,eAAtC;;KAHJ,MAKO,IAAI4F,iBAAA,KAAsB,QAA1B,EAAoC;uBACxBF,MAAA,CAAOnH,aAAP,CAAqByB,eAAtC;KADK,MAEA;uBACY4F,iBAAjB;;QAGItC,OAAA,GAAUa,oCAAA,CACd2B,cADc,EAEd5F,YAFc,EAGdoE,aAHc,CAAhB;;;QAOIwB,cAAA,CAAelH,QAAf,KAA4B,MAA5B,IAAsC,CAACyG,OAAA,CAAQnF,YAAR,CAA3C,EAAkE;4BACtCkD,cAAA,CAAesC,MAAA,CAAOnH,aAAtB,CADsC;QACxDiF,MADwD,GAAAuC,eAAA,CACxDvC,MADwD;QAChDD,KADgD,GAAAwC,eAAA,CAChDxC,KADgD;iBAErDlB,GAAX,IAAkBiB,OAAA,CAAQjB,GAAR,GAAciB,OAAA,CAAQuB,SAAxC;iBACWvC,MAAX,GAAoBkB,MAAA,GAASF,OAAA,CAAQjB,GAArC;iBACWE,IAAX,IAAmBe,OAAA,CAAQf,IAAR,GAAee,OAAA,CAAQwB,UAA1C;iBACWtC,KAAX,GAAmBe,KAAA,GAAQD,OAAA,CAAQf,IAAnC;KALF,MAMO;;mBAEQe,OAAb;;;;;YAKMqC,OAAA,IAAW,CAArB;MACMK,eAAA,GAAkB,OAAOL,OAAP,KAAmB,QAA3C;aACWpD,IAAX,IAAmByD,eAAA,GAAkBL,OAAlB,GAA4BA,OAAA,CAAQpD,IAAR,IAAgB,CAA/D;aACWF,GAAX,IAAkB2D,eAAA,GAAkBL,OAAlB,GAA4BA,OAAA,CAAQtD,GAAR,IAAe,CAA7D;aACWG,KAAX,IAAoBwD,eAAA,GAAkBL,OAAlB,GAA4BA,OAAA,CAAQnD,KAAR,IAAiB,CAAjE;aACWF,MAAX,IAAqB0D,eAAA,GAAkBL,OAAlB,GAA4BA,OAAA,CAAQrD,MAAR,IAAkB,CAAnE;SAEOuD,UAAP;;AC7EF,SAASI,OAATA,CAAAC,IAAA,EAAoC;MAAjB3C,KAAiB,GAAA2C,IAAA,CAAjB3C,KAAiB;IAAVC,MAAU,GAAA0C,IAAA,CAAV1C,MAAU;SAC3BD,KAAA,GAAQC,MAAf;;;;;;;;;;;;AAYF,SAAwB2C,oBAATA,CACbC,SADa,EAEbC,OAFa,EAGbX,MAHa,EAIbnG,SAJa,EAKbqG,iBALa,EAOb;MADAD,OACA,GAAAjE,SAAA,CAAAzE,MAAA,QAAAyE,SAAA,QAAAC,SAAA,GAAAD,SAAA,MADU,CACV;MACI0E,SAAA,CAAUjJ,OAAV,CAAkB,MAAlB,MAA8B,CAAC,CAAnC,EAAsC;WAC7BiJ,SAAP;;MAGIP,UAAA,GAAaJ,aAAA,CACjBC,MADiB,EAEjBnG,SAFiB,EAGjBoG,OAHiB,EAIjBC,iBAJiB,CAAnB;MAOMU,KAAA,GAAQ;SACP;aACIT,UAAA,CAAWtC,KADf;cAEK8C,OAAA,CAAQhE,GAAR,GAAcwD,UAAA,CAAWxD;KAHvB;WAKL;aACEwD,UAAA,CAAWrD,KAAX,GAAmB6D,OAAA,CAAQ7D,KAD7B;cAEGqD,UAAA,CAAWrC;KAPT;YASJ;aACCqC,UAAA,CAAWtC,KADZ;cAEEsC,UAAA,CAAWvD,MAAX,GAAoB+D,OAAA,CAAQ/D;KAX1B;UAaN;aACG+D,OAAA,CAAQ9D,IAAR,GAAesD,UAAA,CAAWtD,IAD7B;cAEIsD,UAAA,CAAWrC;;GAfvB;MAmBM+C,WAAA,GAAcC,MAAA,CAAOC,IAAP,CAAYH,KAAZ,EACjBI,GADiB,CACb,UAAAC,GAAA;;;OAEAL,KAAA,CAAMK,GAAN,CAFA;YAGGV,OAAA,CAAQK,KAAA,CAAMK,GAAN,CAAR;;GAJU,EAMjBC,IANiB,CAMZ,UAACC,CAAD,EAAIC,CAAJ;WAAUA,CAAA,CAAEC,IAAF,GAASF,CAAA,CAAEE,IAArB;GANY,CAApB;MAQMC,aAAA,GAAgBT,WAAA,CAAYU,MAAZ,CACpB,UAAAC,KAAA;QAAG3D,KAAH,GAAA2D,KAAA,CAAG3D,KAAH;MAAUC,MAAV,GAAA0D,KAAA,CAAU1D,MAAV;WACED,KAAA,IAASmC,MAAA,CAAO7B,WAAhB,IAA+BL,MAAA,IAAUkC,MAAA,CAAO5B,YADlD;GADoB,CAAtB;MAKMqD,iBAAA,GAAoBH,aAAA,CAAc/J,MAAd,GAAuB,CAAvB,GACtB+J,aAAA,CAAc,CAAd,EAAiBL,GADK,GAEtBJ,WAAA,CAAY,CAAZ,EAAeI,GAFnB;MAIMS,SAAA,GAAYhB,SAAA,CAAUiB,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAlB;SAEOF,iBAAA,IAAqBC,SAAA,SAAgBA,SAAhB,GAA8B,EAAnD,CAAP;;;ACnEF;;;;;;;;;;AAUA,SAAwBE,mBAATA,CAA6BC,KAA7B,EAAoC7B,MAApC,EAA4CnG,SAA5C,EAA6E;MAAtB+E,aAAsB,GAAA5C,SAAA,CAAAzE,MAAA,QAAAyE,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAN,IAAM;MACpF8F,kBAAA,GAAqBlD,aAAA,GAAgBgB,4BAAA,CAA6BI,MAA7B,CAAhB,GAAuDlF,sBAAA,CAAuBkF,MAAvB,EAA+BpG,gBAAA,CAAiBC,SAAjB,CAA/B,CAAlF;SACO4E,oCAAA,CAAqC5E,SAArC,EAAgDiI,kBAAhD,EAAoElD,aAApE,CAAP;;;ACjBF;;;;;;;AAOA,SAAwBmD,aAATA,CAAuBrJ,OAAvB,EAAgC;MACvCzB,MAAA,GAASyB,OAAA,CAAQG,aAAR,CAAsBC,WAArC;MACMkE,MAAA,GAAS/F,MAAA,CAAO+B,gBAAP,CAAwBN,OAAxB,CAAf;MACMsJ,CAAA,GAAI5E,UAAA,CAAWJ,MAAA,CAAOmC,SAAP,IAAoB,CAA/B,IAAoC/B,UAAA,CAAWJ,MAAA,CAAOiF,YAAP,IAAuB,CAAlC,CAA9C;MACMC,CAAA,GAAI9E,UAAA,CAAWJ,MAAA,CAAOoC,UAAP,IAAqB,CAAhC,IAAqChC,UAAA,CAAWJ,MAAA,CAAOmF,WAAP,IAAsB,CAAjC,CAA/C;MACMlE,MAAA,GAAS;WACNvF,OAAA,CAAQ4F,WAAR,GAAsB4D,CADhB;YAELxJ,OAAA,CAAQ8F,YAAR,GAAuBwD;GAFjC;SAIO/D,MAAP;;;AChBF;;;;;;;AAOA,SAAwBmE,oBAATA,CAA8B1B,SAA9B,EAAyC;MAChD2B,IAAA,GAAO;IAAExF,IAAA,EAAM,OAAR;IAAiBC,KAAA,EAAO,MAAxB;IAAgCF,MAAA,EAAQ,KAAxC;IAA+CD,GAAA,EAAK;EAApD,CAAb;SACO+D,SAAA,CAAU4B,OAAV,CAAkB,wBAAlB,EAA4C,UAAAC,OAAA;WAAWF,IAAA,CAAKE,OAAL,CAAX;GAA5C,CAAP;;;ACNF;;;;;;;;;;AAUA,SAAwBC,gBAATA,CAA0BxC,MAA1B,EAAkCyC,gBAAlC,EAAoD/B,SAApD,EAA+D;cAChEA,SAAA,CAAUiB,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAZ;;;MAGMe,UAAA,GAAaX,aAAA,CAAc/B,MAAd,CAAnB;;;MAGM2C,aAAA,GAAgB;WACbD,UAAA,CAAW7E,KADE;YAEZ6E,UAAA,CAAW5E;GAFrB;;;MAMM8E,OAAA,GAAU,CAAC,OAAD,EAAU,MAAV,EAAkBnL,OAAlB,CAA0BiJ,SAA1B,MAAyC,CAAC,CAA1D;MACMmC,QAAA,GAAWD,OAAA,GAAU,KAAV,GAAkB,MAAnC;MACME,aAAA,GAAgBF,OAAA,GAAU,MAAV,GAAmB,KAAzC;MACMG,WAAA,GAAcH,OAAA,GAAU,QAAV,GAAqB,OAAzC;MACMI,oBAAA,GAAuB,CAACJ,OAAD,GAAW,QAAX,GAAsB,OAAnD;gBAEcC,QAAd,IACEJ,gBAAA,CAAiBI,QAAjB,IACAJ,gBAAA,CAAiBM,WAAjB,IAAgC,CADhC,GAEAL,UAAA,CAAWK,WAAX,IAA0B,CAH5B;MAIIrC,SAAA,KAAcoC,aAAlB,EAAiC;kBACjBA,aAAd,IACEL,gBAAA,CAAiBK,aAAjB,IAAkCJ,UAAA,CAAWM,oBAAX,CADpC;GADF,MAGO;kBACSF,aAAd,IACEL,gBAAA,CAAiBL,oBAAA,CAAqBU,aAArB,CAAjB,CADF;;SAIKH,aAAP;;;AC5CF;;;;;;;;;AASA,SAAwBM,IAATA,CAAcC,GAAd,EAAmBC,KAAnB,EAA0B;;MAEnCC,KAAA,CAAMC,SAAN,CAAgBJ,IAApB,EAA0B;WACjBC,GAAA,CAAID,IAAJ,CAASE,KAAT,CAAP;;;;SAIKD,GAAA,CAAI3B,MAAJ,CAAW4B,KAAX,EAAkB,CAAlB,CAAP;;;ACdF;;;;;;;;;AASA,SAAwBG,SAATA,CAAmBJ,GAAnB,EAAwBK,IAAxB,EAA8BC,KAA9B,EAAqC;;MAE9CJ,KAAA,CAAMC,SAAN,CAAgBC,SAApB,EAA+B;WACtBJ,GAAA,CAAII,SAAJ,CAAc,UAAAG,GAAA;aAAOA,GAAA,CAAIF,IAAJ,MAAcC,KAArB;KAAd,CAAP;;;;MAIIE,KAAA,GAAQT,IAAA,CAAKC,GAAL,EAAU,UAAAS,GAAA;WAAOA,GAAA,CAAIJ,IAAJ,MAAcC,KAArB;GAAV,CAAd;SACON,GAAA,CAAIzL,OAAJ,CAAYiM,KAAZ,CAAP;;;ACfF;;;;;;;;;;AAUA,SAAwBE,YAATA,CAAsBC,SAAtB,EAAiCC,IAAjC,EAAuCC,IAAvC,EAA6C;MACpDC,cAAA,GAAiBD,IAAA,KAAS9H,SAAT,GACnB4H,SADmB,GAEnBA,SAAA,CAAUI,KAAV,CAAgB,CAAhB,EAAmBX,SAAA,CAAUO,SAAV,EAAqB,MAArB,EAA6BE,IAA7B,CAAnB,CAFJ;iBAIeG,OAAf,CAAuB,UAAAxH,QAAA,EAAY;QAC7BA,QAAA,CAAS,UAAT,CAAJ,EAA0B;;cAChByH,IAAR,CAAa,uDAAb;;QAEIxM,EAAA,GAAK+E,QAAA,CAAS,UAAT,KAAwBA,QAAA,CAAS/E,EAA5C,CAJiC;QAK7B+E,QAAA,CAAS0H,OAAT,IAAoBhM,UAAA,CAAWT,EAAX,CAAxB,EAAwC;;;;WAIjCiG,OAAL,CAAaoC,MAAb,GAAsBrC,aAAA,CAAcmG,IAAA,CAAKlG,OAAL,CAAaoC,MAA3B,CAAtB;WACKpC,OAAL,CAAa/D,SAAb,GAAyB8D,aAAA,CAAcmG,IAAA,CAAKlG,OAAL,CAAa/D,SAA3B,CAAzB;aAEOlC,EAAA,CAAGmM,IAAH,EAASpH,QAAT,CAAP;;GAZJ;SAgBOoH,IAAP;;;AC9BF;;;;;;;AAOA,SAAwBO,MAATA,CAAA,EAAkB;;MAE3B,KAAKxC,KAAL,CAAWyC,WAAf,EAA4B;;;MAIxBR,IAAA,GAAO;cACC,IADD;YAED,EAFC;iBAGI,EAHJ;gBAIG,EAJH;aAKA,KALA;aAMA;GANX;;;OAUKlG,OAAL,CAAa/D,SAAb,GAAyB+H,mBAAA,CACvB,KAAKC,KADkB,EAEvB,KAAK7B,MAFkB,EAGvB,KAAKnG,SAHkB,EAIvB,KAAK0K,OAAL,CAAaC,aAJU,CAAzB;;;;;OAUK9D,SAAL,GAAiBD,oBAAA,CACf,KAAK8D,OAAL,CAAa7D,SADE,EAEfoD,IAAA,CAAKlG,OAAL,CAAa/D,SAFE,EAGf,KAAKmG,MAHU,EAIf,KAAKnG,SAJU,EAKf,KAAK0K,OAAL,CAAaV,SAAb,CAAuBY,IAAvB,CAA4BvE,iBALb,EAMf,KAAKqE,OAAL,CAAaV,SAAb,CAAuBY,IAAvB,CAA4BxE,OANb,CAAjB;;;OAUKyE,iBAAL,GAAyBZ,IAAA,CAAKpD,SAA9B;OAEK8D,aAAL,GAAqB,KAAKD,OAAL,CAAaC,aAAlC;;;OAGK5G,OAAL,CAAaoC,MAAb,GAAsBwC,gBAAA,CACpB,KAAKxC,MADe,EAEpB8D,IAAA,CAAKlG,OAAL,CAAa/D,SAFO,EAGpBiK,IAAA,CAAKpD,SAHe,CAAtB;OAMK9C,OAAL,CAAaoC,MAAb,CAAoB2E,QAApB,GAA+B,KAAKJ,OAAL,CAAaC,aAAb,GAC3B,OAD2B,GAE3B,UAFJ;;;SAKOZ,YAAA,CAAa,KAAKC,SAAlB,EAA6BC,IAA7B,CAAP;;;;MAII,CAAC,KAAKjC,KAAL,CAAW+C,SAAhB,EAA2B;SACpB/C,KAAL,CAAW+C,SAAX,GAAuB,IAAvB;SACKL,OAAL,CAAaM,QAAb,CAAsBf,IAAtB;GAFF,MAGO;SACAS,OAAL,CAAaO,QAAb,CAAsBhB,IAAtB;;;;ACxEJ;;;;;;AAMA,SAAwBiB,iBAATA,CAA2BlB,SAA3B,EAAsCmB,YAAtC,EAAoD;SAC1DnB,SAAA,CAAUoB,IAAV,CACL,UAAAzE,IAAA;QAAG0E,IAAH,GAAA1E,IAAA,CAAG0E,IAAH;MAASd,OAAT,GAAA5D,IAAA,CAAS4D,OAAT;WAAuBA,OAAA,IAAWc,IAAA,KAASF,YAA3C;GADK,CAAP;;;ACPF;;;;;;;AAOA,SAAwBG,wBAATA,CAAkCxM,QAAlC,EAA4C;MACnDyM,QAAA,GAAW,CAAC,KAAD,EAAQ,IAAR,EAAc,QAAd,EAAwB,KAAxB,EAA+B,GAA/B,CAAjB;MACMC,SAAA,GAAY1M,QAAA,CAAS2M,MAAT,CAAgB,CAAhB,EAAmBC,WAAnB,KAAmC5M,QAAA,CAASsL,KAAT,CAAe,CAAf,CAArD;OAEK,IAAI3M,CAAA,GAAI,CAAb,EAAgBA,CAAA,GAAI8N,QAAA,CAAS7N,MAA7B,EAAqCD,CAAA,EAArC,EAA0C;QAClCkO,MAAA,GAASJ,QAAA,CAAS9N,CAAT,CAAf;QACMmO,OAAA,GAAUD,MAAA,QAAYA,MAAZ,GAAqBH,SAArB,GAAmC1M,QAAnD;QACI,OAAOzB,QAAA,CAASoC,IAAT,CAAcoM,KAAd,CAAoBD,OAApB,CAAP,KAAwC,WAA5C,EAAyD;aAChDA,OAAP;;;SAGG,IAAP;;;ACfF;;;;;AAKA,SAAwBE,OAATA,CAAA,EAAmB;OAC3B9D,KAAL,CAAWyC,WAAX,GAAyB,IAAzB;;;MAGIS,iBAAA,CAAkB,KAAKlB,SAAvB,EAAkC,YAAlC,CAAJ,EAAqD;SAC9C7D,MAAL,CAAY4F,eAAZ,CAA4B,aAA5B;SACK5F,MAAL,CAAY0F,KAAZ,CAAkBf,QAAlB,GAA6B,EAA7B;SACK3E,MAAL,CAAY0F,KAAZ,CAAkB/I,GAAlB,GAAwB,EAAxB;SACKqD,MAAL,CAAY0F,KAAZ,CAAkB7I,IAAlB,GAAyB,EAAzB;SACKmD,MAAL,CAAY0F,KAAZ,CAAkB5I,KAAlB,GAA0B,EAA1B;SACKkD,MAAL,CAAY0F,KAAZ,CAAkB9I,MAAlB,GAA2B,EAA3B;SACKoD,MAAL,CAAY0F,KAAZ,CAAkBG,UAAlB,GAA+B,EAA/B;SACK7F,MAAL,CAAY0F,KAAZ,CAAkBP,wBAAA,CAAyB,WAAzB,CAAlB,IAA2D,EAA3D;;OAGGW,qBAAL;;;;MAII,KAAKvB,OAAL,CAAawB,eAAjB,EAAkC;SAC3B/F,MAAL,CAAY7G,UAAZ,CAAuB6M,WAAvB,CAAmC,KAAKhG,MAAxC;;SAEK,IAAP;;;AC9BF;;;;;AAKA,SAAwBiG,SAATA,CAAmBvN,OAAnB,EAA4B;MACnCG,aAAA,GAAgBH,OAAA,CAAQG,aAA9B;SACOA,aAAA,GAAgBA,aAAA,CAAcC,WAA9B,GAA4C7B,MAAnD;;ACJF,SAASiP,qBAATA,CAA+BlH,YAA/B,EAA6CmH,KAA7C,EAAoDC,QAApD,EAA8DC,aAA9D,EAA6E;MACrEC,MAAA,GAAStH,YAAA,CAAa9F,QAAb,KAA0B,MAAzC;MACMqN,MAAA,GAASD,MAAA,GAAStH,YAAA,CAAanG,aAAb,CAA2BC,WAApC,GAAkDkG,YAAjE;SACOwH,gBAAP,CAAwBL,KAAxB,EAA+BC,QAA/B,EAAyC;IAAEK,OAAA,EAAS;EAAX,CAAzC;MAEI,CAACH,MAAL,EAAa;0BAETjN,eAAA,CAAgBkN,MAAA,CAAOpN,UAAvB,CADF,EAEEgN,KAFF,EAGEC,QAHF,EAIEC,aAJF;;gBAOYK,IAAd,CAAmBH,MAAnB;;;;;;;;;AASF,SAAwBI,mBAATA,CACb9M,SADa,EAEb0K,OAFa,EAGb1C,KAHa,EAIb+E,WAJa,EAKb;;QAEMA,WAAN,GAAoBA,WAApB;YACU/M,SAAV,EAAqB2M,gBAArB,CAAsC,QAAtC,EAAgD3E,KAAA,CAAM+E,WAAtD,EAAmE;IAAEH,OAAA,EAAS;EAAX,CAAnE;;;MAGMI,aAAA,GAAgBxN,eAAA,CAAgBQ,SAAhB,CAAtB;wBAEEgN,aADF,EAEE,QAFF,EAGEhF,KAAA,CAAM+E,WAHR,EAIE/E,KAAA,CAAMwE,aAJR;QAMMQ,aAAN,GAAsBA,aAAtB;QACMC,aAAN,GAAsB,IAAtB;SAEOjF,KAAP;;;AC5CF;;;;;;AAMA,SAAwBkF,oBAATA,CAAA,EAAgC;MACzC,CAAC,KAAKlF,KAAL,CAAWiF,aAAhB,EAA+B;SACxBjF,KAAL,GAAa8E,mBAAA,CACX,KAAK9M,SADM,EAEX,KAAK0K,OAFM,EAGX,KAAK1C,KAHM,EAIX,KAAKmF,cAJM,CAAb;;;;ACRJ;;;;;;AAMA,SAAwBC,oBAATA,CAA8BpN,SAA9B,EAAyCgI,KAAzC,EAAgD;;YAEnDhI,SAAV,EAAqBqN,mBAArB,CAAyC,QAAzC,EAAmDrF,KAAA,CAAM+E,WAAzD;;;QAGMP,aAAN,CAAoBnC,OAApB,CAA4B,UAAAqC,MAAA,EAAU;WAC7BW,mBAAP,CAA2B,QAA3B,EAAqCrF,KAAA,CAAM+E,WAA3C;GADF;;;QAKMA,WAAN,GAAoB,IAApB;QACMP,aAAN,GAAsB,EAAtB;QACMQ,aAAN,GAAsB,IAAtB;QACMC,aAAN,GAAsB,KAAtB;SACOjF,KAAP;;;ACpBF;;;;;;;AAOA,SAAwBiE,qBAATA,CAAA,EAAiC;MAC1C,KAAKjE,KAAL,CAAWiF,aAAf,EAA8B;yBACP,KAAKE,cAA1B;SACKnF,KAAL,GAAaoF,oBAAA,CAAqB,KAAKpN,SAA1B,EAAqC,KAAKgI,KAA1C,CAAb;;;;ACZJ;;;;;;;AAOA,SAAwBsF,SAATA,CAAmBC,CAAnB,EAAsB;SAC5BA,CAAA,KAAM,EAAN,IAAY,CAACC,KAAA,CAAMjK,UAAA,CAAWgK,CAAX,CAAN,CAAb,IAAqCE,QAAA,CAASF,CAAT,CAA5C;;;ACNF;;;;;;;;AAQA,SAAwBG,SAATA,CAAmB7O,OAAnB,EAA4BsE,MAA5B,EAAoC;SAC1C+D,IAAP,CAAY/D,MAAZ,EAAoBkH,OAApB,CAA4B,UAAAX,IAAA,EAAQ;QAC9BiE,IAAA,GAAO,EAAX;;QAGE,CAAC,OAAD,EAAU,QAAV,EAAoB,KAApB,EAA2B,OAA3B,EAAoC,QAApC,EAA8C,MAA9C,EAAsD/P,OAAtD,CAA8D8L,IAA9D,MACE,CAAC,CADH,IAEA4D,SAAA,CAAUnK,MAAA,CAAOuG,IAAP,CAAV,CAHF,EAIE;aACO,IAAP;;YAEMmC,KAAR,CAAcnC,IAAd,IAAsBvG,MAAA,CAAOuG,IAAP,IAAeiE,IAArC;GAVF;;;ACXF;;;;;;;;AAQA,SAAwBC,aAATA,CAAuB/O,OAAvB,EAAgCgP,UAAhC,EAA4C;SAClD3G,IAAP,CAAY2G,UAAZ,EAAwBxD,OAAxB,CAAgC,UAASX,IAAT,EAAe;QACvCC,KAAA,GAAQkE,UAAA,CAAWnE,IAAX,CAAd;QACIC,KAAA,KAAU,KAAd,EAAqB;cACXmE,YAAR,CAAqBpE,IAArB,EAA2BmE,UAAA,CAAWnE,IAAX,CAA3B;KADF,MAEO;cACGqC,eAAR,CAAwBrC,IAAxB;;GALJ;;;ACJF;;;;;;;;;AASA,SAAwBqE,UAATA,CAAoB9D,IAApB,EAA0B;;;;;YAK7BA,IAAA,CAAK+D,QAAL,CAAc7H,MAAxB,EAAgC8D,IAAA,CAAK9G,MAArC;;;;gBAIc8G,IAAA,CAAK+D,QAAL,CAAc7H,MAA5B,EAAoC8D,IAAA,CAAK4D,UAAzC;;;MAGI5D,IAAA,CAAKgE,YAAL,IAAqBhH,MAAA,CAAOC,IAAP,CAAY+C,IAAA,CAAKiE,WAAjB,EAA8BxQ,MAAvD,EAA+D;cACnDuM,IAAA,CAAKgE,YAAf,EAA6BhE,IAAA,CAAKiE,WAAlC;;SAGKjE,IAAP;;;;;;;;;;;;;AAaF,SAAgBkE,gBAATA,CACLnO,SADK,EAELmG,MAFK,EAGLuE,OAHK,EAIL0D,eAJK,EAKLpG,KALK,EAML;;MAEMY,gBAAA,GAAmBb,mBAAA,CAAoBC,KAApB,EAA2B7B,MAA3B,EAAmCnG,SAAnC,EAA8C0K,OAAA,CAAQC,aAAtD,CAAzB;;;;;MAKM9D,SAAA,GAAYD,oBAAA,CAChB8D,OAAA,CAAQ7D,SADQ,EAEhB+B,gBAFgB,EAGhBzC,MAHgB,EAIhBnG,SAJgB,EAKhB0K,OAAA,CAAQV,SAAR,CAAkBY,IAAlB,CAAuBvE,iBALP,EAMhBqE,OAAA,CAAQV,SAAR,CAAkBY,IAAlB,CAAuBxE,OANP,CAAlB;SASO0H,YAAP,CAAoB,aAApB,EAAmCjH,SAAnC;;;;YAIUV,MAAV,EAAkB;IAAE2E,QAAA,EAAUJ,OAAA,CAAQC,aAAR,GAAwB,OAAxB,GAAkC;EAA9C,CAAlB;SAEOD,OAAP;;;ACvEF;;;;;;;;;;;;;;;;;;;AAmBA,SAAwB2D,iBAATA,CAA2BpE,IAA3B,EAAiCqE,WAAjC,EAA8C;sBAC7BrE,IAAA,CAAKlG,OADwB;IACnDoC,MADmD,GAAAoI,aAAA,CACnDpI,MADmD;IAC3CnG,SAD2C,GAAAuO,aAAA,CAC3CvO,SAD2C;MAEnDwO,KAFmD,GAElC9K,IAFkC,CAEnD8K,KAFmD;IAE5CC,KAF4C,GAElC/K,IAFkC,CAE5C+K,KAF4C;MAGrDC,OAAA,GAAU,SAAVA,OAAUA,CAAAC,CAAA;WAAKA,CAAL;GAAhB;MAEMC,cAAA,GAAiBJ,KAAA,CAAMxO,SAAA,CAAUgE,KAAhB,CAAvB;MACM6K,WAAA,GAAcL,KAAA,CAAMrI,MAAA,CAAOnC,KAAb,CAApB;MAEM8K,UAAA,GAAa,CAAC,MAAD,EAAS,OAAT,EAAkBlR,OAAlB,CAA0BqM,IAAA,CAAKpD,SAA/B,MAA8C,CAAC,CAAlE;MACMkI,WAAA,GAAc9E,IAAA,CAAKpD,SAAL,CAAejJ,OAAf,CAAuB,GAAvB,MAAgC,CAAC,CAArD;MACMoR,eAAA,GAAkBJ,cAAA,GAAiB,CAAjB,KAAuBC,WAAA,GAAc,CAA7D;MACMI,YAAA,GAAeL,cAAA,GAAiB,CAAjB,KAAuB,CAAvB,IAA4BC,WAAA,GAAc,CAAd,KAAoB,CAArE;MAEMK,mBAAA,GAAsB,CAACZ,WAAD,GACxBI,OADwB,GAExBI,UAAA,IAAcC,WAAd,IAA6BC,eAA7B,GACAR,KADA,GAEAC,KAJJ;MAKMU,iBAAA,GAAoB,CAACb,WAAD,GAAeI,OAAf,GAAyBF,KAAnD;SAEO;UACCU,mBAAA,CACJD,YAAA,IAAgB,CAACF,WAAjB,IAAgCT,WAAhC,GACInI,MAAA,CAAOnD,IAAP,GAAc,CADlB,GAEImD,MAAA,CAAOnD,IAHP,CADD;SAMAmM,iBAAA,CAAkBhJ,MAAA,CAAOrD,GAAzB,CANA;YAOGqM,iBAAA,CAAkBhJ,MAAA,CAAOpD,MAAzB,CAPH;WAQEmM,mBAAA,CAAoB/I,MAAA,CAAOlD,KAA3B;GART;;AChCF,IAAMmM,SAAA,GAAYjS,SAAA,IAAa,WAAW2C,IAAX,CAAgBxC,SAAA,CAAUK,SAA1B,CAA/B;;;;;;;;;AASA,SAAwB0R,YAATA,CAAsBpF,IAAtB,EAA4BS,OAA5B,EAAqC;MAC1CvC,CAD0C,GACjCuC,OADiC,CAC1CvC,CAD0C;IACvCE,CADuC,GACjCqC,OADiC,CACvCrC,CADuC;MAE1ClC,MAF0C,GAE/B8D,IAAA,CAAKlG,OAF0B,CAE1CoC,MAF0C;;;;MAK5CmJ,2BAAA,GAA8BlG,IAAA,CAClCa,IAAA,CAAK+D,QAAL,CAAchE,SADoB,EAElC,UAAAnH,QAAA;WAAYA,QAAA,CAASwI,IAAT,KAAkB,YAA9B;GAFkC,EAGlCkE,eAHF;MAIID,2BAAA,KAAgClN,SAApC,EAA+C;YACrCkI,IAAR,CACE,+HADF;;MAIIiF,eAAA,GACJD,2BAAA,KAAgClN,SAAhC,GACIkN,2BADJ,GAEI5E,OAAA,CAAQ6E,eAHd;MAKM5O,YAAA,GAAeH,eAAA,CAAgByJ,IAAA,CAAK+D,QAAL,CAAc7H,MAA9B,CAArB;MACMqJ,gBAAA,GAAmBtL,qBAAA,CAAsBvD,YAAtB,CAAzB;;;MAGMwC,MAAA,GAAS;cACHgD,MAAA,CAAO2E;GADnB;MAIM/G,OAAA,GAAUsK,iBAAA,CACdpE,IADc,EAEd7M,MAAA,CAAOqS,gBAAP,GAA0B,CAA1B,IAA+B,CAACL,SAFlB,CAAhB;MAKM/L,KAAA,GAAQ8E,CAAA,KAAM,QAAN,GAAiB,KAAjB,GAAyB,QAAvC;MACM7E,KAAA,GAAQ+E,CAAA,KAAM,OAAN,GAAgB,MAAhB,GAAyB,OAAvC;;;;;MAKMqH,gBAAA,GAAmBpE,wBAAA,CAAyB,WAAzB,CAAzB;;;;;;;;;;;MAWItI,IAAA,SAAJ;IAAUF,GAAA,SAAV;MACIO,KAAA,KAAU,QAAd,EAAwB;;;QAGlB1C,YAAA,CAAatB,QAAb,KAA0B,MAA9B,EAAsC;YAC9B,CAACsB,YAAA,CAAa4D,YAAd,GAA6BR,OAAA,CAAQhB,MAA3C;KADF,MAEO;YACC,CAACyM,gBAAA,CAAiBvL,MAAlB,GAA2BF,OAAA,CAAQhB,MAAzC;;GANJ,MAQO;UACCgB,OAAA,CAAQjB,GAAd;;MAEEQ,KAAA,KAAU,OAAd,EAAuB;QACjB3C,YAAA,CAAatB,QAAb,KAA0B,MAA9B,EAAsC;aAC7B,CAACsB,YAAA,CAAa2D,WAAd,GAA4BP,OAAA,CAAQd,KAA3C;KADF,MAEO;aACE,CAACuM,gBAAA,CAAiBxL,KAAlB,GAA0BD,OAAA,CAAQd,KAAzC;;GAJJ,MAMO;WACEc,OAAA,CAAQf,IAAf;;MAEEuM,eAAA,IAAmBG,gBAAvB,EAAyC;WAChCA,gBAAP,qBAA0C1M,IAA1C,YAAqDF,GAArD;WACOO,KAAP,IAAgB,CAAhB;WACOC,KAAP,IAAgB,CAAhB;WACO0I,UAAP,GAAoB,WAApB;GAJF,MAKO;;QAEC2D,SAAA,GAAYtM,KAAA,KAAU,QAAV,GAAqB,CAAC,CAAtB,GAA0B,CAA5C;QACMuM,UAAA,GAAatM,KAAA,KAAU,OAAV,GAAoB,CAAC,CAArB,GAAyB,CAA5C;WACOD,KAAP,IAAgBP,GAAA,GAAM6M,SAAtB;WACOrM,KAAP,IAAgBN,IAAA,GAAO4M,UAAvB;WACO5D,UAAP,GAAuB3I,KAAvB,UAAiCC,KAAjC;;;;MAIIuK,UAAA,GAAa;mBACF5D,IAAA,CAAKpD;GADtB;;;OAKKgH,UAAL,GAAAgC,QAAA,KAAuBhC,UAAvB,EAAsC5D,IAAA,CAAK4D,UAA3C;OACK1K,MAAL,GAAA0M,QAAA,KAAmB1M,MAAnB,EAA8B8G,IAAA,CAAK9G,MAAnC;OACK+K,WAAL,GAAA2B,QAAA,KAAwB5F,IAAA,CAAKlG,OAAL,CAAa+L,KAArC,EAA+C7F,IAAA,CAAKiE,WAApD;SAEOjE,IAAP;;;AC5GF;;;;;;;;;;AAUA,SAAwB8F,kBAATA,CACb/F,SADa,EAEbgG,cAFa,EAGbC,aAHa,EAIb;MACMC,UAAA,GAAa9G,IAAA,CAAKY,SAAL,EAAgB,UAAArD,IAAA;QAAG0E,IAAH,GAAA1E,IAAA,CAAG0E,IAAH;WAAcA,IAAA,KAAS2E,cAAvB;GAAhB,CAAnB;MAEMG,UAAA,GACJ,CAAC,CAACD,UAAF,IACAlG,SAAA,CAAUoB,IAAV,CAAe,UAAAvI,QAAA,EAAY;WAEvBA,QAAA,CAASwI,IAAT,KAAkB4E,aAAlB,IACApN,QAAA,CAAS0H,OADT,IAEA1H,QAAA,CAASzB,KAAT,GAAiB8O,UAAA,CAAW9O,KAH9B;GADF,CAFF;MAUI,CAAC+O,UAAL,EAAiB;QACTC,WAAA,SAAkBJ,cAAlB,MAAN;QACMK,SAAA,SAAiBJ,aAAjB,MAAN;YACQ3F,IAAR,CACK+F,SADL,iCAC0CD,WAD1C,iEACgHA,WADhH;;SAIKD,UAAP;;;AC/BF;;;;;;;AAOA,SAAwBL,KAATA,CAAe7F,IAAf,EAAqBS,OAArB,EAA8B;;;;MAEvC,CAACqF,kBAAA,CAAmB9F,IAAA,CAAK+D,QAAL,CAAchE,SAAjC,EAA4C,OAA5C,EAAqD,cAArD,CAAL,EAA2E;WAClEC,IAAP;;MAGEgE,YAAA,GAAevD,OAAA,CAAQ7L,OAA3B;;;MAGI,OAAOoP,YAAP,KAAwB,QAA5B,EAAsC;mBACrBhE,IAAA,CAAK+D,QAAL,CAAc7H,MAAd,CAAqBmK,aAArB,CAAmCrC,YAAnC,CAAf;;;QAGI,CAACA,YAAL,EAAmB;aACVhE,IAAP;;GALJ,MAOO;;;QAGD,CAACA,IAAA,CAAK+D,QAAL,CAAc7H,MAAd,CAAqBpE,QAArB,CAA8BkM,YAA9B,CAAL,EAAkD;cACxC3D,IAAR,CACE,+DADF;aAGOL,IAAP;;;MAIEpD,SAAA,GAAYoD,IAAA,CAAKpD,SAAL,CAAeiB,KAAf,CAAqB,GAArB,EAA0B,CAA1B,CAAlB;sBAC8BmC,IAAA,CAAKlG,OA5BQ;IA4BnCoC,MA5BmC,GAAAoI,aAAA,CA4BnCpI,MA5BmC;IA4B3BnG,SA5B2B,GAAAuO,aAAA,CA4B3BvO,SA5B2B;MA6BrC8O,UAAA,GAAa,CAAC,MAAD,EAAS,OAAT,EAAkBlR,OAAlB,CAA0BiJ,SAA1B,MAAyC,CAAC,CAA7D;MAEM0J,GAAA,GAAMzB,UAAA,GAAa,QAAb,GAAwB,OAApC;MACM0B,eAAA,GAAkB1B,UAAA,GAAa,KAAb,GAAqB,MAA7C;MACM5M,IAAA,GAAOsO,eAAA,CAAgBC,WAAhB,EAAb;MACMC,OAAA,GAAU5B,UAAA,GAAa,MAAb,GAAsB,KAAtC;MACM6B,MAAA,GAAS7B,UAAA,GAAa,QAAb,GAAwB,OAAvC;MACM8B,gBAAA,GAAmB1I,aAAA,CAAc+F,YAAd,EAA4BsC,GAA5B,CAAzB;;;;;;;;MAQIvQ,SAAA,CAAU2Q,MAAV,IAAoBC,gBAApB,GAAuCzK,MAAA,CAAOjE,IAAP,CAA3C,EAAyD;SAClD6B,OAAL,CAAaoC,MAAb,CAAoBjE,IAApB,KACEiE,MAAA,CAAOjE,IAAP,KAAgBlC,SAAA,CAAU2Q,MAAV,IAAoBC,gBAApC,CADF;;;MAIE5Q,SAAA,CAAUkC,IAAV,IAAkB0O,gBAAlB,GAAqCzK,MAAA,CAAOwK,MAAP,CAAzC,EAAyD;SAClD5M,OAAL,CAAaoC,MAAb,CAAoBjE,IAApB,KACElC,SAAA,CAAUkC,IAAV,IAAkB0O,gBAAlB,GAAqCzK,MAAA,CAAOwK,MAAP,CADvC;;OAGG5M,OAAL,CAAaoC,MAAb,GAAsBrC,aAAA,CAAcmG,IAAA,CAAKlG,OAAL,CAAaoC,MAA3B,CAAtB;;;MAGM0K,MAAA,GAAS7Q,SAAA,CAAUkC,IAAV,IAAkBlC,SAAA,CAAUuQ,GAAV,IAAiB,CAAnC,GAAuCK,gBAAA,GAAmB,CAAzE;;;;MAIM1R,GAAA,GAAMN,wBAAA,CAAyBqL,IAAA,CAAK+D,QAAL,CAAc7H,MAAvC,CAAZ;MACM2K,gBAAA,GAAmBvN,UAAA,CAAWrE,GAAA,YAAasR,eAAb,CAAX,CAAzB;MACMO,gBAAA,GAAmBxN,UAAA,CAAWrE,GAAA,YAAasR,eAAb,WAAX,CAAzB;MACIQ,SAAA,GACFH,MAAA,GAAS5G,IAAA,CAAKlG,OAAL,CAAaoC,MAAb,CAAoBjE,IAApB,CAAT,GAAqC4O,gBAArC,GAAwDC,gBAD1D;;;cAIYrN,IAAA,CAAKC,GAAL,CAASD,IAAA,CAAKuN,GAAL,CAAS9K,MAAA,CAAOoK,GAAP,IAAcK,gBAAvB,EAAyCI,SAAzC,CAAT,EAA8D,CAA9D,CAAZ;OAEK/C,YAAL,GAAoBA,YAApB;OACKlK,OAAL,CAAa+L,KAAb,IAAAoB,mBAAA,OAAAC,cAAA,CAAAD,mBAAA,EACGhP,IADH,EACUwB,IAAA,CAAK8K,KAAL,CAAWwC,SAAX,CADV,GAAAG,cAAA,CAAAD,mBAAA,EAEGR,OAFH,EAEa,EAFb,GAAAQ,mBAAA;SAKOjH,IAAP;;;ACvFF;;;;;;;AAOA,SAAwBmH,oBAATA,CAA8BvJ,SAA9B,EAAyC;MAClDA,SAAA,KAAc,KAAlB,EAAyB;WAChB,OAAP;GADF,MAEO,IAAIA,SAAA,KAAc,OAAlB,EAA2B;WACzB,KAAP;;SAEKA,SAAP;;;ACbF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+BA,IAAAwJ,UAAA,GAAe,CACb,YADa,EAEb,MAFa,EAGb,UAHa,EAIb,WAJa,EAKb,KALa,EAMb,SANa,EAOb,aAPa,EAQb,OARa,EASb,WATa,EAUb,YAVa,EAWb,QAXa,EAYb,cAZa,EAab,UAba,EAcb,MAda,EAeb,YAfa,CAAf;;AC7BA;AACA,IAAMC,eAAA,GAAkBD,UAAA,CAAWjH,KAAX,CAAiB,CAAjB,CAAxB;;;;;;;;;;;;AAYA,SAAwBmH,SAATA,CAAmB1K,SAAnB,EAA+C;MAAjB2K,OAAiB,GAAArP,SAAA,CAAAzE,MAAA,QAAAyE,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAP,KAAO;MACtDsP,KAAA,GAAQH,eAAA,CAAgB1T,OAAhB,CAAwBiJ,SAAxB,CAAd;MACMwC,GAAA,GAAMiI,eAAA,CACTlH,KADS,CACHqH,KAAA,GAAQ,CADL,EAETC,MAFS,CAEFJ,eAAA,CAAgBlH,KAAhB,CAAsB,CAAtB,EAAyBqH,KAAzB,CAFE,CAAZ;SAGOD,OAAA,GAAUnI,GAAA,CAAIsI,OAAJ,EAAV,GAA0BtI,GAAjC;;ACZF,IAAMuI,SAAA,GAAY;QACV,MADU;aAEL,WAFK;oBAGE;CAHpB;;;;;;;;;AAaA,SAAwBhH,IAATA,CAAcX,IAAd,EAAoBS,OAApB,EAA6B;;MAEtCQ,iBAAA,CAAkBjB,IAAA,CAAK+D,QAAL,CAAchE,SAAhC,EAA2C,OAA3C,CAAJ,EAAyD;WAChDC,IAAP;;MAGEA,IAAA,CAAK4H,OAAL,IAAgB5H,IAAA,CAAKpD,SAAL,KAAmBoD,IAAA,CAAKY,iBAA5C,EAA+D;;WAEtDZ,IAAP;;MAGI3D,UAAA,GAAaJ,aAAA,CACjB+D,IAAA,CAAK+D,QAAL,CAAc7H,MADG,EAEjB8D,IAAA,CAAK+D,QAAL,CAAchO,SAFG,EAGjB0K,OAAA,CAAQtE,OAHS,EAIjBsE,OAAA,CAAQrE,iBAJS,EAKjB4D,IAAA,CAAKU,aALY,CAAnB;MAQI9D,SAAA,GAAYoD,IAAA,CAAKpD,SAAL,CAAeiB,KAAf,CAAqB,GAArB,EAA0B,CAA1B,CAAhB;MACIgK,iBAAA,GAAoBvJ,oBAAA,CAAqB1B,SAArB,CAAxB;MACIgB,SAAA,GAAYoC,IAAA,CAAKpD,SAAL,CAAeiB,KAAf,CAAqB,GAArB,EAA0B,CAA1B,KAAgC,EAAhD;MAEIiK,SAAA,GAAY,EAAhB;UAEQrH,OAAA,CAAQsH,QAAhB;SACOJ,SAAA,CAAUK,IAAf;kBACc,CAACpL,SAAD,EAAYiL,iBAAZ,CAAZ;;SAEGF,SAAA,CAAUM,SAAf;kBACcX,SAAA,CAAU1K,SAAV,CAAZ;;SAEG+K,SAAA,CAAUO,gBAAf;kBACcZ,SAAA,CAAU1K,SAAV,EAAqB,IAArB,CAAZ;;;kBAGY6D,OAAA,CAAQsH,QAApB;;YAGM3H,OAAV,CAAkB,UAAC+H,IAAD,EAAOX,KAAP,EAAiB;QAC7B5K,SAAA,KAAcuL,IAAd,IAAsBL,SAAA,CAAUrU,MAAV,KAAqB+T,KAAA,GAAQ,CAAvD,EAA0D;aACjDxH,IAAP;;gBAGUA,IAAA,CAAKpD,SAAL,CAAeiB,KAAf,CAAqB,GAArB,EAA0B,CAA1B,CAAZ;wBACoBS,oBAAA,CAAqB1B,SAArB,CAApB;QAEMiC,aAAA,GAAgBmB,IAAA,CAAKlG,OAAL,CAAaoC,MAAnC;QACMkM,UAAA,GAAapI,IAAA,CAAKlG,OAAL,CAAa/D,SAAhC;;;QAGMyO,KAAA,GAAQ/K,IAAA,CAAK+K,KAAnB;QACM6D,WAAA,GACHzL,SAAA,KAAc,MAAd,IACC4H,KAAA,CAAM3F,aAAA,CAAc7F,KAApB,IAA6BwL,KAAA,CAAM4D,UAAA,CAAWrP,IAAjB,CAD/B,IAEC6D,SAAA,KAAc,OAAd,IACC4H,KAAA,CAAM3F,aAAA,CAAc9F,IAApB,IAA4ByL,KAAA,CAAM4D,UAAA,CAAWpP,KAAjB,CAH9B,IAIC4D,SAAA,KAAc,KAAd,IACC4H,KAAA,CAAM3F,aAAA,CAAc/F,MAApB,IAA8B0L,KAAA,CAAM4D,UAAA,CAAWvP,GAAjB,CALhC,IAMC+D,SAAA,KAAc,QAAd,IACC4H,KAAA,CAAM3F,aAAA,CAAchG,GAApB,IAA2B2L,KAAA,CAAM4D,UAAA,CAAWtP,MAAjB,CAR/B;QAUMwP,aAAA,GAAgB9D,KAAA,CAAM3F,aAAA,CAAc9F,IAApB,IAA4ByL,KAAA,CAAMnI,UAAA,CAAWtD,IAAjB,CAAlD;QACMwP,cAAA,GAAiB/D,KAAA,CAAM3F,aAAA,CAAc7F,KAApB,IAA6BwL,KAAA,CAAMnI,UAAA,CAAWrD,KAAjB,CAApD;QACMwP,YAAA,GAAehE,KAAA,CAAM3F,aAAA,CAAchG,GAApB,IAA2B2L,KAAA,CAAMnI,UAAA,CAAWxD,GAAjB,CAAhD;QACM4P,eAAA,GACJjE,KAAA,CAAM3F,aAAA,CAAc/F,MAApB,IAA8B0L,KAAA,CAAMnI,UAAA,CAAWvD,MAAjB,CADhC;QAGM4P,mBAAA,GACH9L,SAAA,KAAc,MAAd,IAAwB0L,aAAzB,IACC1L,SAAA,KAAc,OAAd,IAAyB2L,cAD1B,IAEC3L,SAAA,KAAc,KAAd,IAAuB4L,YAFxB,IAGC5L,SAAA,KAAc,QAAd,IAA0B6L,eAJ7B;;;QAOM5D,UAAA,GAAa,CAAC,KAAD,EAAQ,QAAR,EAAkBlR,OAAlB,CAA0BiJ,SAA1B,MAAyC,CAAC,CAA7D;;;QAGM+L,qBAAA,GACJ,CAAC,CAAClI,OAAA,CAAQmI,cAAV,KACE/D,UAAA,IAAcjH,SAAA,KAAc,OAA5B,IAAuC0K,aAAxC,IACEzD,UAAA,IAAcjH,SAAA,KAAc,KAA5B,IAAqC2K,cADvC,IAEE,CAAC1D,UAAD,IAAejH,SAAA,KAAc,OAA7B,IAAwC4K,YAF1C,IAGE,CAAC3D,UAAD,IAAejH,SAAA,KAAc,KAA7B,IAAsC6K,eAJzC,CADF;;;QAQMI,yBAAA,GACJ,CAAC,CAACpI,OAAA,CAAQqI,uBAAV,KACEjE,UAAA,IAAcjH,SAAA,KAAc,OAA5B,IAAuC2K,cAAxC,IACE1D,UAAA,IAAcjH,SAAA,KAAc,KAA5B,IAAqC0K,aADvC,IAEE,CAACzD,UAAD,IAAejH,SAAA,KAAc,OAA7B,IAAwC6K,eAF1C,IAGE,CAAC5D,UAAD,IAAejH,SAAA,KAAc,KAA7B,IAAsC4K,YAJzC,CADF;QAOMO,gBAAA,GAAmBJ,qBAAA,IAAyBE,yBAAlD;QAEIR,WAAA,IAAeK,mBAAf,IAAsCK,gBAA1C,EAA4D;;WAErDnB,OAAL,GAAe,IAAf;UAEIS,WAAA,IAAeK,mBAAnB,EAAwC;oBAC1BZ,SAAA,CAAUN,KAAA,GAAQ,CAAlB,CAAZ;;UAGEuB,gBAAJ,EAAsB;oBACR5B,oBAAA,CAAqBvJ,SAArB,CAAZ;;WAGGhB,SAAL,GAAiBA,SAAA,IAAagB,SAAA,GAAY,MAAMA,SAAlB,GAA8B,EAA3C,CAAjB;;;;WAIK9D,OAAL,CAAaoC,MAAb,GAAA0J,QAAA,KACK5F,IAAA,CAAKlG,OAAL,CAAaoC,MADlB,EAEKwC,gBAAA,CACDsB,IAAA,CAAK+D,QAAL,CAAc7H,MADb,EAED8D,IAAA,CAAKlG,OAAL,CAAa/D,SAFZ,EAGDiK,IAAA,CAAKpD,SAHJ,CAFL;aASOkD,YAAA,CAAaE,IAAA,CAAK+D,QAAL,CAAchE,SAA3B,EAAsCC,IAAtC,EAA4C,MAA5C,CAAP;;GAjFJ;SAoFOA,IAAP;;;AChJF;;;;;;;AAOA,SAAwBgJ,YAATA,CAAsBhJ,IAAtB,EAA4B;sBACXA,IAAA,CAAKlG,OADM;IACjCoC,MADiC,GAAAoI,aAAA,CACjCpI,MADiC;IACzBnG,SADyB,GAAAuO,aAAA,CACzBvO,SADyB;MAEnC6G,SAAA,GAAYoD,IAAA,CAAKpD,SAAL,CAAeiB,KAAf,CAAqB,GAArB,EAA0B,CAA1B,CAAlB;MACM2G,KAAA,GAAQ/K,IAAA,CAAK+K,KAAnB;MACMK,UAAA,GAAa,CAAC,KAAD,EAAQ,QAAR,EAAkBlR,OAAlB,CAA0BiJ,SAA1B,MAAyC,CAAC,CAA7D;MACM3E,IAAA,GAAO4M,UAAA,GAAa,OAAb,GAAuB,QAApC;MACM6B,MAAA,GAAS7B,UAAA,GAAa,MAAb,GAAsB,KAArC;MACM5F,WAAA,GAAc4F,UAAA,GAAa,OAAb,GAAuB,QAA3C;MAEI3I,MAAA,CAAOjE,IAAP,IAAeuM,KAAA,CAAMzO,SAAA,CAAU2Q,MAAV,CAAN,CAAnB,EAA6C;SACtC5M,OAAL,CAAaoC,MAAb,CAAoBwK,MAApB,IACElC,KAAA,CAAMzO,SAAA,CAAU2Q,MAAV,CAAN,IAA2BxK,MAAA,CAAO+C,WAAP,CAD7B;;MAGE/C,MAAA,CAAOwK,MAAP,IAAiBlC,KAAA,CAAMzO,SAAA,CAAUkC,IAAV,CAAN,CAArB,EAA6C;SACtC6B,OAAL,CAAaoC,MAAb,CAAoBwK,MAApB,IAA8BlC,KAAA,CAAMzO,SAAA,CAAUkC,IAAV,CAAN,CAA9B;;SAGK+H,IAAP;;;ACpBF;;;;;;;;;;;;AAYA,SAAgBiJ,OAATA,CAAiBC,GAAjB,EAAsBjK,WAAtB,EAAmCJ,aAAnC,EAAkDF,gBAAlD,EAAoE;;MAEnEd,KAAA,GAAQqL,GAAA,CAAItJ,KAAJ,CAAU,2BAAV,CAAd;MACMF,KAAA,GAAQ,CAAC7B,KAAA,CAAM,CAAN,CAAf;MACM6F,IAAA,GAAO7F,KAAA,CAAM,CAAN,CAAb;;;MAGI,CAAC6B,KAAL,EAAY;WACHwJ,GAAP;;MAGExF,IAAA,CAAK/P,OAAL,CAAa,GAAb,MAAsB,CAA1B,EAA6B;QACvBiB,OAAA,SAAJ;YACQ8O,IAAR;WACO,IAAL;kBACY7E,aAAV;;WAEG,GAAL;WACK,IAAL;;kBAEYF,gBAAV;;QAGEnG,IAAA,GAAOqB,aAAA,CAAcjF,OAAd,CAAb;WACO4D,IAAA,CAAKyG,WAAL,IAAoB,GAApB,GAA0BS,KAAjC;GAbF,MAcO,IAAIgE,IAAA,KAAS,IAAT,IAAiBA,IAAA,KAAS,IAA9B,EAAoC;;QAErCyF,IAAA,SAAJ;QACIzF,IAAA,KAAS,IAAb,EAAmB;aACVjK,IAAA,CAAKC,GAAL,CACLtG,QAAA,CAASoD,eAAT,CAAyB8D,YADpB,EAELnH,MAAA,CAAOwI,WAAP,IAAsB,CAFjB,CAAP;KADF,MAKO;aACElC,IAAA,CAAKC,GAAL,CACLtG,QAAA,CAASoD,eAAT,CAAyB6D,WADpB,EAELlH,MAAA,CAAOuI,UAAP,IAAqB,CAFhB,CAAP;;WAKKyN,IAAA,GAAO,GAAP,GAAazJ,KAApB;GAdK,MAeA;;;WAGEA,KAAP;;;;;;;;;;;;;;;AAeJ,SAAgB0J,WAATA,CACLxN,MADK,EAELiD,aAFK,EAGLF,gBAHK,EAIL0K,aAJK,EAKL;MACMvP,OAAA,GAAU,CAAC,CAAD,EAAI,CAAJ,CAAhB;;;;;MAKMwP,SAAA,GAAY,CAAC,OAAD,EAAU,MAAV,EAAkB3V,OAAlB,CAA0B0V,aAA1B,MAA6C,CAAC,CAAhE;;;;MAIME,SAAA,GAAY3N,MAAA,CAAOiC,KAAP,CAAa,SAAb,EAAwBX,GAAxB,CAA4B,UAAAsM,IAAA;WAAQA,IAAA,CAAKC,IAAL,EAAR;GAA5B,CAAlB;;;;MAIMC,OAAA,GAAUH,SAAA,CAAU5V,OAAV,CACdwL,IAAA,CAAKoK,SAAL,EAAgB,UAAAC,IAAA;WAAQA,IAAA,CAAKG,MAAL,CAAY,MAAZ,MAAwB,CAAC,CAAjC;GAAhB,CADc,CAAhB;MAIIJ,SAAA,CAAUG,OAAV,KAAsBH,SAAA,CAAUG,OAAV,EAAmB/V,OAAnB,CAA2B,GAA3B,MAAoC,CAAC,CAA/D,EAAkE;YACxD0M,IAAR,CACE,8EADF;;;;;MAOIuJ,UAAA,GAAa,aAAnB;MACIC,GAAA,GAAMH,OAAA,KAAY,CAAC,CAAb,GACN,CACEH,SAAA,CACGpJ,KADH,CACS,CADT,EACYuJ,OADZ,EAEGjC,MAFH,CAEU,CAAC8B,SAAA,CAAUG,OAAV,EAAmB7L,KAAnB,CAAyB+L,UAAzB,EAAqC,CAArC,CAAD,CAFV,CADF,EAIE,CAACL,SAAA,CAAUG,OAAV,EAAmB7L,KAAnB,CAAyB+L,UAAzB,EAAqC,CAArC,CAAD,EAA0CnC,MAA1C,CACE8B,SAAA,CAAUpJ,KAAV,CAAgBuJ,OAAA,GAAU,CAA1B,CADF,CAJF,CADM,GASN,CAACH,SAAD,CATJ;;;QAYMM,GAAA,CAAI3M,GAAJ,CAAQ,UAAC4M,EAAD,EAAKtC,KAAL,EAAe;;QAErBvI,WAAA,GAAc,CAACuI,KAAA,KAAU,CAAV,GAAc,CAAC8B,SAAf,GAA2BA,SAA5B,IAChB,QADgB,GAEhB,OAFJ;QAGIS,iBAAA,GAAoB,KAAxB;WAEED;;;KAGGE,MAHH,CAGU,UAAC3M,CAAD,EAAIC,CAAJ,EAAU;UACZD,CAAA,CAAEA,CAAA,CAAE5J,MAAF,GAAW,CAAb,MAAoB,EAApB,IAA0B,CAAC,GAAD,EAAM,GAAN,EAAWE,OAAX,CAAmB2J,CAAnB,MAA0B,CAAC,CAAzD,EAA4D;UACxDD,CAAA,CAAE5J,MAAF,GAAW,CAAb,IAAkB6J,CAAlB;4BACoB,IAApB;eACOD,CAAP;OAHF,MAIO,IAAI0M,iBAAJ,EAAuB;UAC1B1M,CAAA,CAAE5J,MAAF,GAAW,CAAb,KAAmB6J,CAAnB;4BACoB,KAApB;eACOD,CAAP;OAHK,MAIA;eACEA,CAAA,CAAEoK,MAAF,CAASnK,CAAT,CAAP;;KAbN,EAeK,EAfL;;KAiBGJ,GAjBH,CAiBO,UAAAgM,GAAA;aAAOD,OAAA,CAAQC,GAAR,EAAajK,WAAb,EAA0BJ,aAA1B,EAAyCF,gBAAzC,CAAP;KAjBP,CADF;GANI,CAAN;;;MA6BIyB,OAAJ,CAAY,UAAC0J,EAAD,EAAKtC,KAAL,EAAe;OACtBpH,OAAH,CAAW,UAACoJ,IAAD,EAAOS,MAAP,EAAkB;UACvB5G,SAAA,CAAUmG,IAAV,CAAJ,EAAqB;gBACXhC,KAAR,KAAkBgC,IAAA,IAAQM,EAAA,CAAGG,MAAA,GAAS,CAAZ,MAAmB,GAAnB,GAAyB,CAAC,CAA1B,GAA8B,CAAtC,CAAlB;;KAFJ;GADF;SAOOnQ,OAAP;;;;;;;;;;;;AAYF,SAAwB8B,MAATA,CAAgBoE,IAAhB,EAAAtD,IAAA,EAAkC;MAAVd,MAAU,GAAAc,IAAA,CAAVd,MAAU;MACvCgB,SADuC,GACOoD,IADP,CACvCpD,SADuC;oBACOoD,IADP,CAC5BlG,OAD4B;IACjBoC,MADiB,GAAAoI,aAAA,CACjBpI,MADiB;IACTnG,SADS,GAAAuO,aAAA,CACTvO,SADS;MAEzCsT,aAAA,GAAgBzM,SAAA,CAAUiB,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAtB;MAEI/D,OAAA,SAAJ;MACIuJ,SAAA,CAAU,CAACzH,MAAX,CAAJ,EAAwB;cACZ,CAAC,CAACA,MAAF,EAAU,CAAV,CAAV;GADF,MAEO;cACKwN,WAAA,CAAYxN,MAAZ,EAAoBM,MAApB,EAA4BnG,SAA5B,EAAuCsT,aAAvC,CAAV;;MAGEA,aAAA,KAAkB,MAAtB,EAA8B;WACrBxQ,GAAP,IAAciB,OAAA,CAAQ,CAAR,CAAd;WACOf,IAAP,IAAee,OAAA,CAAQ,CAAR,CAAf;GAFF,MAGO,IAAIuP,aAAA,KAAkB,OAAtB,EAA+B;WAC7BxQ,GAAP,IAAciB,OAAA,CAAQ,CAAR,CAAd;WACOf,IAAP,IAAee,OAAA,CAAQ,CAAR,CAAf;GAFK,MAGA,IAAIuP,aAAA,KAAkB,KAAtB,EAA6B;WAC3BtQ,IAAP,IAAee,OAAA,CAAQ,CAAR,CAAf;WACOjB,GAAP,IAAciB,OAAA,CAAQ,CAAR,CAAd;GAFK,MAGA,IAAIuP,aAAA,KAAkB,QAAtB,EAAgC;WAC9BtQ,IAAP,IAAee,OAAA,CAAQ,CAAR,CAAf;WACOjB,GAAP,IAAciB,OAAA,CAAQ,CAAR,CAAd;;OAGGoC,MAAL,GAAcA,MAAd;SACO8D,IAAP;;;AC5LF;;;;;;;AAOA,SAAwBkK,eAATA,CAAyBlK,IAAzB,EAA+BS,OAA/B,EAAwC;MACjDrE,iBAAA,GACFqE,OAAA,CAAQrE,iBAAR,IAA6B7F,eAAA,CAAgByJ,IAAA,CAAK+D,QAAL,CAAc7H,MAA9B,CAD/B;;;;;MAMI8D,IAAA,CAAK+D,QAAL,CAAchO,SAAd,KAA4BqG,iBAAhC,EAAmD;wBAC7B7F,eAAA,CAAgB6F,iBAAhB,CAApB;;;;;;MAMI+N,aAAA,GAAgB9I,wBAAA,CAAyB,WAAzB,CAAtB;MACM+I,YAAA,GAAepK,IAAA,CAAK+D,QAAL,CAAc7H,MAAd,CAAqB0F,KAA1C,CAfqD;MAgB7C/I,GAhB6C,GAgBHuR,YAhBG,CAgB7CvR,GAhB6C;IAgBxCE,IAhBwC,GAgBHqR,YAhBG,CAgBxCrR,IAhBwC;IAgBjBsR,SAhBiB,GAgBHD,YAhBG,CAgBjCD,aAhBiC;eAiBxCtR,GAAb,GAAmB,EAAnB;eACaE,IAAb,GAAoB,EAApB;eACaoR,aAAb,IAA8B,EAA9B;MAEM9N,UAAA,GAAaJ,aAAA,CACjB+D,IAAA,CAAK+D,QAAL,CAAc7H,MADG,EAEjB8D,IAAA,CAAK+D,QAAL,CAAchO,SAFG,EAGjB0K,OAAA,CAAQtE,OAHS,EAIjBC,iBAJiB,EAKjB4D,IAAA,CAAKU,aALY,CAAnB;;;;eAUa7H,GAAb,GAAmBA,GAAnB;eACaE,IAAb,GAAoBA,IAApB;eACaoR,aAAb,IAA8BE,SAA9B;UAEQhO,UAAR,GAAqBA,UAArB;MAEMlF,KAAA,GAAQsJ,OAAA,CAAQ6J,QAAtB;MACIpO,MAAA,GAAS8D,IAAA,CAAKlG,OAAL,CAAaoC,MAA1B;MAEMmD,KAAA,GAAQ;WAAA,WAAAkL,QACJ3N,SADI,EACO;UACb8C,KAAA,GAAQxD,MAAA,CAAOU,SAAP,CAAZ;UAEEV,MAAA,CAAOU,SAAP,IAAoBP,UAAA,CAAWO,SAAX,CAApB,IACA,CAAC6D,OAAA,CAAQ+J,mBAFX,EAGE;gBACQ/Q,IAAA,CAAKC,GAAL,CAASwC,MAAA,CAAOU,SAAP,CAAT,EAA4BP,UAAA,CAAWO,SAAX,CAA5B,CAAR;;gCAEQA,SAAV,EAAsB8C,KAAtB;KATU;aAAA,WAAA+K,UAWF7N,SAXE,EAWS;UACbmC,QAAA,GAAWnC,SAAA,KAAc,OAAd,GAAwB,MAAxB,GAAiC,KAAlD;UACI8C,KAAA,GAAQxD,MAAA,CAAO6C,QAAP,CAAZ;UAEE7C,MAAA,CAAOU,SAAP,IAAoBP,UAAA,CAAWO,SAAX,CAApB,IACA,CAAC6D,OAAA,CAAQ+J,mBAFX,EAGE;gBACQ/Q,IAAA,CAAKuN,GAAL,CACN9K,MAAA,CAAO6C,QAAP,CADM,EAEN1C,UAAA,CAAWO,SAAX,KACGA,SAAA,KAAc,OAAd,GAAwBV,MAAA,CAAOnC,KAA/B,GAAuCmC,MAAA,CAAOlC,MADjD,CAFM,CAAR;;gCAMQ+E,QAAV,EAAqBW,KAArB;;GAxBJ;QA4BMU,OAAN,CAAc,UAAAxD,SAAA,EAAa;QACnB3E,IAAA,GACJ,CAAC,MAAD,EAAS,KAAT,EAAgBtE,OAAhB,CAAwBiJ,SAAxB,MAAuC,CAAC,CAAxC,GAA4C,SAA5C,GAAwD,WAD1D;0BAEcV,MAAd,EAAyBmD,KAAA,CAAMpH,IAAN,EAAY2E,SAAZ,CAAzB;GAHF;OAMK9C,OAAL,CAAaoC,MAAb,GAAsBA,MAAtB;SAEO8D,IAAP;;;ACvFF;;;;;;;AAOA,SAAwB0K,KAATA,CAAe1K,IAAf,EAAqB;MAC5BpD,SAAA,GAAYoD,IAAA,CAAKpD,SAAvB;MACMyM,aAAA,GAAgBzM,SAAA,CAAUiB,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAtB;MACM8M,cAAA,GAAiB/N,SAAA,CAAUiB,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAvB;;;MAGI8M,cAAJ,EAAoB;wBACY3K,IAAA,CAAKlG,OADjB;MACV/D,SADU,GAAAuO,aAAA,CACVvO,SADU;MACCmG,MADD,GAAAoI,aAAA,CACCpI,MADD;QAEZ2I,UAAA,GAAa,CAAC,QAAD,EAAW,KAAX,EAAkBlR,OAAlB,CAA0B0V,aAA1B,MAA6C,CAAC,CAAjE;QACMpR,IAAA,GAAO4M,UAAA,GAAa,MAAb,GAAsB,KAAnC;QACM5F,WAAA,GAAc4F,UAAA,GAAa,OAAb,GAAuB,QAA3C;QAEM+F,YAAA,GAAe;gCACT3S,IAAV,EAAiBlC,SAAA,CAAUkC,IAAV,CAAjB,CADmB;8BAGhBA,IADH,EACUlC,SAAA,CAAUkC,IAAV,IAAkBlC,SAAA,CAAUkJ,WAAV,CAAlB,GAA2C/C,MAAA,CAAO+C,WAAP,CADrD;KAFF;SAOKnF,OAAL,CAAaoC,MAAb,GAAA0J,QAAA,KAA2B1J,MAA3B,EAAsC0O,YAAA,CAAaD,cAAb,CAAtC;;SAGK3K,IAAP;;;AC1BF;;;;;;;AAOA,SAAwB6K,IAATA,CAAc7K,IAAd,EAAoB;MAC7B,CAAC8F,kBAAA,CAAmB9F,IAAA,CAAK+D,QAAL,CAAchE,SAAjC,EAA4C,MAA5C,EAAoD,iBAApD,CAAL,EAA6E;WACpEC,IAAP;;MAGInD,OAAA,GAAUmD,IAAA,CAAKlG,OAAL,CAAa/D,SAA7B;MACM+U,KAAA,GAAQ3L,IAAA,CACZa,IAAA,CAAK+D,QAAL,CAAchE,SADF,EAEZ,UAAAnH,QAAA;WAAYA,QAAA,CAASwI,IAAT,KAAkB,iBAA9B;GAFY,EAGZ/E,UAHF;MAMEQ,OAAA,CAAQ/D,MAAR,GAAiBgS,KAAA,CAAMjS,GAAvB,IACAgE,OAAA,CAAQ9D,IAAR,GAAe+R,KAAA,CAAM9R,KADrB,IAEA6D,OAAA,CAAQhE,GAAR,GAAciS,KAAA,CAAMhS,MAFpB,IAGA+D,OAAA,CAAQ7D,KAAR,GAAgB8R,KAAA,CAAM/R,IAJxB,EAKE;;QAEIiH,IAAA,CAAK6K,IAAL,KAAc,IAAlB,EAAwB;aACf7K,IAAP;;SAGG6K,IAAL,GAAY,IAAZ;SACKjH,UAAL,CAAgB,qBAAhB,IAAyC,EAAzC;GAZF,MAaO;;QAED5D,IAAA,CAAK6K,IAAL,KAAc,KAAlB,EAAyB;aAChB7K,IAAP;;SAGG6K,IAAL,GAAY,KAAZ;SACKjH,UAAL,CAAgB,qBAAhB,IAAyC,KAAzC;;SAGK5D,IAAP;;;ACzCF;;;;;;;AAOA,SAAwB+K,KAATA,CAAe/K,IAAf,EAAqB;MAC5BpD,SAAA,GAAYoD,IAAA,CAAKpD,SAAvB;MACMyM,aAAA,GAAgBzM,SAAA,CAAUiB,KAAV,CAAgB,GAAhB,EAAqB,CAArB,CAAtB;sBAC8BmC,IAAA,CAAKlG,OAHD;IAG1BoC,MAH0B,GAAAoI,aAAA,CAG1BpI,MAH0B;IAGlBnG,SAHkB,GAAAuO,aAAA,CAGlBvO,SAHkB;MAI5B+I,OAAA,GAAU,CAAC,MAAD,EAAS,OAAT,EAAkBnL,OAAlB,CAA0B0V,aAA1B,MAA6C,CAAC,CAA9D;MAEM2B,cAAA,GAAiB,CAAC,KAAD,EAAQ,MAAR,EAAgBrX,OAAhB,CAAwB0V,aAAxB,MAA2C,CAAC,CAAnE;SAEOvK,OAAA,GAAU,MAAV,GAAmB,KAA1B,IACE/I,SAAA,CAAUsT,aAAV,KACC2B,cAAA,GAAiB9O,MAAA,CAAO4C,OAAA,GAAU,OAAV,GAAoB,QAA3B,CAAjB,GAAwD,CADzD,CADF;OAIKlC,SAAL,GAAiB0B,oBAAA,CAAqB1B,SAArB,CAAjB;OACK9C,OAAL,CAAaoC,MAAb,GAAsBrC,aAAA,CAAcqC,MAAd,CAAtB;SAEO8D,IAAP;;;ACdF;;;;;;;;;;;;;;;;;;;;;AAqBA,IAAAD,SAAA,GAAe;;;;;;;;;SASN;;WAEE,GAFF;;aAII,IAJJ;;QAMD2K;GAfO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;UAwDL;;WAEC,GAFD;;aAIG,IAJH;;QAMF9O,MANE;;;;YAUE;GAlEG;;;;;;;;;;;;;;;;;;mBAsFI;;WAER,GAFQ;;aAIN,IAJM;;QAMXsO,eANW;;;;;;cAYL,CAAC,MAAD,EAAS,OAAT,EAAkB,KAAlB,EAAyB,QAAzB,CAZK;;;;;;;aAmBN,CAnBM;;;;;;uBAyBI;GA/GR;;;;;;;;;;gBA2HC;;WAEL,GAFK;;aAIH,IAJG;;QAMRlB;GAjIO;;;;;;;;;;;SA8IN;;WAEE,GAFF;;aAII,IAJJ;;QAMDnD,KANC;;aAQI;GAtJE;;;;;;;;;;;;QAoKP;;WAEG,GAFH;;aAIK,IAJL;;QAMAlF,IANA;;;;;;;cAaM,MAbN;;;;;aAkBK,CAlBL;;;;;;;uBAyBe,UAzBf;;;;;;;;oBAiCY,KAjCZ;;;;;;;;6BAyCqB;GA7Md;;;;;;;;SAuNN;;WAEE,GAFF;;aAII,KAJJ;;QAMDoK;GA7NO;;;;;;;;;;;QA0OP;;WAEG,GAFH;;aAIK,IAJL;;QAMAF;GAhPO;;;;;;;;;;;;;;;;gBAkQC;;WAEL,GAFK;;aAIH,IAJG;;QAMRzF,YANQ;;;;;;qBAYK,IAZL;;;;;;OAkBT,QAlBS;;;;;;OAwBT;GA1RQ;;;;;;;;;;;;;;;;cA4SD;;WAEH,GAFG;;aAID,IAJC;;QAMNtB,UANM;;YAQFI,gBARE;;;;;;;qBAeO/L;;CA3TrB;;;;;;;;;;;;;;;;;;;;;AC9BA;;;;;;;;;;;;;;;;AAgBA,IAAA8S,QAAA,GAAe;;;;;aAKF,QALE;;;;;iBAWE,KAXF;;;;;iBAiBE,IAjBF;;;;;;mBAwBI,KAxBJ;;;;;;;YAgCH,SAAAlK,SAAA,EAAM,EAhCH;;;;;;;;;YA0CH,SAAAC,SAAA,EAAM,EA1CH;;;;;;;CAAf;;;;;;;;;;;;AClBA;AACA;AAIA,IAOqBkK,MAAA;;;;;;;;;kBASPnV,SAAZ,EAAuBmG,MAAvB,EAA6C;;QAAduE,OAAc,GAAAvI,SAAA,CAAAzE,MAAA,QAAAyE,SAAA,QAAAC,SAAA,GAAAD,SAAA,MAAJ,EAAI;;SAyF7CgL,cAzF6C,GAyF5B;aAAMiI,qBAAA,CAAsBC,KAAA,CAAK7K,MAA3B,CAAN;KAzF4B;;;SAEtCA,MAAL,GAAclM,QAAA,CAAS,KAAKkM,MAAL,CAAY8K,IAAZ,CAAiB,IAAjB,CAAT,CAAd;;;SAGK5K,OAAL,GAAAmF,QAAA,KAAoBsF,MAAA,CAAOD,QAA3B,EAAwCxK,OAAxC;;;SAGK1C,KAAL,GAAa;mBACE,KADF;iBAEA,KAFA;qBAGI;KAHjB;;;SAOKhI,SAAL,GAAiBA,SAAA,IAAaA,SAAA,CAAUuV,MAAvB,GAAgCvV,SAAA,CAAU,CAAV,CAAhC,GAA+CA,SAAhE;SACKmG,MAAL,GAAcA,MAAA,IAAUA,MAAA,CAAOoP,MAAjB,GAA0BpP,MAAA,CAAO,CAAP,CAA1B,GAAsCA,MAApD;;;SAGKuE,OAAL,CAAaV,SAAb,GAAyB,EAAzB;WACO9C,IAAP,CAAA2I,QAAA,KACKsF,MAAA,CAAOD,QAAP,CAAgBlL,SADrB,EAEKU,OAAA,CAAQV,SAFb,GAGGK,OAHH,CAGW,UAAAgB,IAAA,EAAQ;YACZX,OAAL,CAAaV,SAAb,CAAuBqB,IAAvB,IAAAwE,QAAA,KAEMsF,MAAA,CAAOD,QAAP,CAAgBlL,SAAhB,CAA0BqB,IAA1B,KAAmC,EAFzC,EAIMX,OAAA,CAAQV,SAAR,GAAoBU,OAAA,CAAQV,SAAR,CAAkBqB,IAAlB,CAApB,GAA8C,EAJpD;KAJF;;;SAaKrB,SAAL,GAAiB/C,MAAA,CAAOC,IAAP,CAAY,KAAKwD,OAAL,CAAaV,SAAzB,EACd7C,GADc,CACV,UAAAkE,IAAA;;;SAEAgK,KAAA,CAAK3K,OAAL,CAAaV,SAAb,CAAuBqB,IAAvB,CAFA;KADU;;KAMdhE,IANc,CAMT,UAACC,CAAD,EAAIC,CAAJ;aAAUD,CAAA,CAAElG,KAAF,GAAUmG,CAAA,CAAEnG,KAAtB;KANS,CAAjB;;;;;;SAYK4I,SAAL,CAAeK,OAAf,CAAuB,UAAA+D,eAAA,EAAmB;UACpCA,eAAA,CAAgB7D,OAAhB,IAA2BhM,UAAA,CAAW6P,eAAA,CAAgBoH,MAA3B,CAA/B,EAAmE;wBACjDA,MAAhB,CACEH,KAAA,CAAKrV,SADP,EAEEqV,KAAA,CAAKlP,MAFP,EAGEkP,KAAA,CAAK3K,OAHP,EAIE0D,eAJF,EAKEiH,KAAA,CAAKrN,KALP;;KAFJ;;;SAaKwC,MAAL;QAEMyC,aAAA,GAAgB,KAAKvC,OAAL,CAAauC,aAAnC;QACIA,aAAJ,EAAmB;;WAEZC,oBAAL;;SAGGlF,KAAL,CAAWiF,aAAX,GAA2BA,aAA3B;;;;;;;;gCAKO;aACAzC,MAAA,CAAO7L,IAAP,CAAY,IAAZ,CAAP;;;;iCAEQ;aACDmN,OAAA,CAAQnN,IAAR,CAAa,IAAb,CAAP;;;;8CAEqB;aACduO,oBAAA,CAAqBvO,IAArB,CAA0B,IAA1B,CAAP;;;;+CAEsB;aACfsN,qBAAA,CAAsBtN,IAAtB,CAA2B,IAA3B,CAAP;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA1FiBwW,MAAA,CAoHZM,KAAA,GAAQ,CAAC,OAAOrY,MAAP,KAAkB,WAAlB,GAAgCA,MAAhC,GAAyCsY,MAA1C,EAAkDC,WAAA;AApH9CR,MAAA,CAsHZ9D,UAAA,GAAaA,UAAA;AAtHD8D,MAAA,CAwHZD,QAAA,GAAWA,QAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}