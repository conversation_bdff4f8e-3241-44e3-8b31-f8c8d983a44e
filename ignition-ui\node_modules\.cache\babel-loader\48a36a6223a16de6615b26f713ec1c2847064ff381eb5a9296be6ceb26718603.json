{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getCardContentUtilityClass(slot) {\n  return generateUtilityClass('MuiCardContent', slot);\n}\nconst cardClasses = generateUtilityClasses('MuiCardContent', ['root']);\nexport default cardClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getCardContentUtilityClass", "slot", "cardClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/CardContent/cardContentClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getCardContentUtilityClass(slot) {\n  return generateUtilityClass('MuiCardContent', slot);\n}\nconst cardClasses = generateUtilityClasses('MuiCardContent', ['root']);\nexport default cardClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,0BAA0BA,CAACC,IAAI,EAAE;EAC/C,OAAOH,oBAAoB,CAAC,gBAAgB,EAAEG,IAAI,CAAC;AACrD;AACA,MAAMC,WAAW,GAAGH,sBAAsB,CAAC,gBAAgB,EAAE,CAAC,MAAM,CAAC,CAAC;AACtE,eAAeG,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}