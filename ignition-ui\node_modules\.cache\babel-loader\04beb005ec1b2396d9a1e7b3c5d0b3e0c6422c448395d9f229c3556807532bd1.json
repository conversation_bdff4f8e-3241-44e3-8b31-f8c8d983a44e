{"ast": null, "code": "'use strict';\n\nvar getPolyfill = require('./polyfill');\nvar define = require('define-properties');\nmodule.exports = function shimObjectIs() {\n  var polyfill = getPolyfill();\n  define(Object, {\n    is: polyfill\n  }, {\n    is: function testObjectIs() {\n      return Object.is !== polyfill;\n    }\n  });\n  return polyfill;\n};", "map": {"version": 3, "names": ["getPolyfill", "require", "define", "module", "exports", "shimObjectIs", "polyfill", "Object", "is", "testObjectIs"], "sources": ["C:/ignition/ignition-ui/node_modules/object-is/shim.js"], "sourcesContent": ["'use strict';\n\nvar getPolyfill = require('./polyfill');\nvar define = require('define-properties');\n\nmodule.exports = function shimObjectIs() {\n\tvar polyfill = getPolyfill();\n\tdefine(Object, { is: polyfill }, {\n\t\tis: function testObjectIs() {\n\t\t\treturn Object.is !== polyfill;\n\t\t}\n\t});\n\treturn polyfill;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,WAAW,GAAGC,OAAO,CAAC,YAAY,CAAC;AACvC,IAAIC,MAAM,GAAGD,OAAO,CAAC,mBAAmB,CAAC;AAEzCE,MAAM,CAACC,OAAO,GAAG,SAASC,YAAYA,CAAA,EAAG;EACxC,IAAIC,QAAQ,GAAGN,WAAW,CAAC,CAAC;EAC5BE,MAAM,CAACK,MAAM,EAAE;IAAEC,EAAE,EAAEF;EAAS,CAAC,EAAE;IAChCE,EAAE,EAAE,SAASC,YAAYA,CAAA,EAAG;MAC3B,OAAOF,MAAM,CAACC,EAAE,KAAKF,QAAQ;IAC9B;EACD,CAAC,CAAC;EACF,OAAOA,QAAQ;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}