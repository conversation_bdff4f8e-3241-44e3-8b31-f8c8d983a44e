{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"component\", \"size\", \"children\", \"orientation\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport { unstable_capitalize as capitalize } from '@mui/utils';\nimport { useThemeProps } from '../styles';\nimport styled from '../styles/styled';\nimport { getStepperUtilityClass } from './stepperClasses';\nimport useSlot from '../utils/useSlot';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    size,\n    orientation\n  } = ownerState;\n  const slots = {\n    root: ['root', orientation, size && `size${capitalize(size)}`]\n  };\n  return composeClasses(slots, getStepperUtilityClass, {});\n};\nconst StepperRoot = styled('ol', {\n  name: 'JoyStepper',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    '--Stepper-indicatorColumn': 'auto',\n    // For vertical stepper, to control the column width of the indicator.\n    '--Step-connectorThickness': '1px',\n    '--Step-indicatorDotSize': '0.375rem',\n    boxSizing: 'border-box',\n    display: 'flex',\n    margin: 0,\n    // Reset browser default style.\n    padding: 0,\n    // Reset browser default style.\n    variants: [{\n      props: {\n        size: 'sm'\n      },\n      style: _extends({\n        '--Stepper-verticalGap': '0.5rem',\n        '--Step-gap': '0.375rem',\n        '--Step-connectorInset': '0.25rem',\n        '--StepIndicator-size': '1.25rem'\n      }, theme.typography['title-sm'])\n    }, {\n      props: {\n        size: 'md'\n      },\n      style: _extends({\n        '--Stepper-verticalGap': '0.75rem',\n        '--Step-gap': '0.5rem',\n        '--Step-connectorInset': '0.375rem',\n        '--StepIndicator-size': '1.5rem'\n      }, theme.typography['title-md'])\n    }, {\n      props: {\n        size: 'lg'\n      },\n      style: _extends({\n        '--Stepper-verticalGap': '0.75rem',\n        '--Step-gap': '0.5rem',\n        '--Step-connectorInset': '0.5rem',\n        '--StepIndicator-size': '1.75rem'\n      }, theme.typography['title-lg'])\n    }, {\n      props: {\n        orientation: 'vertical'\n      },\n      style: {\n        flexDirection: 'column',\n        gap: 'var(--Stepper-verticalGap)'\n      }\n    }]\n  };\n});\n\n/**\n *\n * Demos:\n *\n * - [Stepper](https://mui.com/joy-ui/react-stepper/)\n *\n * API:\n *\n * - [Stepper API](https://mui.com/joy-ui/api/stepper/)\n */\nconst Stepper = /*#__PURE__*/React.forwardRef(function Stepper(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyStepper'\n  });\n  const {\n      className,\n      component = 'ol',\n      size = 'md',\n      children,\n      orientation = 'horizontal',\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    component,\n    orientation,\n    size\n  });\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    className: clsx(classes.root, className),\n    elementType: StepperRoot,\n    externalForwardedProps,\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(SlotRoot, _extends({}, rootProps, {\n    children: React.Children.map(children, (child, index) => {\n      if (! /*#__PURE__*/React.isValidElement(child)) {\n        return child;\n      }\n      const extraProps = {};\n      if (index === 0) {\n        extraProps['data-first-child'] = '';\n      }\n      if (index === React.Children.count(children) - 1) {\n        extraProps['data-last-child'] = '';\n      }\n      return /*#__PURE__*/React.cloneElement(child, extraProps);\n    })\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Stepper.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Used to render icon or text elements inside the Stepper if `src` is not set.\n   * This can be an element, or just a string.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The component orientation.\n   * @default 'horizontal'\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']),\n  /**\n   * The size of the component.\n   * It accepts theme values between 'sm' and 'lg'.\n   * @default 'md'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['lg', 'md', 'sm']), PropTypes.string]),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Stepper;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "clsx", "PropTypes", "unstable_composeClasses", "composeClasses", "unstable_capitalize", "capitalize", "useThemeProps", "styled", "getStepperUtilityClass", "useSlot", "jsx", "_jsx", "useUtilityClasses", "ownerState", "size", "orientation", "slots", "root", "StepperRoot", "name", "slot", "overridesResolver", "props", "styles", "_ref", "theme", "boxSizing", "display", "margin", "padding", "variants", "style", "typography", "flexDirection", "gap", "Stepper", "forwardRef", "inProps", "ref", "className", "component", "children", "slotProps", "other", "classes", "externalForwardedProps", "SlotRoot", "rootProps", "elementType", "Children", "map", "child", "index", "isValidElement", "extraProps", "count", "cloneElement", "process", "env", "NODE_ENV", "propTypes", "node", "string", "oneOf", "oneOfType", "shape", "func", "object", "sx", "arrayOf", "bool"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Stepper/Stepper.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"component\", \"size\", \"children\", \"orientation\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport { unstable_capitalize as capitalize } from '@mui/utils';\nimport { useThemeProps } from '../styles';\nimport styled from '../styles/styled';\nimport { getStepperUtilityClass } from './stepperClasses';\nimport useSlot from '../utils/useSlot';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    size,\n    orientation\n  } = ownerState;\n  const slots = {\n    root: ['root', orientation, size && `size${capitalize(size)}`]\n  };\n  return composeClasses(slots, getStepperUtilityClass, {});\n};\nconst StepperRoot = styled('ol', {\n  name: 'JoyStepper',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(({\n  theme\n}) => {\n  return {\n    '--Stepper-indicatorColumn': 'auto',\n    // For vertical stepper, to control the column width of the indicator.\n    '--Step-connectorThickness': '1px',\n    '--Step-indicatorDotSize': '0.375rem',\n    boxSizing: 'border-box',\n    display: 'flex',\n    margin: 0,\n    // Reset browser default style.\n    padding: 0,\n    // Reset browser default style.\n    variants: [{\n      props: {\n        size: 'sm'\n      },\n      style: _extends({\n        '--Stepper-verticalGap': '0.5rem',\n        '--Step-gap': '0.375rem',\n        '--Step-connectorInset': '0.25rem',\n        '--StepIndicator-size': '1.25rem'\n      }, theme.typography['title-sm'])\n    }, {\n      props: {\n        size: 'md'\n      },\n      style: _extends({\n        '--Stepper-verticalGap': '0.75rem',\n        '--Step-gap': '0.5rem',\n        '--Step-connectorInset': '0.375rem',\n        '--StepIndicator-size': '1.5rem'\n      }, theme.typography['title-md'])\n    }, {\n      props: {\n        size: 'lg'\n      },\n      style: _extends({\n        '--Stepper-verticalGap': '0.75rem',\n        '--Step-gap': '0.5rem',\n        '--Step-connectorInset': '0.5rem',\n        '--StepIndicator-size': '1.75rem'\n      }, theme.typography['title-lg'])\n    }, {\n      props: {\n        orientation: 'vertical'\n      },\n      style: {\n        flexDirection: 'column',\n        gap: 'var(--Stepper-verticalGap)'\n      }\n    }]\n  };\n});\n\n/**\n *\n * Demos:\n *\n * - [Stepper](https://mui.com/joy-ui/react-stepper/)\n *\n * API:\n *\n * - [Stepper API](https://mui.com/joy-ui/api/stepper/)\n */\nconst Stepper = /*#__PURE__*/React.forwardRef(function Stepper(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyStepper'\n  });\n  const {\n      className,\n      component = 'ol',\n      size = 'md',\n      children,\n      orientation = 'horizontal',\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    component,\n    orientation,\n    size\n  });\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    className: clsx(classes.root, className),\n    elementType: StepperRoot,\n    externalForwardedProps,\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(SlotRoot, _extends({}, rootProps, {\n    children: React.Children.map(children, (child, index) => {\n      if (! /*#__PURE__*/React.isValidElement(child)) {\n        return child;\n      }\n      const extraProps = {};\n      if (index === 0) {\n        extraProps['data-first-child'] = '';\n      }\n      if (index === React.Children.count(children) - 1) {\n        extraProps['data-last-child'] = '';\n      }\n      return /*#__PURE__*/React.cloneElement(child, extraProps);\n    })\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Stepper.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Used to render icon or text elements inside the Stepper if `src` is not set.\n   * This can be an element, or just a string.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The component orientation.\n   * @default 'horizontal'\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']),\n  /**\n   * The size of the component.\n   * It accepts theme values between 'sm' and 'lg'.\n   * @default 'md'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['lg', 'md', 'sm']), PropTypes.string]),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Stepper;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,OAAO,EAAE,WAAW,CAAC;AACrG,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,WAAW;AACrE,SAASC,mBAAmB,IAAIC,UAAU,QAAQ,YAAY;AAC9D,SAASC,aAAa,QAAQ,WAAW;AACzC,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,sBAAsB,QAAQ,kBAAkB;AACzD,OAAOC,OAAO,MAAM,kBAAkB;AACtC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,IAAI;IACJC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEF,WAAW,EAAED,IAAI,IAAI,OAAOT,UAAU,CAACS,IAAI,CAAC,EAAE;EAC/D,CAAC;EACD,OAAOX,cAAc,CAACa,KAAK,EAAER,sBAAsB,EAAE,CAAC,CAAC,CAAC;AAC1D,CAAC;AACD,MAAMU,WAAW,GAAGX,MAAM,CAAC,IAAI,EAAE;EAC/BY,IAAI,EAAE,YAAY;EAClBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC/C,CAAC,CAAC,CAACO,IAAA,IAEG;EAAA,IAFF;IACFC;EACF,CAAC,GAAAD,IAAA;EACC,OAAO;IACL,2BAA2B,EAAE,MAAM;IACnC;IACA,2BAA2B,EAAE,KAAK;IAClC,yBAAyB,EAAE,UAAU;IACrCE,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,MAAM;IACfC,MAAM,EAAE,CAAC;IACT;IACAC,OAAO,EAAE,CAAC;IACV;IACAC,QAAQ,EAAE,CAAC;MACTR,KAAK,EAAE;QACLR,IAAI,EAAE;MACR,CAAC;MACDiB,KAAK,EAAElC,QAAQ,CAAC;QACd,uBAAuB,EAAE,QAAQ;QACjC,YAAY,EAAE,UAAU;QACxB,uBAAuB,EAAE,SAAS;QAClC,sBAAsB,EAAE;MAC1B,CAAC,EAAE4B,KAAK,CAACO,UAAU,CAAC,UAAU,CAAC;IACjC,CAAC,EAAE;MACDV,KAAK,EAAE;QACLR,IAAI,EAAE;MACR,CAAC;MACDiB,KAAK,EAAElC,QAAQ,CAAC;QACd,uBAAuB,EAAE,SAAS;QAClC,YAAY,EAAE,QAAQ;QACtB,uBAAuB,EAAE,UAAU;QACnC,sBAAsB,EAAE;MAC1B,CAAC,EAAE4B,KAAK,CAACO,UAAU,CAAC,UAAU,CAAC;IACjC,CAAC,EAAE;MACDV,KAAK,EAAE;QACLR,IAAI,EAAE;MACR,CAAC;MACDiB,KAAK,EAAElC,QAAQ,CAAC;QACd,uBAAuB,EAAE,SAAS;QAClC,YAAY,EAAE,QAAQ;QACtB,uBAAuB,EAAE,QAAQ;QACjC,sBAAsB,EAAE;MAC1B,CAAC,EAAE4B,KAAK,CAACO,UAAU,CAAC,UAAU,CAAC;IACjC,CAAC,EAAE;MACDV,KAAK,EAAE;QACLP,WAAW,EAAE;MACf,CAAC;MACDgB,KAAK,EAAE;QACLE,aAAa,EAAE,QAAQ;QACvBC,GAAG,EAAE;MACP;IACF,CAAC;EACH,CAAC;AACH,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,OAAO,GAAG,aAAapC,KAAK,CAACqC,UAAU,CAAC,SAASD,OAAOA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC3E,MAAMhB,KAAK,GAAGhB,aAAa,CAAC;IAC1BgB,KAAK,EAAEe,OAAO;IACdlB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFoB,SAAS;MACTC,SAAS,GAAG,IAAI;MAChB1B,IAAI,GAAG,IAAI;MACX2B,QAAQ;MACR1B,WAAW,GAAG,YAAY;MAC1BC,KAAK,GAAG,CAAC,CAAC;MACV0B,SAAS,GAAG,CAAC;IACf,CAAC,GAAGpB,KAAK;IACTqB,KAAK,GAAG/C,6BAA6B,CAAC0B,KAAK,EAAExB,SAAS,CAAC;EACzD,MAAMe,UAAU,GAAGhB,QAAQ,CAAC,CAAC,CAAC,EAAEyB,KAAK,EAAE;IACrCkB,SAAS;IACTzB,WAAW;IACXD;EACF,CAAC,CAAC;EACF,MAAM8B,OAAO,GAAGhC,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMgC,sBAAsB,GAAGhD,QAAQ,CAAC,CAAC,CAAC,EAAE8C,KAAK,EAAE;IACjDH,SAAS;IACTxB,KAAK;IACL0B;EACF,CAAC,CAAC;EACF,MAAM,CAACI,QAAQ,EAAEC,SAAS,CAAC,GAAGtC,OAAO,CAAC,MAAM,EAAE;IAC5C6B,GAAG;IACHC,SAAS,EAAEvC,IAAI,CAAC4C,OAAO,CAAC3B,IAAI,EAAEsB,SAAS,CAAC;IACxCS,WAAW,EAAE9B,WAAW;IACxB2B,sBAAsB;IACtBhC;EACF,CAAC,CAAC;EACF,OAAO,aAAaF,IAAI,CAACmC,QAAQ,EAAEjD,QAAQ,CAAC,CAAC,CAAC,EAAEkD,SAAS,EAAE;IACzDN,QAAQ,EAAE1C,KAAK,CAACkD,QAAQ,CAACC,GAAG,CAACT,QAAQ,EAAE,CAACU,KAAK,EAAEC,KAAK,KAAK;MACvD,IAAI,EAAE,aAAarD,KAAK,CAACsD,cAAc,CAACF,KAAK,CAAC,EAAE;QAC9C,OAAOA,KAAK;MACd;MACA,MAAMG,UAAU,GAAG,CAAC,CAAC;MACrB,IAAIF,KAAK,KAAK,CAAC,EAAE;QACfE,UAAU,CAAC,kBAAkB,CAAC,GAAG,EAAE;MACrC;MACA,IAAIF,KAAK,KAAKrD,KAAK,CAACkD,QAAQ,CAACM,KAAK,CAACd,QAAQ,CAAC,GAAG,CAAC,EAAE;QAChDa,UAAU,CAAC,iBAAiB,CAAC,GAAG,EAAE;MACpC;MACA,OAAO,aAAavD,KAAK,CAACyD,YAAY,CAACL,KAAK,EAAEG,UAAU,CAAC;IAC3D,CAAC;EACH,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGxB,OAAO,CAACyB,SAAS,CAAC,yBAAyB;EACjF;EACA;EACA;EACA;EACA;AACF;AACA;AACA;EACEnB,QAAQ,EAAExC,SAAS,CAAC4D,IAAI;EACxB;AACF;AACA;EACEtB,SAAS,EAAEtC,SAAS,CAAC6D,MAAM;EAC3B;AACF;AACA;AACA;EACEtB,SAAS,EAAEvC,SAAS,CAAC+C,WAAW;EAChC;AACF;AACA;AACA;EACEjC,WAAW,EAAEd,SAAS,CAAC8D,KAAK,CAAC,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;EACxD;AACF;AACA;AACA;AACA;EACEjD,IAAI,EAAEb,SAAS,CAAC,sCAAsC+D,SAAS,CAAC,CAAC/D,SAAS,CAAC8D,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE9D,SAAS,CAAC6D,MAAM,CAAC,CAAC;EACxH;AACF;AACA;AACA;EACEpB,SAAS,EAAEzC,SAAS,CAACgE,KAAK,CAAC;IACzBhD,IAAI,EAAEhB,SAAS,CAAC+D,SAAS,CAAC,CAAC/D,SAAS,CAACiE,IAAI,EAAEjE,SAAS,CAACkE,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEnD,KAAK,EAAEf,SAAS,CAACgE,KAAK,CAAC;IACrBhD,IAAI,EAAEhB,SAAS,CAAC+C;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACEoB,EAAE,EAAEnE,SAAS,CAAC+D,SAAS,CAAC,CAAC/D,SAAS,CAACoE,OAAO,CAACpE,SAAS,CAAC+D,SAAS,CAAC,CAAC/D,SAAS,CAACiE,IAAI,EAAEjE,SAAS,CAACkE,MAAM,EAAElE,SAAS,CAACqE,IAAI,CAAC,CAAC,CAAC,EAAErE,SAAS,CAACiE,IAAI,EAAEjE,SAAS,CAACkE,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAehC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}