{"ast": null, "code": "import { globalStateClasses } from '@mui/utils/generateUtilityClass';\nconst GLOBAL_CLASS_PREFIX = 'base';\nfunction buildStateClass(state) {\n  return `${GLOBAL_CLASS_PREFIX}--${state}`;\n}\nfunction buildSlotClass(componentName, slot) {\n  return `${GLOBAL_CLASS_PREFIX}-${componentName}-${slot}`;\n}\nexport function generateUtilityClass(componentName, slot) {\n  const globalStateClass = globalStateClasses[slot];\n  return globalStateClass ? buildStateClass(globalStateClass) : buildSlotClass(componentName, slot);\n}\nexport function isGlobalState(slot) {\n  return globalStateClasses[slot] !== undefined;\n}", "map": {"version": 3, "names": ["globalStateClasses", "GLOBAL_CLASS_PREFIX", "buildStateClass", "state", "buildSlotClass", "componentName", "slot", "generateUtilityClass", "globalStateClass", "isGlobalState", "undefined"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/generateUtilityClass/index.js"], "sourcesContent": ["import { globalStateClasses } from '@mui/utils/generateUtilityClass';\nconst GLOBAL_CLASS_PREFIX = 'base';\nfunction buildStateClass(state) {\n  return `${GLOBAL_CLASS_PREFIX}--${state}`;\n}\nfunction buildSlotClass(componentName, slot) {\n  return `${GLOBAL_CLASS_PREFIX}-${componentName}-${slot}`;\n}\nexport function generateUtilityClass(componentName, slot) {\n  const globalStateClass = globalStateClasses[slot];\n  return globalStateClass ? buildStateClass(globalStateClass) : buildSlotClass(componentName, slot);\n}\nexport function isGlobalState(slot) {\n  return globalStateClasses[slot] !== undefined;\n}"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,iCAAiC;AACpE,MAAMC,mBAAmB,GAAG,MAAM;AAClC,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC9B,OAAO,GAAGF,mBAAmB,KAAKE,KAAK,EAAE;AAC3C;AACA,SAASC,cAAcA,CAACC,aAAa,EAAEC,IAAI,EAAE;EAC3C,OAAO,GAAGL,mBAAmB,IAAII,aAAa,IAAIC,IAAI,EAAE;AAC1D;AACA,OAAO,SAASC,oBAAoBA,CAACF,aAAa,EAAEC,IAAI,EAAE;EACxD,MAAME,gBAAgB,GAAGR,kBAAkB,CAACM,IAAI,CAAC;EACjD,OAAOE,gBAAgB,GAAGN,eAAe,CAACM,gBAAgB,CAAC,GAAGJ,cAAc,CAACC,aAAa,EAAEC,IAAI,CAAC;AACnG;AACA,OAAO,SAASG,aAAaA,CAACH,IAAI,EAAE;EAClC,OAAON,kBAAkB,CAACM,IAAI,CAAC,KAAKI,SAAS;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}