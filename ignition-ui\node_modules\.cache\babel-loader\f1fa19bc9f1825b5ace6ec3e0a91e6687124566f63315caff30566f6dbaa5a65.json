{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"component\", \"className\", \"children\", \"size\", \"orientation\", \"wrap\", \"variant\", \"color\", \"role\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { unstable_capitalize as capitalize } from '@mui/utils';\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport { styled, useThemeProps } from '../styles';\nimport { resolveSxValue } from '../styles/styleUtils';\nimport { getListUtilityClass } from './listClasses';\nimport NestedListContext from './NestedListContext';\nimport ComponentListContext from './ComponentListContext';\nimport GroupListContext from './GroupListContext';\nimport ListProvider from './ListProvider';\nimport RadioGroupContext from '../RadioGroup/RadioGroupContext';\nimport useSlot from '../utils/useSlot';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    variant,\n    color,\n    size,\n    nesting,\n    orientation,\n    instanceSize\n  } = ownerState;\n  const slots = {\n    root: ['root', orientation, variant && `variant${capitalize(variant)}`, color && `color${capitalize(color)}`, !instanceSize && !nesting && size && `size${capitalize(size)}`, instanceSize && `size${capitalize(instanceSize)}`, nesting && 'nesting']\n  };\n  return composeClasses(slots, getListUtilityClass, {});\n};\nexport const StyledList = styled('ul')(_ref => {\n  let {\n    theme,\n    ownerState\n  } = _ref;\n  var _theme$variants;\n  const {\n    p,\n    padding,\n    borderRadius\n  } = resolveSxValue({\n    theme,\n    ownerState\n  }, ['p', 'padding', 'borderRadius']);\n  function applySizeVars(size) {\n    if (size === 'sm') {\n      return {\n        '--ListDivider-gap': '0.25rem',\n        '--ListItem-minHeight': '2rem',\n        '--ListItem-paddingY': '3px',\n        '--ListItem-paddingX': ownerState.marker ? '3px' : '0.5rem',\n        '--ListItem-gap': '0.5rem',\n        '--ListItemDecorator-size': ownerState.orientation === 'horizontal' ? '1.5rem' : '2rem',\n        '--Icon-fontSize': theme.vars.fontSize.lg\n      };\n    }\n    if (size === 'md') {\n      return {\n        '--ListDivider-gap': '0.375rem',\n        '--ListItem-minHeight': '2.25rem',\n        '--ListItem-paddingY': '0.25rem',\n        '--ListItem-paddingX': ownerState.marker ? '0.25rem' : '0.75rem',\n        '--ListItem-gap': '0.625rem',\n        '--ListItemDecorator-size': ownerState.orientation === 'horizontal' ? '1.75rem' : '2.5rem',\n        '--Icon-fontSize': theme.vars.fontSize.xl\n      };\n    }\n    if (size === 'lg') {\n      return {\n        '--ListDivider-gap': '0.5rem',\n        '--ListItem-minHeight': '2.75rem',\n        '--ListItem-paddingY': '0.375rem',\n        '--ListItem-paddingX': ownerState.marker ? '0.5rem' : '1rem',\n        '--ListItem-gap': '0.75rem',\n        '--ListItemDecorator-size': ownerState.orientation === 'horizontal' ? '2.25rem' : '3rem',\n        '--Icon-fontSize': theme.vars.fontSize.xl2\n      };\n    }\n    return {};\n  }\n  return [ownerState.nesting && _extends({}, applySizeVars(ownerState.instanceSize), {\n    '--ListItem-paddingRight': 'var(--ListItem-paddingX)',\n    '--ListItem-paddingLeft': 'var(--NestedListItem-paddingLeft)',\n    // reset ListItem, ListItemButton negative margin (caused by NestedListItem)\n    '--ListItemButton-marginBlock': '0px',\n    '--ListItemButton-marginInline': '0px',\n    '--ListItem-marginBlock': '0px',\n    '--ListItem-marginInline': '0px',\n    padding: 0\n  }, ownerState.marker && {\n    paddingInlineStart: 'calc(3ch - var(--_List-markerDeduct, 0px))' // the width of the marker\n  }, {\n    marginInlineStart: 'var(--NestedList-marginLeft)',\n    marginInlineEnd: 'var(--NestedList-marginRight)',\n    marginBlockStart: 'var(--List-gap)',\n    marginBlockEnd: 'initial' // reset user agent stylesheet.\n  }), !ownerState.nesting && _extends({}, applySizeVars(ownerState.size), {\n    '--List-gap': '0px',\n    '--List-nestedInsetStart': '0px',\n    '--ListItem-paddingLeft': 'var(--ListItem-paddingX)',\n    '--ListItem-paddingRight': 'var(--ListItem-paddingX)'\n  }, ownerState.marker && {\n    '--_List-markerDeduct': '1ch'\n  }, {\n    // Automatic radius adjustment kicks in only if '--List-padding' and '--List-radius' are provided.\n    '--unstable_List-childRadius': 'calc(max(var(--List-radius) - var(--List-padding), min(var(--List-padding) / 2, var(--List-radius) / 2)) - var(--variant-borderWidth, 0px))',\n    '--ListItem-radius': 'var(--unstable_List-childRadius)',\n    // by default, The ListItem & ListItemButton use automatic radius adjustment based on the parent List.\n    '--ListItem-startActionTranslateX': 'calc(0.5 * var(--ListItem-paddingLeft))',\n    '--ListItem-endActionTranslateX': 'calc(-0.5 * var(--ListItem-paddingRight))',\n    margin: 'initial'\n  }, theme.typography[`body-${ownerState.size}`], ownerState.orientation === 'horizontal' ? _extends({}, ownerState.wrap ? {\n    padding: 'var(--List-padding)',\n    // Fallback is not needed for row-wrap List\n    marginInlineStart: 'calc(-1 * var(--List-gap))',\n    marginBlockStart: 'calc(-1 * var(--List-gap))'\n  } : {\n    paddingInline: 'var(--List-padding, var(--ListDivider-gap))',\n    paddingBlock: 'var(--List-padding)'\n  }) : {\n    paddingBlock: 'var(--List-padding, var(--ListDivider-gap))',\n    paddingInline: 'var(--List-padding)'\n  }, ownerState.marker && {\n    paddingInlineStart: '3ch' // the width of the marker\n  }), _extends({\n    boxSizing: 'border-box',\n    borderRadius: 'var(--List-radius)',\n    listStyle: 'none',\n    display: 'flex',\n    flexDirection: ownerState.orientation === 'horizontal' ? 'row' : 'column'\n  }, ownerState.wrap && {\n    flexWrap: 'wrap'\n  }, ownerState.marker && {\n    '--_List-markerDisplay': 'list-item',\n    '--_List-markerType': ownerState.marker,\n    lineHeight: 'calc(var(--ListItem-minHeight) - 2 * var(--ListItem-paddingY))'\n  }, {\n    flexGrow: 1,\n    position: 'relative'\n  }, (_theme$variants = theme.variants[ownerState.variant]) == null ? void 0 : _theme$variants[ownerState.color], {\n    '--unstable_List-borderWidth': 'var(--variant-borderWidth, 0px)'\n  }, borderRadius !== undefined && {\n    '--List-radius': borderRadius\n  }, p !== undefined && {\n    '--List-padding': p\n  }, padding !== undefined && {\n    '--List-padding': padding\n  })];\n});\nconst ListRoot = styled(StyledList, {\n  name: 'JoyList',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({});\n/**\n *\n * Demos:\n *\n * - [Lists](https://mui.com/joy-ui/react-list/)\n *\n * API:\n *\n * - [List API](https://mui.com/joy-ui/api/list/)\n */\nconst List = /*#__PURE__*/React.forwardRef(function List(inProps, ref) {\n  var _inProps$size;\n  const nesting = React.useContext(NestedListContext);\n  const group = React.useContext(GroupListContext);\n  const radioGroupContext = React.useContext(RadioGroupContext);\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyList'\n  });\n  const {\n      component,\n      className,\n      children,\n      size: sizeProp,\n      orientation = 'vertical',\n      wrap = false,\n      variant = 'plain',\n      color = 'neutral',\n      role: roleProp,\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const size = sizeProp || ((_inProps$size = inProps.size) != null ? _inProps$size : 'md');\n  let role;\n  if (group) {\n    role = 'group';\n  }\n  if (radioGroupContext) {\n    role = 'presentation';\n  }\n  if (roleProp) {\n    role = roleProp;\n  }\n  const ownerState = _extends({}, props, {\n    instanceSize: inProps.size,\n    size,\n    nesting,\n    orientation,\n    wrap,\n    variant,\n    color,\n    role\n  });\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    className: clsx(classes.root, className),\n    elementType: ListRoot,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      as: component,\n      role,\n      'aria-labelledby': typeof nesting === 'string' ? nesting : undefined\n    }\n  });\n  return /*#__PURE__*/_jsx(SlotRoot, _extends({}, rootProps, {\n    children: /*#__PURE__*/_jsx(ComponentListContext.Provider, {\n      value: `${typeof component === 'string' ? component : ''}:${role || ''}`,\n      children: /*#__PURE__*/_jsx(ListProvider, {\n        row: orientation === 'horizontal',\n        wrap: wrap,\n        children: children\n      })\n    })\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? List.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component. It supports those theme colors that make sense for this component.\n   * @default 'neutral'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['danger', 'neutral', 'primary', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The marker (such as a disc, character, or custom counter style) of the list items.\n   * When this prop is specified, the List Item changes the CSS display to `list-item` in order to apply the marker.\n   *\n   * To see all available options, check out the [MDN list-style-type page](https://developer.mozilla.org/en-US/docs/Web/CSS/list-style-type).\n   */\n  marker: PropTypes.string,\n  /**\n   * The component orientation.\n   * @default 'vertical'\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']),\n  /**\n   * @ignore\n   */\n  role: PropTypes /* @typescript-to-proptypes-ignore */.string,\n  /**\n   * The size of the component (affect other nested list* components).\n   * @default 'md'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['sm', 'md', 'lg']), PropTypes.string]),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The [global variant](https://mui.com/joy-ui/main-features/global-variants/) to use.\n   * @default 'plain'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['outlined', 'plain', 'soft', 'solid']), PropTypes.string]),\n  /**\n   * Only for horizontal list.\n   * If `true`, the list sets the flex-wrap to \"wrap\" and adjust margin to have gap-like behavior (will move to `gap` in the future).\n   *\n   * @default false\n   */\n  wrap: PropTypes.bool\n} : void 0;\nexport default List;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "unstable_capitalize", "capitalize", "unstable_composeClasses", "composeClasses", "styled", "useThemeProps", "resolveSxValue", "getListUtilityClass", "NestedListContext", "ComponentListContext", "GroupListContext", "ListProvider", "RadioGroupContext", "useSlot", "jsx", "_jsx", "useUtilityClasses", "ownerState", "variant", "color", "size", "nesting", "orientation", "instanceSize", "slots", "root", "StyledList", "_ref", "theme", "_theme$variants", "p", "padding", "borderRadius", "applySizeVars", "marker", "vars", "fontSize", "lg", "xl", "xl2", "paddingInlineStart", "marginInlineStart", "marginInlineEnd", "marginBlockStart", "marginBlockEnd", "margin", "typography", "wrap", "paddingInline", "paddingBlock", "boxSizing", "listStyle", "display", "flexDirection", "flexWrap", "lineHeight", "flexGrow", "position", "variants", "undefined", "ListRoot", "name", "slot", "overridesResolver", "props", "styles", "List", "forwardRef", "inProps", "ref", "_inProps$size", "useContext", "group", "radioGroupContext", "component", "className", "children", "sizeProp", "role", "roleProp", "slotProps", "other", "classes", "externalForwardedProps", "SlotRoot", "rootProps", "elementType", "additionalProps", "as", "Provider", "value", "row", "process", "env", "NODE_ENV", "propTypes", "node", "string", "oneOfType", "oneOf", "shape", "func", "object", "sx", "arrayOf", "bool"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/List/List.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"component\", \"className\", \"children\", \"size\", \"orientation\", \"wrap\", \"variant\", \"color\", \"role\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { unstable_capitalize as capitalize } from '@mui/utils';\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport { styled, useThemeProps } from '../styles';\nimport { resolveSxValue } from '../styles/styleUtils';\nimport { getListUtilityClass } from './listClasses';\nimport NestedListContext from './NestedListContext';\nimport ComponentListContext from './ComponentListContext';\nimport GroupListContext from './GroupListContext';\nimport ListProvider from './ListProvider';\nimport RadioGroupContext from '../RadioGroup/RadioGroupContext';\nimport useSlot from '../utils/useSlot';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    variant,\n    color,\n    size,\n    nesting,\n    orientation,\n    instanceSize\n  } = ownerState;\n  const slots = {\n    root: ['root', orientation, variant && `variant${capitalize(variant)}`, color && `color${capitalize(color)}`, !instanceSize && !nesting && size && `size${capitalize(size)}`, instanceSize && `size${capitalize(instanceSize)}`, nesting && 'nesting']\n  };\n  return composeClasses(slots, getListUtilityClass, {});\n};\nexport const StyledList = styled('ul')(({\n  theme,\n  ownerState\n}) => {\n  var _theme$variants;\n  const {\n    p,\n    padding,\n    borderRadius\n  } = resolveSxValue({\n    theme,\n    ownerState\n  }, ['p', 'padding', 'borderRadius']);\n  function applySizeVars(size) {\n    if (size === 'sm') {\n      return {\n        '--ListDivider-gap': '0.25rem',\n        '--ListItem-minHeight': '2rem',\n        '--ListItem-paddingY': '3px',\n        '--ListItem-paddingX': ownerState.marker ? '3px' : '0.5rem',\n        '--ListItem-gap': '0.5rem',\n        '--ListItemDecorator-size': ownerState.orientation === 'horizontal' ? '1.5rem' : '2rem',\n        '--Icon-fontSize': theme.vars.fontSize.lg\n      };\n    }\n    if (size === 'md') {\n      return {\n        '--ListDivider-gap': '0.375rem',\n        '--ListItem-minHeight': '2.25rem',\n        '--ListItem-paddingY': '0.25rem',\n        '--ListItem-paddingX': ownerState.marker ? '0.25rem' : '0.75rem',\n        '--ListItem-gap': '0.625rem',\n        '--ListItemDecorator-size': ownerState.orientation === 'horizontal' ? '1.75rem' : '2.5rem',\n        '--Icon-fontSize': theme.vars.fontSize.xl\n      };\n    }\n    if (size === 'lg') {\n      return {\n        '--ListDivider-gap': '0.5rem',\n        '--ListItem-minHeight': '2.75rem',\n        '--ListItem-paddingY': '0.375rem',\n        '--ListItem-paddingX': ownerState.marker ? '0.5rem' : '1rem',\n        '--ListItem-gap': '0.75rem',\n        '--ListItemDecorator-size': ownerState.orientation === 'horizontal' ? '2.25rem' : '3rem',\n        '--Icon-fontSize': theme.vars.fontSize.xl2\n      };\n    }\n    return {};\n  }\n  return [ownerState.nesting && _extends({}, applySizeVars(ownerState.instanceSize), {\n    '--ListItem-paddingRight': 'var(--ListItem-paddingX)',\n    '--ListItem-paddingLeft': 'var(--NestedListItem-paddingLeft)',\n    // reset ListItem, ListItemButton negative margin (caused by NestedListItem)\n    '--ListItemButton-marginBlock': '0px',\n    '--ListItemButton-marginInline': '0px',\n    '--ListItem-marginBlock': '0px',\n    '--ListItem-marginInline': '0px',\n    padding: 0\n  }, ownerState.marker && {\n    paddingInlineStart: 'calc(3ch - var(--_List-markerDeduct, 0px))' // the width of the marker\n  }, {\n    marginInlineStart: 'var(--NestedList-marginLeft)',\n    marginInlineEnd: 'var(--NestedList-marginRight)',\n    marginBlockStart: 'var(--List-gap)',\n    marginBlockEnd: 'initial' // reset user agent stylesheet.\n  }), !ownerState.nesting && _extends({}, applySizeVars(ownerState.size), {\n    '--List-gap': '0px',\n    '--List-nestedInsetStart': '0px',\n    '--ListItem-paddingLeft': 'var(--ListItem-paddingX)',\n    '--ListItem-paddingRight': 'var(--ListItem-paddingX)'\n  }, ownerState.marker && {\n    '--_List-markerDeduct': '1ch'\n  }, {\n    // Automatic radius adjustment kicks in only if '--List-padding' and '--List-radius' are provided.\n    '--unstable_List-childRadius': 'calc(max(var(--List-radius) - var(--List-padding), min(var(--List-padding) / 2, var(--List-radius) / 2)) - var(--variant-borderWidth, 0px))',\n    '--ListItem-radius': 'var(--unstable_List-childRadius)',\n    // by default, The ListItem & ListItemButton use automatic radius adjustment based on the parent List.\n    '--ListItem-startActionTranslateX': 'calc(0.5 * var(--ListItem-paddingLeft))',\n    '--ListItem-endActionTranslateX': 'calc(-0.5 * var(--ListItem-paddingRight))',\n    margin: 'initial'\n  }, theme.typography[`body-${ownerState.size}`], ownerState.orientation === 'horizontal' ? _extends({}, ownerState.wrap ? {\n    padding: 'var(--List-padding)',\n    // Fallback is not needed for row-wrap List\n    marginInlineStart: 'calc(-1 * var(--List-gap))',\n    marginBlockStart: 'calc(-1 * var(--List-gap))'\n  } : {\n    paddingInline: 'var(--List-padding, var(--ListDivider-gap))',\n    paddingBlock: 'var(--List-padding)'\n  }) : {\n    paddingBlock: 'var(--List-padding, var(--ListDivider-gap))',\n    paddingInline: 'var(--List-padding)'\n  }, ownerState.marker && {\n    paddingInlineStart: '3ch' // the width of the marker\n  }), _extends({\n    boxSizing: 'border-box',\n    borderRadius: 'var(--List-radius)',\n    listStyle: 'none',\n    display: 'flex',\n    flexDirection: ownerState.orientation === 'horizontal' ? 'row' : 'column'\n  }, ownerState.wrap && {\n    flexWrap: 'wrap'\n  }, ownerState.marker && {\n    '--_List-markerDisplay': 'list-item',\n    '--_List-markerType': ownerState.marker,\n    lineHeight: 'calc(var(--ListItem-minHeight) - 2 * var(--ListItem-paddingY))'\n  }, {\n    flexGrow: 1,\n    position: 'relative'\n  }, (_theme$variants = theme.variants[ownerState.variant]) == null ? void 0 : _theme$variants[ownerState.color], {\n    '--unstable_List-borderWidth': 'var(--variant-borderWidth, 0px)'\n  }, borderRadius !== undefined && {\n    '--List-radius': borderRadius\n  }, p !== undefined && {\n    '--List-padding': p\n  }, padding !== undefined && {\n    '--List-padding': padding\n  })];\n});\nconst ListRoot = styled(StyledList, {\n  name: 'JoyList',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({});\n/**\n *\n * Demos:\n *\n * - [Lists](https://mui.com/joy-ui/react-list/)\n *\n * API:\n *\n * - [List API](https://mui.com/joy-ui/api/list/)\n */\nconst List = /*#__PURE__*/React.forwardRef(function List(inProps, ref) {\n  var _inProps$size;\n  const nesting = React.useContext(NestedListContext);\n  const group = React.useContext(GroupListContext);\n  const radioGroupContext = React.useContext(RadioGroupContext);\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyList'\n  });\n  const {\n      component,\n      className,\n      children,\n      size: sizeProp,\n      orientation = 'vertical',\n      wrap = false,\n      variant = 'plain',\n      color = 'neutral',\n      role: roleProp,\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const size = sizeProp || ((_inProps$size = inProps.size) != null ? _inProps$size : 'md');\n  let role;\n  if (group) {\n    role = 'group';\n  }\n  if (radioGroupContext) {\n    role = 'presentation';\n  }\n  if (roleProp) {\n    role = roleProp;\n  }\n  const ownerState = _extends({}, props, {\n    instanceSize: inProps.size,\n    size,\n    nesting,\n    orientation,\n    wrap,\n    variant,\n    color,\n    role\n  });\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    className: clsx(classes.root, className),\n    elementType: ListRoot,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      as: component,\n      role,\n      'aria-labelledby': typeof nesting === 'string' ? nesting : undefined\n    }\n  });\n  return /*#__PURE__*/_jsx(SlotRoot, _extends({}, rootProps, {\n    children: /*#__PURE__*/_jsx(ComponentListContext.Provider, {\n      value: `${typeof component === 'string' ? component : ''}:${role || ''}`,\n      children: /*#__PURE__*/_jsx(ListProvider, {\n        row: orientation === 'horizontal',\n        wrap: wrap,\n        children: children\n      })\n    })\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? List.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component. It supports those theme colors that make sense for this component.\n   * @default 'neutral'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['danger', 'neutral', 'primary', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The marker (such as a disc, character, or custom counter style) of the list items.\n   * When this prop is specified, the List Item changes the CSS display to `list-item` in order to apply the marker.\n   *\n   * To see all available options, check out the [MDN list-style-type page](https://developer.mozilla.org/en-US/docs/Web/CSS/list-style-type).\n   */\n  marker: PropTypes.string,\n  /**\n   * The component orientation.\n   * @default 'vertical'\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']),\n  /**\n   * @ignore\n   */\n  role: PropTypes /* @typescript-to-proptypes-ignore */.string,\n  /**\n   * The size of the component (affect other nested list* components).\n   * @default 'md'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['sm', 'md', 'lg']), PropTypes.string]),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The [global variant](https://mui.com/joy-ui/main-features/global-variants/) to use.\n   * @default 'plain'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['outlined', 'plain', 'soft', 'solid']), PropTypes.string]),\n  /**\n   * Only for horizontal list.\n   * If `true`, the list sets the flex-wrap to \"wrap\" and adjust margin to have gap-like behavior (will move to `gap` in the future).\n   *\n   * @default false\n   */\n  wrap: PropTypes.bool\n} : void 0;\nexport default List;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,CAAC;AACzI,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,mBAAmB,IAAIC,UAAU,QAAQ,YAAY;AAC9D,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,0BAA0B;AACpF,SAASC,MAAM,EAAEC,aAAa,QAAQ,WAAW;AACjD,SAASC,cAAc,QAAQ,sBAAsB;AACrD,SAASC,mBAAmB,QAAQ,eAAe;AACnD,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,oBAAoB,MAAM,wBAAwB;AACzD,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,iBAAiB,MAAM,iCAAiC;AAC/D,OAAOC,OAAO,MAAM,kBAAkB;AACtC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC,KAAK;IACLC,IAAI;IACJC,OAAO;IACPC,WAAW;IACXC;EACF,CAAC,GAAGN,UAAU;EACd,MAAMO,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEH,WAAW,EAAEJ,OAAO,IAAI,UAAUjB,UAAU,CAACiB,OAAO,CAAC,EAAE,EAAEC,KAAK,IAAI,QAAQlB,UAAU,CAACkB,KAAK,CAAC,EAAE,EAAE,CAACI,YAAY,IAAI,CAACF,OAAO,IAAID,IAAI,IAAI,OAAOnB,UAAU,CAACmB,IAAI,CAAC,EAAE,EAAEG,YAAY,IAAI,OAAOtB,UAAU,CAACsB,YAAY,CAAC,EAAE,EAAEF,OAAO,IAAI,SAAS;EACvP,CAAC;EACD,OAAOlB,cAAc,CAACqB,KAAK,EAAEjB,mBAAmB,EAAE,CAAC,CAAC,CAAC;AACvD,CAAC;AACD,OAAO,MAAMmB,UAAU,GAAGtB,MAAM,CAAC,IAAI,CAAC,CAACuB,IAAA,IAGjC;EAAA,IAHkC;IACtCC,KAAK;IACLX;EACF,CAAC,GAAAU,IAAA;EACC,IAAIE,eAAe;EACnB,MAAM;IACJC,CAAC;IACDC,OAAO;IACPC;EACF,CAAC,GAAG1B,cAAc,CAAC;IACjBsB,KAAK;IACLX;EACF,CAAC,EAAE,CAAC,GAAG,EAAE,SAAS,EAAE,cAAc,CAAC,CAAC;EACpC,SAASgB,aAAaA,CAACb,IAAI,EAAE;IAC3B,IAAIA,IAAI,KAAK,IAAI,EAAE;MACjB,OAAO;QACL,mBAAmB,EAAE,SAAS;QAC9B,sBAAsB,EAAE,MAAM;QAC9B,qBAAqB,EAAE,KAAK;QAC5B,qBAAqB,EAAEH,UAAU,CAACiB,MAAM,GAAG,KAAK,GAAG,QAAQ;QAC3D,gBAAgB,EAAE,QAAQ;QAC1B,0BAA0B,EAAEjB,UAAU,CAACK,WAAW,KAAK,YAAY,GAAG,QAAQ,GAAG,MAAM;QACvF,iBAAiB,EAAEM,KAAK,CAACO,IAAI,CAACC,QAAQ,CAACC;MACzC,CAAC;IACH;IACA,IAAIjB,IAAI,KAAK,IAAI,EAAE;MACjB,OAAO;QACL,mBAAmB,EAAE,UAAU;QAC/B,sBAAsB,EAAE,SAAS;QACjC,qBAAqB,EAAE,SAAS;QAChC,qBAAqB,EAAEH,UAAU,CAACiB,MAAM,GAAG,SAAS,GAAG,SAAS;QAChE,gBAAgB,EAAE,UAAU;QAC5B,0BAA0B,EAAEjB,UAAU,CAACK,WAAW,KAAK,YAAY,GAAG,SAAS,GAAG,QAAQ;QAC1F,iBAAiB,EAAEM,KAAK,CAACO,IAAI,CAACC,QAAQ,CAACE;MACzC,CAAC;IACH;IACA,IAAIlB,IAAI,KAAK,IAAI,EAAE;MACjB,OAAO;QACL,mBAAmB,EAAE,QAAQ;QAC7B,sBAAsB,EAAE,SAAS;QACjC,qBAAqB,EAAE,UAAU;QACjC,qBAAqB,EAAEH,UAAU,CAACiB,MAAM,GAAG,QAAQ,GAAG,MAAM;QAC5D,gBAAgB,EAAE,SAAS;QAC3B,0BAA0B,EAAEjB,UAAU,CAACK,WAAW,KAAK,YAAY,GAAG,SAAS,GAAG,MAAM;QACxF,iBAAiB,EAAEM,KAAK,CAACO,IAAI,CAACC,QAAQ,CAACG;MACzC,CAAC;IACH;IACA,OAAO,CAAC,CAAC;EACX;EACA,OAAO,CAACtB,UAAU,CAACI,OAAO,IAAI1B,QAAQ,CAAC,CAAC,CAAC,EAAEsC,aAAa,CAAChB,UAAU,CAACM,YAAY,CAAC,EAAE;IACjF,yBAAyB,EAAE,0BAA0B;IACrD,wBAAwB,EAAE,mCAAmC;IAC7D;IACA,8BAA8B,EAAE,KAAK;IACrC,+BAA+B,EAAE,KAAK;IACtC,wBAAwB,EAAE,KAAK;IAC/B,yBAAyB,EAAE,KAAK;IAChCQ,OAAO,EAAE;EACX,CAAC,EAAEd,UAAU,CAACiB,MAAM,IAAI;IACtBM,kBAAkB,EAAE,4CAA4C,CAAC;EACnE,CAAC,EAAE;IACDC,iBAAiB,EAAE,8BAA8B;IACjDC,eAAe,EAAE,+BAA+B;IAChDC,gBAAgB,EAAE,iBAAiB;IACnCC,cAAc,EAAE,SAAS,CAAC;EAC5B,CAAC,CAAC,EAAE,CAAC3B,UAAU,CAACI,OAAO,IAAI1B,QAAQ,CAAC,CAAC,CAAC,EAAEsC,aAAa,CAAChB,UAAU,CAACG,IAAI,CAAC,EAAE;IACtE,YAAY,EAAE,KAAK;IACnB,yBAAyB,EAAE,KAAK;IAChC,wBAAwB,EAAE,0BAA0B;IACpD,yBAAyB,EAAE;EAC7B,CAAC,EAAEH,UAAU,CAACiB,MAAM,IAAI;IACtB,sBAAsB,EAAE;EAC1B,CAAC,EAAE;IACD;IACA,6BAA6B,EAAE,6IAA6I;IAC5K,mBAAmB,EAAE,kCAAkC;IACvD;IACA,kCAAkC,EAAE,yCAAyC;IAC7E,gCAAgC,EAAE,2CAA2C;IAC7EW,MAAM,EAAE;EACV,CAAC,EAAEjB,KAAK,CAACkB,UAAU,CAAC,QAAQ7B,UAAU,CAACG,IAAI,EAAE,CAAC,EAAEH,UAAU,CAACK,WAAW,KAAK,YAAY,GAAG3B,QAAQ,CAAC,CAAC,CAAC,EAAEsB,UAAU,CAAC8B,IAAI,GAAG;IACvHhB,OAAO,EAAE,qBAAqB;IAC9B;IACAU,iBAAiB,EAAE,4BAA4B;IAC/CE,gBAAgB,EAAE;EACpB,CAAC,GAAG;IACFK,aAAa,EAAE,6CAA6C;IAC5DC,YAAY,EAAE;EAChB,CAAC,CAAC,GAAG;IACHA,YAAY,EAAE,6CAA6C;IAC3DD,aAAa,EAAE;EACjB,CAAC,EAAE/B,UAAU,CAACiB,MAAM,IAAI;IACtBM,kBAAkB,EAAE,KAAK,CAAC;EAC5B,CAAC,CAAC,EAAE7C,QAAQ,CAAC;IACXuD,SAAS,EAAE,YAAY;IACvBlB,YAAY,EAAE,oBAAoB;IAClCmB,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,MAAM;IACfC,aAAa,EAAEpC,UAAU,CAACK,WAAW,KAAK,YAAY,GAAG,KAAK,GAAG;EACnE,CAAC,EAAEL,UAAU,CAAC8B,IAAI,IAAI;IACpBO,QAAQ,EAAE;EACZ,CAAC,EAAErC,UAAU,CAACiB,MAAM,IAAI;IACtB,uBAAuB,EAAE,WAAW;IACpC,oBAAoB,EAAEjB,UAAU,CAACiB,MAAM;IACvCqB,UAAU,EAAE;EACd,CAAC,EAAE;IACDC,QAAQ,EAAE,CAAC;IACXC,QAAQ,EAAE;EACZ,CAAC,EAAE,CAAC5B,eAAe,GAAGD,KAAK,CAAC8B,QAAQ,CAACzC,UAAU,CAACC,OAAO,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGW,eAAe,CAACZ,UAAU,CAACE,KAAK,CAAC,EAAE;IAC9G,6BAA6B,EAAE;EACjC,CAAC,EAAEa,YAAY,KAAK2B,SAAS,IAAI;IAC/B,eAAe,EAAE3B;EACnB,CAAC,EAAEF,CAAC,KAAK6B,SAAS,IAAI;IACpB,gBAAgB,EAAE7B;EACpB,CAAC,EAAEC,OAAO,KAAK4B,SAAS,IAAI;IAC1B,gBAAgB,EAAE5B;EACpB,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,MAAM6B,QAAQ,GAAGxD,MAAM,CAACsB,UAAU,EAAE;EAClCmC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACxC;AAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyC,IAAI,GAAG,aAAarE,KAAK,CAACsE,UAAU,CAAC,SAASD,IAAIA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACrE,IAAIC,aAAa;EACjB,MAAMjD,OAAO,GAAGxB,KAAK,CAAC0E,UAAU,CAAC/D,iBAAiB,CAAC;EACnD,MAAMgE,KAAK,GAAG3E,KAAK,CAAC0E,UAAU,CAAC7D,gBAAgB,CAAC;EAChD,MAAM+D,iBAAiB,GAAG5E,KAAK,CAAC0E,UAAU,CAAC3D,iBAAiB,CAAC;EAC7D,MAAMoD,KAAK,GAAG3D,aAAa,CAAC;IAC1B2D,KAAK,EAAEI,OAAO;IACdP,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFa,SAAS;MACTC,SAAS;MACTC,QAAQ;MACRxD,IAAI,EAAEyD,QAAQ;MACdvD,WAAW,GAAG,UAAU;MACxByB,IAAI,GAAG,KAAK;MACZ7B,OAAO,GAAG,OAAO;MACjBC,KAAK,GAAG,SAAS;MACjB2D,IAAI,EAAEC,QAAQ;MACdvD,KAAK,GAAG,CAAC,CAAC;MACVwD,SAAS,GAAG,CAAC;IACf,CAAC,GAAGhB,KAAK;IACTiB,KAAK,GAAGvF,6BAA6B,CAACsE,KAAK,EAAEpE,SAAS,CAAC;EACzD,MAAMwB,IAAI,GAAGyD,QAAQ,KAAK,CAACP,aAAa,GAAGF,OAAO,CAAChD,IAAI,KAAK,IAAI,GAAGkD,aAAa,GAAG,IAAI,CAAC;EACxF,IAAIQ,IAAI;EACR,IAAIN,KAAK,EAAE;IACTM,IAAI,GAAG,OAAO;EAChB;EACA,IAAIL,iBAAiB,EAAE;IACrBK,IAAI,GAAG,cAAc;EACvB;EACA,IAAIC,QAAQ,EAAE;IACZD,IAAI,GAAGC,QAAQ;EACjB;EACA,MAAM9D,UAAU,GAAGtB,QAAQ,CAAC,CAAC,CAAC,EAAEqE,KAAK,EAAE;IACrCzC,YAAY,EAAE6C,OAAO,CAAChD,IAAI;IAC1BA,IAAI;IACJC,OAAO;IACPC,WAAW;IACXyB,IAAI;IACJ7B,OAAO;IACPC,KAAK;IACL2D;EACF,CAAC,CAAC;EACF,MAAMI,OAAO,GAAGlE,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMkE,sBAAsB,GAAGxF,QAAQ,CAAC,CAAC,CAAC,EAAEsF,KAAK,EAAE;IACjDP,SAAS;IACTlD,KAAK;IACLwD;EACF,CAAC,CAAC;EACF,MAAM,CAACI,QAAQ,EAAEC,SAAS,CAAC,GAAGxE,OAAO,CAAC,MAAM,EAAE;IAC5CwD,GAAG;IACHM,SAAS,EAAE5E,IAAI,CAACmF,OAAO,CAACzD,IAAI,EAAEkD,SAAS,CAAC;IACxCW,WAAW,EAAE1B,QAAQ;IACrBuB,sBAAsB;IACtBlE,UAAU;IACVsE,eAAe,EAAE;MACfC,EAAE,EAAEd,SAAS;MACbI,IAAI;MACJ,iBAAiB,EAAE,OAAOzD,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAGsC;IAC7D;EACF,CAAC,CAAC;EACF,OAAO,aAAa5C,IAAI,CAACqE,QAAQ,EAAEzF,QAAQ,CAAC,CAAC,CAAC,EAAE0F,SAAS,EAAE;IACzDT,QAAQ,EAAE,aAAa7D,IAAI,CAACN,oBAAoB,CAACgF,QAAQ,EAAE;MACzDC,KAAK,EAAE,GAAG,OAAOhB,SAAS,KAAK,QAAQ,GAAGA,SAAS,GAAG,EAAE,IAAII,IAAI,IAAI,EAAE,EAAE;MACxEF,QAAQ,EAAE,aAAa7D,IAAI,CAACJ,YAAY,EAAE;QACxCgF,GAAG,EAAErE,WAAW,KAAK,YAAY;QACjCyB,IAAI,EAAEA,IAAI;QACV6B,QAAQ,EAAEA;MACZ,CAAC;IACH,CAAC;EACH,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFgB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG5B,IAAI,CAAC6B,SAAS,CAAC,yBAAyB;EAC9E;EACA;EACA;EACA;EACA;AACF;AACA;EACEnB,QAAQ,EAAE9E,SAAS,CAACkG,IAAI;EACxB;AACF;AACA;EACErB,SAAS,EAAE7E,SAAS,CAACmG,MAAM;EAC3B;AACF;AACA;AACA;EACE9E,KAAK,EAAErB,SAAS,CAAC,sCAAsCoG,SAAS,CAAC,CAACpG,SAAS,CAACqG,KAAK,CAAC,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,EAAErG,SAAS,CAACmG,MAAM,CAAC,CAAC;EAC7J;AACF;AACA;AACA;EACEvB,SAAS,EAAE5E,SAAS,CAACwF,WAAW;EAChC;AACF;AACA;AACA;AACA;AACA;EACEpD,MAAM,EAAEpC,SAAS,CAACmG,MAAM;EACxB;AACF;AACA;AACA;EACE3E,WAAW,EAAExB,SAAS,CAACqG,KAAK,CAAC,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;EACxD;AACF;AACA;EACErB,IAAI,EAAEhF,SAAS,CAAC,sCAAsCmG,MAAM;EAC5D;AACF;AACA;AACA;EACE7E,IAAI,EAAEtB,SAAS,CAAC,sCAAsCoG,SAAS,CAAC,CAACpG,SAAS,CAACqG,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAErG,SAAS,CAACmG,MAAM,CAAC,CAAC;EACxH;AACF;AACA;AACA;EACEjB,SAAS,EAAElF,SAAS,CAACsG,KAAK,CAAC;IACzB3E,IAAI,EAAE3B,SAAS,CAACoG,SAAS,CAAC,CAACpG,SAAS,CAACuG,IAAI,EAAEvG,SAAS,CAACwG,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;EACE9E,KAAK,EAAE1B,SAAS,CAACsG,KAAK,CAAC;IACrB3E,IAAI,EAAE3B,SAAS,CAACwF;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACEiB,EAAE,EAAEzG,SAAS,CAACoG,SAAS,CAAC,CAACpG,SAAS,CAAC0G,OAAO,CAAC1G,SAAS,CAACoG,SAAS,CAAC,CAACpG,SAAS,CAACuG,IAAI,EAAEvG,SAAS,CAACwG,MAAM,EAAExG,SAAS,CAAC2G,IAAI,CAAC,CAAC,CAAC,EAAE3G,SAAS,CAACuG,IAAI,EAAEvG,SAAS,CAACwG,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;EACEpF,OAAO,EAAEpB,SAAS,CAAC,sCAAsCoG,SAAS,CAAC,CAACpG,SAAS,CAACqG,KAAK,CAAC,CAAC,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,EAAErG,SAAS,CAACmG,MAAM,CAAC,CAAC;EAC/I;AACF;AACA;AACA;AACA;AACA;EACElD,IAAI,EAAEjD,SAAS,CAAC2G;AAClB,CAAC,GAAG,KAAK,CAAC;AACV,eAAevC,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}