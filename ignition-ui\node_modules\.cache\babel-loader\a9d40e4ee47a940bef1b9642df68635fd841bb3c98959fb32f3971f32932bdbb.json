{"ast": null, "code": "'use client';\n\nexport { useOption } from './useOption';\nexport * from './useOption.types';\nexport * from './useOptionContextStabilizer';", "map": {"version": 3, "names": ["useOption"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/useOption/index.js"], "sourcesContent": ["'use client';\n\nexport { useOption } from './useOption';\nexport * from './useOption.types';\nexport * from './useOptionContextStabilizer';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,SAAS,QAAQ,aAAa;AACvC,cAAc,mBAAmB;AACjC,cAAc,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}