{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\n/**\n * Allows child elements to be transitioned in and out.\n *\n * Demos:\n *\n * - [Transitions](https://mui.com/base-ui/react-transitions/#hooks)\n *\n * API:\n *\n * - [useTransitionTrigger API](https://mui.com/base-ui/react-transitions/hooks-api/#use-transition-trigger)\n */\nexport function useTransitionTrigger(requestEnter) {\n  const [exitTransitionFinished, setExitTransitionFinished] = React.useState(true);\n  const hasPendingExitTransition = React.useRef(false);\n  const registeredTransitions = React.useRef(0);\n  const [hasTransition, setHasTransition] = React.useState(false);\n  const previousRequestEnter = React.useRef(requestEnter);\n  React.useEffect(() => {\n    if (!requestEnter &&\n    // checking registeredTransitions.current instead of hasTransition to avoid this effect re-firing whenever hasTransition changes\n    registeredTransitions.current > 0 &&\n    // prevents waiting for a pending transition right after mounting\n    previousRequestEnter.current !== requestEnter) {\n      hasPendingExitTransition.current = true;\n      setExitTransitionFinished(false);\n    }\n    previousRequestEnter.current = requestEnter;\n  }, [requestEnter]);\n  const handleExited = React.useCallback(() => {\n    hasPendingExitTransition.current = false;\n    setExitTransitionFinished(true);\n  }, []);\n  const registerTransition = React.useCallback(() => {\n    registeredTransitions.current += 1;\n    setHasTransition(true);\n    return () => {\n      registeredTransitions.current -= 1;\n      if (registeredTransitions.current === 0) {\n        setHasTransition(false);\n      }\n    };\n  }, []);\n  let hasExited;\n  if (!hasTransition) {\n    // If there are no transitions registered, the `exited` state is opposite of `requestEnter` immediately.\n    hasExited = !requestEnter;\n  } else if (requestEnter) {\n    hasExited = false;\n  } else {\n    hasExited = !hasPendingExitTransition.current && exitTransitionFinished;\n  }\n  const contextValue = React.useMemo(() => ({\n    requestedEnter: requestEnter,\n    onExited: handleExited,\n    registerTransition,\n    hasExited\n  }), [handleExited, requestEnter, registerTransition, hasExited]);\n  return {\n    contextValue,\n    hasExited\n  };\n}", "map": {"version": 3, "names": ["React", "useTransitionTrigger", "requestEnter", "exitTransitionFinished", "setExitTransitionFinished", "useState", "hasPendingExitTransition", "useRef", "registeredTransitions", "hasTransition", "setHasTransition", "previousRequestEnter", "useEffect", "current", "handleExited", "useCallback", "registerTransition", "hasExited", "contextValue", "useMemo", "requestedEnter", "onExited"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/useTransition/useTransitionTrigger.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\n/**\n * Allows child elements to be transitioned in and out.\n *\n * Demos:\n *\n * - [Transitions](https://mui.com/base-ui/react-transitions/#hooks)\n *\n * API:\n *\n * - [useTransitionTrigger API](https://mui.com/base-ui/react-transitions/hooks-api/#use-transition-trigger)\n */\nexport function useTransitionTrigger(requestEnter) {\n  const [exitTransitionFinished, setExitTransitionFinished] = React.useState(true);\n  const hasPendingExitTransition = React.useRef(false);\n  const registeredTransitions = React.useRef(0);\n  const [hasTransition, setHasTransition] = React.useState(false);\n  const previousRequestEnter = React.useRef(requestEnter);\n  React.useEffect(() => {\n    if (!requestEnter &&\n    // checking registeredTransitions.current instead of hasTransition to avoid this effect re-firing whenever hasTransition changes\n    registeredTransitions.current > 0 &&\n    // prevents waiting for a pending transition right after mounting\n    previousRequestEnter.current !== requestEnter) {\n      hasPendingExitTransition.current = true;\n      setExitTransitionFinished(false);\n    }\n    previousRequestEnter.current = requestEnter;\n  }, [requestEnter]);\n  const handleExited = React.useCallback(() => {\n    hasPendingExitTransition.current = false;\n    setExitTransitionFinished(true);\n  }, []);\n  const registerTransition = React.useCallback(() => {\n    registeredTransitions.current += 1;\n    setHasTransition(true);\n    return () => {\n      registeredTransitions.current -= 1;\n      if (registeredTransitions.current === 0) {\n        setHasTransition(false);\n      }\n    };\n  }, []);\n  let hasExited;\n  if (!hasTransition) {\n    // If there are no transitions registered, the `exited` state is opposite of `requestEnter` immediately.\n    hasExited = !requestEnter;\n  } else if (requestEnter) {\n    hasExited = false;\n  } else {\n    hasExited = !hasPendingExitTransition.current && exitTransitionFinished;\n  }\n  const contextValue = React.useMemo(() => ({\n    requestedEnter: requestEnter,\n    onExited: handleExited,\n    registerTransition,\n    hasExited\n  }), [handleExited, requestEnter, registerTransition, hasExited]);\n  return {\n    contextValue,\n    hasExited\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,oBAAoBA,CAACC,YAAY,EAAE;EACjD,MAAM,CAACC,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGJ,KAAK,CAACK,QAAQ,CAAC,IAAI,CAAC;EAChF,MAAMC,wBAAwB,GAAGN,KAAK,CAACO,MAAM,CAAC,KAAK,CAAC;EACpD,MAAMC,qBAAqB,GAAGR,KAAK,CAACO,MAAM,CAAC,CAAC,CAAC;EAC7C,MAAM,CAACE,aAAa,EAAEC,gBAAgB,CAAC,GAAGV,KAAK,CAACK,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAMM,oBAAoB,GAAGX,KAAK,CAACO,MAAM,CAACL,YAAY,CAAC;EACvDF,KAAK,CAACY,SAAS,CAAC,MAAM;IACpB,IAAI,CAACV,YAAY;IACjB;IACAM,qBAAqB,CAACK,OAAO,GAAG,CAAC;IACjC;IACAF,oBAAoB,CAACE,OAAO,KAAKX,YAAY,EAAE;MAC7CI,wBAAwB,CAACO,OAAO,GAAG,IAAI;MACvCT,yBAAyB,CAAC,KAAK,CAAC;IAClC;IACAO,oBAAoB,CAACE,OAAO,GAAGX,YAAY;EAC7C,CAAC,EAAE,CAACA,YAAY,CAAC,CAAC;EAClB,MAAMY,YAAY,GAAGd,KAAK,CAACe,WAAW,CAAC,MAAM;IAC3CT,wBAAwB,CAACO,OAAO,GAAG,KAAK;IACxCT,yBAAyB,CAAC,IAAI,CAAC;EACjC,CAAC,EAAE,EAAE,CAAC;EACN,MAAMY,kBAAkB,GAAGhB,KAAK,CAACe,WAAW,CAAC,MAAM;IACjDP,qBAAqB,CAACK,OAAO,IAAI,CAAC;IAClCH,gBAAgB,CAAC,IAAI,CAAC;IACtB,OAAO,MAAM;MACXF,qBAAqB,CAACK,OAAO,IAAI,CAAC;MAClC,IAAIL,qBAAqB,CAACK,OAAO,KAAK,CAAC,EAAE;QACvCH,gBAAgB,CAAC,KAAK,CAAC;MACzB;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,IAAIO,SAAS;EACb,IAAI,CAACR,aAAa,EAAE;IAClB;IACAQ,SAAS,GAAG,CAACf,YAAY;EAC3B,CAAC,MAAM,IAAIA,YAAY,EAAE;IACvBe,SAAS,GAAG,KAAK;EACnB,CAAC,MAAM;IACLA,SAAS,GAAG,CAACX,wBAAwB,CAACO,OAAO,IAAIV,sBAAsB;EACzE;EACA,MAAMe,YAAY,GAAGlB,KAAK,CAACmB,OAAO,CAAC,OAAO;IACxCC,cAAc,EAAElB,YAAY;IAC5BmB,QAAQ,EAAEP,YAAY;IACtBE,kBAAkB;IAClBC;EACF,CAAC,CAAC,EAAE,CAACH,YAAY,EAAEZ,YAAY,EAAEc,kBAAkB,EAAEC,SAAS,CAAC,CAAC;EAChE,OAAO;IACLC,YAAY;IACZD;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}