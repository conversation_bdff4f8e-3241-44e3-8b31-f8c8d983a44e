{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getMenuButtonUtilityClass(slot) {\n  return generateUtilityClass('MuiMenuButton', slot);\n}\nconst menuButtonClasses = generateUtilityClasses('MuiMenuButton', ['root', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorInfo', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid', 'disabled', 'sizeSm', 'sizeMd', 'sizeLg', 'fullWidth', 'startDecorator', 'endDecorator', 'loading', 'loadingIndicatorCenter']);\nexport default menuButtonClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getMenuButtonUtilityClass", "slot", "menuButtonClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/MenuButton/menuButtonClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getMenuButtonUtilityClass(slot) {\n  return generateUtilityClass('MuiMenuButton', slot);\n}\nconst menuButtonClasses = generateUtilityClasses('MuiMenuButton', ['root', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorInfo', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid', 'disabled', 'sizeSm', 'sizeMd', 'sizeLg', 'fullWidth', 'startDecorator', 'endDecorator', 'loading', 'loadingIndicatorCenter']);\nexport default menuButtonClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,yBAAyBA,CAACC,IAAI,EAAE;EAC9C,OAAOH,oBAAoB,CAAC,eAAe,EAAEG,IAAI,CAAC;AACpD;AACA,MAAMC,iBAAiB,GAAGH,sBAAsB,CAAC,eAAe,EAAE,CAAC,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,WAAW,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,iBAAiB,EAAE,aAAa,EAAE,cAAc,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,WAAW,EAAE,gBAAgB,EAAE,cAAc,EAAE,SAAS,EAAE,wBAAwB,CAAC,CAAC;AACvX,eAAeG,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}