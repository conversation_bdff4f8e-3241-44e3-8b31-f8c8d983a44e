{"ast": null, "code": "import extendTheme from './extendTheme';\nconst defaultTheme = extendTheme();\nexport default defaultTheme;", "map": {"version": 3, "names": ["extendTheme", "defaultTheme"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/styles/defaultTheme.js"], "sourcesContent": ["import extendTheme from './extendTheme';\nconst defaultTheme = extendTheme();\nexport default defaultTheme;"], "mappings": "AAAA,OAAOA,WAAW,MAAM,eAAe;AACvC,MAAMC,YAAY,GAAGD,WAAW,CAAC,CAAC;AAClC,eAAeC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}