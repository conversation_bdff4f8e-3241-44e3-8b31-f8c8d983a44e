{"ast": null, "code": "'use strict';\n\n/** @type {import('./Reflect.getPrototypeOf')} */\nmodule.exports = typeof Reflect !== 'undefined' && Reflect.getPrototypeOf || null;", "map": {"version": 3, "names": ["module", "exports", "Reflect", "getPrototypeOf"], "sources": ["C:/ignition/ignition-ui/node_modules/get-proto/Reflect.getPrototypeOf.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./Reflect.getPrototypeOf')} */\nmodule.exports = (typeof Reflect !== 'undefined' && Reflect.getPrototypeOf) || null;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACAA,MAAM,CAACC,OAAO,GAAI,OAAOC,OAAO,KAAK,WAAW,IAAIA,OAAO,CAACC,cAAc,IAAK,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}