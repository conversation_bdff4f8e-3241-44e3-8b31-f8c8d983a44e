{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getAccordionDetailsUtilityClass(slot) {\n  return generateUtilityClass('MuiAccordionDetails', slot);\n}\nconst accordionDetailsClasses = generateUtilityClasses('MuiAccordionDetails', ['root', 'content', 'expanded']);\nexport default accordionDetailsClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getAccordionDetailsUtilityClass", "slot", "accordionDetailsClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/AccordionDetails/accordionDetailsClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getAccordionDetailsUtilityClass(slot) {\n  return generateUtilityClass('MuiAccordionDetails', slot);\n}\nconst accordionDetailsClasses = generateUtilityClasses('MuiAccordionDetails', ['root', 'content', 'expanded']);\nexport default accordionDetailsClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,+BAA+BA,CAACC,IAAI,EAAE;EACpD,OAAOH,oBAAoB,CAAC,qBAAqB,EAAEG,IAAI,CAAC;AAC1D;AACA,MAAMC,uBAAuB,GAAGH,sBAAsB,CAAC,qBAAqB,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;AAC9G,eAAeG,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}