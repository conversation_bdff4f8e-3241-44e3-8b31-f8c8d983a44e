import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  IconButton,
  Avatar,
  Divider,
  CircularProgress,
  Chip,
  Tooltip
} from '@mui/material';
import Iconify from 'components/Iconify/index';
import { mainYellowColor } from "helpers/constants";
import { useLocation } from 'react-router-dom';

const AgentTab = ({ planInfo, onPlanUpdate }) => {
  const [conversations, setConversations] = useState([]);
  const [currentMessage, setCurrentMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);
  const location = useLocation();

  // Load conversations from localStorage
  useEffect(() => {
    const savedConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');
    const planConversations = savedConversations.filter(conv => conv.planId === planInfo?.id);
    setConversations(planConversations);

    // Check for pending message from ChatbotBar
    const pendingMessage = localStorage.getItem('pending_agent_message');
    if (pendingMessage) {
      try {
        const messageData = JSON.parse(pendingMessage);
        if (messageData.planInfo?.id === planInfo?.id) {
          handleSendMessage(messageData.message);
        }
        // Clear the pending message
        localStorage.removeItem('pending_agent_message');
      } catch (error) {
        console.error('Error processing pending message:', error);
        localStorage.removeItem('pending_agent_message');
      }
    }

    // If coming from chatbot bar via navigation state, add the initial message
    if (location.state?.message) {
      handleSendMessage(location.state.message);
    }
  }, [planInfo?.id, location.state]);

  // Auto scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [conversations]);

  const handleSendMessage = async (messageText = currentMessage) => {
    if (!messageText.trim() || isLoading) return;

    setIsLoading(true);
    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: messageText.trim(),
      timestamp: new Date().toISOString()
    };

    const newConversations = [...conversations, userMessage];
    setConversations(newConversations);
    setCurrentMessage('');

    try {
      // Simulate AI processing (replace with actual AI API call)
      await new Promise(resolve => setTimeout(resolve, 2000));

      const aiResponse = {
        id: Date.now() + 1,
        type: 'assistant',
        content: generateAIResponse(messageText, planInfo),
        timestamp: new Date().toISOString(),
        actions: extractActions(messageText)
      };

      const updatedConversations = [...newConversations, aiResponse];
      setConversations(updatedConversations);

      // Save to localStorage
      const allConversations = JSON.parse(localStorage.getItem('agent_conversations') || '[]');
      const otherPlanConversations = allConversations.filter(conv => conv.planId !== planInfo?.id);
      const planConversations = updatedConversations.map(conv => ({
        ...conv,
        planId: planInfo?.id,
        planName: planInfo?.name
      }));
      
      localStorage.setItem('agent_conversations', JSON.stringify([
        ...otherPlanConversations,
        ...planConversations
      ]));

    } catch (error) {
      console.error('Error processing message:', error);
      const errorResponse = {
        id: Date.now() + 1,
        type: 'assistant',
        content: "I'm sorry, I encountered an error processing your request. Please try again.",
        timestamp: new Date().toISOString(),
        isError: true
      };
      setConversations(prev => [...prev, errorResponse]);
    } finally {
      setIsLoading(false);
    }
  };

  const generateAIResponse = (message, planInfo) => {
    // Simple response generation (replace with actual AI integration)
    const lowerMessage = message.toLowerCase();
    
    if (lowerMessage.includes('progress') || lowerMessage.includes('status')) {
      return `Based on your project "${planInfo?.name}", you currently have ${planInfo?.milestones?.length || 0} milestones. I can help you track progress and update task statuses. Would you like me to show you the current completion status?`;
    }
    
    if (lowerMessage.includes('complete') || lowerMessage.includes('done')) {
      return `I can help you mark tasks as completed. Please specify which task you'd like to mark as done, and I'll update it for you. You can say something like "Mark task [task name] as completed".`;
    }
    
    if (lowerMessage.includes('add') || lowerMessage.includes('create')) {
      return `I can help you add new tasks or subtasks to your project. Please specify which milestone you'd like to add to and what the new task should be. For example: "Add task [task name] to [milestone name]".`;
    }
    
    if (lowerMessage.includes('delete') || lowerMessage.includes('remove')) {
      return `I can help you remove tasks or subtasks. Please specify what you'd like to delete, and I'll handle it for you. For safety, I'll ask for confirmation before making any deletions.`;
    }
    
    return `I understand you want to "${message}". I'm here to help you manage your project "${planInfo?.name}". I can help you:

• Mark tasks as completed or update their status
• Add new tasks and subtasks to milestones  
• Update task descriptions and details
• Delete completed or unnecessary tasks
• Show project progress and statistics

What specific action would you like me to take?`;
  };

  const extractActions = (message) => {
    // Extract potential actions from the message
    const actions = [];
    const lowerMessage = message.toLowerCase();
    
    if (lowerMessage.includes('complete') || lowerMessage.includes('done')) {
      actions.push({ type: 'complete_task', confidence: 0.8 });
    }
    if (lowerMessage.includes('add') || lowerMessage.includes('create')) {
      actions.push({ type: 'add_task', confidence: 0.8 });
    }
    if (lowerMessage.includes('delete') || lowerMessage.includes('remove')) {
      actions.push({ type: 'delete_task', confidence: 0.7 });
    }
    
    return actions;
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const formatTimestamp = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <Box sx={{ height: '70vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Paper
        elevation={0}
        sx={{
          p: 2,
          borderRadius: '12px 12px 0 0',
          border: '1px solid #f0f0f0',
          borderBottom: 'none',
          backgroundColor: '#fafafa'
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <Avatar
            sx={{
              backgroundColor: mainYellowColor,
              width: 40,
              height: 40
            }}
          >
            <Iconify icon="mdi:robot" width={24} height={24} color="#fff" />
          </Avatar>
          <Box>
            <Typography
              variant="h6"
              sx={{
                fontFamily: '"Recursive Variable", sans-serif',
                fontWeight: 600,
                color: '#333'
              }}
            >
              AI Project Agent
            </Typography>
            <Typography
              variant="caption"
              sx={{
                color: '#666',
                fontFamily: '"Recursive Variable", sans-serif'
              }}
            >
              Managing: {planInfo?.name}
            </Typography>
          </Box>
          <Chip
            label="Beta"
            size="small"
            sx={{
              backgroundColor: `${mainYellowColor}20`,
              color: mainYellowColor,
              fontWeight: 600,
              ml: 'auto'
            }}
          />
        </Box>
      </Paper>

      {/* Messages Area */}
      <Paper
        elevation={0}
        sx={{
          flex: 1,
          border: '1px solid #f0f0f0',
          borderTop: 'none',
          borderBottom: 'none',
          overflow: 'auto',
          p: 2,
          backgroundColor: '#fff'
        }}
      >
        {conversations.length === 0 ? (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: '100%',
              textAlign: 'center'
            }}
          >
            <Avatar
              sx={{
                backgroundColor: `${mainYellowColor}20`,
                width: 60,
                height: 60,
                mb: 2
              }}
            >
              <Iconify icon="mdi:robot" width={32} height={32} color={mainYellowColor} />
            </Avatar>
            <Typography
              variant="h6"
              sx={{
                fontFamily: '"Recursive Variable", sans-serif',
                fontWeight: 600,
                color: '#333',
                mb: 1
              }}
            >
              Welcome to AI Project Agent
            </Typography>
            <Typography
              variant="body2"
              sx={{
                color: '#666',
                fontFamily: '"Recursive Variable", sans-serif',
                maxWidth: 400
              }}
            >
              I'm here to help you manage your project. Ask me questions or request changes to tasks, milestones, and more!
            </Typography>
          </Box>
        ) : (
          <Box>
            {conversations.map((message) => (
              <Box
                key={message.id}
                sx={{
                  display: 'flex',
                  justifyContent: message.type === 'user' ? 'flex-end' : 'flex-start',
                  mb: 2
                }}
              >
                <Box
                  sx={{
                    maxWidth: '70%',
                    display: 'flex',
                    flexDirection: message.type === 'user' ? 'row-reverse' : 'row',
                    alignItems: 'flex-start',
                    gap: 1
                  }}
                >
                  <Avatar
                    sx={{
                      width: 32,
                      height: 32,
                      backgroundColor: message.type === 'user' ? '#e0e0e0' : mainYellowColor
                    }}
                  >
                    <Iconify
                      icon={message.type === 'user' ? "material-symbols:person" : "mdi:robot"}
                      width={18}
                      height={18}
                      color={message.type === 'user' ? '#666' : '#fff'}
                    />
                  </Avatar>
                  <Box>
                    <Paper
                      elevation={0}
                      sx={{
                        p: 1.5,
                        borderRadius: '12px',
                        backgroundColor: message.type === 'user' ? mainYellowColor : '#f5f5f5',
                        color: message.type === 'user' ? '#fff' : '#333',
                        border: message.isError ? '1px solid #f44336' : 'none'
                      }}
                    >
                      <Typography
                        variant="body2"
                        sx={{
                          fontFamily: '"Recursive Variable", sans-serif',
                          lineHeight: 1.5,
                          whiteSpace: 'pre-line'
                        }}
                      >
                        {message.content}
                      </Typography>
                    </Paper>
                    <Typography
                      variant="caption"
                      sx={{
                        color: '#999',
                        fontFamily: '"Recursive Variable", sans-serif',
                        fontSize: '0.7rem',
                        mt: 0.5,
                        display: 'block',
                        textAlign: message.type === 'user' ? 'right' : 'left'
                      }}
                    >
                      {formatTimestamp(message.timestamp)}
                    </Typography>
                  </Box>
                </Box>
              </Box>
            ))}
            {isLoading && (
              <Box sx={{ display: 'flex', justifyContent: 'flex-start', mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                  <Avatar
                    sx={{
                      width: 32,
                      height: 32,
                      backgroundColor: mainYellowColor
                    }}
                  >
                    <Iconify icon="mdi:robot" width={18} height={18} color="#fff" />
                  </Avatar>
                  <Paper
                    elevation={0}
                    sx={{
                      p: 1.5,
                      borderRadius: '12px',
                      backgroundColor: '#f5f5f5',
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}
                  >
                    <CircularProgress size={16} sx={{ color: mainYellowColor }} />
                    <Typography
                      variant="body2"
                      sx={{
                        fontFamily: '"Recursive Variable", sans-serif',
                        color: '#666'
                      }}
                    >
                      Thinking...
                    </Typography>
                  </Paper>
                </Box>
              </Box>
            )}
            <div ref={messagesEndRef} />
          </Box>
        )}
      </Paper>

      {/* Input Area */}
      <Paper
        elevation={0}
        sx={{
          p: 2,
          borderRadius: '0 0 12px 12px',
          border: '1px solid #f0f0f0',
          borderTop: 'none'
        }}
      >
        <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>
          <TextField
            inputRef={inputRef}
            value={currentMessage}
            onChange={(e) => setCurrentMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask me anything about your project..."
            multiline
            maxRows={3}
            fullWidth
            variant="outlined"
            disabled={isLoading}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: '8px',
                fontFamily: '"Recursive Variable", sans-serif'
              }
            }}
          />
          <Tooltip title="Send message">
            <IconButton
              onClick={() => handleSendMessage()}
              disabled={!currentMessage.trim() || isLoading}
              sx={{
                backgroundColor: currentMessage.trim() && !isLoading ? mainYellowColor : '#f0f0f0',
                color: currentMessage.trim() && !isLoading ? '#fff' : '#999',
                '&:hover': {
                  backgroundColor: currentMessage.trim() && !isLoading ? '#E69500' : '#f0f0f0'
                }
              }}
            >
              <Iconify icon="material-symbols:send" width={20} height={20} />
            </IconButton>
          </Tooltip>
        </Box>
      </Paper>
    </Box>
  );
};

export default AgentTab;
