{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useForkRef as useForkRef, unstable_useId as useId, unstable_useEnhancedEffect as useEnhancedEffect, visuallyHidden as visuallyHiddenStyle } from '@mui/utils';\nimport { useButton } from '../useButton';\nimport { SelectActionTypes } from './useSelect.types';\nimport { ListActionTypes, useList } from '../useList';\nimport { defaultOptionStringifier } from './defaultOptionStringifier';\nimport { useCompoundParent } from '../useCompound';\nimport { extractEventHandlers } from '../utils/extractEventHandlers';\nimport { selectReducer } from './selectReducer';\nimport { combineHooksSlotProps } from '../utils/combineHooksSlotProps';\nfunction defaultFormValueProvider(selectedOption) {\n  if (Array.isArray(selectedOption)) {\n    if (selectedOption.length === 0) {\n      return '';\n    }\n    return JSON.stringify(selectedOption.map(o => o.value));\n  }\n  if ((selectedOption == null ? void 0 : selectedOption.value) == null) {\n    return '';\n  }\n  if (typeof selectedOption.value === 'string' || typeof selectedOption.value === 'number') {\n    return selectedOption.value;\n  }\n  return JSON.stringify(selectedOption.value);\n}\n\n/**\n *\n * Demos:\n *\n * - [Select](https://mui.com/base-ui/react-select/#hooks)\n *\n * API:\n *\n * - [useSelect API](https://mui.com/base-ui/react-select/hooks-api/#use-select)\n */\nfunction useSelect(props) {\n  const {\n    areOptionsEqual,\n    buttonRef: buttonRefProp,\n    defaultOpen = false,\n    defaultValue: defaultValueProp,\n    disabled = false,\n    listboxId: listboxIdProp,\n    listboxRef: listboxRefProp,\n    multiple = false,\n    name,\n    required,\n    onChange,\n    onHighlightChange,\n    onOpenChange,\n    open: openProp,\n    options: optionsParam,\n    getOptionAsString = defaultOptionStringifier,\n    getSerializedValue = defaultFormValueProvider,\n    value: valueProp,\n    componentName = 'useSelect'\n  } = props;\n  const buttonRef = React.useRef(null);\n  const handleButtonRef = useForkRef(buttonRefProp, buttonRef);\n  const listboxRef = React.useRef(null);\n  const listboxId = useId(listboxIdProp);\n  let defaultValue;\n  if (valueProp === undefined && defaultValueProp === undefined) {\n    defaultValue = [];\n  } else if (defaultValueProp !== undefined) {\n    if (multiple) {\n      defaultValue = defaultValueProp;\n    } else {\n      defaultValue = defaultValueProp == null ? [] : [defaultValueProp];\n    }\n  }\n  const value = React.useMemo(() => {\n    if (valueProp !== undefined) {\n      if (multiple) {\n        return valueProp;\n      }\n      return valueProp == null ? [] : [valueProp];\n    }\n    return undefined;\n  }, [valueProp, multiple]);\n  const {\n    subitems,\n    contextValue: compoundComponentContextValue\n  } = useCompoundParent();\n  const options = React.useMemo(() => {\n    if (optionsParam != null) {\n      return new Map(optionsParam.map((option, index) => [option.value, {\n        value: option.value,\n        label: option.label,\n        disabled: option.disabled,\n        ref: /*#__PURE__*/React.createRef(),\n        id: `${listboxId}_${index}`\n      }]));\n    }\n    return subitems;\n  }, [optionsParam, subitems, listboxId]);\n  const handleListboxRef = useForkRef(listboxRefProp, listboxRef);\n  const {\n    getRootProps: getButtonRootProps,\n    active: buttonActive,\n    focusVisible: buttonFocusVisible,\n    rootRef: mergedButtonRef\n  } = useButton({\n    disabled,\n    rootRef: handleButtonRef\n  });\n  const optionValues = React.useMemo(() => Array.from(options.keys()), [options]);\n  const getOptionByValue = React.useCallback(valueToGet => {\n    // This can't be simply `options.get(valueToGet)` because of the `areOptionsEqual` prop.\n    // If it's provided, we assume that the user wants to compare the options by value.\n    if (areOptionsEqual !== undefined) {\n      const similarValue = optionValues.find(optionValue => areOptionsEqual(optionValue, valueToGet));\n      return options.get(similarValue);\n    }\n    return options.get(valueToGet);\n  }, [options, areOptionsEqual, optionValues]);\n  const isItemDisabled = React.useCallback(valueToCheck => {\n    var _option$disabled;\n    const option = getOptionByValue(valueToCheck);\n    return (_option$disabled = option == null ? void 0 : option.disabled) != null ? _option$disabled : false;\n  }, [getOptionByValue]);\n  const stringifyOption = React.useCallback(valueToCheck => {\n    const option = getOptionByValue(valueToCheck);\n    if (!option) {\n      return '';\n    }\n    return getOptionAsString(option);\n  }, [getOptionByValue, getOptionAsString]);\n  const controlledState = React.useMemo(() => ({\n    selectedValues: value,\n    open: openProp\n  }), [value, openProp]);\n  const getItemId = React.useCallback(itemValue => {\n    var _options$get;\n    return (_options$get = options.get(itemValue)) == null ? void 0 : _options$get.id;\n  }, [options]);\n  const handleSelectionChange = React.useCallback((event, newValues) => {\n    if (multiple) {\n      onChange == null || onChange(event, newValues);\n    } else {\n      var _newValues$;\n      onChange == null || onChange(event, (_newValues$ = newValues[0]) != null ? _newValues$ : null);\n    }\n  }, [multiple, onChange]);\n  const handleHighlightChange = React.useCallback((event, newValue) => {\n    onHighlightChange == null || onHighlightChange(event, newValue != null ? newValue : null);\n  }, [onHighlightChange]);\n  const handleStateChange = React.useCallback((event, field, fieldValue) => {\n    if (field === 'open') {\n      onOpenChange == null || onOpenChange(fieldValue);\n      if (fieldValue === false && (event == null ? void 0 : event.type) !== 'blur') {\n        var _buttonRef$current;\n        (_buttonRef$current = buttonRef.current) == null || _buttonRef$current.focus();\n      }\n    }\n  }, [onOpenChange]);\n  const getItemDomElement = React.useCallback(itemId => {\n    var _subitems$get$ref$cur, _subitems$get;\n    if (itemId == null) {\n      return null;\n    }\n    return (_subitems$get$ref$cur = (_subitems$get = subitems.get(itemId)) == null ? void 0 : _subitems$get.ref.current) != null ? _subitems$get$ref$cur : null;\n  }, [subitems]);\n  const useListParameters = {\n    getInitialState: () => {\n      var _defaultValue;\n      return {\n        highlightedValue: null,\n        selectedValues: (_defaultValue = defaultValue) != null ? _defaultValue : [],\n        open: defaultOpen\n      };\n    },\n    getItemId,\n    controlledProps: controlledState,\n    focusManagement: 'DOM',\n    getItemDomElement,\n    itemComparer: areOptionsEqual,\n    isItemDisabled,\n    rootRef: handleListboxRef,\n    onChange: handleSelectionChange,\n    onHighlightChange: handleHighlightChange,\n    onStateChange: handleStateChange,\n    reducerActionContext: React.useMemo(() => ({\n      multiple\n    }), [multiple]),\n    items: optionValues,\n    getItemAsString: stringifyOption,\n    selectionMode: multiple ? 'multiple' : 'single',\n    stateReducer: selectReducer,\n    componentName\n  };\n  const {\n    dispatch,\n    getRootProps: getListboxRootProps,\n    contextValue: listContextValue,\n    state: {\n      open,\n      highlightedValue: highlightedOption,\n      selectedValues: selectedOptions\n    },\n    rootRef: mergedListRootRef\n  } = useList(useListParameters);\n\n  // store the initial open state to prevent focus stealing\n  // (the first option gets focused only when the select is opened by the user)\n  const isInitiallyOpen = React.useRef(open);\n  useEnhancedEffect(() => {\n    if (open && highlightedOption !== null) {\n      var _getOptionByValue;\n      const optionRef = (_getOptionByValue = getOptionByValue(highlightedOption)) == null ? void 0 : _getOptionByValue.ref;\n      if (!listboxRef.current || !(optionRef != null && optionRef.current)) {\n        return;\n      }\n      if (!isInitiallyOpen.current) {\n        optionRef.current.focus({\n          preventScroll: true\n        });\n      }\n      const listboxClientRect = listboxRef.current.getBoundingClientRect();\n      const optionClientRect = optionRef.current.getBoundingClientRect();\n      if (optionClientRect.top < listboxClientRect.top) {\n        listboxRef.current.scrollTop -= listboxClientRect.top - optionClientRect.top;\n      } else if (optionClientRect.bottom > listboxClientRect.bottom) {\n        listboxRef.current.scrollTop += optionClientRect.bottom - listboxClientRect.bottom;\n      }\n    }\n  }, [open, highlightedOption, getOptionByValue]);\n  const getOptionMetadata = React.useCallback(optionValue => getOptionByValue(optionValue), [getOptionByValue]);\n  const createHandleButtonClick = externalEventHandlers => event => {\n    var _externalEventHandler;\n    externalEventHandlers == null || (_externalEventHandler = externalEventHandlers.onClick) == null || _externalEventHandler.call(externalEventHandlers, event);\n    if (!event.defaultMuiPrevented) {\n      const action = {\n        type: SelectActionTypes.buttonClick,\n        event\n      };\n      dispatch(action);\n    }\n  };\n  const createHandleButtonKeyDown = otherHandlers => event => {\n    var _otherHandlers$onKeyD;\n    (_otherHandlers$onKeyD = otherHandlers.onKeyDown) == null || _otherHandlers$onKeyD.call(otherHandlers, event);\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    if (event.key === 'ArrowDown' || event.key === 'ArrowUp') {\n      event.preventDefault();\n      dispatch({\n        type: ListActionTypes.keyDown,\n        key: event.key,\n        event\n      });\n    }\n  };\n  const getButtonOwnRootProps = function () {\n    let otherHandlers = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    return {\n      onClick: createHandleButtonClick(otherHandlers),\n      onKeyDown: createHandleButtonKeyDown(otherHandlers)\n    };\n  };\n  const getSelectTriggerProps = function () {\n    let otherHandlers = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    return _extends({}, otherHandlers, getButtonOwnRootProps(otherHandlers), {\n      role: 'combobox',\n      'aria-expanded': open,\n      'aria-controls': listboxId\n    });\n  };\n  const getButtonProps = function () {\n    let externalProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    const externalEventHandlers = extractEventHandlers(externalProps);\n    const combinedProps = combineHooksSlotProps(getSelectTriggerProps, getButtonRootProps);\n    return _extends({}, externalProps, combinedProps(externalEventHandlers));\n  };\n  const createListboxHandleBlur = otherHandlers => event => {\n    var _otherHandlers$onBlur, _listboxRef$current;\n    (_otherHandlers$onBlur = otherHandlers.onBlur) == null || _otherHandlers$onBlur.call(otherHandlers, event);\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    if ((_listboxRef$current = listboxRef.current) != null && _listboxRef$current.contains(event.relatedTarget) || event.relatedTarget === buttonRef.current) {\n      event.defaultMuiPrevented = true;\n    }\n  };\n  const getOwnListboxHandlers = function () {\n    let otherHandlers = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    return {\n      onBlur: createListboxHandleBlur(otherHandlers)\n    };\n  };\n  const getListboxProps = function () {\n    let externalProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    const externalEventHandlers = extractEventHandlers(externalProps);\n    const getCombinedRootProps = combineHooksSlotProps(getOwnListboxHandlers, getListboxRootProps);\n    return _extends({\n      id: listboxId,\n      role: 'listbox',\n      'aria-multiselectable': multiple ? 'true' : undefined\n    }, externalProps, getCombinedRootProps(externalEventHandlers));\n  };\n  React.useDebugValue({\n    selectedOptions,\n    highlightedOption,\n    open\n  });\n  const contextValue = React.useMemo(() => _extends({}, listContextValue, compoundComponentContextValue), [listContextValue, compoundComponentContextValue]);\n  let selectValue;\n  if (props.multiple) {\n    selectValue = selectedOptions;\n  } else {\n    selectValue = selectedOptions.length > 0 ? selectedOptions[0] : null;\n  }\n  let selectedOptionsMetadata;\n  if (multiple) {\n    selectedOptionsMetadata = selectValue.map(v => getOptionMetadata(v)).filter(o => o !== undefined);\n  } else {\n    var _getOptionMetadata;\n    selectedOptionsMetadata = (_getOptionMetadata = getOptionMetadata(selectValue)) != null ? _getOptionMetadata : null;\n  }\n  const createHandleHiddenInputChange = externalEventHandlers => event => {\n    var _externalEventHandler2;\n    externalEventHandlers == null || (_externalEventHandler2 = externalEventHandlers.onChange) == null || _externalEventHandler2.call(externalEventHandlers, event);\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    const option = options.get(event.target.value);\n\n    // support autofill\n    if (event.target.value === '') {\n      dispatch({\n        type: ListActionTypes.clearSelection\n      });\n    } else if (option !== undefined) {\n      dispatch({\n        type: SelectActionTypes.browserAutoFill,\n        item: option.value,\n        event\n      });\n    }\n  };\n  const getHiddenInputProps = function () {\n    let externalProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    const externalEventHandlers = extractEventHandlers(externalProps);\n    return _extends({\n      name,\n      tabIndex: -1,\n      'aria-hidden': true,\n      required: required ? true : undefined,\n      value: getSerializedValue(selectedOptionsMetadata),\n      style: visuallyHiddenStyle\n    }, externalProps, {\n      onChange: createHandleHiddenInputChange(externalEventHandlers)\n    });\n  };\n  return {\n    buttonActive,\n    buttonFocusVisible,\n    buttonRef: mergedButtonRef,\n    contextValue,\n    disabled,\n    dispatch,\n    getButtonProps,\n    getHiddenInputProps,\n    getListboxProps,\n    getOptionMetadata,\n    listboxRef: mergedListRootRef,\n    open,\n    options: optionValues,\n    value: selectValue,\n    highlightedOption\n  };\n}\nexport { useSelect };", "map": {"version": 3, "names": ["_extends", "React", "unstable_useForkRef", "useForkRef", "unstable_useId", "useId", "unstable_useEnhancedEffect", "useEnhancedEffect", "visuallyHidden", "visuallyHiddenStyle", "useButton", "SelectActionTypes", "ListActionTypes", "useList", "defaultOptionStringifier", "useCompoundParent", "extractEventHandlers", "selectReducer", "combineHooksSlotProps", "defaultFormValueProvider", "selectedOption", "Array", "isArray", "length", "JSON", "stringify", "map", "o", "value", "useSelect", "props", "areOptionsEqual", "buttonRef", "buttonRefProp", "defaultOpen", "defaultValue", "defaultValueProp", "disabled", "listboxId", "listboxIdProp", "listboxRef", "listboxRefProp", "multiple", "name", "required", "onChange", "onHighlightChange", "onOpenChange", "open", "openProp", "options", "optionsParam", "getOptionAsString", "getSerializedValue", "valueProp", "componentName", "useRef", "handleButtonRef", "undefined", "useMemo", "subitems", "contextValue", "compoundComponentContextValue", "Map", "option", "index", "label", "ref", "createRef", "id", "handleListboxRef", "getRootProps", "getButtonRootProps", "active", "buttonActive", "focusVisible", "buttonFocusVisible", "rootRef", "mergedButtonRef", "optionValues", "from", "keys", "getOptionByValue", "useCallback", "valueToGet", "similarValue", "find", "optionValue", "get", "isItemDisabled", "valueToCheck", "_option$disabled", "stringifyOption", "controlledState", "<PERSON><PERSON><PERSON><PERSON>", "getItemId", "itemValue", "_options$get", "handleSelectionChange", "event", "newValues", "_newValues$", "handleHighlightChange", "newValue", "handleStateChange", "field", "fieldValue", "type", "_buttonRef$current", "current", "focus", "getItemDomElement", "itemId", "_subitems$get$ref$cur", "_subitems$get", "useListParameters", "getInitialState", "_defaultValue", "highlightedValue", "controlledProps", "focusManagement", "itemComparer", "onStateChange", "reducerActionContext", "items", "getItemAsString", "selectionMode", "stateReducer", "dispatch", "getListboxRootProps", "listContextValue", "state", "highlightedOption", "selectedOptions", "mergedListRootRef", "isInitiallyOpen", "_getOptionByValue", "optionRef", "preventScroll", "listboxClientRect", "getBoundingClientRect", "optionClientRect", "top", "scrollTop", "bottom", "getOptionMetadata", "createHandleButtonClick", "externalEventHandlers", "_externalEventHandler", "onClick", "call", "defaultMuiPrevented", "action", "buttonClick", "createHandleButtonKeyDown", "otherHandlers", "_otherHandlers$onKeyD", "onKeyDown", "key", "preventDefault", "keyDown", "getButtonOwnRootProps", "arguments", "getSelectTriggerProps", "role", "getButtonProps", "externalProps", "combinedProps", "createListboxHandleBlur", "_otherHandlers$onBlur", "_listboxRef$current", "onBlur", "contains", "relatedTarget", "getOwnListboxHandlers", "getListboxProps", "getCombinedRootProps", "useDebugValue", "selectValue", "selectedOptionsMetadata", "v", "filter", "_getOptionMetadata", "createHandleHiddenInputChange", "_externalEventHandler2", "target", "clearSelection", "browserAutoFill", "item", "getHiddenInputProps", "tabIndex", "style"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/useSelect/useSelect.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useForkRef as useForkRef, unstable_useId as useId, unstable_useEnhancedEffect as useEnhancedEffect, visuallyHidden as visuallyHiddenStyle } from '@mui/utils';\nimport { useButton } from '../useButton';\nimport { SelectActionTypes } from './useSelect.types';\nimport { ListActionTypes, useList } from '../useList';\nimport { defaultOptionStringifier } from './defaultOptionStringifier';\nimport { useCompoundParent } from '../useCompound';\nimport { extractEventHandlers } from '../utils/extractEventHandlers';\nimport { selectReducer } from './selectReducer';\nimport { combineHooksSlotProps } from '../utils/combineHooksSlotProps';\nfunction defaultFormValueProvider(selectedOption) {\n  if (Array.isArray(selectedOption)) {\n    if (selectedOption.length === 0) {\n      return '';\n    }\n    return JSON.stringify(selectedOption.map(o => o.value));\n  }\n  if ((selectedOption == null ? void 0 : selectedOption.value) == null) {\n    return '';\n  }\n  if (typeof selectedOption.value === 'string' || typeof selectedOption.value === 'number') {\n    return selectedOption.value;\n  }\n  return JSON.stringify(selectedOption.value);\n}\n\n/**\n *\n * Demos:\n *\n * - [Select](https://mui.com/base-ui/react-select/#hooks)\n *\n * API:\n *\n * - [useSelect API](https://mui.com/base-ui/react-select/hooks-api/#use-select)\n */\nfunction useSelect(props) {\n  const {\n    areOptionsEqual,\n    buttonRef: buttonRefProp,\n    defaultOpen = false,\n    defaultValue: defaultValueProp,\n    disabled = false,\n    listboxId: listboxIdProp,\n    listboxRef: listboxRefProp,\n    multiple = false,\n    name,\n    required,\n    onChange,\n    onHighlightChange,\n    onOpenChange,\n    open: openProp,\n    options: optionsParam,\n    getOptionAsString = defaultOptionStringifier,\n    getSerializedValue = defaultFormValueProvider,\n    value: valueProp,\n    componentName = 'useSelect'\n  } = props;\n  const buttonRef = React.useRef(null);\n  const handleButtonRef = useForkRef(buttonRefProp, buttonRef);\n  const listboxRef = React.useRef(null);\n  const listboxId = useId(listboxIdProp);\n  let defaultValue;\n  if (valueProp === undefined && defaultValueProp === undefined) {\n    defaultValue = [];\n  } else if (defaultValueProp !== undefined) {\n    if (multiple) {\n      defaultValue = defaultValueProp;\n    } else {\n      defaultValue = defaultValueProp == null ? [] : [defaultValueProp];\n    }\n  }\n  const value = React.useMemo(() => {\n    if (valueProp !== undefined) {\n      if (multiple) {\n        return valueProp;\n      }\n      return valueProp == null ? [] : [valueProp];\n    }\n    return undefined;\n  }, [valueProp, multiple]);\n  const {\n    subitems,\n    contextValue: compoundComponentContextValue\n  } = useCompoundParent();\n  const options = React.useMemo(() => {\n    if (optionsParam != null) {\n      return new Map(optionsParam.map((option, index) => [option.value, {\n        value: option.value,\n        label: option.label,\n        disabled: option.disabled,\n        ref: /*#__PURE__*/React.createRef(),\n        id: `${listboxId}_${index}`\n      }]));\n    }\n    return subitems;\n  }, [optionsParam, subitems, listboxId]);\n  const handleListboxRef = useForkRef(listboxRefProp, listboxRef);\n  const {\n    getRootProps: getButtonRootProps,\n    active: buttonActive,\n    focusVisible: buttonFocusVisible,\n    rootRef: mergedButtonRef\n  } = useButton({\n    disabled,\n    rootRef: handleButtonRef\n  });\n  const optionValues = React.useMemo(() => Array.from(options.keys()), [options]);\n  const getOptionByValue = React.useCallback(valueToGet => {\n    // This can't be simply `options.get(valueToGet)` because of the `areOptionsEqual` prop.\n    // If it's provided, we assume that the user wants to compare the options by value.\n    if (areOptionsEqual !== undefined) {\n      const similarValue = optionValues.find(optionValue => areOptionsEqual(optionValue, valueToGet));\n      return options.get(similarValue);\n    }\n    return options.get(valueToGet);\n  }, [options, areOptionsEqual, optionValues]);\n  const isItemDisabled = React.useCallback(valueToCheck => {\n    var _option$disabled;\n    const option = getOptionByValue(valueToCheck);\n    return (_option$disabled = option == null ? void 0 : option.disabled) != null ? _option$disabled : false;\n  }, [getOptionByValue]);\n  const stringifyOption = React.useCallback(valueToCheck => {\n    const option = getOptionByValue(valueToCheck);\n    if (!option) {\n      return '';\n    }\n    return getOptionAsString(option);\n  }, [getOptionByValue, getOptionAsString]);\n  const controlledState = React.useMemo(() => ({\n    selectedValues: value,\n    open: openProp\n  }), [value, openProp]);\n  const getItemId = React.useCallback(itemValue => {\n    var _options$get;\n    return (_options$get = options.get(itemValue)) == null ? void 0 : _options$get.id;\n  }, [options]);\n  const handleSelectionChange = React.useCallback((event, newValues) => {\n    if (multiple) {\n      onChange == null || onChange(event, newValues);\n    } else {\n      var _newValues$;\n      onChange == null || onChange(event, (_newValues$ = newValues[0]) != null ? _newValues$ : null);\n    }\n  }, [multiple, onChange]);\n  const handleHighlightChange = React.useCallback((event, newValue) => {\n    onHighlightChange == null || onHighlightChange(event, newValue != null ? newValue : null);\n  }, [onHighlightChange]);\n  const handleStateChange = React.useCallback((event, field, fieldValue) => {\n    if (field === 'open') {\n      onOpenChange == null || onOpenChange(fieldValue);\n      if (fieldValue === false && (event == null ? void 0 : event.type) !== 'blur') {\n        var _buttonRef$current;\n        (_buttonRef$current = buttonRef.current) == null || _buttonRef$current.focus();\n      }\n    }\n  }, [onOpenChange]);\n  const getItemDomElement = React.useCallback(itemId => {\n    var _subitems$get$ref$cur, _subitems$get;\n    if (itemId == null) {\n      return null;\n    }\n    return (_subitems$get$ref$cur = (_subitems$get = subitems.get(itemId)) == null ? void 0 : _subitems$get.ref.current) != null ? _subitems$get$ref$cur : null;\n  }, [subitems]);\n  const useListParameters = {\n    getInitialState: () => {\n      var _defaultValue;\n      return {\n        highlightedValue: null,\n        selectedValues: (_defaultValue = defaultValue) != null ? _defaultValue : [],\n        open: defaultOpen\n      };\n    },\n    getItemId,\n    controlledProps: controlledState,\n    focusManagement: 'DOM',\n    getItemDomElement,\n    itemComparer: areOptionsEqual,\n    isItemDisabled,\n    rootRef: handleListboxRef,\n    onChange: handleSelectionChange,\n    onHighlightChange: handleHighlightChange,\n    onStateChange: handleStateChange,\n    reducerActionContext: React.useMemo(() => ({\n      multiple\n    }), [multiple]),\n    items: optionValues,\n    getItemAsString: stringifyOption,\n    selectionMode: multiple ? 'multiple' : 'single',\n    stateReducer: selectReducer,\n    componentName\n  };\n  const {\n    dispatch,\n    getRootProps: getListboxRootProps,\n    contextValue: listContextValue,\n    state: {\n      open,\n      highlightedValue: highlightedOption,\n      selectedValues: selectedOptions\n    },\n    rootRef: mergedListRootRef\n  } = useList(useListParameters);\n\n  // store the initial open state to prevent focus stealing\n  // (the first option gets focused only when the select is opened by the user)\n  const isInitiallyOpen = React.useRef(open);\n  useEnhancedEffect(() => {\n    if (open && highlightedOption !== null) {\n      var _getOptionByValue;\n      const optionRef = (_getOptionByValue = getOptionByValue(highlightedOption)) == null ? void 0 : _getOptionByValue.ref;\n      if (!listboxRef.current || !(optionRef != null && optionRef.current)) {\n        return;\n      }\n      if (!isInitiallyOpen.current) {\n        optionRef.current.focus({\n          preventScroll: true\n        });\n      }\n      const listboxClientRect = listboxRef.current.getBoundingClientRect();\n      const optionClientRect = optionRef.current.getBoundingClientRect();\n      if (optionClientRect.top < listboxClientRect.top) {\n        listboxRef.current.scrollTop -= listboxClientRect.top - optionClientRect.top;\n      } else if (optionClientRect.bottom > listboxClientRect.bottom) {\n        listboxRef.current.scrollTop += optionClientRect.bottom - listboxClientRect.bottom;\n      }\n    }\n  }, [open, highlightedOption, getOptionByValue]);\n  const getOptionMetadata = React.useCallback(optionValue => getOptionByValue(optionValue), [getOptionByValue]);\n  const createHandleButtonClick = externalEventHandlers => event => {\n    var _externalEventHandler;\n    externalEventHandlers == null || (_externalEventHandler = externalEventHandlers.onClick) == null || _externalEventHandler.call(externalEventHandlers, event);\n    if (!event.defaultMuiPrevented) {\n      const action = {\n        type: SelectActionTypes.buttonClick,\n        event\n      };\n      dispatch(action);\n    }\n  };\n  const createHandleButtonKeyDown = otherHandlers => event => {\n    var _otherHandlers$onKeyD;\n    (_otherHandlers$onKeyD = otherHandlers.onKeyDown) == null || _otherHandlers$onKeyD.call(otherHandlers, event);\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    if (event.key === 'ArrowDown' || event.key === 'ArrowUp') {\n      event.preventDefault();\n      dispatch({\n        type: ListActionTypes.keyDown,\n        key: event.key,\n        event\n      });\n    }\n  };\n  const getButtonOwnRootProps = (otherHandlers = {}) => ({\n    onClick: createHandleButtonClick(otherHandlers),\n    onKeyDown: createHandleButtonKeyDown(otherHandlers)\n  });\n  const getSelectTriggerProps = (otherHandlers = {}) => {\n    return _extends({}, otherHandlers, getButtonOwnRootProps(otherHandlers), {\n      role: 'combobox',\n      'aria-expanded': open,\n      'aria-controls': listboxId\n    });\n  };\n  const getButtonProps = (externalProps = {}) => {\n    const externalEventHandlers = extractEventHandlers(externalProps);\n    const combinedProps = combineHooksSlotProps(getSelectTriggerProps, getButtonRootProps);\n    return _extends({}, externalProps, combinedProps(externalEventHandlers));\n  };\n  const createListboxHandleBlur = otherHandlers => event => {\n    var _otherHandlers$onBlur, _listboxRef$current;\n    (_otherHandlers$onBlur = otherHandlers.onBlur) == null || _otherHandlers$onBlur.call(otherHandlers, event);\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    if ((_listboxRef$current = listboxRef.current) != null && _listboxRef$current.contains(event.relatedTarget) || event.relatedTarget === buttonRef.current) {\n      event.defaultMuiPrevented = true;\n    }\n  };\n  const getOwnListboxHandlers = (otherHandlers = {}) => ({\n    onBlur: createListboxHandleBlur(otherHandlers)\n  });\n  const getListboxProps = (externalProps = {}) => {\n    const externalEventHandlers = extractEventHandlers(externalProps);\n    const getCombinedRootProps = combineHooksSlotProps(getOwnListboxHandlers, getListboxRootProps);\n    return _extends({\n      id: listboxId,\n      role: 'listbox',\n      'aria-multiselectable': multiple ? 'true' : undefined\n    }, externalProps, getCombinedRootProps(externalEventHandlers));\n  };\n  React.useDebugValue({\n    selectedOptions,\n    highlightedOption,\n    open\n  });\n  const contextValue = React.useMemo(() => _extends({}, listContextValue, compoundComponentContextValue), [listContextValue, compoundComponentContextValue]);\n  let selectValue;\n  if (props.multiple) {\n    selectValue = selectedOptions;\n  } else {\n    selectValue = selectedOptions.length > 0 ? selectedOptions[0] : null;\n  }\n  let selectedOptionsMetadata;\n  if (multiple) {\n    selectedOptionsMetadata = selectValue.map(v => getOptionMetadata(v)).filter(o => o !== undefined);\n  } else {\n    var _getOptionMetadata;\n    selectedOptionsMetadata = (_getOptionMetadata = getOptionMetadata(selectValue)) != null ? _getOptionMetadata : null;\n  }\n  const createHandleHiddenInputChange = externalEventHandlers => event => {\n    var _externalEventHandler2;\n    externalEventHandlers == null || (_externalEventHandler2 = externalEventHandlers.onChange) == null || _externalEventHandler2.call(externalEventHandlers, event);\n    if (event.defaultMuiPrevented) {\n      return;\n    }\n    const option = options.get(event.target.value);\n\n    // support autofill\n    if (event.target.value === '') {\n      dispatch({\n        type: ListActionTypes.clearSelection\n      });\n    } else if (option !== undefined) {\n      dispatch({\n        type: SelectActionTypes.browserAutoFill,\n        item: option.value,\n        event\n      });\n    }\n  };\n  const getHiddenInputProps = (externalProps = {}) => {\n    const externalEventHandlers = extractEventHandlers(externalProps);\n    return _extends({\n      name,\n      tabIndex: -1,\n      'aria-hidden': true,\n      required: required ? true : undefined,\n      value: getSerializedValue(selectedOptionsMetadata),\n      style: visuallyHiddenStyle\n    }, externalProps, {\n      onChange: createHandleHiddenInputChange(externalEventHandlers)\n    });\n  };\n  return {\n    buttonActive,\n    buttonFocusVisible,\n    buttonRef: mergedButtonRef,\n    contextValue,\n    disabled,\n    dispatch,\n    getButtonProps,\n    getHiddenInputProps,\n    getListboxProps,\n    getOptionMetadata,\n    listboxRef: mergedListRootRef,\n    open,\n    options: optionValues,\n    value: selectValue,\n    highlightedOption\n  };\n}\nexport { useSelect };"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,mBAAmB,IAAIC,UAAU,EAAEC,cAAc,IAAIC,KAAK,EAAEC,0BAA0B,IAAIC,iBAAiB,EAAEC,cAAc,IAAIC,mBAAmB,QAAQ,YAAY;AAC/K,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,iBAAiB,QAAQ,mBAAmB;AACrD,SAASC,eAAe,EAAEC,OAAO,QAAQ,YAAY;AACrD,SAASC,wBAAwB,QAAQ,4BAA4B;AACrE,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,SAASC,oBAAoB,QAAQ,+BAA+B;AACpE,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SAASC,wBAAwBA,CAACC,cAAc,EAAE;EAChD,IAAIC,KAAK,CAACC,OAAO,CAACF,cAAc,CAAC,EAAE;IACjC,IAAIA,cAAc,CAACG,MAAM,KAAK,CAAC,EAAE;MAC/B,OAAO,EAAE;IACX;IACA,OAAOC,IAAI,CAACC,SAAS,CAACL,cAAc,CAACM,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACC,KAAK,CAAC,CAAC;EACzD;EACA,IAAI,CAACR,cAAc,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,cAAc,CAACQ,KAAK,KAAK,IAAI,EAAE;IACpE,OAAO,EAAE;EACX;EACA,IAAI,OAAOR,cAAc,CAACQ,KAAK,KAAK,QAAQ,IAAI,OAAOR,cAAc,CAACQ,KAAK,KAAK,QAAQ,EAAE;IACxF,OAAOR,cAAc,CAACQ,KAAK;EAC7B;EACA,OAAOJ,IAAI,CAACC,SAAS,CAACL,cAAc,CAACQ,KAAK,CAAC;AAC7C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,SAASA,CAACC,KAAK,EAAE;EACxB,MAAM;IACJC,eAAe;IACfC,SAAS,EAAEC,aAAa;IACxBC,WAAW,GAAG,KAAK;IACnBC,YAAY,EAAEC,gBAAgB;IAC9BC,QAAQ,GAAG,KAAK;IAChBC,SAAS,EAAEC,aAAa;IACxBC,UAAU,EAAEC,cAAc;IAC1BC,QAAQ,GAAG,KAAK;IAChBC,IAAI;IACJC,QAAQ;IACRC,QAAQ;IACRC,iBAAiB;IACjBC,YAAY;IACZC,IAAI,EAAEC,QAAQ;IACdC,OAAO,EAAEC,YAAY;IACrBC,iBAAiB,GAAGtC,wBAAwB;IAC5CuC,kBAAkB,GAAGlC,wBAAwB;IAC7CS,KAAK,EAAE0B,SAAS;IAChBC,aAAa,GAAG;EAClB,CAAC,GAAGzB,KAAK;EACT,MAAME,SAAS,GAAG/B,KAAK,CAACuD,MAAM,CAAC,IAAI,CAAC;EACpC,MAAMC,eAAe,GAAGtD,UAAU,CAAC8B,aAAa,EAAED,SAAS,CAAC;EAC5D,MAAMQ,UAAU,GAAGvC,KAAK,CAACuD,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMlB,SAAS,GAAGjC,KAAK,CAACkC,aAAa,CAAC;EACtC,IAAIJ,YAAY;EAChB,IAAImB,SAAS,KAAKI,SAAS,IAAItB,gBAAgB,KAAKsB,SAAS,EAAE;IAC7DvB,YAAY,GAAG,EAAE;EACnB,CAAC,MAAM,IAAIC,gBAAgB,KAAKsB,SAAS,EAAE;IACzC,IAAIhB,QAAQ,EAAE;MACZP,YAAY,GAAGC,gBAAgB;IACjC,CAAC,MAAM;MACLD,YAAY,GAAGC,gBAAgB,IAAI,IAAI,GAAG,EAAE,GAAG,CAACA,gBAAgB,CAAC;IACnE;EACF;EACA,MAAMR,KAAK,GAAG3B,KAAK,CAAC0D,OAAO,CAAC,MAAM;IAChC,IAAIL,SAAS,KAAKI,SAAS,EAAE;MAC3B,IAAIhB,QAAQ,EAAE;QACZ,OAAOY,SAAS;MAClB;MACA,OAAOA,SAAS,IAAI,IAAI,GAAG,EAAE,GAAG,CAACA,SAAS,CAAC;IAC7C;IACA,OAAOI,SAAS;EAClB,CAAC,EAAE,CAACJ,SAAS,EAAEZ,QAAQ,CAAC,CAAC;EACzB,MAAM;IACJkB,QAAQ;IACRC,YAAY,EAAEC;EAChB,CAAC,GAAG/C,iBAAiB,CAAC,CAAC;EACvB,MAAMmC,OAAO,GAAGjD,KAAK,CAAC0D,OAAO,CAAC,MAAM;IAClC,IAAIR,YAAY,IAAI,IAAI,EAAE;MACxB,OAAO,IAAIY,GAAG,CAACZ,YAAY,CAACzB,GAAG,CAAC,CAACsC,MAAM,EAAEC,KAAK,KAAK,CAACD,MAAM,CAACpC,KAAK,EAAE;QAChEA,KAAK,EAAEoC,MAAM,CAACpC,KAAK;QACnBsC,KAAK,EAAEF,MAAM,CAACE,KAAK;QACnB7B,QAAQ,EAAE2B,MAAM,CAAC3B,QAAQ;QACzB8B,GAAG,EAAE,aAAalE,KAAK,CAACmE,SAAS,CAAC,CAAC;QACnCC,EAAE,EAAE,GAAG/B,SAAS,IAAI2B,KAAK;MAC3B,CAAC,CAAC,CAAC,CAAC;IACN;IACA,OAAOL,QAAQ;EACjB,CAAC,EAAE,CAACT,YAAY,EAAES,QAAQ,EAAEtB,SAAS,CAAC,CAAC;EACvC,MAAMgC,gBAAgB,GAAGnE,UAAU,CAACsC,cAAc,EAAED,UAAU,CAAC;EAC/D,MAAM;IACJ+B,YAAY,EAAEC,kBAAkB;IAChCC,MAAM,EAAEC,YAAY;IACpBC,YAAY,EAAEC,kBAAkB;IAChCC,OAAO,EAAEC;EACX,CAAC,GAAGpE,SAAS,CAAC;IACZ2B,QAAQ;IACRwC,OAAO,EAAEpB;EACX,CAAC,CAAC;EACF,MAAMsB,YAAY,GAAG9E,KAAK,CAAC0D,OAAO,CAAC,MAAMtC,KAAK,CAAC2D,IAAI,CAAC9B,OAAO,CAAC+B,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC/B,OAAO,CAAC,CAAC;EAC/E,MAAMgC,gBAAgB,GAAGjF,KAAK,CAACkF,WAAW,CAACC,UAAU,IAAI;IACvD;IACA;IACA,IAAIrD,eAAe,KAAK2B,SAAS,EAAE;MACjC,MAAM2B,YAAY,GAAGN,YAAY,CAACO,IAAI,CAACC,WAAW,IAAIxD,eAAe,CAACwD,WAAW,EAAEH,UAAU,CAAC,CAAC;MAC/F,OAAOlC,OAAO,CAACsC,GAAG,CAACH,YAAY,CAAC;IAClC;IACA,OAAOnC,OAAO,CAACsC,GAAG,CAACJ,UAAU,CAAC;EAChC,CAAC,EAAE,CAAClC,OAAO,EAAEnB,eAAe,EAAEgD,YAAY,CAAC,CAAC;EAC5C,MAAMU,cAAc,GAAGxF,KAAK,CAACkF,WAAW,CAACO,YAAY,IAAI;IACvD,IAAIC,gBAAgB;IACpB,MAAM3B,MAAM,GAAGkB,gBAAgB,CAACQ,YAAY,CAAC;IAC7C,OAAO,CAACC,gBAAgB,GAAG3B,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC3B,QAAQ,KAAK,IAAI,GAAGsD,gBAAgB,GAAG,KAAK;EAC1G,CAAC,EAAE,CAACT,gBAAgB,CAAC,CAAC;EACtB,MAAMU,eAAe,GAAG3F,KAAK,CAACkF,WAAW,CAACO,YAAY,IAAI;IACxD,MAAM1B,MAAM,GAAGkB,gBAAgB,CAACQ,YAAY,CAAC;IAC7C,IAAI,CAAC1B,MAAM,EAAE;MACX,OAAO,EAAE;IACX;IACA,OAAOZ,iBAAiB,CAACY,MAAM,CAAC;EAClC,CAAC,EAAE,CAACkB,gBAAgB,EAAE9B,iBAAiB,CAAC,CAAC;EACzC,MAAMyC,eAAe,GAAG5F,KAAK,CAAC0D,OAAO,CAAC,OAAO;IAC3CmC,cAAc,EAAElE,KAAK;IACrBoB,IAAI,EAAEC;EACR,CAAC,CAAC,EAAE,CAACrB,KAAK,EAAEqB,QAAQ,CAAC,CAAC;EACtB,MAAM8C,SAAS,GAAG9F,KAAK,CAACkF,WAAW,CAACa,SAAS,IAAI;IAC/C,IAAIC,YAAY;IAChB,OAAO,CAACA,YAAY,GAAG/C,OAAO,CAACsC,GAAG,CAACQ,SAAS,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGC,YAAY,CAAC5B,EAAE;EACnF,CAAC,EAAE,CAACnB,OAAO,CAAC,CAAC;EACb,MAAMgD,qBAAqB,GAAGjG,KAAK,CAACkF,WAAW,CAAC,CAACgB,KAAK,EAAEC,SAAS,KAAK;IACpE,IAAI1D,QAAQ,EAAE;MACZG,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAACsD,KAAK,EAAEC,SAAS,CAAC;IAChD,CAAC,MAAM;MACL,IAAIC,WAAW;MACfxD,QAAQ,IAAI,IAAI,IAAIA,QAAQ,CAACsD,KAAK,EAAE,CAACE,WAAW,GAAGD,SAAS,CAAC,CAAC,CAAC,KAAK,IAAI,GAAGC,WAAW,GAAG,IAAI,CAAC;IAChG;EACF,CAAC,EAAE,CAAC3D,QAAQ,EAAEG,QAAQ,CAAC,CAAC;EACxB,MAAMyD,qBAAqB,GAAGrG,KAAK,CAACkF,WAAW,CAAC,CAACgB,KAAK,EAAEI,QAAQ,KAAK;IACnEzD,iBAAiB,IAAI,IAAI,IAAIA,iBAAiB,CAACqD,KAAK,EAAEI,QAAQ,IAAI,IAAI,GAAGA,QAAQ,GAAG,IAAI,CAAC;EAC3F,CAAC,EAAE,CAACzD,iBAAiB,CAAC,CAAC;EACvB,MAAM0D,iBAAiB,GAAGvG,KAAK,CAACkF,WAAW,CAAC,CAACgB,KAAK,EAAEM,KAAK,EAAEC,UAAU,KAAK;IACxE,IAAID,KAAK,KAAK,MAAM,EAAE;MACpB1D,YAAY,IAAI,IAAI,IAAIA,YAAY,CAAC2D,UAAU,CAAC;MAChD,IAAIA,UAAU,KAAK,KAAK,IAAI,CAACP,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACQ,IAAI,MAAM,MAAM,EAAE;QAC5E,IAAIC,kBAAkB;QACtB,CAACA,kBAAkB,GAAG5E,SAAS,CAAC6E,OAAO,KAAK,IAAI,IAAID,kBAAkB,CAACE,KAAK,CAAC,CAAC;MAChF;IACF;EACF,CAAC,EAAE,CAAC/D,YAAY,CAAC,CAAC;EAClB,MAAMgE,iBAAiB,GAAG9G,KAAK,CAACkF,WAAW,CAAC6B,MAAM,IAAI;IACpD,IAAIC,qBAAqB,EAAEC,aAAa;IACxC,IAAIF,MAAM,IAAI,IAAI,EAAE;MAClB,OAAO,IAAI;IACb;IACA,OAAO,CAACC,qBAAqB,GAAG,CAACC,aAAa,GAAGtD,QAAQ,CAAC4B,GAAG,CAACwB,MAAM,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGE,aAAa,CAAC/C,GAAG,CAAC0C,OAAO,KAAK,IAAI,GAAGI,qBAAqB,GAAG,IAAI;EAC7J,CAAC,EAAE,CAACrD,QAAQ,CAAC,CAAC;EACd,MAAMuD,iBAAiB,GAAG;IACxBC,eAAe,EAAEA,CAAA,KAAM;MACrB,IAAIC,aAAa;MACjB,OAAO;QACLC,gBAAgB,EAAE,IAAI;QACtBxB,cAAc,EAAE,CAACuB,aAAa,GAAGlF,YAAY,KAAK,IAAI,GAAGkF,aAAa,GAAG,EAAE;QAC3ErE,IAAI,EAAEd;MACR,CAAC;IACH,CAAC;IACD6D,SAAS;IACTwB,eAAe,EAAE1B,eAAe;IAChC2B,eAAe,EAAE,KAAK;IACtBT,iBAAiB;IACjBU,YAAY,EAAE1F,eAAe;IAC7B0D,cAAc;IACdZ,OAAO,EAAEP,gBAAgB;IACzBzB,QAAQ,EAAEqD,qBAAqB;IAC/BpD,iBAAiB,EAAEwD,qBAAqB;IACxCoB,aAAa,EAAElB,iBAAiB;IAChCmB,oBAAoB,EAAE1H,KAAK,CAAC0D,OAAO,CAAC,OAAO;MACzCjB;IACF,CAAC,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;IACfkF,KAAK,EAAE7C,YAAY;IACnB8C,eAAe,EAAEjC,eAAe;IAChCkC,aAAa,EAAEpF,QAAQ,GAAG,UAAU,GAAG,QAAQ;IAC/CqF,YAAY,EAAE9G,aAAa;IAC3BsC;EACF,CAAC;EACD,MAAM;IACJyE,QAAQ;IACRzD,YAAY,EAAE0D,mBAAmB;IACjCpE,YAAY,EAAEqE,gBAAgB;IAC9BC,KAAK,EAAE;MACLnF,IAAI;MACJsE,gBAAgB,EAAEc,iBAAiB;MACnCtC,cAAc,EAAEuC;IAClB,CAAC;IACDxD,OAAO,EAAEyD;EACX,CAAC,GAAGzH,OAAO,CAACsG,iBAAiB,CAAC;;EAE9B;EACA;EACA,MAAMoB,eAAe,GAAGtI,KAAK,CAACuD,MAAM,CAACR,IAAI,CAAC;EAC1CzC,iBAAiB,CAAC,MAAM;IACtB,IAAIyC,IAAI,IAAIoF,iBAAiB,KAAK,IAAI,EAAE;MACtC,IAAII,iBAAiB;MACrB,MAAMC,SAAS,GAAG,CAACD,iBAAiB,GAAGtD,gBAAgB,CAACkD,iBAAiB,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGI,iBAAiB,CAACrE,GAAG;MACpH,IAAI,CAAC3B,UAAU,CAACqE,OAAO,IAAI,EAAE4B,SAAS,IAAI,IAAI,IAAIA,SAAS,CAAC5B,OAAO,CAAC,EAAE;QACpE;MACF;MACA,IAAI,CAAC0B,eAAe,CAAC1B,OAAO,EAAE;QAC5B4B,SAAS,CAAC5B,OAAO,CAACC,KAAK,CAAC;UACtB4B,aAAa,EAAE;QACjB,CAAC,CAAC;MACJ;MACA,MAAMC,iBAAiB,GAAGnG,UAAU,CAACqE,OAAO,CAAC+B,qBAAqB,CAAC,CAAC;MACpE,MAAMC,gBAAgB,GAAGJ,SAAS,CAAC5B,OAAO,CAAC+B,qBAAqB,CAAC,CAAC;MAClE,IAAIC,gBAAgB,CAACC,GAAG,GAAGH,iBAAiB,CAACG,GAAG,EAAE;QAChDtG,UAAU,CAACqE,OAAO,CAACkC,SAAS,IAAIJ,iBAAiB,CAACG,GAAG,GAAGD,gBAAgB,CAACC,GAAG;MAC9E,CAAC,MAAM,IAAID,gBAAgB,CAACG,MAAM,GAAGL,iBAAiB,CAACK,MAAM,EAAE;QAC7DxG,UAAU,CAACqE,OAAO,CAACkC,SAAS,IAAIF,gBAAgB,CAACG,MAAM,GAAGL,iBAAiB,CAACK,MAAM;MACpF;IACF;EACF,CAAC,EAAE,CAAChG,IAAI,EAAEoF,iBAAiB,EAAElD,gBAAgB,CAAC,CAAC;EAC/C,MAAM+D,iBAAiB,GAAGhJ,KAAK,CAACkF,WAAW,CAACI,WAAW,IAAIL,gBAAgB,CAACK,WAAW,CAAC,EAAE,CAACL,gBAAgB,CAAC,CAAC;EAC7G,MAAMgE,uBAAuB,GAAGC,qBAAqB,IAAIhD,KAAK,IAAI;IAChE,IAAIiD,qBAAqB;IACzBD,qBAAqB,IAAI,IAAI,IAAI,CAACC,qBAAqB,GAAGD,qBAAqB,CAACE,OAAO,KAAK,IAAI,IAAID,qBAAqB,CAACE,IAAI,CAACH,qBAAqB,EAAEhD,KAAK,CAAC;IAC5J,IAAI,CAACA,KAAK,CAACoD,mBAAmB,EAAE;MAC9B,MAAMC,MAAM,GAAG;QACb7C,IAAI,EAAEhG,iBAAiB,CAAC8I,WAAW;QACnCtD;MACF,CAAC;MACD6B,QAAQ,CAACwB,MAAM,CAAC;IAClB;EACF,CAAC;EACD,MAAME,yBAAyB,GAAGC,aAAa,IAAIxD,KAAK,IAAI;IAC1D,IAAIyD,qBAAqB;IACzB,CAACA,qBAAqB,GAAGD,aAAa,CAACE,SAAS,KAAK,IAAI,IAAID,qBAAqB,CAACN,IAAI,CAACK,aAAa,EAAExD,KAAK,CAAC;IAC7G,IAAIA,KAAK,CAACoD,mBAAmB,EAAE;MAC7B;IACF;IACA,IAAIpD,KAAK,CAAC2D,GAAG,KAAK,WAAW,IAAI3D,KAAK,CAAC2D,GAAG,KAAK,SAAS,EAAE;MACxD3D,KAAK,CAAC4D,cAAc,CAAC,CAAC;MACtB/B,QAAQ,CAAC;QACPrB,IAAI,EAAE/F,eAAe,CAACoJ,OAAO;QAC7BF,GAAG,EAAE3D,KAAK,CAAC2D,GAAG;QACd3D;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EACD,MAAM8D,qBAAqB,GAAG,SAAAA,CAAA;IAAA,IAACN,aAAa,GAAAO,SAAA,CAAA3I,MAAA,QAAA2I,SAAA,QAAAxG,SAAA,GAAAwG,SAAA,MAAG,CAAC,CAAC;IAAA,OAAM;MACrDb,OAAO,EAAEH,uBAAuB,CAACS,aAAa,CAAC;MAC/CE,SAAS,EAAEH,yBAAyB,CAACC,aAAa;IACpD,CAAC;EAAA,CAAC;EACF,MAAMQ,qBAAqB,GAAG,SAAAA,CAAA,EAAwB;IAAA,IAAvBR,aAAa,GAAAO,SAAA,CAAA3I,MAAA,QAAA2I,SAAA,QAAAxG,SAAA,GAAAwG,SAAA,MAAG,CAAC,CAAC;IAC/C,OAAOlK,QAAQ,CAAC,CAAC,CAAC,EAAE2J,aAAa,EAAEM,qBAAqB,CAACN,aAAa,CAAC,EAAE;MACvES,IAAI,EAAE,UAAU;MAChB,eAAe,EAAEpH,IAAI;MACrB,eAAe,EAAEV;IACnB,CAAC,CAAC;EACJ,CAAC;EACD,MAAM+H,cAAc,GAAG,SAAAA,CAAA,EAAwB;IAAA,IAAvBC,aAAa,GAAAJ,SAAA,CAAA3I,MAAA,QAAA2I,SAAA,QAAAxG,SAAA,GAAAwG,SAAA,MAAG,CAAC,CAAC;IACxC,MAAMf,qBAAqB,GAAGnI,oBAAoB,CAACsJ,aAAa,CAAC;IACjE,MAAMC,aAAa,GAAGrJ,qBAAqB,CAACiJ,qBAAqB,EAAE3F,kBAAkB,CAAC;IACtF,OAAOxE,QAAQ,CAAC,CAAC,CAAC,EAAEsK,aAAa,EAAEC,aAAa,CAACpB,qBAAqB,CAAC,CAAC;EAC1E,CAAC;EACD,MAAMqB,uBAAuB,GAAGb,aAAa,IAAIxD,KAAK,IAAI;IACxD,IAAIsE,qBAAqB,EAAEC,mBAAmB;IAC9C,CAACD,qBAAqB,GAAGd,aAAa,CAACgB,MAAM,KAAK,IAAI,IAAIF,qBAAqB,CAACnB,IAAI,CAACK,aAAa,EAAExD,KAAK,CAAC;IAC1G,IAAIA,KAAK,CAACoD,mBAAmB,EAAE;MAC7B;IACF;IACA,IAAI,CAACmB,mBAAmB,GAAGlI,UAAU,CAACqE,OAAO,KAAK,IAAI,IAAI6D,mBAAmB,CAACE,QAAQ,CAACzE,KAAK,CAAC0E,aAAa,CAAC,IAAI1E,KAAK,CAAC0E,aAAa,KAAK7I,SAAS,CAAC6E,OAAO,EAAE;MACxJV,KAAK,CAACoD,mBAAmB,GAAG,IAAI;IAClC;EACF,CAAC;EACD,MAAMuB,qBAAqB,GAAG,SAAAA,CAAA;IAAA,IAACnB,aAAa,GAAAO,SAAA,CAAA3I,MAAA,QAAA2I,SAAA,QAAAxG,SAAA,GAAAwG,SAAA,MAAG,CAAC,CAAC;IAAA,OAAM;MACrDS,MAAM,EAAEH,uBAAuB,CAACb,aAAa;IAC/C,CAAC;EAAA,CAAC;EACF,MAAMoB,eAAe,GAAG,SAAAA,CAAA,EAAwB;IAAA,IAAvBT,aAAa,GAAAJ,SAAA,CAAA3I,MAAA,QAAA2I,SAAA,QAAAxG,SAAA,GAAAwG,SAAA,MAAG,CAAC,CAAC;IACzC,MAAMf,qBAAqB,GAAGnI,oBAAoB,CAACsJ,aAAa,CAAC;IACjE,MAAMU,oBAAoB,GAAG9J,qBAAqB,CAAC4J,qBAAqB,EAAE7C,mBAAmB,CAAC;IAC9F,OAAOjI,QAAQ,CAAC;MACdqE,EAAE,EAAE/B,SAAS;MACb8H,IAAI,EAAE,SAAS;MACf,sBAAsB,EAAE1H,QAAQ,GAAG,MAAM,GAAGgB;IAC9C,CAAC,EAAE4G,aAAa,EAAEU,oBAAoB,CAAC7B,qBAAqB,CAAC,CAAC;EAChE,CAAC;EACDlJ,KAAK,CAACgL,aAAa,CAAC;IAClB5C,eAAe;IACfD,iBAAiB;IACjBpF;EACF,CAAC,CAAC;EACF,MAAMa,YAAY,GAAG5D,KAAK,CAAC0D,OAAO,CAAC,MAAM3D,QAAQ,CAAC,CAAC,CAAC,EAAEkI,gBAAgB,EAAEpE,6BAA6B,CAAC,EAAE,CAACoE,gBAAgB,EAAEpE,6BAA6B,CAAC,CAAC;EAC1J,IAAIoH,WAAW;EACf,IAAIpJ,KAAK,CAACY,QAAQ,EAAE;IAClBwI,WAAW,GAAG7C,eAAe;EAC/B,CAAC,MAAM;IACL6C,WAAW,GAAG7C,eAAe,CAAC9G,MAAM,GAAG,CAAC,GAAG8G,eAAe,CAAC,CAAC,CAAC,GAAG,IAAI;EACtE;EACA,IAAI8C,uBAAuB;EAC3B,IAAIzI,QAAQ,EAAE;IACZyI,uBAAuB,GAAGD,WAAW,CAACxJ,GAAG,CAAC0J,CAAC,IAAInC,iBAAiB,CAACmC,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC1J,CAAC,IAAIA,CAAC,KAAK+B,SAAS,CAAC;EACnG,CAAC,MAAM;IACL,IAAI4H,kBAAkB;IACtBH,uBAAuB,GAAG,CAACG,kBAAkB,GAAGrC,iBAAiB,CAACiC,WAAW,CAAC,KAAK,IAAI,GAAGI,kBAAkB,GAAG,IAAI;EACrH;EACA,MAAMC,6BAA6B,GAAGpC,qBAAqB,IAAIhD,KAAK,IAAI;IACtE,IAAIqF,sBAAsB;IAC1BrC,qBAAqB,IAAI,IAAI,IAAI,CAACqC,sBAAsB,GAAGrC,qBAAqB,CAACtG,QAAQ,KAAK,IAAI,IAAI2I,sBAAsB,CAAClC,IAAI,CAACH,qBAAqB,EAAEhD,KAAK,CAAC;IAC/J,IAAIA,KAAK,CAACoD,mBAAmB,EAAE;MAC7B;IACF;IACA,MAAMvF,MAAM,GAAGd,OAAO,CAACsC,GAAG,CAACW,KAAK,CAACsF,MAAM,CAAC7J,KAAK,CAAC;;IAE9C;IACA,IAAIuE,KAAK,CAACsF,MAAM,CAAC7J,KAAK,KAAK,EAAE,EAAE;MAC7BoG,QAAQ,CAAC;QACPrB,IAAI,EAAE/F,eAAe,CAAC8K;MACxB,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI1H,MAAM,KAAKN,SAAS,EAAE;MAC/BsE,QAAQ,CAAC;QACPrB,IAAI,EAAEhG,iBAAiB,CAACgL,eAAe;QACvCC,IAAI,EAAE5H,MAAM,CAACpC,KAAK;QAClBuE;MACF,CAAC,CAAC;IACJ;EACF,CAAC;EACD,MAAM0F,mBAAmB,GAAG,SAAAA,CAAA,EAAwB;IAAA,IAAvBvB,aAAa,GAAAJ,SAAA,CAAA3I,MAAA,QAAA2I,SAAA,QAAAxG,SAAA,GAAAwG,SAAA,MAAG,CAAC,CAAC;IAC7C,MAAMf,qBAAqB,GAAGnI,oBAAoB,CAACsJ,aAAa,CAAC;IACjE,OAAOtK,QAAQ,CAAC;MACd2C,IAAI;MACJmJ,QAAQ,EAAE,CAAC,CAAC;MACZ,aAAa,EAAE,IAAI;MACnBlJ,QAAQ,EAAEA,QAAQ,GAAG,IAAI,GAAGc,SAAS;MACrC9B,KAAK,EAAEyB,kBAAkB,CAAC8H,uBAAuB,CAAC;MAClDY,KAAK,EAAEtL;IACT,CAAC,EAAE6J,aAAa,EAAE;MAChBzH,QAAQ,EAAE0I,6BAA6B,CAACpC,qBAAqB;IAC/D,CAAC,CAAC;EACJ,CAAC;EACD,OAAO;IACLzE,YAAY;IACZE,kBAAkB;IAClB5C,SAAS,EAAE8C,eAAe;IAC1BjB,YAAY;IACZxB,QAAQ;IACR2F,QAAQ;IACRqC,cAAc;IACdwB,mBAAmB;IACnBd,eAAe;IACf9B,iBAAiB;IACjBzG,UAAU,EAAE8F,iBAAiB;IAC7BtF,IAAI;IACJE,OAAO,EAAE6B,YAAY;IACrBnD,KAAK,EAAEsJ,WAAW;IAClB9C;EACF,CAAC;AACH;AACA,SAASvG,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}