{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { ListContext } from '../useList';\n\n/**\n * Stabilizes the ListContext value for the Option component, so it doesn't change when sibling Options update.\n *\n * Demos:\n *\n * - [Select](https://mui.com/base-ui/react-select/#hooks)\n *\n * API:\n *\n * - [useOptionContextStabilizer API](https://mui.com/base-ui/react-select/hooks-api/#use-option-context-stabilizer)\n *\n * @param value - The value of the Option.\n * @returns The stable ListContext value.\n */\nexport function useOptionContextStabilizer(value) {\n  const listContext = React.useContext(ListContext);\n  if (!listContext) {\n    throw new Error('Option: ListContext was not found.');\n  }\n  const {\n    getItemState,\n    dispatch\n  } = listContext;\n  const {\n    highlighted,\n    selected,\n    focusable\n  } = getItemState(value);\n\n  // The local version of getItemState can be only called with the current Option's value.\n  // It doesn't make much sense to render an Option depending on other Options' state anyway.\n  const localGetItemState = React.useCallback(itemValue => {\n    if (itemValue !== value) {\n      throw new Error(['Base UI Option: Tried to access the state of another Option.', 'This is unsupported when the Option uses the OptionContextStabilizer as a performance optimization.'].join('/n'));\n    }\n    return {\n      highlighted,\n      selected,\n      focusable\n    };\n  }, [highlighted, selected, focusable, value]);\n\n  // Create a local (per Option) instance of the ListContext that changes only when\n  // the getItemState's return value changes.\n  // This makes Options re-render only when their state actually change, not when any Option's state changes.\n  const localContextValue = React.useMemo(() => ({\n    dispatch,\n    getItemState: localGetItemState\n  }), [dispatch, localGetItemState]);\n  return {\n    contextValue: localContextValue\n  };\n}", "map": {"version": 3, "names": ["React", "ListContext", "useOptionContextStabilizer", "value", "listContext", "useContext", "Error", "getItemState", "dispatch", "highlighted", "selected", "focusable", "localGetItemState", "useCallback", "itemValue", "join", "localContextValue", "useMemo", "contextValue"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/useOption/useOptionContextStabilizer.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { ListContext } from '../useList';\n\n/**\n * Stabilizes the ListContext value for the Option component, so it doesn't change when sibling Options update.\n *\n * Demos:\n *\n * - [Select](https://mui.com/base-ui/react-select/#hooks)\n *\n * API:\n *\n * - [useOptionContextStabilizer API](https://mui.com/base-ui/react-select/hooks-api/#use-option-context-stabilizer)\n *\n * @param value - The value of the Option.\n * @returns The stable ListContext value.\n */\nexport function useOptionContextStabilizer(value) {\n  const listContext = React.useContext(ListContext);\n  if (!listContext) {\n    throw new Error('Option: ListContext was not found.');\n  }\n  const {\n    getItemState,\n    dispatch\n  } = listContext;\n  const {\n    highlighted,\n    selected,\n    focusable\n  } = getItemState(value);\n\n  // The local version of getItemState can be only called with the current Option's value.\n  // It doesn't make much sense to render an Option depending on other Options' state anyway.\n  const localGetItemState = React.useCallback(itemValue => {\n    if (itemValue !== value) {\n      throw new Error(['Base UI Option: Tried to access the state of another Option.', 'This is unsupported when the Option uses the OptionContextStabilizer as a performance optimization.'].join('/n'));\n    }\n    return {\n      highlighted,\n      selected,\n      focusable\n    };\n  }, [highlighted, selected, focusable, value]);\n\n  // Create a local (per Option) instance of the ListContext that changes only when\n  // the getItemState's return value changes.\n  // This makes Options re-render only when their state actually change, not when any Option's state changes.\n  const localContextValue = React.useMemo(() => ({\n    dispatch,\n    getItemState: localGetItemState\n  }), [dispatch, localGetItemState]);\n  return {\n    contextValue: localContextValue\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,QAAQ,YAAY;;AAExC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,0BAA0BA,CAACC,KAAK,EAAE;EAChD,MAAMC,WAAW,GAAGJ,KAAK,CAACK,UAAU,CAACJ,WAAW,CAAC;EACjD,IAAI,CAACG,WAAW,EAAE;IAChB,MAAM,IAAIE,KAAK,CAAC,oCAAoC,CAAC;EACvD;EACA,MAAM;IACJC,YAAY;IACZC;EACF,CAAC,GAAGJ,WAAW;EACf,MAAM;IACJK,WAAW;IACXC,QAAQ;IACRC;EACF,CAAC,GAAGJ,YAAY,CAACJ,KAAK,CAAC;;EAEvB;EACA;EACA,MAAMS,iBAAiB,GAAGZ,KAAK,CAACa,WAAW,CAACC,SAAS,IAAI;IACvD,IAAIA,SAAS,KAAKX,KAAK,EAAE;MACvB,MAAM,IAAIG,KAAK,CAAC,CAAC,8DAA8D,EAAE,qGAAqG,CAAC,CAACS,IAAI,CAAC,IAAI,CAAC,CAAC;IACrM;IACA,OAAO;MACLN,WAAW;MACXC,QAAQ;MACRC;IACF,CAAC;EACH,CAAC,EAAE,CAACF,WAAW,EAAEC,QAAQ,EAAEC,SAAS,EAAER,KAAK,CAAC,CAAC;;EAE7C;EACA;EACA;EACA,MAAMa,iBAAiB,GAAGhB,KAAK,CAACiB,OAAO,CAAC,OAAO;IAC7CT,QAAQ;IACRD,YAAY,EAAEK;EAChB,CAAC,CAAC,EAAE,CAACJ,QAAQ,EAAEI,iBAAiB,CAAC,CAAC;EAClC,OAAO;IACLM,YAAY,EAAEF;EAChB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}