{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nvar _excluded = [\"initMapStateToProps\", \"initMapDispatchToProps\", \"initMergeProps\"];\nimport verifySubselectors from './verifySubselectors';\nexport function impureFinalPropsSelectorFactory(mapStateToProps, mapDispatchToProps, mergeProps, dispatch) {\n  return function impureFinalPropsSelector(state, ownProps) {\n    return mergeProps(mapStateToProps(state, ownProps), mapDispatchToProps(dispatch, ownProps), ownProps);\n  };\n}\nexport function pureFinalPropsSelectorFactory(mapStateToProps, mapDispatchToProps, mergeProps, dispatch, _ref) {\n  var areStatesEqual = _ref.areStatesEqual,\n    areOwnPropsEqual = _ref.areOwnPropsEqual,\n    areStatePropsEqual = _ref.areStatePropsEqual;\n  var hasRunAtLeastOnce = false;\n  var state;\n  var ownProps;\n  var stateProps;\n  var dispatchProps;\n  var mergedProps;\n  function handleFirstCall(firstState, firstOwnProps) {\n    state = firstState;\n    ownProps = firstOwnProps;\n    stateProps = mapStateToProps(state, ownProps);\n    dispatchProps = mapDispatchToProps(dispatch, ownProps);\n    mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n    hasRunAtLeastOnce = true;\n    return mergedProps;\n  }\n  function handleNewPropsAndNewState() {\n    stateProps = mapStateToProps(state, ownProps);\n    if (mapDispatchToProps.dependsOnOwnProps) dispatchProps = mapDispatchToProps(dispatch, ownProps);\n    mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n    return mergedProps;\n  }\n  function handleNewProps() {\n    if (mapStateToProps.dependsOnOwnProps) stateProps = mapStateToProps(state, ownProps);\n    if (mapDispatchToProps.dependsOnOwnProps) dispatchProps = mapDispatchToProps(dispatch, ownProps);\n    mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n    return mergedProps;\n  }\n  function handleNewState() {\n    var nextStateProps = mapStateToProps(state, ownProps);\n    var statePropsChanged = !areStatePropsEqual(nextStateProps, stateProps);\n    stateProps = nextStateProps;\n    if (statePropsChanged) mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n    return mergedProps;\n  }\n  function handleSubsequentCalls(nextState, nextOwnProps) {\n    var propsChanged = !areOwnPropsEqual(nextOwnProps, ownProps);\n    var stateChanged = !areStatesEqual(nextState, state, nextOwnProps, ownProps);\n    state = nextState;\n    ownProps = nextOwnProps;\n    if (propsChanged && stateChanged) return handleNewPropsAndNewState();\n    if (propsChanged) return handleNewProps();\n    if (stateChanged) return handleNewState();\n    return mergedProps;\n  }\n  return function pureFinalPropsSelector(nextState, nextOwnProps) {\n    return hasRunAtLeastOnce ? handleSubsequentCalls(nextState, nextOwnProps) : handleFirstCall(nextState, nextOwnProps);\n  };\n} // TODO: Add more comments\n// If pure is true, the selector returned by selectorFactory will memoize its results,\n// allowing connectAdvanced's shouldComponentUpdate to return false if final\n// props have not changed. If false, the selector will always return a new\n// object and shouldComponentUpdate will always return true.\n\nexport default function finalPropsSelectorFactory(dispatch, _ref2) {\n  var initMapStateToProps = _ref2.initMapStateToProps,\n    initMapDispatchToProps = _ref2.initMapDispatchToProps,\n    initMergeProps = _ref2.initMergeProps,\n    options = _objectWithoutPropertiesLoose(_ref2, _excluded);\n  var mapStateToProps = initMapStateToProps(dispatch, options);\n  var mapDispatchToProps = initMapDispatchToProps(dispatch, options);\n  var mergeProps = initMergeProps(dispatch, options);\n  if (process.env.NODE_ENV !== 'production') {\n    verifySubselectors(mapStateToProps, mapDispatchToProps, mergeProps, options.displayName);\n  }\n  var selectorFactory = options.pure ? pureFinalPropsSelectorFactory : impureFinalPropsSelectorFactory;\n  return selectorFactory(mapStateToProps, mapDispatchToProps, mergeProps, dispatch, options);\n}", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_excluded", "verifySubselectors", "impureFinalPropsSelectorFactory", "mapStateToProps", "mapDispatchToProps", "mergeProps", "dispatch", "impureFinalPropsSelector", "state", "ownProps", "pureFinalPropsSelectorFactory", "_ref", "areStatesEqual", "areOwnPropsEqual", "areStatePropsEqual", "hasRunAtLeastOnce", "stateProps", "dispatchProps", "mergedProps", "handleFirstCall", "firstState", "firstOwnProps", "handleNewPropsAndNewState", "dependsOnOwnProps", "handleNewProps", "handleNewState", "nextStateProps", "statePropsChanged", "handleSubsequentCalls", "nextState", "nextOwnProps", "propsChanged", "stateChanged", "pureFinalPropsSelector", "finalPropsSelectorFactory", "_ref2", "initMapStateToProps", "initMapDispatchToProps", "initMergeProps", "options", "process", "env", "NODE_ENV", "displayName", "selectorFactory", "pure"], "sources": ["C:/ignition/ignition-ui/node_modules/react-beautiful-dnd/node_modules/react-redux/es/connect/selectorFactory.js"], "sourcesContent": ["import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nvar _excluded = [\"initMapStateToProps\", \"initMapDispatchToProps\", \"initMergeProps\"];\nimport verifySubselectors from './verifySubselectors';\nexport function impureFinalPropsSelectorFactory(mapStateToProps, mapDispatchToProps, mergeProps, dispatch) {\n  return function impureFinalPropsSelector(state, ownProps) {\n    return mergeProps(mapStateToProps(state, ownProps), mapDispatchToProps(dispatch, ownProps), ownProps);\n  };\n}\nexport function pureFinalPropsSelectorFactory(mapStateToProps, mapDispatchToProps, mergeProps, dispatch, _ref) {\n  var areStatesEqual = _ref.areStatesEqual,\n      areOwnPropsEqual = _ref.areOwnPropsEqual,\n      areStatePropsEqual = _ref.areStatePropsEqual;\n  var hasRunAtLeastOnce = false;\n  var state;\n  var ownProps;\n  var stateProps;\n  var dispatchProps;\n  var mergedProps;\n\n  function handleFirstCall(firstState, firstOwnProps) {\n    state = firstState;\n    ownProps = firstOwnProps;\n    stateProps = mapStateToProps(state, ownProps);\n    dispatchProps = mapDispatchToProps(dispatch, ownProps);\n    mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n    hasRunAtLeastOnce = true;\n    return mergedProps;\n  }\n\n  function handleNewPropsAndNewState() {\n    stateProps = mapStateToProps(state, ownProps);\n    if (mapDispatchToProps.dependsOnOwnProps) dispatchProps = mapDispatchToProps(dispatch, ownProps);\n    mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n    return mergedProps;\n  }\n\n  function handleNewProps() {\n    if (mapStateToProps.dependsOnOwnProps) stateProps = mapStateToProps(state, ownProps);\n    if (mapDispatchToProps.dependsOnOwnProps) dispatchProps = mapDispatchToProps(dispatch, ownProps);\n    mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n    return mergedProps;\n  }\n\n  function handleNewState() {\n    var nextStateProps = mapStateToProps(state, ownProps);\n    var statePropsChanged = !areStatePropsEqual(nextStateProps, stateProps);\n    stateProps = nextStateProps;\n    if (statePropsChanged) mergedProps = mergeProps(stateProps, dispatchProps, ownProps);\n    return mergedProps;\n  }\n\n  function handleSubsequentCalls(nextState, nextOwnProps) {\n    var propsChanged = !areOwnPropsEqual(nextOwnProps, ownProps);\n    var stateChanged = !areStatesEqual(nextState, state, nextOwnProps, ownProps);\n    state = nextState;\n    ownProps = nextOwnProps;\n    if (propsChanged && stateChanged) return handleNewPropsAndNewState();\n    if (propsChanged) return handleNewProps();\n    if (stateChanged) return handleNewState();\n    return mergedProps;\n  }\n\n  return function pureFinalPropsSelector(nextState, nextOwnProps) {\n    return hasRunAtLeastOnce ? handleSubsequentCalls(nextState, nextOwnProps) : handleFirstCall(nextState, nextOwnProps);\n  };\n} // TODO: Add more comments\n// If pure is true, the selector returned by selectorFactory will memoize its results,\n// allowing connectAdvanced's shouldComponentUpdate to return false if final\n// props have not changed. If false, the selector will always return a new\n// object and shouldComponentUpdate will always return true.\n\nexport default function finalPropsSelectorFactory(dispatch, _ref2) {\n  var initMapStateToProps = _ref2.initMapStateToProps,\n      initMapDispatchToProps = _ref2.initMapDispatchToProps,\n      initMergeProps = _ref2.initMergeProps,\n      options = _objectWithoutPropertiesLoose(_ref2, _excluded);\n\n  var mapStateToProps = initMapStateToProps(dispatch, options);\n  var mapDispatchToProps = initMapDispatchToProps(dispatch, options);\n  var mergeProps = initMergeProps(dispatch, options);\n\n  if (process.env.NODE_ENV !== 'production') {\n    verifySubselectors(mapStateToProps, mapDispatchToProps, mergeProps, options.displayName);\n  }\n\n  var selectorFactory = options.pure ? pureFinalPropsSelectorFactory : impureFinalPropsSelectorFactory;\n  return selectorFactory(mapStateToProps, mapDispatchToProps, mergeProps, dispatch, options);\n}"], "mappings": "AAAA,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,IAAIC,SAAS,GAAG,CAAC,qBAAqB,EAAE,wBAAwB,EAAE,gBAAgB,CAAC;AACnF,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAO,SAASC,+BAA+BA,CAACC,eAAe,EAAEC,kBAAkB,EAAEC,UAAU,EAAEC,QAAQ,EAAE;EACzG,OAAO,SAASC,wBAAwBA,CAACC,KAAK,EAAEC,QAAQ,EAAE;IACxD,OAAOJ,UAAU,CAACF,eAAe,CAACK,KAAK,EAAEC,QAAQ,CAAC,EAAEL,kBAAkB,CAACE,QAAQ,EAAEG,QAAQ,CAAC,EAAEA,QAAQ,CAAC;EACvG,CAAC;AACH;AACA,OAAO,SAASC,6BAA6BA,CAACP,eAAe,EAAEC,kBAAkB,EAAEC,UAAU,EAAEC,QAAQ,EAAEK,IAAI,EAAE;EAC7G,IAAIC,cAAc,GAAGD,IAAI,CAACC,cAAc;IACpCC,gBAAgB,GAAGF,IAAI,CAACE,gBAAgB;IACxCC,kBAAkB,GAAGH,IAAI,CAACG,kBAAkB;EAChD,IAAIC,iBAAiB,GAAG,KAAK;EAC7B,IAAIP,KAAK;EACT,IAAIC,QAAQ;EACZ,IAAIO,UAAU;EACd,IAAIC,aAAa;EACjB,IAAIC,WAAW;EAEf,SAASC,eAAeA,CAACC,UAAU,EAAEC,aAAa,EAAE;IAClDb,KAAK,GAAGY,UAAU;IAClBX,QAAQ,GAAGY,aAAa;IACxBL,UAAU,GAAGb,eAAe,CAACK,KAAK,EAAEC,QAAQ,CAAC;IAC7CQ,aAAa,GAAGb,kBAAkB,CAACE,QAAQ,EAAEG,QAAQ,CAAC;IACtDS,WAAW,GAAGb,UAAU,CAACW,UAAU,EAAEC,aAAa,EAAER,QAAQ,CAAC;IAC7DM,iBAAiB,GAAG,IAAI;IACxB,OAAOG,WAAW;EACpB;EAEA,SAASI,yBAAyBA,CAAA,EAAG;IACnCN,UAAU,GAAGb,eAAe,CAACK,KAAK,EAAEC,QAAQ,CAAC;IAC7C,IAAIL,kBAAkB,CAACmB,iBAAiB,EAAEN,aAAa,GAAGb,kBAAkB,CAACE,QAAQ,EAAEG,QAAQ,CAAC;IAChGS,WAAW,GAAGb,UAAU,CAACW,UAAU,EAAEC,aAAa,EAAER,QAAQ,CAAC;IAC7D,OAAOS,WAAW;EACpB;EAEA,SAASM,cAAcA,CAAA,EAAG;IACxB,IAAIrB,eAAe,CAACoB,iBAAiB,EAAEP,UAAU,GAAGb,eAAe,CAACK,KAAK,EAAEC,QAAQ,CAAC;IACpF,IAAIL,kBAAkB,CAACmB,iBAAiB,EAAEN,aAAa,GAAGb,kBAAkB,CAACE,QAAQ,EAAEG,QAAQ,CAAC;IAChGS,WAAW,GAAGb,UAAU,CAACW,UAAU,EAAEC,aAAa,EAAER,QAAQ,CAAC;IAC7D,OAAOS,WAAW;EACpB;EAEA,SAASO,cAAcA,CAAA,EAAG;IACxB,IAAIC,cAAc,GAAGvB,eAAe,CAACK,KAAK,EAAEC,QAAQ,CAAC;IACrD,IAAIkB,iBAAiB,GAAG,CAACb,kBAAkB,CAACY,cAAc,EAAEV,UAAU,CAAC;IACvEA,UAAU,GAAGU,cAAc;IAC3B,IAAIC,iBAAiB,EAAET,WAAW,GAAGb,UAAU,CAACW,UAAU,EAAEC,aAAa,EAAER,QAAQ,CAAC;IACpF,OAAOS,WAAW;EACpB;EAEA,SAASU,qBAAqBA,CAACC,SAAS,EAAEC,YAAY,EAAE;IACtD,IAAIC,YAAY,GAAG,CAAClB,gBAAgB,CAACiB,YAAY,EAAErB,QAAQ,CAAC;IAC5D,IAAIuB,YAAY,GAAG,CAACpB,cAAc,CAACiB,SAAS,EAAErB,KAAK,EAAEsB,YAAY,EAAErB,QAAQ,CAAC;IAC5ED,KAAK,GAAGqB,SAAS;IACjBpB,QAAQ,GAAGqB,YAAY;IACvB,IAAIC,YAAY,IAAIC,YAAY,EAAE,OAAOV,yBAAyB,CAAC,CAAC;IACpE,IAAIS,YAAY,EAAE,OAAOP,cAAc,CAAC,CAAC;IACzC,IAAIQ,YAAY,EAAE,OAAOP,cAAc,CAAC,CAAC;IACzC,OAAOP,WAAW;EACpB;EAEA,OAAO,SAASe,sBAAsBA,CAACJ,SAAS,EAAEC,YAAY,EAAE;IAC9D,OAAOf,iBAAiB,GAAGa,qBAAqB,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGX,eAAe,CAACU,SAAS,EAAEC,YAAY,CAAC;EACtH,CAAC;AACH,CAAC,CAAC;AACF;AACA;AACA;AACA;;AAEA,eAAe,SAASI,yBAAyBA,CAAC5B,QAAQ,EAAE6B,KAAK,EAAE;EACjE,IAAIC,mBAAmB,GAAGD,KAAK,CAACC,mBAAmB;IAC/CC,sBAAsB,GAAGF,KAAK,CAACE,sBAAsB;IACrDC,cAAc,GAAGH,KAAK,CAACG,cAAc;IACrCC,OAAO,GAAGxC,6BAA6B,CAACoC,KAAK,EAAEnC,SAAS,CAAC;EAE7D,IAAIG,eAAe,GAAGiC,mBAAmB,CAAC9B,QAAQ,EAAEiC,OAAO,CAAC;EAC5D,IAAInC,kBAAkB,GAAGiC,sBAAsB,CAAC/B,QAAQ,EAAEiC,OAAO,CAAC;EAClE,IAAIlC,UAAU,GAAGiC,cAAc,CAAChC,QAAQ,EAAEiC,OAAO,CAAC;EAElD,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzCzC,kBAAkB,CAACE,eAAe,EAAEC,kBAAkB,EAAEC,UAAU,EAAEkC,OAAO,CAACI,WAAW,CAAC;EAC1F;EAEA,IAAIC,eAAe,GAAGL,OAAO,CAACM,IAAI,GAAGnC,6BAA6B,GAAGR,+BAA+B;EACpG,OAAO0C,eAAe,CAACzC,eAAe,EAAEC,kBAAkB,EAAEC,UAAU,EAAEC,QAAQ,EAAEiC,OAAO,CAAC;AAC5F", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}