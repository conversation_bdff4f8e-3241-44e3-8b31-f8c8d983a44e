{"ast": null, "code": "'use client';\n\n// do not remove the following import (https://github.com/microsoft/TypeScript/issues/29808#issuecomment-**********)\n/* eslint-disable @typescript-eslint/no-unused-vars */\n// @ts-ignore\nimport { unstable_createCssVarsProvider as createCssVarsProvider } from '@mui/system';\nimport defaultTheme from './defaultTheme';\nimport THEME_ID from './identifier';\nimport { defaultConfig } from '../InitColorSchemeScript/InitColorSchemeScript';\nconst {\n  CssVarsProvider,\n  useColorScheme,\n  getInitColorSchemeScript: getInitColorSchemeScriptSystem\n} = createCssVarsProvider({\n  themeId: THEME_ID,\n  theme: defaultTheme,\n  attribute: defaultConfig.attribute,\n  modeStorageKey: defaultConfig.modeStorageKey,\n  colorSchemeStorageKey: defaultConfig.colorSchemeStorageKey,\n  defaultColorScheme: {\n    light: defaultConfig.defaultLightColorScheme,\n    dark: defaultConfig.defaultDarkColorScheme\n  }\n});\n\n/**\n * @deprecated use `InitColorSchemeScript` instead\n *\n * ```diff\n * - import { getInitColorSchemeScript } from '@mui/joy/styles';\n * + import InitColorSchemeScript from '@mui/joy/InitColorSchemeScript';\n *\n * - getInitColorSchemeScript();\n * + <InitColorSchemeScript />;\n * ```\n */\nexport const getInitColorSchemeScript = getInitColorSchemeScriptSystem;\nexport { CssVarsProvider, useColorScheme };", "map": {"version": 3, "names": ["unstable_createCssVarsProvider", "createCssVarsProvider", "defaultTheme", "THEME_ID", "defaultConfig", "CssVarsProvider", "useColorScheme", "getInitColorSchemeScript", "getInitColorSchemeScriptSystem", "themeId", "theme", "attribute", "modeStorageKey", "colorSchemeStorageKey", "defaultColorScheme", "light", "defaultLightColorScheme", "dark", "defaultDarkColorScheme"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/styles/CssVarsProvider.js"], "sourcesContent": ["'use client';\n\n// do not remove the following import (https://github.com/microsoft/TypeScript/issues/29808#issuecomment-**********)\n/* eslint-disable @typescript-eslint/no-unused-vars */\n// @ts-ignore\nimport { unstable_createCssVarsProvider as createCssVarsProvider } from '@mui/system';\nimport defaultTheme from './defaultTheme';\nimport THEME_ID from './identifier';\nimport { defaultConfig } from '../InitColorSchemeScript/InitColorSchemeScript';\nconst {\n  CssVarsProvider,\n  useColorScheme,\n  getInitColorSchemeScript: getInitColorSchemeScriptSystem\n} = createCssVarsProvider({\n  themeId: THEME_ID,\n  theme: defaultTheme,\n  attribute: defaultConfig.attribute,\n  modeStorageKey: defaultConfig.modeStorageKey,\n  colorSchemeStorageKey: defaultConfig.colorSchemeStorageKey,\n  defaultColorScheme: {\n    light: defaultConfig.defaultLightColorScheme,\n    dark: defaultConfig.defaultDarkColorScheme\n  }\n});\n\n/**\n * @deprecated use `InitColorSchemeScript` instead\n *\n * ```diff\n * - import { getInitColorSchemeScript } from '@mui/joy/styles';\n * + import InitColorSchemeScript from '@mui/joy/InitColorSchemeScript';\n *\n * - getInitColorSchemeScript();\n * + <InitColorSchemeScript />;\n * ```\n */\nexport const getInitColorSchemeScript = getInitColorSchemeScriptSystem;\nexport { CssVarsProvider, useColorScheme };"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA,SAASA,8BAA8B,IAAIC,qBAAqB,QAAQ,aAAa;AACrF,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,QAAQ,MAAM,cAAc;AACnC,SAASC,aAAa,QAAQ,gDAAgD;AAC9E,MAAM;EACJC,eAAe;EACfC,cAAc;EACdC,wBAAwB,EAAEC;AAC5B,CAAC,GAAGP,qBAAqB,CAAC;EACxBQ,OAAO,EAAEN,QAAQ;EACjBO,KAAK,EAAER,YAAY;EACnBS,SAAS,EAAEP,aAAa,CAACO,SAAS;EAClCC,cAAc,EAAER,aAAa,CAACQ,cAAc;EAC5CC,qBAAqB,EAAET,aAAa,CAACS,qBAAqB;EAC1DC,kBAAkB,EAAE;IAClBC,KAAK,EAAEX,aAAa,CAACY,uBAAuB;IAC5CC,IAAI,EAAEb,aAAa,CAACc;EACtB;AACF,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMX,wBAAwB,GAAGC,8BAA8B;AACtE,SAASH,eAAe,EAAEC,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}