{"ast": null, "code": "'use client';\n\nexport { useInput } from './useInput';\nexport * from './useInput.types';", "map": {"version": 3, "names": ["useInput"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/useInput/index.js"], "sourcesContent": ["'use client';\n\nexport { useInput } from './useInput';\nexport * from './useInput.types';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,QAAQ,QAAQ,YAAY;AACrC,cAAc,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}