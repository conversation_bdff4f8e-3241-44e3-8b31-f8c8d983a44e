{"ast": null, "code": "'use client';\n\nexport { Switch } from './Switch';\nexport * from './Switch.types';\nexport * from './switchClasses';", "map": {"version": 3, "names": ["Switch"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/Switch/index.js"], "sourcesContent": ["'use client';\n\nexport { Switch } from './Switch';\nexport * from './Switch.types';\nexport * from './switchClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,MAAM,QAAQ,UAAU;AACjC,cAAc,gBAAgB;AAC9B,cAAc,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}