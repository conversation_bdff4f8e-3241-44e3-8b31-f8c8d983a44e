{"ast": null, "code": "'use strict';\n\n/** @type {import('./syntax')} */\nmodule.exports = SyntaxError;", "map": {"version": 3, "names": ["module", "exports", "SyntaxError"], "sources": ["C:/ignition/ignition-ui/node_modules/es-errors/syntax.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./syntax')} */\nmodule.exports = SyntaxError;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACAA,MAAM,CAACC,OAAO,GAAGC,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}