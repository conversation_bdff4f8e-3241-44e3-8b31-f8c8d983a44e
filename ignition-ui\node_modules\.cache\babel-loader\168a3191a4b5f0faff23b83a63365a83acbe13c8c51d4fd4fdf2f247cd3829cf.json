{"ast": null, "code": "var _jsxFileName = \"C:\\\\ignition\\\\ignition-ui\\\\src\\\\components\\\\Sidebar\\\\RightSidebar.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport dayjs from 'dayjs';\nimport { Box, Typography, IconButton, Tooltip, CircularProgress, Link } from '@mui/material';\nimport { APIURL, mainYellowColor } from \"helpers/constants\";\nimport { getHeaders } from \"helpers/functions\";\nimport Iconify from 'components/Iconify/index';\nimport styles from './rightSidebar.module.scss';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RightSidebar = _ref => {\n  _s();\n  let {\n    isOpen,\n    onToggle\n  } = _ref;\n  const [allTasks, setAllTasks] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const navigate = useNavigate();\n  const sampleTasks = [{\n    id: 'sample-1',\n    title: 'Complete AI Project Report',\n    plan_name: 'Generative AI Learning Plan',\n    end_date: dayjs().add(2, 'hour').format(),\n    status: 'todo',\n    priority: 'urgent' // Urgent\n  }, {\n    id: 'sample-2',\n    title: 'Team Meeting on Project Progress',\n    plan_name: 'Generative AI Learning Plan',\n    end_date: dayjs().add(4, 'hour').format(),\n    status: 'todo',\n    priority: 'high' // High priority\n  }, {\n    id: 'sample-3',\n    title: 'Research on New LLM Models',\n    plan_name: 'Generative AI Learning Plan',\n    end_date: dayjs().add(1, 'day').format(),\n    status: 'done',\n    priority: 'normal' // Normal\n  }, {\n    id: 'sample-4',\n    title: 'Prepare Slides for Presentation',\n    plan_name: 'Generative AI Learning Plan',\n    end_date: dayjs().add(5, 'hour').format(),\n    status: 'todo',\n    priority: 'high' // High priority\n  }];\n  useEffect(() => {\n    fetchTodayTasks();\n\n    // Automatically update task list every 5 minutes\n    const intervalId = setInterval(() => {\n      fetchTodayTasks();\n    }, 5 * 60 * 1000);\n    return () => clearInterval(intervalId);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  const fetchTodayTasks = async () => {\n    setLoading(true);\n    try {\n      const response = await axios.get(`${APIURL}/api/tasks/todo-by-status`, {\n        headers: getHeaders()\n      });\n      const tasksForToday = response.data.filter(task => {\n        if (!task.end_date) return false;\n        return dayjs(task.end_date).format('YYYY-MM-DD') === today;\n      });\n      if (tasksForToday.length === 0) {\n        setShowSampleTasks(true);\n        setTodayTasks(sampleTasks);\n      } else {\n        setShowSampleTasks(false);\n        setTodayTasks(tasksForToday);\n      }\n    } catch (error) {\n      console.error('Error fetching today tasks:', error);\n      setShowSampleTasks(true);\n      setTodayTasks(sampleTasks);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleTaskStatusChange = async (taskId, newStatus) => {\n    if (showSampleTasks) {\n      // If displaying sample data, just update the state\n      const updatedTasks = todayTasks.map(task => task.id === taskId ? {\n        ...task,\n        status: newStatus\n      } : task);\n      setTodayTasks(updatedTasks);\n      return;\n    }\n    try {\n      await axios.put(`${APIURL}/api/tasks/${taskId}/status`, {\n        status: newStatus\n      }, {\n        headers: getHeaders()\n      });\n      fetchTodayTasks();\n    } catch (error) {\n      console.error('Error updating task status:', error);\n    }\n  };\n  const handlePlanClick = planName => {\n    if (!showSampleTasks) {\n      // Navigate to plan detail page\n      navigate('/d/plans');\n    }\n  };\n\n  // Check if a task is urgent (deadline within 3 hours)\n  const isUrgentTask = task => {\n    if (task.priority === 'urgent') return true;\n    const endTime = dayjs(task.end_date);\n    const now = dayjs();\n    const hoursLeft = endTime.diff(now, 'hour');\n    return hoursLeft <= 3 && hoursLeft >= 0;\n  };\n\n  // Check if a task is high priority\n  const isHighPriorityTask = task => {\n    return task.priority === 'high';\n  };\n\n  // Format time remaining\n  const formatTimeLeft = task => {\n    const endTime = dayjs(task.end_date);\n    const now = dayjs();\n    if (endTime.isBefore(now)) {\n      return 'Overdue';\n    }\n    const hoursLeft = endTime.diff(now, 'hour');\n    const minutesLeft = endTime.diff(now, 'minute') % 60;\n    if (hoursLeft === 0) {\n      return `${minutesLeft} minutes`;\n    } else if (hoursLeft < 24) {\n      return `${hoursLeft} hours ${minutesLeft} minutes`;\n    } else {\n      const daysLeft = Math.floor(hoursLeft / 24);\n      return `${daysLeft} days`;\n    }\n  };\n\n  // Sort tasks by priority and deadline\n  const sortedTasks = [...todayTasks].sort((a, b) => {\n    // Prioritize urgent tasks\n    if (isUrgentTask(a) && !isUrgentTask(b)) return -1;\n    if (!isUrgentTask(a) && isUrgentTask(b)) return 1;\n\n    // Then prioritize tasks with high priority\n    if (isHighPriorityTask(a) && !isHighPriorityTask(b)) return -1;\n    if (!isHighPriorityTask(a) && isHighPriorityTask(b)) return 1;\n\n    // Finally sort by closest deadline\n    return dayjs(a.end_date).diff(dayjs(b.end_date));\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `${styles.rightSidebar} ${isOpen ? styles.open : ''}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.rightSidebarHeader,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        className: styles.rightSidebarTitle,\n        children: [/*#__PURE__*/_jsxDEV(Iconify, {\n          icon: \"material-symbols:today\",\n          width: 22,\n          height: 22,\n          color: mainYellowColor\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this), \"Today's Tasks\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"Refresh\",\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: fetchTodayTasks,\n          className: styles.refreshButton,\n          children: /*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"material-symbols:refresh\",\n            width: 18,\n            height: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.rightSidebarContent,\n      children: loading ? /*#__PURE__*/_jsxDEV(Box, {\n        className: styles.loadingContainer,\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 30,\n          sx: {\n            color: mainYellowColor\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 11\n      }, this) : sortedTasks.length > 0 ? sortedTasks.map(task => {\n        const isUrgent = isUrgentTask(task);\n        const isHighPriority = isHighPriorityTask(task);\n        const timeLeft = formatTimeLeft(task);\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `${styles.taskItem} ${isUrgent ? styles.urgent : ''} ${isHighPriority && !isUrgent ? styles.priority : ''}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.taskHeader,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              className: styles.taskTitle,\n              children: task.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 201,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: task.status === 'done' ? 'Mark as incomplete' : 'Mark as complete',\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                className: styles.taskCheckbox,\n                onClick: () => handleTaskStatusChange(task.id || task.slug, task.status === 'done' ? 'todo' : 'done'),\n                size: \"small\",\n                children: /*#__PURE__*/_jsxDEV(Iconify, {\n                  icon: task.status === 'done' ? \"material-symbols:check-circle\" : \"material-symbols:radio-button-unchecked\",\n                  width: 18,\n                  height: 18,\n                  sx: {\n                    color: task.status === 'done' ? mainYellowColor : undefined\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 202,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.taskBadges,\n            children: [isUrgent && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: styles.urgentBadge,\n              children: [/*#__PURE__*/_jsxDEV(Iconify, {\n                icon: \"material-symbols:warning\",\n                width: 12,\n                height: 12\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 23\n              }, this), \"Urgent\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 220,\n              columnNumber: 21\n            }, this), isHighPriority && !isUrgent && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: styles.priorityBadge,\n              children: [/*#__PURE__*/_jsxDEV(Iconify, {\n                icon: \"material-symbols:priority-high\",\n                width: 12,\n                height: 12\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 23\n              }, this), \"High Priority\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 17\n          }, this), task.plan_name && /*#__PURE__*/_jsxDEV(Link, {\n            className: styles.planName,\n            onClick: () => handlePlanClick(task.plan_name),\n            sx: {\n              cursor: 'pointer',\n              color: mainYellowColor\n            },\n            children: task.plan_name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.taskDateCover,\n            children: [/*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"material-symbols:schedule\",\n              width: 16,\n              height: 16,\n              color: isUrgent ? \"#f44336\" : \"#64748b\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              className: `${styles.taskDate} ${isUrgent ? styles.urgentTime : ''}`,\n              children: task.end_date ? `Remaining: ${timeLeft}` : 'No deadline'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 17\n          }, this)]\n        }, task.id || task.slug, true, {\n          fileName: _jsxFileName,\n          lineNumber: 196,\n          columnNumber: 15\n        }, this);\n      }) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.noTasksMessage,\n        children: [/*#__PURE__*/_jsxDEV(Iconify, {\n          icon: \"material-symbols:check-circle-outline\",\n          width: 40,\n          height: 40,\n          color: mainYellowColor\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          children: \"No tasks for today!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.rightSidebarFooter,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        className: styles.footerText,\n        children: [todayTasks.length, \" tasks for today\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 260,\n        columnNumber: 9\n      }, this), isOpen && /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"Hide sidebar\",\n        placement: \"top\",\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: onToggle,\n          className: styles.closeButton,\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"material-symbols:chevron-right\",\n            width: 20,\n            height: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 264,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 171,\n    columnNumber: 5\n  }, this);\n};\n_s(RightSidebar, \"bZhBVSbfIoRF2yiJOYY2dy0ZmO8=\", false, function () {\n  return [useNavigate];\n});\n_c = RightSidebar;\nexport default RightSidebar;\nvar _c;\n$RefreshReg$(_c, \"RightSidebar\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "dayjs", "Box", "Typography", "IconButton", "<PERSON><PERSON><PERSON>", "CircularProgress", "Link", "APIURL", "mainYellowColor", "getHeaders", "Iconify", "styles", "useNavigate", "jsxDEV", "_jsxDEV", "RightSidebar", "_ref", "_s", "isOpen", "onToggle", "allTasks", "setAllTasks", "loading", "setLoading", "navigate", "sampleTasks", "id", "title", "plan_name", "end_date", "add", "format", "status", "priority", "fetchTodayTasks", "intervalId", "setInterval", "clearInterval", "response", "get", "headers", "tasksForToday", "data", "filter", "task", "today", "length", "setShowSampleTasks", "setTodayTasks", "error", "console", "handleTaskStatusChange", "taskId", "newStatus", "showSampleTasks", "updatedTasks", "todayTasks", "map", "put", "handlePlanClick", "planName", "isUrgentTask", "endTime", "now", "hoursLeft", "diff", "isHighPriorityTask", "formatTimeLeft", "isBefore", "minutesLeft", "daysLeft", "Math", "floor", "sortedTasks", "sort", "a", "b", "className", "rightSidebar", "open", "children", "rightSidebarHeader", "variant", "rightSidebarTitle", "icon", "width", "height", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "refreshButton", "rightSidebarContent", "loadingContainer", "size", "sx", "is<PERSON><PERSON>", "isHighPriority", "timeLeft", "taskItem", "urgent", "<PERSON><PERSON><PERSON><PERSON>", "taskTitle", "taskCheckbox", "slug", "undefined", "taskBadges", "urgentBadge", "priorityBadge", "cursor", "taskDateCover", "taskDate", "urgentTime", "noTasksMessage", "rightSide<PERSON>Footer", "footerText", "placement", "closeButton", "_c", "$RefreshReg$"], "sources": ["C:/ignition/ignition-ui/src/components/Sidebar/RightSidebar.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport axios from 'axios';\r\nimport dayjs from 'dayjs';\r\nimport { Box, Typography, IconButton, Tooltip, CircularProgress, Link } from '@mui/material';\r\nimport { APIURL, mainYellowColor } from \"helpers/constants\";\r\nimport { getHeaders } from \"helpers/functions\";\r\nimport Iconify from 'components/Iconify/index';\r\nimport styles from './rightSidebar.module.scss';\r\nimport { useNavigate } from 'react-router-dom';\r\n\r\nconst RightSidebar = ({ isOpen, onToggle }) => {\r\n  const [allTasks, setAllTasks] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const navigate = useNavigate();\r\n\r\n  const sampleTasks = [\r\n    {\r\n      id: 'sample-1',\r\n      title: 'Complete AI Project Report',\r\n      plan_name: 'Generative AI Learning Plan',\r\n      end_date: dayjs().add(2, 'hour').format(),\r\n      status: 'todo',\r\n      priority: 'urgent' // Urgent\r\n    },\r\n    {\r\n      id: 'sample-2',\r\n      title: 'Team Meeting on Project Progress',\r\n      plan_name: 'Generative AI Learning Plan',\r\n      end_date: dayjs().add(4, 'hour').format(),\r\n      status: 'todo',\r\n      priority: 'high' // High priority\r\n    },\r\n    {\r\n      id: 'sample-3',\r\n      title: 'Research on New LLM Models',\r\n      plan_name: 'Generative AI Learning Plan',\r\n      end_date: dayjs().add(1, 'day').format(),\r\n      status: 'done',\r\n      priority: 'normal' // Normal\r\n    },\r\n    {\r\n      id: 'sample-4',\r\n      title: 'Prepare Slides for Presentation',\r\n      plan_name: 'Generative AI Learning Plan',\r\n      end_date: dayjs().add(5, 'hour').format(),\r\n      status: 'todo',\r\n      priority: 'high' // High priority\r\n    }\r\n  ];\r\n\r\n  useEffect(() => {\r\n    fetchTodayTasks();\r\n\r\n    // Automatically update task list every 5 minutes\r\n    const intervalId = setInterval(() => {\r\n      fetchTodayTasks();\r\n    }, 5 * 60 * 1000);\r\n\r\n    return () => clearInterval(intervalId);\r\n  // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, []);\r\n\r\n  const fetchTodayTasks = async () => {\r\n    setLoading(true);\r\n    try {\r\n      const response = await axios.get(`${APIURL}/api/tasks/todo-by-status`, { headers: getHeaders() });\r\n\r\n      const tasksForToday = response.data.filter(task => {\r\n        if (!task.end_date) return false;\r\n        return dayjs(task.end_date).format('YYYY-MM-DD') === today;\r\n      });\r\n\r\n      if (tasksForToday.length === 0) {\r\n        setShowSampleTasks(true);\r\n        setTodayTasks(sampleTasks);\r\n      } else {\r\n        setShowSampleTasks(false);\r\n        setTodayTasks(tasksForToday);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error fetching today tasks:', error);\r\n      setShowSampleTasks(true);\r\n      setTodayTasks(sampleTasks);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleTaskStatusChange = async (taskId, newStatus) => {\r\n    if (showSampleTasks) {\r\n      // If displaying sample data, just update the state\r\n      const updatedTasks = todayTasks.map(task =>\r\n        task.id === taskId ? { ...task, status: newStatus } : task\r\n      );\r\n      setTodayTasks(updatedTasks);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await axios.put(\r\n        `${APIURL}/api/tasks/${taskId}/status`,\r\n        { status: newStatus },\r\n        { headers: getHeaders() }\r\n      );\r\n      fetchTodayTasks();\r\n    } catch (error) {\r\n      console.error('Error updating task status:', error);\r\n    }\r\n  };\r\n\r\n  const handlePlanClick = (planName) => {\r\n    if (!showSampleTasks) {\r\n      // Navigate to plan detail page\r\n      navigate('/d/plans');\r\n    }\r\n  };\r\n\r\n  // Check if a task is urgent (deadline within 3 hours)\r\n  const isUrgentTask = (task) => {\r\n    if (task.priority === 'urgent') return true;\r\n    \r\n    const endTime = dayjs(task.end_date);\r\n    const now = dayjs();\r\n    const hoursLeft = endTime.diff(now, 'hour');\r\n    \r\n    return hoursLeft <= 3 && hoursLeft >= 0;\r\n  };\r\n\r\n  // Check if a task is high priority\r\n  const isHighPriorityTask = (task) => {\r\n    return task.priority === 'high';\r\n  };\r\n\r\n  // Format time remaining\r\n  const formatTimeLeft = (task) => {\r\n    const endTime = dayjs(task.end_date);\r\n    const now = dayjs();\r\n    \r\n    if (endTime.isBefore(now)) {\r\n      return 'Overdue';\r\n    }\r\n    \r\n    const hoursLeft = endTime.diff(now, 'hour');\r\n    const minutesLeft = endTime.diff(now, 'minute') % 60;\r\n    \r\n    if (hoursLeft === 0) {\r\n      return `${minutesLeft} minutes`;\r\n    } else if (hoursLeft < 24) {\r\n      return `${hoursLeft} hours ${minutesLeft} minutes`;\r\n    } else {\r\n      const daysLeft = Math.floor(hoursLeft / 24);\r\n      return `${daysLeft} days`;\r\n    }\r\n  };\r\n\r\n  // Sort tasks by priority and deadline\r\n  const sortedTasks = [...todayTasks].sort((a, b) => {\r\n    // Prioritize urgent tasks\r\n    if (isUrgentTask(a) && !isUrgentTask(b)) return -1;\r\n    if (!isUrgentTask(a) && isUrgentTask(b)) return 1;\r\n    \r\n    // Then prioritize tasks with high priority\r\n    if (isHighPriorityTask(a) && !isHighPriorityTask(b)) return -1;\r\n    if (!isHighPriorityTask(a) && isHighPriorityTask(b)) return 1;\r\n    \r\n    // Finally sort by closest deadline\r\n    return dayjs(a.end_date).diff(dayjs(b.end_date));\r\n  });\r\n\r\n  return (\r\n    <div className={`${styles.rightSidebar} ${isOpen ? styles.open : ''}`}>\r\n      <div className={styles.rightSidebarHeader}>\r\n        <Typography variant=\"h6\" className={styles.rightSidebarTitle}>\r\n          <Iconify icon=\"material-symbols:today\" width={22} height={22} color={mainYellowColor} />\r\n          Today's Tasks\r\n        </Typography>\r\n        <Tooltip title=\"Refresh\">\r\n          <IconButton onClick={fetchTodayTasks} className={styles.refreshButton}>\r\n            <Iconify icon=\"material-symbols:refresh\" width={18} height={18} />\r\n          </IconButton>\r\n        </Tooltip>\r\n      </div>\r\n\r\n      <div className={styles.rightSidebarContent}>\r\n        {loading ? (\r\n          <Box className={styles.loadingContainer}>\r\n            <CircularProgress size={30} sx={{ color: mainYellowColor }} />\r\n          </Box>\r\n        ) : sortedTasks.length > 0 ? (\r\n          sortedTasks.map((task) => {\r\n            const isUrgent = isUrgentTask(task);\r\n            const isHighPriority = isHighPriorityTask(task);\r\n            const timeLeft = formatTimeLeft(task);\r\n            \r\n            return (\r\n              <div \r\n                key={task.id || task.slug} \r\n                className={`${styles.taskItem} ${isUrgent ? styles.urgent : ''} ${isHighPriority && !isUrgent ? styles.priority : ''}`}\r\n              >\r\n                <div className={styles.taskHeader}>\r\n                  <Typography className={styles.taskTitle}>{task.title}</Typography>\r\n                  <Tooltip title={task.status === 'done' ? 'Mark as incomplete' : 'Mark as complete'}>\r\n                    <IconButton\r\n                      className={styles.taskCheckbox}\r\n                      onClick={() => handleTaskStatusChange(task.id || task.slug, task.status === 'done' ? 'todo' : 'done')}\r\n                      size=\"small\"\r\n                    >\r\n                      <Iconify\r\n                        icon={task.status === 'done' ? \"material-symbols:check-circle\" : \"material-symbols:radio-button-unchecked\"}\r\n                        width={18}\r\n                        height={18}\r\n                        sx={{ color: task.status === 'done' ? mainYellowColor : undefined }}\r\n                      />\r\n                    </IconButton>\r\n                  </Tooltip>\r\n                </div>\r\n                \r\n                <div className={styles.taskBadges}>\r\n                  {isUrgent && (\r\n                    <span className={styles.urgentBadge}>\r\n                      <Iconify icon=\"material-symbols:warning\" width={12} height={12} />\r\n                      Urgent\r\n                    </span>\r\n                  )}\r\n                  {isHighPriority && !isUrgent && (\r\n                    <span className={styles.priorityBadge}>\r\n                      <Iconify icon=\"material-symbols:priority-high\" width={12} height={12} />\r\n                      High Priority\r\n                    </span>\r\n                  )}\r\n                </div>\r\n                \r\n                {task.plan_name && (\r\n                  <Link \r\n                    className={styles.planName}\r\n                    onClick={() => handlePlanClick(task.plan_name)}\r\n                    sx={{ cursor: 'pointer', color: mainYellowColor }}\r\n                  >\r\n                    {task.plan_name}\r\n                  </Link>\r\n                )}\r\n                <div className={styles.taskDateCover}>\r\n                  <Iconify icon=\"material-symbols:schedule\" width={16} height={16} color={isUrgent ? \"#f44336\" : \"#64748b\"} />\r\n                  <Typography className={`${styles.taskDate} ${isUrgent ? styles.urgentTime : ''}`}>\r\n                    {task.end_date ? `Remaining: ${timeLeft}` : 'No deadline'}\r\n                  </Typography>\r\n                </div>\r\n              </div>\r\n            );\r\n          })\r\n        ) : (\r\n          <div className={styles.noTasksMessage}>\r\n            <Iconify icon=\"material-symbols:check-circle-outline\" width={40} height={40} color={mainYellowColor} />\r\n            <Typography>No tasks for today!</Typography>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      <div className={styles.rightSidebarFooter}>\r\n        <Typography variant=\"caption\" className={styles.footerText}>\r\n          {todayTasks.length} tasks for today\r\n        </Typography>\r\n        {isOpen && (\r\n          <Tooltip title=\"Hide sidebar\" placement=\"top\">\r\n            <IconButton onClick={onToggle} className={styles.closeButton} size=\"small\">\r\n              <Iconify icon=\"material-symbols:chevron-right\" width={20} height={20} />\r\n            </IconButton>\r\n          </Tooltip>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RightSidebar; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,EAAEC,UAAU,EAAEC,UAAU,EAAEC,OAAO,EAAEC,gBAAgB,EAAEC,IAAI,QAAQ,eAAe;AAC5F,SAASC,MAAM,EAAEC,eAAe,QAAQ,mBAAmB;AAC3D,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,YAAY,GAAGC,IAAA,IAA0B;EAAAC,EAAA;EAAA,IAAzB;IAAEC,MAAM;IAAEC;EAAS,CAAC,GAAAH,IAAA;EACxC,MAAM,CAACI,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM2B,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAE9B,MAAMa,WAAW,GAAG,CAClB;IACEC,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,4BAA4B;IACnCC,SAAS,EAAE,6BAA6B;IACxCC,QAAQ,EAAE7B,KAAK,CAAC,CAAC,CAAC8B,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAACC,MAAM,CAAC,CAAC;IACzCC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE,QAAQ,CAAC;EACrB,CAAC,EACD;IACEP,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,kCAAkC;IACzCC,SAAS,EAAE,6BAA6B;IACxCC,QAAQ,EAAE7B,KAAK,CAAC,CAAC,CAAC8B,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAACC,MAAM,CAAC,CAAC;IACzCC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE,MAAM,CAAC;EACnB,CAAC,EACD;IACEP,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,4BAA4B;IACnCC,SAAS,EAAE,6BAA6B;IACxCC,QAAQ,EAAE7B,KAAK,CAAC,CAAC,CAAC8B,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAACC,MAAM,CAAC,CAAC;IACxCC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE,QAAQ,CAAC;EACrB,CAAC,EACD;IACEP,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,iCAAiC;IACxCC,SAAS,EAAE,6BAA6B;IACxCC,QAAQ,EAAE7B,KAAK,CAAC,CAAC,CAAC8B,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAACC,MAAM,CAAC,CAAC;IACzCC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE,MAAM,CAAC;EACnB,CAAC,CACF;EAEDnC,SAAS,CAAC,MAAM;IACdoC,eAAe,CAAC,CAAC;;IAEjB;IACA,MAAMC,UAAU,GAAGC,WAAW,CAAC,MAAM;MACnCF,eAAe,CAAC,CAAC;IACnB,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;IAEjB,OAAO,MAAMG,aAAa,CAACF,UAAU,CAAC;IACxC;EACA,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClCX,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMe,QAAQ,GAAG,MAAMvC,KAAK,CAACwC,GAAG,CAAC,GAAGhC,MAAM,2BAA2B,EAAE;QAAEiC,OAAO,EAAE/B,UAAU,CAAC;MAAE,CAAC,CAAC;MAEjG,MAAMgC,aAAa,GAAGH,QAAQ,CAACI,IAAI,CAACC,MAAM,CAACC,IAAI,IAAI;QACjD,IAAI,CAACA,IAAI,CAACf,QAAQ,EAAE,OAAO,KAAK;QAChC,OAAO7B,KAAK,CAAC4C,IAAI,CAACf,QAAQ,CAAC,CAACE,MAAM,CAAC,YAAY,CAAC,KAAKc,KAAK;MAC5D,CAAC,CAAC;MAEF,IAAIJ,aAAa,CAACK,MAAM,KAAK,CAAC,EAAE;QAC9BC,kBAAkB,CAAC,IAAI,CAAC;QACxBC,aAAa,CAACvB,WAAW,CAAC;MAC5B,CAAC,MAAM;QACLsB,kBAAkB,CAAC,KAAK,CAAC;QACzBC,aAAa,CAACP,aAAa,CAAC;MAC9B;IACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnDF,kBAAkB,CAAC,IAAI,CAAC;MACxBC,aAAa,CAACvB,WAAW,CAAC;IAC5B,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM4B,sBAAsB,GAAG,MAAAA,CAAOC,MAAM,EAAEC,SAAS,KAAK;IAC1D,IAAIC,eAAe,EAAE;MACnB;MACA,MAAMC,YAAY,GAAGC,UAAU,CAACC,GAAG,CAACb,IAAI,IACtCA,IAAI,CAAClB,EAAE,KAAK0B,MAAM,GAAG;QAAE,GAAGR,IAAI;QAAEZ,MAAM,EAAEqB;MAAU,CAAC,GAAGT,IACxD,CAAC;MACDI,aAAa,CAACO,YAAY,CAAC;MAC3B;IACF;IAEA,IAAI;MACF,MAAMxD,KAAK,CAAC2D,GAAG,CACb,GAAGnD,MAAM,cAAc6C,MAAM,SAAS,EACtC;QAAEpB,MAAM,EAAEqB;MAAU,CAAC,EACrB;QAAEb,OAAO,EAAE/B,UAAU,CAAC;MAAE,CAC1B,CAAC;MACDyB,eAAe,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD;EACF,CAAC;EAED,MAAMU,eAAe,GAAIC,QAAQ,IAAK;IACpC,IAAI,CAACN,eAAe,EAAE;MACpB;MACA9B,QAAQ,CAAC,UAAU,CAAC;IACtB;EACF,CAAC;;EAED;EACA,MAAMqC,YAAY,GAAIjB,IAAI,IAAK;IAC7B,IAAIA,IAAI,CAACX,QAAQ,KAAK,QAAQ,EAAE,OAAO,IAAI;IAE3C,MAAM6B,OAAO,GAAG9D,KAAK,CAAC4C,IAAI,CAACf,QAAQ,CAAC;IACpC,MAAMkC,GAAG,GAAG/D,KAAK,CAAC,CAAC;IACnB,MAAMgE,SAAS,GAAGF,OAAO,CAACG,IAAI,CAACF,GAAG,EAAE,MAAM,CAAC;IAE3C,OAAOC,SAAS,IAAI,CAAC,IAAIA,SAAS,IAAI,CAAC;EACzC,CAAC;;EAED;EACA,MAAME,kBAAkB,GAAItB,IAAI,IAAK;IACnC,OAAOA,IAAI,CAACX,QAAQ,KAAK,MAAM;EACjC,CAAC;;EAED;EACA,MAAMkC,cAAc,GAAIvB,IAAI,IAAK;IAC/B,MAAMkB,OAAO,GAAG9D,KAAK,CAAC4C,IAAI,CAACf,QAAQ,CAAC;IACpC,MAAMkC,GAAG,GAAG/D,KAAK,CAAC,CAAC;IAEnB,IAAI8D,OAAO,CAACM,QAAQ,CAACL,GAAG,CAAC,EAAE;MACzB,OAAO,SAAS;IAClB;IAEA,MAAMC,SAAS,GAAGF,OAAO,CAACG,IAAI,CAACF,GAAG,EAAE,MAAM,CAAC;IAC3C,MAAMM,WAAW,GAAGP,OAAO,CAACG,IAAI,CAACF,GAAG,EAAE,QAAQ,CAAC,GAAG,EAAE;IAEpD,IAAIC,SAAS,KAAK,CAAC,EAAE;MACnB,OAAO,GAAGK,WAAW,UAAU;IACjC,CAAC,MAAM,IAAIL,SAAS,GAAG,EAAE,EAAE;MACzB,OAAO,GAAGA,SAAS,UAAUK,WAAW,UAAU;IACpD,CAAC,MAAM;MACL,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACR,SAAS,GAAG,EAAE,CAAC;MAC3C,OAAO,GAAGM,QAAQ,OAAO;IAC3B;EACF,CAAC;;EAED;EACA,MAAMG,WAAW,GAAG,CAAC,GAAGjB,UAAU,CAAC,CAACkB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IACjD;IACA,IAAIf,YAAY,CAACc,CAAC,CAAC,IAAI,CAACd,YAAY,CAACe,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IAClD,IAAI,CAACf,YAAY,CAACc,CAAC,CAAC,IAAId,YAAY,CAACe,CAAC,CAAC,EAAE,OAAO,CAAC;;IAEjD;IACA,IAAIV,kBAAkB,CAACS,CAAC,CAAC,IAAI,CAACT,kBAAkB,CAACU,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IAC9D,IAAI,CAACV,kBAAkB,CAACS,CAAC,CAAC,IAAIT,kBAAkB,CAACU,CAAC,CAAC,EAAE,OAAO,CAAC;;IAE7D;IACA,OAAO5E,KAAK,CAAC2E,CAAC,CAAC9C,QAAQ,CAAC,CAACoC,IAAI,CAACjE,KAAK,CAAC4E,CAAC,CAAC/C,QAAQ,CAAC,CAAC;EAClD,CAAC,CAAC;EAEF,oBACEf,OAAA;IAAK+D,SAAS,EAAE,GAAGlE,MAAM,CAACmE,YAAY,IAAI5D,MAAM,GAAGP,MAAM,CAACoE,IAAI,GAAG,EAAE,EAAG;IAAAC,QAAA,gBACpElE,OAAA;MAAK+D,SAAS,EAAElE,MAAM,CAACsE,kBAAmB;MAAAD,QAAA,gBACxClE,OAAA,CAACZ,UAAU;QAACgF,OAAO,EAAC,IAAI;QAACL,SAAS,EAAElE,MAAM,CAACwE,iBAAkB;QAAAH,QAAA,gBAC3DlE,OAAA,CAACJ,OAAO;UAAC0E,IAAI,EAAC,wBAAwB;UAACC,KAAK,EAAE,EAAG;UAACC,MAAM,EAAE,EAAG;UAACC,KAAK,EAAE/E;QAAgB;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,iBAE1F;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb7E,OAAA,CAACV,OAAO;QAACuB,KAAK,EAAC,SAAS;QAAAqD,QAAA,eACtBlE,OAAA,CAACX,UAAU;UAACyF,OAAO,EAAE1D,eAAgB;UAAC2C,SAAS,EAAElE,MAAM,CAACkF,aAAc;UAAAb,QAAA,eACpElE,OAAA,CAACJ,OAAO;YAAC0E,IAAI,EAAC,0BAA0B;YAACC,KAAK,EAAE,EAAG;YAACC,MAAM,EAAE;UAAG;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAEN7E,OAAA;MAAK+D,SAAS,EAAElE,MAAM,CAACmF,mBAAoB;MAAAd,QAAA,EACxC1D,OAAO,gBACNR,OAAA,CAACb,GAAG;QAAC4E,SAAS,EAAElE,MAAM,CAACoF,gBAAiB;QAAAf,QAAA,eACtClE,OAAA,CAACT,gBAAgB;UAAC2F,IAAI,EAAE,EAAG;UAACC,EAAE,EAAE;YAAEV,KAAK,EAAE/E;UAAgB;QAAE;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,GACJlB,WAAW,CAAC3B,MAAM,GAAG,CAAC,GACxB2B,WAAW,CAAChB,GAAG,CAAEb,IAAI,IAAK;QACxB,MAAMsD,QAAQ,GAAGrC,YAAY,CAACjB,IAAI,CAAC;QACnC,MAAMuD,cAAc,GAAGjC,kBAAkB,CAACtB,IAAI,CAAC;QAC/C,MAAMwD,QAAQ,GAAGjC,cAAc,CAACvB,IAAI,CAAC;QAErC,oBACE9B,OAAA;UAEE+D,SAAS,EAAE,GAAGlE,MAAM,CAAC0F,QAAQ,IAAIH,QAAQ,GAAGvF,MAAM,CAAC2F,MAAM,GAAG,EAAE,IAAIH,cAAc,IAAI,CAACD,QAAQ,GAAGvF,MAAM,CAACsB,QAAQ,GAAG,EAAE,EAAG;UAAA+C,QAAA,gBAEvHlE,OAAA;YAAK+D,SAAS,EAAElE,MAAM,CAAC4F,UAAW;YAAAvB,QAAA,gBAChClE,OAAA,CAACZ,UAAU;cAAC2E,SAAS,EAAElE,MAAM,CAAC6F,SAAU;cAAAxB,QAAA,EAAEpC,IAAI,CAACjB;YAAK;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAClE7E,OAAA,CAACV,OAAO;cAACuB,KAAK,EAAEiB,IAAI,CAACZ,MAAM,KAAK,MAAM,GAAG,oBAAoB,GAAG,kBAAmB;cAAAgD,QAAA,eACjFlE,OAAA,CAACX,UAAU;gBACT0E,SAAS,EAAElE,MAAM,CAAC8F,YAAa;gBAC/Bb,OAAO,EAAEA,CAAA,KAAMzC,sBAAsB,CAACP,IAAI,CAAClB,EAAE,IAAIkB,IAAI,CAAC8D,IAAI,EAAE9D,IAAI,CAACZ,MAAM,KAAK,MAAM,GAAG,MAAM,GAAG,MAAM,CAAE;gBACtGgE,IAAI,EAAC,OAAO;gBAAAhB,QAAA,eAEZlE,OAAA,CAACJ,OAAO;kBACN0E,IAAI,EAAExC,IAAI,CAACZ,MAAM,KAAK,MAAM,GAAG,+BAA+B,GAAG,yCAA0C;kBAC3GqD,KAAK,EAAE,EAAG;kBACVC,MAAM,EAAE,EAAG;kBACXW,EAAE,EAAE;oBAAEV,KAAK,EAAE3C,IAAI,CAACZ,MAAM,KAAK,MAAM,GAAGxB,eAAe,GAAGmG;kBAAU;gBAAE;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eAEN7E,OAAA;YAAK+D,SAAS,EAAElE,MAAM,CAACiG,UAAW;YAAA5B,QAAA,GAC/BkB,QAAQ,iBACPpF,OAAA;cAAM+D,SAAS,EAAElE,MAAM,CAACkG,WAAY;cAAA7B,QAAA,gBAClClE,OAAA,CAACJ,OAAO;gBAAC0E,IAAI,EAAC,0BAA0B;gBAACC,KAAK,EAAE,EAAG;gBAACC,MAAM,EAAE;cAAG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,UAEpE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP,EACAQ,cAAc,IAAI,CAACD,QAAQ,iBAC1BpF,OAAA;cAAM+D,SAAS,EAAElE,MAAM,CAACmG,aAAc;cAAA9B,QAAA,gBACpClE,OAAA,CAACJ,OAAO;gBAAC0E,IAAI,EAAC,gCAAgC;gBAACC,KAAK,EAAE,EAAG;gBAACC,MAAM,EAAE;cAAG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,iBAE1E;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAEL/C,IAAI,CAAChB,SAAS,iBACbd,OAAA,CAACR,IAAI;YACHuE,SAAS,EAAElE,MAAM,CAACiD,QAAS;YAC3BgC,OAAO,EAAEA,CAAA,KAAMjC,eAAe,CAACf,IAAI,CAAChB,SAAS,CAAE;YAC/CqE,EAAE,EAAE;cAAEc,MAAM,EAAE,SAAS;cAAExB,KAAK,EAAE/E;YAAgB,CAAE;YAAAwE,QAAA,EAEjDpC,IAAI,CAAChB;UAAS;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CACP,eACD7E,OAAA;YAAK+D,SAAS,EAAElE,MAAM,CAACqG,aAAc;YAAAhC,QAAA,gBACnClE,OAAA,CAACJ,OAAO;cAAC0E,IAAI,EAAC,2BAA2B;cAACC,KAAK,EAAE,EAAG;cAACC,MAAM,EAAE,EAAG;cAACC,KAAK,EAAEW,QAAQ,GAAG,SAAS,GAAG;YAAU;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5G7E,OAAA,CAACZ,UAAU;cAAC2E,SAAS,EAAE,GAAGlE,MAAM,CAACsG,QAAQ,IAAIf,QAAQ,GAAGvF,MAAM,CAACuG,UAAU,GAAG,EAAE,EAAG;cAAAlC,QAAA,EAC9EpC,IAAI,CAACf,QAAQ,GAAG,cAAcuE,QAAQ,EAAE,GAAG;YAAa;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA,GAlDD/C,IAAI,CAAClB,EAAE,IAAIkB,IAAI,CAAC8D,IAAI;UAAAlB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmDtB,CAAC;MAEV,CAAC,CAAC,gBAEF7E,OAAA;QAAK+D,SAAS,EAAElE,MAAM,CAACwG,cAAe;QAAAnC,QAAA,gBACpClE,OAAA,CAACJ,OAAO;UAAC0E,IAAI,EAAC,uCAAuC;UAACC,KAAK,EAAE,EAAG;UAACC,MAAM,EAAE,EAAG;UAACC,KAAK,EAAE/E;QAAgB;UAAAgF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvG7E,OAAA,CAACZ,UAAU;UAAA8E,QAAA,EAAC;QAAmB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEN7E,OAAA;MAAK+D,SAAS,EAAElE,MAAM,CAACyG,kBAAmB;MAAApC,QAAA,gBACxClE,OAAA,CAACZ,UAAU;QAACgF,OAAO,EAAC,SAAS;QAACL,SAAS,EAAElE,MAAM,CAAC0G,UAAW;QAAArC,QAAA,GACxDxB,UAAU,CAACV,MAAM,EAAC,kBACrB;MAAA;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EACZzE,MAAM,iBACLJ,OAAA,CAACV,OAAO;QAACuB,KAAK,EAAC,cAAc;QAAC2F,SAAS,EAAC,KAAK;QAAAtC,QAAA,eAC3ClE,OAAA,CAACX,UAAU;UAACyF,OAAO,EAAEzE,QAAS;UAAC0D,SAAS,EAAElE,MAAM,CAAC4G,WAAY;UAACvB,IAAI,EAAC,OAAO;UAAAhB,QAAA,eACxElE,OAAA,CAACJ,OAAO;YAAC0E,IAAI,EAAC,gCAAgC;YAACC,KAAK,EAAE,EAAG;YAACC,MAAM,EAAE;UAAG;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACV;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1E,EAAA,CAtQIF,YAAY;EAAA,QAGCH,WAAW;AAAA;AAAA4G,EAAA,GAHxBzG,YAAY;AAwQlB,eAAeA,YAAY;AAAC,IAAAyG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}