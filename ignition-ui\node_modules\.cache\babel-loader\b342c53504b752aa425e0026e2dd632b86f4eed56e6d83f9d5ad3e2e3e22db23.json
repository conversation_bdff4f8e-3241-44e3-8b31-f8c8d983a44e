{"ast": null, "code": "\"use strict\";\n\nexports.__esModule = true;\nexports.getChildMapping = getChildMapping;\nexports.mergeChildMappings = mergeChildMappings;\nexports.getInitialChildMapping = getInitialChildMapping;\nexports.getNextChildMapping = getNextChildMapping;\nvar _react = require(\"react\");\n\n/**\n * Given `this.props.children`, return an object mapping key to child.\n *\n * @param {*} children `this.props.children`\n * @return {object} Mapping of key to child\n */\nfunction getChildMapping(children, mapFn) {\n  var mapper = function mapper(child) {\n    return mapFn && (0, _react.isValidElement)(child) ? mapFn(child) : child;\n  };\n  var result = Object.create(null);\n  if (children) _react.Children.map(children, function (c) {\n    return c;\n  }).forEach(function (child) {\n    // run the map function here instead so that the key is the computed one\n    result[child.key] = mapper(child);\n  });\n  return result;\n}\n/**\n * When you're adding or removing children some may be added or removed in the\n * same render pass. We want to show *both* since we want to simultaneously\n * animate elements in and out. This function takes a previous set of keys\n * and a new set of keys and merges them with its best guess of the correct\n * ordering. In the future we may expose some of the utilities in\n * ReactMultiChild to make this easy, but for now React itself does not\n * directly have this concept of the union of prevChildren and nextChildren\n * so we implement it here.\n *\n * @param {object} prev prev children as returned from\n * `ReactTransitionChildMapping.getChildMapping()`.\n * @param {object} next next children as returned from\n * `ReactTransitionChildMapping.getChildMapping()`.\n * @return {object} a key set that contains all keys in `prev` and all keys\n * in `next` in a reasonable order.\n */\n\nfunction mergeChildMappings(prev, next) {\n  prev = prev || {};\n  next = next || {};\n  function getValueForKey(key) {\n    return key in next ? next[key] : prev[key];\n  } // For each key of `next`, the list of keys to insert before that key in\n  // the combined list\n\n  var nextKeysPending = Object.create(null);\n  var pendingKeys = [];\n  for (var prevKey in prev) {\n    if (prevKey in next) {\n      if (pendingKeys.length) {\n        nextKeysPending[prevKey] = pendingKeys;\n        pendingKeys = [];\n      }\n    } else {\n      pendingKeys.push(prevKey);\n    }\n  }\n  var i;\n  var childMapping = {};\n  for (var nextKey in next) {\n    if (nextKeysPending[nextKey]) {\n      for (i = 0; i < nextKeysPending[nextKey].length; i++) {\n        var pendingNextKey = nextKeysPending[nextKey][i];\n        childMapping[nextKeysPending[nextKey][i]] = getValueForKey(pendingNextKey);\n      }\n    }\n    childMapping[nextKey] = getValueForKey(nextKey);\n  } // Finally, add the keys which didn't appear before any key in `next`\n\n  for (i = 0; i < pendingKeys.length; i++) {\n    childMapping[pendingKeys[i]] = getValueForKey(pendingKeys[i]);\n  }\n  return childMapping;\n}\nfunction getProp(child, prop, props) {\n  return props[prop] != null ? props[prop] : child.props[prop];\n}\nfunction getInitialChildMapping(props, onExited) {\n  return getChildMapping(props.children, function (child) {\n    return (0, _react.cloneElement)(child, {\n      onExited: onExited.bind(null, child),\n      in: true,\n      appear: getProp(child, 'appear', props),\n      enter: getProp(child, 'enter', props),\n      exit: getProp(child, 'exit', props)\n    });\n  });\n}\nfunction getNextChildMapping(nextProps, prevChildMapping, onExited) {\n  var nextChildMapping = getChildMapping(nextProps.children);\n  var children = mergeChildMappings(prevChildMapping, nextChildMapping);\n  Object.keys(children).forEach(function (key) {\n    var child = children[key];\n    if (!(0, _react.isValidElement)(child)) return;\n    var hasPrev = key in prevChildMapping;\n    var hasNext = key in nextChildMapping;\n    var prevChild = prevChildMapping[key];\n    var isLeaving = (0, _react.isValidElement)(prevChild) && !prevChild.props.in; // item is new (entering)\n\n    if (hasNext && (!hasPrev || isLeaving)) {\n      // console.log('entering', key)\n      children[key] = (0, _react.cloneElement)(child, {\n        onExited: onExited.bind(null, child),\n        in: true,\n        exit: getProp(child, 'exit', nextProps),\n        enter: getProp(child, 'enter', nextProps)\n      });\n    } else if (!hasNext && hasPrev && !isLeaving) {\n      // item is old (exiting)\n      // console.log('leaving', key)\n      children[key] = (0, _react.cloneElement)(child, {\n        in: false\n      });\n    } else if (hasNext && hasPrev && (0, _react.isValidElement)(prevChild)) {\n      // item hasn't changed transition states\n      // copy over the last transition props;\n      // console.log('unchanged', key)\n      children[key] = (0, _react.cloneElement)(child, {\n        onExited: onExited.bind(null, child),\n        in: prevChild.props.in,\n        exit: getProp(child, 'exit', nextProps),\n        enter: getProp(child, 'enter', nextProps)\n      });\n    }\n  });\n  return children;\n}", "map": {"version": 3, "names": ["exports", "__esModule", "get<PERSON>hildMapping", "mergeChildMappings", "getInitialChildMapping", "getNextChildMapping", "_react", "require", "children", "mapFn", "mapper", "child", "isValidElement", "result", "Object", "create", "Children", "map", "c", "for<PERSON>ach", "key", "prev", "next", "getValueForKey", "nextKeysPending", "<PERSON><PERSON><PERSON><PERSON>", "prev<PERSON><PERSON>", "length", "push", "i", "childMapping", "<PERSON><PERSON><PERSON>", "pendingNextKey", "getProp", "prop", "props", "onExited", "cloneElement", "bind", "in", "appear", "enter", "exit", "nextProps", "prevChildMapping", "next<PERSON><PERSON>dMapping", "keys", "has<PERSON>rev", "hasNext", "prev<PERSON><PERSON><PERSON>", "isLeaving"], "sources": ["C:/ignition/ignition-ui/node_modules/reactstrap/node_modules/react-transition-group/utils/ChildMapping.js"], "sourcesContent": ["\"use strict\";\n\nexports.__esModule = true;\nexports.getChildMapping = getChildMapping;\nexports.mergeChildMappings = mergeChildMappings;\nexports.getInitialChildMapping = getInitialChildMapping;\nexports.getNextChildMapping = getNextChildMapping;\n\nvar _react = require(\"react\");\n\n/**\n * Given `this.props.children`, return an object mapping key to child.\n *\n * @param {*} children `this.props.children`\n * @return {object} Mapping of key to child\n */\nfunction getChildMapping(children, mapFn) {\n  var mapper = function mapper(child) {\n    return mapFn && (0, _react.isValidElement)(child) ? mapFn(child) : child;\n  };\n\n  var result = Object.create(null);\n  if (children) _react.Children.map(children, function (c) {\n    return c;\n  }).forEach(function (child) {\n    // run the map function here instead so that the key is the computed one\n    result[child.key] = mapper(child);\n  });\n  return result;\n}\n/**\n * When you're adding or removing children some may be added or removed in the\n * same render pass. We want to show *both* since we want to simultaneously\n * animate elements in and out. This function takes a previous set of keys\n * and a new set of keys and merges them with its best guess of the correct\n * ordering. In the future we may expose some of the utilities in\n * ReactMultiChild to make this easy, but for now React itself does not\n * directly have this concept of the union of prevChildren and nextChildren\n * so we implement it here.\n *\n * @param {object} prev prev children as returned from\n * `ReactTransitionChildMapping.getChildMapping()`.\n * @param {object} next next children as returned from\n * `ReactTransitionChildMapping.getChildMapping()`.\n * @return {object} a key set that contains all keys in `prev` and all keys\n * in `next` in a reasonable order.\n */\n\n\nfunction mergeChildMappings(prev, next) {\n  prev = prev || {};\n  next = next || {};\n\n  function getValueForKey(key) {\n    return key in next ? next[key] : prev[key];\n  } // For each key of `next`, the list of keys to insert before that key in\n  // the combined list\n\n\n  var nextKeysPending = Object.create(null);\n  var pendingKeys = [];\n\n  for (var prevKey in prev) {\n    if (prevKey in next) {\n      if (pendingKeys.length) {\n        nextKeysPending[prevKey] = pendingKeys;\n        pendingKeys = [];\n      }\n    } else {\n      pendingKeys.push(prevKey);\n    }\n  }\n\n  var i;\n  var childMapping = {};\n\n  for (var nextKey in next) {\n    if (nextKeysPending[nextKey]) {\n      for (i = 0; i < nextKeysPending[nextKey].length; i++) {\n        var pendingNextKey = nextKeysPending[nextKey][i];\n        childMapping[nextKeysPending[nextKey][i]] = getValueForKey(pendingNextKey);\n      }\n    }\n\n    childMapping[nextKey] = getValueForKey(nextKey);\n  } // Finally, add the keys which didn't appear before any key in `next`\n\n\n  for (i = 0; i < pendingKeys.length; i++) {\n    childMapping[pendingKeys[i]] = getValueForKey(pendingKeys[i]);\n  }\n\n  return childMapping;\n}\n\nfunction getProp(child, prop, props) {\n  return props[prop] != null ? props[prop] : child.props[prop];\n}\n\nfunction getInitialChildMapping(props, onExited) {\n  return getChildMapping(props.children, function (child) {\n    return (0, _react.cloneElement)(child, {\n      onExited: onExited.bind(null, child),\n      in: true,\n      appear: getProp(child, 'appear', props),\n      enter: getProp(child, 'enter', props),\n      exit: getProp(child, 'exit', props)\n    });\n  });\n}\n\nfunction getNextChildMapping(nextProps, prevChildMapping, onExited) {\n  var nextChildMapping = getChildMapping(nextProps.children);\n  var children = mergeChildMappings(prevChildMapping, nextChildMapping);\n  Object.keys(children).forEach(function (key) {\n    var child = children[key];\n    if (!(0, _react.isValidElement)(child)) return;\n    var hasPrev = key in prevChildMapping;\n    var hasNext = key in nextChildMapping;\n    var prevChild = prevChildMapping[key];\n    var isLeaving = (0, _react.isValidElement)(prevChild) && !prevChild.props.in; // item is new (entering)\n\n    if (hasNext && (!hasPrev || isLeaving)) {\n      // console.log('entering', key)\n      children[key] = (0, _react.cloneElement)(child, {\n        onExited: onExited.bind(null, child),\n        in: true,\n        exit: getProp(child, 'exit', nextProps),\n        enter: getProp(child, 'enter', nextProps)\n      });\n    } else if (!hasNext && hasPrev && !isLeaving) {\n      // item is old (exiting)\n      // console.log('leaving', key)\n      children[key] = (0, _react.cloneElement)(child, {\n        in: false\n      });\n    } else if (hasNext && hasPrev && (0, _react.isValidElement)(prevChild)) {\n      // item hasn't changed transition states\n      // copy over the last transition props;\n      // console.log('unchanged', key)\n      children[key] = (0, _react.cloneElement)(child, {\n        onExited: onExited.bind(null, child),\n        in: prevChild.props.in,\n        exit: getProp(child, 'exit', nextProps),\n        enter: getProp(child, 'enter', nextProps)\n      });\n    }\n  });\n  return children;\n}"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AACzBD,OAAO,CAACE,eAAe,GAAGA,eAAe;AACzCF,OAAO,CAACG,kBAAkB,GAAGA,kBAAkB;AAC/CH,OAAO,CAACI,sBAAsB,GAAGA,sBAAsB;AACvDJ,OAAO,CAACK,mBAAmB,GAAGA,mBAAmB;AAEjD,IAAIC,MAAM,GAAGC,OAAO,CAAC,OAAO,CAAC;;AAE7B;AACA;AACA;AACA;AACA;AACA;AACA,SAASL,eAAeA,CAACM,QAAQ,EAAEC,KAAK,EAAE;EACxC,IAAIC,MAAM,GAAG,SAASA,MAAMA,CAACC,KAAK,EAAE;IAClC,OAAOF,KAAK,IAAI,CAAC,CAAC,EAAEH,MAAM,CAACM,cAAc,EAAED,KAAK,CAAC,GAAGF,KAAK,CAACE,KAAK,CAAC,GAAGA,KAAK;EAC1E,CAAC;EAED,IAAIE,MAAM,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAChC,IAAIP,QAAQ,EAAEF,MAAM,CAACU,QAAQ,CAACC,GAAG,CAACT,QAAQ,EAAE,UAAUU,CAAC,EAAE;IACvD,OAAOA,CAAC;EACV,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUR,KAAK,EAAE;IAC1B;IACAE,MAAM,CAACF,KAAK,CAACS,GAAG,CAAC,GAAGV,MAAM,CAACC,KAAK,CAAC;EACnC,CAAC,CAAC;EACF,OAAOE,MAAM;AACf;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAGA,SAASV,kBAAkBA,CAACkB,IAAI,EAAEC,IAAI,EAAE;EACtCD,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;EACjBC,IAAI,GAAGA,IAAI,IAAI,CAAC,CAAC;EAEjB,SAASC,cAAcA,CAACH,GAAG,EAAE;IAC3B,OAAOA,GAAG,IAAIE,IAAI,GAAGA,IAAI,CAACF,GAAG,CAAC,GAAGC,IAAI,CAACD,GAAG,CAAC;EAC5C,CAAC,CAAC;EACF;;EAGA,IAAII,eAAe,GAAGV,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EACzC,IAAIU,WAAW,GAAG,EAAE;EAEpB,KAAK,IAAIC,OAAO,IAAIL,IAAI,EAAE;IACxB,IAAIK,OAAO,IAAIJ,IAAI,EAAE;MACnB,IAAIG,WAAW,CAACE,MAAM,EAAE;QACtBH,eAAe,CAACE,OAAO,CAAC,GAAGD,WAAW;QACtCA,WAAW,GAAG,EAAE;MAClB;IACF,CAAC,MAAM;MACLA,WAAW,CAACG,IAAI,CAACF,OAAO,CAAC;IAC3B;EACF;EAEA,IAAIG,CAAC;EACL,IAAIC,YAAY,GAAG,CAAC,CAAC;EAErB,KAAK,IAAIC,OAAO,IAAIT,IAAI,EAAE;IACxB,IAAIE,eAAe,CAACO,OAAO,CAAC,EAAE;MAC5B,KAAKF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,eAAe,CAACO,OAAO,CAAC,CAACJ,MAAM,EAAEE,CAAC,EAAE,EAAE;QACpD,IAAIG,cAAc,GAAGR,eAAe,CAACO,OAAO,CAAC,CAACF,CAAC,CAAC;QAChDC,YAAY,CAACN,eAAe,CAACO,OAAO,CAAC,CAACF,CAAC,CAAC,CAAC,GAAGN,cAAc,CAACS,cAAc,CAAC;MAC5E;IACF;IAEAF,YAAY,CAACC,OAAO,CAAC,GAAGR,cAAc,CAACQ,OAAO,CAAC;EACjD,CAAC,CAAC;;EAGF,KAAKF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,WAAW,CAACE,MAAM,EAAEE,CAAC,EAAE,EAAE;IACvCC,YAAY,CAACL,WAAW,CAACI,CAAC,CAAC,CAAC,GAAGN,cAAc,CAACE,WAAW,CAACI,CAAC,CAAC,CAAC;EAC/D;EAEA,OAAOC,YAAY;AACrB;AAEA,SAASG,OAAOA,CAACtB,KAAK,EAAEuB,IAAI,EAAEC,KAAK,EAAE;EACnC,OAAOA,KAAK,CAACD,IAAI,CAAC,IAAI,IAAI,GAAGC,KAAK,CAACD,IAAI,CAAC,GAAGvB,KAAK,CAACwB,KAAK,CAACD,IAAI,CAAC;AAC9D;AAEA,SAAS9B,sBAAsBA,CAAC+B,KAAK,EAAEC,QAAQ,EAAE;EAC/C,OAAOlC,eAAe,CAACiC,KAAK,CAAC3B,QAAQ,EAAE,UAAUG,KAAK,EAAE;IACtD,OAAO,CAAC,CAAC,EAAEL,MAAM,CAAC+B,YAAY,EAAE1B,KAAK,EAAE;MACrCyB,QAAQ,EAAEA,QAAQ,CAACE,IAAI,CAAC,IAAI,EAAE3B,KAAK,CAAC;MACpC4B,EAAE,EAAE,IAAI;MACRC,MAAM,EAAEP,OAAO,CAACtB,KAAK,EAAE,QAAQ,EAAEwB,KAAK,CAAC;MACvCM,KAAK,EAAER,OAAO,CAACtB,KAAK,EAAE,OAAO,EAAEwB,KAAK,CAAC;MACrCO,IAAI,EAAET,OAAO,CAACtB,KAAK,EAAE,MAAM,EAAEwB,KAAK;IACpC,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAEA,SAAS9B,mBAAmBA,CAACsC,SAAS,EAAEC,gBAAgB,EAAER,QAAQ,EAAE;EAClE,IAAIS,gBAAgB,GAAG3C,eAAe,CAACyC,SAAS,CAACnC,QAAQ,CAAC;EAC1D,IAAIA,QAAQ,GAAGL,kBAAkB,CAACyC,gBAAgB,EAAEC,gBAAgB,CAAC;EACrE/B,MAAM,CAACgC,IAAI,CAACtC,QAAQ,CAAC,CAACW,OAAO,CAAC,UAAUC,GAAG,EAAE;IAC3C,IAAIT,KAAK,GAAGH,QAAQ,CAACY,GAAG,CAAC;IACzB,IAAI,CAAC,CAAC,CAAC,EAAEd,MAAM,CAACM,cAAc,EAAED,KAAK,CAAC,EAAE;IACxC,IAAIoC,OAAO,GAAG3B,GAAG,IAAIwB,gBAAgB;IACrC,IAAII,OAAO,GAAG5B,GAAG,IAAIyB,gBAAgB;IACrC,IAAII,SAAS,GAAGL,gBAAgB,CAACxB,GAAG,CAAC;IACrC,IAAI8B,SAAS,GAAG,CAAC,CAAC,EAAE5C,MAAM,CAACM,cAAc,EAAEqC,SAAS,CAAC,IAAI,CAACA,SAAS,CAACd,KAAK,CAACI,EAAE,CAAC,CAAC;;IAE9E,IAAIS,OAAO,KAAK,CAACD,OAAO,IAAIG,SAAS,CAAC,EAAE;MACtC;MACA1C,QAAQ,CAACY,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEd,MAAM,CAAC+B,YAAY,EAAE1B,KAAK,EAAE;QAC9CyB,QAAQ,EAAEA,QAAQ,CAACE,IAAI,CAAC,IAAI,EAAE3B,KAAK,CAAC;QACpC4B,EAAE,EAAE,IAAI;QACRG,IAAI,EAAET,OAAO,CAACtB,KAAK,EAAE,MAAM,EAAEgC,SAAS,CAAC;QACvCF,KAAK,EAAER,OAAO,CAACtB,KAAK,EAAE,OAAO,EAAEgC,SAAS;MAC1C,CAAC,CAAC;IACJ,CAAC,MAAM,IAAI,CAACK,OAAO,IAAID,OAAO,IAAI,CAACG,SAAS,EAAE;MAC5C;MACA;MACA1C,QAAQ,CAACY,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEd,MAAM,CAAC+B,YAAY,EAAE1B,KAAK,EAAE;QAC9C4B,EAAE,EAAE;MACN,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIS,OAAO,IAAID,OAAO,IAAI,CAAC,CAAC,EAAEzC,MAAM,CAACM,cAAc,EAAEqC,SAAS,CAAC,EAAE;MACtE;MACA;MACA;MACAzC,QAAQ,CAACY,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEd,MAAM,CAAC+B,YAAY,EAAE1B,KAAK,EAAE;QAC9CyB,QAAQ,EAAEA,QAAQ,CAACE,IAAI,CAAC,IAAI,EAAE3B,KAAK,CAAC;QACpC4B,EAAE,EAAEU,SAAS,CAACd,KAAK,CAACI,EAAE;QACtBG,IAAI,EAAET,OAAO,CAACtB,KAAK,EAAE,MAAM,EAAEgC,SAAS,CAAC;QACvCF,KAAK,EAAER,OAAO,CAACtB,KAAK,EAAE,OAAO,EAAEgC,SAAS;MAC1C,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,OAAOnC,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}