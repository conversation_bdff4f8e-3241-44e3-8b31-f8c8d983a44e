{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getTabsUtilityClass(slot) {\n  return generateUtilityClass('MuiTabs', slot);\n}\nconst tabListClasses = generateUtilityClasses('MuiTabs', ['root', 'horizontal', 'vertical', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid', 'sizeSm', 'sizeMd', 'sizeLg']);\nexport default tabListClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getTabsUtilityClass", "slot", "tabListClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Tabs/tabsClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getTabsUtilityClass(slot) {\n  return generateUtilityClass('MuiTabs', slot);\n}\nconst tabListClasses = generateUtilityClasses('MuiTabs', ['root', 'horizontal', 'vertical', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid', 'sizeSm', 'sizeMd', 'sizeLg']);\nexport default tabListClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,mBAAmBA,CAACC,IAAI,EAAE;EACxC,OAAOH,oBAAoB,CAAC,SAAS,EAAEG,IAAI,CAAC;AAC9C;AACA,MAAMC,cAAc,GAAGH,sBAAsB,CAAC,SAAS,EAAE,CAAC,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,iBAAiB,EAAE,aAAa,EAAE,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;AAC3R,eAAeG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}