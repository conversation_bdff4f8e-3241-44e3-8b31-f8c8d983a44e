from plans.models import Plan, Task, Milestone, Subtask, Risk
from users.models import User
from django.db import transaction


def convert_to_json(data):
    converted_data = {"object": "list", "data": [],
                      "first_id": None, "last_id": None, "has_more": False}

    for message in data:
        message_dict = {item[0]: item[1] for item in message}

        if 'content' in message_dict:
            content_list = message_dict['content']
            if isinstance(content_list, list) and len(content_list) > 0:
                content_data = []
                for content_item in content_list:
                    content_dict = {}
                    for key, value in content_item:
                        if key == "text":
                            text_data = {sub_item[0]: sub_item[1]
                                         for sub_item in value}
                            content_dict[key] = text_data
                        else:
                            content_dict[key] = value
                    content_data.append(content_dict)
                message_dict['content'] = content_data

        converted_data['data'].append(message_dict)

    if converted_data['data']:
        converted_data['first_id'] = converted_data['data'][0]['id']
        converted_data['last_id'] = converted_data['data'][-1]['id']

    return converted_data


def convert_json_text(input_text):
    json_start = input_text.find('{')
    json_end = input_text.rfind('}') + 1
    if json_start != -1 and json_end != -1:
        return input_text[json_start:json_end]
    else:
        return None


def create_prompt_demo(**kwargs):
    return f"As a {kwargs['role']}, generate a plan in {kwargs['language']} with the prompt: {kwargs['prompt']} "


def generate_openai_prompt(**kwargs):
    """
    Generate OpenAI prompt based on project details passed through kwargs.
    """
    prompt = kwargs.get('prompt', 'Undefined project')
    role = kwargs.get('role', 'Undefined user')
    language = kwargs.get('language', 'Undefined')
    duration = kwargs.get('duration', '3 months')

    return (
        f"I want you to create a comprehensive, detailed, and highly actionable step-by-step plan for my project. Act as an expert project manager with deep domain knowledge in {role}'s field.\n\n"
        f"- Project requirements: {prompt}.\n"
        f"- My role: {role}.\n"
        f"- Language: {language}.\n"
        f"- Project duration: {duration}.\n\n"
        f"- Plan structure requirements: \n"
        f"    - The plan MUST have exactly 7-8 major milestones representing comprehensive coverage of the entire project lifecycle from initiation to completion.\n"
        f"    - Include milestones for: project initiation/planning, requirements analysis, design/architecture, development/implementation, testing/quality assurance, deployment/launch, monitoring/optimization, and project closure/evaluation as appropriate to the project type.\n"
        f"    - Each milestone needs to have 3-5 main tasks that are clear, focused objectives directly contributing to achieving that milestone.\n"
        f"    - Each task needs to have 4-6 highly specific, actionable subtasks that are concrete, measurable actions.\n"
        f"    - Include realistic estimated time frames for each milestone and task (in days or weeks).\n"
        f"- Description quality and specificity requirements: \n"
        f"    - Each milestone must have a detailed description (150-200 words) that explains its purpose, objectives, expected outcomes, key challenges, and how it advances the overall project.\n"
        f"    - Each task needs to have a comprehensive description (120-150 words) that explains exactly what needs to be done, including specific methodologies, tools, resources required, expected deliverables, and clear completion criteria.\n"
        f"    - Each subtask MUST have a highly specific, actionable description (80-120 words) that includes:\n"
        f"        * Exact steps to be performed\n"
        f"        * Specific tools, software, or resources to be used\n"
        f"        * Measurable outcomes or deliverables\n"
        f"        * Clear completion criteria that anyone can verify\n"
        f"        * Technical details, file names, or specific configurations when relevant\n"
        f"        * Time estimates for completion\n"
        f"    - Subtasks should be written so that a team member can execute them without needing additional clarification or interpretation.\n"
        f"- Hierarchy and actionability requirements: \n"
        f"    - SUBTASKS must be concrete, measurable actions that directly contribute to completing their parent TASK\n"
        f"    - TASKS must be clear objectives that directly contribute to achieving their parent MILESTONE\n"
        f"    - MILESTONES must represent significant project phases or deliverables that advance the project toward completion\n"
        f"    - Each subtask completion should directly advance the task toward completion\n"
        f"    - Each task completion should directly advance the milestone toward completion\n"
        f"    - Each milestone completion should directly advance the overall project toward successful delivery\n"
        f"    - Avoid vague language like 'research', 'analyze', 'review' - instead specify exactly what to research, what to analyze, what to review and what the expected output is\n"
        f"    - Use action verbs and specific deliverables: 'Create X document', 'Configure Y system', 'Test Z functionality', 'Deploy A to B environment'\n"
        f"- Domain-specific content: \n"
        f"    - Include industry-specific terminology, methodologies, and best practices relevant to {role}'s field.\n"
        f"    - Reference specific tools, technologies, or frameworks that would be used in this type of project.\n"
        f"    - Incorporate domain knowledge that demonstrates expertise in this field.\n"
        f"- Risk management: \n"
        f"    - For each milestone, identify 2-3 potential risks that are specific to this type of project.\n"
        f"    - Provide detailed mitigation strategies for each risk, explaining exactly how to prevent or address the issue.\n"
        f"    - Include contingency plans for critical tasks that might face obstacles.\n"
        f"- Success metrics: \n"
        f"    - Define 3-5 clear, measurable success criteria for each milestone.\n"
        f"    - Include quantifiable indicators to track progress (e.g., completion percentages, quality metrics).\n"
        f"    - Specify how to validate that each milestone has been successfully completed.\n"
        f"- Dependencies and resources: \n"
        f"    - Identify dependencies between tasks and milestones.\n"
        f"    - Specify key resources, skills, or expertise needed for each milestone.\n"
        f"- Plan flow and comprehensive coverage: \n"
        f"    - The plan must be presented in chronological order, covering the complete project lifecycle from initiation to closure.\n"
        f"    - Ensure the 7-8 milestones comprehensively cover: project planning, requirements gathering, design/architecture, development/implementation, testing/quality assurance, deployment/launch, monitoring/optimization, and project closure/evaluation.\n"
        f"    - Each milestone should represent a significant phase that produces tangible deliverables or achieves measurable outcomes.\n"
        f"    - Ensure logical dependencies between tasks and milestones are clearly defined.\n"
        f"    - Make sure the plan is realistic and achievable within the given timeframe of {duration}.\n"
        f"    - Include appropriate milestones for planning, execution, testing, deployment, and closure phases as relevant to the project type.\n\n"
        f"CRITICAL REQUIREMENTS SUMMARY:\n"
        f"- MUST create exactly 7-8 milestones (not fewer)\n"
        f"- MUST make subtasks highly specific and actionable (no vague descriptions)\n"
        f"- MUST ensure each subtask can be executed without additional clarification\n"
        f"- MUST create a clear hierarchy where subtasks → tasks → milestones → project completion\n"
        f"- MUST cover the complete project lifecycle from initiation to closure\n\n"
        f"Finally, please format the return result as JSON with the following structure:\n"
        f"{{'name': 'overview description of the plan',\n"
        f"'description': 'detailed introduction to the plan',\n"
        f"'milestones': [\n"
        f"  {{'name': 'name of this milestone',\n"
        f"   'description': 'detailed description of this milestone',\n"
        f"   'estimated_duration': 'estimated time to complete this milestone',\n"
        f"   'success_criteria': 'measurable criteria to determine milestone completion',\n"
        f"   'risks': [\n"
        f"     {{'risk': 'potential risk description', 'mitigation': 'strategy to mitigate this risk'}}\n"
        f"   ],\n"
        f"   'tasks': [\n"
        f"     {{'name': 'name of this task',\n"
        f"      'description': 'detailed description of this task',\n"
        f"      'estimated_duration': 'estimated time to complete this task',\n"
        f"      'subtasks': [\n"
        f"        {{'name': 'name of this subtask',\n"
        f"         'description': 'detailed description of this subtask'}}\n"
        f"      ]}}\n"
        f"   ]}}\n"
        f"]}}"
    )


def save_plan_to_db(plan_data_dict, user_id):
    with transaction.atomic():
        # Save plan
        plan = Plan.objects.create(
            name=plan_data_dict['name'],
            description=plan_data_dict['description'],
            user=User.objects.get(id=user_id)
        )
        plan_data_dict['slug'] = plan.slug

        # Save milestones
        for milestone_data in plan_data_dict['milestones']:
            milestone = Milestone.objects.create(
                name=milestone_data['name'],
                description=milestone_data.get('description', ''),
                plan=plan,
                estimated_duration=milestone_data.get('estimated_duration', ''),
                success_criteria=milestone_data.get('success_criteria', '')
            )

            # Save risks if they exist
            if 'risks' in milestone_data and milestone_data['risks']:
                for risk_data in milestone_data['risks']:
                    Risk.objects.create(
                        risk=risk_data.get('risk', ''),
                        mitigation=risk_data.get('mitigation', ''),
                        milestone=milestone
                    )

            # Save tasks
            for task_data in milestone_data['tasks']:
                task = Task.objects.create(
                    name=task_data['name'],
                    description=task_data.get('description', ''),
                    milestone=milestone,
                    estimated_duration=task_data.get('estimated_duration', '')
                )

                # Save subtasks
                if 'subtasks' in task_data and task_data['subtasks']:
                    for subtask_data in task_data['subtasks']:
                        Subtask.objects.create(
                            name=subtask_data['name'],
                            description=subtask_data.get('description', ''),
                            task=task
                        )
    return plan_data_dict
