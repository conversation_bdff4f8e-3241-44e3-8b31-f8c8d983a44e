{"ast": null, "code": "'use client';\n\n/**\n * @ignore - do not document.\n */\nexport default (function DeletedTextField() {\n  throw new Error(['MUI: `TextField` component has been removed in favor of Input composition.', '', 'To migrate, run `npx @mui/codemod@latest v5.0.0/joy-text-field-to-input <path>`.', 'For the codemod detail, visit https://github.com/mui/material-ui/blob/master/packages/mui-codemod/README.md#joy-text-field-to-input\\n\\nTo learn more why it has been removed, visit the RFC https://github.com/mui/material-ui/issues/34176'].join('\\n'));\n});", "map": {"version": 3, "names": ["DeletedTextField", "Error", "join"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/TextField/TextField.js"], "sourcesContent": ["'use client';\n\n/**\n * @ignore - do not document.\n */\nexport default (function DeletedTextField() {\n  throw new Error(['MUI: `TextField` component has been removed in favor of Input composition.', '', 'To migrate, run `npx @mui/codemod@latest v5.0.0/joy-text-field-to-input <path>`.', 'For the codemod detail, visit https://github.com/mui/material-ui/blob/master/packages/mui-codemod/README.md#joy-text-field-to-input\\n\\nTo learn more why it has been removed, visit the RFC https://github.com/mui/material-ui/issues/34176'].join('\\n'));\n});"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA,gBAAgB,SAASA,gBAAgBA,CAAA,EAAG;EAC1C,MAAM,IAAIC,KAAK,CAAC,CAAC,4EAA4E,EAAE,EAAE,EAAE,kFAAkF,EAAE,6OAA6O,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;AACnb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}