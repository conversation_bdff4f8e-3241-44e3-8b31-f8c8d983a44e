{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getFormLabelUtilityClass(slot) {\n  return generateUtilityClass('MuiFormLabel', slot);\n}\nconst formLabelClasses = generateUtilityClasses('MuiFormLabel', ['root', 'asterisk']);\nexport default formLabelClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getFormLabelUtilityClass", "slot", "formLabelClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/FormLabel/formLabelClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getFormLabelUtilityClass(slot) {\n  return generateUtilityClass('MuiFormLabel', slot);\n}\nconst formLabelClasses = generateUtilityClasses('MuiFormLabel', ['root', 'asterisk']);\nexport default formLabelClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,wBAAwBA,CAACC,IAAI,EAAE;EAC7C,OAAOH,oBAAoB,CAAC,cAAc,EAAEG,IAAI,CAAC;AACnD;AACA,MAAMC,gBAAgB,GAAGH,sBAAsB,CAAC,cAAc,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;AACrF,eAAeG,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}