{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { NumberInputActionTypes } from './numberInputAction.types';\nimport { clampStepwise, isNumber } from './utils';\nfunction getClampedValues(rawValue, context) {\n  const {\n    min,\n    max,\n    step\n  } = context;\n  const clampedValue = rawValue === null ? null : clampStepwise(rawValue, min, max, step);\n  const newInputValue = clampedValue === null ? '' : String(clampedValue);\n  return {\n    value: clampedValue,\n    inputValue: newInputValue\n  };\n}\nfunction stepValue(state, context, direction, multiplier) {\n  const {\n    value\n  } = state;\n  const {\n    step = 1,\n    min,\n    max\n  } = context;\n  if (isNumber(value)) {\n    return {\n      up: value + (step != null ? step : 1) * multiplier,\n      down: value - (step != null ? step : 1) * multiplier\n    }[direction];\n  }\n  return {\n    up: min != null ? min : 1,\n    down: max != null ? max : -1\n  }[direction];\n}\nfunction handleClamp(state, context, inputValue) {\n  const {\n    getInputValueAsString\n  } = context;\n  const numberValueAsString = getInputValueAsString(inputValue);\n  const intermediateValue = numberValueAsString === '' || numberValueAsString === '-' ? null : parseInt(numberValueAsString, 10);\n  const clampedValues = getClampedValues(intermediateValue, context);\n  return _extends({}, state, clampedValues);\n}\nfunction handleInputChange(state, context, inputValue) {\n  const {\n    getInputValueAsString\n  } = context;\n  const numberValueAsString = getInputValueAsString(inputValue);\n  if (numberValueAsString.match(/^-?\\d+?$/) || numberValueAsString === '' || numberValueAsString === '-') {\n    return _extends({}, state, {\n      inputValue: numberValueAsString\n    });\n  }\n  return state;\n}\n\n// use this for ArrowUp, ArrowDown, button clicks\n// use this with applyMultiplier: true for PageUp, PageDown, button shift-clicks\nfunction handleStep(state, context, applyMultiplier, direction) {\n  const multiplier = applyMultiplier ? context.shiftMultiplier : 1;\n  const newValue = stepValue(state, context, direction, multiplier);\n  const clampedValues = getClampedValues(newValue, context);\n  return _extends({}, state, clampedValues);\n}\nfunction handleToMinOrMax(state, context, to) {\n  const newValue = context[to];\n  if (!isNumber(newValue)) {\n    return state;\n  }\n  return _extends({}, state, {\n    value: newValue,\n    inputValue: String(newValue)\n  });\n}\nexport function numberInputReducer(state, action) {\n  const {\n    type,\n    context\n  } = action;\n  switch (type) {\n    case NumberInputActionTypes.clamp:\n      return handleClamp(state, context, action.inputValue);\n    case NumberInputActionTypes.inputChange:\n      return handleInputChange(state, context, action.inputValue);\n    case NumberInputActionTypes.increment:\n      return handleStep(state, context, action.applyMultiplier, 'up');\n    case NumberInputActionTypes.decrement:\n      return handleStep(state, context, action.applyMultiplier, 'down');\n    case NumberInputActionTypes.incrementToMax:\n      return handleToMinOrMax(state, context, 'max');\n    case NumberInputActionTypes.decrementToMin:\n      return handleToMinOrMax(state, context, 'min');\n    case NumberInputActionTypes.resetInputValue:\n      return _extends({}, state, {\n        inputValue: String(state.value)\n      });\n    default:\n      return state;\n  }\n}", "map": {"version": 3, "names": ["_extends", "NumberInputActionTypes", "clampStepwise", "isNumber", "getClampedValues", "rawValue", "context", "min", "max", "step", "clampedValue", "newInputValue", "String", "value", "inputValue", "<PERSON><PERSON><PERSON><PERSON>", "state", "direction", "multiplier", "up", "down", "handleClamp", "getInputValueAsString", "numberValueAsString", "intermediateValue", "parseInt", "<PERSON><PERSON><PERSON><PERSON>", "handleInputChange", "match", "handleStep", "applyMultiplier", "shiftMultiplier", "newValue", "handleToMinOrMax", "to", "numberInputReducer", "action", "type", "clamp", "inputChange", "increment", "decrement", "incrementToMax", "decrementToMin", "resetInputValue"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/unstable_useNumberInput/numberInputReducer.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { NumberInputActionTypes } from './numberInputAction.types';\nimport { clampStepwise, isNumber } from './utils';\nfunction getClampedValues(rawValue, context) {\n  const {\n    min,\n    max,\n    step\n  } = context;\n  const clampedValue = rawValue === null ? null : clampStepwise(rawValue, min, max, step);\n  const newInputValue = clampedValue === null ? '' : String(clampedValue);\n  return {\n    value: clampedValue,\n    inputValue: newInputValue\n  };\n}\nfunction stepValue(state, context, direction, multiplier) {\n  const {\n    value\n  } = state;\n  const {\n    step = 1,\n    min,\n    max\n  } = context;\n  if (isNumber(value)) {\n    return {\n      up: value + (step != null ? step : 1) * multiplier,\n      down: value - (step != null ? step : 1) * multiplier\n    }[direction];\n  }\n  return {\n    up: min != null ? min : 1,\n    down: max != null ? max : -1\n  }[direction];\n}\nfunction handleClamp(state, context, inputValue) {\n  const {\n    getInputValueAsString\n  } = context;\n  const numberValueAsString = getInputValueAsString(inputValue);\n  const intermediateValue = numberValueAsString === '' || numberValueAsString === '-' ? null : parseInt(numberValueAsString, 10);\n  const clampedValues = getClampedValues(intermediateValue, context);\n  return _extends({}, state, clampedValues);\n}\nfunction handleInputChange(state, context, inputValue) {\n  const {\n    getInputValueAsString\n  } = context;\n  const numberValueAsString = getInputValueAsString(inputValue);\n  if (numberValueAsString.match(/^-?\\d+?$/) || numberValueAsString === '' || numberValueAsString === '-') {\n    return _extends({}, state, {\n      inputValue: numberValueAsString\n    });\n  }\n  return state;\n}\n\n// use this for ArrowUp, ArrowDown, button clicks\n// use this with applyMultiplier: true for PageUp, PageDown, button shift-clicks\nfunction handleStep(state, context, applyMultiplier, direction) {\n  const multiplier = applyMultiplier ? context.shiftMultiplier : 1;\n  const newValue = stepValue(state, context, direction, multiplier);\n  const clampedValues = getClampedValues(newValue, context);\n  return _extends({}, state, clampedValues);\n}\nfunction handleToMinOrMax(state, context, to) {\n  const newValue = context[to];\n  if (!isNumber(newValue)) {\n    return state;\n  }\n  return _extends({}, state, {\n    value: newValue,\n    inputValue: String(newValue)\n  });\n}\nexport function numberInputReducer(state, action) {\n  const {\n    type,\n    context\n  } = action;\n  switch (type) {\n    case NumberInputActionTypes.clamp:\n      return handleClamp(state, context, action.inputValue);\n    case NumberInputActionTypes.inputChange:\n      return handleInputChange(state, context, action.inputValue);\n    case NumberInputActionTypes.increment:\n      return handleStep(state, context, action.applyMultiplier, 'up');\n    case NumberInputActionTypes.decrement:\n      return handleStep(state, context, action.applyMultiplier, 'down');\n    case NumberInputActionTypes.incrementToMax:\n      return handleToMinOrMax(state, context, 'max');\n    case NumberInputActionTypes.decrementToMin:\n      return handleToMinOrMax(state, context, 'min');\n    case NumberInputActionTypes.resetInputValue:\n      return _extends({}, state, {\n        inputValue: String(state.value)\n      });\n    default:\n      return state;\n  }\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,SAASC,aAAa,EAAEC,QAAQ,QAAQ,SAAS;AACjD,SAASC,gBAAgBA,CAACC,QAAQ,EAAEC,OAAO,EAAE;EAC3C,MAAM;IACJC,GAAG;IACHC,GAAG;IACHC;EACF,CAAC,GAAGH,OAAO;EACX,MAAMI,YAAY,GAAGL,QAAQ,KAAK,IAAI,GAAG,IAAI,GAAGH,aAAa,CAACG,QAAQ,EAAEE,GAAG,EAAEC,GAAG,EAAEC,IAAI,CAAC;EACvF,MAAME,aAAa,GAAGD,YAAY,KAAK,IAAI,GAAG,EAAE,GAAGE,MAAM,CAACF,YAAY,CAAC;EACvE,OAAO;IACLG,KAAK,EAAEH,YAAY;IACnBI,UAAU,EAAEH;EACd,CAAC;AACH;AACA,SAASI,SAASA,CAACC,KAAK,EAAEV,OAAO,EAAEW,SAAS,EAAEC,UAAU,EAAE;EACxD,MAAM;IACJL;EACF,CAAC,GAAGG,KAAK;EACT,MAAM;IACJP,IAAI,GAAG,CAAC;IACRF,GAAG;IACHC;EACF,CAAC,GAAGF,OAAO;EACX,IAAIH,QAAQ,CAACU,KAAK,CAAC,EAAE;IACnB,OAAO;MACLM,EAAE,EAAEN,KAAK,GAAG,CAACJ,IAAI,IAAI,IAAI,GAAGA,IAAI,GAAG,CAAC,IAAIS,UAAU;MAClDE,IAAI,EAAEP,KAAK,GAAG,CAACJ,IAAI,IAAI,IAAI,GAAGA,IAAI,GAAG,CAAC,IAAIS;IAC5C,CAAC,CAACD,SAAS,CAAC;EACd;EACA,OAAO;IACLE,EAAE,EAAEZ,GAAG,IAAI,IAAI,GAAGA,GAAG,GAAG,CAAC;IACzBa,IAAI,EAAEZ,GAAG,IAAI,IAAI,GAAGA,GAAG,GAAG,CAAC;EAC7B,CAAC,CAACS,SAAS,CAAC;AACd;AACA,SAASI,WAAWA,CAACL,KAAK,EAAEV,OAAO,EAAEQ,UAAU,EAAE;EAC/C,MAAM;IACJQ;EACF,CAAC,GAAGhB,OAAO;EACX,MAAMiB,mBAAmB,GAAGD,qBAAqB,CAACR,UAAU,CAAC;EAC7D,MAAMU,iBAAiB,GAAGD,mBAAmB,KAAK,EAAE,IAAIA,mBAAmB,KAAK,GAAG,GAAG,IAAI,GAAGE,QAAQ,CAACF,mBAAmB,EAAE,EAAE,CAAC;EAC9H,MAAMG,aAAa,GAAGtB,gBAAgB,CAACoB,iBAAiB,EAAElB,OAAO,CAAC;EAClE,OAAON,QAAQ,CAAC,CAAC,CAAC,EAAEgB,KAAK,EAAEU,aAAa,CAAC;AAC3C;AACA,SAASC,iBAAiBA,CAACX,KAAK,EAAEV,OAAO,EAAEQ,UAAU,EAAE;EACrD,MAAM;IACJQ;EACF,CAAC,GAAGhB,OAAO;EACX,MAAMiB,mBAAmB,GAAGD,qBAAqB,CAACR,UAAU,CAAC;EAC7D,IAAIS,mBAAmB,CAACK,KAAK,CAAC,UAAU,CAAC,IAAIL,mBAAmB,KAAK,EAAE,IAAIA,mBAAmB,KAAK,GAAG,EAAE;IACtG,OAAOvB,QAAQ,CAAC,CAAC,CAAC,EAAEgB,KAAK,EAAE;MACzBF,UAAU,EAAES;IACd,CAAC,CAAC;EACJ;EACA,OAAOP,KAAK;AACd;;AAEA;AACA;AACA,SAASa,UAAUA,CAACb,KAAK,EAAEV,OAAO,EAAEwB,eAAe,EAAEb,SAAS,EAAE;EAC9D,MAAMC,UAAU,GAAGY,eAAe,GAAGxB,OAAO,CAACyB,eAAe,GAAG,CAAC;EAChE,MAAMC,QAAQ,GAAGjB,SAAS,CAACC,KAAK,EAAEV,OAAO,EAAEW,SAAS,EAAEC,UAAU,CAAC;EACjE,MAAMQ,aAAa,GAAGtB,gBAAgB,CAAC4B,QAAQ,EAAE1B,OAAO,CAAC;EACzD,OAAON,QAAQ,CAAC,CAAC,CAAC,EAAEgB,KAAK,EAAEU,aAAa,CAAC;AAC3C;AACA,SAASO,gBAAgBA,CAACjB,KAAK,EAAEV,OAAO,EAAE4B,EAAE,EAAE;EAC5C,MAAMF,QAAQ,GAAG1B,OAAO,CAAC4B,EAAE,CAAC;EAC5B,IAAI,CAAC/B,QAAQ,CAAC6B,QAAQ,CAAC,EAAE;IACvB,OAAOhB,KAAK;EACd;EACA,OAAOhB,QAAQ,CAAC,CAAC,CAAC,EAAEgB,KAAK,EAAE;IACzBH,KAAK,EAAEmB,QAAQ;IACflB,UAAU,EAAEF,MAAM,CAACoB,QAAQ;EAC7B,CAAC,CAAC;AACJ;AACA,OAAO,SAASG,kBAAkBA,CAACnB,KAAK,EAAEoB,MAAM,EAAE;EAChD,MAAM;IACJC,IAAI;IACJ/B;EACF,CAAC,GAAG8B,MAAM;EACV,QAAQC,IAAI;IACV,KAAKpC,sBAAsB,CAACqC,KAAK;MAC/B,OAAOjB,WAAW,CAACL,KAAK,EAAEV,OAAO,EAAE8B,MAAM,CAACtB,UAAU,CAAC;IACvD,KAAKb,sBAAsB,CAACsC,WAAW;MACrC,OAAOZ,iBAAiB,CAACX,KAAK,EAAEV,OAAO,EAAE8B,MAAM,CAACtB,UAAU,CAAC;IAC7D,KAAKb,sBAAsB,CAACuC,SAAS;MACnC,OAAOX,UAAU,CAACb,KAAK,EAAEV,OAAO,EAAE8B,MAAM,CAACN,eAAe,EAAE,IAAI,CAAC;IACjE,KAAK7B,sBAAsB,CAACwC,SAAS;MACnC,OAAOZ,UAAU,CAACb,KAAK,EAAEV,OAAO,EAAE8B,MAAM,CAACN,eAAe,EAAE,MAAM,CAAC;IACnE,KAAK7B,sBAAsB,CAACyC,cAAc;MACxC,OAAOT,gBAAgB,CAACjB,KAAK,EAAEV,OAAO,EAAE,KAAK,CAAC;IAChD,KAAKL,sBAAsB,CAAC0C,cAAc;MACxC,OAAOV,gBAAgB,CAACjB,KAAK,EAAEV,OAAO,EAAE,KAAK,CAAC;IAChD,KAAKL,sBAAsB,CAAC2C,eAAe;MACzC,OAAO5C,QAAQ,CAAC,CAAC,CAAC,EAAEgB,KAAK,EAAE;QACzBF,UAAU,EAAEF,MAAM,CAACI,KAAK,CAACH,KAAK;MAChC,CAAC,CAAC;IACJ;MACE,OAAOG,KAAK;EAChB;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}