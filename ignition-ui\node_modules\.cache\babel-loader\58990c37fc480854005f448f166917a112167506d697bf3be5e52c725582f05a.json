{"ast": null, "code": "export { default as appendOwnerState } from '@mui/utils/appendOwnerState';", "map": {"version": 3, "names": ["default", "appendOwnerState"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/utils/appendOwnerState.js"], "sourcesContent": ["export { default as appendOwnerState } from '@mui/utils/appendOwnerState';"], "mappings": "AAAA,SAASA,OAAO,IAAIC,gBAAgB,QAAQ,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}