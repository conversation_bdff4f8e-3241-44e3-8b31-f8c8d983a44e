{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nexport const isVariantPalette = colorPalette => colorPalette && typeof colorPalette === 'object' && Object.keys(colorPalette).some(value => {\n  var _value$match;\n  return (_value$match = value.match) == null ? void 0 : _value$match.call(value, /^(plain(Hover|Active|Disabled)?(Color|Bg)|outlined(Hover|Active|Disabled)?(Color|Border|Bg)|soft(Hover|Active|Disabled)?(Color|Bg)|solid(Hover|Active|Disabled)?(Color|Bg))$/);\n});\nconst assignCss = (target, variantVar, value) => {\n  if (variantVar.includes('Color')) {\n    target.color = value;\n  }\n  if (variantVar.includes('Bg')) {\n    target.backgroundColor = value;\n  }\n  if (variantVar.includes('Border')) {\n    target.borderColor = value;\n  }\n};\n\n/**\n *\n * @param name variant name\n * @example 'plain'\n *\n * @param palette object that contains palette tokens\n * @example { primary: { plainColor: '', plainHoverColor: '', ...tokens }, ...other palette }\n *\n * @param getCssVar a function that receive variant token and return a CSS variable\n *\n * result will be the stylesheet based on the palette tokens\n * @example {\n *   color: '--token',\n *   backgroundColor: '--token',\n *   '--variant-borderWidth': '0px',\n * }\n * @example {\n *   cursor: 'pointer',\n *   color: '--token',\n *   backgroundColor: '--token',\n *   '--variant-borderWidth': '1px',\n * }\n * @example {\n *   pointerEvents: 'none',\n *   cursor: 'default',\n *   color: '--token',\n *   backgroundColor: '--token',\n *   '--variant-borderWidth': '0px',\n * }\n */\nexport const createVariantStyle = (name, palette, getCssVar) => {\n  const result = {};\n  Object.entries(palette || {}).forEach(_ref => {\n    let [variantVar, value] = _ref;\n    if (variantVar.match(new RegExp(`${name}(color|bg|border)`, 'i')) && !!value) {\n      const cssVar = getCssVar ? getCssVar(variantVar) : value;\n      if (variantVar.includes('Disabled')) {\n        result.pointerEvents = 'none';\n        result.cursor = 'default';\n        result['--Icon-color'] = 'currentColor';\n      }\n      if (variantVar.match(/(Hover|Active|Disabled)/)) {\n        assignCss(result, variantVar, cssVar);\n      } else {\n        // initial state\n        if (!result['--variant-borderWidth']) {\n          // important to prevent inheritance, otherwise the children will have the wrong styles e.g.\n          //   <Card variant=\"outlined\">\n          //     <Typography variant=\"soft\">\n          result['--variant-borderWidth'] = '0px';\n        }\n        if (variantVar.includes('Border')) {\n          result['--variant-borderWidth'] = '1px';\n          result.border = 'var(--variant-borderWidth) solid';\n        }\n        // border color should come later\n        assignCss(result, variantVar, cssVar);\n      }\n    }\n  });\n  return result;\n};\n// It's used only in extendTheme, so it's safe to always include default values\nexport const createVariant = (variant, theme) => {\n  let result = {};\n  if (theme) {\n    const {\n      getCssVar,\n      palette\n    } = theme;\n    Object.entries(palette).forEach(entry => {\n      const [color, colorPalette] = entry;\n      if (isVariantPalette(colorPalette) && typeof colorPalette === 'object') {\n        result = _extends({}, result, {\n          [color]: createVariantStyle(variant, colorPalette, variantVar => `var(--variant-${variantVar}, ${getCssVar(`palette-${color}-${variantVar}`, palette[color][variantVar])})`)\n        });\n      }\n    });\n  }\n  result.context = createVariantStyle(variant, {\n    plainColor: 'var(--variant-plainColor)',\n    plainHoverColor: `var(--variant-plainHoverColor)`,\n    plainHoverBg: 'var(--variant-plainHoverBg)',\n    plainActiveBg: 'var(--variant-plainActiveBg)',\n    plainDisabledColor: 'var(--variant-plainDisabledColor)',\n    outlinedColor: 'var(--variant-outlinedColor)',\n    outlinedBorder: 'var(--variant-outlinedBorder)',\n    outlinedHoverColor: `var(--variant-outlinedHoverColor)`,\n    outlinedHoverBorder: `var(--variant-outlinedHoverBorder)`,\n    outlinedHoverBg: `var(--variant-outlinedHoverBg)`,\n    outlinedActiveBg: `var(--variant-outlinedActiveBg)`,\n    outlinedDisabledColor: `var(--variant-outlinedDisabledColor)`,\n    outlinedDisabledBorder: `var(--variant-outlinedDisabledBorder)`,\n    softColor: 'var(--variant-softColor)',\n    softBg: 'var(--variant-softBg)',\n    softHoverColor: 'var(--variant-softHoverColor)',\n    softHoverBg: 'var(--variant-softHoverBg)',\n    softActiveBg: 'var(--variant-softActiveBg)',\n    softDisabledColor: 'var(--variant-softDisabledColor)',\n    softDisabledBg: 'var(--variant-softDisabledBg)',\n    solidColor: 'var(--variant-solidColor)',\n    solidBg: 'var(--variant-solidBg)',\n    solidHoverBg: 'var(--variant-solidHoverBg)',\n    solidActiveBg: 'var(--variant-solidActiveBg)',\n    solidDisabledColor: 'var(--variant-solidDisabledColor)',\n    solidDisabledBg: 'var(--variant-solidDisabledBg)'\n  });\n  return result;\n};", "map": {"version": 3, "names": ["_extends", "isVariantPalette", "colorPalette", "Object", "keys", "some", "value", "_value$match", "match", "call", "assignCss", "target", "variantVar", "includes", "color", "backgroundColor", "borderColor", "createVariantStyle", "name", "palette", "getCssVar", "result", "entries", "for<PERSON>ach", "_ref", "RegExp", "cssVar", "pointerEvents", "cursor", "border", "createVariant", "variant", "theme", "entry", "context", "plainColor", "plainHoverColor", "plainHoverBg", "plainActiveBg", "plainDisabledColor", "outlinedColor", "outlinedBorder", "outlinedHoverColor", "outlinedHoverBorder", "outlinedHoverBg", "outlinedActiveBg", "outlinedDisabledColor", "outlinedDisabledBorder", "softColor", "softBg", "softHoverColor", "softHoverBg", "softActiveBg", "softDisabledColor", "softDisabledBg", "solidColor", "solidBg", "solidHoverBg", "solidActiveBg", "solidDisabledColor", "solidDisabledBg"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/styles/variantUtils.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nexport const isVariantPalette = colorPalette => colorPalette && typeof colorPalette === 'object' && Object.keys(colorPalette).some(value => {\n  var _value$match;\n  return (_value$match = value.match) == null ? void 0 : _value$match.call(value, /^(plain(Hover|Active|Disabled)?(Color|Bg)|outlined(Hover|Active|Disabled)?(Color|Border|Bg)|soft(Hover|Active|Disabled)?(Color|Bg)|solid(Hover|Active|Disabled)?(Color|Bg))$/);\n});\nconst assignCss = (target, variantVar, value) => {\n  if (variantVar.includes('Color')) {\n    target.color = value;\n  }\n  if (variantVar.includes('Bg')) {\n    target.backgroundColor = value;\n  }\n  if (variantVar.includes('Border')) {\n    target.borderColor = value;\n  }\n};\n\n/**\n *\n * @param name variant name\n * @example 'plain'\n *\n * @param palette object that contains palette tokens\n * @example { primary: { plainColor: '', plainHoverColor: '', ...tokens }, ...other palette }\n *\n * @param getCssVar a function that receive variant token and return a CSS variable\n *\n * result will be the stylesheet based on the palette tokens\n * @example {\n *   color: '--token',\n *   backgroundColor: '--token',\n *   '--variant-borderWidth': '0px',\n * }\n * @example {\n *   cursor: 'pointer',\n *   color: '--token',\n *   backgroundColor: '--token',\n *   '--variant-borderWidth': '1px',\n * }\n * @example {\n *   pointerEvents: 'none',\n *   cursor: 'default',\n *   color: '--token',\n *   backgroundColor: '--token',\n *   '--variant-borderWidth': '0px',\n * }\n */\nexport const createVariantStyle = (name, palette, getCssVar) => {\n  const result = {};\n  Object.entries(palette || {}).forEach(([variantVar, value]) => {\n    if (variantVar.match(new RegExp(`${name}(color|bg|border)`, 'i')) && !!value) {\n      const cssVar = getCssVar ? getCssVar(variantVar) : value;\n      if (variantVar.includes('Disabled')) {\n        result.pointerEvents = 'none';\n        result.cursor = 'default';\n        result['--Icon-color'] = 'currentColor';\n      }\n      if (variantVar.match(/(Hover|Active|Disabled)/)) {\n        assignCss(result, variantVar, cssVar);\n      } else {\n        // initial state\n        if (!result['--variant-borderWidth']) {\n          // important to prevent inheritance, otherwise the children will have the wrong styles e.g.\n          //   <Card variant=\"outlined\">\n          //     <Typography variant=\"soft\">\n          result['--variant-borderWidth'] = '0px';\n        }\n        if (variantVar.includes('Border')) {\n          result['--variant-borderWidth'] = '1px';\n          result.border = 'var(--variant-borderWidth) solid';\n        }\n        // border color should come later\n        assignCss(result, variantVar, cssVar);\n      }\n    }\n  });\n  return result;\n};\n// It's used only in extendTheme, so it's safe to always include default values\nexport const createVariant = (variant, theme) => {\n  let result = {};\n  if (theme) {\n    const {\n      getCssVar,\n      palette\n    } = theme;\n    Object.entries(palette).forEach(entry => {\n      const [color, colorPalette] = entry;\n      if (isVariantPalette(colorPalette) && typeof colorPalette === 'object') {\n        result = _extends({}, result, {\n          [color]: createVariantStyle(variant, colorPalette, variantVar => `var(--variant-${variantVar}, ${getCssVar(`palette-${color}-${variantVar}`, palette[color][variantVar])})`)\n        });\n      }\n    });\n  }\n  result.context = createVariantStyle(variant, {\n    plainColor: 'var(--variant-plainColor)',\n    plainHoverColor: `var(--variant-plainHoverColor)`,\n    plainHoverBg: 'var(--variant-plainHoverBg)',\n    plainActiveBg: 'var(--variant-plainActiveBg)',\n    plainDisabledColor: 'var(--variant-plainDisabledColor)',\n    outlinedColor: 'var(--variant-outlinedColor)',\n    outlinedBorder: 'var(--variant-outlinedBorder)',\n    outlinedHoverColor: `var(--variant-outlinedHoverColor)`,\n    outlinedHoverBorder: `var(--variant-outlinedHoverBorder)`,\n    outlinedHoverBg: `var(--variant-outlinedHoverBg)`,\n    outlinedActiveBg: `var(--variant-outlinedActiveBg)`,\n    outlinedDisabledColor: `var(--variant-outlinedDisabledColor)`,\n    outlinedDisabledBorder: `var(--variant-outlinedDisabledBorder)`,\n    softColor: 'var(--variant-softColor)',\n    softBg: 'var(--variant-softBg)',\n    softHoverColor: 'var(--variant-softHoverColor)',\n    softHoverBg: 'var(--variant-softHoverBg)',\n    softActiveBg: 'var(--variant-softActiveBg)',\n    softDisabledColor: 'var(--variant-softDisabledColor)',\n    softDisabledBg: 'var(--variant-softDisabledBg)',\n    solidColor: 'var(--variant-solidColor)',\n    solidBg: 'var(--variant-solidBg)',\n    solidHoverBg: 'var(--variant-solidHoverBg)',\n    solidActiveBg: 'var(--variant-solidActiveBg)',\n    solidDisabledColor: 'var(--variant-solidDisabledColor)',\n    solidDisabledBg: 'var(--variant-solidDisabledBg)'\n  });\n  return result;\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,MAAMC,gBAAgB,GAAGC,YAAY,IAAIA,YAAY,IAAI,OAAOA,YAAY,KAAK,QAAQ,IAAIC,MAAM,CAACC,IAAI,CAACF,YAAY,CAAC,CAACG,IAAI,CAACC,KAAK,IAAI;EAC1I,IAAIC,YAAY;EAChB,OAAO,CAACA,YAAY,GAAGD,KAAK,CAACE,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,YAAY,CAACE,IAAI,CAACH,KAAK,EAAE,8KAA8K,CAAC;AACjQ,CAAC,CAAC;AACF,MAAMI,SAAS,GAAGA,CAACC,MAAM,EAAEC,UAAU,EAAEN,KAAK,KAAK;EAC/C,IAAIM,UAAU,CAACC,QAAQ,CAAC,OAAO,CAAC,EAAE;IAChCF,MAAM,CAACG,KAAK,GAAGR,KAAK;EACtB;EACA,IAAIM,UAAU,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE;IAC7BF,MAAM,CAACI,eAAe,GAAGT,KAAK;EAChC;EACA,IAAIM,UAAU,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAE;IACjCF,MAAM,CAACK,WAAW,GAAGV,KAAK;EAC5B;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMW,kBAAkB,GAAGA,CAACC,IAAI,EAAEC,OAAO,EAAEC,SAAS,KAAK;EAC9D,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjBlB,MAAM,CAACmB,OAAO,CAACH,OAAO,IAAI,CAAC,CAAC,CAAC,CAACI,OAAO,CAACC,IAAA,IAAyB;IAAA,IAAxB,CAACZ,UAAU,EAAEN,KAAK,CAAC,GAAAkB,IAAA;IACxD,IAAIZ,UAAU,CAACJ,KAAK,CAAC,IAAIiB,MAAM,CAAC,GAAGP,IAAI,mBAAmB,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,CAACZ,KAAK,EAAE;MAC5E,MAAMoB,MAAM,GAAGN,SAAS,GAAGA,SAAS,CAACR,UAAU,CAAC,GAAGN,KAAK;MACxD,IAAIM,UAAU,CAACC,QAAQ,CAAC,UAAU,CAAC,EAAE;QACnCQ,MAAM,CAACM,aAAa,GAAG,MAAM;QAC7BN,MAAM,CAACO,MAAM,GAAG,SAAS;QACzBP,MAAM,CAAC,cAAc,CAAC,GAAG,cAAc;MACzC;MACA,IAAIT,UAAU,CAACJ,KAAK,CAAC,yBAAyB,CAAC,EAAE;QAC/CE,SAAS,CAACW,MAAM,EAAET,UAAU,EAAEc,MAAM,CAAC;MACvC,CAAC,MAAM;QACL;QACA,IAAI,CAACL,MAAM,CAAC,uBAAuB,CAAC,EAAE;UACpC;UACA;UACA;UACAA,MAAM,CAAC,uBAAuB,CAAC,GAAG,KAAK;QACzC;QACA,IAAIT,UAAU,CAACC,QAAQ,CAAC,QAAQ,CAAC,EAAE;UACjCQ,MAAM,CAAC,uBAAuB,CAAC,GAAG,KAAK;UACvCA,MAAM,CAACQ,MAAM,GAAG,kCAAkC;QACpD;QACA;QACAnB,SAAS,CAACW,MAAM,EAAET,UAAU,EAAEc,MAAM,CAAC;MACvC;IACF;EACF,CAAC,CAAC;EACF,OAAOL,MAAM;AACf,CAAC;AACD;AACA,OAAO,MAAMS,aAAa,GAAGA,CAACC,OAAO,EAAEC,KAAK,KAAK;EAC/C,IAAIX,MAAM,GAAG,CAAC,CAAC;EACf,IAAIW,KAAK,EAAE;IACT,MAAM;MACJZ,SAAS;MACTD;IACF,CAAC,GAAGa,KAAK;IACT7B,MAAM,CAACmB,OAAO,CAACH,OAAO,CAAC,CAACI,OAAO,CAACU,KAAK,IAAI;MACvC,MAAM,CAACnB,KAAK,EAAEZ,YAAY,CAAC,GAAG+B,KAAK;MACnC,IAAIhC,gBAAgB,CAACC,YAAY,CAAC,IAAI,OAAOA,YAAY,KAAK,QAAQ,EAAE;QACtEmB,MAAM,GAAGrB,QAAQ,CAAC,CAAC,CAAC,EAAEqB,MAAM,EAAE;UAC5B,CAACP,KAAK,GAAGG,kBAAkB,CAACc,OAAO,EAAE7B,YAAY,EAAEU,UAAU,IAAI,iBAAiBA,UAAU,KAAKQ,SAAS,CAAC,WAAWN,KAAK,IAAIF,UAAU,EAAE,EAAEO,OAAO,CAACL,KAAK,CAAC,CAACF,UAAU,CAAC,CAAC,GAAG;QAC7K,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;EACAS,MAAM,CAACa,OAAO,GAAGjB,kBAAkB,CAACc,OAAO,EAAE;IAC3CI,UAAU,EAAE,2BAA2B;IACvCC,eAAe,EAAE,gCAAgC;IACjDC,YAAY,EAAE,6BAA6B;IAC3CC,aAAa,EAAE,8BAA8B;IAC7CC,kBAAkB,EAAE,mCAAmC;IACvDC,aAAa,EAAE,8BAA8B;IAC7CC,cAAc,EAAE,+BAA+B;IAC/CC,kBAAkB,EAAE,mCAAmC;IACvDC,mBAAmB,EAAE,oCAAoC;IACzDC,eAAe,EAAE,gCAAgC;IACjDC,gBAAgB,EAAE,iCAAiC;IACnDC,qBAAqB,EAAE,sCAAsC;IAC7DC,sBAAsB,EAAE,uCAAuC;IAC/DC,SAAS,EAAE,0BAA0B;IACrCC,MAAM,EAAE,uBAAuB;IAC/BC,cAAc,EAAE,+BAA+B;IAC/CC,WAAW,EAAE,4BAA4B;IACzCC,YAAY,EAAE,6BAA6B;IAC3CC,iBAAiB,EAAE,kCAAkC;IACrDC,cAAc,EAAE,+BAA+B;IAC/CC,UAAU,EAAE,2BAA2B;IACvCC,OAAO,EAAE,wBAAwB;IACjCC,YAAY,EAAE,6BAA6B;IAC3CC,aAAa,EAAE,8BAA8B;IAC7CC,kBAAkB,EAAE,mCAAmC;IACvDC,eAAe,EAAE;EACnB,CAAC,CAAC;EACF,OAAOvC,MAAM;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}