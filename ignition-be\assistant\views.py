import os
import re
import time
import json
import requests
from starlette import status
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated
from assistant.serializers import Plan<PERSON>romptSerializer
from rest_framework import status
from assistant.assistant_functions import OpenRouterConfig
from assistant.utils import convert_json_text, generate_openai_prompt, save_plan_to_db
# from assistant.tasks import call_assistant_api
from urllib.error import HTTPError, URLError
from dotenv import load_dotenv
from django.db.models import Q
from plans.models import Plan, Milestone, Task, Subtask, Risk
from plans.serializers import PlanViewSerializer
import threading
load_dotenv()


class CreateAssitantThreadView(APIView):
    def post(self, request):
        openrouter_config = OpenRouterConfig()
        try:
            thread = openrouter_config.create_thread()
            return Response({
                "data": thread,
                "status": status.HTTP_200_OK
            }, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({
                "error": str(e),
                "status": status.HTTP_400_BAD_REQUEST
            }, status=status.HTTP_400_BAD_REQUEST)


class AddMessageView(APIView):
    @swagger_auto_schema(
        operation_description="Add a message to an OpenAI thread",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['thread_id', 'message'],
            properties={
                'thread_id': openapi.Schema(type=openapi.TYPE_STRING, description='Thread ID'),
                'message': openapi.Schema(type=openapi.TYPE_STRING, description='Message content')
            },
        ),
        responses={200: openapi.Response('Response', openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'data': openapi.Schema(type=openapi.TYPE_STRING, description='Response data'),
                'status': openapi.Schema(type=openapi.TYPE_INTEGER, description='Response status')
            }
        ))}
    )
    def post(self, request):
        openrouter_config = OpenRouterConfig()
        try:
            thread_id = request.data.get("thread_id")
            message = request.data.get("message")
            response = openrouter_config.add_message(thread_id, message)
            return Response({
                "data": response,
                "status": status.HTTP_200_OK
            }, status=status.HTTP_200_OK)
        except Exception as e:
            return Response({
                "error": str(e),
                "status": status.HTTP_400_BAD_REQUEST
            }, status=status.HTTP_400_BAD_REQUEST)


class CreateRunAssistantGetResultView(APIView):
    @swagger_auto_schema(
        operation_description="Add a message to an OpenAI thread",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=['thread_id', 'content'],
            properties={
                'thread_id': openapi.Schema(type=openapi.TYPE_STRING, description='Thread ID'),
            },
        ),
        responses={200: openapi.Response('Response', openapi.Schema(
            type=openapi.TYPE_OBJECT,
            properties={
                'data': openapi.Schema(type=openapi.TYPE_STRING, description='Response data'),
                'status': openapi.Schema(type=openapi.TYPE_INTEGER, description='Response status')
            }
        ))}
    )
    def post(self, request):
        thread_id = request.data.get('thread_id')
        assistant_id = "openrouter-assistant"  # We don't need specific assistant ID for OpenRouter

        if not thread_id:
            return Response({
                "error": "Missing thread_id in the request."
            }, status=status.HTTP_400_BAD_REQUEST)

        openrouter_config = OpenRouterConfig()
        try:
            response = openrouter_config.create_run_assistant(thread_id, assistant_id)
            run_id = response["id"]

            # For OpenRouter, the response is immediate, so no need for polling
            if response["status"] == 'completed':
                result_message = openrouter_config.list_messages_assistant(thread_id)
                return Response({
                    "data": result_message,
                    "status": status.HTTP_200_OK
                }, status=status.HTTP_200_OK)
            else:
                return Response({
                    "error": "Run not completed",
                    "status": status.HTTP_400_BAD_REQUEST
                }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({
                "error": str(e),
                "status": status.HTTP_400_BAD_REQUEST
            }, status=status.HTTP_400_BAD_REQUEST)


class CreateProjectPlannerByAssistantView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(request_body=PlanPromptSerializer, responses={200: 'Plan created successfully'})
    def post(self, request):
        serializer = PlanPromptSerializer(data=request.data)
        if serializer.is_valid():
            prompt = serializer.validated_data['prompt']
            language = serializer.validated_data.get('language', 'English')
            role = serializer.validated_data.get('role', 'Project Manager')
            user = request.user
            try:
                openrouter_config = OpenRouterConfig()
                content = generate_openai_prompt(
                    prompt=prompt,
                    role=role,
                    language=language
                )

                # Use OpenRouter chat completion instead of threads
                messages = [
                    {"role": "system", "content": "You are an expert project planning assistant. You MUST follow ALL requirements exactly as specified. You MUST create detailed, specific subtasks that are 50+ words each with exact tools, deliverables, and steps. You MUST NOT create vague or short subtasks. You MUST create exactly 7-8 milestones. Failure to follow these requirements is unacceptable."},
                    {"role": "user", "content": content}
                ]

                chat_response = openrouter_config.chat_completion(
                    messages=messages,
                    temperature=0.7,
                    max_tokens=4000
                )

                # Process OpenRouter response
                if chat_response.get('choices') and chat_response['choices'][0].get('message'):
                    created_plan_data = chat_response['choices'][0]['message']['content']
                    converted_data = convert_json_text(created_plan_data)
                    plan_data_dict = json.loads(converted_data, strict=False)
                    saved_plan_data = save_plan_to_db(plan_data_dict, request.user.id)
                    return Response(saved_plan_data, status=status.HTTP_200_OK)
                else:
                    return Response({
                        "message": "Invalid response from OpenRouter API"
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            except HTTPError as e:
                print(f"HTTP Error occurred using Assistant API: {e}")
                return Response({
                    "message": "HTTP Error occurred using Assistant API",
                    "status": str(e)
                }, status=status.HTTP_404_NOT_FOUND)
            except URLError as e:
                print(f"URL Error occurred using Assistant API: {e}")
                return Response({
                    "message": "URL Error occurred using Assistant API",
                    "status": str(e)
                }, status=status.HTTP_400_BAD_REQUEST)
            except Exception as e:
                print(f"An Error occurred: {e}")
                return Response({
                    "message": f"An error occurred: {e}"
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        return Response({
            'error_messages': serializer.errors,
            'error_code': 400
        }, status=status.HTTP_400_BAD_REQUEST)


class CreateProjectPlannerByChatView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(request_body=PlanPromptSerializer, responses={202: 'Plan creation in progress'})
    def post(self, request):
        serializer = PlanPromptSerializer(data=request.data)
        if serializer.is_valid():
            prompt = serializer.validated_data['prompt']
            language = serializer.validated_data.get('language', 'English')
            role = serializer.validated_data.get('role', 'Project Manager')
            duration = serializer.validated_data.get('duration', '3 tháng')
            user = request.user

            # Tạo bản ghi plan tạm thời
            plan = Plan.objects.create(
                name="Plan being generated...",
                description="Waiting for AI response...",
                user=user,
                status="pending"
            )

            # Bắt đầu thread để xử lý yêu cầu OpenRouter
            thread = threading.Thread(
                target=self.process_openrouter_request,
                args=(prompt, language, role, duration, user.id, plan.id)
            )
            thread.daemon = True  # Thread sẽ tự động kết thúc khi chương trình chính kết thúc
            thread.start()

            return Response({
                "message": "Plan creation in progress",
                "plan_id": plan.id,
                "plan_slug": plan.slug,
                "status": "pending"
            }, status=status.HTTP_202_ACCEPTED)

        return Response({
            'error_messages': serializer.errors,
            'error_code': 400
        }, status=status.HTTP_400_BAD_REQUEST)

    def process_openrouter_request(self, prompt, language, role, duration, user_id, plan_id):
        try:
            # Lấy plan từ database
            plan = Plan.objects.get(id=plan_id)
            plan.status = "processing"
            plan.save()

            # Gọi OpenRouter API
            openrouter_config = OpenRouterConfig()
            openai_prompt = generate_openai_prompt(
                prompt=prompt,
                role=role,
                language=language,
                duration=duration
            )

            messages = [
                {"role": "system", "content": "You are an expert project planning assistant. You MUST follow ALL requirements exactly as specified. You MUST create detailed, specific subtasks that are 50+ words each with exact tools, deliverables, and steps. You MUST NOT create vague or short subtasks. You MUST create exactly 7-8 milestones. Failure to follow these requirements is unacceptable."},
                {"role": "user", "content": openai_prompt}
            ]

            chat_completion = openrouter_config.chat_completion(
                messages=messages,
                temperature=0.7,
                max_tokens=4000
            )

            # Xử lý kết quả
            if chat_completion.get('choices') and chat_completion['choices'][0].get('message'):
                plan_data = chat_completion['choices'][0]['message']['content']
                json_data_match = re.search(r"\{.*\}", plan_data, re.DOTALL)
                
                if json_data_match:
                    plan_data_cleaned = json_data_match.group(0)
                    
                    try:
                        plan_data_dict = json.loads(plan_data_cleaned, strict=False)
                        
                        # Cập nhật plan hiện có thay vì tạo mới
                        plan.name = plan_data_dict['name']
                        plan.description = plan_data_dict['description']
                        plan.status = "completed"
                        plan.save()
                        
                        # Lưu milestones, tasks, subtasks
                        for milestone_data in plan_data_dict['milestones']:
                            milestone = Milestone.objects.create(
                                name=milestone_data['name'],
                                description=milestone_data.get('description', ''),
                                plan=plan,
                                estimated_duration=milestone_data.get('estimated_duration', ''),
                                success_criteria=milestone_data.get('success_criteria', '')
                            )
                            
                            # Lưu risks nếu có
                            if 'risks' in milestone_data and milestone_data['risks']:
                                for risk_data in milestone_data['risks']:
                                    Risk.objects.create(
                                        risk=risk_data.get('risk', ''),
                                        mitigation=risk_data.get('mitigation', ''),
                                        milestone=milestone
                                    )
                            
                            # Lưu tasks
                            for task_data in milestone_data['tasks']:
                                task = Task.objects.create(
                                    name=task_data['name'],
                                    description=task_data.get('description', ''),
                                    milestone=milestone,
                                    estimated_duration=task_data.get('estimated_duration', '')
                                )
                                
                                # Lưu subtasks
                                if 'subtasks' in task_data and task_data['subtasks']:
                                    for subtask_data in task_data['subtasks']:
                                        Subtask.objects.create(
                                            name=subtask_data['name'],
                                            description=subtask_data.get('description', ''),
                                            task=task
                                        )
                    except Exception as e:
                        plan.status = "failed"
                        plan.description = f"Error processing plan data: {str(e)}"
                        plan.save()
                else:
                    plan.status = "failed"
                    plan.description = "No valid JSON found in the response"
                    plan.save()
            else:
                plan.status = "failed"
                plan.description = "Invalid response structure from OpenAI API"
                plan.save()
                
        except Exception as e:
            # Cập nhật plan với thông báo lỗi
            try:
                plan = Plan.objects.get(id=plan_id)
                plan.status = "failed"
                plan.description = f"An error occurred: {str(e)}"
                plan.save()
            except:
                print(f"Could not update plan status for plan_id {plan_id}: {str(e)}")


class PlanStatusView(APIView):
    permission_classes = [IsAuthenticated]
    
    @swagger_auto_schema(responses={200: 'Plan status retrieved successfully'})
    def get(self, request, plan_id):
        try:
            plan = Plan.objects.get(id=plan_id, user=request.user)
            
            if plan.status == 'completed':
                # Lấy thông tin đầy đủ của plan
                serializer = PlanViewSerializer(plan)
                return Response(serializer.data)
            else:
                # Chỉ trả về thông tin cơ bản và trạng thái
                return Response({
                    'id': plan.id,
                    'slug': plan.slug,
                    'name': plan.name,
                    'description': plan.description,
                    'status': plan.status,
                    'created_at': plan.created_at
                })
                
        except Plan.DoesNotExist:
            return Response({
                'error': 'Plan not found'
            }, status=status.HTTP_404_NOT_FOUND)
