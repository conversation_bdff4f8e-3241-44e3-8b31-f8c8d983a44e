{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getTabPanelUtilityClass(slot) {\n  return generateUtilityClass('MuiTabPanel', slot);\n}\nconst tabListClasses = generateUtilityClasses('MuiTabPanel', ['root', 'hidden', 'sizeSm', 'sizeMd', 'sizeLg', 'horizontal', 'vertical', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid']);\nexport default tabListClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getTabPanelUtilityClass", "slot", "tabListClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/TabPanel/tabPanelClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getTabPanelUtilityClass(slot) {\n  return generateUtilityClass('MuiTabPanel', slot);\n}\nconst tabListClasses = generateUtilityClasses('MuiTabPanel', ['root', 'hidden', 'sizeSm', 'sizeMd', 'sizeLg', 'horizontal', 'vertical', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid']);\nexport default tabListClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,uBAAuBA,CAACC,IAAI,EAAE;EAC5C,OAAOH,oBAAoB,CAAC,aAAa,EAAEG,IAAI,CAAC;AAClD;AACA,MAAMC,cAAc,GAAGH,sBAAsB,CAAC,aAAa,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,UAAU,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,iBAAiB,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;AACzS,eAAeG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}