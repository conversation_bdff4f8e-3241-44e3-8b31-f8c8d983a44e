{"ast": null, "code": "import * as React from 'react';\nconst RowListContext = /*#__PURE__*/React.createContext(false);\nif (process.env.NODE_ENV !== 'production') {\n  RowListContext.displayName = 'RowListContext';\n}\nexport default RowListContext;", "map": {"version": 3, "names": ["React", "RowListContext", "createContext", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/List/RowListContext.js"], "sourcesContent": ["import * as React from 'react';\nconst RowListContext = /*#__PURE__*/React.createContext(false);\nif (process.env.NODE_ENV !== 'production') {\n  RowListContext.displayName = 'RowListContext';\n}\nexport default RowListContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,cAAc,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,KAAK,CAAC;AAC9D,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,cAAc,CAACK,WAAW,GAAG,gBAAgB;AAC/C;AACA,eAAeL,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}