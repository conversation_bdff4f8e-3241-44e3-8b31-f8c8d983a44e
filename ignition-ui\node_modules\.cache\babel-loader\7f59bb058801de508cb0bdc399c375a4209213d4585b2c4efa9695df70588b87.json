{"ast": null, "code": "var _jsxFileName = \"C:\\\\ignition\\\\ignition-ui\\\\src\\\\components\\\\Sidebar\\\\RightSidebar.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport axios from 'axios';\nimport dayjs from 'dayjs';\nimport { Box, Typography, IconButton, Tooltip, CircularProgress, Link } from '@mui/material';\nimport { APIURL, mainYellowColor } from \"helpers/constants\";\nimport { getHeaders } from \"helpers/functions\";\nimport Iconify from 'components/Iconify/index';\nimport styles from './rightSidebar.module.scss';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RightSidebar = _ref => {\n  _s();\n  let {\n    isOpen,\n    onToggle\n  } = _ref;\n  const [allTasks, setAllTasks] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const navigate = useNavigate();\n  const sampleTasks = [{\n    id: 'sample-1',\n    title: 'Complete AI Project Report',\n    plan_name: 'Generative AI Learning Plan',\n    end_date: dayjs().add(2, 'hour').format(),\n    status: 'todo',\n    priority: 'urgent' // Urgent\n  }, {\n    id: 'sample-2',\n    title: 'Team Meeting on Project Progress',\n    plan_name: 'Generative AI Learning Plan',\n    end_date: dayjs().add(4, 'hour').format(),\n    status: 'todo',\n    priority: 'high' // High priority\n  }, {\n    id: 'sample-3',\n    title: 'Research on New LLM Models',\n    plan_name: 'Generative AI Learning Plan',\n    end_date: dayjs().add(1, 'day').format(),\n    status: 'done',\n    priority: 'normal' // Normal\n  }, {\n    id: 'sample-4',\n    title: 'Prepare Slides for Presentation',\n    plan_name: 'Generative AI Learning Plan',\n    end_date: dayjs().add(5, 'hour').format(),\n    status: 'todo',\n    priority: 'high' // High priority\n  }];\n  useEffect(() => {\n    fetchAllTasks();\n\n    // Automatically update task list every 5 minutes\n    const intervalId = setInterval(() => {\n      fetchAllTasks();\n    }, 5 * 60 * 1000);\n    return () => clearInterval(intervalId);\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  const fetchAllTasks = async () => {\n    setLoading(true);\n    try {\n      const response = await axios.get(`${APIURL}/api/tasks/todo-by-status`, {\n        headers: getHeaders()\n      });\n\n      // Filter out completed tasks and sort by priority and deadline\n      const activeTasks = response.data.filter(task => task.status !== 3); // Not completed\n      setAllTasks(activeTasks);\n    } catch (error) {\n      console.error('Error fetching tasks:', error);\n      setAllTasks([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleTaskStatusChange = async (taskId, newStatus) => {\n    if (showSampleTasks) {\n      // If displaying sample data, just update the state\n      const updatedTasks = todayTasks.map(task => task.id === taskId ? {\n        ...task,\n        status: newStatus\n      } : task);\n      setTodayTasks(updatedTasks);\n      return;\n    }\n    try {\n      await axios.put(`${APIURL}/api/tasks/${taskId}/status`, {\n        status: newStatus\n      }, {\n        headers: getHeaders()\n      });\n      fetchTodayTasks();\n    } catch (error) {\n      console.error('Error updating task status:', error);\n    }\n  };\n  const handlePlanClick = planName => {\n    if (!showSampleTasks) {\n      // Navigate to plan detail page\n      navigate('/d/plans');\n    }\n  };\n  const getPriorityLabel = priority => {\n    const priorityMap = {\n      1: 'Low',\n      2: 'Medium',\n      3: 'High',\n      4: 'Critical'\n    };\n    return priorityMap[priority] || 'Medium';\n  };\n  const getPriorityColor = priority => {\n    const colorMap = {\n      1: '#10b981',\n      // green\n      2: '#f59e0b',\n      // yellow\n      3: '#f97316',\n      // orange\n      4: '#ef4444' // red\n    };\n    return colorMap[priority] || '#f59e0b';\n  };\n\n  // Check if a task is urgent (deadline within 3 hours or critical priority)\n  const isUrgentTask = task => {\n    if (task.priority === 4) return true; // Critical priority\n\n    if (!task.end_date) return false;\n    const endTime = dayjs(task.end_date);\n    const now = dayjs();\n    const hoursLeft = endTime.diff(now, 'hour');\n    return hoursLeft <= 3 && hoursLeft >= 0;\n  };\n\n  // Check if a task is high priority\n  const isHighPriorityTask = task => {\n    return task.priority === 3; // High priority\n  };\n\n  // Format time remaining\n  const formatTimeLeft = task => {\n    const endTime = dayjs(task.end_date);\n    const now = dayjs();\n    if (endTime.isBefore(now)) {\n      return 'Overdue';\n    }\n    const hoursLeft = endTime.diff(now, 'hour');\n    const minutesLeft = endTime.diff(now, 'minute') % 60;\n    if (hoursLeft === 0) {\n      return `${minutesLeft} minutes`;\n    } else if (hoursLeft < 24) {\n      return `${hoursLeft} hours ${minutesLeft} minutes`;\n    } else {\n      const daysLeft = Math.floor(hoursLeft / 24);\n      return `${daysLeft} days`;\n    }\n  };\n\n  // Sort tasks by priority and deadline\n  const sortedTasks = [...todayTasks].sort((a, b) => {\n    // Prioritize urgent tasks\n    if (isUrgentTask(a) && !isUrgentTask(b)) return -1;\n    if (!isUrgentTask(a) && isUrgentTask(b)) return 1;\n\n    // Then prioritize tasks with high priority\n    if (isHighPriorityTask(a) && !isHighPriorityTask(b)) return -1;\n    if (!isHighPriorityTask(a) && isHighPriorityTask(b)) return 1;\n\n    // Finally sort by closest deadline\n    return dayjs(a.end_date).diff(dayjs(b.end_date));\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `${styles.rightSidebar} ${isOpen ? styles.open : ''}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.rightSidebarHeader,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        className: styles.rightSidebarTitle,\n        children: [/*#__PURE__*/_jsxDEV(Iconify, {\n          icon: \"material-symbols:today\",\n          width: 22,\n          height: 22,\n          color: mainYellowColor\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), \"Today's Tasks\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 184,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"Refresh\",\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: fetchTodayTasks,\n          className: styles.refreshButton,\n          children: /*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"material-symbols:refresh\",\n            width: 18,\n            height: 18\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 183,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.rightSidebarContent,\n      children: loading ? /*#__PURE__*/_jsxDEV(Box, {\n        className: styles.loadingContainer,\n        children: /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 30,\n          sx: {\n            color: mainYellowColor\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 11\n      }, this) : sortedTasks.length > 0 ? sortedTasks.map(task => {\n        const isUrgent = isUrgentTask(task);\n        const isHighPriority = isHighPriorityTask(task);\n        const timeLeft = formatTimeLeft(task);\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `${styles.taskItem} ${isUrgent ? styles.urgent : ''} ${isHighPriority && !isUrgent ? styles.priority : ''}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.taskHeader,\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              className: styles.taskTitle,\n              children: task.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 212,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: task.status === 'done' ? 'Mark as incomplete' : 'Mark as complete',\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                className: styles.taskCheckbox,\n                onClick: () => handleTaskStatusChange(task.id || task.slug, task.status === 'done' ? 'todo' : 'done'),\n                size: \"small\",\n                children: /*#__PURE__*/_jsxDEV(Iconify, {\n                  icon: task.status === 'done' ? \"material-symbols:check-circle\" : \"material-symbols:radio-button-unchecked\",\n                  width: 18,\n                  height: 18,\n                  sx: {\n                    color: task.status === 'done' ? mainYellowColor : undefined\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 219,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 214,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 211,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.taskBadges,\n            children: [isUrgent && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: styles.urgentBadge,\n              children: [/*#__PURE__*/_jsxDEV(Iconify, {\n                icon: \"material-symbols:warning\",\n                width: 12,\n                height: 12\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 23\n              }, this), \"Urgent\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 21\n            }, this), isHighPriority && !isUrgent && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: styles.priorityBadge,\n              children: [/*#__PURE__*/_jsxDEV(Iconify, {\n                icon: \"material-symbols:priority-high\",\n                width: 12,\n                height: 12\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 238,\n                columnNumber: 23\n              }, this), \"High Priority\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 229,\n            columnNumber: 17\n          }, this), task.plan_name && /*#__PURE__*/_jsxDEV(Link, {\n            className: styles.planName,\n            onClick: () => handlePlanClick(task.plan_name),\n            sx: {\n              cursor: 'pointer',\n              color: mainYellowColor\n            },\n            children: task.plan_name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 245,\n            columnNumber: 19\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: styles.taskDateCover,\n            children: [/*#__PURE__*/_jsxDEV(Iconify, {\n              icon: \"material-symbols:schedule\",\n              width: 16,\n              height: 16,\n              color: isUrgent ? \"#f44336\" : \"#64748b\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              className: `${styles.taskDate} ${isUrgent ? styles.urgentTime : ''}`,\n              children: task.end_date ? `Remaining: ${timeLeft}` : 'No deadline'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 255,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 253,\n            columnNumber: 17\n          }, this)]\n        }, task.id || task.slug, true, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 15\n        }, this);\n      }) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: styles.noTasksMessage,\n        children: [/*#__PURE__*/_jsxDEV(Iconify, {\n          icon: \"material-symbols:check-circle-outline\",\n          width: 40,\n          height: 40,\n          color: mainYellowColor\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          children: \"No tasks for today!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 195,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: styles.rightSidebarFooter,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"caption\",\n        className: styles.footerText,\n        children: [todayTasks.length, \" tasks for today\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this), isOpen && /*#__PURE__*/_jsxDEV(Tooltip, {\n        title: \"Hide sidebar\",\n        placement: \"top\",\n        children: /*#__PURE__*/_jsxDEV(IconButton, {\n          onClick: onToggle,\n          className: styles.closeButton,\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"material-symbols:chevron-right\",\n            width: 20,\n            height: 20\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 276,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 275,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 270,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 182,\n    columnNumber: 5\n  }, this);\n};\n_s(RightSidebar, \"bZhBVSbfIoRF2yiJOYY2dy0ZmO8=\", false, function () {\n  return [useNavigate];\n});\n_c = RightSidebar;\nexport default RightSidebar;\nvar _c;\n$RefreshReg$(_c, \"RightSidebar\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "axios", "dayjs", "Box", "Typography", "IconButton", "<PERSON><PERSON><PERSON>", "CircularProgress", "Link", "APIURL", "mainYellowColor", "getHeaders", "Iconify", "styles", "useNavigate", "jsxDEV", "_jsxDEV", "RightSidebar", "_ref", "_s", "isOpen", "onToggle", "allTasks", "setAllTasks", "loading", "setLoading", "navigate", "sampleTasks", "id", "title", "plan_name", "end_date", "add", "format", "status", "priority", "fetchAllTasks", "intervalId", "setInterval", "clearInterval", "response", "get", "headers", "activeTasks", "data", "filter", "task", "error", "console", "handleTaskStatusChange", "taskId", "newStatus", "showSampleTasks", "updatedTasks", "todayTasks", "map", "setTodayTasks", "put", "fetchTodayTasks", "handlePlanClick", "planName", "getPriorityLabel", "priorityMap", "getPriorityColor", "colorMap", "isUrgentTask", "endTime", "now", "hoursLeft", "diff", "isHighPriorityTask", "formatTimeLeft", "isBefore", "minutesLeft", "daysLeft", "Math", "floor", "sortedTasks", "sort", "a", "b", "className", "rightSidebar", "open", "children", "rightSidebarHeader", "variant", "rightSidebarTitle", "icon", "width", "height", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "refreshButton", "rightSidebarContent", "loadingContainer", "size", "sx", "length", "is<PERSON><PERSON>", "isHighPriority", "timeLeft", "taskItem", "urgent", "<PERSON><PERSON><PERSON><PERSON>", "taskTitle", "taskCheckbox", "slug", "undefined", "taskBadges", "urgentBadge", "priorityBadge", "cursor", "taskDateCover", "taskDate", "urgentTime", "noTasksMessage", "rightSide<PERSON>Footer", "footerText", "placement", "closeButton", "_c", "$RefreshReg$"], "sources": ["C:/ignition/ignition-ui/src/components/Sidebar/RightSidebar.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport axios from 'axios';\r\nimport dayjs from 'dayjs';\r\nimport { Box, Typography, IconButton, Tooltip, CircularProgress, Link } from '@mui/material';\r\nimport { APIURL, mainYellowColor } from \"helpers/constants\";\r\nimport { getHeaders } from \"helpers/functions\";\r\nimport Iconify from 'components/Iconify/index';\r\nimport styles from './rightSidebar.module.scss';\r\nimport { useNavigate } from 'react-router-dom';\r\n\r\nconst RightSidebar = ({ isOpen, onToggle }) => {\r\n  const [allTasks, setAllTasks] = useState([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const navigate = useNavigate();\r\n\r\n  const sampleTasks = [\r\n    {\r\n      id: 'sample-1',\r\n      title: 'Complete AI Project Report',\r\n      plan_name: 'Generative AI Learning Plan',\r\n      end_date: dayjs().add(2, 'hour').format(),\r\n      status: 'todo',\r\n      priority: 'urgent' // Urgent\r\n    },\r\n    {\r\n      id: 'sample-2',\r\n      title: 'Team Meeting on Project Progress',\r\n      plan_name: 'Generative AI Learning Plan',\r\n      end_date: dayjs().add(4, 'hour').format(),\r\n      status: 'todo',\r\n      priority: 'high' // High priority\r\n    },\r\n    {\r\n      id: 'sample-3',\r\n      title: 'Research on New LLM Models',\r\n      plan_name: 'Generative AI Learning Plan',\r\n      end_date: dayjs().add(1, 'day').format(),\r\n      status: 'done',\r\n      priority: 'normal' // Normal\r\n    },\r\n    {\r\n      id: 'sample-4',\r\n      title: 'Prepare Slides for Presentation',\r\n      plan_name: 'Generative AI Learning Plan',\r\n      end_date: dayjs().add(5, 'hour').format(),\r\n      status: 'todo',\r\n      priority: 'high' // High priority\r\n    }\r\n  ];\r\n\r\n  useEffect(() => {\r\n    fetchAllTasks();\r\n\r\n    // Automatically update task list every 5 minutes\r\n    const intervalId = setInterval(() => {\r\n      fetchAllTasks();\r\n    }, 5 * 60 * 1000);\r\n\r\n    return () => clearInterval(intervalId);\r\n  // eslint-disable-next-line react-hooks/exhaustive-deps\r\n  }, []);\r\n\r\n  const fetchAllTasks = async () => {\r\n    setLoading(true);\r\n    try {\r\n      const response = await axios.get(`${APIURL}/api/tasks/todo-by-status`, { headers: getHeaders() });\r\n\r\n      // Filter out completed tasks and sort by priority and deadline\r\n      const activeTasks = response.data.filter(task => task.status !== 3); // Not completed\r\n      setAllTasks(activeTasks);\r\n    } catch (error) {\r\n      console.error('Error fetching tasks:', error);\r\n      setAllTasks([]);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleTaskStatusChange = async (taskId, newStatus) => {\r\n    if (showSampleTasks) {\r\n      // If displaying sample data, just update the state\r\n      const updatedTasks = todayTasks.map(task =>\r\n        task.id === taskId ? { ...task, status: newStatus } : task\r\n      );\r\n      setTodayTasks(updatedTasks);\r\n      return;\r\n    }\r\n\r\n    try {\r\n      await axios.put(\r\n        `${APIURL}/api/tasks/${taskId}/status`,\r\n        { status: newStatus },\r\n        { headers: getHeaders() }\r\n      );\r\n      fetchTodayTasks();\r\n    } catch (error) {\r\n      console.error('Error updating task status:', error);\r\n    }\r\n  };\r\n\r\n  const handlePlanClick = (planName) => {\r\n    if (!showSampleTasks) {\r\n      // Navigate to plan detail page\r\n      navigate('/d/plans');\r\n    }\r\n  };\r\n\r\n  const getPriorityLabel = (priority) => {\r\n    const priorityMap = {\r\n      1: 'Low',\r\n      2: 'Medium',\r\n      3: 'High',\r\n      4: 'Critical'\r\n    };\r\n    return priorityMap[priority] || 'Medium';\r\n  };\r\n\r\n  const getPriorityColor = (priority) => {\r\n    const colorMap = {\r\n      1: '#10b981', // green\r\n      2: '#f59e0b', // yellow\r\n      3: '#f97316', // orange\r\n      4: '#ef4444'  // red\r\n    };\r\n    return colorMap[priority] || '#f59e0b';\r\n  };\r\n\r\n  // Check if a task is urgent (deadline within 3 hours or critical priority)\r\n  const isUrgentTask = (task) => {\r\n    if (task.priority === 4) return true; // Critical priority\r\n\r\n    if (!task.end_date) return false;\r\n    const endTime = dayjs(task.end_date);\r\n    const now = dayjs();\r\n    const hoursLeft = endTime.diff(now, 'hour');\r\n\r\n    return hoursLeft <= 3 && hoursLeft >= 0;\r\n  };\r\n\r\n  // Check if a task is high priority\r\n  const isHighPriorityTask = (task) => {\r\n    return task.priority === 3; // High priority\r\n  };\r\n\r\n  // Format time remaining\r\n  const formatTimeLeft = (task) => {\r\n    const endTime = dayjs(task.end_date);\r\n    const now = dayjs();\r\n    \r\n    if (endTime.isBefore(now)) {\r\n      return 'Overdue';\r\n    }\r\n    \r\n    const hoursLeft = endTime.diff(now, 'hour');\r\n    const minutesLeft = endTime.diff(now, 'minute') % 60;\r\n    \r\n    if (hoursLeft === 0) {\r\n      return `${minutesLeft} minutes`;\r\n    } else if (hoursLeft < 24) {\r\n      return `${hoursLeft} hours ${minutesLeft} minutes`;\r\n    } else {\r\n      const daysLeft = Math.floor(hoursLeft / 24);\r\n      return `${daysLeft} days`;\r\n    }\r\n  };\r\n\r\n  // Sort tasks by priority and deadline\r\n  const sortedTasks = [...todayTasks].sort((a, b) => {\r\n    // Prioritize urgent tasks\r\n    if (isUrgentTask(a) && !isUrgentTask(b)) return -1;\r\n    if (!isUrgentTask(a) && isUrgentTask(b)) return 1;\r\n    \r\n    // Then prioritize tasks with high priority\r\n    if (isHighPriorityTask(a) && !isHighPriorityTask(b)) return -1;\r\n    if (!isHighPriorityTask(a) && isHighPriorityTask(b)) return 1;\r\n    \r\n    // Finally sort by closest deadline\r\n    return dayjs(a.end_date).diff(dayjs(b.end_date));\r\n  });\r\n\r\n  return (\r\n    <div className={`${styles.rightSidebar} ${isOpen ? styles.open : ''}`}>\r\n      <div className={styles.rightSidebarHeader}>\r\n        <Typography variant=\"h6\" className={styles.rightSidebarTitle}>\r\n          <Iconify icon=\"material-symbols:today\" width={22} height={22} color={mainYellowColor} />\r\n          Today's Tasks\r\n        </Typography>\r\n        <Tooltip title=\"Refresh\">\r\n          <IconButton onClick={fetchTodayTasks} className={styles.refreshButton}>\r\n            <Iconify icon=\"material-symbols:refresh\" width={18} height={18} />\r\n          </IconButton>\r\n        </Tooltip>\r\n      </div>\r\n\r\n      <div className={styles.rightSidebarContent}>\r\n        {loading ? (\r\n          <Box className={styles.loadingContainer}>\r\n            <CircularProgress size={30} sx={{ color: mainYellowColor }} />\r\n          </Box>\r\n        ) : sortedTasks.length > 0 ? (\r\n          sortedTasks.map((task) => {\r\n            const isUrgent = isUrgentTask(task);\r\n            const isHighPriority = isHighPriorityTask(task);\r\n            const timeLeft = formatTimeLeft(task);\r\n            \r\n            return (\r\n              <div \r\n                key={task.id || task.slug} \r\n                className={`${styles.taskItem} ${isUrgent ? styles.urgent : ''} ${isHighPriority && !isUrgent ? styles.priority : ''}`}\r\n              >\r\n                <div className={styles.taskHeader}>\r\n                  <Typography className={styles.taskTitle}>{task.title}</Typography>\r\n                  <Tooltip title={task.status === 'done' ? 'Mark as incomplete' : 'Mark as complete'}>\r\n                    <IconButton\r\n                      className={styles.taskCheckbox}\r\n                      onClick={() => handleTaskStatusChange(task.id || task.slug, task.status === 'done' ? 'todo' : 'done')}\r\n                      size=\"small\"\r\n                    >\r\n                      <Iconify\r\n                        icon={task.status === 'done' ? \"material-symbols:check-circle\" : \"material-symbols:radio-button-unchecked\"}\r\n                        width={18}\r\n                        height={18}\r\n                        sx={{ color: task.status === 'done' ? mainYellowColor : undefined }}\r\n                      />\r\n                    </IconButton>\r\n                  </Tooltip>\r\n                </div>\r\n                \r\n                <div className={styles.taskBadges}>\r\n                  {isUrgent && (\r\n                    <span className={styles.urgentBadge}>\r\n                      <Iconify icon=\"material-symbols:warning\" width={12} height={12} />\r\n                      Urgent\r\n                    </span>\r\n                  )}\r\n                  {isHighPriority && !isUrgent && (\r\n                    <span className={styles.priorityBadge}>\r\n                      <Iconify icon=\"material-symbols:priority-high\" width={12} height={12} />\r\n                      High Priority\r\n                    </span>\r\n                  )}\r\n                </div>\r\n                \r\n                {task.plan_name && (\r\n                  <Link \r\n                    className={styles.planName}\r\n                    onClick={() => handlePlanClick(task.plan_name)}\r\n                    sx={{ cursor: 'pointer', color: mainYellowColor }}\r\n                  >\r\n                    {task.plan_name}\r\n                  </Link>\r\n                )}\r\n                <div className={styles.taskDateCover}>\r\n                  <Iconify icon=\"material-symbols:schedule\" width={16} height={16} color={isUrgent ? \"#f44336\" : \"#64748b\"} />\r\n                  <Typography className={`${styles.taskDate} ${isUrgent ? styles.urgentTime : ''}`}>\r\n                    {task.end_date ? `Remaining: ${timeLeft}` : 'No deadline'}\r\n                  </Typography>\r\n                </div>\r\n              </div>\r\n            );\r\n          })\r\n        ) : (\r\n          <div className={styles.noTasksMessage}>\r\n            <Iconify icon=\"material-symbols:check-circle-outline\" width={40} height={40} color={mainYellowColor} />\r\n            <Typography>No tasks for today!</Typography>\r\n          </div>\r\n        )}\r\n      </div>\r\n\r\n      <div className={styles.rightSidebarFooter}>\r\n        <Typography variant=\"caption\" className={styles.footerText}>\r\n          {todayTasks.length} tasks for today\r\n        </Typography>\r\n        {isOpen && (\r\n          <Tooltip title=\"Hide sidebar\" placement=\"top\">\r\n            <IconButton onClick={onToggle} className={styles.closeButton} size=\"small\">\r\n              <Iconify icon=\"material-symbols:chevron-right\" width={20} height={20} />\r\n            </IconButton>\r\n          </Tooltip>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default RightSidebar; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,GAAG,EAAEC,UAAU,EAAEC,UAAU,EAAEC,OAAO,EAAEC,gBAAgB,EAAEC,IAAI,QAAQ,eAAe;AAC5F,SAASC,MAAM,EAAEC,eAAe,QAAQ,mBAAmB;AAC3D,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,YAAY,GAAGC,IAAA,IAA0B;EAAAC,EAAA;EAAA,IAAzB;IAAEC,MAAM;IAAEC;EAAS,CAAC,GAAAH,IAAA;EACxC,MAAM,CAACI,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACyB,OAAO,EAAEC,UAAU,CAAC,GAAG1B,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM2B,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAE9B,MAAMa,WAAW,GAAG,CAClB;IACEC,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,4BAA4B;IACnCC,SAAS,EAAE,6BAA6B;IACxCC,QAAQ,EAAE7B,KAAK,CAAC,CAAC,CAAC8B,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAACC,MAAM,CAAC,CAAC;IACzCC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE,QAAQ,CAAC;EACrB,CAAC,EACD;IACEP,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,kCAAkC;IACzCC,SAAS,EAAE,6BAA6B;IACxCC,QAAQ,EAAE7B,KAAK,CAAC,CAAC,CAAC8B,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAACC,MAAM,CAAC,CAAC;IACzCC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE,MAAM,CAAC;EACnB,CAAC,EACD;IACEP,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,4BAA4B;IACnCC,SAAS,EAAE,6BAA6B;IACxCC,QAAQ,EAAE7B,KAAK,CAAC,CAAC,CAAC8B,GAAG,CAAC,CAAC,EAAE,KAAK,CAAC,CAACC,MAAM,CAAC,CAAC;IACxCC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE,QAAQ,CAAC;EACrB,CAAC,EACD;IACEP,EAAE,EAAE,UAAU;IACdC,KAAK,EAAE,iCAAiC;IACxCC,SAAS,EAAE,6BAA6B;IACxCC,QAAQ,EAAE7B,KAAK,CAAC,CAAC,CAAC8B,GAAG,CAAC,CAAC,EAAE,MAAM,CAAC,CAACC,MAAM,CAAC,CAAC;IACzCC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE,MAAM,CAAC;EACnB,CAAC,CACF;EAEDnC,SAAS,CAAC,MAAM;IACdoC,aAAa,CAAC,CAAC;;IAEf;IACA,MAAMC,UAAU,GAAGC,WAAW,CAAC,MAAM;MACnCF,aAAa,CAAC,CAAC;IACjB,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC;IAEjB,OAAO,MAAMG,aAAa,CAACF,UAAU,CAAC;IACxC;EACA,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMD,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCX,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMe,QAAQ,GAAG,MAAMvC,KAAK,CAACwC,GAAG,CAAC,GAAGhC,MAAM,2BAA2B,EAAE;QAAEiC,OAAO,EAAE/B,UAAU,CAAC;MAAE,CAAC,CAAC;;MAEjG;MACA,MAAMgC,WAAW,GAAGH,QAAQ,CAACI,IAAI,CAACC,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACZ,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC;MACrEX,WAAW,CAACoB,WAAW,CAAC;IAC1B,CAAC,CAAC,OAAOI,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;MAC7CxB,WAAW,CAAC,EAAE,CAAC;IACjB,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMwB,sBAAsB,GAAG,MAAAA,CAAOC,MAAM,EAAEC,SAAS,KAAK;IAC1D,IAAIC,eAAe,EAAE;MACnB;MACA,MAAMC,YAAY,GAAGC,UAAU,CAACC,GAAG,CAACT,IAAI,IACtCA,IAAI,CAAClB,EAAE,KAAKsB,MAAM,GAAG;QAAE,GAAGJ,IAAI;QAAEZ,MAAM,EAAEiB;MAAU,CAAC,GAAGL,IACxD,CAAC;MACDU,aAAa,CAACH,YAAY,CAAC;MAC3B;IACF;IAEA,IAAI;MACF,MAAMpD,KAAK,CAACwD,GAAG,CACb,GAAGhD,MAAM,cAAcyC,MAAM,SAAS,EACtC;QAAEhB,MAAM,EAAEiB;MAAU,CAAC,EACrB;QAAET,OAAO,EAAE/B,UAAU,CAAC;MAAE,CAC1B,CAAC;MACD+C,eAAe,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACrD;EACF,CAAC;EAED,MAAMY,eAAe,GAAIC,QAAQ,IAAK;IACpC,IAAI,CAACR,eAAe,EAAE;MACpB;MACA1B,QAAQ,CAAC,UAAU,CAAC;IACtB;EACF,CAAC;EAED,MAAMmC,gBAAgB,GAAI1B,QAAQ,IAAK;IACrC,MAAM2B,WAAW,GAAG;MAClB,CAAC,EAAE,KAAK;MACR,CAAC,EAAE,QAAQ;MACX,CAAC,EAAE,MAAM;MACT,CAAC,EAAE;IACL,CAAC;IACD,OAAOA,WAAW,CAAC3B,QAAQ,CAAC,IAAI,QAAQ;EAC1C,CAAC;EAED,MAAM4B,gBAAgB,GAAI5B,QAAQ,IAAK;IACrC,MAAM6B,QAAQ,GAAG;MACf,CAAC,EAAE,SAAS;MAAE;MACd,CAAC,EAAE,SAAS;MAAE;MACd,CAAC,EAAE,SAAS;MAAE;MACd,CAAC,EAAE,SAAS,CAAE;IAChB,CAAC;IACD,OAAOA,QAAQ,CAAC7B,QAAQ,CAAC,IAAI,SAAS;EACxC,CAAC;;EAED;EACA,MAAM8B,YAAY,GAAInB,IAAI,IAAK;IAC7B,IAAIA,IAAI,CAACX,QAAQ,KAAK,CAAC,EAAE,OAAO,IAAI,CAAC,CAAC;;IAEtC,IAAI,CAACW,IAAI,CAACf,QAAQ,EAAE,OAAO,KAAK;IAChC,MAAMmC,OAAO,GAAGhE,KAAK,CAAC4C,IAAI,CAACf,QAAQ,CAAC;IACpC,MAAMoC,GAAG,GAAGjE,KAAK,CAAC,CAAC;IACnB,MAAMkE,SAAS,GAAGF,OAAO,CAACG,IAAI,CAACF,GAAG,EAAE,MAAM,CAAC;IAE3C,OAAOC,SAAS,IAAI,CAAC,IAAIA,SAAS,IAAI,CAAC;EACzC,CAAC;;EAED;EACA,MAAME,kBAAkB,GAAIxB,IAAI,IAAK;IACnC,OAAOA,IAAI,CAACX,QAAQ,KAAK,CAAC,CAAC,CAAC;EAC9B,CAAC;;EAED;EACA,MAAMoC,cAAc,GAAIzB,IAAI,IAAK;IAC/B,MAAMoB,OAAO,GAAGhE,KAAK,CAAC4C,IAAI,CAACf,QAAQ,CAAC;IACpC,MAAMoC,GAAG,GAAGjE,KAAK,CAAC,CAAC;IAEnB,IAAIgE,OAAO,CAACM,QAAQ,CAACL,GAAG,CAAC,EAAE;MACzB,OAAO,SAAS;IAClB;IAEA,MAAMC,SAAS,GAAGF,OAAO,CAACG,IAAI,CAACF,GAAG,EAAE,MAAM,CAAC;IAC3C,MAAMM,WAAW,GAAGP,OAAO,CAACG,IAAI,CAACF,GAAG,EAAE,QAAQ,CAAC,GAAG,EAAE;IAEpD,IAAIC,SAAS,KAAK,CAAC,EAAE;MACnB,OAAO,GAAGK,WAAW,UAAU;IACjC,CAAC,MAAM,IAAIL,SAAS,GAAG,EAAE,EAAE;MACzB,OAAO,GAAGA,SAAS,UAAUK,WAAW,UAAU;IACpD,CAAC,MAAM;MACL,MAAMC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACR,SAAS,GAAG,EAAE,CAAC;MAC3C,OAAO,GAAGM,QAAQ,OAAO;IAC3B;EACF,CAAC;;EAED;EACA,MAAMG,WAAW,GAAG,CAAC,GAAGvB,UAAU,CAAC,CAACwB,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IACjD;IACA,IAAIf,YAAY,CAACc,CAAC,CAAC,IAAI,CAACd,YAAY,CAACe,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IAClD,IAAI,CAACf,YAAY,CAACc,CAAC,CAAC,IAAId,YAAY,CAACe,CAAC,CAAC,EAAE,OAAO,CAAC;;IAEjD;IACA,IAAIV,kBAAkB,CAACS,CAAC,CAAC,IAAI,CAACT,kBAAkB,CAACU,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;IAC9D,IAAI,CAACV,kBAAkB,CAACS,CAAC,CAAC,IAAIT,kBAAkB,CAACU,CAAC,CAAC,EAAE,OAAO,CAAC;;IAE7D;IACA,OAAO9E,KAAK,CAAC6E,CAAC,CAAChD,QAAQ,CAAC,CAACsC,IAAI,CAACnE,KAAK,CAAC8E,CAAC,CAACjD,QAAQ,CAAC,CAAC;EAClD,CAAC,CAAC;EAEF,oBACEf,OAAA;IAAKiE,SAAS,EAAE,GAAGpE,MAAM,CAACqE,YAAY,IAAI9D,MAAM,GAAGP,MAAM,CAACsE,IAAI,GAAG,EAAE,EAAG;IAAAC,QAAA,gBACpEpE,OAAA;MAAKiE,SAAS,EAAEpE,MAAM,CAACwE,kBAAmB;MAAAD,QAAA,gBACxCpE,OAAA,CAACZ,UAAU;QAACkF,OAAO,EAAC,IAAI;QAACL,SAAS,EAAEpE,MAAM,CAAC0E,iBAAkB;QAAAH,QAAA,gBAC3DpE,OAAA,CAACJ,OAAO;UAAC4E,IAAI,EAAC,wBAAwB;UAACC,KAAK,EAAE,EAAG;UAACC,MAAM,EAAE,EAAG;UAACC,KAAK,EAAEjF;QAAgB;UAAAkF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,iBAE1F;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACb/E,OAAA,CAACV,OAAO;QAACuB,KAAK,EAAC,SAAS;QAAAuD,QAAA,eACtBpE,OAAA,CAACX,UAAU;UAAC2F,OAAO,EAAEtC,eAAgB;UAACuB,SAAS,EAAEpE,MAAM,CAACoF,aAAc;UAAAb,QAAA,eACpEpE,OAAA,CAACJ,OAAO;YAAC4E,IAAI,EAAC,0BAA0B;YAACC,KAAK,EAAE,EAAG;YAACC,MAAM,EAAE;UAAG;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC,eAEN/E,OAAA;MAAKiE,SAAS,EAAEpE,MAAM,CAACqF,mBAAoB;MAAAd,QAAA,EACxC5D,OAAO,gBACNR,OAAA,CAACb,GAAG;QAAC8E,SAAS,EAAEpE,MAAM,CAACsF,gBAAiB;QAAAf,QAAA,eACtCpE,OAAA,CAACT,gBAAgB;UAAC6F,IAAI,EAAE,EAAG;UAACC,EAAE,EAAE;YAAEV,KAAK,EAAEjF;UAAgB;QAAE;UAAAkF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D,CAAC,GACJlB,WAAW,CAACyB,MAAM,GAAG,CAAC,GACxBzB,WAAW,CAACtB,GAAG,CAAET,IAAI,IAAK;QACxB,MAAMyD,QAAQ,GAAGtC,YAAY,CAACnB,IAAI,CAAC;QACnC,MAAM0D,cAAc,GAAGlC,kBAAkB,CAACxB,IAAI,CAAC;QAC/C,MAAM2D,QAAQ,GAAGlC,cAAc,CAACzB,IAAI,CAAC;QAErC,oBACE9B,OAAA;UAEEiE,SAAS,EAAE,GAAGpE,MAAM,CAAC6F,QAAQ,IAAIH,QAAQ,GAAG1F,MAAM,CAAC8F,MAAM,GAAG,EAAE,IAAIH,cAAc,IAAI,CAACD,QAAQ,GAAG1F,MAAM,CAACsB,QAAQ,GAAG,EAAE,EAAG;UAAAiD,QAAA,gBAEvHpE,OAAA;YAAKiE,SAAS,EAAEpE,MAAM,CAAC+F,UAAW;YAAAxB,QAAA,gBAChCpE,OAAA,CAACZ,UAAU;cAAC6E,SAAS,EAAEpE,MAAM,CAACgG,SAAU;cAAAzB,QAAA,EAAEtC,IAAI,CAACjB;YAAK;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa,CAAC,eAClE/E,OAAA,CAACV,OAAO;cAACuB,KAAK,EAAEiB,IAAI,CAACZ,MAAM,KAAK,MAAM,GAAG,oBAAoB,GAAG,kBAAmB;cAAAkD,QAAA,eACjFpE,OAAA,CAACX,UAAU;gBACT4E,SAAS,EAAEpE,MAAM,CAACiG,YAAa;gBAC/Bd,OAAO,EAAEA,CAAA,KAAM/C,sBAAsB,CAACH,IAAI,CAAClB,EAAE,IAAIkB,IAAI,CAACiE,IAAI,EAAEjE,IAAI,CAACZ,MAAM,KAAK,MAAM,GAAG,MAAM,GAAG,MAAM,CAAE;gBACtGkE,IAAI,EAAC,OAAO;gBAAAhB,QAAA,eAEZpE,OAAA,CAACJ,OAAO;kBACN4E,IAAI,EAAE1C,IAAI,CAACZ,MAAM,KAAK,MAAM,GAAG,+BAA+B,GAAG,yCAA0C;kBAC3GuD,KAAK,EAAE,EAAG;kBACVC,MAAM,EAAE,EAAG;kBACXW,EAAE,EAAE;oBAAEV,KAAK,EAAE7C,IAAI,CAACZ,MAAM,KAAK,MAAM,GAAGxB,eAAe,GAAGsG;kBAAU;gBAAE;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eAEN/E,OAAA;YAAKiE,SAAS,EAAEpE,MAAM,CAACoG,UAAW;YAAA7B,QAAA,GAC/BmB,QAAQ,iBACPvF,OAAA;cAAMiE,SAAS,EAAEpE,MAAM,CAACqG,WAAY;cAAA9B,QAAA,gBAClCpE,OAAA,CAACJ,OAAO;gBAAC4E,IAAI,EAAC,0BAA0B;gBAACC,KAAK,EAAE,EAAG;gBAACC,MAAM,EAAE;cAAG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,UAEpE;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP,EACAS,cAAc,IAAI,CAACD,QAAQ,iBAC1BvF,OAAA;cAAMiE,SAAS,EAAEpE,MAAM,CAACsG,aAAc;cAAA/B,QAAA,gBACpCpE,OAAA,CAACJ,OAAO;gBAAC4E,IAAI,EAAC,gCAAgC;gBAACC,KAAK,EAAE,EAAG;gBAACC,MAAM,EAAE;cAAG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,iBAE1E;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,EAELjD,IAAI,CAAChB,SAAS,iBACbd,OAAA,CAACR,IAAI;YACHyE,SAAS,EAAEpE,MAAM,CAAC+C,QAAS;YAC3BoC,OAAO,EAAEA,CAAA,KAAMrC,eAAe,CAACb,IAAI,CAAChB,SAAS,CAAE;YAC/CuE,EAAE,EAAE;cAAEe,MAAM,EAAE,SAAS;cAAEzB,KAAK,EAAEjF;YAAgB,CAAE;YAAA0E,QAAA,EAEjDtC,IAAI,CAAChB;UAAS;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACX,CACP,eACD/E,OAAA;YAAKiE,SAAS,EAAEpE,MAAM,CAACwG,aAAc;YAAAjC,QAAA,gBACnCpE,OAAA,CAACJ,OAAO;cAAC4E,IAAI,EAAC,2BAA2B;cAACC,KAAK,EAAE,EAAG;cAACC,MAAM,EAAE,EAAG;cAACC,KAAK,EAAEY,QAAQ,GAAG,SAAS,GAAG;YAAU;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC5G/E,OAAA,CAACZ,UAAU;cAAC6E,SAAS,EAAE,GAAGpE,MAAM,CAACyG,QAAQ,IAAIf,QAAQ,GAAG1F,MAAM,CAAC0G,UAAU,GAAG,EAAE,EAAG;cAAAnC,QAAA,EAC9EtC,IAAI,CAACf,QAAQ,GAAG,cAAc0E,QAAQ,EAAE,GAAG;YAAa;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA,GAlDDjD,IAAI,CAAClB,EAAE,IAAIkB,IAAI,CAACiE,IAAI;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmDtB,CAAC;MAEV,CAAC,CAAC,gBAEF/E,OAAA;QAAKiE,SAAS,EAAEpE,MAAM,CAAC2G,cAAe;QAAApC,QAAA,gBACpCpE,OAAA,CAACJ,OAAO;UAAC4E,IAAI,EAAC,uCAAuC;UAACC,KAAK,EAAE,EAAG;UAACC,MAAM,EAAE,EAAG;UAACC,KAAK,EAAEjF;QAAgB;UAAAkF,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACvG/E,OAAA,CAACZ,UAAU;UAAAgF,QAAA,EAAC;QAAmB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAEN/E,OAAA;MAAKiE,SAAS,EAAEpE,MAAM,CAAC4G,kBAAmB;MAAArC,QAAA,gBACxCpE,OAAA,CAACZ,UAAU;QAACkF,OAAO,EAAC,SAAS;QAACL,SAAS,EAAEpE,MAAM,CAAC6G,UAAW;QAAAtC,QAAA,GACxD9B,UAAU,CAACgD,MAAM,EAAC,kBACrB;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,EACZ3E,MAAM,iBACLJ,OAAA,CAACV,OAAO;QAACuB,KAAK,EAAC,cAAc;QAAC8F,SAAS,EAAC,KAAK;QAAAvC,QAAA,eAC3CpE,OAAA,CAACX,UAAU;UAAC2F,OAAO,EAAE3E,QAAS;UAAC4D,SAAS,EAAEpE,MAAM,CAAC+G,WAAY;UAACxB,IAAI,EAAC,OAAO;UAAAhB,QAAA,eACxEpE,OAAA,CAACJ,OAAO;YAAC4E,IAAI,EAAC,gCAAgC;YAACC,KAAK,EAAE,EAAG;YAACC,MAAM,EAAE;UAAG;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CACV;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5E,EAAA,CAjRIF,YAAY;EAAA,QAGCH,WAAW;AAAA;AAAA+G,EAAA,GAHxB5G,YAAY;AAmRlB,eAAeA,YAAY;AAAC,IAAA4G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}