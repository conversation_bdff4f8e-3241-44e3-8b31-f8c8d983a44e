{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getAccordionGroupUtilityClass(slot) {\n  return generateUtilityClass('MuiAccordionGroup', slot);\n}\nconst accordionGroupClasses = generateUtilityClasses('MuiAccordionGroup', ['root', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid', 'sizeSm', 'sizeMd', 'sizeLg']);\nexport default accordionGroupClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getAccordionGroupUtilityClass", "slot", "accordionGroupClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/AccordionGroup/accordionGroupClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getAccordionGroupUtilityClass(slot) {\n  return generateUtilityClass('MuiAccordionGroup', slot);\n}\nconst accordionGroupClasses = generateUtilityClasses('MuiAccordionGroup', ['root', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid', 'sizeSm', 'sizeMd', 'sizeLg']);\nexport default accordionGroupClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,6BAA6BA,CAACC,IAAI,EAAE;EAClD,OAAOH,oBAAoB,CAAC,mBAAmB,EAAEG,IAAI,CAAC;AACxD;AACA,MAAMC,qBAAqB,GAAGH,sBAAsB,CAAC,mBAAmB,EAAE,CAAC,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,iBAAiB,EAAE,aAAa,EAAE,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;AAClR,eAAeG,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}