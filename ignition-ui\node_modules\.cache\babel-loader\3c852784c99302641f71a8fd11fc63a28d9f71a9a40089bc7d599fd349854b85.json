{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { useThemeProps as systemUseThemeProps } from '@mui/system';\nimport defaultTheme from './defaultTheme';\nimport THEME_ID from './identifier';\nexport default function useThemeProps(_ref) {\n  let {\n    props,\n    name\n  } = _ref;\n  return systemUseThemeProps({\n    props,\n    name,\n    defaultTheme: _extends({}, defaultTheme, {\n      components: {}\n    }),\n    themeId: THEME_ID\n  });\n}", "map": {"version": 3, "names": ["_extends", "useThemeProps", "systemUseThemeProps", "defaultTheme", "THEME_ID", "_ref", "props", "name", "components", "themeId"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/styles/useThemeProps.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { useThemeProps as systemUseThemeProps } from '@mui/system';\nimport defaultTheme from './defaultTheme';\nimport THEME_ID from './identifier';\nexport default function useThemeProps({\n  props,\n  name\n}) {\n  return systemUseThemeProps({\n    props,\n    name,\n    defaultTheme: _extends({}, defaultTheme, {\n      components: {}\n    }),\n    themeId: THEME_ID\n  });\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,aAAa,IAAIC,mBAAmB,QAAQ,aAAa;AAClE,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,QAAQ,MAAM,cAAc;AACnC,eAAe,SAASH,aAAaA,CAAAI,IAAA,EAGlC;EAAA,IAHmC;IACpCC,KAAK;IACLC;EACF,CAAC,GAAAF,IAAA;EACC,OAAOH,mBAAmB,CAAC;IACzBI,KAAK;IACLC,IAAI;IACJJ,YAAY,EAAEH,QAAQ,CAAC,CAAC,CAAC,EAAEG,YAAY,EAAE;MACvCK,UAAU,EAAE,CAAC;IACf,CAAC,CAAC;IACFC,OAAO,EAAEL;EACX,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}