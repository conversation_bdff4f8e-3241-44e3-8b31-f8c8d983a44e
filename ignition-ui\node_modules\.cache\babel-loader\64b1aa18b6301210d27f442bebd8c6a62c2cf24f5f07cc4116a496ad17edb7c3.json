{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\n/**\n * @ignore - do not document.\n *\n * Use this function determine the host element correctly on the server (in a SSR context, for example Next.js)\n */\nexport function useRootElementName(parameters) {\n  const {\n    rootElementName: rootElementNameProp = '',\n    componentName\n  } = parameters;\n  const [rootElementName, setRootElementName] = React.useState(rootElementNameProp.toUpperCase());\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (rootElementNameProp && rootElementName !== rootElementNameProp.toUpperCase()) {\n        console.error(`useRootElementName: the \\`rootElementName\\` prop of ${componentName ? `the ${componentName} component` : 'a component'} expected the '${rootElementNameProp}' element, but a '${rootElementName.toLowerCase()}' was rendered instead`, 'This may cause hydration issues in an SSR context, for example in a Next.js app');\n      }\n    }, [rootElementNameProp, rootElementName, componentName]);\n  }\n  const updateRootElementName = React.useCallback(instance => {\n    var _instance$tagName;\n    setRootElementName((_instance$tagName = instance == null ? void 0 : instance.tagName) != null ? _instance$tagName : '');\n  }, []);\n  return [rootElementName, updateRootElementName];\n}", "map": {"version": 3, "names": ["React", "useRootElementName", "parameters", "rootElementName", "rootElementNameProp", "componentName", "setRootElementName", "useState", "toUpperCase", "process", "env", "NODE_ENV", "useEffect", "console", "error", "toLowerCase", "updateRootElementName", "useCallback", "instance", "_instance$tagName", "tagName"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/utils/useRootElementName.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\n/**\n * @ignore - do not document.\n *\n * Use this function determine the host element correctly on the server (in a SSR context, for example Next.js)\n */\nexport function useRootElementName(parameters) {\n  const {\n    rootElementName: rootElementNameProp = '',\n    componentName\n  } = parameters;\n  const [rootElementName, setRootElementName] = React.useState(rootElementNameProp.toUpperCase());\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (rootElementNameProp && rootElementName !== rootElementNameProp.toUpperCase()) {\n        console.error(`useRootElementName: the \\`rootElementName\\` prop of ${componentName ? `the ${componentName} component` : 'a component'} expected the '${rootElementNameProp}' element, but a '${rootElementName.toLowerCase()}' was rendered instead`, 'This may cause hydration issues in an SSR context, for example in a Next.js app');\n      }\n    }, [rootElementNameProp, rootElementName, componentName]);\n  }\n  const updateRootElementName = React.useCallback(instance => {\n    var _instance$tagName;\n    setRootElementName((_instance$tagName = instance == null ? void 0 : instance.tagName) != null ? _instance$tagName : '');\n  }, []);\n  return [rootElementName, updateRootElementName];\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,kBAAkBA,CAACC,UAAU,EAAE;EAC7C,MAAM;IACJC,eAAe,EAAEC,mBAAmB,GAAG,EAAE;IACzCC;EACF,CAAC,GAAGH,UAAU;EACd,MAAM,CAACC,eAAe,EAAEG,kBAAkB,CAAC,GAAGN,KAAK,CAACO,QAAQ,CAACH,mBAAmB,CAACI,WAAW,CAAC,CAAC,CAAC;EAC/F,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACAX,KAAK,CAACY,SAAS,CAAC,MAAM;MACpB,IAAIR,mBAAmB,IAAID,eAAe,KAAKC,mBAAmB,CAACI,WAAW,CAAC,CAAC,EAAE;QAChFK,OAAO,CAACC,KAAK,CAAC,uDAAuDT,aAAa,GAAG,OAAOA,aAAa,YAAY,GAAG,aAAa,kBAAkBD,mBAAmB,qBAAqBD,eAAe,CAACY,WAAW,CAAC,CAAC,wBAAwB,EAAE,iFAAiF,CAAC;MAC1U;IACF,CAAC,EAAE,CAACX,mBAAmB,EAAED,eAAe,EAAEE,aAAa,CAAC,CAAC;EAC3D;EACA,MAAMW,qBAAqB,GAAGhB,KAAK,CAACiB,WAAW,CAACC,QAAQ,IAAI;IAC1D,IAAIC,iBAAiB;IACrBb,kBAAkB,CAAC,CAACa,iBAAiB,GAAGD,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACE,OAAO,KAAK,IAAI,GAAGD,iBAAiB,GAAG,EAAE,CAAC;EACzH,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,CAAChB,eAAe,EAAEa,qBAAqB,CAAC;AACjD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}