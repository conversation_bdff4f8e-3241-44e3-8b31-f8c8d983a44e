{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getStepUtilityClass(slot) {\n  return generateUtilityClass('MuiStep', slot);\n}\nconst stepClasses = generateUtilityClasses('MuiStep', ['root', 'indicator', 'horizontal', 'vertical', 'active', 'completed', 'disabled']);\nexport default stepClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getStepUtilityClass", "slot", "stepClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Step/stepClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getStepUtilityClass(slot) {\n  return generateUtilityClass('MuiStep', slot);\n}\nconst stepClasses = generateUtilityClasses('MuiStep', ['root', 'indicator', 'horizontal', 'vertical', 'active', 'completed', 'disabled']);\nexport default stepClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,mBAAmBA,CAACC,IAAI,EAAE;EACxC,OAAOH,oBAAoB,CAAC,SAAS,EAAEG,IAAI,CAAC;AAC9C;AACA,MAAMC,WAAW,GAAGH,sBAAsB,CAAC,SAAS,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE,QAAQ,EAAE,WAAW,EAAE,UAAU,CAAC,CAAC;AACzI,eAAeG,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}