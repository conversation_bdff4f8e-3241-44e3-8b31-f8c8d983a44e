{"ast": null, "code": "import React from'react';import{Box,Typography,Card,Avatar,LinearProgress}from'@mui/material';import{Link}from\"react-router-dom\";import dayjs from'dayjs';import Iconify from'components/Iconify/index';import{mainYellowColor}from\"helpers/constants\";import styles from\"../styles.module.scss\";// Status constants\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const STATUS={NOT_STARTED:1,IN_PROGRESS:2,COMPLETED:3};const PlanCard=_ref=>{var _plan$milestones,_plan$milestones2,_plan$milestones3,_plan$milestones4,_plan$milestones5,_plan$user_access_lev,_plan$user_access_lev2;let{plan,currentUser,index}=_ref;// Calculate subtask progress\nconst calculateSubtaskProgress=subtask=>{// Kiểm tra subtask tồn tại\nif(!subtask){return 0;}// If progress is explicitly set, use it\nif(subtask.progress!==undefined&&subtask.progress!==null){return subtask.progress;}// Otherwise, determine based on status\nswitch(subtask.status){case STATUS.COMPLETED:return 100;case STATUS.IN_PROGRESS:return 50;default:return 0;}};// Calculate task progress based on subtasks\nconst calculateTaskProgress=task=>{// Kiểm tra task tồn tại\nif(!task){return 0;}// Đảm bảo subtasks là một mảng\nconst subtasks=task.subtasks||[];// If no subtasks, base on task status\nif(subtasks.length===0){switch(task.status){case STATUS.COMPLETED:return 100;case STATUS.IN_PROGRESS:return 50;default:return 0;}}// Calculate progress based on subtasks\nlet totalProgress=0;subtasks.forEach(subtask=>{totalProgress+=calculateSubtaskProgress(subtask);});return Math.floor(totalProgress/subtasks.length);};// Calculate milestone progress based on tasks\nconst calculateMilestoneProgress=milestone=>{// Kiểm tra milestone tồn tại\nif(!milestone){return 0;}// Đảm bảo tasks là một mảng\nconst tasks=milestone.tasks||[];// If no tasks, return 0\nif(tasks.length===0){return 0;}// Calculate progress based on tasks\nlet totalProgress=0;tasks.forEach(task=>{// Đảm bảo task tồn tại và có thuộc tính cần thiết\nif(task){totalProgress+=calculateTaskProgress(task);}});return Math.floor(totalProgress/tasks.length);};// Calculate overall plan progress\nconst calculatePlanProgress=()=>{if(!plan||!plan.milestones||plan.milestones.length===0){return 0;}let totalProgress=0;plan.milestones.forEach(milestone=>{totalProgress+=calculateMilestoneProgress(milestone);});return Math.floor(totalProgress/plan.milestones.length);};// Calculate overview information from plan data\nconst totalMilestones=((_plan$milestones=plan.milestones)===null||_plan$milestones===void 0?void 0:_plan$milestones.length)||0;// Calculate total tasks from all milestones\nconst totalTasks=((_plan$milestones2=plan.milestones)===null||_plan$milestones2===void 0?void 0:_plan$milestones2.reduce((sum,milestone)=>{var _milestone$tasks;return sum+(((_milestone$tasks=milestone.tasks)===null||_milestone$tasks===void 0?void 0:_milestone$tasks.length)||0);},0))||0;// Calculate total subtasks from all tasks\nconst totalSubtasks=((_plan$milestones3=plan.milestones)===null||_plan$milestones3===void 0?void 0:_plan$milestones3.reduce((sum,milestone)=>{var _milestone$tasks2;return sum+((_milestone$tasks2=milestone.tasks)===null||_milestone$tasks2===void 0?void 0:_milestone$tasks2.reduce((taskSum,task)=>{var _task$subtasks;return taskSum+(((_task$subtasks=task.subtasks)===null||_task$subtasks===void 0?void 0:_task$subtasks.length)||0);},0));},0))||0;// Calculate progress and task status counts\nconst progress=calculatePlanProgress();// Calculate completed, in progress, and not started tasks\nlet completedTasks=0;let inProgressTasks=0;let notStartedTasks=0;(_plan$milestones4=plan.milestones)===null||_plan$milestones4===void 0?void 0:_plan$milestones4.forEach(milestone=>{var _milestone$tasks3;(_milestone$tasks3=milestone.tasks)===null||_milestone$tasks3===void 0?void 0:_milestone$tasks3.forEach(task=>{const taskProgress=calculateTaskProgress(task);if(taskProgress===100){completedTasks++;}else if(taskProgress>0){inProgressTasks++;}else{notStartedTasks++;}});});// Determine plan status\nlet status={color:'#4CAF50',label:'Active'};if(progress===100){status={color:'#2196F3',label:'Completed'};}else if(progress===0){status={color:'#FFC107',label:'Not Started'};}else if(progress>0){status={color:'#FF9800',label:'In Progress'};}// Create tags from plan information\nconst tags=[];if(plan.start_date&&plan.end_date){const duration=dayjs(plan.end_date).diff(dayjs(plan.start_date),'day');tags.push(`${duration} days`);}if(totalMilestones>0){tags.push(`${totalMilestones} milestones`);}// Check if there are assignees\nconst hasAssignees=(_plan$milestones5=plan.milestones)===null||_plan$milestones5===void 0?void 0:_plan$milestones5.some(milestone=>{var _milestone$tasks4;return(_milestone$tasks4=milestone.tasks)===null||_milestone$tasks4===void 0?void 0:_milestone$tasks4.some(task=>{var _task$assignees;return((_task$assignees=task.assignees)===null||_task$assignees===void 0?void 0:_task$assignees.length)>0;});});if(hasAssignees){tags.push('Has assignees');}return/*#__PURE__*/_jsxs(Card,{className:styles.planCard,sx:{position:'relative'},children:[/*#__PURE__*/_jsx(Box,{className:styles.statusIndicator,sx:{backgroundColor:status.color},children:/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{fontFamily:'\"Recursive Variable\", sans-serif'},children:status.label})}),/*#__PURE__*/_jsxs(Link,{to:`/d/plan/${plan.slug}`,className:styles.planCardLink,children:[/*#__PURE__*/_jsxs(Box,{className:styles.cardHeader,children:[/*#__PURE__*/_jsx(Box,{className:styles.cardIcon,children:/*#__PURE__*/_jsx(Iconify,{icon:\"codicon:project\",width:24,height:24,color:mainYellowColor})}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",className:styles.planName,noWrap:true,sx:{fontFamily:'\"Recursive Variable\", sans-serif',color:'#333',fontSize:'1.2rem',fontWeight:700,margin:0,flex:1,overflow:'hidden',textOverflow:'ellipsis'},children:plan.name})]}),/*#__PURE__*/_jsx(Box,{className:styles.accessLevelSection,children:/*#__PURE__*/_jsxs(Box,{className:styles.accessBadge,children:[/*#__PURE__*/_jsx(Iconify,{icon:\"material-symbols:security\",width:16,height:16,color:mainYellowColor}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{fontFamily:'\"Recursive Variable\", sans-serif',fontSize:'0.75rem',fontWeight:600,color:'#333'},children:(_plan$user_access_lev=plan.user_access_level)!==null&&_plan$user_access_lev!==void 0&&_plan$user_access_lev.is_head_owner?'Head Owner':(_plan$user_access_lev2=plan.user_access_level)!==null&&_plan$user_access_lev2!==void 0&&_plan$user_access_lev2.access_level?plan.user_access_level.access_level.charAt(0).toUpperCase()+plan.user_access_level.access_level.slice(1):plan.user.id===currentUser.id?'Owner':'Shared'})]})}),/*#__PURE__*/_jsx(Box,{className:styles.cardMeta,children:(plan.start_date||plan.end_date)&&/*#__PURE__*/_jsxs(Box,{className:styles.metaItem,children:[/*#__PURE__*/_jsx(Iconify,{icon:\"carbon:calendar\",width:16,height:16,color:mainYellowColor}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",children:[/*#__PURE__*/_jsx(\"span\",{className:styles.metaLabel,children:\"Timeline: \"}),plan.start_date?dayjs(plan.start_date).format('MM/DD/YYYY'):'Not started',plan.start_date&&plan.end_date&&\" ~ \",plan.end_date?dayjs(plan.end_date).format('MM/DD/YYYY'):'No end date']})]})}),/*#__PURE__*/_jsxs(Box,{className:styles.planStructure,children:[/*#__PURE__*/_jsxs(Box,{className:styles.structureItem,children:[/*#__PURE__*/_jsx(Box,{className:styles.structureIcon,sx:{backgroundColor:'#E3F2FD'},children:/*#__PURE__*/_jsx(Iconify,{icon:\"mdi:flag-variant\",width:16,height:16,color:\"#2196F3\"})}),/*#__PURE__*/_jsxs(Box,{className:styles.structureInfo,children:[/*#__PURE__*/_jsx(Typography,{variant:\"caption\",className:styles.structureLabel,sx:{fontSize:'0.7rem',color:'#666',fontFamily:'\"Recursive Variable\", sans-serif'},children:\"Milestones\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",className:styles.structureValue,sx:{fontWeight:700,fontSize:'1.2rem',color:'#333',fontFamily:'\"Recursive Variable\", sans-serif',margin:0,lineHeight:1.2},children:totalMilestones})]})]}),/*#__PURE__*/_jsxs(Box,{className:styles.structureItem,children:[/*#__PURE__*/_jsx(Box,{className:styles.structureIcon,sx:{backgroundColor:'#FFF8E1'},children:/*#__PURE__*/_jsx(Iconify,{icon:\"mdi:checkbox-marked-outline\",width:16,height:16,color:\"#FFA000\"})}),/*#__PURE__*/_jsxs(Box,{className:styles.structureInfo,children:[/*#__PURE__*/_jsx(Typography,{variant:\"caption\",className:styles.structureLabel,sx:{fontSize:'0.7rem',color:'#666',fontFamily:'\"Recursive Variable\", sans-serif'},children:\"Tasks\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",className:styles.structureValue,sx:{fontWeight:700,fontSize:'1.2rem',color:'#333',fontFamily:'\"Recursive Variable\", sans-serif',margin:0,lineHeight:1.2},children:totalTasks})]})]}),/*#__PURE__*/_jsxs(Box,{className:styles.structureItem,children:[/*#__PURE__*/_jsx(Box,{className:styles.structureIcon,sx:{backgroundColor:'#E8F5E9'},children:/*#__PURE__*/_jsx(Iconify,{icon:\"mdi:format-list-checks\",width:16,height:16,color:\"#4CAF50\"})}),/*#__PURE__*/_jsxs(Box,{className:styles.structureInfo,children:[/*#__PURE__*/_jsx(Typography,{variant:\"caption\",className:styles.structureLabel,sx:{fontSize:'0.7rem',color:'#666',fontFamily:'\"Recursive Variable\", sans-serif'},children:\"Subtasks\"}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",className:styles.structureValue,sx:{fontWeight:700,fontSize:'1.2rem',color:'#333',fontFamily:'\"Recursive Variable\", sans-serif',margin:0,lineHeight:1.2},children:totalSubtasks})]})]})]}),/*#__PURE__*/_jsxs(Box,{className:styles.progressSection,children:[/*#__PURE__*/_jsxs(Box,{className:styles.progressHeader,children:[/*#__PURE__*/_jsx(Typography,{variant:\"caption\",color:\"textSecondary\",sx:{fontFamily:'\"Recursive Variable\", sans-serif',fontSize:'0.7rem',color:'#666'},children:\"Progress\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"h6\",className:styles.progressValue,sx:{fontWeight:700,fontSize:'1.2rem',color:'#333',fontFamily:'\"Recursive Variable\", sans-serif',margin:0,lineHeight:1.2},children:[progress,\"%\"]})]}),/*#__PURE__*/_jsx(LinearProgress,{variant:\"determinate\",value:progress,sx:{height:8,borderRadius:4,backgroundColor:'rgba(0, 0, 0, 0.05)','& .MuiLinearProgress-bar':{borderRadius:4,backgroundColor:progress===100?'#4CAF50':mainYellowColor}}}),/*#__PURE__*/_jsxs(Box,{className:styles.taskStatusLegend,children:[/*#__PURE__*/_jsxs(Box,{className:styles.legendItem,children:[/*#__PURE__*/_jsx(Box,{className:styles.legendColor,sx:{backgroundColor:'#4CAF50'}}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",className:styles.legendText,sx:{fontFamily:'\"Recursive Variable\", sans-serif',fontSize:'0.75rem',color:'#666'},children:[completedTasks,\" completed\"]})]}),/*#__PURE__*/_jsxs(Box,{className:styles.legendItem,children:[/*#__PURE__*/_jsx(Box,{className:styles.legendColor,sx:{backgroundColor:'#FF9800'}}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",className:styles.legendText,sx:{fontFamily:'\"Recursive Variable\", sans-serif',fontSize:'0.75rem',color:'#666'},children:[inProgressTasks,\" in progress\"]})]}),/*#__PURE__*/_jsxs(Box,{className:styles.legendItem,children:[/*#__PURE__*/_jsx(Box,{className:styles.legendColor,sx:{backgroundColor:'#E0E0E0'}}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",className:styles.legendText,sx:{fontFamily:'\"Recursive Variable\", sans-serif',fontSize:'0.75rem',color:'#666'},children:[notStartedTasks,\" not started\"]})]})]})]}),/*#__PURE__*/_jsxs(Box,{className:styles.planCreator,sx:{borderTop:'none',display:'flex',alignItems:'center',gap:'8px',marginTop:'8px',paddingTop:'8px',justifyContent:'space-between',fontFamily:'\"Recursive Variable\", sans-serif',marginBottom:'8px'},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:1},children:[/*#__PURE__*/_jsx(Avatar,{src:plan.user.avatar||'',alt:`${plan.user.first_name} ${plan.user.last_name}`,className:styles.creatorAvatar,sx:{width:'28px',height:'28px'}}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"caption\",className:styles.creatorLabel,sx:{color:'#888',fontSize:'0.7rem',display:'block',fontFamily:'\"Recursive Variable\", sans-serif'},children:\"Created by\"}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",className:styles.creatorName,sx:{fontWeight:600,fontSize:'0.8rem',color:'#333',fontFamily:'\"Recursive Variable\", sans-serif'},children:[plan.user.first_name,\" \",plan.user.last_name]})]})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:1},children:[plan.user.id!==currentUser.id&&/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',gap:'4px',backgroundColor:'rgba(255, 99, 71, 0.9)',color:'white',padding:'4px 10px',borderRadius:'16px',fontSize:'0.75rem',fontWeight:500,boxShadow:'0 2px 4px rgba(0, 0, 0, 0.1)',marginRight:'8px'},children:[/*#__PURE__*/_jsx(Iconify,{icon:\"ph:share-network\",width:14,height:14}),/*#__PURE__*/_jsx(Typography,{variant:\"caption\",sx:{fontFamily:'\"Recursive Variable\", sans-serif',color:'white'},children:\"Shared\"})]}),/*#__PURE__*/_jsxs(Typography,{variant:\"caption\",className:styles.lastUpdated,sx:{color:'#888',fontSize:'0.7rem',display:'flex',alignItems:'center',gap:'4px',fontFamily:'\"Recursive Variable\", sans-serif'},children:[/*#__PURE__*/_jsx(Iconify,{icon:\"mdi:clock-outline\",width:14,height:14}),dayjs(plan.updated_at).fromNow()]})]})]})]})]},index);};export default PlanCard;", "map": {"version": 3, "names": ["React", "Box", "Typography", "Card", "Avatar", "LinearProgress", "Link", "dayjs", "Iconify", "mainYellowColor", "styles", "jsx", "_jsx", "jsxs", "_jsxs", "STATUS", "NOT_STARTED", "IN_PROGRESS", "COMPLETED", "PlanCard", "_ref", "_plan$milestones", "_plan$milestones2", "_plan$milestones3", "_plan$milestones4", "_plan$milestones5", "_plan$user_access_lev", "_plan$user_access_lev2", "plan", "currentUser", "index", "calculateSubtaskProgress", "subtask", "progress", "undefined", "status", "calculateTaskProgress", "task", "subtasks", "length", "totalProgress", "for<PERSON>ach", "Math", "floor", "calculateMilestoneProgress", "milestone", "tasks", "calculatePlanProgress", "milestones", "totalMilestones", "totalTasks", "reduce", "sum", "_milestone$tasks", "totalSubtasks", "_milestone$tasks2", "taskSum", "_task$subtasks", "completedTasks", "inProgressTasks", "notStartedTasks", "_milestone$tasks3", "taskProgress", "color", "label", "tags", "start_date", "end_date", "duration", "diff", "push", "hasAssignees", "some", "_milestone$tasks4", "_task$assignees", "assignees", "className", "planCard", "sx", "position", "children", "statusIndicator", "backgroundColor", "variant", "fontFamily", "to", "slug", "planCardLink", "<PERSON><PERSON><PERSON><PERSON>", "cardIcon", "icon", "width", "height", "planName", "noWrap", "fontSize", "fontWeight", "margin", "flex", "overflow", "textOverflow", "name", "accessLevelSection", "accessBadge", "user_access_level", "is_head_owner", "access_level", "char<PERSON>t", "toUpperCase", "slice", "user", "id", "cardMeta", "metaItem", "metaLabel", "format", "planStructure", "structureItem", "structureIcon", "structureInfo", "structureLabel", "structureValue", "lineHeight", "progressSection", "progressHeader", "progressValue", "value", "borderRadius", "taskStatusLegend", "legendItem", "legendColor", "legendText", "planCreator", "borderTop", "display", "alignItems", "gap", "marginTop", "paddingTop", "justifyContent", "marginBottom", "src", "avatar", "alt", "first_name", "last_name", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "padding", "boxShadow", "marginRight", "lastUpdated", "updated_at", "fromNow"], "sources": ["C:/ignition/ignition-ui/src/views/home/<USER>/PlanCard.js"], "sourcesContent": ["import React from 'react';\r\nimport { Box, Typography, Card, Avatar, LinearProgress } from '@mui/material';\r\nimport { Link } from \"react-router-dom\";\r\nimport dayjs from 'dayjs';\r\nimport Iconify from 'components/Iconify/index';\r\nimport { mainYellowColor } from \"helpers/constants\";\r\nimport styles from \"../styles.module.scss\";\r\n\r\n// Status constants\r\nconst STATUS = {\r\n  NOT_STARTED: 1,\r\n  IN_PROGRESS: 2,\r\n  COMPLETED: 3\r\n};\r\n\r\nconst PlanCard = ({ plan, currentUser, index }) => {\r\n  // Calculate subtask progress\r\n  const calculateSubtaskProgress = (subtask) => {\r\n    // Kiểm tra subtask tồn tại\r\n    if (!subtask) {\r\n      return 0;\r\n    }\r\n    \r\n    // If progress is explicitly set, use it\r\n    if (subtask.progress !== undefined && subtask.progress !== null) {\r\n      return subtask.progress;\r\n    }\r\n    \r\n    // Otherwise, determine based on status\r\n    switch (subtask.status) {\r\n      case STATUS.COMPLETED:\r\n        return 100;\r\n      case STATUS.IN_PROGRESS:\r\n        return 50;\r\n      default:\r\n        return 0;\r\n    }\r\n  };\r\n\r\n  // Calculate task progress based on subtasks\r\n  const calculateTaskProgress = (task) => {\r\n    // Ki<PERSON>m tra task tồn tại\r\n    if (!task) {\r\n      return 0;\r\n    }\r\n    \r\n    // Đ<PERSON>m bảo subtasks là một mảng\r\n    const subtasks = task.subtasks || [];\r\n    \r\n    // If no subtasks, base on task status\r\n    if (subtasks.length === 0) {\r\n      switch (task.status) {\r\n        case STATUS.COMPLETED:\r\n          return 100;\r\n        case STATUS.IN_PROGRESS:\r\n          return 50;\r\n        default:\r\n          return 0;\r\n      }\r\n    }\r\n    \r\n    // Calculate progress based on subtasks\r\n    let totalProgress = 0;\r\n    subtasks.forEach(subtask => {\r\n      totalProgress += calculateSubtaskProgress(subtask);\r\n    });\r\n    \r\n    return Math.floor(totalProgress / subtasks.length);\r\n  };\r\n\r\n  // Calculate milestone progress based on tasks\r\n  const calculateMilestoneProgress = (milestone) => {\r\n    // Kiểm tra milestone tồn tại\r\n    if (!milestone) {\r\n      return 0;\r\n    }\r\n    \r\n    // Đảm bảo tasks là một mảng\r\n    const tasks = milestone.tasks || [];\r\n    \r\n    // If no tasks, return 0\r\n    if (tasks.length === 0) {\r\n      return 0;\r\n    }\r\n    \r\n    // Calculate progress based on tasks\r\n    let totalProgress = 0;\r\n    tasks.forEach(task => {\r\n      // Đảm bảo task tồn tại và có thuộc tính cần thiết\r\n      if (task) {\r\n        totalProgress += calculateTaskProgress(task);\r\n      }\r\n    });\r\n    \r\n    return Math.floor(totalProgress / tasks.length);\r\n  };\r\n\r\n  // Calculate overall plan progress\r\n  const calculatePlanProgress = () => {\r\n    if (!plan || !plan.milestones || plan.milestones.length === 0) {\r\n      return 0;\r\n    }\r\n    \r\n    let totalProgress = 0;\r\n    plan.milestones.forEach(milestone => {\r\n      totalProgress += calculateMilestoneProgress(milestone);\r\n    });\r\n    \r\n    return Math.floor(totalProgress / plan.milestones.length);\r\n  };\r\n\r\n  // Calculate overview information from plan data\r\n  const totalMilestones = plan.milestones?.length || 0;\r\n\r\n  // Calculate total tasks from all milestones\r\n  const totalTasks = plan.milestones?.reduce((sum, milestone) =>\r\n    sum + (milestone.tasks?.length || 0), 0) || 0;\r\n\r\n  // Calculate total subtasks from all tasks\r\n  const totalSubtasks = plan.milestones?.reduce((sum, milestone) =>\r\n    sum + milestone.tasks?.reduce((taskSum, task) =>\r\n      taskSum + (task.subtasks?.length || 0), 0), 0) || 0;\r\n\r\n  // Calculate progress and task status counts\r\n  const progress = calculatePlanProgress();\r\n  \r\n  // Calculate completed, in progress, and not started tasks\r\n  let completedTasks = 0;\r\n  let inProgressTasks = 0;\r\n  let notStartedTasks = 0;\r\n\r\n  plan.milestones?.forEach(milestone => {\r\n    milestone.tasks?.forEach(task => {\r\n      const taskProgress = calculateTaskProgress(task);\r\n      if (taskProgress === 100) {\r\n        completedTasks++;\r\n      } else if (taskProgress > 0) {\r\n        inProgressTasks++;\r\n      } else {\r\n        notStartedTasks++;\r\n      }\r\n    });\r\n  });\r\n\r\n  // Determine plan status\r\n  let status = { color: '#4CAF50', label: 'Active' };\r\n  if (progress === 100) {\r\n    status = { color: '#2196F3', label: 'Completed' };\r\n  } else if (progress === 0) {\r\n    status = { color: '#FFC107', label: 'Not Started' };\r\n  } else if (progress > 0) {\r\n    status = { color: '#FF9800', label: 'In Progress' };\r\n  }\r\n\r\n  // Create tags from plan information\r\n  const tags = [];\r\n  if (plan.start_date && plan.end_date) {\r\n    const duration = dayjs(plan.end_date).diff(dayjs(plan.start_date), 'day');\r\n    tags.push(`${duration} days`);\r\n  }\r\n\r\n  if (totalMilestones > 0) {\r\n    tags.push(`${totalMilestones} milestones`);\r\n  }\r\n\r\n  // Check if there are assignees\r\n  const hasAssignees = plan.milestones?.some(milestone =>\r\n    milestone.tasks?.some(task => task.assignees?.length > 0));\r\n\r\n  if (hasAssignees) {\r\n    tags.push('Has assignees');\r\n  }\r\n\r\n  return (\r\n    <Card className={styles.planCard} key={index} sx={{ position: 'relative' }}>\r\n      {/* Always show status indicator at top right */}\r\n      <Box \r\n        className={styles.statusIndicator} \r\n        sx={{ \r\n          backgroundColor: status.color\r\n        }}\r\n      >\r\n        <Typography \r\n          variant=\"caption\"\r\n          sx={{\r\n            fontFamily: '\"Recursive Variable\", sans-serif'\r\n          }}\r\n        >\r\n          {status.label}\r\n        </Typography>\r\n      </Box>\r\n      \r\n      <Link to={`/d/plan/${plan.slug}`} className={styles.planCardLink}>\r\n        <Box className={styles.cardHeader}>\r\n          <Box className={styles.cardIcon}>\r\n            <Iconify icon=\"codicon:project\" width={24} height={24} color={mainYellowColor} />\r\n          </Box>\r\n          <Typography\r\n            variant=\"h6\"\r\n            className={styles.planName}\r\n            noWrap\r\n            sx={{\r\n              fontFamily: '\"Recursive Variable\", sans-serif',\r\n              color: '#333',\r\n              fontSize: '1.2rem',\r\n              fontWeight: 700,\r\n              margin: 0,\r\n              flex: 1,\r\n              overflow: 'hidden',\r\n              textOverflow: 'ellipsis'\r\n            }}\r\n          >\r\n            {plan.name}\r\n          </Typography>\r\n        </Box>\r\n\r\n        {/* Access Level Badge */}\r\n        <Box className={styles.accessLevelSection}>\r\n          <Box className={styles.accessBadge}>\r\n            <Iconify icon=\"material-symbols:security\" width={16} height={16} color={mainYellowColor} />\r\n            <Typography\r\n              variant=\"caption\"\r\n              sx={{\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                fontSize: '0.75rem',\r\n                fontWeight: 600,\r\n                color: '#333'\r\n              }}\r\n            >\r\n              {plan.user_access_level?.is_head_owner\r\n                ? 'Head Owner'\r\n                : plan.user_access_level?.access_level\r\n                  ? plan.user_access_level.access_level.charAt(0).toUpperCase() + plan.user_access_level.access_level.slice(1)\r\n                  : (plan.user.id === currentUser.id ? 'Owner' : 'Shared')\r\n              }\r\n            </Typography>\r\n          </Box>\r\n        </Box>\r\n\r\n        <Box className={styles.cardMeta}>\r\n          {(plan.start_date || plan.end_date) && (\r\n            <Box className={styles.metaItem}>\r\n              <Iconify icon=\"carbon:calendar\" width={16} height={16} color={mainYellowColor} />\r\n              <Typography variant=\"caption\">\r\n                <span className={styles.metaLabel}>Timeline: </span>\r\n                {plan.start_date ? dayjs(plan.start_date).format('MM/DD/YYYY') : 'Not started'}\r\n                {plan.start_date && plan.end_date && \" ~ \"}\r\n                {plan.end_date ? dayjs(plan.end_date).format('MM/DD/YYYY') : 'No end date'}\r\n              </Typography>\r\n            </Box>\r\n          )}\r\n        </Box>\r\n\r\n        {/* Display plan structure overview */}\r\n        <Box className={styles.planStructure}>\r\n          <Box className={styles.structureItem}>\r\n            <Box className={styles.structureIcon} sx={{ backgroundColor: '#E3F2FD' }}>\r\n              <Iconify icon=\"mdi:flag-variant\" width={16} height={16} color=\"#2196F3\" />\r\n            </Box>\r\n            <Box className={styles.structureInfo}>\r\n              <Typography\r\n                variant=\"caption\"\r\n                className={styles.structureLabel}\r\n                sx={{\r\n                  fontSize: '0.7rem',\r\n                  color: '#666',\r\n                  fontFamily: '\"Recursive Variable\", sans-serif'\r\n                }}\r\n              >\r\n                Milestones\r\n              </Typography>\r\n              <Typography\r\n                variant=\"h6\"\r\n                className={styles.structureValue}\r\n                sx={{\r\n                  fontWeight: 700,\r\n                  fontSize: '1.2rem',\r\n                  color: '#333',\r\n                  fontFamily: '\"Recursive Variable\", sans-serif',\r\n                  margin: 0,\r\n                  lineHeight: 1.2\r\n                }}\r\n              >\r\n                {totalMilestones}\r\n              </Typography>\r\n            </Box>\r\n          </Box>\r\n\r\n          <Box className={styles.structureItem}>\r\n            <Box className={styles.structureIcon} sx={{ backgroundColor: '#FFF8E1' }}>\r\n              <Iconify icon=\"mdi:checkbox-marked-outline\" width={16} height={16} color=\"#FFA000\" />\r\n            </Box>\r\n            <Box className={styles.structureInfo}>\r\n              <Typography\r\n                variant=\"caption\"\r\n                className={styles.structureLabel}\r\n                sx={{\r\n                  fontSize: '0.7rem',\r\n                  color: '#666',\r\n                  fontFamily: '\"Recursive Variable\", sans-serif'\r\n                }}\r\n              >\r\n                Tasks\r\n              </Typography>\r\n              <Typography\r\n                variant=\"h6\"\r\n                className={styles.structureValue}\r\n                sx={{\r\n                  fontWeight: 700,\r\n                  fontSize: '1.2rem',\r\n                  color: '#333',\r\n                  fontFamily: '\"Recursive Variable\", sans-serif',\r\n                  margin: 0,\r\n                  lineHeight: 1.2\r\n                }}\r\n              >\r\n                {totalTasks}\r\n              </Typography>\r\n            </Box>\r\n          </Box>\r\n\r\n          <Box className={styles.structureItem}>\r\n            <Box className={styles.structureIcon} sx={{ backgroundColor: '#E8F5E9' }}>\r\n              <Iconify icon=\"mdi:format-list-checks\" width={16} height={16} color=\"#4CAF50\" />\r\n            </Box>\r\n            <Box className={styles.structureInfo}>\r\n              <Typography\r\n                variant=\"caption\"\r\n                className={styles.structureLabel}\r\n                sx={{\r\n                  fontSize: '0.7rem',\r\n                  color: '#666',\r\n                  fontFamily: '\"Recursive Variable\", sans-serif'\r\n                }}\r\n              >\r\n                Subtasks\r\n              </Typography>\r\n              <Typography\r\n                variant=\"h6\"\r\n                className={styles.structureValue}\r\n                sx={{\r\n                  fontWeight: 700,\r\n                  fontSize: '1.2rem',\r\n                  color: '#333',\r\n                  fontFamily: '\"Recursive Variable\", sans-serif',\r\n                  margin: 0,\r\n                  lineHeight: 1.2\r\n                }}\r\n              >\r\n                {totalSubtasks}\r\n              </Typography>\r\n            </Box>\r\n          </Box>\r\n        </Box>\r\n\r\n        {/* Display progress */}\r\n        <Box className={styles.progressSection}>\r\n          <Box className={styles.progressHeader}>\r\n            <Typography\r\n              variant=\"caption\"\r\n              color=\"textSecondary\"\r\n              sx={{\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                fontSize: '0.7rem',\r\n                color: '#666'\r\n              }}\r\n            >\r\n              Progress\r\n            </Typography>\r\n            <Typography\r\n              variant=\"h6\"\r\n              className={styles.progressValue}\r\n              sx={{\r\n                fontWeight: 700,\r\n                fontSize: '1.2rem',\r\n                color: '#333',\r\n                fontFamily: '\"Recursive Variable\", sans-serif',\r\n                margin: 0,\r\n                lineHeight: 1.2\r\n              }}\r\n            >\r\n              {progress}%\r\n            </Typography>\r\n          </Box>\r\n          <LinearProgress \r\n            variant=\"determinate\" \r\n            value={progress} \r\n            sx={{\r\n              height: 8,\r\n              borderRadius: 4,\r\n              backgroundColor: 'rgba(0, 0, 0, 0.05)',\r\n              '& .MuiLinearProgress-bar': {\r\n                borderRadius: 4,\r\n                backgroundColor: progress === 100 ? '#4CAF50' : mainYellowColor,\r\n              }\r\n            }}\r\n          />\r\n\r\n          {/* Display task status distribution */}\r\n          <Box className={styles.taskStatusLegend}>\r\n            <Box className={styles.legendItem}>\r\n              <Box className={styles.legendColor} sx={{ backgroundColor: '#4CAF50' }} />\r\n              <Typography\r\n                variant=\"caption\"\r\n                className={styles.legendText}\r\n                sx={{\r\n                  fontFamily: '\"Recursive Variable\", sans-serif',\r\n                  fontSize: '0.75rem',\r\n                  color: '#666'\r\n                }}\r\n              >\r\n                {completedTasks} completed\r\n              </Typography>\r\n            </Box>\r\n            <Box className={styles.legendItem}>\r\n              <Box className={styles.legendColor} sx={{ backgroundColor: '#FF9800' }} />\r\n              <Typography\r\n                variant=\"caption\"\r\n                className={styles.legendText}\r\n                sx={{\r\n                  fontFamily: '\"Recursive Variable\", sans-serif',\r\n                  fontSize: '0.75rem',\r\n                  color: '#666'\r\n                }}\r\n              >\r\n                {inProgressTasks} in progress\r\n              </Typography>\r\n            </Box>\r\n            <Box className={styles.legendItem}>\r\n              <Box className={styles.legendColor} sx={{ backgroundColor: '#E0E0E0' }} />\r\n              <Typography\r\n                variant=\"caption\"\r\n                className={styles.legendText}\r\n                sx={{\r\n                  fontFamily: '\"Recursive Variable\", sans-serif',\r\n                  fontSize: '0.75rem',\r\n                  color: '#666'\r\n                }}\r\n              >\r\n                {notStartedTasks} not started\r\n              </Typography>\r\n            </Box>\r\n          </Box>\r\n        </Box>\r\n\r\n        {/* Display plan creator and creation time */}\r\n        <Box \r\n          className={styles.planCreator} \r\n          sx={{ \r\n            borderTop: 'none',\r\n            display: 'flex',\r\n            alignItems: 'center',\r\n            gap: '8px',\r\n            marginTop: '8px',\r\n            paddingTop: '8px',\r\n            justifyContent: 'space-between',\r\n            fontFamily: '\"Recursive Variable\", sans-serif',\r\n            marginBottom: '8px'\r\n          }}\r\n        >\r\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n            <Avatar\r\n              src={plan.user.avatar || ''}\r\n              alt={`${plan.user.first_name} ${plan.user.last_name}`}\r\n              className={styles.creatorAvatar}\r\n              sx={{ width: '28px', height: '28px' }}\r\n            />\r\n            <Box>\r\n              <Typography\r\n                variant=\"caption\"\r\n                className={styles.creatorLabel}\r\n                sx={{\r\n                  color: '#888',\r\n                  fontSize: '0.7rem',\r\n                  display: 'block',\r\n                  fontFamily: '\"Recursive Variable\", sans-serif'\r\n                }}\r\n              >\r\n                Created by\r\n              </Typography>\r\n              <Typography\r\n                variant=\"caption\"\r\n                className={styles.creatorName}\r\n                sx={{\r\n                  fontWeight: 600,\r\n                  fontSize: '0.8rem',\r\n                  color: '#333',\r\n                  fontFamily: '\"Recursive Variable\", sans-serif'\r\n                }}\r\n              >\r\n                {plan.user.first_name} {plan.user.last_name}\r\n              </Typography>\r\n            </Box>\r\n          </Box>\r\n\r\n          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\r\n            {plan.user.id !== currentUser.id && (\r\n              <Box \r\n                sx={{ \r\n                  display: 'flex',\r\n                  alignItems: 'center',\r\n                  gap: '4px',\r\n                  backgroundColor: 'rgba(255, 99, 71, 0.9)',\r\n                  color: 'white',\r\n                  padding: '4px 10px',\r\n                  borderRadius: '16px',\r\n                  fontSize: '0.75rem',\r\n                  fontWeight: 500,\r\n                  boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',\r\n                  marginRight: '8px'\r\n                }}\r\n              >\r\n                <Iconify icon=\"ph:share-network\" width={14} height={14} />\r\n                <Typography \r\n                  variant=\"caption\"\r\n                  sx={{\r\n                    fontFamily: '\"Recursive Variable\", sans-serif',\r\n                    color: 'white'\r\n                  }}\r\n                >\r\n                  Shared\r\n                </Typography>\r\n              </Box>\r\n            )}\r\n            <Typography\r\n              variant=\"caption\"\r\n              className={styles.lastUpdated}\r\n              sx={{\r\n                color: '#888',\r\n                fontSize: '0.7rem',\r\n                display: 'flex',\r\n                alignItems: 'center',\r\n                gap: '4px',\r\n                fontFamily: '\"Recursive Variable\", sans-serif'\r\n              }}\r\n            >\r\n              <Iconify icon=\"mdi:clock-outline\" width={14} height={14} />\r\n              {dayjs(plan.updated_at).fromNow()}\r\n            </Typography>\r\n          </Box>\r\n        </Box>\r\n      </Link>\r\n    </Card>\r\n  );\r\n};\r\n\r\nexport default PlanCard;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,GAAG,CAAEC,UAAU,CAAEC,IAAI,CAAEC,MAAM,CAAEC,cAAc,KAAQ,eAAe,CAC7E,OAASC,IAAI,KAAQ,kBAAkB,CACvC,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,OAAO,KAAM,0BAA0B,CAC9C,OAASC,eAAe,KAAQ,mBAAmB,CACnD,MAAO,CAAAC,MAAM,KAAM,uBAAuB,CAE1C;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,MAAM,CAAG,CACbC,WAAW,CAAE,CAAC,CACdC,WAAW,CAAE,CAAC,CACdC,SAAS,CAAE,CACb,CAAC,CAED,KAAM,CAAAC,QAAQ,CAAGC,IAAA,EAAkC,KAAAC,gBAAA,CAAAC,iBAAA,CAAAC,iBAAA,CAAAC,iBAAA,CAAAC,iBAAA,CAAAC,qBAAA,CAAAC,sBAAA,IAAjC,CAAEC,IAAI,CAAEC,WAAW,CAAEC,KAAM,CAAC,CAAAV,IAAA,CAC5C;AACA,KAAM,CAAAW,wBAAwB,CAAIC,OAAO,EAAK,CAC5C;AACA,GAAI,CAACA,OAAO,CAAE,CACZ,MAAO,EAAC,CACV,CAEA;AACA,GAAIA,OAAO,CAACC,QAAQ,GAAKC,SAAS,EAAIF,OAAO,CAACC,QAAQ,GAAK,IAAI,CAAE,CAC/D,MAAO,CAAAD,OAAO,CAACC,QAAQ,CACzB,CAEA;AACA,OAAQD,OAAO,CAACG,MAAM,EACpB,IAAK,CAAApB,MAAM,CAACG,SAAS,CACnB,MAAO,IAAG,CACZ,IAAK,CAAAH,MAAM,CAACE,WAAW,CACrB,MAAO,GAAE,CACX,QACE,MAAO,EAAC,CACZ,CACF,CAAC,CAED;AACA,KAAM,CAAAmB,qBAAqB,CAAIC,IAAI,EAAK,CACtC;AACA,GAAI,CAACA,IAAI,CAAE,CACT,MAAO,EAAC,CACV,CAEA;AACA,KAAM,CAAAC,QAAQ,CAAGD,IAAI,CAACC,QAAQ,EAAI,EAAE,CAEpC;AACA,GAAIA,QAAQ,CAACC,MAAM,GAAK,CAAC,CAAE,CACzB,OAAQF,IAAI,CAACF,MAAM,EACjB,IAAK,CAAApB,MAAM,CAACG,SAAS,CACnB,MAAO,IAAG,CACZ,IAAK,CAAAH,MAAM,CAACE,WAAW,CACrB,MAAO,GAAE,CACX,QACE,MAAO,EAAC,CACZ,CACF,CAEA;AACA,GAAI,CAAAuB,aAAa,CAAG,CAAC,CACrBF,QAAQ,CAACG,OAAO,CAACT,OAAO,EAAI,CAC1BQ,aAAa,EAAIT,wBAAwB,CAACC,OAAO,CAAC,CACpD,CAAC,CAAC,CAEF,MAAO,CAAAU,IAAI,CAACC,KAAK,CAACH,aAAa,CAAGF,QAAQ,CAACC,MAAM,CAAC,CACpD,CAAC,CAED;AACA,KAAM,CAAAK,0BAA0B,CAAIC,SAAS,EAAK,CAChD;AACA,GAAI,CAACA,SAAS,CAAE,CACd,MAAO,EAAC,CACV,CAEA;AACA,KAAM,CAAAC,KAAK,CAAGD,SAAS,CAACC,KAAK,EAAI,EAAE,CAEnC;AACA,GAAIA,KAAK,CAACP,MAAM,GAAK,CAAC,CAAE,CACtB,MAAO,EAAC,CACV,CAEA;AACA,GAAI,CAAAC,aAAa,CAAG,CAAC,CACrBM,KAAK,CAACL,OAAO,CAACJ,IAAI,EAAI,CACpB;AACA,GAAIA,IAAI,CAAE,CACRG,aAAa,EAAIJ,qBAAqB,CAACC,IAAI,CAAC,CAC9C,CACF,CAAC,CAAC,CAEF,MAAO,CAAAK,IAAI,CAACC,KAAK,CAACH,aAAa,CAAGM,KAAK,CAACP,MAAM,CAAC,CACjD,CAAC,CAED;AACA,KAAM,CAAAQ,qBAAqB,CAAGA,CAAA,GAAM,CAClC,GAAI,CAACnB,IAAI,EAAI,CAACA,IAAI,CAACoB,UAAU,EAAIpB,IAAI,CAACoB,UAAU,CAACT,MAAM,GAAK,CAAC,CAAE,CAC7D,MAAO,EAAC,CACV,CAEA,GAAI,CAAAC,aAAa,CAAG,CAAC,CACrBZ,IAAI,CAACoB,UAAU,CAACP,OAAO,CAACI,SAAS,EAAI,CACnCL,aAAa,EAAII,0BAA0B,CAACC,SAAS,CAAC,CACxD,CAAC,CAAC,CAEF,MAAO,CAAAH,IAAI,CAACC,KAAK,CAACH,aAAa,CAAGZ,IAAI,CAACoB,UAAU,CAACT,MAAM,CAAC,CAC3D,CAAC,CAED;AACA,KAAM,CAAAU,eAAe,CAAG,EAAA5B,gBAAA,CAAAO,IAAI,CAACoB,UAAU,UAAA3B,gBAAA,iBAAfA,gBAAA,CAAiBkB,MAAM,GAAI,CAAC,CAEpD;AACA,KAAM,CAAAW,UAAU,CAAG,EAAA5B,iBAAA,CAAAM,IAAI,CAACoB,UAAU,UAAA1B,iBAAA,iBAAfA,iBAAA,CAAiB6B,MAAM,CAAC,CAACC,GAAG,CAAEP,SAAS,QAAAQ,gBAAA,OACxD,CAAAD,GAAG,EAAI,EAAAC,gBAAA,CAAAR,SAAS,CAACC,KAAK,UAAAO,gBAAA,iBAAfA,gBAAA,CAAiBd,MAAM,GAAI,CAAC,CAAC,GAAE,CAAC,CAAC,GAAI,CAAC,CAE/C;AACA,KAAM,CAAAe,aAAa,CAAG,EAAA/B,iBAAA,CAAAK,IAAI,CAACoB,UAAU,UAAAzB,iBAAA,iBAAfA,iBAAA,CAAiB4B,MAAM,CAAC,CAACC,GAAG,CAAEP,SAAS,QAAAU,iBAAA,OAC3D,CAAAH,GAAG,GAAAG,iBAAA,CAAGV,SAAS,CAACC,KAAK,UAAAS,iBAAA,iBAAfA,iBAAA,CAAiBJ,MAAM,CAAC,CAACK,OAAO,CAAEnB,IAAI,QAAAoB,cAAA,OAC1C,CAAAD,OAAO,EAAI,EAAAC,cAAA,CAAApB,IAAI,CAACC,QAAQ,UAAAmB,cAAA,iBAAbA,cAAA,CAAelB,MAAM,GAAI,CAAC,CAAC,GAAE,CAAC,CAAC,IAAE,CAAC,CAAC,GAAI,CAAC,CAEvD;AACA,KAAM,CAAAN,QAAQ,CAAGc,qBAAqB,CAAC,CAAC,CAExC;AACA,GAAI,CAAAW,cAAc,CAAG,CAAC,CACtB,GAAI,CAAAC,eAAe,CAAG,CAAC,CACvB,GAAI,CAAAC,eAAe,CAAG,CAAC,CAEvB,CAAApC,iBAAA,CAAAI,IAAI,CAACoB,UAAU,UAAAxB,iBAAA,iBAAfA,iBAAA,CAAiBiB,OAAO,CAACI,SAAS,EAAI,KAAAgB,iBAAA,CACpC,CAAAA,iBAAA,CAAAhB,SAAS,CAACC,KAAK,UAAAe,iBAAA,iBAAfA,iBAAA,CAAiBpB,OAAO,CAACJ,IAAI,EAAI,CAC/B,KAAM,CAAAyB,YAAY,CAAG1B,qBAAqB,CAACC,IAAI,CAAC,CAChD,GAAIyB,YAAY,GAAK,GAAG,CAAE,CACxBJ,cAAc,EAAE,CAClB,CAAC,IAAM,IAAII,YAAY,CAAG,CAAC,CAAE,CAC3BH,eAAe,EAAE,CACnB,CAAC,IAAM,CACLC,eAAe,EAAE,CACnB,CACF,CAAC,CAAC,CACJ,CAAC,CAAC,CAEF;AACA,GAAI,CAAAzB,MAAM,CAAG,CAAE4B,KAAK,CAAE,SAAS,CAAEC,KAAK,CAAE,QAAS,CAAC,CAClD,GAAI/B,QAAQ,GAAK,GAAG,CAAE,CACpBE,MAAM,CAAG,CAAE4B,KAAK,CAAE,SAAS,CAAEC,KAAK,CAAE,WAAY,CAAC,CACnD,CAAC,IAAM,IAAI/B,QAAQ,GAAK,CAAC,CAAE,CACzBE,MAAM,CAAG,CAAE4B,KAAK,CAAE,SAAS,CAAEC,KAAK,CAAE,aAAc,CAAC,CACrD,CAAC,IAAM,IAAI/B,QAAQ,CAAG,CAAC,CAAE,CACvBE,MAAM,CAAG,CAAE4B,KAAK,CAAE,SAAS,CAAEC,KAAK,CAAE,aAAc,CAAC,CACrD,CAEA;AACA,KAAM,CAAAC,IAAI,CAAG,EAAE,CACf,GAAIrC,IAAI,CAACsC,UAAU,EAAItC,IAAI,CAACuC,QAAQ,CAAE,CACpC,KAAM,CAAAC,QAAQ,CAAG7D,KAAK,CAACqB,IAAI,CAACuC,QAAQ,CAAC,CAACE,IAAI,CAAC9D,KAAK,CAACqB,IAAI,CAACsC,UAAU,CAAC,CAAE,KAAK,CAAC,CACzED,IAAI,CAACK,IAAI,CAAC,GAAGF,QAAQ,OAAO,CAAC,CAC/B,CAEA,GAAInB,eAAe,CAAG,CAAC,CAAE,CACvBgB,IAAI,CAACK,IAAI,CAAC,GAAGrB,eAAe,aAAa,CAAC,CAC5C,CAEA;AACA,KAAM,CAAAsB,YAAY,EAAA9C,iBAAA,CAAGG,IAAI,CAACoB,UAAU,UAAAvB,iBAAA,iBAAfA,iBAAA,CAAiB+C,IAAI,CAAC3B,SAAS,OAAA4B,iBAAA,QAAAA,iBAAA,CAClD5B,SAAS,CAACC,KAAK,UAAA2B,iBAAA,iBAAfA,iBAAA,CAAiBD,IAAI,CAACnC,IAAI,OAAAqC,eAAA,OAAI,EAAAA,eAAA,CAAArC,IAAI,CAACsC,SAAS,UAAAD,eAAA,iBAAdA,eAAA,CAAgBnC,MAAM,EAAG,CAAC,GAAC,GAAC,CAE5D,GAAIgC,YAAY,CAAE,CAChBN,IAAI,CAACK,IAAI,CAAC,eAAe,CAAC,CAC5B,CAEA,mBACExD,KAAA,CAACX,IAAI,EAACyE,SAAS,CAAElE,MAAM,CAACmE,QAAS,CAAaC,EAAE,CAAE,CAAEC,QAAQ,CAAE,UAAW,CAAE,CAAAC,QAAA,eAEzEpE,IAAA,CAACX,GAAG,EACF2E,SAAS,CAAElE,MAAM,CAACuE,eAAgB,CAClCH,EAAE,CAAE,CACFI,eAAe,CAAE/C,MAAM,CAAC4B,KAC1B,CAAE,CAAAiB,QAAA,cAEFpE,IAAA,CAACV,UAAU,EACTiF,OAAO,CAAC,SAAS,CACjBL,EAAE,CAAE,CACFM,UAAU,CAAE,kCACd,CAAE,CAAAJ,QAAA,CAED7C,MAAM,CAAC6B,KAAK,CACH,CAAC,CACV,CAAC,cAENlD,KAAA,CAACR,IAAI,EAAC+E,EAAE,CAAE,WAAWzD,IAAI,CAAC0D,IAAI,EAAG,CAACV,SAAS,CAAElE,MAAM,CAAC6E,YAAa,CAAAP,QAAA,eAC/DlE,KAAA,CAACb,GAAG,EAAC2E,SAAS,CAAElE,MAAM,CAAC8E,UAAW,CAAAR,QAAA,eAChCpE,IAAA,CAACX,GAAG,EAAC2E,SAAS,CAAElE,MAAM,CAAC+E,QAAS,CAAAT,QAAA,cAC9BpE,IAAA,CAACJ,OAAO,EAACkF,IAAI,CAAC,iBAAiB,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAAC7B,KAAK,CAAEtD,eAAgB,CAAE,CAAC,CAC9E,CAAC,cACNG,IAAA,CAACV,UAAU,EACTiF,OAAO,CAAC,IAAI,CACZP,SAAS,CAAElE,MAAM,CAACmF,QAAS,CAC3BC,MAAM,MACNhB,EAAE,CAAE,CACFM,UAAU,CAAE,kCAAkC,CAC9CrB,KAAK,CAAE,MAAM,CACbgC,QAAQ,CAAE,QAAQ,CAClBC,UAAU,CAAE,GAAG,CACfC,MAAM,CAAE,CAAC,CACTC,IAAI,CAAE,CAAC,CACPC,QAAQ,CAAE,QAAQ,CAClBC,YAAY,CAAE,UAChB,CAAE,CAAApB,QAAA,CAEDpD,IAAI,CAACyE,IAAI,CACA,CAAC,EACV,CAAC,cAGNzF,IAAA,CAACX,GAAG,EAAC2E,SAAS,CAAElE,MAAM,CAAC4F,kBAAmB,CAAAtB,QAAA,cACxClE,KAAA,CAACb,GAAG,EAAC2E,SAAS,CAAElE,MAAM,CAAC6F,WAAY,CAAAvB,QAAA,eACjCpE,IAAA,CAACJ,OAAO,EAACkF,IAAI,CAAC,2BAA2B,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAAC7B,KAAK,CAAEtD,eAAgB,CAAE,CAAC,cAC3FG,IAAA,CAACV,UAAU,EACTiF,OAAO,CAAC,SAAS,CACjBL,EAAE,CAAE,CACFM,UAAU,CAAE,kCAAkC,CAC9CW,QAAQ,CAAE,SAAS,CACnBC,UAAU,CAAE,GAAG,CACfjC,KAAK,CAAE,MACT,CAAE,CAAAiB,QAAA,CAED,CAAAtD,qBAAA,CAAAE,IAAI,CAAC4E,iBAAiB,UAAA9E,qBAAA,WAAtBA,qBAAA,CAAwB+E,aAAa,CAClC,YAAY,CACZ,CAAA9E,sBAAA,CAAAC,IAAI,CAAC4E,iBAAiB,UAAA7E,sBAAA,WAAtBA,sBAAA,CAAwB+E,YAAY,CAClC9E,IAAI,CAAC4E,iBAAiB,CAACE,YAAY,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAGhF,IAAI,CAAC4E,iBAAiB,CAACE,YAAY,CAACG,KAAK,CAAC,CAAC,CAAC,CACzGjF,IAAI,CAACkF,IAAI,CAACC,EAAE,GAAKlF,WAAW,CAACkF,EAAE,CAAG,OAAO,CAAG,QAAS,CAElD,CAAC,EACV,CAAC,CACH,CAAC,cAENnG,IAAA,CAACX,GAAG,EAAC2E,SAAS,CAAElE,MAAM,CAACsG,QAAS,CAAAhC,QAAA,CAC7B,CAACpD,IAAI,CAACsC,UAAU,EAAItC,IAAI,CAACuC,QAAQ,gBAChCrD,KAAA,CAACb,GAAG,EAAC2E,SAAS,CAAElE,MAAM,CAACuG,QAAS,CAAAjC,QAAA,eAC9BpE,IAAA,CAACJ,OAAO,EAACkF,IAAI,CAAC,iBAAiB,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAAC7B,KAAK,CAAEtD,eAAgB,CAAE,CAAC,cACjFK,KAAA,CAACZ,UAAU,EAACiF,OAAO,CAAC,SAAS,CAAAH,QAAA,eAC3BpE,IAAA,SAAMgE,SAAS,CAAElE,MAAM,CAACwG,SAAU,CAAAlC,QAAA,CAAC,YAAU,CAAM,CAAC,CACnDpD,IAAI,CAACsC,UAAU,CAAG3D,KAAK,CAACqB,IAAI,CAACsC,UAAU,CAAC,CAACiD,MAAM,CAAC,YAAY,CAAC,CAAG,aAAa,CAC7EvF,IAAI,CAACsC,UAAU,EAAItC,IAAI,CAACuC,QAAQ,EAAI,KAAK,CACzCvC,IAAI,CAACuC,QAAQ,CAAG5D,KAAK,CAACqB,IAAI,CAACuC,QAAQ,CAAC,CAACgD,MAAM,CAAC,YAAY,CAAC,CAAG,aAAa,EAChE,CAAC,EACV,CACN,CACE,CAAC,cAGNrG,KAAA,CAACb,GAAG,EAAC2E,SAAS,CAAElE,MAAM,CAAC0G,aAAc,CAAApC,QAAA,eACnClE,KAAA,CAACb,GAAG,EAAC2E,SAAS,CAAElE,MAAM,CAAC2G,aAAc,CAAArC,QAAA,eACnCpE,IAAA,CAACX,GAAG,EAAC2E,SAAS,CAAElE,MAAM,CAAC4G,aAAc,CAACxC,EAAE,CAAE,CAAEI,eAAe,CAAE,SAAU,CAAE,CAAAF,QAAA,cACvEpE,IAAA,CAACJ,OAAO,EAACkF,IAAI,CAAC,kBAAkB,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAAC7B,KAAK,CAAC,SAAS,CAAE,CAAC,CACvE,CAAC,cACNjD,KAAA,CAACb,GAAG,EAAC2E,SAAS,CAAElE,MAAM,CAAC6G,aAAc,CAAAvC,QAAA,eACnCpE,IAAA,CAACV,UAAU,EACTiF,OAAO,CAAC,SAAS,CACjBP,SAAS,CAAElE,MAAM,CAAC8G,cAAe,CACjC1C,EAAE,CAAE,CACFiB,QAAQ,CAAE,QAAQ,CAClBhC,KAAK,CAAE,MAAM,CACbqB,UAAU,CAAE,kCACd,CAAE,CAAAJ,QAAA,CACH,YAED,CAAY,CAAC,cACbpE,IAAA,CAACV,UAAU,EACTiF,OAAO,CAAC,IAAI,CACZP,SAAS,CAAElE,MAAM,CAAC+G,cAAe,CACjC3C,EAAE,CAAE,CACFkB,UAAU,CAAE,GAAG,CACfD,QAAQ,CAAE,QAAQ,CAClBhC,KAAK,CAAE,MAAM,CACbqB,UAAU,CAAE,kCAAkC,CAC9Ca,MAAM,CAAE,CAAC,CACTyB,UAAU,CAAE,GACd,CAAE,CAAA1C,QAAA,CAED/B,eAAe,CACN,CAAC,EACV,CAAC,EACH,CAAC,cAENnC,KAAA,CAACb,GAAG,EAAC2E,SAAS,CAAElE,MAAM,CAAC2G,aAAc,CAAArC,QAAA,eACnCpE,IAAA,CAACX,GAAG,EAAC2E,SAAS,CAAElE,MAAM,CAAC4G,aAAc,CAACxC,EAAE,CAAE,CAAEI,eAAe,CAAE,SAAU,CAAE,CAAAF,QAAA,cACvEpE,IAAA,CAACJ,OAAO,EAACkF,IAAI,CAAC,6BAA6B,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAAC7B,KAAK,CAAC,SAAS,CAAE,CAAC,CAClF,CAAC,cACNjD,KAAA,CAACb,GAAG,EAAC2E,SAAS,CAAElE,MAAM,CAAC6G,aAAc,CAAAvC,QAAA,eACnCpE,IAAA,CAACV,UAAU,EACTiF,OAAO,CAAC,SAAS,CACjBP,SAAS,CAAElE,MAAM,CAAC8G,cAAe,CACjC1C,EAAE,CAAE,CACFiB,QAAQ,CAAE,QAAQ,CAClBhC,KAAK,CAAE,MAAM,CACbqB,UAAU,CAAE,kCACd,CAAE,CAAAJ,QAAA,CACH,OAED,CAAY,CAAC,cACbpE,IAAA,CAACV,UAAU,EACTiF,OAAO,CAAC,IAAI,CACZP,SAAS,CAAElE,MAAM,CAAC+G,cAAe,CACjC3C,EAAE,CAAE,CACFkB,UAAU,CAAE,GAAG,CACfD,QAAQ,CAAE,QAAQ,CAClBhC,KAAK,CAAE,MAAM,CACbqB,UAAU,CAAE,kCAAkC,CAC9Ca,MAAM,CAAE,CAAC,CACTyB,UAAU,CAAE,GACd,CAAE,CAAA1C,QAAA,CAED9B,UAAU,CACD,CAAC,EACV,CAAC,EACH,CAAC,cAENpC,KAAA,CAACb,GAAG,EAAC2E,SAAS,CAAElE,MAAM,CAAC2G,aAAc,CAAArC,QAAA,eACnCpE,IAAA,CAACX,GAAG,EAAC2E,SAAS,CAAElE,MAAM,CAAC4G,aAAc,CAACxC,EAAE,CAAE,CAAEI,eAAe,CAAE,SAAU,CAAE,CAAAF,QAAA,cACvEpE,IAAA,CAACJ,OAAO,EAACkF,IAAI,CAAC,wBAAwB,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAAC7B,KAAK,CAAC,SAAS,CAAE,CAAC,CAC7E,CAAC,cACNjD,KAAA,CAACb,GAAG,EAAC2E,SAAS,CAAElE,MAAM,CAAC6G,aAAc,CAAAvC,QAAA,eACnCpE,IAAA,CAACV,UAAU,EACTiF,OAAO,CAAC,SAAS,CACjBP,SAAS,CAAElE,MAAM,CAAC8G,cAAe,CACjC1C,EAAE,CAAE,CACFiB,QAAQ,CAAE,QAAQ,CAClBhC,KAAK,CAAE,MAAM,CACbqB,UAAU,CAAE,kCACd,CAAE,CAAAJ,QAAA,CACH,UAED,CAAY,CAAC,cACbpE,IAAA,CAACV,UAAU,EACTiF,OAAO,CAAC,IAAI,CACZP,SAAS,CAAElE,MAAM,CAAC+G,cAAe,CACjC3C,EAAE,CAAE,CACFkB,UAAU,CAAE,GAAG,CACfD,QAAQ,CAAE,QAAQ,CAClBhC,KAAK,CAAE,MAAM,CACbqB,UAAU,CAAE,kCAAkC,CAC9Ca,MAAM,CAAE,CAAC,CACTyB,UAAU,CAAE,GACd,CAAE,CAAA1C,QAAA,CAED1B,aAAa,CACJ,CAAC,EACV,CAAC,EACH,CAAC,EACH,CAAC,cAGNxC,KAAA,CAACb,GAAG,EAAC2E,SAAS,CAAElE,MAAM,CAACiH,eAAgB,CAAA3C,QAAA,eACrClE,KAAA,CAACb,GAAG,EAAC2E,SAAS,CAAElE,MAAM,CAACkH,cAAe,CAAA5C,QAAA,eACpCpE,IAAA,CAACV,UAAU,EACTiF,OAAO,CAAC,SAAS,CACjBpB,KAAK,CAAC,eAAe,CACrBe,EAAE,CAAE,CACFM,UAAU,CAAE,kCAAkC,CAC9CW,QAAQ,CAAE,QAAQ,CAClBhC,KAAK,CAAE,MACT,CAAE,CAAAiB,QAAA,CACH,UAED,CAAY,CAAC,cACblE,KAAA,CAACZ,UAAU,EACTiF,OAAO,CAAC,IAAI,CACZP,SAAS,CAAElE,MAAM,CAACmH,aAAc,CAChC/C,EAAE,CAAE,CACFkB,UAAU,CAAE,GAAG,CACfD,QAAQ,CAAE,QAAQ,CAClBhC,KAAK,CAAE,MAAM,CACbqB,UAAU,CAAE,kCAAkC,CAC9Ca,MAAM,CAAE,CAAC,CACTyB,UAAU,CAAE,GACd,CAAE,CAAA1C,QAAA,EAED/C,QAAQ,CAAC,GACZ,EAAY,CAAC,EACV,CAAC,cACNrB,IAAA,CAACP,cAAc,EACb8E,OAAO,CAAC,aAAa,CACrB2C,KAAK,CAAE7F,QAAS,CAChB6C,EAAE,CAAE,CACFc,MAAM,CAAE,CAAC,CACTmC,YAAY,CAAE,CAAC,CACf7C,eAAe,CAAE,qBAAqB,CACtC,0BAA0B,CAAE,CAC1B6C,YAAY,CAAE,CAAC,CACf7C,eAAe,CAAEjD,QAAQ,GAAK,GAAG,CAAG,SAAS,CAAGxB,eAClD,CACF,CAAE,CACH,CAAC,cAGFK,KAAA,CAACb,GAAG,EAAC2E,SAAS,CAAElE,MAAM,CAACsH,gBAAiB,CAAAhD,QAAA,eACtClE,KAAA,CAACb,GAAG,EAAC2E,SAAS,CAAElE,MAAM,CAACuH,UAAW,CAAAjD,QAAA,eAChCpE,IAAA,CAACX,GAAG,EAAC2E,SAAS,CAAElE,MAAM,CAACwH,WAAY,CAACpD,EAAE,CAAE,CAAEI,eAAe,CAAE,SAAU,CAAE,CAAE,CAAC,cAC1EpE,KAAA,CAACZ,UAAU,EACTiF,OAAO,CAAC,SAAS,CACjBP,SAAS,CAAElE,MAAM,CAACyH,UAAW,CAC7BrD,EAAE,CAAE,CACFM,UAAU,CAAE,kCAAkC,CAC9CW,QAAQ,CAAE,SAAS,CACnBhC,KAAK,CAAE,MACT,CAAE,CAAAiB,QAAA,EAEDtB,cAAc,CAAC,YAClB,EAAY,CAAC,EACV,CAAC,cACN5C,KAAA,CAACb,GAAG,EAAC2E,SAAS,CAAElE,MAAM,CAACuH,UAAW,CAAAjD,QAAA,eAChCpE,IAAA,CAACX,GAAG,EAAC2E,SAAS,CAAElE,MAAM,CAACwH,WAAY,CAACpD,EAAE,CAAE,CAAEI,eAAe,CAAE,SAAU,CAAE,CAAE,CAAC,cAC1EpE,KAAA,CAACZ,UAAU,EACTiF,OAAO,CAAC,SAAS,CACjBP,SAAS,CAAElE,MAAM,CAACyH,UAAW,CAC7BrD,EAAE,CAAE,CACFM,UAAU,CAAE,kCAAkC,CAC9CW,QAAQ,CAAE,SAAS,CACnBhC,KAAK,CAAE,MACT,CAAE,CAAAiB,QAAA,EAEDrB,eAAe,CAAC,cACnB,EAAY,CAAC,EACV,CAAC,cACN7C,KAAA,CAACb,GAAG,EAAC2E,SAAS,CAAElE,MAAM,CAACuH,UAAW,CAAAjD,QAAA,eAChCpE,IAAA,CAACX,GAAG,EAAC2E,SAAS,CAAElE,MAAM,CAACwH,WAAY,CAACpD,EAAE,CAAE,CAAEI,eAAe,CAAE,SAAU,CAAE,CAAE,CAAC,cAC1EpE,KAAA,CAACZ,UAAU,EACTiF,OAAO,CAAC,SAAS,CACjBP,SAAS,CAAElE,MAAM,CAACyH,UAAW,CAC7BrD,EAAE,CAAE,CACFM,UAAU,CAAE,kCAAkC,CAC9CW,QAAQ,CAAE,SAAS,CACnBhC,KAAK,CAAE,MACT,CAAE,CAAAiB,QAAA,EAEDpB,eAAe,CAAC,cACnB,EAAY,CAAC,EACV,CAAC,EACH,CAAC,EACH,CAAC,cAGN9C,KAAA,CAACb,GAAG,EACF2E,SAAS,CAAElE,MAAM,CAAC0H,WAAY,CAC9BtD,EAAE,CAAE,CACFuD,SAAS,CAAE,MAAM,CACjBC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,GAAG,CAAE,KAAK,CACVC,SAAS,CAAE,KAAK,CAChBC,UAAU,CAAE,KAAK,CACjBC,cAAc,CAAE,eAAe,CAC/BvD,UAAU,CAAE,kCAAkC,CAC9CwD,YAAY,CAAE,KAChB,CAAE,CAAA5D,QAAA,eAEFlE,KAAA,CAACb,GAAG,EAAC6E,EAAE,CAAE,CAAEwD,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAAxD,QAAA,eACzDpE,IAAA,CAACR,MAAM,EACLyI,GAAG,CAAEjH,IAAI,CAACkF,IAAI,CAACgC,MAAM,EAAI,EAAG,CAC5BC,GAAG,CAAE,GAAGnH,IAAI,CAACkF,IAAI,CAACkC,UAAU,IAAIpH,IAAI,CAACkF,IAAI,CAACmC,SAAS,EAAG,CACtDrE,SAAS,CAAElE,MAAM,CAACwI,aAAc,CAChCpE,EAAE,CAAE,CAAEa,KAAK,CAAE,MAAM,CAAEC,MAAM,CAAE,MAAO,CAAE,CACvC,CAAC,cACF9E,KAAA,CAACb,GAAG,EAAA+E,QAAA,eACFpE,IAAA,CAACV,UAAU,EACTiF,OAAO,CAAC,SAAS,CACjBP,SAAS,CAAElE,MAAM,CAACyI,YAAa,CAC/BrE,EAAE,CAAE,CACFf,KAAK,CAAE,MAAM,CACbgC,QAAQ,CAAE,QAAQ,CAClBuC,OAAO,CAAE,OAAO,CAChBlD,UAAU,CAAE,kCACd,CAAE,CAAAJ,QAAA,CACH,YAED,CAAY,CAAC,cACblE,KAAA,CAACZ,UAAU,EACTiF,OAAO,CAAC,SAAS,CACjBP,SAAS,CAAElE,MAAM,CAAC0I,WAAY,CAC9BtE,EAAE,CAAE,CACFkB,UAAU,CAAE,GAAG,CACfD,QAAQ,CAAE,QAAQ,CAClBhC,KAAK,CAAE,MAAM,CACbqB,UAAU,CAAE,kCACd,CAAE,CAAAJ,QAAA,EAEDpD,IAAI,CAACkF,IAAI,CAACkC,UAAU,CAAC,GAAC,CAACpH,IAAI,CAACkF,IAAI,CAACmC,SAAS,EACjC,CAAC,EACV,CAAC,EACH,CAAC,cAENnI,KAAA,CAACb,GAAG,EAAC6E,EAAE,CAAE,CAAEwD,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAAxD,QAAA,EACxDpD,IAAI,CAACkF,IAAI,CAACC,EAAE,GAAKlF,WAAW,CAACkF,EAAE,eAC9BjG,KAAA,CAACb,GAAG,EACF6E,EAAE,CAAE,CACFwD,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,GAAG,CAAE,KAAK,CACVtD,eAAe,CAAE,wBAAwB,CACzCnB,KAAK,CAAE,OAAO,CACdsF,OAAO,CAAE,UAAU,CACnBtB,YAAY,CAAE,MAAM,CACpBhC,QAAQ,CAAE,SAAS,CACnBC,UAAU,CAAE,GAAG,CACfsD,SAAS,CAAE,8BAA8B,CACzCC,WAAW,CAAE,KACf,CAAE,CAAAvE,QAAA,eAEFpE,IAAA,CAACJ,OAAO,EAACkF,IAAI,CAAC,kBAAkB,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAAE,CAAC,cAC1DhF,IAAA,CAACV,UAAU,EACTiF,OAAO,CAAC,SAAS,CACjBL,EAAE,CAAE,CACFM,UAAU,CAAE,kCAAkC,CAC9CrB,KAAK,CAAE,OACT,CAAE,CAAAiB,QAAA,CACH,QAED,CAAY,CAAC,EACV,CACN,cACDlE,KAAA,CAACZ,UAAU,EACTiF,OAAO,CAAC,SAAS,CACjBP,SAAS,CAAElE,MAAM,CAAC8I,WAAY,CAC9B1E,EAAE,CAAE,CACFf,KAAK,CAAE,MAAM,CACbgC,QAAQ,CAAE,QAAQ,CAClBuC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,GAAG,CAAE,KAAK,CACVpD,UAAU,CAAE,kCACd,CAAE,CAAAJ,QAAA,eAEFpE,IAAA,CAACJ,OAAO,EAACkF,IAAI,CAAC,mBAAmB,CAACC,KAAK,CAAE,EAAG,CAACC,MAAM,CAAE,EAAG,CAAE,CAAC,CAC1DrF,KAAK,CAACqB,IAAI,CAAC6H,UAAU,CAAC,CAACC,OAAO,CAAC,CAAC,EACvB,CAAC,EACV,CAAC,EACH,CAAC,EACF,CAAC,GA/W8B5H,KAgXjC,CAAC,CAEX,CAAC,CAED,cAAe,CAAAX,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}