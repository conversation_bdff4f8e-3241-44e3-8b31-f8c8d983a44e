{"ast": null, "code": "'use strict';\n\n/** @type {import('./max')} */\nmodule.exports = Math.max;", "map": {"version": 3, "names": ["module", "exports", "Math", "max"], "sources": ["C:/ignition/ignition-ui/node_modules/math-intrinsics/max.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./max')} */\nmodule.exports = Math.max;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACAA,MAAM,CAACC,OAAO,GAAGC,IAAI,CAACC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}