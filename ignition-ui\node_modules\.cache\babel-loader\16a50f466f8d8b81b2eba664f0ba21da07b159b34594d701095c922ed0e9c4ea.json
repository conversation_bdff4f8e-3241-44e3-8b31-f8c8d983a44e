{"ast": null, "code": "'use client';\n\nexport { Popper } from './Popper';\nexport { popperClasses, getPopperUtilityClass } from './popperClasses';", "map": {"version": 3, "names": ["<PERSON><PERSON>", "popperClasses", "getPopperUtilityClass"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/Popper/index.js"], "sourcesContent": ["'use client';\n\nexport { Popper } from './Popper';\nexport { popperClasses, getPopperUtilityClass } from './popperClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,MAAM,QAAQ,UAAU;AACjC,SAASC,aAAa,EAAEC,qBAAqB,QAAQ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}