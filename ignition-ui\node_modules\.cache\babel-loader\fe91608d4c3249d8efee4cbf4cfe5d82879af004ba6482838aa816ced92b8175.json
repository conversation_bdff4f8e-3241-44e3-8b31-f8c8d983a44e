{"ast": null, "code": "export { StyledEngineProvider as default } from '@mui/system';", "map": {"version": 3, "names": ["StyledEngineProvider", "default"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/styles/StyledEngineProvider.js"], "sourcesContent": ["export { StyledEngineProvider as default } from '@mui/system';"], "mappings": "AAAA,SAASA,oBAAoB,IAAIC,OAAO,QAAQ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}