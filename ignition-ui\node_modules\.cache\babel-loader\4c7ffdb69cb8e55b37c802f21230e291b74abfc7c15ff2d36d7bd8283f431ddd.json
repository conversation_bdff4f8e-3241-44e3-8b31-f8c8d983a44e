{"ast": null, "code": "export { applySolidInversion, applySoftInversion } from './colorInversionUtils';", "map": {"version": 3, "names": ["applySolidInversion", "applySoftInversion"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/colorInversion/index.js"], "sourcesContent": ["export { applySolidInversion, applySoftInversion } from './colorInversionUtils';"], "mappings": "AAAA,SAASA,mBAAmB,EAAEC,kBAAkB,QAAQ,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}