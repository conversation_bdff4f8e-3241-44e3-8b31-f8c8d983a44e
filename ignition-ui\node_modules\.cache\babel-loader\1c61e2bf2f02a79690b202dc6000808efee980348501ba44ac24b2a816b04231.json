{"ast": null, "code": "import * as React from 'react';\nconst ListSubheaderContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== 'production') {\n  ListSubheaderContext.displayName = 'ListSubheaderContext';\n}\nexport default ListSubheaderContext;", "map": {"version": 3, "names": ["React", "ListSubheaderContext", "createContext", "undefined", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/ListSubheader/ListSubheaderContext.js"], "sourcesContent": ["import * as React from 'react';\nconst ListSubheaderContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== 'production') {\n  ListSubheaderContext.displayName = 'ListSubheaderContext';\n}\nexport default ListSubheaderContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,oBAAoB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAACC,SAAS,CAAC;AACxE,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCL,oBAAoB,CAACM,WAAW,GAAG,sBAAsB;AAC3D;AACA,eAAeN,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}