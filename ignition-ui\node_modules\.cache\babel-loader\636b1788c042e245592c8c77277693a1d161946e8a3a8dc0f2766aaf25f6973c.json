{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getAspectRatioUtilityClass(slot) {\n  return generateUtilityClass('MuiAspectRatio', slot);\n}\nconst aspectRatioClasses = generateUtilityClasses('MuiAspectRatio', ['root', 'content', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid']);\nexport default aspectRatioClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getAspectRatioUtilityClass", "slot", "aspectRatioClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/AspectRatio/aspectRatioClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getAspectRatioUtilityClass(slot) {\n  return generateUtilityClass('MuiAspectRatio', slot);\n}\nconst aspectRatioClasses = generateUtilityClasses('MuiAspectRatio', ['root', 'content', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid']);\nexport default aspectRatioClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,0BAA0BA,CAACC,IAAI,EAAE;EAC/C,OAAOH,oBAAoB,CAAC,gBAAgB,EAAEG,IAAI,CAAC;AACrD;AACA,MAAMC,kBAAkB,GAAGH,sBAAsB,CAAC,gBAAgB,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,iBAAiB,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;AACzP,eAAeG,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}