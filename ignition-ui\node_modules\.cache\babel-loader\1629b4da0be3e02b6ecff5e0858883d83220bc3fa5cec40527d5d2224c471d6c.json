{"ast": null, "code": "import * as React from 'react';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst VariantColorContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== 'production') {\n  VariantColorContext.displayName = 'VariantColorContext';\n}\n\n/**\n * @internal For internal usage only.\n *\n * Use this function in a slot to get the matched default variant and color when the parent's variant and/or color changes.\n */\nexport function getChildVariantAndColor(parentVariant, parentColor) {\n  let childColor = parentColor;\n  let childVariant = parentVariant;\n  if (parentVariant === 'outlined') {\n    childColor = 'neutral';\n    childVariant = 'plain';\n  }\n  if (parentVariant === 'plain') {\n    childColor = 'neutral';\n  }\n  return {\n    variant: childVariant,\n    color: childColor\n  };\n}\n\n/**\n * @internal For internal usage only.\n *\n * This hook should be used in a children that are connected with its parent\n * to get the matched default variant and color when the parent's variant and/or color changes.\n *\n * For example, the `Option` component in `Select` component is using this function.\n */\nexport function useVariantColor(instanceVariant, instanceColor) {\n  let alwaysInheritColor = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  const value = React.useContext(VariantColorContext);\n  const [variant, color] = typeof value === 'string' ? value.split(':') : [];\n  const result = getChildVariantAndColor(variant || undefined, color || undefined);\n  result.variant = instanceVariant || result.variant;\n  result.color = instanceColor || (alwaysInheritColor ? color : result.color);\n  return result;\n}\n\n/**\n * @internal For internal usage only.\n */\nexport function VariantColorProvider(_ref) {\n  let {\n    children,\n    color,\n    variant\n  } = _ref;\n  return /*#__PURE__*/_jsx(VariantColorContext.Provider, {\n    value: `${variant || ''}:${color || ''}`,\n    children: children\n  });\n}", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "VariantColorContext", "createContext", "undefined", "process", "env", "NODE_ENV", "displayName", "getChildVariantAndColor", "parentVariant", "parentColor", "childColor", "child<PERSON><PERSON>t", "variant", "color", "useVariantColor", "instance<PERSON><PERSON><PERSON>", "instanceColor", "alwaysInheritColor", "arguments", "length", "value", "useContext", "split", "result", "VariantColorProvider", "_ref", "children", "Provider"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/styles/variantColorInheritance.js"], "sourcesContent": ["import * as React from 'react';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst VariantColorContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== 'production') {\n  VariantColorContext.displayName = 'VariantColorContext';\n}\n\n/**\n * @internal For internal usage only.\n *\n * Use this function in a slot to get the matched default variant and color when the parent's variant and/or color changes.\n */\nexport function getChildVariantAndColor(parentVariant, parentColor) {\n  let childColor = parentColor;\n  let childVariant = parentVariant;\n  if (parentVariant === 'outlined') {\n    childColor = 'neutral';\n    childVariant = 'plain';\n  }\n  if (parentVariant === 'plain') {\n    childColor = 'neutral';\n  }\n  return {\n    variant: childVariant,\n    color: childColor\n  };\n}\n\n/**\n * @internal For internal usage only.\n *\n * This hook should be used in a children that are connected with its parent\n * to get the matched default variant and color when the parent's variant and/or color changes.\n *\n * For example, the `Option` component in `Select` component is using this function.\n */\nexport function useVariantColor(instanceVariant, instanceColor, alwaysInheritColor = false) {\n  const value = React.useContext(VariantColorContext);\n  const [variant, color] = typeof value === 'string' ? value.split(':') : [];\n  const result = getChildVariantAndColor(variant || undefined, color || undefined);\n  result.variant = instanceVariant || result.variant;\n  result.color = instanceColor || (alwaysInheritColor ? color : result.color);\n  return result;\n}\n\n/**\n * @internal For internal usage only.\n */\nexport function VariantColorProvider({\n  children,\n  color,\n  variant\n}) {\n  return /*#__PURE__*/_jsx(VariantColorContext.Provider, {\n    value: `${variant || ''}:${color || ''}`,\n    children: children\n  });\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,mBAAmB,GAAG,aAAaH,KAAK,CAACI,aAAa,CAACC,SAAS,CAAC;AACvE,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCL,mBAAmB,CAACM,WAAW,GAAG,qBAAqB;AACzD;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,uBAAuBA,CAACC,aAAa,EAAEC,WAAW,EAAE;EAClE,IAAIC,UAAU,GAAGD,WAAW;EAC5B,IAAIE,YAAY,GAAGH,aAAa;EAChC,IAAIA,aAAa,KAAK,UAAU,EAAE;IAChCE,UAAU,GAAG,SAAS;IACtBC,YAAY,GAAG,OAAO;EACxB;EACA,IAAIH,aAAa,KAAK,OAAO,EAAE;IAC7BE,UAAU,GAAG,SAAS;EACxB;EACA,OAAO;IACLE,OAAO,EAAED,YAAY;IACrBE,KAAK,EAAEH;EACT,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,eAAeA,CAACC,eAAe,EAAEC,aAAa,EAA8B;EAAA,IAA5BC,kBAAkB,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAhB,SAAA,GAAAgB,SAAA,MAAG,KAAK;EACxF,MAAME,KAAK,GAAGvB,KAAK,CAACwB,UAAU,CAACrB,mBAAmB,CAAC;EACnD,MAAM,CAACY,OAAO,EAAEC,KAAK,CAAC,GAAG,OAAOO,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAACE,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE;EAC1E,MAAMC,MAAM,GAAGhB,uBAAuB,CAACK,OAAO,IAAIV,SAAS,EAAEW,KAAK,IAAIX,SAAS,CAAC;EAChFqB,MAAM,CAACX,OAAO,GAAGG,eAAe,IAAIQ,MAAM,CAACX,OAAO;EAClDW,MAAM,CAACV,KAAK,GAAGG,aAAa,KAAKC,kBAAkB,GAAGJ,KAAK,GAAGU,MAAM,CAACV,KAAK,CAAC;EAC3E,OAAOU,MAAM;AACf;;AAEA;AACA;AACA;AACA,OAAO,SAASC,oBAAoBA,CAAAC,IAAA,EAIjC;EAAA,IAJkC;IACnCC,QAAQ;IACRb,KAAK;IACLD;EACF,CAAC,GAAAa,IAAA;EACC,OAAO,aAAa1B,IAAI,CAACC,mBAAmB,CAAC2B,QAAQ,EAAE;IACrDP,KAAK,EAAE,GAAGR,OAAO,IAAI,EAAE,IAAIC,KAAK,IAAI,EAAE,EAAE;IACxCa,QAAQ,EAAEA;EACZ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}