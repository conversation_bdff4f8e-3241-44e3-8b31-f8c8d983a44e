{"ast": null, "code": "import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nconst COMPONENT_NAME = 'MenuButton';\nexport function getMenuButtonUtilityClass(slot) {\n  return generateUtilityClass(COMPONENT_NAME, slot);\n}\nexport const menuButtonClasses = generateUtilityClasses(COMPONENT_NAME, ['root', 'active', 'disabled', 'expanded']);", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "COMPONENT_NAME", "getMenuButtonUtilityClass", "slot", "menuButtonClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/MenuButton/menuButtonClasses.js"], "sourcesContent": ["import { generateUtilityClass } from '../generateUtilityClass';\nimport { generateUtilityClasses } from '../generateUtilityClasses';\nconst COMPONENT_NAME = 'MenuButton';\nexport function getMenuButtonUtilityClass(slot) {\n  return generateUtilityClass(COMPONENT_NAME, slot);\n}\nexport const menuButtonClasses = generateUtilityClasses(COMPONENT_NAME, ['root', 'active', 'disabled', 'expanded']);"], "mappings": "AAAA,SAASA,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,sBAAsB,QAAQ,2BAA2B;AAClE,MAAMC,cAAc,GAAG,YAAY;AACnC,OAAO,SAASC,yBAAyBA,CAACC,IAAI,EAAE;EAC9C,OAAOJ,oBAAoB,CAACE,cAAc,EAAEE,IAAI,CAAC;AACnD;AACA,OAAO,MAAMC,iBAAiB,GAAGJ,sBAAsB,CAACC,cAAc,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}