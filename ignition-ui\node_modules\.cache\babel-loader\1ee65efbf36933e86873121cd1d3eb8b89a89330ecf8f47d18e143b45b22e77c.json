{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"component\", \"htmlFor\", \"id\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport { styled, useThemeProps } from '../styles';\nimport useSlot from '../utils/useSlot';\nimport { getFormLabelUtilityClass } from './formLabelClasses';\nimport FormControlContext from '../FormControl/FormControlContext';\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = () => {\n  const slots = {\n    root: ['root'],\n    asterisk: ['asterisk']\n  };\n  return composeClasses(slots, getFormLabelUtilityClass, {});\n};\nconst FormLabelRoot = styled('label', {\n  name: 'JoyFormLabel',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(_ref2 => {\n  let {\n    theme\n  } = _ref2;\n  return {\n    '--Icon-fontSize': 'calc(var(--FormLabel-lineHeight) * 1em)',\n    WebkitTapHighlightColor: 'transparent',\n    alignSelf: 'var(--FormLabel-alignSelf)',\n    // to not fill the block space. It seems like a bug when clicking on empty space (within the label area), even though it is not.\n    display: 'flex',\n    gap: '2px',\n    alignItems: 'center',\n    flexWrap: 'wrap',\n    userSelect: 'none',\n    fontFamily: theme.vars.fontFamily.body,\n    fontSize: `var(--FormLabel-fontSize, ${theme.vars.fontSize.sm})`,\n    fontWeight: theme.vars.fontWeight.md,\n    lineHeight: `var(--FormLabel-lineHeight, ${theme.vars.lineHeight.sm})`,\n    color: `var(--FormLabel-color, ${theme.vars.palette.text.primary})`,\n    margin: 'var(--FormLabel-margin, 0px)'\n  };\n});\nconst AsteriskComponent = styled('span', {\n  name: 'JoyFormLabel',\n  slot: 'Asterisk',\n  overridesResolver: (props, styles) => styles.asterisk\n})({\n  color: 'var(--FormLabel-asteriskColor)'\n});\n/**\n *\n * Demos:\n *\n * - [Input](https://mui.com/joy-ui/react-input/)\n *\n * API:\n *\n * - [FormLabel API](https://mui.com/joy-ui/api/form-label/)\n */\nconst FormLabel = /*#__PURE__*/React.forwardRef(function FormLabel(inProps, ref) {\n  var _ref, _inProps$required;\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyFormLabel'\n  });\n  const {\n      children,\n      component = 'label',\n      htmlFor,\n      id,\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const formControl = React.useContext(FormControlContext);\n  const required = (_ref = (_inProps$required = inProps.required) != null ? _inProps$required : formControl == null ? void 0 : formControl.required) != null ? _ref : false;\n  const ownerState = _extends({}, props, {\n    required\n  });\n  const classes = useUtilityClasses();\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const [SlotRoot, rootProps] = useSlot('root', {\n    additionalProps: {\n      htmlFor: htmlFor != null ? htmlFor : formControl == null ? void 0 : formControl.htmlFor,\n      id: id != null ? id : formControl == null ? void 0 : formControl.labelId\n    },\n    ref,\n    className: classes.root,\n    elementType: FormLabelRoot,\n    externalForwardedProps,\n    ownerState\n  });\n  const [SlotAsterisk, asteriskProps] = useSlot('asterisk', {\n    additionalProps: {\n      'aria-hidden': true\n    },\n    className: classes.asterisk,\n    elementType: AsteriskComponent,\n    externalForwardedProps,\n    ownerState\n  });\n  return /*#__PURE__*/_jsxs(SlotRoot, _extends({}, rootProps, {\n    children: [children, required && /*#__PURE__*/_jsxs(SlotAsterisk, _extends({}, asteriskProps, {\n      children: [\"\\u2009\", '*']\n    }))]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? FormLabel.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * @ignore\n   */\n  htmlFor: PropTypes.string,\n  /**\n   * @ignore\n   */\n  id: PropTypes.string,\n  /**\n   * The asterisk is added if required=`true`\n   */\n  required: PropTypes.bool,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    asterisk: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    asterisk: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default FormLabel;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "unstable_composeClasses", "composeClasses", "styled", "useThemeProps", "useSlot", "getFormLabelUtilityClass", "FormControlContext", "jsxs", "_jsxs", "useUtilityClasses", "slots", "root", "asterisk", "FormLabelRoot", "name", "slot", "overridesResolver", "props", "styles", "_ref2", "theme", "WebkitTapHighlightColor", "alignSelf", "display", "gap", "alignItems", "flexWrap", "userSelect", "fontFamily", "vars", "body", "fontSize", "sm", "fontWeight", "md", "lineHeight", "color", "palette", "text", "primary", "margin", "AsteriskComponent", "FormLabel", "forwardRef", "inProps", "ref", "_ref", "_inProps$required", "children", "component", "htmlFor", "id", "slotProps", "other", "formControl", "useContext", "required", "ownerState", "classes", "externalForwardedProps", "SlotRoot", "rootProps", "additionalProps", "labelId", "className", "elementType", "SlotAsterisk", "asteriskProps", "process", "env", "NODE_ENV", "propTypes", "node", "string", "bool", "shape", "oneOfType", "func", "object", "sx", "arrayOf"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/FormLabel/FormLabel.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"component\", \"htmlFor\", \"id\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_composeClasses as composeClasses } from '@mui/base/composeClasses';\nimport { styled, useThemeProps } from '../styles';\nimport useSlot from '../utils/useSlot';\nimport { getFormLabelUtilityClass } from './formLabelClasses';\nimport FormControlContext from '../FormControl/FormControlContext';\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = () => {\n  const slots = {\n    root: ['root'],\n    asterisk: ['asterisk']\n  };\n  return composeClasses(slots, getFormLabelUtilityClass, {});\n};\nconst FormLabelRoot = styled('label', {\n  name: 'JoyFormLabel',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(({\n  theme\n}) => ({\n  '--Icon-fontSize': 'calc(var(--FormLabel-lineHeight) * 1em)',\n  WebkitTapHighlightColor: 'transparent',\n  alignSelf: 'var(--FormLabel-alignSelf)',\n  // to not fill the block space. It seems like a bug when clicking on empty space (within the label area), even though it is not.\n  display: 'flex',\n  gap: '2px',\n  alignItems: 'center',\n  flexWrap: 'wrap',\n  userSelect: 'none',\n  fontFamily: theme.vars.fontFamily.body,\n  fontSize: `var(--FormLabel-fontSize, ${theme.vars.fontSize.sm})`,\n  fontWeight: theme.vars.fontWeight.md,\n  lineHeight: `var(--FormLabel-lineHeight, ${theme.vars.lineHeight.sm})`,\n  color: `var(--FormLabel-color, ${theme.vars.palette.text.primary})`,\n  margin: 'var(--FormLabel-margin, 0px)'\n}));\nconst AsteriskComponent = styled('span', {\n  name: 'JoyFormLabel',\n  slot: 'Asterisk',\n  overridesResolver: (props, styles) => styles.asterisk\n})({\n  color: 'var(--FormLabel-asteriskColor)'\n});\n/**\n *\n * Demos:\n *\n * - [Input](https://mui.com/joy-ui/react-input/)\n *\n * API:\n *\n * - [FormLabel API](https://mui.com/joy-ui/api/form-label/)\n */\nconst FormLabel = /*#__PURE__*/React.forwardRef(function FormLabel(inProps, ref) {\n  var _ref, _inProps$required;\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyFormLabel'\n  });\n  const {\n      children,\n      component = 'label',\n      htmlFor,\n      id,\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const formControl = React.useContext(FormControlContext);\n  const required = (_ref = (_inProps$required = inProps.required) != null ? _inProps$required : formControl == null ? void 0 : formControl.required) != null ? _ref : false;\n  const ownerState = _extends({}, props, {\n    required\n  });\n  const classes = useUtilityClasses();\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const [SlotRoot, rootProps] = useSlot('root', {\n    additionalProps: {\n      htmlFor: htmlFor != null ? htmlFor : formControl == null ? void 0 : formControl.htmlFor,\n      id: id != null ? id : formControl == null ? void 0 : formControl.labelId\n    },\n    ref,\n    className: classes.root,\n    elementType: FormLabelRoot,\n    externalForwardedProps,\n    ownerState\n  });\n  const [SlotAsterisk, asteriskProps] = useSlot('asterisk', {\n    additionalProps: {\n      'aria-hidden': true\n    },\n    className: classes.asterisk,\n    elementType: AsteriskComponent,\n    externalForwardedProps,\n    ownerState\n  });\n  return /*#__PURE__*/_jsxs(SlotRoot, _extends({}, rootProps, {\n    children: [children, required && /*#__PURE__*/_jsxs(SlotAsterisk, _extends({}, asteriskProps, {\n      children: [\"\\u2009\", '*']\n    }))]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? FormLabel.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * @ignore\n   */\n  htmlFor: PropTypes.string,\n  /**\n   * @ignore\n   */\n  id: PropTypes.string,\n  /**\n   * The asterisk is added if required=`true`\n   */\n  required: PropTypes.bool,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    asterisk: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    asterisk: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default FormLabel;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,CAAC;AAClF,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,0BAA0B;AACpF,SAASC,MAAM,EAAEC,aAAa,QAAQ,WAAW;AACjD,OAAOC,OAAO,MAAM,kBAAkB;AACtC,SAASC,wBAAwB,QAAQ,oBAAoB;AAC7D,OAAOC,kBAAkB,MAAM,mCAAmC;AAClE,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAC9B,MAAMC,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,CAAC;IACdC,QAAQ,EAAE,CAAC,UAAU;EACvB,CAAC;EACD,OAAOX,cAAc,CAACS,KAAK,EAAEL,wBAAwB,EAAE,CAAC,CAAC,CAAC;AAC5D,CAAC;AACD,MAAMQ,aAAa,GAAGX,MAAM,CAAC,OAAO,EAAE;EACpCY,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACP;AAC/C,CAAC,CAAC,CAACQ,KAAA;EAAA,IAAC;IACFC;EACF,CAAC,GAAAD,KAAA;EAAA,OAAM;IACL,iBAAiB,EAAE,yCAAyC;IAC5DE,uBAAuB,EAAE,aAAa;IACtCC,SAAS,EAAE,4BAA4B;IACvC;IACAC,OAAO,EAAE,MAAM;IACfC,GAAG,EAAE,KAAK;IACVC,UAAU,EAAE,QAAQ;IACpBC,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,MAAM;IAClBC,UAAU,EAAER,KAAK,CAACS,IAAI,CAACD,UAAU,CAACE,IAAI;IACtCC,QAAQ,EAAE,6BAA6BX,KAAK,CAACS,IAAI,CAACE,QAAQ,CAACC,EAAE,GAAG;IAChEC,UAAU,EAAEb,KAAK,CAACS,IAAI,CAACI,UAAU,CAACC,EAAE;IACpCC,UAAU,EAAE,+BAA+Bf,KAAK,CAACS,IAAI,CAACM,UAAU,CAACH,EAAE,GAAG;IACtEI,KAAK,EAAE,0BAA0BhB,KAAK,CAACS,IAAI,CAACQ,OAAO,CAACC,IAAI,CAACC,OAAO,GAAG;IACnEC,MAAM,EAAE;EACV,CAAC;AAAA,CAAC,CAAC;AACH,MAAMC,iBAAiB,GAAGvC,MAAM,CAAC,MAAM,EAAE;EACvCY,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,UAAU;EAChBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC/C,CAAC,CAAC,CAAC;EACDwB,KAAK,EAAE;AACT,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,SAAS,GAAG,aAAa5C,KAAK,CAAC6C,UAAU,CAAC,SAASD,SAASA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC/E,IAAIC,IAAI,EAAEC,iBAAiB;EAC3B,MAAM9B,KAAK,GAAGd,aAAa,CAAC;IAC1Bc,KAAK,EAAE2B,OAAO;IACd9B,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFkC,QAAQ;MACRC,SAAS,GAAG,OAAO;MACnBC,OAAO;MACPC,EAAE;MACFzC,KAAK,GAAG,CAAC,CAAC;MACV0C,SAAS,GAAG,CAAC;IACf,CAAC,GAAGnC,KAAK;IACToC,KAAK,GAAGzD,6BAA6B,CAACqB,KAAK,EAAEpB,SAAS,CAAC;EACzD,MAAMyD,WAAW,GAAGxD,KAAK,CAACyD,UAAU,CAACjD,kBAAkB,CAAC;EACxD,MAAMkD,QAAQ,GAAG,CAACV,IAAI,GAAG,CAACC,iBAAiB,GAAGH,OAAO,CAACY,QAAQ,KAAK,IAAI,GAAGT,iBAAiB,GAAGO,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACE,QAAQ,KAAK,IAAI,GAAGV,IAAI,GAAG,KAAK;EACzK,MAAMW,UAAU,GAAG9D,QAAQ,CAAC,CAAC,CAAC,EAAEsB,KAAK,EAAE;IACrCuC;EACF,CAAC,CAAC;EACF,MAAME,OAAO,GAAGjD,iBAAiB,CAAC,CAAC;EACnC,MAAMkD,sBAAsB,GAAGhE,QAAQ,CAAC,CAAC,CAAC,EAAE0D,KAAK,EAAE;IACjDJ,SAAS;IACTvC,KAAK;IACL0C;EACF,CAAC,CAAC;EACF,MAAM,CAACQ,QAAQ,EAAEC,SAAS,CAAC,GAAGzD,OAAO,CAAC,MAAM,EAAE;IAC5C0D,eAAe,EAAE;MACfZ,OAAO,EAAEA,OAAO,IAAI,IAAI,GAAGA,OAAO,GAAGI,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACJ,OAAO;MACvFC,EAAE,EAAEA,EAAE,IAAI,IAAI,GAAGA,EAAE,GAAGG,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACS;IACnE,CAAC;IACDlB,GAAG;IACHmB,SAAS,EAAEN,OAAO,CAAC/C,IAAI;IACvBsD,WAAW,EAAEpD,aAAa;IAC1B8C,sBAAsB;IACtBF;EACF,CAAC,CAAC;EACF,MAAM,CAACS,YAAY,EAAEC,aAAa,CAAC,GAAG/D,OAAO,CAAC,UAAU,EAAE;IACxD0D,eAAe,EAAE;MACf,aAAa,EAAE;IACjB,CAAC;IACDE,SAAS,EAAEN,OAAO,CAAC9C,QAAQ;IAC3BqD,WAAW,EAAExB,iBAAiB;IAC9BkB,sBAAsB;IACtBF;EACF,CAAC,CAAC;EACF,OAAO,aAAajD,KAAK,CAACoD,QAAQ,EAAEjE,QAAQ,CAAC,CAAC,CAAC,EAAEkE,SAAS,EAAE;IAC1Db,QAAQ,EAAE,CAACA,QAAQ,EAAEQ,QAAQ,IAAI,aAAahD,KAAK,CAAC0D,YAAY,EAAEvE,QAAQ,CAAC,CAAC,CAAC,EAAEwE,aAAa,EAAE;MAC5FnB,QAAQ,EAAE,CAAC,QAAQ,EAAE,GAAG;IAC1B,CAAC,CAAC,CAAC;EACL,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFoB,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG5B,SAAS,CAAC6B,SAAS,CAAC,yBAAyB;EACnF;EACA;EACA;EACA;EACA;AACF;AACA;EACEvB,QAAQ,EAAEjD,SAAS,CAACyE,IAAI;EACxB;AACF;AACA;AACA;EACEvB,SAAS,EAAElD,SAAS,CAACkE,WAAW;EAChC;AACF;AACA;EACEf,OAAO,EAAEnD,SAAS,CAAC0E,MAAM;EACzB;AACF;AACA;EACEtB,EAAE,EAAEpD,SAAS,CAAC0E,MAAM;EACpB;AACF;AACA;EACEjB,QAAQ,EAAEzD,SAAS,CAAC2E,IAAI;EACxB;AACF;AACA;AACA;EACEtB,SAAS,EAAErD,SAAS,CAAC4E,KAAK,CAAC;IACzB/D,QAAQ,EAAEb,SAAS,CAAC6E,SAAS,CAAC,CAAC7E,SAAS,CAAC8E,IAAI,EAAE9E,SAAS,CAAC+E,MAAM,CAAC,CAAC;IACjEnE,IAAI,EAAEZ,SAAS,CAAC6E,SAAS,CAAC,CAAC7E,SAAS,CAAC8E,IAAI,EAAE9E,SAAS,CAAC+E,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEpE,KAAK,EAAEX,SAAS,CAAC4E,KAAK,CAAC;IACrB/D,QAAQ,EAAEb,SAAS,CAACkE,WAAW;IAC/BtD,IAAI,EAAEZ,SAAS,CAACkE;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACEc,EAAE,EAAEhF,SAAS,CAAC6E,SAAS,CAAC,CAAC7E,SAAS,CAACiF,OAAO,CAACjF,SAAS,CAAC6E,SAAS,CAAC,CAAC7E,SAAS,CAAC8E,IAAI,EAAE9E,SAAS,CAAC+E,MAAM,EAAE/E,SAAS,CAAC2E,IAAI,CAAC,CAAC,CAAC,EAAE3E,SAAS,CAAC8E,IAAI,EAAE9E,SAAS,CAAC+E,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAepC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}