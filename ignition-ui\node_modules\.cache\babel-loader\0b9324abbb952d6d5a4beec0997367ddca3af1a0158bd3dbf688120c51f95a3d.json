{"ast": null, "code": "export default function shouldSkipGeneratingVar(keys) {\n  var _keys$;\n  return !!keys[0].match(/^(typography|variants|breakpoints)$/) || !!keys[0].match(/sxConfig$/) ||\n  // ends with sxConfig\n  keys[0] === 'palette' && !!((_keys$ = keys[1]) != null && _keys$.match(/^(mode)$/)) || keys[0] === 'focus' && keys[1] !== 'thickness';\n}", "map": {"version": 3, "names": ["shouldSkipGeneratingVar", "keys", "_keys$", "match"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/styles/shouldSkipGeneratingVar.js"], "sourcesContent": ["export default function shouldSkipGeneratingVar(keys) {\n  var _keys$;\n  return !!keys[0].match(/^(typography|variants|breakpoints)$/) || !!keys[0].match(/sxConfig$/) ||\n  // ends with sxConfig\n  keys[0] === 'palette' && !!((_keys$ = keys[1]) != null && _keys$.match(/^(mode)$/)) || keys[0] === 'focus' && keys[1] !== 'thickness';\n}"], "mappings": "AAAA,eAAe,SAASA,uBAAuBA,CAACC,IAAI,EAAE;EACpD,IAAIC,MAAM;EACV,OAAO,CAAC,CAACD,IAAI,CAAC,CAAC,CAAC,CAACE,KAAK,CAAC,qCAAqC,CAAC,IAAI,CAAC,CAACF,IAAI,CAAC,CAAC,CAAC,CAACE,KAAK,CAAC,WAAW,CAAC;EAC7F;EACAF,IAAI,CAAC,CAAC,CAAC,KAAK,SAAS,IAAI,CAAC,EAAE,CAACC,MAAM,GAAGD,IAAI,CAAC,CAAC,CAAC,KAAK,IAAI,IAAIC,MAAM,CAACC,KAAK,CAAC,UAAU,CAAC,CAAC,IAAIF,IAAI,CAAC,CAAC,CAAC,KAAK,OAAO,IAAIA,IAAI,CAAC,CAAC,CAAC,KAAK,WAAW;AACvI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}