{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getMenuItemUtilityClass(slot) {\n  return generateUtilityClass('MuiMenuItem', slot);\n}\nconst menuItemClasses = generateUtilityClasses('MuiMenuItem', ['root', 'focusVisible', 'disabled', 'selected', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantSoft', 'variantOutlined', 'variantSolid']);\nexport default menuItemClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getMenuItemUtilityClass", "slot", "menuItemClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/MenuItem/menuItemClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getMenuItemUtilityClass(slot) {\n  return generateUtilityClass('MuiMenuItem', slot);\n}\nconst menuItemClasses = generateUtilityClasses('MuiMenuItem', ['root', 'focusVisible', 'disabled', 'selected', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantSoft', 'variantOutlined', 'variantSolid']);\nexport default menuItemClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,uBAAuBA,CAACC,IAAI,EAAE;EAC5C,OAAOH,oBAAoB,CAAC,aAAa,EAAEG,IAAI,CAAC;AAClD;AACA,MAAMC,eAAe,GAAGH,sBAAsB,CAAC,aAAa,EAAE,CAAC,MAAM,EAAE,cAAc,EAAE,UAAU,EAAE,UAAU,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,iBAAiB,EAAE,cAAc,CAAC,CAAC;AAChR,eAAeG,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}