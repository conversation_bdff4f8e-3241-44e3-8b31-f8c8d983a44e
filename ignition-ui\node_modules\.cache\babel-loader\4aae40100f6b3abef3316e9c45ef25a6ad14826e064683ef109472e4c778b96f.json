{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getMenuListUtilityClass(slot) {\n  return generateUtilityClass('MuiMenuList', slot);\n}\nconst menuClasses = generateUtilityClasses('MuiMenuList', ['root', 'nested', 'sizeSm', 'sizeMd', 'sizeLg', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid']);\nexport default menuClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getMenuListUtilityClass", "slot", "menuClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/MenuList/menuListClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getMenuListUtilityClass(slot) {\n  return generateUtilityClass('MuiMenuList', slot);\n}\nconst menuClasses = generateUtilityClasses('MuiMenuList', ['root', 'nested', 'sizeSm', 'sizeMd', 'sizeLg', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid']);\nexport default menuClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,uBAAuBA,CAACC,IAAI,EAAE;EAC5C,OAAOH,oBAAoB,CAAC,aAAa,EAAEG,IAAI,CAAC;AAClD;AACA,MAAMC,WAAW,GAAGH,sBAAsB,CAAC,aAAa,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,iBAAiB,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;AAC5Q,eAAeG,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}