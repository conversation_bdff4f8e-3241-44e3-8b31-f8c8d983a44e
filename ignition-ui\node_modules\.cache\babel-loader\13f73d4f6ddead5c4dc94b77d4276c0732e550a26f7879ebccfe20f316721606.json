{"ast": null, "code": "/**\r\n * Prints a warning in the console if it exists.\r\n *\r\n * @param {String} message The warning message.\r\n * @returns {void}\r\n */\nexport default function warning(message) {\n  /* eslint-disable no-console */\n  if (typeof console !== 'undefined' && typeof console.error === 'function') {\n    console.error(message);\n  }\n  /* eslint-enable no-console */\n\n  try {\n    // This error was thrown as a convenience so that if you enable\n    // \"break on all exceptions\" in your console,\n    // it would pause the execution at this line.\n    throw new Error(message);\n    /* eslint-disable no-empty */\n  } catch (e) {}\n  /* eslint-enable no-empty */\n}", "map": {"version": 3, "names": ["warning", "message", "console", "error", "Error", "e"], "sources": ["C:/ignition/ignition-ui/node_modules/react-beautiful-dnd/node_modules/react-redux/es/utils/warning.js"], "sourcesContent": ["/**\r\n * Prints a warning in the console if it exists.\r\n *\r\n * @param {String} message The warning message.\r\n * @returns {void}\r\n */\nexport default function warning(message) {\n  /* eslint-disable no-console */\n  if (typeof console !== 'undefined' && typeof console.error === 'function') {\n    console.error(message);\n  }\n  /* eslint-enable no-console */\n\n\n  try {\n    // This error was thrown as a convenience so that if you enable\n    // \"break on all exceptions\" in your console,\n    // it would pause the execution at this line.\n    throw new Error(message);\n    /* eslint-disable no-empty */\n  } catch (e) {}\n  /* eslint-enable no-empty */\n\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,OAAOA,CAACC,OAAO,EAAE;EACvC;EACA,IAAI,OAAOC,OAAO,KAAK,WAAW,IAAI,OAAOA,OAAO,CAACC,KAAK,KAAK,UAAU,EAAE;IACzED,OAAO,CAACC,KAAK,CAACF,OAAO,CAAC;EACxB;EACA;;EAGA,IAAI;IACF;IACA;IACA;IACA,MAAM,IAAIG,KAAK,CAACH,OAAO,CAAC;IACxB;EACF,CAAC,CAAC,OAAOI,CAAC,EAAE,CAAC;EACb;AAEF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}