{"ast": null, "code": "var _jsxFileName = \"C:\\\\ignition\\\\ignition-ui\\\\src\\\\views\\\\plan\\\\detail\\\\components\\\\ChatbotBar.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { Box, TextField, IconButton, Paper, Typography, Chip, Tooltip, Collapse, CircularProgress } from '@mui/material';\nimport Iconify from 'components/Iconify/index';\nimport { mainYellowColor } from \"helpers/constants\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatbotBar = _ref => {\n  _s();\n  let {\n    planInfo,\n    onPlanUpdate,\n    onSwitchToAgent\n  } = _ref;\n  const [isExpanded, setIsExpanded] = useState(false);\n  const [message, setMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  // Generate contextual suggestions based on project data\n  const generateSuggestions = () => {\n    var _planInfo$milestones;\n    const baseSuggestions = [\"Show project progress\", \"What should I work on next?\", \"Add a new milestone\", \"Help me organize tasks\"];\n    if ((planInfo === null || planInfo === void 0 ? void 0 : (_planInfo$milestones = planInfo.milestones) === null || _planInfo$milestones === void 0 ? void 0 : _planInfo$milestones.length) > 0) {\n      var _milestone$tasks;\n      const milestone = planInfo.milestones[0];\n      if (((_milestone$tasks = milestone.tasks) === null || _milestone$tasks === void 0 ? void 0 : _milestone$tasks.length) > 0) {\n        const incompleteTasks = milestone.tasks.filter(task => task.status !== 'completed');\n        if (incompleteTasks.length > 0) {\n          baseSuggestions.unshift(`Mark \"${incompleteTasks[0].name}\" as completed`);\n        }\n      }\n      baseSuggestions.push(`Add task to \"${milestone.name}\"`);\n    }\n    return baseSuggestions.slice(0, 5); // Limit to 5 suggestions\n  };\n  const [suggestions] = useState(generateSuggestions());\n  const inputRef = useRef(null);\n\n  // Focus input when expanded\n  useEffect(() => {\n    if (isExpanded && inputRef.current) {\n      inputRef.current.focus();\n    }\n  }, [isExpanded]);\n  const handleToggleExpand = () => {\n    setIsExpanded(!isExpanded);\n  };\n  const handleSendMessage = async () => {\n    if (!message.trim() || isLoading) return;\n    console.log('ChatbotBar - planInfo:', planInfo); // Debug log\n    setIsLoading(true);\n    try {\n      // Validate planInfo before proceeding\n      if (!(planInfo !== null && planInfo !== void 0 && planInfo.id)) {\n        console.error('ChatbotBar - Missing planInfo or planInfo.id:', planInfo);\n        return;\n      }\n\n      // Save conversation to localStorage for Agent tab\n      const conversationHistory = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\n      const newConversation = {\n        id: Date.now(),\n        planId: planInfo.id,\n        planName: planInfo.name,\n        message: message.trim(),\n        timestamp: new Date().toISOString(),\n        response: null // Will be filled by AI response\n      };\n      conversationHistory.push(newConversation);\n      localStorage.setItem('agent_conversations', JSON.stringify(conversationHistory));\n      console.log('ChatbotBar - Conversation saved, switching to agent tab'); // Debug log\n\n      // Switch to Agent tab with the conversation\n      if (onSwitchToAgent) {\n        onSwitchToAgent({\n          message: message.trim(),\n          planInfo: planInfo,\n          conversationId: newConversation.id\n        });\n      } else {\n        console.error('ChatbotBar - onSwitchToAgent callback not provided');\n      }\n    } catch (error) {\n      console.error('Error sending message:', error);\n    } finally {\n      setIsLoading(false);\n      setMessage('');\n      setIsExpanded(false);\n    }\n  };\n  const handleKeyPress = e => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n  const handleSuggestionClick = suggestion => {\n    setMessage(suggestion);\n    if (inputRef.current) {\n      inputRef.current.focus();\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Paper, {\n    elevation: 0,\n    sx: {\n      mb: 2,\n      borderRadius: '12px',\n      border: '1px solid #f0f0f0',\n      overflow: 'hidden',\n      transition: 'all 0.3s ease',\n      backgroundColor: '#fafafa'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'space-between',\n        p: 2,\n        cursor: 'pointer',\n        '&:hover': {\n          backgroundColor: '#f5f5f5'\n        }\n      },\n      onClick: handleToggleExpand,\n      children: [/*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 1.5\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            width: 32,\n            height: 32,\n            borderRadius: '50%',\n            backgroundColor: mainYellowColor,\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Iconify, {\n            icon: \"mdi:robot\",\n            width: 18,\n            height: 18,\n            color: \"#fff\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"h6\",\n            sx: {\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              fontWeight: 600,\n              fontSize: '1rem',\n              color: '#333',\n              mb: 0.5\n            },\n            children: \"AI Project Assistant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 162,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            sx: {\n              color: '#666',\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              fontSize: '0.8rem'\n            },\n            children: \"Ask questions or make changes to your project\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 174,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 161,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Chip, {\n          label: \"Beta\",\n          size: \"small\",\n          sx: {\n            backgroundColor: `${mainYellowColor}20`,\n            color: mainYellowColor,\n            fontWeight: 600,\n            fontSize: '0.7rem',\n            height: '20px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 188,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(IconButton, {\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(Iconify, {\n            icon: isExpanded ? \"material-symbols:expand-less\" : \"material-symbols:expand-more\",\n            width: 20,\n            height: 20,\n            color: \"#666\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 200,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 199,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Collapse, {\n      in: isExpanded,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          px: 2,\n          pb: 2\n        },\n        children: [/*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"caption\",\n            sx: {\n              color: '#666',\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              fontSize: '0.8rem',\n              mb: 1,\n              display: 'block'\n            },\n            children: \"Quick suggestions:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              flexWrap: 'wrap',\n              gap: 1\n            },\n            children: suggestions.map((suggestion, index) => /*#__PURE__*/_jsxDEV(Chip, {\n              label: suggestion,\n              size: \"small\",\n              onClick: () => handleSuggestionClick(suggestion),\n              sx: {\n                backgroundColor: '#fff',\n                border: '1px solid #e0e0e0',\n                cursor: 'pointer',\n                fontSize: '0.75rem',\n                '&:hover': {\n                  backgroundColor: `${mainYellowColor}10`,\n                  borderColor: mainYellowColor\n                }\n              }\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            gap: 1,\n            alignItems: 'flex-end'\n          },\n          children: [/*#__PURE__*/_jsxDEV(TextField, {\n            inputRef: inputRef,\n            value: message,\n            onChange: e => setMessage(e.target.value),\n            onKeyPress: handleKeyPress,\n            placeholder: \"Ask me anything about your project or request changes...\",\n            multiline: true,\n            maxRows: 3,\n            fullWidth: true,\n            variant: \"outlined\",\n            disabled: isLoading,\n            sx: {\n              '& .MuiOutlinedInput-root': {\n                borderRadius: '8px',\n                backgroundColor: '#fff',\n                fontFamily: '\"Recursive Variable\", sans-serif',\n                fontSize: '0.9rem'\n              }\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 251,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: \"Send message\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              onClick: handleSendMessage,\n              disabled: !message.trim() || isLoading,\n              sx: {\n                backgroundColor: message.trim() && !isLoading ? mainYellowColor : '#f0f0f0',\n                color: message.trim() && !isLoading ? '#fff' : '#999',\n                '&:hover': {\n                  backgroundColor: message.trim() && !isLoading ? '#E69500' : '#f0f0f0'\n                },\n                mb: 0.5\n              },\n              children: isLoading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n                size: 20,\n                sx: {\n                  color: '#999'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(Iconify, {\n                icon: \"material-symbols:send\",\n                width: 20,\n                height: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 272,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"caption\",\n          sx: {\n            color: '#999',\n            fontFamily: '\"Recursive Variable\", sans-serif',\n            fontSize: '0.75rem',\n            mt: 1,\n            display: 'block'\n          },\n          children: \"\\uD83D\\uDCA1 I can help you mark tasks as complete, add new tasks, update descriptions, and more!\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 211,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 122,\n    columnNumber: 5\n  }, this);\n};\n_s(ChatbotBar, \"YHr0NLgTjLO4vtskngeQu2BMq1o=\");\n_c = ChatbotBar;\nexport default ChatbotBar;\nvar _c;\n$RefreshReg$(_c, \"ChatbotBar\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "Box", "TextField", "IconButton", "Paper", "Typography", "Chip", "<PERSON><PERSON><PERSON>", "Collapse", "CircularProgress", "Iconify", "mainYellowColor", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_ref", "_s", "planInfo", "onPlanUpdate", "onSwitchToAgent", "isExpanded", "setIsExpanded", "message", "setMessage", "isLoading", "setIsLoading", "generateSuggestions", "_planInfo$milestones", "baseSuggestions", "milestones", "length", "_milestone$tasks", "milestone", "tasks", "incompleteTasks", "filter", "task", "status", "unshift", "name", "push", "slice", "suggestions", "inputRef", "current", "focus", "handleToggleExpand", "handleSendMessage", "trim", "console", "log", "id", "error", "conversationHistory", "JSON", "parse", "localStorage", "getItem", "newConversation", "Date", "now", "planId", "planName", "timestamp", "toISOString", "response", "setItem", "stringify", "conversationId", "handleKeyPress", "e", "key", "shift<PERSON>ey", "preventDefault", "handleSuggestionClick", "suggestion", "elevation", "sx", "mb", "borderRadius", "border", "overflow", "transition", "backgroundColor", "children", "display", "alignItems", "justifyContent", "p", "cursor", "onClick", "gap", "width", "height", "icon", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "variant", "fontFamily", "fontWeight", "fontSize", "label", "size", "in", "px", "pb", "flexWrap", "map", "index", "borderColor", "value", "onChange", "target", "onKeyPress", "placeholder", "multiline", "maxRows", "fullWidth", "disabled", "title", "mt", "_c", "$RefreshReg$"], "sources": ["C:/ignition/ignition-ui/src/views/plan/detail/components/ChatbotBar.js"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport {\n  Box,\n  TextField,\n  IconButton,\n  Paper,\n  Typography,\n  Chip,\n  Tooltip,\n  Collapse,\n  CircularProgress\n} from '@mui/material';\nimport Iconify from 'components/Iconify/index';\nimport { mainYellowColor } from \"helpers/constants\";\n\nconst ChatbotBar = ({ planInfo, onPlanUpdate, onSwitchToAgent }) => {\n  const [isExpanded, setIsExpanded] = useState(false);\n  const [message, setMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  // Generate contextual suggestions based on project data\n  const generateSuggestions = () => {\n    const baseSuggestions = [\n      \"Show project progress\",\n      \"What should I work on next?\",\n      \"Add a new milestone\",\n      \"Help me organize tasks\"\n    ];\n\n    if (planInfo?.milestones?.length > 0) {\n      const milestone = planInfo.milestones[0];\n      if (milestone.tasks?.length > 0) {\n        const incompleteTasks = milestone.tasks.filter(task => task.status !== 'completed');\n        if (incompleteTasks.length > 0) {\n          baseSuggestions.unshift(`Mark \"${incompleteTasks[0].name}\" as completed`);\n        }\n      }\n      baseSuggestions.push(`Add task to \"${milestone.name}\"`);\n    }\n\n    return baseSuggestions.slice(0, 5); // Limit to 5 suggestions\n  };\n\n  const [suggestions] = useState(generateSuggestions());\n  \n  const inputRef = useRef(null);\n\n  // Focus input when expanded\n  useEffect(() => {\n    if (isExpanded && inputRef.current) {\n      inputRef.current.focus();\n    }\n  }, [isExpanded]);\n\n  const handleToggleExpand = () => {\n    setIsExpanded(!isExpanded);\n  };\n\n  const handleSendMessage = async () => {\n    if (!message.trim() || isLoading) return;\n\n    console.log('ChatbotBar - planInfo:', planInfo); // Debug log\n    setIsLoading(true);\n\n    try {\n      // Validate planInfo before proceeding\n      if (!planInfo?.id) {\n        console.error('ChatbotBar - Missing planInfo or planInfo.id:', planInfo);\n        return;\n      }\n\n      // Save conversation to localStorage for Agent tab\n      const conversationHistory = JSON.parse(localStorage.getItem('agent_conversations') || '[]');\n      const newConversation = {\n        id: Date.now(),\n        planId: planInfo.id,\n        planName: planInfo.name,\n        message: message.trim(),\n        timestamp: new Date().toISOString(),\n        response: null // Will be filled by AI response\n      };\n\n      conversationHistory.push(newConversation);\n      localStorage.setItem('agent_conversations', JSON.stringify(conversationHistory));\n\n      console.log('ChatbotBar - Conversation saved, switching to agent tab'); // Debug log\n\n      // Switch to Agent tab with the conversation\n      if (onSwitchToAgent) {\n        onSwitchToAgent({\n          message: message.trim(),\n          planInfo: planInfo,\n          conversationId: newConversation.id\n        });\n      } else {\n        console.error('ChatbotBar - onSwitchToAgent callback not provided');\n      }\n\n    } catch (error) {\n      console.error('Error sending message:', error);\n    } finally {\n      setIsLoading(false);\n      setMessage('');\n      setIsExpanded(false);\n    }\n  };\n\n  const handleKeyPress = (e) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      handleSendMessage();\n    }\n  };\n\n  const handleSuggestionClick = (suggestion) => {\n    setMessage(suggestion);\n    if (inputRef.current) {\n      inputRef.current.focus();\n    }\n  };\n\n  return (\n    <Paper\n      elevation={0}\n      sx={{\n        mb: 2,\n        borderRadius: '12px',\n        border: '1px solid #f0f0f0',\n        overflow: 'hidden',\n        transition: 'all 0.3s ease',\n        backgroundColor: '#fafafa'\n      }}\n    >\n      {/* Chatbot Header */}\n      <Box\n        sx={{\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'space-between',\n          p: 2,\n          cursor: 'pointer',\n          '&:hover': {\n            backgroundColor: '#f5f5f5'\n          }\n        }}\n        onClick={handleToggleExpand}\n      >\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1.5 }}>\n          <Box\n            sx={{\n              width: 32,\n              height: 32,\n              borderRadius: '50%',\n              backgroundColor: mainYellowColor,\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center'\n            }}\n          >\n            <Iconify icon=\"mdi:robot\" width={18} height={18} color=\"#fff\" />\n          </Box>\n          <Box>\n            <Typography\n              variant=\"h6\"\n              sx={{\n                fontFamily: '\"Recursive Variable\", sans-serif',\n                fontWeight: 600,\n                fontSize: '1rem',\n                color: '#333',\n                mb: 0.5\n              }}\n            >\n              AI Project Assistant\n            </Typography>\n            <Typography\n              variant=\"caption\"\n              sx={{\n                color: '#666',\n                fontFamily: '\"Recursive Variable\", sans-serif',\n                fontSize: '0.8rem'\n              }}\n            >\n              Ask questions or make changes to your project\n            </Typography>\n          </Box>\n        </Box>\n\n        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>\n          <Chip\n            label=\"Beta\"\n            size=\"small\"\n            sx={{\n              backgroundColor: `${mainYellowColor}20`,\n              color: mainYellowColor,\n              fontWeight: 600,\n              fontSize: '0.7rem',\n              height: '20px'\n            }}\n          />\n          <IconButton size=\"small\">\n            <Iconify \n              icon={isExpanded ? \"material-symbols:expand-less\" : \"material-symbols:expand-more\"} \n              width={20} \n              height={20} \n              color=\"#666\"\n            />\n          </IconButton>\n        </Box>\n      </Box>\n\n      {/* Expanded Content */}\n      <Collapse in={isExpanded}>\n        <Box sx={{ px: 2, pb: 2 }}>\n          {/* Quick Suggestions */}\n          <Box sx={{ mb: 2 }}>\n            <Typography\n              variant=\"caption\"\n              sx={{\n                color: '#666',\n                fontFamily: '\"Recursive Variable\", sans-serif',\n                fontSize: '0.8rem',\n                mb: 1,\n                display: 'block'\n              }}\n            >\n              Quick suggestions:\n            </Typography>\n            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>\n              {suggestions.map((suggestion, index) => (\n                <Chip\n                  key={index}\n                  label={suggestion}\n                  size=\"small\"\n                  onClick={() => handleSuggestionClick(suggestion)}\n                  sx={{\n                    backgroundColor: '#fff',\n                    border: '1px solid #e0e0e0',\n                    cursor: 'pointer',\n                    fontSize: '0.75rem',\n                    '&:hover': {\n                      backgroundColor: `${mainYellowColor}10`,\n                      borderColor: mainYellowColor\n                    }\n                  }}\n                />\n              ))}\n            </Box>\n          </Box>\n\n          {/* Input Field */}\n          <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>\n            <TextField\n              inputRef={inputRef}\n              value={message}\n              onChange={(e) => setMessage(e.target.value)}\n              onKeyPress={handleKeyPress}\n              placeholder=\"Ask me anything about your project or request changes...\"\n              multiline\n              maxRows={3}\n              fullWidth\n              variant=\"outlined\"\n              disabled={isLoading}\n              sx={{\n                '& .MuiOutlinedInput-root': {\n                  borderRadius: '8px',\n                  backgroundColor: '#fff',\n                  fontFamily: '\"Recursive Variable\", sans-serif',\n                  fontSize: '0.9rem'\n                }\n              }}\n            />\n            <Tooltip title=\"Send message\">\n              <IconButton\n                onClick={handleSendMessage}\n                disabled={!message.trim() || isLoading}\n                sx={{\n                  backgroundColor: message.trim() && !isLoading ? mainYellowColor : '#f0f0f0',\n                  color: message.trim() && !isLoading ? '#fff' : '#999',\n                  '&:hover': {\n                    backgroundColor: message.trim() && !isLoading ? '#E69500' : '#f0f0f0'\n                  },\n                  mb: 0.5\n                }}\n              >\n                {isLoading ? (\n                  <CircularProgress size={20} sx={{ color: '#999' }} />\n                ) : (\n                  <Iconify icon=\"material-symbols:send\" width={20} height={20} />\n                )}\n              </IconButton>\n            </Tooltip>\n          </Box>\n\n          {/* Helper Text */}\n          <Typography\n            variant=\"caption\"\n            sx={{\n              color: '#999',\n              fontFamily: '\"Recursive Variable\", sans-serif',\n              fontSize: '0.75rem',\n              mt: 1,\n              display: 'block'\n            }}\n          >\n            💡 I can help you mark tasks as complete, add new tasks, update descriptions, and more!\n          </Typography>\n        </Box>\n      </Collapse>\n    </Paper>\n  );\n};\n\nexport default ChatbotBar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,SACEC,GAAG,EACHC,SAAS,EACTC,UAAU,EACVC,KAAK,EACLC,UAAU,EACVC,IAAI,EACJC,OAAO,EACPC,QAAQ,EACRC,gBAAgB,QACX,eAAe;AACtB,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,SAASC,eAAe,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,UAAU,GAAGC,IAAA,IAAiD;EAAAC,EAAA;EAAA,IAAhD;IAAEC,QAAQ;IAAEC,YAAY;IAAEC;EAAgB,CAAC,GAAAJ,IAAA;EAC7D,MAAM,CAACK,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACwB,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAAC0B,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACjD;EACA,MAAM4B,mBAAmB,GAAGA,CAAA,KAAM;IAAA,IAAAC,oBAAA;IAChC,MAAMC,eAAe,GAAG,CACtB,uBAAuB,EACvB,6BAA6B,EAC7B,qBAAqB,EACrB,wBAAwB,CACzB;IAED,IAAI,CAAAX,QAAQ,aAARA,QAAQ,wBAAAU,oBAAA,GAARV,QAAQ,CAAEY,UAAU,cAAAF,oBAAA,uBAApBA,oBAAA,CAAsBG,MAAM,IAAG,CAAC,EAAE;MAAA,IAAAC,gBAAA;MACpC,MAAMC,SAAS,GAAGf,QAAQ,CAACY,UAAU,CAAC,CAAC,CAAC;MACxC,IAAI,EAAAE,gBAAA,GAAAC,SAAS,CAACC,KAAK,cAAAF,gBAAA,uBAAfA,gBAAA,CAAiBD,MAAM,IAAG,CAAC,EAAE;QAC/B,MAAMI,eAAe,GAAGF,SAAS,CAACC,KAAK,CAACE,MAAM,CAACC,IAAI,IAAIA,IAAI,CAACC,MAAM,KAAK,WAAW,CAAC;QACnF,IAAIH,eAAe,CAACJ,MAAM,GAAG,CAAC,EAAE;UAC9BF,eAAe,CAACU,OAAO,CAAC,SAASJ,eAAe,CAAC,CAAC,CAAC,CAACK,IAAI,gBAAgB,CAAC;QAC3E;MACF;MACAX,eAAe,CAACY,IAAI,CAAC,gBAAgBR,SAAS,CAACO,IAAI,GAAG,CAAC;IACzD;IAEA,OAAOX,eAAe,CAACa,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACtC,CAAC;EAED,MAAM,CAACC,WAAW,CAAC,GAAG5C,QAAQ,CAAC4B,mBAAmB,CAAC,CAAC,CAAC;EAErD,MAAMiB,QAAQ,GAAG5C,MAAM,CAAC,IAAI,CAAC;;EAE7B;EACAC,SAAS,CAAC,MAAM;IACd,IAAIoB,UAAU,IAAIuB,QAAQ,CAACC,OAAO,EAAE;MAClCD,QAAQ,CAACC,OAAO,CAACC,KAAK,CAAC,CAAC;IAC1B;EACF,CAAC,EAAE,CAACzB,UAAU,CAAC,CAAC;EAEhB,MAAM0B,kBAAkB,GAAGA,CAAA,KAAM;IAC/BzB,aAAa,CAAC,CAACD,UAAU,CAAC;EAC5B,CAAC;EAED,MAAM2B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI,CAACzB,OAAO,CAAC0B,IAAI,CAAC,CAAC,IAAIxB,SAAS,EAAE;IAElCyB,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEjC,QAAQ,CAAC,CAAC,CAAC;IACjDQ,YAAY,CAAC,IAAI,CAAC;IAElB,IAAI;MACF;MACA,IAAI,EAACR,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAEkC,EAAE,GAAE;QACjBF,OAAO,CAACG,KAAK,CAAC,+CAA+C,EAAEnC,QAAQ,CAAC;QACxE;MACF;;MAEA;MACA,MAAMoC,mBAAmB,GAAGC,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAAC,qBAAqB,CAAC,IAAI,IAAI,CAAC;MAC3F,MAAMC,eAAe,GAAG;QACtBP,EAAE,EAAEQ,IAAI,CAACC,GAAG,CAAC,CAAC;QACdC,MAAM,EAAE5C,QAAQ,CAACkC,EAAE;QACnBW,QAAQ,EAAE7C,QAAQ,CAACsB,IAAI;QACvBjB,OAAO,EAAEA,OAAO,CAAC0B,IAAI,CAAC,CAAC;QACvBe,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC;QACnCC,QAAQ,EAAE,IAAI,CAAC;MACjB,CAAC;MAEDZ,mBAAmB,CAACb,IAAI,CAACkB,eAAe,CAAC;MACzCF,YAAY,CAACU,OAAO,CAAC,qBAAqB,EAAEZ,IAAI,CAACa,SAAS,CAACd,mBAAmB,CAAC,CAAC;MAEhFJ,OAAO,CAACC,GAAG,CAAC,yDAAyD,CAAC,CAAC,CAAC;;MAExE;MACA,IAAI/B,eAAe,EAAE;QACnBA,eAAe,CAAC;UACdG,OAAO,EAAEA,OAAO,CAAC0B,IAAI,CAAC,CAAC;UACvB/B,QAAQ,EAAEA,QAAQ;UAClBmD,cAAc,EAAEV,eAAe,CAACP;QAClC,CAAC,CAAC;MACJ,CAAC,MAAM;QACLF,OAAO,CAACG,KAAK,CAAC,oDAAoD,CAAC;MACrE;IAEF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAChD,CAAC,SAAS;MACR3B,YAAY,CAAC,KAAK,CAAC;MACnBF,UAAU,CAAC,EAAE,CAAC;MACdF,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMgD,cAAc,GAAIC,CAAC,IAAK;IAC5B,IAAIA,CAAC,CAACC,GAAG,KAAK,OAAO,IAAI,CAACD,CAAC,CAACE,QAAQ,EAAE;MACpCF,CAAC,CAACG,cAAc,CAAC,CAAC;MAClB1B,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC;EAED,MAAM2B,qBAAqB,GAAIC,UAAU,IAAK;IAC5CpD,UAAU,CAACoD,UAAU,CAAC;IACtB,IAAIhC,QAAQ,CAACC,OAAO,EAAE;MACpBD,QAAQ,CAACC,OAAO,CAACC,KAAK,CAAC,CAAC;IAC1B;EACF,CAAC;EAED,oBACEhC,OAAA,CAACT,KAAK;IACJwE,SAAS,EAAE,CAAE;IACbC,EAAE,EAAE;MACFC,EAAE,EAAE,CAAC;MACLC,YAAY,EAAE,MAAM;MACpBC,MAAM,EAAE,mBAAmB;MAC3BC,QAAQ,EAAE,QAAQ;MAClBC,UAAU,EAAE,eAAe;MAC3BC,eAAe,EAAE;IACnB,CAAE;IAAAC,QAAA,gBAGFvE,OAAA,CAACZ,GAAG;MACF4E,EAAE,EAAE;QACFQ,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,eAAe;QAC/BC,CAAC,EAAE,CAAC;QACJC,MAAM,EAAE,SAAS;QACjB,SAAS,EAAE;UACTN,eAAe,EAAE;QACnB;MACF,CAAE;MACFO,OAAO,EAAE5C,kBAAmB;MAAAsC,QAAA,gBAE5BvE,OAAA,CAACZ,GAAG;QAAC4E,EAAE,EAAE;UAAEQ,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEK,GAAG,EAAE;QAAI,CAAE;QAAAP,QAAA,gBAC3DvE,OAAA,CAACZ,GAAG;UACF4E,EAAE,EAAE;YACFe,KAAK,EAAE,EAAE;YACTC,MAAM,EAAE,EAAE;YACVd,YAAY,EAAE,KAAK;YACnBI,eAAe,EAAExE,eAAe;YAChC0E,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBC,cAAc,EAAE;UAClB,CAAE;UAAAH,QAAA,eAEFvE,OAAA,CAACH,OAAO;YAACoF,IAAI,EAAC,WAAW;YAACF,KAAK,EAAE,EAAG;YAACC,MAAM,EAAE,EAAG;YAACE,KAAK,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7D,CAAC,eACNtF,OAAA,CAACZ,GAAG;UAAAmF,QAAA,gBACFvE,OAAA,CAACR,UAAU;YACT+F,OAAO,EAAC,IAAI;YACZvB,EAAE,EAAE;cACFwB,UAAU,EAAE,kCAAkC;cAC9CC,UAAU,EAAE,GAAG;cACfC,QAAQ,EAAE,MAAM;cAChBR,KAAK,EAAE,MAAM;cACbjB,EAAE,EAAE;YACN,CAAE;YAAAM,QAAA,EACH;UAED;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbtF,OAAA,CAACR,UAAU;YACT+F,OAAO,EAAC,SAAS;YACjBvB,EAAE,EAAE;cACFkB,KAAK,EAAE,MAAM;cACbM,UAAU,EAAE,kCAAkC;cAC9CE,QAAQ,EAAE;YACZ,CAAE;YAAAnB,QAAA,EACH;UAED;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACV,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENtF,OAAA,CAACZ,GAAG;QAAC4E,EAAE,EAAE;UAAEQ,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEK,GAAG,EAAE;QAAE,CAAE;QAAAP,QAAA,gBACzDvE,OAAA,CAACP,IAAI;UACHkG,KAAK,EAAC,MAAM;UACZC,IAAI,EAAC,OAAO;UACZ5B,EAAE,EAAE;YACFM,eAAe,EAAE,GAAGxE,eAAe,IAAI;YACvCoF,KAAK,EAAEpF,eAAe;YACtB2F,UAAU,EAAE,GAAG;YACfC,QAAQ,EAAE,QAAQ;YAClBV,MAAM,EAAE;UACV;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFtF,OAAA,CAACV,UAAU;UAACsG,IAAI,EAAC,OAAO;UAAArB,QAAA,eACtBvE,OAAA,CAACH,OAAO;YACNoF,IAAI,EAAE1E,UAAU,GAAG,8BAA8B,GAAG,8BAA+B;YACnFwE,KAAK,EAAE,EAAG;YACVC,MAAM,EAAE,EAAG;YACXE,KAAK,EAAC;UAAM;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtF,OAAA,CAACL,QAAQ;MAACkG,EAAE,EAAEtF,UAAW;MAAAgE,QAAA,eACvBvE,OAAA,CAACZ,GAAG;QAAC4E,EAAE,EAAE;UAAE8B,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAxB,QAAA,gBAExBvE,OAAA,CAACZ,GAAG;UAAC4E,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAM,QAAA,gBACjBvE,OAAA,CAACR,UAAU;YACT+F,OAAO,EAAC,SAAS;YACjBvB,EAAE,EAAE;cACFkB,KAAK,EAAE,MAAM;cACbM,UAAU,EAAE,kCAAkC;cAC9CE,QAAQ,EAAE,QAAQ;cAClBzB,EAAE,EAAE,CAAC;cACLO,OAAO,EAAE;YACX,CAAE;YAAAD,QAAA,EACH;UAED;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACbtF,OAAA,CAACZ,GAAG;YAAC4E,EAAE,EAAE;cAAEQ,OAAO,EAAE,MAAM;cAAEwB,QAAQ,EAAE,MAAM;cAAElB,GAAG,EAAE;YAAE,CAAE;YAAAP,QAAA,EACpD1C,WAAW,CAACoE,GAAG,CAAC,CAACnC,UAAU,EAAEoC,KAAK,kBACjClG,OAAA,CAACP,IAAI;cAEHkG,KAAK,EAAE7B,UAAW;cAClB8B,IAAI,EAAC,OAAO;cACZf,OAAO,EAAEA,CAAA,KAAMhB,qBAAqB,CAACC,UAAU,CAAE;cACjDE,EAAE,EAAE;gBACFM,eAAe,EAAE,MAAM;gBACvBH,MAAM,EAAE,mBAAmB;gBAC3BS,MAAM,EAAE,SAAS;gBACjBc,QAAQ,EAAE,SAAS;gBACnB,SAAS,EAAE;kBACTpB,eAAe,EAAE,GAAGxE,eAAe,IAAI;kBACvCqG,WAAW,EAAErG;gBACf;cACF;YAAE,GAbGoG,KAAK;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAcX,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNtF,OAAA,CAACZ,GAAG;UAAC4E,EAAE,EAAE;YAAEQ,OAAO,EAAE,MAAM;YAAEM,GAAG,EAAE,CAAC;YAAEL,UAAU,EAAE;UAAW,CAAE;UAAAF,QAAA,gBAC3DvE,OAAA,CAACX,SAAS;YACRyC,QAAQ,EAAEA,QAAS;YACnBsE,KAAK,EAAE3F,OAAQ;YACf4F,QAAQ,EAAG5C,CAAC,IAAK/C,UAAU,CAAC+C,CAAC,CAAC6C,MAAM,CAACF,KAAK,CAAE;YAC5CG,UAAU,EAAE/C,cAAe;YAC3BgD,WAAW,EAAC,0DAA0D;YACtEC,SAAS;YACTC,OAAO,EAAE,CAAE;YACXC,SAAS;YACTpB,OAAO,EAAC,UAAU;YAClBqB,QAAQ,EAAEjG,SAAU;YACpBqD,EAAE,EAAE;cACF,0BAA0B,EAAE;gBAC1BE,YAAY,EAAE,KAAK;gBACnBI,eAAe,EAAE,MAAM;gBACvBkB,UAAU,EAAE,kCAAkC;gBAC9CE,QAAQ,EAAE;cACZ;YACF;UAAE;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACFtF,OAAA,CAACN,OAAO;YAACmH,KAAK,EAAC,cAAc;YAAAtC,QAAA,eAC3BvE,OAAA,CAACV,UAAU;cACTuF,OAAO,EAAE3C,iBAAkB;cAC3B0E,QAAQ,EAAE,CAACnG,OAAO,CAAC0B,IAAI,CAAC,CAAC,IAAIxB,SAAU;cACvCqD,EAAE,EAAE;gBACFM,eAAe,EAAE7D,OAAO,CAAC0B,IAAI,CAAC,CAAC,IAAI,CAACxB,SAAS,GAAGb,eAAe,GAAG,SAAS;gBAC3EoF,KAAK,EAAEzE,OAAO,CAAC0B,IAAI,CAAC,CAAC,IAAI,CAACxB,SAAS,GAAG,MAAM,GAAG,MAAM;gBACrD,SAAS,EAAE;kBACT2D,eAAe,EAAE7D,OAAO,CAAC0B,IAAI,CAAC,CAAC,IAAI,CAACxB,SAAS,GAAG,SAAS,GAAG;gBAC9D,CAAC;gBACDsD,EAAE,EAAE;cACN,CAAE;cAAAM,QAAA,EAED5D,SAAS,gBACRX,OAAA,CAACJ,gBAAgB;gBAACgG,IAAI,EAAE,EAAG;gBAAC5B,EAAE,EAAE;kBAAEkB,KAAK,EAAE;gBAAO;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,gBAErDtF,OAAA,CAACH,OAAO;gBAACoF,IAAI,EAAC,uBAAuB;gBAACF,KAAK,EAAE,EAAG;gBAACC,MAAM,EAAE;cAAG;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAC/D;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACS;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eAGNtF,OAAA,CAACR,UAAU;UACT+F,OAAO,EAAC,SAAS;UACjBvB,EAAE,EAAE;YACFkB,KAAK,EAAE,MAAM;YACbM,UAAU,EAAE,kCAAkC;YAC9CE,QAAQ,EAAE,SAAS;YACnBoB,EAAE,EAAE,CAAC;YACLtC,OAAO,EAAE;UACX,CAAE;UAAAD,QAAA,EACH;QAED;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEZ,CAAC;AAACnF,EAAA,CAtSIF,UAAU;AAAA8G,EAAA,GAAV9G,UAAU;AAwShB,eAAeA,UAAU;AAAC,IAAA8G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}