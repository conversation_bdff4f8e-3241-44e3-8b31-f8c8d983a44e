{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"color\", \"component\", \"size\", \"variant\", \"children\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport { useThemeProps } from '../styles';\nimport styled from '../styles/styled';\nimport { getAvatarGroupUtilityClass } from './avatarGroupClasses';\nimport useSlot from '../utils/useSlot';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const AvatarGroupContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== 'production') {\n  AvatarGroupContext.displayName = 'AvatarGroupContext';\n}\nconst useUtilityClasses = () => {\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getAvatarGroupUtilityClass, {});\n};\nconst AvatarGroupGroupRoot = styled('div', {\n  name: 'JoyAvatarGroup',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(_ref => {\n  let {\n    ownerState,\n    theme\n  } = _ref;\n  return _extends({}, ownerState.size === 'sm' && {\n    '--AvatarGroup-gap': '-0.375rem',\n    '--Avatar-ringSize': '2px'\n  }, ownerState.size === 'md' && {\n    '--AvatarGroup-gap': '-0.5rem',\n    '--Avatar-ringSize': '2px'\n  }, ownerState.size === 'lg' && {\n    '--AvatarGroup-gap': '-0.625rem',\n    '--Avatar-ringSize': '4px'\n  }, {\n    '--Avatar-ring': `0 0 0 var(--Avatar-ringSize) var(--Avatar-ringColor, ${theme.vars.palette.background.surface})`,\n    '--Avatar-marginInlineStart': 'var(--AvatarGroup-gap)',\n    display: 'flex',\n    marginInlineStart: 'calc(-1 * var(--AvatarGroup-gap))'\n  });\n});\n\n/**\n *\n * Demos:\n *\n * - [Avatar](https://mui.com/joy-ui/react-avatar/)\n *\n * API:\n *\n * - [AvatarGroup API](https://mui.com/joy-ui/api/avatar-group/)\n */\nconst AvatarGroup = /*#__PURE__*/React.forwardRef(function AvatarGroup(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyAvatarGroup'\n  });\n  const {\n      className,\n      color,\n      component = 'div',\n      size = 'md',\n      variant,\n      children,\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = React.useMemo(() => _extends({}, props, {\n    color,\n    component,\n    size,\n    variant\n  }), [color, component, props, size, variant]);\n  const classes = useUtilityClasses();\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    className: clsx(classes.root, className),\n    elementType: AvatarGroupGroupRoot,\n    externalForwardedProps: _extends({}, other, {\n      component,\n      slots,\n      slotProps\n    }),\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(AvatarGroupContext.Provider, {\n    value: ownerState,\n    children: /*#__PURE__*/_jsx(SlotRoot, _extends({}, rootProps, {\n      children: children\n    }))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? AvatarGroup.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Used to render icon or text elements inside the AvatarGroup if `src` is not set.\n   * This can be an element, or just a string.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component. It supports those theme colors that make sense for this component.\n   * @default 'neutral'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['danger', 'neutral', 'primary', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The size of the component.\n   * It accepts theme values between 'sm' and 'lg'.\n   * @default 'md'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['lg', 'md', 'sm']), PropTypes.string]),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The [global variant](https://mui.com/joy-ui/main-features/global-variants/) to use.\n   * @default 'soft'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['outlined', 'plain', 'soft', 'solid']), PropTypes.string])\n} : void 0;\nexport default AvatarGroup;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "clsx", "PropTypes", "unstable_composeClasses", "composeClasses", "useThemeProps", "styled", "getAvatarGroupUtilityClass", "useSlot", "jsx", "_jsx", "AvatarGroupContext", "createContext", "undefined", "process", "env", "NODE_ENV", "displayName", "useUtilityClasses", "slots", "root", "AvatarGroupGroupRoot", "name", "slot", "overridesResolver", "props", "styles", "_ref", "ownerState", "theme", "size", "vars", "palette", "background", "surface", "display", "marginInlineStart", "AvatarGroup", "forwardRef", "inProps", "ref", "className", "color", "component", "variant", "children", "slotProps", "other", "useMemo", "classes", "SlotRoot", "rootProps", "elementType", "externalForwardedProps", "Provider", "value", "propTypes", "node", "string", "oneOfType", "oneOf", "shape", "func", "object", "sx", "arrayOf", "bool"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/AvatarGroup/AvatarGroup.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"color\", \"component\", \"size\", \"variant\", \"children\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport { useThemeProps } from '../styles';\nimport styled from '../styles/styled';\nimport { getAvatarGroupUtilityClass } from './avatarGroupClasses';\nimport useSlot from '../utils/useSlot';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport const AvatarGroupContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== 'production') {\n  AvatarGroupContext.displayName = 'AvatarGroupContext';\n}\nconst useUtilityClasses = () => {\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getAvatarGroupUtilityClass, {});\n};\nconst AvatarGroupGroupRoot = styled('div', {\n  name: 'JoyAvatarGroup',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(({\n  ownerState,\n  theme\n}) => _extends({}, ownerState.size === 'sm' && {\n  '--AvatarGroup-gap': '-0.375rem',\n  '--Avatar-ringSize': '2px'\n}, ownerState.size === 'md' && {\n  '--AvatarGroup-gap': '-0.5rem',\n  '--Avatar-ringSize': '2px'\n}, ownerState.size === 'lg' && {\n  '--AvatarGroup-gap': '-0.625rem',\n  '--Avatar-ringSize': '4px'\n}, {\n  '--Avatar-ring': `0 0 0 var(--Avatar-ringSize) var(--Avatar-ringColor, ${theme.vars.palette.background.surface})`,\n  '--Avatar-marginInlineStart': 'var(--AvatarGroup-gap)',\n  display: 'flex',\n  marginInlineStart: 'calc(-1 * var(--AvatarGroup-gap))'\n}));\n\n/**\n *\n * Demos:\n *\n * - [Avatar](https://mui.com/joy-ui/react-avatar/)\n *\n * API:\n *\n * - [AvatarGroup API](https://mui.com/joy-ui/api/avatar-group/)\n */\nconst AvatarGroup = /*#__PURE__*/React.forwardRef(function AvatarGroup(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyAvatarGroup'\n  });\n  const {\n      className,\n      color,\n      component = 'div',\n      size = 'md',\n      variant,\n      children,\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = React.useMemo(() => _extends({}, props, {\n    color,\n    component,\n    size,\n    variant\n  }), [color, component, props, size, variant]);\n  const classes = useUtilityClasses();\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    className: clsx(classes.root, className),\n    elementType: AvatarGroupGroupRoot,\n    externalForwardedProps: _extends({}, other, {\n      component,\n      slots,\n      slotProps\n    }),\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(AvatarGroupContext.Provider, {\n    value: ownerState,\n    children: /*#__PURE__*/_jsx(SlotRoot, _extends({}, rootProps, {\n      children: children\n    }))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? AvatarGroup.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Used to render icon or text elements inside the AvatarGroup if `src` is not set.\n   * This can be an element, or just a string.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component. It supports those theme colors that make sense for this component.\n   * @default 'neutral'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['danger', 'neutral', 'primary', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The size of the component.\n   * It accepts theme values between 'sm' and 'lg'.\n   * @default 'md'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['lg', 'md', 'sm']), PropTypes.string]),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The [global variant](https://mui.com/joy-ui/main-features/global-variants/) to use.\n   * @default 'soft'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['outlined', 'plain', 'soft', 'solid']), PropTypes.string])\n} : void 0;\nexport default AvatarGroup;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,OAAO,EAAE,WAAW,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,CAAC;AAC1G,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,WAAW;AACrE,SAASC,aAAa,QAAQ,WAAW;AACzC,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,0BAA0B,QAAQ,sBAAsB;AACjE,OAAOC,OAAO,MAAM,kBAAkB;AACtC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,OAAO,MAAMC,kBAAkB,GAAG,aAAaX,KAAK,CAACY,aAAa,CAACC,SAAS,CAAC;AAC7E,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCL,kBAAkB,CAACM,WAAW,GAAG,oBAAoB;AACvD;AACA,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAC9B,MAAMC,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,OAAOhB,cAAc,CAACe,KAAK,EAAEZ,0BAA0B,EAAE,CAAC,CAAC,CAAC;AAC9D,CAAC;AACD,MAAMc,oBAAoB,GAAGf,MAAM,CAAC,KAAK,EAAE;EACzCgB,IAAI,EAAE,gBAAgB;EACtBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC/C,CAAC,CAAC,CAACO,IAAA;EAAA,IAAC;IACFC,UAAU;IACVC;EACF,CAAC,GAAAF,IAAA;EAAA,OAAK7B,QAAQ,CAAC,CAAC,CAAC,EAAE8B,UAAU,CAACE,IAAI,KAAK,IAAI,IAAI;IAC7C,mBAAmB,EAAE,WAAW;IAChC,mBAAmB,EAAE;EACvB,CAAC,EAAEF,UAAU,CAACE,IAAI,KAAK,IAAI,IAAI;IAC7B,mBAAmB,EAAE,SAAS;IAC9B,mBAAmB,EAAE;EACvB,CAAC,EAAEF,UAAU,CAACE,IAAI,KAAK,IAAI,IAAI;IAC7B,mBAAmB,EAAE,WAAW;IAChC,mBAAmB,EAAE;EACvB,CAAC,EAAE;IACD,eAAe,EAAE,wDAAwDD,KAAK,CAACE,IAAI,CAACC,OAAO,CAACC,UAAU,CAACC,OAAO,GAAG;IACjH,4BAA4B,EAAE,wBAAwB;IACtDC,OAAO,EAAE,MAAM;IACfC,iBAAiB,EAAE;EACrB,CAAC,CAAC;AAAA,EAAC;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,GAAG,aAAarC,KAAK,CAACsC,UAAU,CAAC,SAASD,WAAWA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACnF,MAAMf,KAAK,GAAGpB,aAAa,CAAC;IAC1BoB,KAAK,EAAEc,OAAO;IACdjB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFmB,SAAS;MACTC,KAAK;MACLC,SAAS,GAAG,KAAK;MACjBb,IAAI,GAAG,IAAI;MACXc,OAAO;MACPC,QAAQ;MACR1B,KAAK,GAAG,CAAC,CAAC;MACV2B,SAAS,GAAG,CAAC;IACf,CAAC,GAAGrB,KAAK;IACTsB,KAAK,GAAGlD,6BAA6B,CAAC4B,KAAK,EAAE1B,SAAS,CAAC;EACzD,MAAM6B,UAAU,GAAG5B,KAAK,CAACgD,OAAO,CAAC,MAAMlD,QAAQ,CAAC,CAAC,CAAC,EAAE2B,KAAK,EAAE;IACzDiB,KAAK;IACLC,SAAS;IACTb,IAAI;IACJc;EACF,CAAC,CAAC,EAAE,CAACF,KAAK,EAAEC,SAAS,EAAElB,KAAK,EAAEK,IAAI,EAAEc,OAAO,CAAC,CAAC;EAC7C,MAAMK,OAAO,GAAG/B,iBAAiB,CAAC,CAAC;EACnC,MAAM,CAACgC,QAAQ,EAAEC,SAAS,CAAC,GAAG3C,OAAO,CAAC,MAAM,EAAE;IAC5CgC,GAAG;IACHC,SAAS,EAAExC,IAAI,CAACgD,OAAO,CAAC7B,IAAI,EAAEqB,SAAS,CAAC;IACxCW,WAAW,EAAE/B,oBAAoB;IACjCgC,sBAAsB,EAAEvD,QAAQ,CAAC,CAAC,CAAC,EAAEiD,KAAK,EAAE;MAC1CJ,SAAS;MACTxB,KAAK;MACL2B;IACF,CAAC,CAAC;IACFlB;EACF,CAAC,CAAC;EACF,OAAO,aAAalB,IAAI,CAACC,kBAAkB,CAAC2C,QAAQ,EAAE;IACpDC,KAAK,EAAE3B,UAAU;IACjBiB,QAAQ,EAAE,aAAanC,IAAI,CAACwC,QAAQ,EAAEpD,QAAQ,CAAC,CAAC,CAAC,EAAEqD,SAAS,EAAE;MAC5DN,QAAQ,EAAEA;IACZ,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACF/B,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGqB,WAAW,CAACmB,SAAS,CAAC,yBAAyB;EACrF;EACA;EACA;EACA;EACA;AACF;AACA;AACA;EACEX,QAAQ,EAAE3C,SAAS,CAACuD,IAAI;EACxB;AACF;AACA;EACEhB,SAAS,EAAEvC,SAAS,CAACwD,MAAM;EAC3B;AACF;AACA;AACA;EACEhB,KAAK,EAAExC,SAAS,CAAC,sCAAsCyD,SAAS,CAAC,CAACzD,SAAS,CAAC0D,KAAK,CAAC,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,EAAE1D,SAAS,CAACwD,MAAM,CAAC,CAAC;EAC7J;AACF;AACA;AACA;EACEf,SAAS,EAAEzC,SAAS,CAACkD,WAAW;EAChC;AACF;AACA;AACA;AACA;EACEtB,IAAI,EAAE5B,SAAS,CAAC,sCAAsCyD,SAAS,CAAC,CAACzD,SAAS,CAAC0D,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAE1D,SAAS,CAACwD,MAAM,CAAC,CAAC;EACxH;AACF;AACA;AACA;EACEZ,SAAS,EAAE5C,SAAS,CAAC2D,KAAK,CAAC;IACzBzC,IAAI,EAAElB,SAAS,CAACyD,SAAS,CAAC,CAACzD,SAAS,CAAC4D,IAAI,EAAE5D,SAAS,CAAC6D,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;EACE5C,KAAK,EAAEjB,SAAS,CAAC2D,KAAK,CAAC;IACrBzC,IAAI,EAAElB,SAAS,CAACkD;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACEY,EAAE,EAAE9D,SAAS,CAACyD,SAAS,CAAC,CAACzD,SAAS,CAAC+D,OAAO,CAAC/D,SAAS,CAACyD,SAAS,CAAC,CAACzD,SAAS,CAAC4D,IAAI,EAAE5D,SAAS,CAAC6D,MAAM,EAAE7D,SAAS,CAACgE,IAAI,CAAC,CAAC,CAAC,EAAEhE,SAAS,CAAC4D,IAAI,EAAE5D,SAAS,CAAC6D,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;EACEnB,OAAO,EAAE1C,SAAS,CAAC,sCAAsCyD,SAAS,CAAC,CAACzD,SAAS,CAAC0D,KAAK,CAAC,CAAC,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,EAAE1D,SAAS,CAACwD,MAAM,CAAC;AAChJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAerB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}