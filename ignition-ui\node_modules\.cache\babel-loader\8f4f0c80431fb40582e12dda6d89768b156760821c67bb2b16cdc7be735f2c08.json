{"ast": null, "code": "var _jsxFileName = \"C:\\\\ignition\\\\ignition-ui\\\\src\\\\components\\\\Loading\\\\HexagonBallLoading.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { Box } from '@mui/material';\nimport Matter from 'matter-js';\nimport styles from './styles.module.scss';\n\n/**\r\n * Component displays loading screen with a bouncing ball inside a rotating hexagon\r\n * @param {Object} props - Component props\r\n * @param {boolean} props.fromCreatePlan - Flag to determine if component is called from CreatePlan page\r\n * @returns {JSX.Element} - HexagonBallLoading component\r\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HexagonBallLoading = _ref => {\n  _s();\n  let {\n    fromCreatePlan\n  } = _ref;\n  const canvasRef = useRef(null);\n  const engineRef = useRef(null);\n  const requestRef = useRef(null);\n  const ballRef = useRef(null);\n  const hexagonEdgesRef = useRef([]);\n  useEffect(() => {\n    // Realistic physics parameters\n    const rotationSpeed = 0.012; // Slower, more natural rotation\n    const gravity = 0.0001; // More realistic gravity strength\n    const ballRestitution = 1; // Realistic bounce with energy loss\n    const wallRestitution = 0; // Walls absorb some energy\n\n    // Initialize Matter.js modules\n    const Engine = Matter.Engine;\n    const Render = Matter.Render;\n    const World = Matter.World;\n    const Bodies = Matter.Bodies;\n    const Body = Matter.Body;\n\n    // Create engine with realistic physics\n    engineRef.current = Engine.create({\n      gravity: {\n        x: 0,\n        y: gravity,\n        scale: 1\n      }\n    });\n\n    // Set realistic timing for physics simulation\n    engineRef.current.timing.timeScale = 1;\n\n    // Create renderer\n    const render = Render.create({\n      canvas: canvasRef.current,\n      engine: engineRef.current,\n      options: {\n        width: 352,\n        height: 352,\n        wireframes: false,\n        background: '#ffffff',\n        showAngleIndicator: false,\n        showCollisions: false,\n        showVelocity: false\n      }\n    });\n\n    // Create hexagon\n    const hexagonRadius = 132;\n    const hexagonSides = 6;\n    const centerX = render.options.width / 2;\n    const centerY = render.options.height / 2;\n\n    // Create hexagon vertices\n    const hexagonVertices = [];\n    for (let i = 0; i < hexagonSides; i++) {\n      const angle = Math.PI * 2 * i / hexagonSides;\n      const x = hexagonRadius * Math.cos(angle);\n      const y = hexagonRadius * Math.sin(angle);\n      hexagonVertices.push({\n        x,\n        y\n      });\n    }\n\n    // Create hexagon edges with realistic physics\n    hexagonEdgesRef.current = [];\n    for (let i = 0; i < hexagonSides; i++) {\n      const j = (i + 1) % hexagonSides;\n      const edgeOptions = {\n        restitution: wallRestitution,\n        // Use realistic wall bounce\n        friction: 0.05,\n        // Reduced friction for smoother movement\n        frictionStatic: 0.1,\n        isStatic: true,\n        render: {\n          fillStyle: 'transparent',\n          strokeStyle: '#0f0f0f',\n          lineWidth: 2 // Slightly thicker for better visibility\n        }\n      };\n\n      // Calculate edge position and angle\n      const vertex1 = hexagonVertices[i];\n      const vertex2 = hexagonVertices[j];\n      const edgeLength = Math.sqrt(Math.pow(vertex2.x - vertex1.x, 2) + Math.pow(vertex2.y - vertex1.y, 2));\n      const edgeAngle = Math.atan2(vertex2.y - vertex1.y, vertex2.x - vertex1.x);\n      const edgeCenterX = centerX + (vertex1.x + vertex2.x) / 2;\n      const edgeCenterY = centerY + (vertex1.y + vertex2.y) / 2;\n\n      // Create edge\n      const edge = Bodies.rectangle(edgeCenterX, edgeCenterY, edgeLength, 1, edgeOptions);\n\n      // Rotate edge to angle\n      Body.rotate(edge, edgeAngle);\n      hexagonEdgesRef.current.push(edge);\n    }\n\n    // Create ball with realistic physics\n    const originalBallRadius = 15;\n    const ballRadius = originalBallRadius * 0.65; // Slightly smaller for better movement\n\n    // Start ball at a random position within the hexagon for variety\n    const startAngle = Math.random() * Math.PI * 2;\n    const startDistance = hexagonRadius * 0.3; // Start closer to center\n    const startX = centerX + Math.cos(startAngle) * startDistance;\n    const startY = centerY + Math.sin(startAngle) * startDistance;\n    ballRef.current = Bodies.circle(startX, startY, ballRadius, {\n      restitution: ballRestitution,\n      // Realistic bounce with energy loss\n      friction: 0.02,\n      // Very low friction for smooth rolling\n      frictionAir: 0.0005,\n      // Minimal air resistance\n      density: 0.002,\n      // Slightly heavier for more realistic momentum\n      inertia: Infinity,\n      // Prevent rotation for cleaner movement\n      render: {\n        fillStyle: '#F0A500',\n        strokeStyle: '#E69500',\n        lineWidth: 1\n      }\n    });\n\n    // Add realistic initial velocity\n    const initialSpeed = 0.8;\n    const initialAngle = Math.random() * Math.PI * 2;\n    Body.setVelocity(ballRef.current, {\n      x: Math.cos(initialAngle) * initialSpeed,\n      y: Math.sin(initialAngle) * initialSpeed\n    });\n\n    // Add bodies to world\n    World.add(engineRef.current.world, [...hexagonEdgesRef.current, ballRef.current]);\n\n    // Run renderer\n    Render.run(render);\n\n    // Animation loop function with realistic physics\n    const animate = () => {\n      // Update engine with consistent timing\n      Engine.update(engineRef.current, 16.667);\n\n      // Smoothly rotate hexagon edges\n      if (hexagonEdgesRef.current.length > 0) {\n        hexagonEdgesRef.current.forEach(edge => {\n          // Calculate new position after rotation\n          const currentX = edge.position.x - centerX;\n          const currentY = edge.position.y - centerY;\n          const newX = currentX * Math.cos(rotationSpeed) - currentY * Math.sin(rotationSpeed);\n          const newY = currentX * Math.sin(rotationSpeed) + currentY * Math.cos(rotationSpeed);\n          Body.setPosition(edge, {\n            x: centerX + newX,\n            y: centerY + newY\n          });\n          Body.rotate(edge, rotationSpeed);\n        });\n      }\n\n      // Realistic ball physics management\n      if (ballRef.current) {\n        const ballPos = ballRef.current.position;\n        const velocity = ballRef.current.velocity;\n        const speed = Math.sqrt(velocity.x * velocity.x + velocity.y * velocity.y);\n\n        // Calculate distance from ball to hexagon center\n        const distanceFromCenter = Math.sqrt(Math.pow(ballPos.x - centerX, 2) + Math.pow(ballPos.y - centerY, 2));\n\n        // Boundary checking with realistic physics\n        const maxDistance = hexagonRadius * 0.88; // Allow ball to get close to edges\n\n        if (distanceFromCenter > maxDistance) {\n          // Calculate normalized direction vector\n          const directionX = ballPos.x - centerX;\n          const directionY = ballPos.y - centerY;\n          const magnitude = Math.sqrt(directionX * directionX + directionY * directionY);\n          const normalizedX = directionX / magnitude;\n          const normalizedY = directionY / magnitude;\n\n          // Position ball at boundary\n          const newPosX = centerX + normalizedX * maxDistance;\n          const newPosY = centerY + normalizedY * maxDistance;\n          Body.setPosition(ballRef.current, {\n            x: newPosX,\n            y: newPosY\n          });\n\n          // Realistic bounce physics\n          const currentVelocity = ballRef.current.velocity;\n          const dotProduct = currentVelocity.x * normalizedX + currentVelocity.y * normalizedY;\n          if (dotProduct > 0) {\n            // Reflect velocity with energy loss\n            const reflectedVelX = currentVelocity.x - 2 * dotProduct * normalizedX;\n            const reflectedVelY = currentVelocity.y - 2 * dotProduct * normalizedY;\n            Body.setVelocity(ballRef.current, {\n              x: reflectedVelX * ballRestitution,\n              y: reflectedVelY * ballRestitution\n            });\n          }\n        }\n\n        // Maintain minimum energy for continuous movement\n        if (speed < 0.3) {\n          // Add subtle random impulse to keep ball moving\n          const impulseAngle = Math.random() * Math.PI * 2;\n          const impulseMagnitude = 0.15;\n          Body.applyForce(ballRef.current, ballRef.current.position, {\n            x: Math.cos(impulseAngle) * impulseMagnitude * 0.001,\n            y: Math.sin(impulseAngle) * impulseMagnitude * 0.001\n          });\n        }\n\n        // Limit maximum speed for realistic movement\n        if (speed > 4) {\n          const limitedVelX = velocity.x / speed * 4;\n          const limitedVelY = velocity.y / speed * 4;\n          Body.setVelocity(ballRef.current, {\n            x: limitedVelX,\n            y: limitedVelY\n          });\n        }\n      }\n\n      // Continue animation loop\n      requestRef.current = requestAnimationFrame(animate);\n    };\n\n    // Start animation loop\n    requestRef.current = requestAnimationFrame(animate);\n\n    // Cleanup when component unmounts\n    return () => {\n      // Cancel animation loop\n      if (requestRef.current) {\n        cancelAnimationFrame(requestRef.current);\n      }\n\n      // Cleanup renderer and engine\n      Render.stop(render);\n      World.clear(engineRef.current.world);\n      Engine.clear(engineRef.current);\n      render.canvas = null;\n      render.context = null;\n      render.textures = {};\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: styles.loadingContainer,\n    sx: {\n      ...(!fromCreatePlan && {\n        minHeight: '90vh'\n      })\n    },\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      className: styles.gameWrapper,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        className: styles.hexagonLoadingContainer,\n        children: /*#__PURE__*/_jsxDEV(\"canvas\", {\n          ref: canvasRef,\n          className: styles.hexagonCanvas\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 266,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 265,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 264,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 262,\n    columnNumber: 5\n  }, this);\n};\n_s(HexagonBallLoading, \"uDBlP5n4TAfWzOEW2LE7YQXblas=\");\n_c = HexagonBallLoading;\nexport default HexagonBallLoading;\nvar _c;\n$RefreshReg$(_c, \"HexagonBallLoading\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "Box", "Matter", "styles", "jsxDEV", "_jsxDEV", "HexagonBallLoading", "_ref", "_s", "fromCreatePlan", "canvasRef", "engineRef", "requestRef", "ballRef", "hexagonEdgesRef", "rotationSpeed", "gravity", "ballRestitution", "wallRestitution", "Engine", "Render", "World", "Bodies", "Body", "current", "create", "x", "y", "scale", "timing", "timeScale", "render", "canvas", "engine", "options", "width", "height", "wireframes", "background", "showAngleIndicator", "showCollisions", "showVelocity", "hexagonRadius", "hexagonSides", "centerX", "centerY", "hexagonVertices", "i", "angle", "Math", "PI", "cos", "sin", "push", "j", "edgeOptions", "restitution", "friction", "frictionStatic", "isStatic", "fillStyle", "strokeStyle", "lineWidth", "vertex1", "vertex2", "edge<PERSON><PERSON><PERSON>", "sqrt", "pow", "edgeAngle", "atan2", "edgeCenterX", "edgeCenterY", "edge", "rectangle", "rotate", "originalBallRadius", "ballRadius", "startAngle", "random", "startDistance", "startX", "startY", "circle", "frictionAir", "density", "inertia", "Infinity", "initialSpeed", "initialAngle", "setVelocity", "add", "world", "run", "animate", "update", "length", "for<PERSON>ach", "currentX", "position", "currentY", "newX", "newY", "setPosition", "ballPos", "velocity", "speed", "distanceFromCenter", "maxDistance", "directionX", "directionY", "magnitude", "normalizedX", "normalizedY", "newPosX", "newPosY", "currentVelocity", "dotProduct", "reflectedVelX", "reflectedVelY", "impulseAngle", "impulseMagnitude", "applyForce", "limitedVelX", "limitedVelY", "requestAnimationFrame", "cancelAnimationFrame", "stop", "clear", "context", "textures", "className", "loadingContainer", "sx", "minHeight", "children", "gameWrapper", "hexagonLoadingContainer", "ref", "hexagonCanvas", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/ignition/ignition-ui/src/components/Loading/HexagonBallLoading.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\r\nimport { Box } from '@mui/material';\r\nimport Matter from 'matter-js';\r\nimport styles from './styles.module.scss';\r\n\r\n/**\r\n * Component displays loading screen with a bouncing ball inside a rotating hexagon\r\n * @param {Object} props - Component props\r\n * @param {boolean} props.fromCreatePlan - Flag to determine if component is called from CreatePlan page\r\n * @returns {JSX.Element} - HexagonBallLoading component\r\n */\r\nconst HexagonBallLoading = ({ fromCreatePlan }) => {\r\n  const canvasRef = useRef(null);\r\n  const engineRef = useRef(null);\r\n  const requestRef = useRef(null);\r\n  const ballRef = useRef(null);\r\n  const hexagonEdgesRef = useRef([]);\r\n\r\n  useEffect(() => {\r\n    // Realistic physics parameters\r\n    const rotationSpeed = 0.012; // Slower, more natural rotation\r\n    const gravity = 0.0001; // More realistic gravity strength\r\n    const ballRestitution = 1; // Realistic bounce with energy loss\r\n    const wallRestitution = 0; // Walls absorb some energy\r\n\r\n    // Initialize Matter.js modules\r\n    const Engine = Matter.Engine;\r\n    const Render = Matter.Render;\r\n    const World = Matter.World;\r\n    const Bodies = Matter.Bodies;\r\n    const Body = Matter.Body;\r\n\r\n    // Create engine with realistic physics\r\n    engineRef.current = Engine.create({\r\n      gravity: { x: 0, y: gravity, scale: 1 },\r\n    });\r\n\r\n    // Set realistic timing for physics simulation\r\n    engineRef.current.timing.timeScale = 1;\r\n\r\n    // Create renderer\r\n    const render = Render.create({\r\n      canvas: canvasRef.current,\r\n      engine: engineRef.current,\r\n      options: {\r\n        width: 352,\r\n        height: 352,\r\n        wireframes: false,\r\n        background: '#ffffff',\r\n        showAngleIndicator: false,\r\n        showCollisions: false,\r\n        showVelocity: false,\r\n      },\r\n    });\r\n\r\n    // Create hexagon\r\n    const hexagonRadius = 132;\r\n    const hexagonSides = 6;\r\n    const centerX = render.options.width / 2;\r\n    const centerY = render.options.height / 2;\r\n\r\n    // Create hexagon vertices\r\n    const hexagonVertices = [];\r\n    for (let i = 0; i < hexagonSides; i++) {\r\n      const angle = (Math.PI * 2 * i) / hexagonSides;\r\n      const x = hexagonRadius * Math.cos(angle);\r\n      const y = hexagonRadius * Math.sin(angle);\r\n      hexagonVertices.push({ x, y });\r\n    }\r\n\r\n    // Create hexagon edges with realistic physics\r\n    hexagonEdgesRef.current = [];\r\n    for (let i = 0; i < hexagonSides; i++) {\r\n      const j = (i + 1) % hexagonSides;\r\n      const edgeOptions = {\r\n        restitution: wallRestitution, // Use realistic wall bounce\r\n        friction: 0.05, // Reduced friction for smoother movement\r\n        frictionStatic: 0.1,\r\n        isStatic: true,\r\n        render: {\r\n          fillStyle: 'transparent',\r\n          strokeStyle: '#0f0f0f',\r\n          lineWidth: 2, // Slightly thicker for better visibility\r\n        },\r\n      };\r\n\r\n      // Calculate edge position and angle\r\n      const vertex1 = hexagonVertices[i];\r\n      const vertex2 = hexagonVertices[j];\r\n      const edgeLength = Math.sqrt(\r\n        Math.pow(vertex2.x - vertex1.x, 2) + Math.pow(vertex2.y - vertex1.y, 2)\r\n      );\r\n      const edgeAngle = Math.atan2(vertex2.y - vertex1.y, vertex2.x - vertex1.x);\r\n      const edgeCenterX = centerX + (vertex1.x + vertex2.x) / 2;\r\n      const edgeCenterY = centerY + (vertex1.y + vertex2.y) / 2;\r\n\r\n      // Create edge\r\n      const edge = Bodies.rectangle(\r\n        edgeCenterX,\r\n        edgeCenterY,\r\n        edgeLength,\r\n        1,\r\n        edgeOptions\r\n      );\r\n\r\n      // Rotate edge to angle\r\n      Body.rotate(edge, edgeAngle);\r\n\r\n      hexagonEdgesRef.current.push(edge);\r\n    }\r\n\r\n    // Create ball with realistic physics\r\n    const originalBallRadius = 15;\r\n    const ballRadius = originalBallRadius * 0.65; // Slightly smaller for better movement\r\n\r\n    // Start ball at a random position within the hexagon for variety\r\n    const startAngle = Math.random() * Math.PI * 2;\r\n    const startDistance = hexagonRadius * 0.3; // Start closer to center\r\n    const startX = centerX + Math.cos(startAngle) * startDistance;\r\n    const startY = centerY + Math.sin(startAngle) * startDistance;\r\n\r\n    ballRef.current = Bodies.circle(startX, startY, ballRadius, {\r\n      restitution: ballRestitution, // Realistic bounce with energy loss\r\n      friction: 0.02, // Very low friction for smooth rolling\r\n      frictionAir: 0.0005, // Minimal air resistance\r\n      density: 0.002, // Slightly heavier for more realistic momentum\r\n      inertia: Infinity, // Prevent rotation for cleaner movement\r\n      render: {\r\n        fillStyle: '#F0A500',\r\n        strokeStyle: '#E69500',\r\n        lineWidth: 1,\r\n      },\r\n    });\r\n\r\n    // Add realistic initial velocity\r\n    const initialSpeed = 0.8;\r\n    const initialAngle = Math.random() * Math.PI * 2;\r\n    Body.setVelocity(ballRef.current, {\r\n      x: Math.cos(initialAngle) * initialSpeed,\r\n      y: Math.sin(initialAngle) * initialSpeed\r\n    });\r\n\r\n    // Add bodies to world\r\n    World.add(engineRef.current.world, [...hexagonEdgesRef.current, ballRef.current]);\r\n\r\n    // Run renderer\r\n    Render.run(render);\r\n\r\n    // Animation loop function with realistic physics\r\n    const animate = () => {\r\n      // Update engine with consistent timing\r\n      Engine.update(engineRef.current, 16.667);\r\n\r\n      // Smoothly rotate hexagon edges\r\n      if (hexagonEdgesRef.current.length > 0) {\r\n        hexagonEdgesRef.current.forEach(edge => {\r\n          // Calculate new position after rotation\r\n          const currentX = edge.position.x - centerX;\r\n          const currentY = edge.position.y - centerY;\r\n\r\n          const newX = currentX * Math.cos(rotationSpeed) - currentY * Math.sin(rotationSpeed);\r\n          const newY = currentX * Math.sin(rotationSpeed) + currentY * Math.cos(rotationSpeed);\r\n\r\n          Body.setPosition(edge, {\r\n            x: centerX + newX,\r\n            y: centerY + newY\r\n          });\r\n          Body.rotate(edge, rotationSpeed);\r\n        });\r\n      }\r\n\r\n      // Realistic ball physics management\r\n      if (ballRef.current) {\r\n        const ballPos = ballRef.current.position;\r\n        const velocity = ballRef.current.velocity;\r\n        const speed = Math.sqrt(velocity.x * velocity.x + velocity.y * velocity.y);\r\n\r\n        // Calculate distance from ball to hexagon center\r\n        const distanceFromCenter = Math.sqrt(\r\n          Math.pow(ballPos.x - centerX, 2) + Math.pow(ballPos.y - centerY, 2)\r\n        );\r\n\r\n        // Boundary checking with realistic physics\r\n        const maxDistance = hexagonRadius * 0.88; // Allow ball to get close to edges\r\n\r\n        if (distanceFromCenter > maxDistance) {\r\n          // Calculate normalized direction vector\r\n          const directionX = ballPos.x - centerX;\r\n          const directionY = ballPos.y - centerY;\r\n          const magnitude = Math.sqrt(directionX * directionX + directionY * directionY);\r\n\r\n          const normalizedX = directionX / magnitude;\r\n          const normalizedY = directionY / magnitude;\r\n\r\n          // Position ball at boundary\r\n          const newPosX = centerX + normalizedX * maxDistance;\r\n          const newPosY = centerY + normalizedY * maxDistance;\r\n\r\n          Body.setPosition(ballRef.current, { x: newPosX, y: newPosY });\r\n\r\n          // Realistic bounce physics\r\n          const currentVelocity = ballRef.current.velocity;\r\n          const dotProduct = currentVelocity.x * normalizedX + currentVelocity.y * normalizedY;\r\n\r\n          if (dotProduct > 0) {\r\n            // Reflect velocity with energy loss\r\n            const reflectedVelX = currentVelocity.x - 2 * dotProduct * normalizedX;\r\n            const reflectedVelY = currentVelocity.y - 2 * dotProduct * normalizedY;\r\n\r\n            Body.setVelocity(ballRef.current, {\r\n              x: reflectedVelX * ballRestitution,\r\n              y: reflectedVelY * ballRestitution\r\n            });\r\n          }\r\n        }\r\n\r\n        // Maintain minimum energy for continuous movement\r\n        if (speed < 0.3) {\r\n          // Add subtle random impulse to keep ball moving\r\n          const impulseAngle = Math.random() * Math.PI * 2;\r\n          const impulseMagnitude = 0.15;\r\n\r\n          Body.applyForce(ballRef.current, ballRef.current.position, {\r\n            x: Math.cos(impulseAngle) * impulseMagnitude * 0.001,\r\n            y: Math.sin(impulseAngle) * impulseMagnitude * 0.001\r\n          });\r\n        }\r\n\r\n        // Limit maximum speed for realistic movement\r\n        if (speed > 4) {\r\n          const limitedVelX = (velocity.x / speed) * 4;\r\n          const limitedVelY = (velocity.y / speed) * 4;\r\n          Body.setVelocity(ballRef.current, { x: limitedVelX, y: limitedVelY });\r\n        }\r\n      }\r\n\r\n      // Continue animation loop\r\n      requestRef.current = requestAnimationFrame(animate);\r\n    };\r\n\r\n    // Start animation loop\r\n    requestRef.current = requestAnimationFrame(animate);\r\n\r\n    // Cleanup when component unmounts\r\n    return () => {\r\n      // Cancel animation loop\r\n      if (requestRef.current) {\r\n        cancelAnimationFrame(requestRef.current);\r\n      }\r\n\r\n      // Cleanup renderer and engine\r\n      Render.stop(render);\r\n      World.clear(engineRef.current.world);\r\n      Engine.clear(engineRef.current);\r\n      render.canvas = null;\r\n      render.context = null;\r\n      render.textures = {};\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <Box className={styles.loadingContainer}\r\n      sx={{ ...(!fromCreatePlan && { minHeight: '90vh' }) }}>\r\n      <Box className={styles.gameWrapper}>\r\n        <Box className={styles.hexagonLoadingContainer}>\r\n          <canvas ref={canvasRef} className={styles.hexagonCanvas} />\r\n        </Box>\r\n      </Box>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default HexagonBallLoading;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,SAASC,GAAG,QAAQ,eAAe;AACnC,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,MAAM,MAAM,sBAAsB;;AAEzC;AACA;AACA;AACA;AACA;AACA;AALA,SAAAC,MAAA,IAAAC,OAAA;AAMA,MAAMC,kBAAkB,GAAGC,IAAA,IAAwB;EAAAC,EAAA;EAAA,IAAvB;IAAEC;EAAe,CAAC,GAAAF,IAAA;EAC5C,MAAMG,SAAS,GAAGV,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMW,SAAS,GAAGX,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMY,UAAU,GAAGZ,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMa,OAAO,GAAGb,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAMc,eAAe,GAAGd,MAAM,CAAC,EAAE,CAAC;EAElCD,SAAS,CAAC,MAAM;IACd;IACA,MAAMgB,aAAa,GAAG,KAAK,CAAC,CAAC;IAC7B,MAAMC,OAAO,GAAG,MAAM,CAAC,CAAC;IACxB,MAAMC,eAAe,GAAG,CAAC,CAAC,CAAC;IAC3B,MAAMC,eAAe,GAAG,CAAC,CAAC,CAAC;;IAE3B;IACA,MAAMC,MAAM,GAAGjB,MAAM,CAACiB,MAAM;IAC5B,MAAMC,MAAM,GAAGlB,MAAM,CAACkB,MAAM;IAC5B,MAAMC,KAAK,GAAGnB,MAAM,CAACmB,KAAK;IAC1B,MAAMC,MAAM,GAAGpB,MAAM,CAACoB,MAAM;IAC5B,MAAMC,IAAI,GAAGrB,MAAM,CAACqB,IAAI;;IAExB;IACAZ,SAAS,CAACa,OAAO,GAAGL,MAAM,CAACM,MAAM,CAAC;MAChCT,OAAO,EAAE;QAAEU,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAEX,OAAO;QAAEY,KAAK,EAAE;MAAE;IACxC,CAAC,CAAC;;IAEF;IACAjB,SAAS,CAACa,OAAO,CAACK,MAAM,CAACC,SAAS,GAAG,CAAC;;IAEtC;IACA,MAAMC,MAAM,GAAGX,MAAM,CAACK,MAAM,CAAC;MAC3BO,MAAM,EAAEtB,SAAS,CAACc,OAAO;MACzBS,MAAM,EAAEtB,SAAS,CAACa,OAAO;MACzBU,OAAO,EAAE;QACPC,KAAK,EAAE,GAAG;QACVC,MAAM,EAAE,GAAG;QACXC,UAAU,EAAE,KAAK;QACjBC,UAAU,EAAE,SAAS;QACrBC,kBAAkB,EAAE,KAAK;QACzBC,cAAc,EAAE,KAAK;QACrBC,YAAY,EAAE;MAChB;IACF,CAAC,CAAC;;IAEF;IACA,MAAMC,aAAa,GAAG,GAAG;IACzB,MAAMC,YAAY,GAAG,CAAC;IACtB,MAAMC,OAAO,GAAGb,MAAM,CAACG,OAAO,CAACC,KAAK,GAAG,CAAC;IACxC,MAAMU,OAAO,GAAGd,MAAM,CAACG,OAAO,CAACE,MAAM,GAAG,CAAC;;IAEzC;IACA,MAAMU,eAAe,GAAG,EAAE;IAC1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,YAAY,EAAEI,CAAC,EAAE,EAAE;MACrC,MAAMC,KAAK,GAAIC,IAAI,CAACC,EAAE,GAAG,CAAC,GAAGH,CAAC,GAAIJ,YAAY;MAC9C,MAAMjB,CAAC,GAAGgB,aAAa,GAAGO,IAAI,CAACE,GAAG,CAACH,KAAK,CAAC;MACzC,MAAMrB,CAAC,GAAGe,aAAa,GAAGO,IAAI,CAACG,GAAG,CAACJ,KAAK,CAAC;MACzCF,eAAe,CAACO,IAAI,CAAC;QAAE3B,CAAC;QAAEC;MAAE,CAAC,CAAC;IAChC;;IAEA;IACAb,eAAe,CAACU,OAAO,GAAG,EAAE;IAC5B,KAAK,IAAIuB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,YAAY,EAAEI,CAAC,EAAE,EAAE;MACrC,MAAMO,CAAC,GAAG,CAACP,CAAC,GAAG,CAAC,IAAIJ,YAAY;MAChC,MAAMY,WAAW,GAAG;QAClBC,WAAW,EAAEtC,eAAe;QAAE;QAC9BuC,QAAQ,EAAE,IAAI;QAAE;QAChBC,cAAc,EAAE,GAAG;QACnBC,QAAQ,EAAE,IAAI;QACd5B,MAAM,EAAE;UACN6B,SAAS,EAAE,aAAa;UACxBC,WAAW,EAAE,SAAS;UACtBC,SAAS,EAAE,CAAC,CAAE;QAChB;MACF,CAAC;;MAED;MACA,MAAMC,OAAO,GAAGjB,eAAe,CAACC,CAAC,CAAC;MAClC,MAAMiB,OAAO,GAAGlB,eAAe,CAACQ,CAAC,CAAC;MAClC,MAAMW,UAAU,GAAGhB,IAAI,CAACiB,IAAI,CAC1BjB,IAAI,CAACkB,GAAG,CAACH,OAAO,CAACtC,CAAC,GAAGqC,OAAO,CAACrC,CAAC,EAAE,CAAC,CAAC,GAAGuB,IAAI,CAACkB,GAAG,CAACH,OAAO,CAACrC,CAAC,GAAGoC,OAAO,CAACpC,CAAC,EAAE,CAAC,CACxE,CAAC;MACD,MAAMyC,SAAS,GAAGnB,IAAI,CAACoB,KAAK,CAACL,OAAO,CAACrC,CAAC,GAAGoC,OAAO,CAACpC,CAAC,EAAEqC,OAAO,CAACtC,CAAC,GAAGqC,OAAO,CAACrC,CAAC,CAAC;MAC1E,MAAM4C,WAAW,GAAG1B,OAAO,GAAG,CAACmB,OAAO,CAACrC,CAAC,GAAGsC,OAAO,CAACtC,CAAC,IAAI,CAAC;MACzD,MAAM6C,WAAW,GAAG1B,OAAO,GAAG,CAACkB,OAAO,CAACpC,CAAC,GAAGqC,OAAO,CAACrC,CAAC,IAAI,CAAC;;MAEzD;MACA,MAAM6C,IAAI,GAAGlD,MAAM,CAACmD,SAAS,CAC3BH,WAAW,EACXC,WAAW,EACXN,UAAU,EACV,CAAC,EACDV,WACF,CAAC;;MAED;MACAhC,IAAI,CAACmD,MAAM,CAACF,IAAI,EAAEJ,SAAS,CAAC;MAE5BtD,eAAe,CAACU,OAAO,CAAC6B,IAAI,CAACmB,IAAI,CAAC;IACpC;;IAEA;IACA,MAAMG,kBAAkB,GAAG,EAAE;IAC7B,MAAMC,UAAU,GAAGD,kBAAkB,GAAG,IAAI,CAAC,CAAC;;IAE9C;IACA,MAAME,UAAU,GAAG5B,IAAI,CAAC6B,MAAM,CAAC,CAAC,GAAG7B,IAAI,CAACC,EAAE,GAAG,CAAC;IAC9C,MAAM6B,aAAa,GAAGrC,aAAa,GAAG,GAAG,CAAC,CAAC;IAC3C,MAAMsC,MAAM,GAAGpC,OAAO,GAAGK,IAAI,CAACE,GAAG,CAAC0B,UAAU,CAAC,GAAGE,aAAa;IAC7D,MAAME,MAAM,GAAGpC,OAAO,GAAGI,IAAI,CAACG,GAAG,CAACyB,UAAU,CAAC,GAAGE,aAAa;IAE7DlE,OAAO,CAACW,OAAO,GAAGF,MAAM,CAAC4D,MAAM,CAACF,MAAM,EAAEC,MAAM,EAAEL,UAAU,EAAE;MAC1DpB,WAAW,EAAEvC,eAAe;MAAE;MAC9BwC,QAAQ,EAAE,IAAI;MAAE;MAChB0B,WAAW,EAAE,MAAM;MAAE;MACrBC,OAAO,EAAE,KAAK;MAAE;MAChBC,OAAO,EAAEC,QAAQ;MAAE;MACnBvD,MAAM,EAAE;QACN6B,SAAS,EAAE,SAAS;QACpBC,WAAW,EAAE,SAAS;QACtBC,SAAS,EAAE;MACb;IACF,CAAC,CAAC;;IAEF;IACA,MAAMyB,YAAY,GAAG,GAAG;IACxB,MAAMC,YAAY,GAAGvC,IAAI,CAAC6B,MAAM,CAAC,CAAC,GAAG7B,IAAI,CAACC,EAAE,GAAG,CAAC;IAChD3B,IAAI,CAACkE,WAAW,CAAC5E,OAAO,CAACW,OAAO,EAAE;MAChCE,CAAC,EAAEuB,IAAI,CAACE,GAAG,CAACqC,YAAY,CAAC,GAAGD,YAAY;MACxC5D,CAAC,EAAEsB,IAAI,CAACG,GAAG,CAACoC,YAAY,CAAC,GAAGD;IAC9B,CAAC,CAAC;;IAEF;IACAlE,KAAK,CAACqE,GAAG,CAAC/E,SAAS,CAACa,OAAO,CAACmE,KAAK,EAAE,CAAC,GAAG7E,eAAe,CAACU,OAAO,EAAEX,OAAO,CAACW,OAAO,CAAC,CAAC;;IAEjF;IACAJ,MAAM,CAACwE,GAAG,CAAC7D,MAAM,CAAC;;IAElB;IACA,MAAM8D,OAAO,GAAGA,CAAA,KAAM;MACpB;MACA1E,MAAM,CAAC2E,MAAM,CAACnF,SAAS,CAACa,OAAO,EAAE,MAAM,CAAC;;MAExC;MACA,IAAIV,eAAe,CAACU,OAAO,CAACuE,MAAM,GAAG,CAAC,EAAE;QACtCjF,eAAe,CAACU,OAAO,CAACwE,OAAO,CAACxB,IAAI,IAAI;UACtC;UACA,MAAMyB,QAAQ,GAAGzB,IAAI,CAAC0B,QAAQ,CAACxE,CAAC,GAAGkB,OAAO;UAC1C,MAAMuD,QAAQ,GAAG3B,IAAI,CAAC0B,QAAQ,CAACvE,CAAC,GAAGkB,OAAO;UAE1C,MAAMuD,IAAI,GAAGH,QAAQ,GAAGhD,IAAI,CAACE,GAAG,CAACpC,aAAa,CAAC,GAAGoF,QAAQ,GAAGlD,IAAI,CAACG,GAAG,CAACrC,aAAa,CAAC;UACpF,MAAMsF,IAAI,GAAGJ,QAAQ,GAAGhD,IAAI,CAACG,GAAG,CAACrC,aAAa,CAAC,GAAGoF,QAAQ,GAAGlD,IAAI,CAACE,GAAG,CAACpC,aAAa,CAAC;UAEpFQ,IAAI,CAAC+E,WAAW,CAAC9B,IAAI,EAAE;YACrB9C,CAAC,EAAEkB,OAAO,GAAGwD,IAAI;YACjBzE,CAAC,EAAEkB,OAAO,GAAGwD;UACf,CAAC,CAAC;UACF9E,IAAI,CAACmD,MAAM,CAACF,IAAI,EAAEzD,aAAa,CAAC;QAClC,CAAC,CAAC;MACJ;;MAEA;MACA,IAAIF,OAAO,CAACW,OAAO,EAAE;QACnB,MAAM+E,OAAO,GAAG1F,OAAO,CAACW,OAAO,CAAC0E,QAAQ;QACxC,MAAMM,QAAQ,GAAG3F,OAAO,CAACW,OAAO,CAACgF,QAAQ;QACzC,MAAMC,KAAK,GAAGxD,IAAI,CAACiB,IAAI,CAACsC,QAAQ,CAAC9E,CAAC,GAAG8E,QAAQ,CAAC9E,CAAC,GAAG8E,QAAQ,CAAC7E,CAAC,GAAG6E,QAAQ,CAAC7E,CAAC,CAAC;;QAE1E;QACA,MAAM+E,kBAAkB,GAAGzD,IAAI,CAACiB,IAAI,CAClCjB,IAAI,CAACkB,GAAG,CAACoC,OAAO,CAAC7E,CAAC,GAAGkB,OAAO,EAAE,CAAC,CAAC,GAAGK,IAAI,CAACkB,GAAG,CAACoC,OAAO,CAAC5E,CAAC,GAAGkB,OAAO,EAAE,CAAC,CACpE,CAAC;;QAED;QACA,MAAM8D,WAAW,GAAGjE,aAAa,GAAG,IAAI,CAAC,CAAC;;QAE1C,IAAIgE,kBAAkB,GAAGC,WAAW,EAAE;UACpC;UACA,MAAMC,UAAU,GAAGL,OAAO,CAAC7E,CAAC,GAAGkB,OAAO;UACtC,MAAMiE,UAAU,GAAGN,OAAO,CAAC5E,CAAC,GAAGkB,OAAO;UACtC,MAAMiE,SAAS,GAAG7D,IAAI,CAACiB,IAAI,CAAC0C,UAAU,GAAGA,UAAU,GAAGC,UAAU,GAAGA,UAAU,CAAC;UAE9E,MAAME,WAAW,GAAGH,UAAU,GAAGE,SAAS;UAC1C,MAAME,WAAW,GAAGH,UAAU,GAAGC,SAAS;;UAE1C;UACA,MAAMG,OAAO,GAAGrE,OAAO,GAAGmE,WAAW,GAAGJ,WAAW;UACnD,MAAMO,OAAO,GAAGrE,OAAO,GAAGmE,WAAW,GAAGL,WAAW;UAEnDpF,IAAI,CAAC+E,WAAW,CAACzF,OAAO,CAACW,OAAO,EAAE;YAAEE,CAAC,EAAEuF,OAAO;YAAEtF,CAAC,EAAEuF;UAAQ,CAAC,CAAC;;UAE7D;UACA,MAAMC,eAAe,GAAGtG,OAAO,CAACW,OAAO,CAACgF,QAAQ;UAChD,MAAMY,UAAU,GAAGD,eAAe,CAACzF,CAAC,GAAGqF,WAAW,GAAGI,eAAe,CAACxF,CAAC,GAAGqF,WAAW;UAEpF,IAAII,UAAU,GAAG,CAAC,EAAE;YAClB;YACA,MAAMC,aAAa,GAAGF,eAAe,CAACzF,CAAC,GAAG,CAAC,GAAG0F,UAAU,GAAGL,WAAW;YACtE,MAAMO,aAAa,GAAGH,eAAe,CAACxF,CAAC,GAAG,CAAC,GAAGyF,UAAU,GAAGJ,WAAW;YAEtEzF,IAAI,CAACkE,WAAW,CAAC5E,OAAO,CAACW,OAAO,EAAE;cAChCE,CAAC,EAAE2F,aAAa,GAAGpG,eAAe;cAClCU,CAAC,EAAE2F,aAAa,GAAGrG;YACrB,CAAC,CAAC;UACJ;QACF;;QAEA;QACA,IAAIwF,KAAK,GAAG,GAAG,EAAE;UACf;UACA,MAAMc,YAAY,GAAGtE,IAAI,CAAC6B,MAAM,CAAC,CAAC,GAAG7B,IAAI,CAACC,EAAE,GAAG,CAAC;UAChD,MAAMsE,gBAAgB,GAAG,IAAI;UAE7BjG,IAAI,CAACkG,UAAU,CAAC5G,OAAO,CAACW,OAAO,EAAEX,OAAO,CAACW,OAAO,CAAC0E,QAAQ,EAAE;YACzDxE,CAAC,EAAEuB,IAAI,CAACE,GAAG,CAACoE,YAAY,CAAC,GAAGC,gBAAgB,GAAG,KAAK;YACpD7F,CAAC,EAAEsB,IAAI,CAACG,GAAG,CAACmE,YAAY,CAAC,GAAGC,gBAAgB,GAAG;UACjD,CAAC,CAAC;QACJ;;QAEA;QACA,IAAIf,KAAK,GAAG,CAAC,EAAE;UACb,MAAMiB,WAAW,GAAIlB,QAAQ,CAAC9E,CAAC,GAAG+E,KAAK,GAAI,CAAC;UAC5C,MAAMkB,WAAW,GAAInB,QAAQ,CAAC7E,CAAC,GAAG8E,KAAK,GAAI,CAAC;UAC5ClF,IAAI,CAACkE,WAAW,CAAC5E,OAAO,CAACW,OAAO,EAAE;YAAEE,CAAC,EAAEgG,WAAW;YAAE/F,CAAC,EAAEgG;UAAY,CAAC,CAAC;QACvE;MACF;;MAEA;MACA/G,UAAU,CAACY,OAAO,GAAGoG,qBAAqB,CAAC/B,OAAO,CAAC;IACrD,CAAC;;IAED;IACAjF,UAAU,CAACY,OAAO,GAAGoG,qBAAqB,CAAC/B,OAAO,CAAC;;IAEnD;IACA,OAAO,MAAM;MACX;MACA,IAAIjF,UAAU,CAACY,OAAO,EAAE;QACtBqG,oBAAoB,CAACjH,UAAU,CAACY,OAAO,CAAC;MAC1C;;MAEA;MACAJ,MAAM,CAAC0G,IAAI,CAAC/F,MAAM,CAAC;MACnBV,KAAK,CAAC0G,KAAK,CAACpH,SAAS,CAACa,OAAO,CAACmE,KAAK,CAAC;MACpCxE,MAAM,CAAC4G,KAAK,CAACpH,SAAS,CAACa,OAAO,CAAC;MAC/BO,MAAM,CAACC,MAAM,GAAG,IAAI;MACpBD,MAAM,CAACiG,OAAO,GAAG,IAAI;MACrBjG,MAAM,CAACkG,QAAQ,GAAG,CAAC,CAAC;IACtB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACE5H,OAAA,CAACJ,GAAG;IAACiI,SAAS,EAAE/H,MAAM,CAACgI,gBAAiB;IACtCC,EAAE,EAAE;MAAE,IAAI,CAAC3H,cAAc,IAAI;QAAE4H,SAAS,EAAE;MAAO,CAAC;IAAE,CAAE;IAAAC,QAAA,eACtDjI,OAAA,CAACJ,GAAG;MAACiI,SAAS,EAAE/H,MAAM,CAACoI,WAAY;MAAAD,QAAA,eACjCjI,OAAA,CAACJ,GAAG;QAACiI,SAAS,EAAE/H,MAAM,CAACqI,uBAAwB;QAAAF,QAAA,eAC7CjI,OAAA;UAAQoI,GAAG,EAAE/H,SAAU;UAACwH,SAAS,EAAE/H,MAAM,CAACuI;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACtI,EAAA,CAnQIF,kBAAkB;AAAAyI,EAAA,GAAlBzI,kBAAkB;AAqQxB,eAAeA,kBAAkB;AAAC,IAAAyI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}