{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getTypographyUtilityClass(slot) {\n  return generateUtilityClass('MuiTypography', slot);\n}\nconst typographyClasses = generateUtilityClasses('MuiTypography', ['root', 'h1', 'h2', 'h3', 'h4', 'title-lg', 'title-md', 'title-sm', 'body-lg', 'body-md', 'body-sm', 'body-xs', 'noWrap', 'gutterBottom', 'startDecorator', 'endDecorator', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid']);\nexport default typographyClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getTypographyUtilityClass", "slot", "typographyClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Typography/typographyClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getTypographyUtilityClass(slot) {\n  return generateUtilityClass('MuiTypography', slot);\n}\nconst typographyClasses = generateUtilityClasses('MuiTypography', ['root', 'h1', 'h2', 'h3', 'h4', 'title-lg', 'title-md', 'title-sm', 'body-lg', 'body-md', 'body-sm', 'body-xs', 'noWrap', 'gutterBottom', 'startDecorator', 'endDecorator', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid']);\nexport default typographyClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,yBAAyBA,CAACC,IAAI,EAAE;EAC9C,OAAOH,oBAAoB,CAAC,eAAe,EAAEG,IAAI,CAAC;AACpD;AACA,MAAMC,iBAAiB,GAAGH,sBAAsB,CAAC,eAAe,EAAE,CAAC,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,cAAc,EAAE,gBAAgB,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,iBAAiB,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;AAChZ,eAAeG,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}