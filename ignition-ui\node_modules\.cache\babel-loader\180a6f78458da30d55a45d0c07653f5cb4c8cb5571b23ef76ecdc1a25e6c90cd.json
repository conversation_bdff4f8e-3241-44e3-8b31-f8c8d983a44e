{"ast": null, "code": "'use strict';\n\nvar bind = require('function-bind');\nvar $TypeError = require('es-errors/type');\nvar $call = require('./functionCall');\nvar $actualApply = require('./actualApply');\n\n/** @type {(args: [Function, thisArg?: unknown, ...args: unknown[]]) => Function} TODO FIXME, find a way to use import('.') */\nmodule.exports = function callBindBasic(args) {\n  if (args.length < 1 || typeof args[0] !== 'function') {\n    throw new $TypeError('a function is required');\n  }\n  return $actualApply(bind, $call, args);\n};", "map": {"version": 3, "names": ["bind", "require", "$TypeError", "$call", "$actualApply", "module", "exports", "callBindBasic", "args", "length"], "sources": ["C:/ignition/ignition-ui/node_modules/call-bind-apply-helpers/index.js"], "sourcesContent": ["'use strict';\n\nvar bind = require('function-bind');\nvar $TypeError = require('es-errors/type');\n\nvar $call = require('./functionCall');\nvar $actualApply = require('./actualApply');\n\n/** @type {(args: [Function, thisArg?: unknown, ...args: unknown[]]) => Function} TODO FIXME, find a way to use import('.') */\nmodule.exports = function callBindBasic(args) {\n\tif (args.length < 1 || typeof args[0] !== 'function') {\n\t\tthrow new $TypeError('a function is required');\n\t}\n\treturn $actualApply(bind, $call, args);\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,IAAI,GAAGC,OAAO,CAAC,eAAe,CAAC;AACnC,IAAIC,UAAU,GAAGD,OAAO,CAAC,gBAAgB,CAAC;AAE1C,IAAIE,KAAK,GAAGF,OAAO,CAAC,gBAAgB,CAAC;AACrC,IAAIG,YAAY,GAAGH,OAAO,CAAC,eAAe,CAAC;;AAE3C;AACAI,MAAM,CAACC,OAAO,GAAG,SAASC,aAAaA,CAACC,IAAI,EAAE;EAC7C,IAAIA,IAAI,CAACC,MAAM,GAAG,CAAC,IAAI,OAAOD,IAAI,CAAC,CAAC,CAAC,KAAK,UAAU,EAAE;IACrD,MAAM,IAAIN,UAAU,CAAC,wBAAwB,CAAC;EAC/C;EACA,OAAOE,YAAY,CAACJ,IAAI,EAAEG,KAAK,EAAEK,IAAI,CAAC;AACvC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}