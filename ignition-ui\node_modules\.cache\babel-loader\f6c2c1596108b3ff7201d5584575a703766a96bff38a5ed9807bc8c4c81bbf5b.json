{"ast": null, "code": "'use strict';\n\nvar callBound = require('call-bound');\nvar getDay = callBound('Date.prototype.getDay');\n/** @type {import('.')} */\nvar tryDateObject = function tryDateGetDayCall(value) {\n  try {\n    getDay(value);\n    return true;\n  } catch (e) {\n    return false;\n  }\n};\n\n/** @type {(value: unknown) => string} */\nvar toStr = callBound('Object.prototype.toString');\nvar dateClass = '[object Date]';\nvar hasToStringTag = require('has-tostringtag/shams')();\n\n/** @type {import('.')} */\nmodule.exports = function isDateObject(value) {\n  if (typeof value !== 'object' || value === null) {\n    return false;\n  }\n  return hasToStringTag ? tryDateObject(value) : toStr(value) === dateClass;\n};", "map": {"version": 3, "names": ["callBound", "require", "getDay", "tryDateObject", "tryDateGetDayCall", "value", "e", "toStr", "dateClass", "hasToStringTag", "module", "exports", "isDateObject"], "sources": ["C:/ignition/ignition-ui/node_modules/is-date-object/index.js"], "sourcesContent": ["'use strict';\n\nvar callBound = require('call-bound');\n\nvar getDay = callBound('Date.prototype.getDay');\n/** @type {import('.')} */\nvar tryDateObject = function tryDateGetDayCall(value) {\n\ttry {\n\t\tgetDay(value);\n\t\treturn true;\n\t} catch (e) {\n\t\treturn false;\n\t}\n};\n\n/** @type {(value: unknown) => string} */\nvar toStr = callBound('Object.prototype.toString');\nvar dateClass = '[object Date]';\nvar hasToStringTag = require('has-tostringtag/shams')();\n\n/** @type {import('.')} */\nmodule.exports = function isDateObject(value) {\n\tif (typeof value !== 'object' || value === null) {\n\t\treturn false;\n\t}\n\treturn hasToStringTag ? tryDateObject(value) : toStr(value) === dateClass;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,SAAS,GAAGC,OAAO,CAAC,YAAY,CAAC;AAErC,IAAIC,MAAM,GAAGF,SAAS,CAAC,uBAAuB,CAAC;AAC/C;AACA,IAAIG,aAAa,GAAG,SAASC,iBAAiBA,CAACC,KAAK,EAAE;EACrD,IAAI;IACHH,MAAM,CAACG,KAAK,CAAC;IACb,OAAO,IAAI;EACZ,CAAC,CAAC,OAAOC,CAAC,EAAE;IACX,OAAO,KAAK;EACb;AACD,CAAC;;AAED;AACA,IAAIC,KAAK,GAAGP,SAAS,CAAC,2BAA2B,CAAC;AAClD,IAAIQ,SAAS,GAAG,eAAe;AAC/B,IAAIC,cAAc,GAAGR,OAAO,CAAC,uBAAuB,CAAC,CAAC,CAAC;;AAEvD;AACAS,MAAM,CAACC,OAAO,GAAG,SAASC,YAAYA,CAACP,KAAK,EAAE;EAC7C,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,KAAK,IAAI,EAAE;IAChD,OAAO,KAAK;EACb;EACA,OAAOI,cAAc,GAAGN,aAAa,CAACE,KAAK,CAAC,GAAGE,KAAK,CAACF,KAAK,CAAC,KAAKG,SAAS;AAC1E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}