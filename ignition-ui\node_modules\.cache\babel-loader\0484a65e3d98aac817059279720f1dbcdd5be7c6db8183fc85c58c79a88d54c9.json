{"ast": null, "code": "var _jsxFileName = \"C:\\\\ignition\\\\ignition-ui\\\\src\\\\components\\\\Loading\\\\HexagonBallLoading.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useRef } from 'react';\nimport { Box } from '@mui/material';\nimport Matter from 'matter-js';\nimport styles from './styles.module.scss';\n\n/**\r\n * Component displays loading screen with a bouncing ball inside a rotating hexagon\r\n * @param {Object} props - Component props\r\n * @param {boolean} props.fromCreatePlan - Flag to determine if component is called from CreatePlan page\r\n * @returns {JSX.Element} - HexagonBallLoading component\r\n */\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HexagonBallLoading = _ref => {\n  _s();\n  let {\n    fromCreatePlan\n  } = _ref;\n  const canvasRef = useRef(null);\n  const engineRef = useRef(null);\n  const requestRef = useRef(null);\n  const ballRef = useRef(null);\n  const hexagonEdgesRef = useRef([]);\n  useEffect(() => {\n    // Set optimal parameters for natural motion\n    const rotationSpeed = 0.01; // Moderate rotation speed\n    const gravity = 0.001; // Sufficient gravity\n    const restitution = 0.8; // High bounce for continuous ball movement\n\n    // Initialize Matter.js modules\n    const Engine = Matter.Engine;\n    const Render = Matter.Render;\n    const World = Matter.World;\n    const Bodies = Matter.Bodies;\n    const Body = Matter.Body;\n\n    // Create engine\n    engineRef.current = Engine.create({\n      gravity: {\n        x: 0,\n        y: gravity,\n        scale: 1\n      }\n    });\n\n    // Create renderer\n    const render = Render.create({\n      canvas: canvasRef.current,\n      engine: engineRef.current,\n      options: {\n        width: 352,\n        height: 352,\n        wireframes: false,\n        background: '#000000',\n        showAngleIndicator: false,\n        showCollisions: false,\n        showVelocity: false\n      }\n    });\n\n    // Create hexagon\n    const hexagonRadius = 132;\n    const hexagonSides = 6;\n    const centerX = render.options.width / 2;\n    const centerY = render.options.height / 2;\n\n    // Create hexagon vertices\n    const hexagonVertices = [];\n    for (let i = 0; i < hexagonSides; i++) {\n      const angle = Math.PI * 2 * i / hexagonSides;\n      const x = hexagonRadius * Math.cos(angle);\n      const y = hexagonRadius * Math.sin(angle);\n      hexagonVertices.push({\n        x,\n        y\n      });\n    }\n\n    // Create hexagon edges\n    hexagonEdgesRef.current = [];\n    for (let i = 0; i < hexagonSides; i++) {\n      const j = (i + 1) % hexagonSides;\n      const edgeOptions = {\n        restitution: 0.7,\n        friction: 0.1,\n        isStatic: true,\n        render: {\n          fillStyle: 'transparent',\n          strokeStyle: '#FFFFFF',\n          lineWidth: 1\n        }\n      };\n\n      // Calculate edge position and angle\n      const vertex1 = hexagonVertices[i];\n      const vertex2 = hexagonVertices[j];\n      const edgeLength = Math.sqrt(Math.pow(vertex2.x - vertex1.x, 2) + Math.pow(vertex2.y - vertex1.y, 2));\n      const edgeAngle = Math.atan2(vertex2.y - vertex1.y, vertex2.x - vertex1.x);\n      const edgeCenterX = centerX + (vertex1.x + vertex2.x) / 2;\n      const edgeCenterY = centerY + (vertex1.y + vertex2.y) / 2;\n\n      // Create edge\n      const edge = Bodies.rectangle(edgeCenterX, edgeCenterY, edgeLength, 1, edgeOptions);\n\n      // Rotate edge to angle\n      Body.rotate(edge, edgeAngle);\n      hexagonEdgesRef.current.push(edge);\n    }\n\n    // Create ball\n    const originalBallRadius = 15;\n    // Reduce by additional 10% from current size (72% * 0.9 = 64.8%)\n    const ballRadius = originalBallRadius * 0.648; // 72% * 0.9 = 64.8% of original size\n\n    // Place ball inside hexagon, closer to center\n    ballRef.current = Bodies.circle(centerX, centerY, ballRadius, {\n      restitution: restitution,\n      friction: 0.05,\n      frictionAir: 0.001,\n      density: 0.001,\n      render: {\n        fillStyle: '#F0A500'\n      }\n    });\n\n    // Add initial force to start ball movement\n    Body.applyForce(ballRef.current, ballRef.current.position, {\n      x: 0.0002,\n      y: -0.0002\n    });\n\n    // Add bodies to world\n    World.add(engineRef.current.world, [...hexagonEdgesRef.current, ballRef.current]);\n\n    // Run renderer\n    Render.run(render);\n\n    // Animation loop function\n    const animate = () => {\n      // Update engine\n      Engine.update(engineRef.current, 16.667);\n\n      // Rotate hexagon edges\n      if (hexagonEdgesRef.current.length > 0) {\n        hexagonEdgesRef.current.forEach(edge => {\n          // Rotate edge around hexagon center\n          Body.setPosition(edge, {\n            x: centerX + (edge.position.x - centerX) * Math.cos(rotationSpeed) - (edge.position.y - centerY) * Math.sin(rotationSpeed),\n            y: centerY + (edge.position.x - centerX) * Math.sin(rotationSpeed) + (edge.position.y - centerY) * Math.cos(rotationSpeed)\n          });\n          Body.rotate(edge, rotationSpeed);\n        });\n      }\n\n      // Check ball position\n      if (ballRef.current) {\n        const ballPos = ballRef.current.position;\n\n        // Calculate distance from ball to hexagon center\n        const distanceFromCenter = Math.sqrt(Math.pow(ballPos.x - centerX, 2) + Math.pow(ballPos.y - centerY, 2));\n\n        // If ball goes beyond hexagon safe boundary\n        const safeHexagonRadius = hexagonRadius * 0.85; // Safe boundary inside hexagon\n\n        if (distanceFromCenter > safeHexagonRadius) {\n          // Calculate vector from hexagon center to ball\n          const directionX = ballPos.x - centerX;\n          const directionY = ballPos.y - centerY;\n          const magnitude = Math.sqrt(directionX * directionX + directionY * directionY);\n\n          // Normalize vector\n          const normalizedX = directionX / magnitude;\n          const normalizedY = directionY / magnitude;\n\n          // Reset ball position to within safe boundary\n          const newPosX = centerX + normalizedX * safeHexagonRadius;\n          const newPosY = centerY + normalizedY * safeHexagonRadius;\n\n          // Set new ball position\n          Matter.Body.setPosition(ballRef.current, {\n            x: newPosX,\n            y: newPosY\n          });\n\n          // Adjust velocity for ball to bounce inward\n          const currentVelocity = ballRef.current.velocity;\n          const dotProduct = currentVelocity.x * normalizedX + currentVelocity.y * normalizedY;\n\n          // If ball is moving outward, reverse direction\n          if (dotProduct > 0) {\n            const newVelocityX = currentVelocity.x - 2 * dotProduct * normalizedX;\n            const newVelocityY = currentVelocity.y - 2 * dotProduct * normalizedY;\n            Matter.Body.setVelocity(ballRef.current, {\n              x: newVelocityX * restitution,\n              y: newVelocityY * restitution\n            });\n          }\n        }\n\n        // Ensure ball keeps moving\n        const velocity = ballRef.current.velocity;\n        const speed = Math.sqrt(velocity.x * velocity.x + velocity.y * velocity.y);\n\n        // If ball moves too slowly, add small force\n        if (speed < 0.5) {\n          const randomAngle = Math.random() * Math.PI * 2;\n          const forceMagnitude = 0.0001;\n          Body.applyForce(ballRef.current, ballRef.current.position, {\n            x: forceMagnitude * Math.cos(randomAngle),\n            y: forceMagnitude * Math.sin(randomAngle)\n          });\n        }\n      }\n\n      // Continue animation loop\n      requestRef.current = requestAnimationFrame(animate);\n    };\n\n    // Start animation loop\n    requestRef.current = requestAnimationFrame(animate);\n\n    // Cleanup when component unmounts\n    return () => {\n      // Cancel animation loop\n      if (requestRef.current) {\n        cancelAnimationFrame(requestRef.current);\n      }\n\n      // Cleanup renderer and engine\n      Render.stop(render);\n      World.clear(engineRef.current.world);\n      Engine.clear(engineRef.current);\n      render.canvas = null;\n      render.context = null;\n      render.textures = {};\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(Box, {\n    className: styles.loadingContainer,\n    sx: {\n      ...(!fromCreatePlan && {\n        minHeight: '90vh'\n      })\n    },\n    children: /*#__PURE__*/_jsxDEV(Box, {\n      className: styles.gameWrapper,\n      children: /*#__PURE__*/_jsxDEV(Box, {\n        className: styles.hexagonLoadingContainer,\n        children: /*#__PURE__*/_jsxDEV(\"canvas\", {\n          ref: canvasRef,\n          className: styles.hexagonCanvas\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 245,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 244,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 242,\n    columnNumber: 5\n  }, this);\n};\n_s(HexagonBallLoading, \"uDBlP5n4TAfWzOEW2LE7YQXblas=\");\n_c = HexagonBallLoading;\nexport default HexagonBallLoading;\nvar _c;\n$RefreshReg$(_c, \"HexagonBallLoading\");", "map": {"version": 3, "names": ["React", "useEffect", "useRef", "Box", "Matter", "styles", "jsxDEV", "_jsxDEV", "HexagonBallLoading", "_ref", "_s", "fromCreatePlan", "canvasRef", "engineRef", "requestRef", "ballRef", "hexagonEdgesRef", "rotationSpeed", "gravity", "restitution", "Engine", "Render", "World", "Bodies", "Body", "current", "create", "x", "y", "scale", "render", "canvas", "engine", "options", "width", "height", "wireframes", "background", "showAngleIndicator", "showCollisions", "showVelocity", "hexagonRadius", "hexagonSides", "centerX", "centerY", "hexagonVertices", "i", "angle", "Math", "PI", "cos", "sin", "push", "j", "edgeOptions", "friction", "isStatic", "fillStyle", "strokeStyle", "lineWidth", "vertex1", "vertex2", "edge<PERSON><PERSON><PERSON>", "sqrt", "pow", "edgeAngle", "atan2", "edgeCenterX", "edgeCenterY", "edge", "rectangle", "rotate", "originalBallRadius", "ballRadius", "circle", "frictionAir", "density", "applyForce", "position", "add", "world", "run", "animate", "update", "length", "for<PERSON>ach", "setPosition", "ballPos", "distanceFromCenter", "safeHexagonRadius", "directionX", "directionY", "magnitude", "normalizedX", "normalizedY", "newPosX", "newPosY", "currentVelocity", "velocity", "dotProduct", "newVelocityX", "newVelocityY", "setVelocity", "speed", "randomAngle", "random", "forceMagnitude", "requestAnimationFrame", "cancelAnimationFrame", "stop", "clear", "context", "textures", "className", "loadingContainer", "sx", "minHeight", "children", "gameWrapper", "hexagonLoadingContainer", "ref", "hexagonCanvas", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/ignition/ignition-ui/src/components/Loading/HexagonBallLoading.js"], "sourcesContent": ["import React, { useEffect, useRef } from 'react';\r\nimport { Box } from '@mui/material';\r\nimport Matter from 'matter-js';\r\nimport styles from './styles.module.scss';\r\n\r\n/**\r\n * Component displays loading screen with a bouncing ball inside a rotating hexagon\r\n * @param {Object} props - Component props\r\n * @param {boolean} props.fromCreatePlan - Flag to determine if component is called from CreatePlan page\r\n * @returns {JSX.Element} - HexagonBallLoading component\r\n */\r\nconst HexagonBallLoading = ({ fromCreatePlan }) => {\r\n  const canvasRef = useRef(null);\r\n  const engineRef = useRef(null);\r\n  const requestRef = useRef(null);\r\n  const ballRef = useRef(null);\r\n  const hexagonEdgesRef = useRef([]);\r\n\r\n  useEffect(() => {\r\n    // Set optimal parameters for natural motion\r\n    const rotationSpeed = 0.01; // Moderate rotation speed\r\n    const gravity = 0.001; // Sufficient gravity\r\n    const restitution = 0.8; // High bounce for continuous ball movement\r\n\r\n    // Initialize Matter.js modules\r\n    const Engine = Matter.Engine;\r\n    const Render = Matter.Render;\r\n    const World = Matter.World;\r\n    const Bodies = Matter.Bodies;\r\n    const Body = Matter.Body;\r\n\r\n    // Create engine\r\n    engineRef.current = Engine.create({\r\n      gravity: { x: 0, y: gravity, scale: 1 },\r\n    });\r\n\r\n    // Create renderer\r\n    const render = Render.create({\r\n      canvas: canvasRef.current,\r\n      engine: engineRef.current,\r\n      options: {\r\n        width: 352,\r\n        height: 352,\r\n        wireframes: false,\r\n        background: '#000000',\r\n        showAngleIndicator: false,\r\n        showCollisions: false,\r\n        showVelocity: false,\r\n      },\r\n    });\r\n\r\n    // Create hexagon\r\n    const hexagonRadius = 132;\r\n    const hexagonSides = 6;\r\n    const centerX = render.options.width / 2;\r\n    const centerY = render.options.height / 2;\r\n\r\n    // Create hexagon vertices\r\n    const hexagonVertices = [];\r\n    for (let i = 0; i < hexagonSides; i++) {\r\n      const angle = (Math.PI * 2 * i) / hexagonSides;\r\n      const x = hexagonRadius * Math.cos(angle);\r\n      const y = hexagonRadius * Math.sin(angle);\r\n      hexagonVertices.push({ x, y });\r\n    }\r\n\r\n    // Create hexagon edges\r\n    hexagonEdgesRef.current = [];\r\n    for (let i = 0; i < hexagonSides; i++) {\r\n      const j = (i + 1) % hexagonSides;\r\n      const edgeOptions = {\r\n        restitution: 0.7,\r\n        friction: 0.1,\r\n        isStatic: true,\r\n        render: {\r\n          fillStyle: 'transparent',\r\n          strokeStyle: '#FFFFFF',\r\n          lineWidth: 1,\r\n        },\r\n      };\r\n\r\n      // Calculate edge position and angle\r\n      const vertex1 = hexagonVertices[i];\r\n      const vertex2 = hexagonVertices[j];\r\n      const edgeLength = Math.sqrt(\r\n        Math.pow(vertex2.x - vertex1.x, 2) + Math.pow(vertex2.y - vertex1.y, 2)\r\n      );\r\n      const edgeAngle = Math.atan2(vertex2.y - vertex1.y, vertex2.x - vertex1.x);\r\n      const edgeCenterX = centerX + (vertex1.x + vertex2.x) / 2;\r\n      const edgeCenterY = centerY + (vertex1.y + vertex2.y) / 2;\r\n\r\n      // Create edge\r\n      const edge = Bodies.rectangle(\r\n        edgeCenterX,\r\n        edgeCenterY,\r\n        edgeLength,\r\n        1,\r\n        edgeOptions\r\n      );\r\n\r\n      // Rotate edge to angle\r\n      Body.rotate(edge, edgeAngle);\r\n\r\n      hexagonEdgesRef.current.push(edge);\r\n    }\r\n\r\n    // Create ball\r\n    const originalBallRadius = 15;\r\n    // Reduce by additional 10% from current size (72% * 0.9 = 64.8%)\r\n    const ballRadius = originalBallRadius * 0.648; // 72% * 0.9 = 64.8% of original size\r\n\r\n    // Place ball inside hexagon, closer to center\r\n    ballRef.current = Bodies.circle(centerX, centerY, ballRadius, {\r\n      restitution: restitution,\r\n      friction: 0.05,\r\n      frictionAir: 0.001,\r\n      density: 0.001,\r\n      render: {\r\n        fillStyle: '#F0A500',\r\n      },\r\n    });\r\n\r\n    // Add initial force to start ball movement\r\n    Body.applyForce(ballRef.current, ballRef.current.position, {\r\n      x: 0.0002,\r\n      y: -0.0002\r\n    });\r\n\r\n    // Add bodies to world\r\n    World.add(engineRef.current.world, [...hexagonEdgesRef.current, ballRef.current]);\r\n\r\n    // Run renderer\r\n    Render.run(render);\r\n\r\n    // Animation loop function\r\n    const animate = () => {\r\n      // Update engine\r\n      Engine.update(engineRef.current, 16.667);\r\n\r\n      // Rotate hexagon edges\r\n      if (hexagonEdgesRef.current.length > 0) {\r\n        hexagonEdgesRef.current.forEach(edge => {\r\n          // Rotate edge around hexagon center\r\n          Body.setPosition(edge, {\r\n            x: centerX + (edge.position.x - centerX) * Math.cos(rotationSpeed) - (edge.position.y - centerY) * Math.sin(rotationSpeed),\r\n            y: centerY + (edge.position.x - centerX) * Math.sin(rotationSpeed) + (edge.position.y - centerY) * Math.cos(rotationSpeed)\r\n          });\r\n          Body.rotate(edge, rotationSpeed);\r\n        });\r\n      }\r\n\r\n      // Check ball position\r\n      if (ballRef.current) {\r\n        const ballPos = ballRef.current.position;\r\n\r\n        // Calculate distance from ball to hexagon center\r\n        const distanceFromCenter = Math.sqrt(\r\n          Math.pow(ballPos.x - centerX, 2) + Math.pow(ballPos.y - centerY, 2)\r\n        );\r\n\r\n        // If ball goes beyond hexagon safe boundary\r\n        const safeHexagonRadius = hexagonRadius * 0.85; // Safe boundary inside hexagon\r\n\r\n        if (distanceFromCenter > safeHexagonRadius) {\r\n          // Calculate vector from hexagon center to ball\r\n          const directionX = ballPos.x - centerX;\r\n          const directionY = ballPos.y - centerY;\r\n          const magnitude = Math.sqrt(directionX * directionX + directionY * directionY);\r\n\r\n          // Normalize vector\r\n          const normalizedX = directionX / magnitude;\r\n          const normalizedY = directionY / magnitude;\r\n\r\n          // Reset ball position to within safe boundary\r\n          const newPosX = centerX + normalizedX * safeHexagonRadius;\r\n          const newPosY = centerY + normalizedY * safeHexagonRadius;\r\n\r\n          // Set new ball position\r\n          Matter.Body.setPosition(ballRef.current, {\r\n            x: newPosX,\r\n            y: newPosY\r\n          });\r\n\r\n          // Adjust velocity for ball to bounce inward\r\n          const currentVelocity = ballRef.current.velocity;\r\n          const dotProduct =\r\n            currentVelocity.x * normalizedX +\r\n            currentVelocity.y * normalizedY;\r\n\r\n          // If ball is moving outward, reverse direction\r\n          if (dotProduct > 0) {\r\n            const newVelocityX = currentVelocity.x - 2 * dotProduct * normalizedX;\r\n            const newVelocityY = currentVelocity.y - 2 * dotProduct * normalizedY;\r\n\r\n            Matter.Body.setVelocity(ballRef.current, {\r\n              x: newVelocityX * restitution,\r\n              y: newVelocityY * restitution\r\n            });\r\n          }\r\n        }\r\n\r\n        // Ensure ball keeps moving\r\n        const velocity = ballRef.current.velocity;\r\n        const speed = Math.sqrt(velocity.x * velocity.x + velocity.y * velocity.y);\r\n\r\n        // If ball moves too slowly, add small force\r\n        if (speed < 0.5) {\r\n          const randomAngle = Math.random() * Math.PI * 2;\r\n          const forceMagnitude = 0.0001;\r\n          Body.applyForce(ballRef.current, ballRef.current.position, {\r\n            x: forceMagnitude * Math.cos(randomAngle),\r\n            y: forceMagnitude * Math.sin(randomAngle)\r\n          });\r\n        }\r\n      }\r\n\r\n      // Continue animation loop\r\n      requestRef.current = requestAnimationFrame(animate);\r\n    };\r\n\r\n    // Start animation loop\r\n    requestRef.current = requestAnimationFrame(animate);\r\n\r\n    // Cleanup when component unmounts\r\n    return () => {\r\n      // Cancel animation loop\r\n      if (requestRef.current) {\r\n        cancelAnimationFrame(requestRef.current);\r\n      }\r\n\r\n      // Cleanup renderer and engine\r\n      Render.stop(render);\r\n      World.clear(engineRef.current.world);\r\n      Engine.clear(engineRef.current);\r\n      render.canvas = null;\r\n      render.context = null;\r\n      render.textures = {};\r\n    };\r\n  }, []);\r\n\r\n  return (\r\n    <Box className={styles.loadingContainer}\r\n      sx={{ ...(!fromCreatePlan && { minHeight: '90vh' }) }}>\r\n      <Box className={styles.gameWrapper}>\r\n        <Box className={styles.hexagonLoadingContainer}>\r\n          <canvas ref={canvasRef} className={styles.hexagonCanvas} />\r\n        </Box>\r\n      </Box>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default HexagonBallLoading;\r\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAChD,SAASC,GAAG,QAAQ,eAAe;AACnC,OAAOC,MAAM,MAAM,WAAW;AAC9B,OAAOC,MAAM,MAAM,sBAAsB;;AAEzC;AACA;AACA;AACA;AACA;AACA;AALA,SAAAC,MAAA,IAAAC,OAAA;AAMA,MAAMC,kBAAkB,GAAGC,IAAA,IAAwB;EAAAC,EAAA;EAAA,IAAvB;IAAEC;EAAe,CAAC,GAAAF,IAAA;EAC5C,MAAMG,SAAS,GAAGV,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMW,SAAS,GAAGX,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMY,UAAU,GAAGZ,MAAM,CAAC,IAAI,CAAC;EAC/B,MAAMa,OAAO,GAAGb,MAAM,CAAC,IAAI,CAAC;EAC5B,MAAMc,eAAe,GAAGd,MAAM,CAAC,EAAE,CAAC;EAElCD,SAAS,CAAC,MAAM;IACd;IACA,MAAMgB,aAAa,GAAG,IAAI,CAAC,CAAC;IAC5B,MAAMC,OAAO,GAAG,KAAK,CAAC,CAAC;IACvB,MAAMC,WAAW,GAAG,GAAG,CAAC,CAAC;;IAEzB;IACA,MAAMC,MAAM,GAAGhB,MAAM,CAACgB,MAAM;IAC5B,MAAMC,MAAM,GAAGjB,MAAM,CAACiB,MAAM;IAC5B,MAAMC,KAAK,GAAGlB,MAAM,CAACkB,KAAK;IAC1B,MAAMC,MAAM,GAAGnB,MAAM,CAACmB,MAAM;IAC5B,MAAMC,IAAI,GAAGpB,MAAM,CAACoB,IAAI;;IAExB;IACAX,SAAS,CAACY,OAAO,GAAGL,MAAM,CAACM,MAAM,CAAC;MAChCR,OAAO,EAAE;QAAES,CAAC,EAAE,CAAC;QAAEC,CAAC,EAAEV,OAAO;QAAEW,KAAK,EAAE;MAAE;IACxC,CAAC,CAAC;;IAEF;IACA,MAAMC,MAAM,GAAGT,MAAM,CAACK,MAAM,CAAC;MAC3BK,MAAM,EAAEnB,SAAS,CAACa,OAAO;MACzBO,MAAM,EAAEnB,SAAS,CAACY,OAAO;MACzBQ,OAAO,EAAE;QACPC,KAAK,EAAE,GAAG;QACVC,MAAM,EAAE,GAAG;QACXC,UAAU,EAAE,KAAK;QACjBC,UAAU,EAAE,SAAS;QACrBC,kBAAkB,EAAE,KAAK;QACzBC,cAAc,EAAE,KAAK;QACrBC,YAAY,EAAE;MAChB;IACF,CAAC,CAAC;;IAEF;IACA,MAAMC,aAAa,GAAG,GAAG;IACzB,MAAMC,YAAY,GAAG,CAAC;IACtB,MAAMC,OAAO,GAAGb,MAAM,CAACG,OAAO,CAACC,KAAK,GAAG,CAAC;IACxC,MAAMU,OAAO,GAAGd,MAAM,CAACG,OAAO,CAACE,MAAM,GAAG,CAAC;;IAEzC;IACA,MAAMU,eAAe,GAAG,EAAE;IAC1B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,YAAY,EAAEI,CAAC,EAAE,EAAE;MACrC,MAAMC,KAAK,GAAIC,IAAI,CAACC,EAAE,GAAG,CAAC,GAAGH,CAAC,GAAIJ,YAAY;MAC9C,MAAMf,CAAC,GAAGc,aAAa,GAAGO,IAAI,CAACE,GAAG,CAACH,KAAK,CAAC;MACzC,MAAMnB,CAAC,GAAGa,aAAa,GAAGO,IAAI,CAACG,GAAG,CAACJ,KAAK,CAAC;MACzCF,eAAe,CAACO,IAAI,CAAC;QAAEzB,CAAC;QAAEC;MAAE,CAAC,CAAC;IAChC;;IAEA;IACAZ,eAAe,CAACS,OAAO,GAAG,EAAE;IAC5B,KAAK,IAAIqB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,YAAY,EAAEI,CAAC,EAAE,EAAE;MACrC,MAAMO,CAAC,GAAG,CAACP,CAAC,GAAG,CAAC,IAAIJ,YAAY;MAChC,MAAMY,WAAW,GAAG;QAClBnC,WAAW,EAAE,GAAG;QAChBoC,QAAQ,EAAE,GAAG;QACbC,QAAQ,EAAE,IAAI;QACd1B,MAAM,EAAE;UACN2B,SAAS,EAAE,aAAa;UACxBC,WAAW,EAAE,SAAS;UACtBC,SAAS,EAAE;QACb;MACF,CAAC;;MAED;MACA,MAAMC,OAAO,GAAGf,eAAe,CAACC,CAAC,CAAC;MAClC,MAAMe,OAAO,GAAGhB,eAAe,CAACQ,CAAC,CAAC;MAClC,MAAMS,UAAU,GAAGd,IAAI,CAACe,IAAI,CAC1Bf,IAAI,CAACgB,GAAG,CAACH,OAAO,CAAClC,CAAC,GAAGiC,OAAO,CAACjC,CAAC,EAAE,CAAC,CAAC,GAAGqB,IAAI,CAACgB,GAAG,CAACH,OAAO,CAACjC,CAAC,GAAGgC,OAAO,CAAChC,CAAC,EAAE,CAAC,CACxE,CAAC;MACD,MAAMqC,SAAS,GAAGjB,IAAI,CAACkB,KAAK,CAACL,OAAO,CAACjC,CAAC,GAAGgC,OAAO,CAAChC,CAAC,EAAEiC,OAAO,CAAClC,CAAC,GAAGiC,OAAO,CAACjC,CAAC,CAAC;MAC1E,MAAMwC,WAAW,GAAGxB,OAAO,GAAG,CAACiB,OAAO,CAACjC,CAAC,GAAGkC,OAAO,CAAClC,CAAC,IAAI,CAAC;MACzD,MAAMyC,WAAW,GAAGxB,OAAO,GAAG,CAACgB,OAAO,CAAChC,CAAC,GAAGiC,OAAO,CAACjC,CAAC,IAAI,CAAC;;MAEzD;MACA,MAAMyC,IAAI,GAAG9C,MAAM,CAAC+C,SAAS,CAC3BH,WAAW,EACXC,WAAW,EACXN,UAAU,EACV,CAAC,EACDR,WACF,CAAC;;MAED;MACA9B,IAAI,CAAC+C,MAAM,CAACF,IAAI,EAAEJ,SAAS,CAAC;MAE5BjD,eAAe,CAACS,OAAO,CAAC2B,IAAI,CAACiB,IAAI,CAAC;IACpC;;IAEA;IACA,MAAMG,kBAAkB,GAAG,EAAE;IAC7B;IACA,MAAMC,UAAU,GAAGD,kBAAkB,GAAG,KAAK,CAAC,CAAC;;IAE/C;IACAzD,OAAO,CAACU,OAAO,GAAGF,MAAM,CAACmD,MAAM,CAAC/B,OAAO,EAAEC,OAAO,EAAE6B,UAAU,EAAE;MAC5DtD,WAAW,EAAEA,WAAW;MACxBoC,QAAQ,EAAE,IAAI;MACdoB,WAAW,EAAE,KAAK;MAClBC,OAAO,EAAE,KAAK;MACd9C,MAAM,EAAE;QACN2B,SAAS,EAAE;MACb;IACF,CAAC,CAAC;;IAEF;IACAjC,IAAI,CAACqD,UAAU,CAAC9D,OAAO,CAACU,OAAO,EAAEV,OAAO,CAACU,OAAO,CAACqD,QAAQ,EAAE;MACzDnD,CAAC,EAAE,MAAM;MACTC,CAAC,EAAE,CAAC;IACN,CAAC,CAAC;;IAEF;IACAN,KAAK,CAACyD,GAAG,CAAClE,SAAS,CAACY,OAAO,CAACuD,KAAK,EAAE,CAAC,GAAGhE,eAAe,CAACS,OAAO,EAAEV,OAAO,CAACU,OAAO,CAAC,CAAC;;IAEjF;IACAJ,MAAM,CAAC4D,GAAG,CAACnD,MAAM,CAAC;;IAElB;IACA,MAAMoD,OAAO,GAAGA,CAAA,KAAM;MACpB;MACA9D,MAAM,CAAC+D,MAAM,CAACtE,SAAS,CAACY,OAAO,EAAE,MAAM,CAAC;;MAExC;MACA,IAAIT,eAAe,CAACS,OAAO,CAAC2D,MAAM,GAAG,CAAC,EAAE;QACtCpE,eAAe,CAACS,OAAO,CAAC4D,OAAO,CAAChB,IAAI,IAAI;UACtC;UACA7C,IAAI,CAAC8D,WAAW,CAACjB,IAAI,EAAE;YACrB1C,CAAC,EAAEgB,OAAO,GAAG,CAAC0B,IAAI,CAACS,QAAQ,CAACnD,CAAC,GAAGgB,OAAO,IAAIK,IAAI,CAACE,GAAG,CAACjC,aAAa,CAAC,GAAG,CAACoD,IAAI,CAACS,QAAQ,CAAClD,CAAC,GAAGgB,OAAO,IAAII,IAAI,CAACG,GAAG,CAAClC,aAAa,CAAC;YAC1HW,CAAC,EAAEgB,OAAO,GAAG,CAACyB,IAAI,CAACS,QAAQ,CAACnD,CAAC,GAAGgB,OAAO,IAAIK,IAAI,CAACG,GAAG,CAAClC,aAAa,CAAC,GAAG,CAACoD,IAAI,CAACS,QAAQ,CAAClD,CAAC,GAAGgB,OAAO,IAAII,IAAI,CAACE,GAAG,CAACjC,aAAa;UAC3H,CAAC,CAAC;UACFO,IAAI,CAAC+C,MAAM,CAACF,IAAI,EAAEpD,aAAa,CAAC;QAClC,CAAC,CAAC;MACJ;;MAEA;MACA,IAAIF,OAAO,CAACU,OAAO,EAAE;QACnB,MAAM8D,OAAO,GAAGxE,OAAO,CAACU,OAAO,CAACqD,QAAQ;;QAExC;QACA,MAAMU,kBAAkB,GAAGxC,IAAI,CAACe,IAAI,CAClCf,IAAI,CAACgB,GAAG,CAACuB,OAAO,CAAC5D,CAAC,GAAGgB,OAAO,EAAE,CAAC,CAAC,GAAGK,IAAI,CAACgB,GAAG,CAACuB,OAAO,CAAC3D,CAAC,GAAGgB,OAAO,EAAE,CAAC,CACpE,CAAC;;QAED;QACA,MAAM6C,iBAAiB,GAAGhD,aAAa,GAAG,IAAI,CAAC,CAAC;;QAEhD,IAAI+C,kBAAkB,GAAGC,iBAAiB,EAAE;UAC1C;UACA,MAAMC,UAAU,GAAGH,OAAO,CAAC5D,CAAC,GAAGgB,OAAO;UACtC,MAAMgD,UAAU,GAAGJ,OAAO,CAAC3D,CAAC,GAAGgB,OAAO;UACtC,MAAMgD,SAAS,GAAG5C,IAAI,CAACe,IAAI,CAAC2B,UAAU,GAAGA,UAAU,GAAGC,UAAU,GAAGA,UAAU,CAAC;;UAE9E;UACA,MAAME,WAAW,GAAGH,UAAU,GAAGE,SAAS;UAC1C,MAAME,WAAW,GAAGH,UAAU,GAAGC,SAAS;;UAE1C;UACA,MAAMG,OAAO,GAAGpD,OAAO,GAAGkD,WAAW,GAAGJ,iBAAiB;UACzD,MAAMO,OAAO,GAAGpD,OAAO,GAAGkD,WAAW,GAAGL,iBAAiB;;UAEzD;UACArF,MAAM,CAACoB,IAAI,CAAC8D,WAAW,CAACvE,OAAO,CAACU,OAAO,EAAE;YACvCE,CAAC,EAAEoE,OAAO;YACVnE,CAAC,EAAEoE;UACL,CAAC,CAAC;;UAEF;UACA,MAAMC,eAAe,GAAGlF,OAAO,CAACU,OAAO,CAACyE,QAAQ;UAChD,MAAMC,UAAU,GACdF,eAAe,CAACtE,CAAC,GAAGkE,WAAW,GAC/BI,eAAe,CAACrE,CAAC,GAAGkE,WAAW;;UAEjC;UACA,IAAIK,UAAU,GAAG,CAAC,EAAE;YAClB,MAAMC,YAAY,GAAGH,eAAe,CAACtE,CAAC,GAAG,CAAC,GAAGwE,UAAU,GAAGN,WAAW;YACrE,MAAMQ,YAAY,GAAGJ,eAAe,CAACrE,CAAC,GAAG,CAAC,GAAGuE,UAAU,GAAGL,WAAW;YAErE1F,MAAM,CAACoB,IAAI,CAAC8E,WAAW,CAACvF,OAAO,CAACU,OAAO,EAAE;cACvCE,CAAC,EAAEyE,YAAY,GAAGjF,WAAW;cAC7BS,CAAC,EAAEyE,YAAY,GAAGlF;YACpB,CAAC,CAAC;UACJ;QACF;;QAEA;QACA,MAAM+E,QAAQ,GAAGnF,OAAO,CAACU,OAAO,CAACyE,QAAQ;QACzC,MAAMK,KAAK,GAAGvD,IAAI,CAACe,IAAI,CAACmC,QAAQ,CAACvE,CAAC,GAAGuE,QAAQ,CAACvE,CAAC,GAAGuE,QAAQ,CAACtE,CAAC,GAAGsE,QAAQ,CAACtE,CAAC,CAAC;;QAE1E;QACA,IAAI2E,KAAK,GAAG,GAAG,EAAE;UACf,MAAMC,WAAW,GAAGxD,IAAI,CAACyD,MAAM,CAAC,CAAC,GAAGzD,IAAI,CAACC,EAAE,GAAG,CAAC;UAC/C,MAAMyD,cAAc,GAAG,MAAM;UAC7BlF,IAAI,CAACqD,UAAU,CAAC9D,OAAO,CAACU,OAAO,EAAEV,OAAO,CAACU,OAAO,CAACqD,QAAQ,EAAE;YACzDnD,CAAC,EAAE+E,cAAc,GAAG1D,IAAI,CAACE,GAAG,CAACsD,WAAW,CAAC;YACzC5E,CAAC,EAAE8E,cAAc,GAAG1D,IAAI,CAACG,GAAG,CAACqD,WAAW;UAC1C,CAAC,CAAC;QACJ;MACF;;MAEA;MACA1F,UAAU,CAACW,OAAO,GAAGkF,qBAAqB,CAACzB,OAAO,CAAC;IACrD,CAAC;;IAED;IACApE,UAAU,CAACW,OAAO,GAAGkF,qBAAqB,CAACzB,OAAO,CAAC;;IAEnD;IACA,OAAO,MAAM;MACX;MACA,IAAIpE,UAAU,CAACW,OAAO,EAAE;QACtBmF,oBAAoB,CAAC9F,UAAU,CAACW,OAAO,CAAC;MAC1C;;MAEA;MACAJ,MAAM,CAACwF,IAAI,CAAC/E,MAAM,CAAC;MACnBR,KAAK,CAACwF,KAAK,CAACjG,SAAS,CAACY,OAAO,CAACuD,KAAK,CAAC;MACpC5D,MAAM,CAAC0F,KAAK,CAACjG,SAAS,CAACY,OAAO,CAAC;MAC/BK,MAAM,CAACC,MAAM,GAAG,IAAI;MACpBD,MAAM,CAACiF,OAAO,GAAG,IAAI;MACrBjF,MAAM,CAACkF,QAAQ,GAAG,CAAC,CAAC;IACtB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEzG,OAAA,CAACJ,GAAG;IAAC8G,SAAS,EAAE5G,MAAM,CAAC6G,gBAAiB;IACtCC,EAAE,EAAE;MAAE,IAAI,CAACxG,cAAc,IAAI;QAAEyG,SAAS,EAAE;MAAO,CAAC;IAAE,CAAE;IAAAC,QAAA,eACtD9G,OAAA,CAACJ,GAAG;MAAC8G,SAAS,EAAE5G,MAAM,CAACiH,WAAY;MAAAD,QAAA,eACjC9G,OAAA,CAACJ,GAAG;QAAC8G,SAAS,EAAE5G,MAAM,CAACkH,uBAAwB;QAAAF,QAAA,eAC7C9G,OAAA;UAAQiH,GAAG,EAAE5G,SAAU;UAACqG,SAAS,EAAE5G,MAAM,CAACoH;QAAc;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACnH,EAAA,CA/OIF,kBAAkB;AAAAsH,EAAA,GAAlBtH,kBAAkB;AAiPxB,eAAeA,kBAAkB;AAAC,IAAAsH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}