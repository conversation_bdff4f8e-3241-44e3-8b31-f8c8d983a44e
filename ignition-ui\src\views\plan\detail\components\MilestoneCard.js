import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Chip,
  LinearProgress,
  Button,
  Divider,
  TextField,
  IconButton,
  Tooltip,
  Checkbox,
  CircularProgress,
  Avatar,
  AvatarGroup,
  Collapse
} from '@mui/material';
import Iconify from 'components/Iconify/index';
import { mainYellowColor } from "helpers/constants";
import { STATUS, STATUS_CONFIG } from '../hooks/usePlanData';
import styles from '../styles.module.scss';
import CommentDialog from '../dialogs/CommentDialog';
import DueDateDialog from '../dialogs/DueDateDialog';
import AssignMemberDialog from '../dialogs/AssignMemberDialog';
import { getComments, addComment, updateComment, deleteComment, updateTask, assignMembersToTask } from '../../services';
import { toast } from 'react-toastify';

const MilestoneCard = ({
  milestone,
  compact = false,
  showSubtasks = false,
  calculateMilestoneProgress,
  getMilestoneStatus,
  calculateTaskProgress,
  getTaskStatus,
  calculateSubtaskProgress,
  getSubtaskStatus,
  onUpdateMilestone,
  onUpdateTask,
  onUpdateSubtask,
  onAddTask,
  onAddSubtask,
  onDeleteTask,
  onDeleteSubtask,
  onAssignMembers,
  invitedUsers,
  planOwner
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [milestoneName, setMilestoneName] = useState(milestone.name);
  const [isAddingTask, setIsAddingTask] = useState(false);
  const [newTaskName, setNewTaskName] = useState('');
  // Initialize collapse state from localStorage
  const [isExpanded, setIsExpanded] = useState(() => {
    const savedState = localStorage.getItem(`milestone_${milestone.id || milestone.name}_expanded`);
    return savedState !== null ? JSON.parse(savedState) : true; // Default to expanded
  });
  const inputRef = useRef(null);
  const newTaskInputRef = useRef(null);

  // Handle expand/collapse toggle with state persistence
  const handleToggleExpanded = () => {
    const newExpandedState = !isExpanded;
    setIsExpanded(newExpandedState);
    // Save state to localStorage
    localStorage.setItem(`milestone_${milestone.id || milestone.name}_expanded`, JSON.stringify(newExpandedState));
  };

  // Calculate milestone progress
  const progress = calculateMilestoneProgress ? calculateMilestoneProgress(milestone) : 0;

  // Determine status based on progress
  const milestoneStatus = getMilestoneStatus ? getMilestoneStatus(milestone) : STATUS.NOT_STARTED;
  const statusConfig = STATUS_CONFIG[milestoneStatus] || STATUS_CONFIG[STATUS.NOT_STARTED];

  // Check if description exists and is not empty
  const hasDescription = milestone.description && milestone.description.trim().length > 0;

  // Handle edit mode
  const handleEditClick = () => {
    setIsEditing(true);
  };

  // Handle save changes
  const handleSave = () => {
    // Trim the name to remove leading/trailing whitespace
    const trimmedName = milestoneName.trim();

    // Only update if the name has actually changed (after trimming)
    if (trimmedName !== '' && trimmedName !== milestone.name) {
      if (onUpdateMilestone) {
        onUpdateMilestone({ ...milestone, name: trimmedName });
      }
    } else {
      // Reset to original if empty or unchanged
      setMilestoneName(milestone.name);
    }

    setIsEditing(false);
  };

  // Handle key press events
  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSave();
    } else if (e.key === 'Escape') {
      setMilestoneName(milestone.name);
      setIsEditing(false);
    }
  };

  // Handle add task
  const handleAddTaskClick = () => {
    setIsAddingTask(true);
  };

  // Handle save new task
  const handleSaveNewTask = () => {
    const trimmedName = newTaskName.trim();
    if (trimmedName && onAddTask) {
      const newTask = {
        name: trimmedName,
        milestone: milestone.id,
        status: STATUS.NOT_STARTED,
        progress: 0
      };
      onAddTask(newTask);
      setNewTaskName('');
    }
    setIsAddingTask(false);
  };

  // Handle key press events for new task
  const handleNewTaskKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSaveNewTask();
    } else if (e.key === 'Escape') {
      setNewTaskName('');
      setIsAddingTask(false);
    }
  };

  // Focus input when editing starts
  useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isEditing]);

  // Focus input when adding task
  useEffect(() => {
    if (isAddingTask && newTaskInputRef.current) {
      newTaskInputRef.current.focus();
    }
  }, [isAddingTask]);

  return (
    <Paper
      elevation={0}
      className={styles.milestoneCard}
      sx={{ padding: '16px' }}
    >
      <Box className={styles.milestoneHeader}>
        <Box sx={{ display: 'flex', alignItems: 'center', flex: 1 }}>
          {/* Expand/Collapse Button */}
          <IconButton
            size="small"
            onClick={handleToggleExpanded}
            sx={{
              mr: 1,
              color: '#666',
              '&:hover': {
                backgroundColor: 'rgba(0, 0, 0, 0.04)'
              }
            }}
          >
            <Iconify
              icon={isExpanded ? "material-symbols:expand-less" : "material-symbols:expand-more"}
              width={20}
              height={20}
            />
          </IconButton>

          {isEditing ? (
            <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
              <Iconify icon="material-symbols:flag" width={20} height={20} color={mainYellowColor} sx={{ mr: 1 }} />
              <TextField
                inputRef={inputRef}
                value={milestoneName}
                onChange={(e) => setMilestoneName(e.target.value)}
                onKeyDown={handleKeyPress}
                onBlur={handleSave}
                variant="standard"
                fullWidth
                autoFocus
                sx={{
                  fontFamily: '"Recursive Variable", sans-serif',
                  fontWeight: 600,
                  fontSize: '1.1rem',
                  '& .MuiInputBase-input': {
                    fontWeight: 600,
                    fontSize: '1.1rem',
                    fontFamily: '"Recursive Variable", sans-serif',
                  }
                }}
              />
              <IconButton size="small" onClick={handleSave} sx={{ ml: 1 }}>
                <Iconify icon="material-symbols:check" width={20} height={20} color="#4CAF50" />
              </IconButton>
            </Box>
          ) : (
            <Typography
              variant="h6"
              className={styles.milestoneTitle}
              sx={{
                fontFamily: '"Recursive Variable", sans-serif',
                display: 'flex',
                alignItems: 'center',
                cursor: 'pointer',
                flex: 1,
                '&:hover': {
                  '& .edit-icon': {
                    opacity: 1,
                  }
                }
              }}
              onClick={handleEditClick}
            >
              <Iconify icon="material-symbols:flag" width={20} height={20} color={mainYellowColor} />
              {milestoneName}
              <Tooltip title="Edit milestone name">
                <IconButton
                  size="small"
                  className="edit-icon"
                  sx={{
                    ml: 1,
                    opacity: 0,
                    transition: 'opacity 0.2s',
                    padding: '2px'
                  }}
                >
                  <Iconify icon="material-symbols:edit-outline" width={16} height={16} color="#666" />
                </IconButton>
              </Tooltip>
            </Typography>
          )}
        </Box>

        <Chip
          icon={<Iconify icon={statusConfig.icon} width={16} height={16} />}
          label={statusConfig.label}
          size="small"
          sx={{
            backgroundColor: `${statusConfig.color}20`,
            color: statusConfig.color,
            fontWeight: 600,
            borderRadius: '4px',
            fontFamily: '"Recursive Variable", sans-serif'
          }}
        />
      </Box>

      <Collapse in={isExpanded} timeout="auto" unmountOnExit>
        {/* Description Section */}
        {!compact && hasDescription && (
          <Box sx={{ mb: 2, mt: 1.5 }}>
            <Box
              sx={{
                backgroundColor: '#f9f9f9',
                p: 1.5,
                borderRadius: '8px',
                border: '1px solid #f0f0f0'
              }}
            >
              <Typography
                variant="body2"
                sx={{
                  color: '#333',
                  fontFamily: '"Recursive Variable", sans-serif',
                  whiteSpace: 'pre-line',
                  lineHeight: 1.6,
                  fontWeight: 500
                }}
              >
                {milestone.description}
              </Typography>
            </Box>
          </Box>
        )}

        <Divider sx={{ my: 2, opacity: 0.6 }} />

      <Box sx={{ mb: 2 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 0.5 }}>
          <Typography variant="body2" sx={{ fontWeight: 600, fontFamily: '"Recursive Variable", sans-serif', color: '#333' }}>
            Progress
          </Typography>
          <Typography variant="body2" sx={{ fontWeight: 700, fontFamily: '"Recursive Variable", sans-serif', color: '#333' }}>
            {progress}%
          </Typography>
        </Box>
        <LinearProgress
          variant="determinate"
          value={progress}
          sx={{
            height: 6,
            borderRadius: 3,
            backgroundColor: '#f0f0f0',
            '& .MuiLinearProgress-bar': {
              backgroundColor: statusConfig.color
            }
          }}
        />
      </Box>

      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
        {milestone.estimated_duration && (
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Iconify icon="material-symbols:timer" width={16} height={16} color="#333" />
            <Typography variant="body2" sx={{ fontFamily: '"Recursive Variable", sans-serif', color: '#333', fontWeight: 500 }}>
              {milestone.estimated_duration}
            </Typography>
          </Box>
        )}
      </Box>

      {/* Tasks Section */}
      <Box sx={{ mb: 1 }}>
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            mb: 1
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Iconify
              icon="material-symbols:checklist"
              width={16}
              height={16}
              sx={{ color: '#333' }}
            />
            <Typography
              variant="body2"
              sx={{
                fontWeight: 600,
                color: '#333',
                fontFamily: '"Recursive Variable", sans-serif',
                fontSize: '0.9rem'
              }}
            >
              Tasks
            </Typography>
          </Box>

          {/* Add task button */}
          <Tooltip title="Add new task">
            <IconButton
              size="small"
              onClick={handleAddTaskClick}
              sx={{
                color: mainYellowColor,
                '&:hover': {
                  backgroundColor: `${mainYellowColor}10`
                }
              }}
            >
              <Iconify icon="material-symbols:add-task" width={18} height={18} />
            </IconButton>
          </Tooltip>
        </Box>

        {compact ? (
          <Box className={styles.taskList}>
            {milestone.tasks?.slice(0, 2).map((task, index) => (
              <TaskItem
                key={index}
                task={task}
                showSubtasks={showSubtasks}
                compact={true}
                calculateTaskProgress={calculateTaskProgress}
                getTaskStatus={getTaskStatus}
                calculateSubtaskProgress={calculateSubtaskProgress}
                getSubtaskStatus={getSubtaskStatus}
                onUpdateTask={onUpdateTask}
                onUpdateSubtask={onUpdateSubtask}
                onAddSubtask={onAddSubtask}
                onDeleteTask={onDeleteTask}
                onDeleteSubtask={onDeleteSubtask}
                onAssignMembers={onAssignMembers}
                invitedUsers={invitedUsers}
                planOwner={planOwner}
              />
            ))}

            {milestone.tasks?.length > 2 && (
              <Button
                variant="text"
                size="small"
                sx={{
                  color: mainYellowColor,
                  fontWeight: 600,
                  textTransform: 'none',
                  p: 0,
                  mt: 1,
                  fontFamily: '"Recursive Variable", sans-serif'
                }}
              >
                + {milestone.tasks.length - 2} more tasks
              </Button>
            )}
          </Box>
        ) : (
          <Box className={styles.taskList}>
            {milestone.tasks?.map((task, index) => (
              <TaskItem
                key={index}
                task={task}
                showSubtasks={showSubtasks}
                compact={false}
                calculateTaskProgress={calculateTaskProgress}
                getTaskStatus={getTaskStatus}
                calculateSubtaskProgress={calculateSubtaskProgress}
                getSubtaskStatus={getSubtaskStatus}
                onUpdateTask={onUpdateTask}
                onUpdateSubtask={onUpdateSubtask}
                onAddSubtask={onAddSubtask}
                onDeleteTask={onDeleteTask}
                onDeleteSubtask={onDeleteSubtask}
                onAssignMembers={onAssignMembers}
                invitedUsers={invitedUsers}
                planOwner={planOwner}
              />
            ))}

            {/* New task input field */}
            {isAddingTask && (
              <Box
                className={styles.taskItem}
                sx={{
                  position: 'relative',
                  mt: 1,
                  display: 'flex',
                  alignItems: 'center',
                  backgroundColor: '#f9f9f9',
                  borderRadius: '6px',
                  padding: '8px 12px',
                  border: '1px solid #f0f0f0'
                }}
              >
                <Box
                  sx={{
                    width: 10,
                    height: 10,
                    borderRadius: '50%',
                    backgroundColor: '#CCCCCC',
                    mr: 1.5,
                    flexShrink: 0
                  }}
                />

                <TextField
                  inputRef={newTaskInputRef}
                  value={newTaskName}
                  onChange={(e) => setNewTaskName(e.target.value)}
                  onKeyDown={handleNewTaskKeyPress}
                  placeholder="Enter new task name..."
                  variant="standard"
                  fullWidth
                  autoFocus
                  sx={{
                    fontFamily: '"Recursive Variable", sans-serif',
                    '& .MuiInputBase-input': {
                      fontSize: '0.9rem',
                      fontFamily: '"Recursive Variable", sans-serif',
                      padding: '4px 0'
                    },
                    '& .MuiInput-underline:before': {
                      borderBottomColor: 'rgba(0, 0, 0, 0.1)'
                    }
                  }}
                />

                <Box sx={{ display: 'flex', alignItems: 'center', ml: 'auto' }}>
                  <IconButton size="small" onClick={handleSaveNewTask} sx={{ ml: 1 }}>
                    <Iconify icon="material-symbols:check" width={18} height={18} color="#4CAF50" />
                  </IconButton>

                  <IconButton size="small" onClick={() => setIsAddingTask(false)} sx={{ ml: 0.5 }}>
                    <Iconify icon="material-symbols:close" width={18} height={18} color="#F44336" />
                  </IconButton>
                </Box>
              </Box>
            )}
          </Box>
        )}
      </Box>
      </Collapse>
    </Paper>
  );
};
// Component to display task and subtask
const TaskItem = ({
  task,
  showSubtasks = false,
  compact = false,
  calculateTaskProgress,
  getTaskStatus,
  calculateSubtaskProgress,
  getSubtaskStatus,
  onUpdateTask,
  onUpdateSubtask,
  onAddSubtask,
  onDeleteTask,
  onDeleteSubtask,
  onAssignMembers,
  invitedUsers,
  planOwner
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [taskName, setTaskName] = useState(task.name);
  const taskInputRef = useRef(null);
  const [isAddingSubtask, setIsAddingSubtask] = useState(false);
  const [newSubtaskName, setNewSubtaskName] = useState('');
  const newSubtaskInputRef = useRef(null);

  // Comment functionality
  const [commentDialogOpen, setCommentDialogOpen] = useState(false);
  const [comments, setComments] = useState([]);
  const [loadingComments, setLoadingComments] = useState(false);

  // Enable due date functionality
  const [dueDateDialogOpen, setDueDateDialogOpen] = useState(false);
  const [updatingDueDate, setUpdatingDueDate] = useState(false);

  // Add state for assign member dialog
  const [assignMemberDialogOpen, setAssignMemberDialogOpen] = useState(false);

  // Add state to store current task information
  const [localTask, setLocalTask] = useState(task);

  // Update localTask when task changes from props
  useEffect(() => {
    setLocalTask(task);
  }, [task]);

  const handleOpenComments = (e) => {
    e.stopPropagation();
    setCommentDialogOpen(true);
    setLoadingComments(true);
    getComments(task.id)
      .then(response => {
        console.log('Comments data:', response.data);
        // Check data structure and get correct comments array
        let commentsData = [];
        if (Array.isArray(response.data)) {
          commentsData = response.data;
        } else if (response.data?.data && Array.isArray(response.data.data)) {
          commentsData = response.data.data;
        } else if (response.data?.comments && Array.isArray(response.data.comments)) {
          commentsData = response.data.comments;
        }
        setComments(commentsData);
      })
      .catch(error => {
        console.error('Error fetching comments:', error);
        toast.error('Failed to load comments');
      })
      .finally(() => {
        setLoadingComments(false);
      });
  };

  const handleAddComment = async (content) => {
    try {
      const response = await addComment(task.id, content);
      console.log('Add comment response:', response);

      // Create new comment from response or create temporary object
      let newComment;
      if (response.data?.data) {
        newComment = response.data.data;
      } else {
        // Create temporary object if API does not return new comment
        const currentUser = JSON.parse(localStorage.getItem('user')) || {};
        newComment = {
          id: Date.now(), // Temporary ID
          content: content,
          user: {
            id: currentUser.id,
            first_name: currentUser.first_name,
            last_name: currentUser.last_name,
            avatar: currentUser.avatar
          },
          created_at: new Date().toLocaleString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            day: '2-digit',
            month: '2-digit'
          }).replace(',', '')
        };
      }

      // Update state
      setComments(prevComments => [...prevComments, newComment]);

      // Refresh comments to ensure latest data
      refreshComments();
    } catch (error) {
      console.error('Error adding comment:', error);
      toast.error('Failed to add comment');
    }
  };

  const handleUpdateComment = async (commentId, content) => {
    try {
      // Update UI first
      const updatedCommentTemp = comments.find(c => c.id === commentId);
      if (updatedCommentTemp) {
        const updatedComments = comments.map(c =>
          c.id === commentId ? { ...c, content: content } : c
        );
        setComments(updatedComments);
      }

      // Call API
      const response = await updateComment(commentId, content);
      console.log('Update comment response:', response);

      toast.success('Comment updated successfully');

      // Refresh comments to ensure latest data
      refreshComments();
    } catch (error) {
      console.error('Error updating comment:', error);
      toast.error('Failed to update comment');
      // Refresh comments to restore original state if there is an error
      refreshComments();
    }
  };

  const handleDeleteComment = async (commentId) => {
    try {
      // Update UI first
      setComments(prevComments => prevComments.filter(c => c.id !== commentId));

      // Call API
      await deleteComment(commentId);
      console.log('Comment deleted successfully');

      toast.success('Comment deleted successfully');

      // Refresh comments to ensure latest data
      refreshComments();
    } catch (error) {
      console.error('Error deleting comment:', error);
      toast.error('Failed to delete comment');
      // Refresh comments to restore original state if there is an error
      refreshComments();
    }
  };

  // Refresh comments function
  const refreshComments = async () => {
    try {
      const response = await getComments(task.id);
      console.log('Refreshed comments:', response.data);

      // Handle returned data
      let commentsData = [];
      if (Array.isArray(response.data)) {
        commentsData = response.data;
      } else if (response.data?.data && Array.isArray(response.data.data)) {
        commentsData = response.data.data;
      } else if (response.data?.comments && Array.isArray(response.data.comments)) {
        commentsData = response.data.comments;
      }

      setComments(commentsData);
    } catch (error) {
      console.error('Error refreshing comments:', error);
    }
  };

  // Calculate task progress
  const taskProgress = calculateTaskProgress ? calculateTaskProgress(task) : 0;

  // Determine task status
  const taskStatus = getTaskStatus ? getTaskStatus(task) : (task.status || STATUS.NOT_STARTED);
  const statusConfig = STATUS_CONFIG[taskStatus] || STATUS_CONFIG[STATUS.NOT_STARTED];

  // Check if task has subtasks
  const hasSubtasks = task.subtasks && task.subtasks.length > 0;

  // Check if task is completed
  const isCompleted = taskStatus === STATUS.COMPLETED;

  // Handle edit mode for task
  const handleTaskEditClick = () => {
    setIsEditing(true);
  };

  // Handle save changes for task
  const handleTaskSave = () => {
    // Trim the name to remove leading/trailing whitespace
    const trimmedName = taskName.trim();

    // Only update if the name has actually changed (after trimming)
    if (trimmedName !== '' && trimmedName !== task.name) {
      if (onUpdateTask) {
        onUpdateTask({ ...task, name: trimmedName });
      }
    } else {
      // Reset to original if empty or unchanged
      setTaskName(task.name);
    }

    setIsEditing(false);
  };

  // Handle key press events for task
  const handleTaskKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleTaskSave();
    } else if (e.key === 'Escape') {
      setTaskName(task.name);
      setIsEditing(false);
    }
  };

  // Handle add subtask
  const handleAddSubtaskClick = () => {
    setIsAddingSubtask(true);
  };

  // Handle delete task
  const handleDeleteTaskClick = () => {
    if (onDeleteTask) {
      onDeleteTask(task);
    }
  };

  // Handle save new subtask
  const handleSaveNewSubtask = () => {
    const trimmedName = newSubtaskName.trim();
    if (trimmedName && onAddSubtask) {
      const newSubtask = {
        name: trimmedName,
        task: task.slug,
        status: STATUS.NOT_STARTED,
        progress: 0
      };
      onAddSubtask(newSubtask);
      setNewSubtaskName('');
    }
    setIsAddingSubtask(false);
  };

  // Handle key press events for new subtask
  const handleNewSubtaskKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSaveNewSubtask();
    } else if (e.key === 'Escape') {
      setNewSubtaskName('');
      setIsAddingSubtask(false);
    }
  };

  // Focus input when editing starts
  useEffect(() => {
    if (isEditing && taskInputRef.current) {
      taskInputRef.current.focus();
    }
  }, [isEditing]);

  // Focus input when adding subtask
  useEffect(() => {
    if (isAddingSubtask && newSubtaskInputRef.current) {
      newSubtaskInputRef.current.focus();
    }
  }, [isAddingSubtask]);

  // Check if task has due dates - use localTask instead of task
  const hasDueDates = localTask.start_date || localTask.end_date;

  const handleOpenDueDateDialog = (e) => {
    e.stopPropagation();
    setDueDateDialogOpen(true);
  };

  const handleUpdateDueDate = async (updatedTask) => {
    setUpdatingDueDate(true);
    try {
      const taskToUpdate = {
        ...localTask,
        start_date: updatedTask.start_date,
        end_date: updatedTask.end_date,
        progress: localTask.progress || 0,
        status: localTask.status || 1
      };

      // Update local state first
      setLocalTask(taskToUpdate);

      // Call API
      const response = await updateTask(taskToUpdate);

      // If API returns data, update local state
      if (response && response.data) {
        setLocalTask(response.data);
      }

      // Update state in parent component if necessary
      if (onUpdateTask) {
        onUpdateTask(taskToUpdate);
      }

      toast.success('Due date updated successfully');
    } catch (error) {
      console.error('Error updating due date:', error);
      toast.error('Failed to update due date');

      // If there is an error, restore original state
      setLocalTask(task);
    } finally {
      setUpdatingDueDate(false);
      setDueDateDialogOpen(false); // Close dialog after completion
    }
  };

  // Add handler for member assignment updates
  const handleAssignMembers = async (assignedUserIds) => {
    try {
      // Convert IDs to numbers
      const numericIds = assignedUserIds.map(id => Number(id));
      // Call API to assign members
      const response = await assignMembersToTask(localTask.slug, numericIds);
      // Create updated task with new assignees
      const updatedTask = {
        ...localTask,
        assignees: response.assignees || numericIds.map(id => ({
          id: id,
          first_name: '',
          last_name: '',
          email: ''
        }))
      };

      // Update both local state and parent state
      setLocalTask(updatedTask);
      if (onUpdateTask) {
        onUpdateTask(updatedTask);
      }

      setAssignMemberDialogOpen(false);
      toast.success('Members assigned successfully');
    } catch (error) {
      console.error('Error assigning members:', error);
      toast.error('Failed to assign members');
    }
  };

  return (
    <>
      <Box className={styles.taskItemContainer}>
        <Box
          className={styles.taskItem}
          sx={{ position: 'relative' }}>
          <Box
            sx={{
              width: 10,
              height: 10,
              borderRadius: '50%',
              backgroundColor: statusConfig.color,
              mr: 1.5,
              flexShrink: 0
            }}
          />

          {isEditing ? (
            <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
              <TextField
                inputRef={taskInputRef}
                value={taskName}
                onChange={(e) => setTaskName(e.target.value)}
                onKeyDown={handleTaskKeyPress}
                onBlur={handleTaskSave}
                variant="standard"
                fullWidth
                autoFocus
                sx={{
                  fontFamily: '"Recursive Variable", sans-serif',
                  '& .MuiInputBase-input': {
                    fontSize: '0.9rem',
                    fontFamily: '"Recursive Variable", sans-serif',
                  }
                }}
              />
              <IconButton size="small" onClick={handleTaskSave} sx={{ ml: 1 }}>
                <Iconify icon="material-symbols:check" width={16} height={16} color="#4CAF50" />
              </IconButton>
            </Box>
          ) : (
            <Box sx={{ flexGrow: 1, display: 'flex', flexDirection: 'column', gap: 0.5 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, flexGrow: 1 }}>
                  <Typography
                    variant="body2"
                    className={styles.taskName}
                    sx={{
                      fontFamily: '"Recursive Variable", sans-serif',
                      cursor: 'pointer',
                      display: 'flex',
                      alignItems: 'center',
                      color: isCompleted ? '#4CAF50' : 'inherit',
                      fontWeight: isCompleted ? 500 : 400,
                      flexGrow: 1,
                      '&:hover': {
                        '& .task-edit-icon': {
                          opacity: 1,
                        }
                      }
                    }}
                    onClick={handleTaskEditClick}>
                    {taskName}
                    <Tooltip title="Edit task name">
                      <IconButton
                        size="small"
                        className="task-edit-icon"
                        sx={{
                          ml: 0.5,
                          opacity: 0,
                          transition: 'opacity 0.2s',
                          padding: '2px'
                        }}
                      >
                        <Iconify icon="material-symbols:edit-outline" width={14} height={14} color="#666" />
                      </IconButton>
                    </Tooltip>
                  </Typography>
                </Box>

                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  {/* Display assigned members avatars */}
                  {localTask.assignees && localTask.assignees.length > 0 && (
                    <AvatarGroup
                      max={3}
                      sx={{
                        '& .MuiAvatar-root': {
                          width: 24,
                          height: 24,
                          fontSize: '0.75rem',
                          border: '1.5px solid #fff'
                        }
                      }}
                    >
                      {localTask.assignees.map((assignee, index) => (
                        <Tooltip
                          key={assignee.id}
                          title={assignee.first_name && assignee.last_name
                            ? `${assignee.first_name} ${assignee.last_name}`
                            : assignee.email}
                          arrow
                        >
                          <Avatar
                            src={assignee.avatar}
                            alt={assignee.first_name || assignee.email}
                            sx={{
                              bgcolor: mainYellowColor,
                            }}
                          >
                            {assignee.first_name
                              ? assignee.first_name.charAt(0).toUpperCase()
                              : assignee.email
                                ? assignee.email.charAt(0).toUpperCase()
                                : ''}
                          </Avatar>
                        </Tooltip>
                      ))}
                    </AvatarGroup>
                  )}

                  {/* Hover-expandable toolbar */}
                  <Box
                    className="task-toolbar"
                    sx={{
                      position: 'relative',
                      display: 'flex',
                      alignItems: 'center',
                      '&:hover .toolbar-expanded': {
                        opacity: 1,
                        visibility: 'visible',
                        transform: 'translateX(0)',
                      },
                      '&:hover .toolbar-dots': {
                        opacity: 0,
                        visibility: 'hidden',
                      }
                    }}
                  >
                    {/* Three dots button - visible by default */}
                    <Box
                      className="toolbar-dots"
                      sx={{
                        opacity: 1,
                        visibility: 'visible',
                        transition: 'all 0.2s ease',
                        display: 'flex',
                        alignItems: 'center',
                        cursor: 'pointer',
                        padding: '4px 8px',
                        borderRadius: '6px',
                        '&:hover': {
                          backgroundColor: 'rgba(0, 0, 0, 0.04)'
                        }
                      }}
                    >
                      <Iconify icon="material-symbols:more-horiz" width={20} height={20} color="#666" />
                    </Box>

                    {/* Expanded toolbar - hidden by default */}
                    <Box
                      className="toolbar-expanded"
                      sx={{
                        position: 'absolute',
                        right: 0,
                        top: 0,
                        opacity: 0,
                        visibility: 'hidden',
                        transform: 'translateX(10px)',
                        transition: 'all 0.2s ease',
                        display: 'flex',
                        alignItems: 'center',
                        border: '1px solid #eee',
                        borderRadius: '8px',
                        padding: '2px 4px',
                        background: '#fff',
                        boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                        zIndex: 10
                      }}
                    >
                      {/* Due Date Button */}
                      <Tooltip title={hasDueDates ? "Edit due date" : "Set due date"}>
                        <IconButton
                          size="small"
                          onClick={handleOpenDueDateDialog}
                          sx={{
                            color: hasDueDates ? mainYellowColor : '#888',
                            '&:hover': {
                              color: mainYellowColor,
                              bgcolor: 'rgba(255, 193, 7, 0.08)'
                            }
                          }}
                        >
                          <Iconify
                            icon={hasDueDates ? "material-symbols:calendar-month" : "material-symbols:calendar-add-on"}
                            width={16}
                            height={16}
                          />
                        </IconButton>
                      </Tooltip>

                      {/* Comment Button */}
                      <Tooltip title="Comments">
                        <IconButton
                          size="small"
                          onClick={handleOpenComments}
                          disabled={loadingComments}
                          sx={{
                            color: mainYellowColor,
                            '&:hover': {
                              bgcolor: 'rgba(255, 193, 7, 0.08)'
                            }
                          }}
                        >
                          {loadingComments ? (
                            <CircularProgress size={16} sx={{ color: mainYellowColor }} />
                          ) : (
                            <Iconify icon="material-symbols:comment-outline" width={16} height={16} />
                          )}
                        </IconButton>
                      </Tooltip>

                      {/* Assign Member Button */}
                      <Tooltip title="Assign members">
                        <IconButton
                          size="small"
                          onClick={() => setAssignMemberDialogOpen(true)}
                          sx={{
                            color: localTask.assignees?.length > 0 ? mainYellowColor : '#888',
                            '&:hover': {
                              color: mainYellowColor,
                              bgcolor: 'rgba(255, 193, 7, 0.08)'
                            }
                          }}
                        >
                          <Iconify
                            icon="mdi:account-multiple-plus"
                            width={16}
                            height={16}
                          />
                        </IconButton>
                      </Tooltip>

                      {/* Add subtask button */}
                      <Tooltip title="Add subtask">
                        <IconButton
                          size="small"
                          onClick={handleAddSubtaskClick}
                          sx={{
                            color: mainYellowColor,
                            '&:hover': {
                              bgcolor: 'rgba(255, 193, 7, 0.08)'
                            }
                          }}
                        >
                          <Iconify icon="material-symbols:add-task" width={16} height={16} />
                        </IconButton>
                      </Tooltip>

                      {/* Edit Task Button */}
                      <Tooltip title="Edit task">
                        <IconButton
                          size="small"
                          onClick={handleTaskEditClick}
                          sx={{
                            color: '#666',
                            '&:hover': {
                              bgcolor: 'rgba(0, 0, 0, 0.04)'
                            }
                          }}
                        >
                          <Iconify icon="eva:edit-fill" width={16} height={16} />
                        </IconButton>
                      </Tooltip>

                      {/* Delete task button */}
                      <Tooltip title="Delete task">
                        <IconButton
                          size="small"
                          onClick={handleDeleteTaskClick}
                          sx={{
                            color: '#F44336',
                            '&:hover': {
                              bgcolor: 'rgba(244, 67, 54, 0.08)'
                            }
                          }}
                        >
                          <Iconify icon="material-symbols:delete-outline" width={16} height={16} />
                        </IconButton>
                      </Tooltip>
                    </Box>
                  </Box>

                  {/* Display task progress */}
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      bgcolor: statusConfig.color + '20',
                      px: 1,
                      py: 0.5,
                      borderRadius: '4px',
                    }}
                  >
                    <Typography
                      variant="caption"
                      sx={{
                        fontWeight: 600,
                        color: statusConfig.color,
                        fontFamily: '"Recursive Variable", sans-serif'
                      }}
                    >
                      {taskProgress}%
                    </Typography>
                  </Box>
                </Box>
              </Box>

              {/* Due date display below task name */}
              {hasDueDates && (
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    backgroundColor: `${mainYellowColor}08`,
                    borderRadius: '4px',
                    py: 0.5,
                    px: 0.75,
                    width: 'fit-content'
                  }}
                >
                  <Iconify
                    icon="material-symbols:calendar-month"
                    width={14}
                    height={14}
                    color={mainYellowColor} />
                  <Typography
                    variant="caption"
                    sx={{
                      fontFamily: '"Recursive Variable", sans-serif',
                      fontSize: '0.75rem',
                      color: '#555',
                      fontWeight: 500,
                      lineHeight: 1,
                      ml: 0.5
                    }}>
                    {formatDate(localTask.start_date)} ~ {formatDate(localTask.end_date)}
                  </Typography>
                </Box>
              )}
            </Box>
          )}
        </Box>

        <Box
          sx={{
            pl: 4,
            pr: 1,
            pt: 0.5,
            pb: 0.5,
            borderLeft: `1px dashed ${statusConfig.color}`,
            ml: 1.5,
            mt: 0.5,
            display: (hasSubtasks || isAddingSubtask) && showSubtasks ? 'block' : 'none'
          }}
        >
          {task.subtasks && task.subtasks.map((subtask, index) => (
            <SubtaskItem
              key={index}
              subtask={subtask}
              index={index}
              totalSubtasks={task.subtasks.length}
              taskSlug={task.slug}
              calculateSubtaskProgress={calculateSubtaskProgress}
              getSubtaskStatus={getSubtaskStatus}
              onUpdateSubtask={onUpdateSubtask}
              onDeleteSubtask={onDeleteSubtask}
              onAssignMembers={onAssignMembers}
              invitedUsers={invitedUsers}
              planOwner={planOwner}
            />
          ))}

          {/* New subtask input field */}
          {isAddingSubtask && (
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                py: 0.5,
                borderBottom: '1px dotted #f0f0f0',
                backgroundColor: '#f9f9f9',
                borderRadius: '4px',
                px: 1
              }}
            >
              <Checkbox
                disabled
                size="small"
                sx={{
                  p: 0.5,
                  mr: 0.5,
                  color: '#CCCCCC'
                }}
                icon={<Iconify icon="material-symbols:check-box-outline-blank" width={18} height={18} />}
              />

              <TextField
                inputRef={newSubtaskInputRef}
                value={newSubtaskName}
                onChange={(e) => setNewSubtaskName(e.target.value)}
                onKeyDown={handleNewSubtaskKeyPress}
                placeholder="Enter new subtask name..."
                variant="standard"
                fullWidth
                autoFocus
                sx={{
                  fontFamily: '"Recursive Variable", sans-serif',
                  '& .MuiInputBase-input': {
                    fontSize: '0.85rem',
                    fontFamily: '"Recursive Variable", sans-serif',
                    padding: '4px 0'
                  },
                  '& .MuiInput-underline:before': {
                    borderBottomColor: 'rgba(0, 0, 0, 0.1)'
                  }
                }}
              />

              <Box sx={{ display: 'flex', ml: 1 }}>
                <IconButton size="small" onClick={handleSaveNewSubtask} sx={{ p: 0.5 }}>
                  <Iconify icon="material-symbols:check" width={18} height={18} color="#4CAF50" />
                </IconButton>

                <IconButton size="small" onClick={() => setIsAddingSubtask(false)} sx={{ p: 0.5, ml: 0.5 }}>
                  <Iconify icon="material-symbols:close" width={18} height={18} color="#F44336" />
                </IconButton>
              </Box>
            </Box>
          )}
        </Box>
      </Box>

      {/* Comment Dialog */}
      <CommentDialog
        open={commentDialogOpen}
        onClose={() => setCommentDialogOpen(false)}
        comments={comments}
        onAddComment={handleAddComment}
        onUpdateComment={handleUpdateComment}
        onDeleteComment={handleDeleteComment}
        loading={loadingComments}
        targetName={task.name}
        targetType="task"
      />

      {/* Due Date Dialog */}
      <DueDateDialog
        open={dueDateDialogOpen}
        onClose={() => setDueDateDialogOpen(false)}
        task={localTask}
        onUpdateDueDate={handleUpdateDueDate}
        isUpdating={updatingDueDate}
      />

      {/* Assign Member Dialog */}
      <AssignMemberDialog
        open={assignMemberDialogOpen}
        onClose={() => setAssignMemberDialogOpen(false)}
        task={localTask}
        invitedUsers={invitedUsers}
        planOwner={planOwner}
        onAssignMembers={handleAssignMembers}
      />
    </>
  );
};

// Component for subtask
const SubtaskItem = ({
  subtask,
  index,
  totalSubtasks,
  taskSlug,
  calculateSubtaskProgress,
  getSubtaskStatus,
  onUpdateSubtask,
  onDeleteSubtask,
  onAssignMembers,
  invitedUsers,
  planOwner
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [subtaskName, setSubtaskName] = useState(subtask.name);
  const [isChecked, setIsChecked] = useState(subtask.status === STATUS.COMPLETED || subtask.progress === 100);
  const subtaskInputRef = useRef(null);

  // Calculate subtask progress and status
  const subtaskProgress = calculateSubtaskProgress ? calculateSubtaskProgress(subtask) : 0;
  const subtaskStatus = getSubtaskStatus ? getSubtaskStatus(subtask) : (subtask.status || STATUS.NOT_STARTED);
  const subtaskStatusConfig = STATUS_CONFIG[subtaskStatus] || STATUS_CONFIG[STATUS.NOT_STARTED];

  // Update isChecked when subtask changes from outside
  useEffect(() => {
    setIsChecked(subtask.status === STATUS.COMPLETED || subtask.progress === 100);
  }, [subtask.status, subtask.progress]);

  // Handle edit mode for subtask
  const handleSubtaskEditClick = () => {
    setIsEditing(true);
  };

  // Handle save changes for subtask
  const handleSubtaskSave = () => {
    // Trim the name to remove leading/trailing whitespace
    const trimmedName = subtaskName.trim();

    // Only update if the name has actually changed (after trimming)
    if (trimmedName !== '' && trimmedName !== subtask.name) {
      if (onUpdateSubtask) {
        onUpdateSubtask({ ...subtask, name: trimmedName, task: taskSlug });
      }
    } else {
      // Reset to original if empty or unchanged
      setSubtaskName(subtask.name);
    }

    setIsEditing(false);
  };

  // Handle key press events for subtask
  const handleSubtaskKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSubtaskSave();
    } else if (e.key === 'Escape') {
      setSubtaskName(subtask.name);
      setIsEditing(false);
    }
  };

  // Handle status toggle
  const handleStatusToggle = () => {
    // Update UI immediately to provide user feedback
    const newCheckedState = !isChecked;
    setIsChecked(newCheckedState);

    const newStatus = newCheckedState ? STATUS.COMPLETED : STATUS.NOT_STARTED;
    const newProgress = newCheckedState ? 100 : 0;

    if (onUpdateSubtask) {
      // Ensure to send both status and progress, even when progress = 0
      const updatedData = {
        ...subtask,
        status: newStatus,
        progress: newProgress,
        task: taskSlug
      };

      console.log('Sending subtask update:', updatedData);
      onUpdateSubtask(updatedData);
    }
  };

  // Handle delete subtask
  const handleDeleteSubtaskClick = () => {
    if (window.confirm(`Are you sure you want to delete this subtask?`)) {
      onDeleteSubtask(subtask, taskSlug);
    }
  };

  // Focus input when editing starts
  useEffect(() => {
    if (isEditing && subtaskInputRef.current) {
      subtaskInputRef.current.focus();
    }
  }, [isEditing]);

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        py: 0.5,
        borderBottom: index < totalSubtasks - 1 ? '1px dotted #f0f0f0' : 'none'
      }}
    >
      <Checkbox
        checked={isChecked}
        onChange={handleStatusToggle}
        size="small"
        sx={{
          p: 0.5,
          mr: 0.5,
          color: '#CCCCCC',
          '&.Mui-checked': {
            color: '#4CAF50',
          }
        }}
        icon={<Iconify icon="material-symbols:check-box-outline-blank" width={18} height={18} />}
        checkedIcon={<Iconify icon="material-symbols:check-box" width={18} height={18} />}
      />

      {isEditing ? (
        <Box sx={{ display: 'flex', alignItems: 'center', flexGrow: 1 }}>
          <TextField
            inputRef={subtaskInputRef}
            value={subtaskName}
            onChange={(e) => setSubtaskName(e.target.value)}
            onKeyDown={handleSubtaskKeyPress}
            onBlur={handleSubtaskSave}
            variant="standard"
            fullWidth
            autoFocus
            sx={{
              fontFamily: '"Recursive Variable", sans-serif',
              '& .MuiInputBase-input': {
                fontSize: '0.85rem',
                fontFamily: '"Recursive Variable", sans-serif',
              }
            }}
          />
          <IconButton size="small" onClick={handleSubtaskSave} sx={{ ml: 1 }}>
            <Iconify icon="material-symbols:check" width={14} height={14} color="#4CAF50" />
          </IconButton>
        </Box>
      ) : (
        <Typography
          variant="body2"
          sx={{
            flexGrow: 1,
            fontSize: '0.85rem',
            fontFamily: '"Recursive Variable", sans-serif',
            color: isChecked ? '#4CAF50' : '#555',
            fontWeight: isChecked ? 500 : 400,
            cursor: 'pointer',
            display: 'flex',
            alignItems: 'center',
            '&:hover': {
              '& .subtask-edit-icon': {
                opacity: 1,
              }
            }
          }}
          onClick={handleSubtaskEditClick}
        >
          {subtaskName}
          <Tooltip title="Edit subtask name">
            <IconButton
              size="small"
              className="subtask-edit-icon"
              sx={{
                ml: 0.5,
                opacity: 0,
                transition: 'opacity 0.2s',
                padding: '1px'
              }}
            >
              <Iconify icon="material-symbols:edit-outline" width={12} height={12} color="#666" />
            </IconButton>
          </Tooltip>
        </Typography>
      )}

      <Box sx={{ display: 'flex', alignItems: 'center' }}>
        {/* Delete subtask button */}
        <Tooltip title="Delete subtask">
          <IconButton
            size="small"
            onClick={handleDeleteSubtaskClick}
            sx={{
              p: 0.5,
              color: '#F44336',
              '&:hover': {
                backgroundColor: '#FFEBEE'
              }
            }}
          >
            <Iconify icon="material-symbols:delete-outline" width={14} height={14} />
          </IconButton>
        </Tooltip>
      </Box>
    </Box>
  );
};

// Format date for display
function formatDate(dateString) {
  if (!dateString) return '';

  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '';

    // Only display day and month, not year
    const options = { month: 'short', day: 'numeric' };
    return date.toLocaleDateString('en-US', options);
  } catch (error) {
    return '';
  }
}

export default MilestoneCard;
