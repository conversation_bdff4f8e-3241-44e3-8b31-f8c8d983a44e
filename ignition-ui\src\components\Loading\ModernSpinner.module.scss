// Color variables
$primary-color: #F0A500;
$secondary-color: #FF6B35;
$accent-color: #4ECDC4;
$white: #FFFFFF;
$gradient-1: linear-gradient(45deg, $primary-color, $secondary-color);
$gradient-2: linear-gradient(135deg, $accent-color, $primary-color);

// Size variables
$small-size: 60px;
$medium-size: 120px;
$large-size: 200px;

.loadingWrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
}

// Size classes
.small {
  width: $small-size;
  height: $small-size;
}

.medium {
  width: $medium-size;
  height: $medium-size;
}

.large {
  width: $large-size;
  height: $large-size;
}

// ===== ORBITAL SPINNER =====
.spinnerContainer {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.outerRing, .middleRing, .innerRing {
  position: absolute;
  border-radius: 50%;
  border: 3px solid transparent;
}

.outerRing {
  width: 100%;
  height: 100%;
  border-top: 3px solid $primary-color;
  border-right: 3px solid rgba($primary-color, 0.3);
  animation: rotateClockwise 3s linear infinite;
}

.middleRing {
  width: 75%;
  height: 75%;
  border-top: 3px solid $secondary-color;
  border-left: 3px solid rgba($secondary-color, 0.3);
  animation: rotateCounterClockwise 2s linear infinite;
}

.innerRing {
  width: 50%;
  height: 50%;
  border-top: 3px solid $accent-color;
  border-right: 3px solid rgba($accent-color, 0.3);
  animation: rotateClockwise 1.5s linear infinite;
}

.outerOrb, .middleOrb, .innerOrb {
  position: absolute;
  border-radius: 50%;
  box-shadow: 0 0 20px rgba($primary-color, 0.6);
}

.outerOrb {
  width: 12px;
  height: 12px;
  background: $gradient-1;
  top: -6px;
  right: -6px;
  animation: orbGlow 2s ease-in-out infinite alternate;
}

.middleOrb {
  width: 10px;
  height: 10px;
  background: $gradient-2;
  top: -5px;
  left: -5px;
  animation: orbGlow 1.8s ease-in-out infinite alternate;
}

.innerOrb {
  width: 8px;
  height: 8px;
  background: $accent-color;
  top: -4px;
  right: -4px;
  animation: orbGlow 1.5s ease-in-out infinite alternate;
}

.centerCore {
  width: 25%;
  height: 25%;
  border-radius: 50%;
  background: $gradient-1;
  animation: centerPulse 2s ease-in-out infinite;
  box-shadow: 0 0 30px rgba($primary-color, 0.8);
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.coreInner {
  width: 60%;
  height: 60%;
  border-radius: 50%;
  background: $white;
  animation: innerCorePulse 1s ease-in-out infinite alternate;
}

// Status-based particles
.particle1, .particle2, .particle3 {
  position: absolute;
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background: $accent-color;
}

.particle1 {
  top: 15%;
  right: 15%;
  animation: particleFloat1 3s ease-in-out infinite;
}

.particle2 {
  bottom: 20%;
  left: 10%;
  animation: particleFloat2 2.5s ease-in-out infinite;
}

.particle3 {
  top: 60%;
  right: 5%;
  animation: particleFloat3 3.5s ease-in-out infinite;
}

// ===== PULSE SPINNER =====
.pulseContainer {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pulseRing1, .pulseRing2, .pulseRing3 {
  position: absolute;
  border-radius: 50%;
  border: 2px solid $primary-color;
  animation: pulseRing 2s ease-out infinite;
}

.pulseRing1 {
  width: 100%;
  height: 100%;
  animation-delay: 0s;
}

.pulseRing2 {
  width: 80%;
  height: 80%;
  animation-delay: 0.4s;
}

.pulseRing3 {
  width: 60%;
  height: 60%;
  animation-delay: 0.8s;
}

.pulseCenter {
  width: 30%;
  height: 30%;
  border-radius: 50%;
  background: $gradient-1;
  animation: centerPulse 1.5s ease-in-out infinite;
}

// ===== RIPPLE SPINNER =====
.rippleContainer {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.ripple {
  position: absolute;
  border-radius: 50%;
  border: 2px solid $primary-color;
  animation: rippleEffect 2s ease-out infinite;
  
  &:nth-child(1) {
    animation-delay: 0s;
  }
  
  &:nth-child(2) {
    animation-delay: 0.6s;
  }
  
  &:nth-child(3) {
    animation-delay: 1.2s;
  }
}

// ===== DOTS SPINNER =====
.dotsContainer {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dot1, .dot2, .dot3, .dot4, .dot5 {
  position: absolute;
  width: 15%;
  height: 15%;
  border-radius: 50%;
  background: $primary-color;
  animation: dotBounce 1.5s ease-in-out infinite;
}

.dot1 {
  top: 10%;
  left: 50%;
  transform: translateX(-50%);
  animation-delay: 0s;
}

.dot2 {
  top: 30%;
  right: 20%;
  animation-delay: 0.2s;
}

.dot3 {
  bottom: 30%;
  right: 20%;
  animation-delay: 0.4s;
}

.dot4 {
  bottom: 10%;
  left: 50%;
  transform: translateX(-50%);
  animation-delay: 0.6s;
}

.dot5 {
  top: 30%;
  left: 20%;
  animation-delay: 0.8s;
}

// ===== GALAXY SPINNER =====
.galaxyContainer {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.galaxyArm1, .galaxyArm2, .galaxyArm3 {
  position: absolute;
  width: 100%;
  height: 2px;
  background: linear-gradient(90deg, transparent, $primary-color, transparent);
  border-radius: 2px;
  transform-origin: center;
}

.galaxyArm1 {
  animation: galaxyRotate 4s linear infinite;
}

.galaxyArm2 {
  animation: galaxyRotate 4s linear infinite;
  animation-delay: 1.33s;
  transform: rotate(120deg);
}

.galaxyArm3 {
  animation: galaxyRotate 4s linear infinite;
  animation-delay: 2.66s;
  transform: rotate(240deg);
}

.galaxyCore {
  width: 20%;
  height: 20%;
  border-radius: 50%;
  background: radial-gradient(circle, $primary-color, #000);
  animation: galaxyCorePulse 2s ease-in-out infinite;
  box-shadow: 0 0 40px rgba($primary-color, 0.9);
}

.star1, .star2, .star3, .star4 {
  position: absolute;
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: $white;
  box-shadow: 0 0 10px rgba($white, 0.8);
}

.star1 {
  top: 20%;
  left: 20%;
  animation: starTwinkle 2s ease-in-out infinite, starOrbit1 8s linear infinite;
}

.star2 {
  top: 20%;
  right: 20%;
  animation: starTwinkle 2.5s ease-in-out infinite, starOrbit2 6s linear infinite;
}

.star3 {
  bottom: 20%;
  right: 20%;
  animation: starTwinkle 1.8s ease-in-out infinite, starOrbit3 10s linear infinite;
}

.star4 {
  bottom: 20%;
  left: 20%;
  animation: starTwinkle 2.2s ease-in-out infinite, starOrbit4 7s linear infinite;
}

// Status-based modifications
.creating {
  .outerRing {
    border-top-color: $primary-color;
    animation-duration: 3s;
  }
}

.processing {
  .outerRing {
    border-top-color: $secondary-color;
    animation-duration: 2s;
  }

  .middleRing {
    border-top-color: $accent-color;
    animation-duration: 1.5s;
  }
}

.finalizing {
  .outerRing, .middleRing, .innerRing {
    border-top-color: #4CAF50;
    animation-duration: 1s;
  }
}

// ===== ANIMATIONS =====
@keyframes rotateClockwise {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes rotateCounterClockwise {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(-360deg);
  }
}

@keyframes centerPulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

@keyframes orbGlow {
  0% {
    box-shadow: 0 0 10px rgba($primary-color, 0.4);
    transform: scale(1);
  }
  100% {
    box-shadow: 0 0 25px rgba($primary-color, 0.8);
    transform: scale(1.1);
  }
}

@keyframes pulseRing {
  0% {
    transform: scale(0.8);
    opacity: 1;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

@keyframes rippleEffect {
  0% {
    width: 0;
    height: 0;
    opacity: 1;
  }
  100% {
    width: 100%;
    height: 100%;
    opacity: 0;
  }
}

@keyframes dotBounce {
  0%, 100% {
    transform: scale(1);
    background: $primary-color;
  }
  50% {
    transform: scale(1.3);
    background: $secondary-color;
  }
}

@keyframes innerCorePulse {
  0% {
    opacity: 0.6;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes particleFloat1 {
  0%, 100% {
    transform: translate(0, 0) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translate(10px, -15px) scale(1.2);
    opacity: 1;
  }
}

@keyframes particleFloat2 {
  0%, 100% {
    transform: translate(0, 0) scale(1);
    opacity: 0.7;
  }
  50% {
    transform: translate(-8px, 12px) scale(1.1);
    opacity: 1;
  }
}

@keyframes particleFloat3 {
  0%, 100% {
    transform: translate(0, 0) scale(1);
    opacity: 0.5;
  }
  50% {
    transform: translate(12px, -8px) scale(1.3);
    opacity: 1;
  }
}

@keyframes galaxyRotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes galaxyCorePulse {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 40px rgba($primary-color, 0.9);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 0 60px rgba($primary-color, 1);
  }
}

@keyframes starTwinkle {
  0%, 100% {
    opacity: 0.6;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes starOrbit1 {
  0% {
    transform: rotate(0deg) translateX(60px) rotate(0deg);
  }
  100% {
    transform: rotate(360deg) translateX(60px) rotate(-360deg);
  }
}

@keyframes starOrbit2 {
  0% {
    transform: rotate(0deg) translateX(80px) rotate(0deg);
  }
  100% {
    transform: rotate(360deg) translateX(80px) rotate(-360deg);
  }
}

@keyframes starOrbit3 {
  0% {
    transform: rotate(0deg) translateX(70px) rotate(0deg);
  }
  100% {
    transform: rotate(360deg) translateX(70px) rotate(-360deg);
  }
}

@keyframes starOrbit4 {
  0% {
    transform: rotate(0deg) translateX(90px) rotate(0deg);
  }
  100% {
    transform: rotate(360deg) translateX(90px) rotate(-360deg);
  }
}
