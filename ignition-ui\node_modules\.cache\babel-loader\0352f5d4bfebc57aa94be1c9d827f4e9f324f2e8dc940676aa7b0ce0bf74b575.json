{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getCardUtilityClass(slot) {\n  return generateUtilityClass('MuiCard', slot);\n}\nconst cardClasses = generateUtilityClasses('MuiCard', ['root', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid', 'sizeSm', 'sizeMd', 'sizeLg', 'horizontal', 'vertical']);\nexport default cardClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getCardUtilityClass", "slot", "cardClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Card/cardClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getCardUtilityClass(slot) {\n  return generateUtilityClass('MuiCard', slot);\n}\nconst cardClasses = generateUtilityClasses('MuiCard', ['root', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid', 'sizeSm', 'sizeMd', 'sizeLg', 'horizontal', 'vertical']);\nexport default cardClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,mBAAmBA,CAACC,IAAI,EAAE;EACxC,OAAOH,oBAAoB,CAAC,SAAS,EAAEG,IAAI,CAAC;AAC9C;AACA,MAAMC,WAAW,GAAGH,sBAAsB,CAAC,SAAS,EAAE,CAAC,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,iBAAiB,EAAE,aAAa,EAAE,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;AACxR,eAAeG,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}