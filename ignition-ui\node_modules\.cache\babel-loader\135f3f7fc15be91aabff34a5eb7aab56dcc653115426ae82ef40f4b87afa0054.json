{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { unstable_useEnhancedEffect as useEnhancedEffect } from '@mui/utils';\nimport { CompoundComponentContext } from './useCompoundParent';\n/**\n * Registers a child component with the parent component.\n *\n * @param id A unique key for the child component. If the `id` is `undefined`, the registration logic will not run (this can sometimes be the case during SSR).\n *   This can be either a value, or a function that generates a value based on already registered siblings' ids.\n *   If a function, it's called with the set of the ids of all the items that have already been registered.\n *   Return `existingKeys.size` if you want to use the index of the new item as the id.\n * @param itemMetadata Arbitrary metadata to pass to the parent component. This should be a stable reference (for example a memoized object), to avoid unnecessary re-registrations.\n *\n * @ignore - internal hook.\n */\nexport function useCompoundItem(id, itemMetadata) {\n  const context = React.useContext(CompoundComponentContext);\n  if (context === null) {\n    throw new Error('useCompoundItem must be used within a useCompoundParent');\n  }\n  const {\n    registerItem\n  } = context;\n  const [registeredId, setRegisteredId] = React.useState(typeof id === 'function' ? undefined : id);\n  useEnhancedEffect(() => {\n    const {\n      id: returnedId,\n      deregister\n    } = registerItem(id, itemMetadata);\n    setRegisteredId(returnedId);\n    return deregister;\n  }, [registerItem, itemMetadata, id]);\n  return {\n    id: registeredId,\n    index: registeredId !== undefined ? context.getItemIndex(registeredId) : -1,\n    totalItemCount: context.totalSubitemCount\n  };\n}", "map": {"version": 3, "names": ["React", "unstable_useEnhancedEffect", "useEnhancedEffect", "CompoundComponentContext", "useCompoundItem", "id", "itemMetadata", "context", "useContext", "Error", "registerItem", "registeredId", "setRegisteredId", "useState", "undefined", "returnedId", "deregister", "index", "getItemIndex", "totalItemCount", "totalSubitemCount"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/useCompound/useCompoundItem.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { unstable_useEnhancedEffect as useEnhancedEffect } from '@mui/utils';\nimport { CompoundComponentContext } from './useCompoundParent';\n/**\n * Registers a child component with the parent component.\n *\n * @param id A unique key for the child component. If the `id` is `undefined`, the registration logic will not run (this can sometimes be the case during SSR).\n *   This can be either a value, or a function that generates a value based on already registered siblings' ids.\n *   If a function, it's called with the set of the ids of all the items that have already been registered.\n *   Return `existingKeys.size` if you want to use the index of the new item as the id.\n * @param itemMetadata Arbitrary metadata to pass to the parent component. This should be a stable reference (for example a memoized object), to avoid unnecessary re-registrations.\n *\n * @ignore - internal hook.\n */\nexport function useCompoundItem(id, itemMetadata) {\n  const context = React.useContext(CompoundComponentContext);\n  if (context === null) {\n    throw new Error('useCompoundItem must be used within a useCompoundParent');\n  }\n  const {\n    registerItem\n  } = context;\n  const [registeredId, setRegisteredId] = React.useState(typeof id === 'function' ? undefined : id);\n  useEnhancedEffect(() => {\n    const {\n      id: returnedId,\n      deregister\n    } = registerItem(id, itemMetadata);\n    setRegisteredId(returnedId);\n    return deregister;\n  }, [registerItem, itemMetadata, id]);\n  return {\n    id: registeredId,\n    index: registeredId !== undefined ? context.getItemIndex(registeredId) : -1,\n    totalItemCount: context.totalSubitemCount\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,0BAA0B,IAAIC,iBAAiB,QAAQ,YAAY;AAC5E,SAASC,wBAAwB,QAAQ,qBAAqB;AAC9D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAACC,EAAE,EAAEC,YAAY,EAAE;EAChD,MAAMC,OAAO,GAAGP,KAAK,CAACQ,UAAU,CAACL,wBAAwB,CAAC;EAC1D,IAAII,OAAO,KAAK,IAAI,EAAE;IACpB,MAAM,IAAIE,KAAK,CAAC,yDAAyD,CAAC;EAC5E;EACA,MAAM;IACJC;EACF,CAAC,GAAGH,OAAO;EACX,MAAM,CAACI,YAAY,EAAEC,eAAe,CAAC,GAAGZ,KAAK,CAACa,QAAQ,CAAC,OAAOR,EAAE,KAAK,UAAU,GAAGS,SAAS,GAAGT,EAAE,CAAC;EACjGH,iBAAiB,CAAC,MAAM;IACtB,MAAM;MACJG,EAAE,EAAEU,UAAU;MACdC;IACF,CAAC,GAAGN,YAAY,CAACL,EAAE,EAAEC,YAAY,CAAC;IAClCM,eAAe,CAACG,UAAU,CAAC;IAC3B,OAAOC,UAAU;EACnB,CAAC,EAAE,CAACN,YAAY,EAAEJ,YAAY,EAAED,EAAE,CAAC,CAAC;EACpC,OAAO;IACLA,EAAE,EAAEM,YAAY;IAChBM,KAAK,EAAEN,YAAY,KAAKG,SAAS,GAAGP,OAAO,CAACW,YAAY,CAACP,YAAY,CAAC,GAAG,CAAC,CAAC;IAC3EQ,cAAc,EAAEZ,OAAO,CAACa;EAC1B,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}