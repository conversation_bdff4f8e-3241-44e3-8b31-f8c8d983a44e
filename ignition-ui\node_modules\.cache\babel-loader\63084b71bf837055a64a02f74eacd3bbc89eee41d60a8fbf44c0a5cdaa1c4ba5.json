{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useTabsContext } from '../Tabs';\nimport { TabsListActionTypes } from './useTabsList.types';\nimport { useCompoundParent } from '../useCompound';\nimport { useList } from '../useList';\nimport { tabsListReducer } from './tabsListReducer';\n\n/**\n *\n * Demos:\n *\n * - [Tabs](https://mui.com/base-ui/react-tabs/#hooks)\n *\n * API:\n *\n * - [useTabsList API](https://mui.com/base-ui/react-tabs/hooks-api/#use-tabs-list)\n */\nfunction useTabsList(parameters) {\n  var _selectedValues$;\n  const {\n    rootRef: externalRef\n  } = parameters;\n  const {\n    direction = 'ltr',\n    onSelected,\n    orientation = 'horizontal',\n    value,\n    registerTabIdLookup,\n    selectionFollowsFocus\n  } = useTabsContext();\n  const {\n    subitems,\n    contextValue: compoundComponentContextValue\n  } = useCompoundParent();\n  const tabIdLookup = React.useCallback(tabValue => {\n    var _subitems$get;\n    return (_subitems$get = subitems.get(tabValue)) == null ? void 0 : _subitems$get.id;\n  }, [subitems]);\n  registerTabIdLookup(tabIdLookup);\n  const subitemKeys = React.useMemo(() => Array.from(subitems.keys()), [subitems]);\n  const getTabElement = React.useCallback(tabValue => {\n    var _subitems$get$ref$cur, _subitems$get2;\n    if (tabValue == null) {\n      return null;\n    }\n    return (_subitems$get$ref$cur = (_subitems$get2 = subitems.get(tabValue)) == null ? void 0 : _subitems$get2.ref.current) != null ? _subitems$get$ref$cur : null;\n  }, [subitems]);\n  const isRtl = direction === 'rtl';\n  let listOrientation;\n  if (orientation === 'vertical') {\n    listOrientation = 'vertical';\n  } else {\n    listOrientation = isRtl ? 'horizontal-rtl' : 'horizontal-ltr';\n  }\n  const handleChange = React.useCallback((event, newValue) => {\n    var _newValue$;\n    onSelected(event, (_newValue$ = newValue[0]) != null ? _newValue$ : null);\n  }, [onSelected]);\n  const controlledProps = React.useMemo(() => {\n    if (value === undefined) {\n      return {};\n    }\n    return value != null ? {\n      selectedValues: [value]\n    } : {\n      selectedValues: []\n    };\n  }, [value]);\n  const isItemDisabled = React.useCallback(item => {\n    var _subitems$get$disable, _subitems$get3;\n    return (_subitems$get$disable = (_subitems$get3 = subitems.get(item)) == null ? void 0 : _subitems$get3.disabled) != null ? _subitems$get$disable : false;\n  }, [subitems]);\n  const {\n    contextValue: listContextValue,\n    dispatch,\n    getRootProps: getListboxRootProps,\n    state: {\n      highlightedValue,\n      selectedValues\n    },\n    rootRef: mergedRootRef\n  } = useList({\n    controlledProps,\n    disabledItemsFocusable: !selectionFollowsFocus,\n    focusManagement: 'DOM',\n    getItemDomElement: getTabElement,\n    isItemDisabled,\n    items: subitemKeys,\n    rootRef: externalRef,\n    onChange: handleChange,\n    orientation: listOrientation,\n    reducerActionContext: React.useMemo(() => ({\n      selectionFollowsFocus: selectionFollowsFocus || false\n    }), [selectionFollowsFocus]),\n    selectionMode: 'single',\n    stateReducer: tabsListReducer\n  });\n  React.useEffect(() => {\n    if (value === undefined) {\n      return;\n    }\n\n    // when a value changes externally, the highlighted value should be synced to it\n    if (value != null) {\n      dispatch({\n        type: TabsListActionTypes.valueChange,\n        value\n      });\n    }\n  }, [dispatch, value]);\n  const getRootProps = function () {\n    let externalProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    return _extends({}, externalProps, getListboxRootProps(externalProps), {\n      'aria-orientation': orientation === 'vertical' ? 'vertical' : undefined,\n      role: 'tablist'\n    });\n  };\n  const contextValue = React.useMemo(() => _extends({}, compoundComponentContextValue, listContextValue), [compoundComponentContextValue, listContextValue]);\n  return {\n    contextValue,\n    dispatch,\n    getRootProps,\n    highlightedValue,\n    isRtl,\n    orientation,\n    rootRef: mergedRootRef,\n    selectedValue: (_selectedValues$ = selectedValues[0]) != null ? _selectedValues$ : null\n  };\n}\nexport { useTabsList };", "map": {"version": 3, "names": ["_extends", "React", "useTabsContext", "TabsListActionTypes", "useCompoundParent", "useList", "tabsListReducer", "useTabsList", "parameters", "_selectedValues$", "rootRef", "externalRef", "direction", "onSelected", "orientation", "value", "registerTabIdLookup", "selectionFollowsFocus", "subitems", "contextValue", "compoundComponentContextValue", "tabIdLookup", "useCallback", "tabValue", "_subitems$get", "get", "id", "subitemKeys", "useMemo", "Array", "from", "keys", "getTabElement", "_subitems$get$ref$cur", "_subitems$get2", "ref", "current", "isRtl", "listOrientation", "handleChange", "event", "newValue", "_newValue$", "controlledProps", "undefined", "<PERSON><PERSON><PERSON><PERSON>", "isItemDisabled", "item", "_subitems$get$disable", "_subitems$get3", "disabled", "listContextValue", "dispatch", "getRootProps", "getListboxRootProps", "state", "highlightedValue", "mergedRootRef", "disabledItemsFocusable", "focusManagement", "getItemDomElement", "items", "onChange", "reducerActionContext", "selectionMode", "stateReducer", "useEffect", "type", "valueChange", "externalProps", "arguments", "length", "role", "selected<PERSON><PERSON><PERSON>"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/useTabsList/useTabsList.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { useTabsContext } from '../Tabs';\nimport { TabsListActionTypes } from './useTabsList.types';\nimport { useCompoundParent } from '../useCompound';\nimport { useList } from '../useList';\nimport { tabsListReducer } from './tabsListReducer';\n\n/**\n *\n * Demos:\n *\n * - [Tabs](https://mui.com/base-ui/react-tabs/#hooks)\n *\n * API:\n *\n * - [useTabsList API](https://mui.com/base-ui/react-tabs/hooks-api/#use-tabs-list)\n */\nfunction useTabsList(parameters) {\n  var _selectedValues$;\n  const {\n    rootRef: externalRef\n  } = parameters;\n  const {\n    direction = 'ltr',\n    onSelected,\n    orientation = 'horizontal',\n    value,\n    registerTabIdLookup,\n    selectionFollowsFocus\n  } = useTabsContext();\n  const {\n    subitems,\n    contextValue: compoundComponentContextValue\n  } = useCompoundParent();\n  const tabIdLookup = React.useCallback(tabValue => {\n    var _subitems$get;\n    return (_subitems$get = subitems.get(tabValue)) == null ? void 0 : _subitems$get.id;\n  }, [subitems]);\n  registerTabIdLookup(tabIdLookup);\n  const subitemKeys = React.useMemo(() => Array.from(subitems.keys()), [subitems]);\n  const getTabElement = React.useCallback(tabValue => {\n    var _subitems$get$ref$cur, _subitems$get2;\n    if (tabValue == null) {\n      return null;\n    }\n    return (_subitems$get$ref$cur = (_subitems$get2 = subitems.get(tabValue)) == null ? void 0 : _subitems$get2.ref.current) != null ? _subitems$get$ref$cur : null;\n  }, [subitems]);\n  const isRtl = direction === 'rtl';\n  let listOrientation;\n  if (orientation === 'vertical') {\n    listOrientation = 'vertical';\n  } else {\n    listOrientation = isRtl ? 'horizontal-rtl' : 'horizontal-ltr';\n  }\n  const handleChange = React.useCallback((event, newValue) => {\n    var _newValue$;\n    onSelected(event, (_newValue$ = newValue[0]) != null ? _newValue$ : null);\n  }, [onSelected]);\n  const controlledProps = React.useMemo(() => {\n    if (value === undefined) {\n      return {};\n    }\n    return value != null ? {\n      selectedValues: [value]\n    } : {\n      selectedValues: []\n    };\n  }, [value]);\n  const isItemDisabled = React.useCallback(item => {\n    var _subitems$get$disable, _subitems$get3;\n    return (_subitems$get$disable = (_subitems$get3 = subitems.get(item)) == null ? void 0 : _subitems$get3.disabled) != null ? _subitems$get$disable : false;\n  }, [subitems]);\n  const {\n    contextValue: listContextValue,\n    dispatch,\n    getRootProps: getListboxRootProps,\n    state: {\n      highlightedValue,\n      selectedValues\n    },\n    rootRef: mergedRootRef\n  } = useList({\n    controlledProps,\n    disabledItemsFocusable: !selectionFollowsFocus,\n    focusManagement: 'DOM',\n    getItemDomElement: getTabElement,\n    isItemDisabled,\n    items: subitemKeys,\n    rootRef: externalRef,\n    onChange: handleChange,\n    orientation: listOrientation,\n    reducerActionContext: React.useMemo(() => ({\n      selectionFollowsFocus: selectionFollowsFocus || false\n    }), [selectionFollowsFocus]),\n    selectionMode: 'single',\n    stateReducer: tabsListReducer\n  });\n  React.useEffect(() => {\n    if (value === undefined) {\n      return;\n    }\n\n    // when a value changes externally, the highlighted value should be synced to it\n    if (value != null) {\n      dispatch({\n        type: TabsListActionTypes.valueChange,\n        value\n      });\n    }\n  }, [dispatch, value]);\n  const getRootProps = (externalProps = {}) => {\n    return _extends({}, externalProps, getListboxRootProps(externalProps), {\n      'aria-orientation': orientation === 'vertical' ? 'vertical' : undefined,\n      role: 'tablist'\n    });\n  };\n  const contextValue = React.useMemo(() => _extends({}, compoundComponentContextValue, listContextValue), [compoundComponentContextValue, listContextValue]);\n  return {\n    contextValue,\n    dispatch,\n    getRootProps,\n    highlightedValue,\n    isRtl,\n    orientation,\n    rootRef: mergedRootRef,\n    selectedValue: (_selectedValues$ = selectedValues[0]) != null ? _selectedValues$ : null\n  };\n}\nexport { useTabsList };"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,QAAQ,SAAS;AACxC,SAASC,mBAAmB,QAAQ,qBAAqB;AACzD,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,SAASC,OAAO,QAAQ,YAAY;AACpC,SAASC,eAAe,QAAQ,mBAAmB;;AAEnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,UAAU,EAAE;EAC/B,IAAIC,gBAAgB;EACpB,MAAM;IACJC,OAAO,EAAEC;EACX,CAAC,GAAGH,UAAU;EACd,MAAM;IACJI,SAAS,GAAG,KAAK;IACjBC,UAAU;IACVC,WAAW,GAAG,YAAY;IAC1BC,KAAK;IACLC,mBAAmB;IACnBC;EACF,CAAC,GAAGf,cAAc,CAAC,CAAC;EACpB,MAAM;IACJgB,QAAQ;IACRC,YAAY,EAAEC;EAChB,CAAC,GAAGhB,iBAAiB,CAAC,CAAC;EACvB,MAAMiB,WAAW,GAAGpB,KAAK,CAACqB,WAAW,CAACC,QAAQ,IAAI;IAChD,IAAIC,aAAa;IACjB,OAAO,CAACA,aAAa,GAAGN,QAAQ,CAACO,GAAG,CAACF,QAAQ,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGC,aAAa,CAACE,EAAE;EACrF,CAAC,EAAE,CAACR,QAAQ,CAAC,CAAC;EACdF,mBAAmB,CAACK,WAAW,CAAC;EAChC,MAAMM,WAAW,GAAG1B,KAAK,CAAC2B,OAAO,CAAC,MAAMC,KAAK,CAACC,IAAI,CAACZ,QAAQ,CAACa,IAAI,CAAC,CAAC,CAAC,EAAE,CAACb,QAAQ,CAAC,CAAC;EAChF,MAAMc,aAAa,GAAG/B,KAAK,CAACqB,WAAW,CAACC,QAAQ,IAAI;IAClD,IAAIU,qBAAqB,EAAEC,cAAc;IACzC,IAAIX,QAAQ,IAAI,IAAI,EAAE;MACpB,OAAO,IAAI;IACb;IACA,OAAO,CAACU,qBAAqB,GAAG,CAACC,cAAc,GAAGhB,QAAQ,CAACO,GAAG,CAACF,QAAQ,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGW,cAAc,CAACC,GAAG,CAACC,OAAO,KAAK,IAAI,GAAGH,qBAAqB,GAAG,IAAI;EACjK,CAAC,EAAE,CAACf,QAAQ,CAAC,CAAC;EACd,MAAMmB,KAAK,GAAGzB,SAAS,KAAK,KAAK;EACjC,IAAI0B,eAAe;EACnB,IAAIxB,WAAW,KAAK,UAAU,EAAE;IAC9BwB,eAAe,GAAG,UAAU;EAC9B,CAAC,MAAM;IACLA,eAAe,GAAGD,KAAK,GAAG,gBAAgB,GAAG,gBAAgB;EAC/D;EACA,MAAME,YAAY,GAAGtC,KAAK,CAACqB,WAAW,CAAC,CAACkB,KAAK,EAAEC,QAAQ,KAAK;IAC1D,IAAIC,UAAU;IACd7B,UAAU,CAAC2B,KAAK,EAAE,CAACE,UAAU,GAAGD,QAAQ,CAAC,CAAC,CAAC,KAAK,IAAI,GAAGC,UAAU,GAAG,IAAI,CAAC;EAC3E,CAAC,EAAE,CAAC7B,UAAU,CAAC,CAAC;EAChB,MAAM8B,eAAe,GAAG1C,KAAK,CAAC2B,OAAO,CAAC,MAAM;IAC1C,IAAIb,KAAK,KAAK6B,SAAS,EAAE;MACvB,OAAO,CAAC,CAAC;IACX;IACA,OAAO7B,KAAK,IAAI,IAAI,GAAG;MACrB8B,cAAc,EAAE,CAAC9B,KAAK;IACxB,CAAC,GAAG;MACF8B,cAAc,EAAE;IAClB,CAAC;EACH,CAAC,EAAE,CAAC9B,KAAK,CAAC,CAAC;EACX,MAAM+B,cAAc,GAAG7C,KAAK,CAACqB,WAAW,CAACyB,IAAI,IAAI;IAC/C,IAAIC,qBAAqB,EAAEC,cAAc;IACzC,OAAO,CAACD,qBAAqB,GAAG,CAACC,cAAc,GAAG/B,QAAQ,CAACO,GAAG,CAACsB,IAAI,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGE,cAAc,CAACC,QAAQ,KAAK,IAAI,GAAGF,qBAAqB,GAAG,KAAK;EAC3J,CAAC,EAAE,CAAC9B,QAAQ,CAAC,CAAC;EACd,MAAM;IACJC,YAAY,EAAEgC,gBAAgB;IAC9BC,QAAQ;IACRC,YAAY,EAAEC,mBAAmB;IACjCC,KAAK,EAAE;MACLC,gBAAgB;MAChBX;IACF,CAAC;IACDnC,OAAO,EAAE+C;EACX,CAAC,GAAGpD,OAAO,CAAC;IACVsC,eAAe;IACfe,sBAAsB,EAAE,CAACzC,qBAAqB;IAC9C0C,eAAe,EAAE,KAAK;IACtBC,iBAAiB,EAAE5B,aAAa;IAChCc,cAAc;IACde,KAAK,EAAElC,WAAW;IAClBjB,OAAO,EAAEC,WAAW;IACpBmD,QAAQ,EAAEvB,YAAY;IACtBzB,WAAW,EAAEwB,eAAe;IAC5ByB,oBAAoB,EAAE9D,KAAK,CAAC2B,OAAO,CAAC,OAAO;MACzCX,qBAAqB,EAAEA,qBAAqB,IAAI;IAClD,CAAC,CAAC,EAAE,CAACA,qBAAqB,CAAC,CAAC;IAC5B+C,aAAa,EAAE,QAAQ;IACvBC,YAAY,EAAE3D;EAChB,CAAC,CAAC;EACFL,KAAK,CAACiE,SAAS,CAAC,MAAM;IACpB,IAAInD,KAAK,KAAK6B,SAAS,EAAE;MACvB;IACF;;IAEA;IACA,IAAI7B,KAAK,IAAI,IAAI,EAAE;MACjBqC,QAAQ,CAAC;QACPe,IAAI,EAAEhE,mBAAmB,CAACiE,WAAW;QACrCrD;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACqC,QAAQ,EAAErC,KAAK,CAAC,CAAC;EACrB,MAAMsC,YAAY,GAAG,SAAAA,CAAA,EAAwB;IAAA,IAAvBgB,aAAa,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA1B,SAAA,GAAA0B,SAAA,MAAG,CAAC,CAAC;IACtC,OAAOtE,QAAQ,CAAC,CAAC,CAAC,EAAEqE,aAAa,EAAEf,mBAAmB,CAACe,aAAa,CAAC,EAAE;MACrE,kBAAkB,EAAEvD,WAAW,KAAK,UAAU,GAAG,UAAU,GAAG8B,SAAS;MACvE4B,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;EACD,MAAMrD,YAAY,GAAGlB,KAAK,CAAC2B,OAAO,CAAC,MAAM5B,QAAQ,CAAC,CAAC,CAAC,EAAEoB,6BAA6B,EAAE+B,gBAAgB,CAAC,EAAE,CAAC/B,6BAA6B,EAAE+B,gBAAgB,CAAC,CAAC;EAC1J,OAAO;IACLhC,YAAY;IACZiC,QAAQ;IACRC,YAAY;IACZG,gBAAgB;IAChBnB,KAAK;IACLvB,WAAW;IACXJ,OAAO,EAAE+C,aAAa;IACtBgB,aAAa,EAAE,CAAChE,gBAAgB,GAAGoC,cAAc,CAAC,CAAC,CAAC,KAAK,IAAI,GAAGpC,gBAAgB,GAAG;EACrF,CAAC;AACH;AACA,SAASF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}