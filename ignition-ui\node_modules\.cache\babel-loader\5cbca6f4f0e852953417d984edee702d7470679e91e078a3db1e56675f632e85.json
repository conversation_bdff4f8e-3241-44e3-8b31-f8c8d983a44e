{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getAccordionUtilityClass(slot) {\n  return generateUtilityClass('MuiAccordion', slot);\n}\nconst accordionClasses = generateUtilityClasses('MuiAccordion', ['root', 'expanded', 'disabled']);\nexport default accordionClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getAccordionUtilityClass", "slot", "accordionClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Accordion/accordionClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getAccordionUtilityClass(slot) {\n  return generateUtilityClass('MuiAccordion', slot);\n}\nconst accordionClasses = generateUtilityClasses('MuiAccordion', ['root', 'expanded', 'disabled']);\nexport default accordionClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,wBAAwBA,CAACC,IAAI,EAAE;EAC7C,OAAOH,oBAAoB,CAAC,cAAc,EAAEG,IAAI,CAAC;AACnD;AACA,MAAMC,gBAAgB,GAAGH,sBAAsB,CAAC,cAAc,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;AACjG,eAAeG,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}