{"ast": null, "code": "var _jsxFileName = \"C:\\\\ignition\\\\ignition-ui\\\\src\\\\views\\\\plan\\\\detail\\\\dialogs\\\\OptOutDialog.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Dialog, DialogTitle, DialogContent, DialogActions, Button, Typography, CircularProgress } from '@mui/material';\nimport Iconify from 'components/Iconify/index';\nimport styles from '../styles.module.scss';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst OptOutDialog = _ref => {\n  _s();\n  let {\n    open,\n    onClose,\n    onOptOut\n  } = _ref;\n  const [loading, setLoading] = useState(false);\n  const handleOptOut = async () => {\n    setLoading(true);\n    try {\n      const success = await onOptOut();\n      if (!success) {\n        setLoading(false);\n      }\n      // If successful, the page will navigate away\n    } catch (err) {\n      console.error('<PERSON>rror opting out of plan:', err);\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(Dialog, {\n    open: open,\n    onClose: loading ? null : onClose,\n    maxWidth: \"sm\",\n    fullWidth: true,\n    className: styles.deleteDialog,\n    PaperProps: {\n      sx: {\n        borderRadius: '12px',\n        padding: '8px'\n      }\n    },\n    children: [/*#__PURE__*/_jsxDEV(DialogTitle, {\n      className: styles.dialogTitle,\n      sx: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: 1,\n        fontFamily: '\"Recursive Variable\", sans-serif',\n        fontSize: '1.3rem',\n        fontWeight: 600,\n        color: '#333',\n        pb: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(Iconify, {\n        icon: \"material-symbols:logout\",\n        width: 24,\n        height: 24,\n        color: \"#f44336\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this), \"Opt Out of Plan\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogContent, {\n      className: styles.dialogContent,\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body1\",\n        sx: {\n          fontFamily: '\"Recursive Variable\", sans-serif',\n          color: '#666',\n          lineHeight: 1.6,\n          fontSize: '1rem'\n        },\n        children: \"Are you sure you want to opt out of this plan? You will no longer have access to this plan and its content. This action cannot be undone.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        sx: {\n          fontFamily: '\"Recursive Variable\", sans-serif',\n          color: '#999',\n          lineHeight: 1.5,\n          fontSize: '0.9rem',\n          mt: 2,\n          fontStyle: 'italic'\n        },\n        children: \"Note: You can be re-invited to this plan by the plan owner if needed.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(DialogActions, {\n      className: styles.dialogActions,\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        onClick: onClose,\n        disabled: loading,\n        sx: {\n          color: '#666',\n          textTransform: 'none',\n          fontFamily: '\"Recursive Variable\", sans-serif',\n          fontWeight: 600\n        },\n        children: \"Cancel\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: handleOptOut,\n        disabled: loading,\n        variant: \"contained\",\n        color: \"error\",\n        startIcon: loading ? /*#__PURE__*/_jsxDEV(CircularProgress, {\n          size: 16,\n          color: \"inherit\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 32\n        }, this) : /*#__PURE__*/_jsxDEV(Iconify, {\n          icon: \"material-symbols:logout\",\n          width: 16,\n          height: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 109,\n          columnNumber: 81\n        }, this),\n        sx: {\n          textTransform: 'none',\n          fontFamily: '\"Recursive Variable\", sans-serif',\n          fontWeight: 600\n        },\n        children: loading ? 'Opting Out...' : 'Opt Out'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 104,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 32,\n    columnNumber: 5\n  }, this);\n};\n_s(OptOutDialog, \"/Rjh5rPqCCqf0XYnTUk9ZNavw3Q=\");\n_c = OptOutDialog;\nexport default OptOutDialog;\nvar _c;\n$RefreshReg$(_c, \"OptOutDialog\");", "map": {"version": 3, "names": ["React", "useState", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "<PERSON><PERSON>", "Typography", "CircularProgress", "Iconify", "styles", "jsxDEV", "_jsxDEV", "OptOutDialog", "_ref", "_s", "open", "onClose", "onOptOut", "loading", "setLoading", "handleOptOut", "success", "err", "console", "error", "max<PERSON><PERSON><PERSON>", "fullWidth", "className", "deleteDialog", "PaperProps", "sx", "borderRadius", "padding", "children", "dialogTitle", "display", "alignItems", "gap", "fontFamily", "fontSize", "fontWeight", "color", "pb", "icon", "width", "height", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "dialogContent", "variant", "lineHeight", "mt", "fontStyle", "dialogActions", "onClick", "disabled", "textTransform", "startIcon", "size", "_c", "$RefreshReg$"], "sources": ["C:/ignition/ignition-ui/src/views/plan/detail/dialogs/OptOutDialog.js"], "sourcesContent": ["import React, { useState } from 'react';\nimport { \n  Dialog, \n  DialogTitle, \n  DialogContent, \n  DialogActions, \n  Button, \n  Typography, \n  CircularProgress\n} from '@mui/material';\nimport Iconify from 'components/Iconify/index';\nimport styles from '../styles.module.scss';\n\nconst OptOutDialog = ({ open, onClose, onOptOut }) => {\n  const [loading, setLoading] = useState(false);\n\n  const handleOptOut = async () => {\n    setLoading(true);\n    try {\n      const success = await onOptOut();\n      if (!success) {\n        setLoading(false);\n      }\n      // If successful, the page will navigate away\n    } catch (err) {\n      console.error('Error opting out of plan:', err);\n      setLoading(false);\n    }\n  };\n\n  return (\n    <Dialog \n      open={open} \n      onClose={loading ? null : onClose}\n      maxWidth=\"sm\"\n      fullWidth\n      className={styles.deleteDialog}\n      PaperProps={{\n        sx: {\n          borderRadius: '12px',\n          padding: '8px'\n        }\n      }}\n    >\n      <DialogTitle \n        className={styles.dialogTitle}\n        sx={{ \n          display: 'flex', \n          alignItems: 'center', \n          gap: 1,\n          fontFamily: '\"Recursive Variable\", sans-serif',\n          fontSize: '1.3rem',\n          fontWeight: 600,\n          color: '#333',\n          pb: 1\n        }}\n      >\n        <Iconify icon=\"material-symbols:logout\" width={24} height={24} color=\"#f44336\" />\n        Opt Out of Plan\n      </DialogTitle>\n      \n      <DialogContent className={styles.dialogContent}>\n        <Typography \n          variant=\"body1\"\n          sx={{ \n            fontFamily: '\"Recursive Variable\", sans-serif',\n            color: '#666',\n            lineHeight: 1.6,\n            fontSize: '1rem'\n          }}\n        >\n          Are you sure you want to opt out of this plan? You will no longer have access to this plan and its content. \n          This action cannot be undone.\n        </Typography>\n        \n        <Typography \n          variant=\"body2\"\n          sx={{ \n            fontFamily: '\"Recursive Variable\", sans-serif',\n            color: '#999',\n            lineHeight: 1.5,\n            fontSize: '0.9rem',\n            mt: 2,\n            fontStyle: 'italic'\n          }}\n        >\n          Note: You can be re-invited to this plan by the plan owner if needed.\n        </Typography>\n      </DialogContent>\n      \n      <DialogActions className={styles.dialogActions}>\n        <Button \n          onClick={onClose}\n          disabled={loading}\n          sx={{ \n            color: '#666',\n            textTransform: 'none',\n            fontFamily: '\"Recursive Variable\", sans-serif',\n            fontWeight: 600\n          }}\n        >\n          Cancel\n        </Button>\n        <Button \n          onClick={handleOptOut}\n          disabled={loading}\n          variant=\"contained\"\n          color=\"error\"\n          startIcon={loading ? <CircularProgress size={16} color=\"inherit\" /> : <Iconify icon=\"material-symbols:logout\" width={16} height={16} />}\n          sx={{ \n            textTransform: 'none',\n            fontFamily: '\"Recursive Variable\", sans-serif',\n            fontWeight: 600\n          }}\n        >\n          {loading ? 'Opting Out...' : 'Opt Out'}\n        </Button>\n      </DialogActions>\n    </Dialog>\n  );\n};\n\nexport default OptOutDialog;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SACEC,MAAM,EACNC,WAAW,EACXC,aAAa,EACbC,aAAa,EACbC,MAAM,EACNC,UAAU,EACVC,gBAAgB,QACX,eAAe;AACtB,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,OAAOC,MAAM,MAAM,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3C,MAAMC,YAAY,GAAGC,IAAA,IAAiC;EAAAC,EAAA;EAAA,IAAhC;IAAEC,IAAI;IAAEC,OAAO;IAAEC;EAAS,CAAC,GAAAJ,IAAA;EAC/C,MAAM,CAACK,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EAE7C,MAAMoB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/BD,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAME,OAAO,GAAG,MAAMJ,QAAQ,CAAC,CAAC;MAChC,IAAI,CAACI,OAAO,EAAE;QACZF,UAAU,CAAC,KAAK,CAAC;MACnB;MACA;IACF,CAAC,CAAC,OAAOG,GAAG,EAAE;MACZC,OAAO,CAACC,KAAK,CAAC,2BAA2B,EAAEF,GAAG,CAAC;MAC/CH,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACER,OAAA,CAACV,MAAM;IACLc,IAAI,EAAEA,IAAK;IACXC,OAAO,EAAEE,OAAO,GAAG,IAAI,GAAGF,OAAQ;IAClCS,QAAQ,EAAC,IAAI;IACbC,SAAS;IACTC,SAAS,EAAElB,MAAM,CAACmB,YAAa;IAC/BC,UAAU,EAAE;MACVC,EAAE,EAAE;QACFC,YAAY,EAAE,MAAM;QACpBC,OAAO,EAAE;MACX;IACF,CAAE;IAAAC,QAAA,gBAEFtB,OAAA,CAACT,WAAW;MACVyB,SAAS,EAAElB,MAAM,CAACyB,WAAY;MAC9BJ,EAAE,EAAE;QACFK,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBC,GAAG,EAAE,CAAC;QACNC,UAAU,EAAE,kCAAkC;QAC9CC,QAAQ,EAAE,QAAQ;QAClBC,UAAU,EAAE,GAAG;QACfC,KAAK,EAAE,MAAM;QACbC,EAAE,EAAE;MACN,CAAE;MAAAT,QAAA,gBAEFtB,OAAA,CAACH,OAAO;QAACmC,IAAI,EAAC,yBAAyB;QAACC,KAAK,EAAE,EAAG;QAACC,MAAM,EAAE,EAAG;QAACJ,KAAK,EAAC;MAAS;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,mBAEnF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAa,CAAC,eAEdtC,OAAA,CAACR,aAAa;MAACwB,SAAS,EAAElB,MAAM,CAACyC,aAAc;MAAAjB,QAAA,gBAC7CtB,OAAA,CAACL,UAAU;QACT6C,OAAO,EAAC,OAAO;QACfrB,EAAE,EAAE;UACFQ,UAAU,EAAE,kCAAkC;UAC9CG,KAAK,EAAE,MAAM;UACbW,UAAU,EAAE,GAAG;UACfb,QAAQ,EAAE;QACZ,CAAE;QAAAN,QAAA,EACH;MAGD;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbtC,OAAA,CAACL,UAAU;QACT6C,OAAO,EAAC,OAAO;QACfrB,EAAE,EAAE;UACFQ,UAAU,EAAE,kCAAkC;UAC9CG,KAAK,EAAE,MAAM;UACbW,UAAU,EAAE,GAAG;UACfb,QAAQ,EAAE,QAAQ;UAClBc,EAAE,EAAE,CAAC;UACLC,SAAS,EAAE;QACb,CAAE;QAAArB,QAAA,EACH;MAED;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAEhBtC,OAAA,CAACP,aAAa;MAACuB,SAAS,EAAElB,MAAM,CAAC8C,aAAc;MAAAtB,QAAA,gBAC7CtB,OAAA,CAACN,MAAM;QACLmD,OAAO,EAAExC,OAAQ;QACjByC,QAAQ,EAAEvC,OAAQ;QAClBY,EAAE,EAAE;UACFW,KAAK,EAAE,MAAM;UACbiB,aAAa,EAAE,MAAM;UACrBpB,UAAU,EAAE,kCAAkC;UAC9CE,UAAU,EAAE;QACd,CAAE;QAAAP,QAAA,EACH;MAED;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTtC,OAAA,CAACN,MAAM;QACLmD,OAAO,EAAEpC,YAAa;QACtBqC,QAAQ,EAAEvC,OAAQ;QAClBiC,OAAO,EAAC,WAAW;QACnBV,KAAK,EAAC,OAAO;QACbkB,SAAS,EAAEzC,OAAO,gBAAGP,OAAA,CAACJ,gBAAgB;UAACqD,IAAI,EAAE,EAAG;UAACnB,KAAK,EAAC;QAAS;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGtC,OAAA,CAACH,OAAO;UAACmC,IAAI,EAAC,yBAAyB;UAACC,KAAK,EAAE,EAAG;UAACC,MAAM,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACxInB,EAAE,EAAE;UACF4B,aAAa,EAAE,MAAM;UACrBpB,UAAU,EAAE,kCAAkC;UAC9CE,UAAU,EAAE;QACd,CAAE;QAAAP,QAAA,EAEDf,OAAO,GAAG,eAAe,GAAG;MAAS;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACV,CAAC;AAEb,CAAC;AAACnC,EAAA,CA3GIF,YAAY;AAAAiD,EAAA,GAAZjD,YAAY;AA6GlB,eAAeA,YAAY;AAAC,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}