{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nvar _excluded = [\"pure\", \"areStatesEqual\", \"areOwnPropsEqual\", \"areStatePropsEqual\", \"areMergedPropsEqual\"];\nimport connectAdvanced from '../components/connectAdvanced';\nimport shallowEqual from '../utils/shallowEqual';\nimport defaultMapDispatchToPropsFactories from './mapDispatchToProps';\nimport defaultMapStateToPropsFactories from './mapStateToProps';\nimport defaultMergePropsFactories from './mergeProps';\nimport defaultSelectorFactory from './selectorFactory';\n/*\r\n  connect is a facade over connectAdvanced. It turns its args into a compatible\r\n  selectorFactory, which has the signature:\r\n\r\n    (dispatch, options) => (nextState, nextOwnProps) => nextFinalProps\r\n  \r\n  connect passes its args to connectAdvanced as options, which will in turn pass them to\r\n  selectorFactory each time a Connect component instance is instantiated or hot reloaded.\r\n\r\n  selectorFactory returns a final props selector from its mapStateToProps,\r\n  mapStateToPropsFactories, mapDispatchToProps, mapDispatchToPropsFactories, mergeProps,\r\n  mergePropsFactories, and pure args.\r\n\r\n  The resulting final props selector is called by the Connect component instance whenever\r\n  it receives new props or store state.\r\n */\n\nfunction match(arg, factories, name) {\n  for (var i = factories.length - 1; i >= 0; i--) {\n    var result = factories[i](arg);\n    if (result) return result;\n  }\n  return function (dispatch, options) {\n    throw new Error(\"Invalid value of type \" + typeof arg + \" for \" + name + \" argument when connecting component \" + options.wrappedComponentName + \".\");\n  };\n}\nfunction strictEqual(a, b) {\n  return a === b;\n} // createConnect with default args builds the 'official' connect behavior. Calling it with\n// different options opens up some testing and extensibility scenarios\n\nexport function createConnect(_temp) {\n  var _ref = _temp === void 0 ? {} : _temp,\n    _ref$connectHOC = _ref.connectHOC,\n    connectHOC = _ref$connectHOC === void 0 ? connectAdvanced : _ref$connectHOC,\n    _ref$mapStateToPropsF = _ref.mapStateToPropsFactories,\n    mapStateToPropsFactories = _ref$mapStateToPropsF === void 0 ? defaultMapStateToPropsFactories : _ref$mapStateToPropsF,\n    _ref$mapDispatchToPro = _ref.mapDispatchToPropsFactories,\n    mapDispatchToPropsFactories = _ref$mapDispatchToPro === void 0 ? defaultMapDispatchToPropsFactories : _ref$mapDispatchToPro,\n    _ref$mergePropsFactor = _ref.mergePropsFactories,\n    mergePropsFactories = _ref$mergePropsFactor === void 0 ? defaultMergePropsFactories : _ref$mergePropsFactor,\n    _ref$selectorFactory = _ref.selectorFactory,\n    selectorFactory = _ref$selectorFactory === void 0 ? defaultSelectorFactory : _ref$selectorFactory;\n  return function connect(mapStateToProps, mapDispatchToProps, mergeProps, _ref2) {\n    if (_ref2 === void 0) {\n      _ref2 = {};\n    }\n    var _ref3 = _ref2,\n      _ref3$pure = _ref3.pure,\n      pure = _ref3$pure === void 0 ? true : _ref3$pure,\n      _ref3$areStatesEqual = _ref3.areStatesEqual,\n      areStatesEqual = _ref3$areStatesEqual === void 0 ? strictEqual : _ref3$areStatesEqual,\n      _ref3$areOwnPropsEqua = _ref3.areOwnPropsEqual,\n      areOwnPropsEqual = _ref3$areOwnPropsEqua === void 0 ? shallowEqual : _ref3$areOwnPropsEqua,\n      _ref3$areStatePropsEq = _ref3.areStatePropsEqual,\n      areStatePropsEqual = _ref3$areStatePropsEq === void 0 ? shallowEqual : _ref3$areStatePropsEq,\n      _ref3$areMergedPropsE = _ref3.areMergedPropsEqual,\n      areMergedPropsEqual = _ref3$areMergedPropsE === void 0 ? shallowEqual : _ref3$areMergedPropsE,\n      extraOptions = _objectWithoutPropertiesLoose(_ref3, _excluded);\n    var initMapStateToProps = match(mapStateToProps, mapStateToPropsFactories, 'mapStateToProps');\n    var initMapDispatchToProps = match(mapDispatchToProps, mapDispatchToPropsFactories, 'mapDispatchToProps');\n    var initMergeProps = match(mergeProps, mergePropsFactories, 'mergeProps');\n    return connectHOC(selectorFactory, _extends({\n      // used in error messages\n      methodName: 'connect',\n      // used to compute Connect's displayName from the wrapped component's displayName.\n      getDisplayName: function getDisplayName(name) {\n        return \"Connect(\" + name + \")\";\n      },\n      // if mapStateToProps is falsy, the Connect component doesn't subscribe to store state changes\n      shouldHandleStateChanges: Boolean(mapStateToProps),\n      // passed through to selectorFactory\n      initMapStateToProps: initMapStateToProps,\n      initMapDispatchToProps: initMapDispatchToProps,\n      initMergeProps: initMergeProps,\n      pure: pure,\n      areStatesEqual: areStatesEqual,\n      areOwnPropsEqual: areOwnPropsEqual,\n      areStatePropsEqual: areStatePropsEqual,\n      areMergedPropsEqual: areMergedPropsEqual\n    }, extraOptions));\n  };\n}\nexport default /*#__PURE__*/createConnect();", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "connectAdvanced", "shallowEqual", "defaultMapDispatchToPropsFactories", "defaultMapStateToPropsFactories", "defaultMergePropsFactories", "defaultSelectorFactory", "match", "arg", "factories", "name", "i", "length", "result", "dispatch", "options", "Error", "wrappedComponentName", "strictEqual", "a", "b", "createConnect", "_temp", "_ref", "_ref$connectHOC", "connectHOC", "_ref$mapStateToPropsF", "mapStateToPropsFactories", "_ref$mapDispatchToPro", "mapDispatchToPropsFactories", "_ref$mergePropsFactor", "mergePropsFactories", "_ref$selectorFactory", "selectorFactory", "connect", "mapStateToProps", "mapDispatchToProps", "mergeProps", "_ref2", "_ref3", "_ref3$pure", "pure", "_ref3$areStatesEqual", "areStatesEqual", "_ref3$areOwnPropsEqua", "areOwnPropsEqual", "_ref3$areStatePropsEq", "areStatePropsEqual", "_ref3$areMergedPropsE", "areMergedPropsEqual", "extraOptions", "initMapStateToProps", "initMapDispatchToProps", "initMergeProps", "methodName", "getDisplayName", "shouldHandleStateChanges", "Boolean"], "sources": ["C:/ignition/ignition-ui/node_modules/react-beautiful-dnd/node_modules/react-redux/es/connect/connect.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nvar _excluded = [\"pure\", \"areStatesEqual\", \"areOwnPropsEqual\", \"areStatePropsEqual\", \"areMergedPropsEqual\"];\nimport connectAdvanced from '../components/connectAdvanced';\nimport shallowEqual from '../utils/shallowEqual';\nimport defaultMapDispatchToPropsFactories from './mapDispatchToProps';\nimport defaultMapStateToPropsFactories from './mapStateToProps';\nimport defaultMergePropsFactories from './mergeProps';\nimport defaultSelectorFactory from './selectorFactory';\n/*\r\n  connect is a facade over connectAdvanced. It turns its args into a compatible\r\n  selectorFactory, which has the signature:\r\n\r\n    (dispatch, options) => (nextState, nextOwnProps) => nextFinalProps\r\n  \r\n  connect passes its args to connectAdvanced as options, which will in turn pass them to\r\n  selectorFactory each time a Connect component instance is instantiated or hot reloaded.\r\n\r\n  selectorFactory returns a final props selector from its mapStateToProps,\r\n  mapStateToPropsFactories, mapDispatchToProps, mapDispatchToPropsFactories, mergeProps,\r\n  mergePropsFactories, and pure args.\r\n\r\n  The resulting final props selector is called by the Connect component instance whenever\r\n  it receives new props or store state.\r\n */\n\nfunction match(arg, factories, name) {\n  for (var i = factories.length - 1; i >= 0; i--) {\n    var result = factories[i](arg);\n    if (result) return result;\n  }\n\n  return function (dispatch, options) {\n    throw new Error(\"Invalid value of type \" + typeof arg + \" for \" + name + \" argument when connecting component \" + options.wrappedComponentName + \".\");\n  };\n}\n\nfunction strictEqual(a, b) {\n  return a === b;\n} // createConnect with default args builds the 'official' connect behavior. Calling it with\n// different options opens up some testing and extensibility scenarios\n\n\nexport function createConnect(_temp) {\n  var _ref = _temp === void 0 ? {} : _temp,\n      _ref$connectHOC = _ref.connectHOC,\n      connectHOC = _ref$connectHOC === void 0 ? connectAdvanced : _ref$connectHOC,\n      _ref$mapStateToPropsF = _ref.mapStateToPropsFactories,\n      mapStateToPropsFactories = _ref$mapStateToPropsF === void 0 ? defaultMapStateToPropsFactories : _ref$mapStateToPropsF,\n      _ref$mapDispatchToPro = _ref.mapDispatchToPropsFactories,\n      mapDispatchToPropsFactories = _ref$mapDispatchToPro === void 0 ? defaultMapDispatchToPropsFactories : _ref$mapDispatchToPro,\n      _ref$mergePropsFactor = _ref.mergePropsFactories,\n      mergePropsFactories = _ref$mergePropsFactor === void 0 ? defaultMergePropsFactories : _ref$mergePropsFactor,\n      _ref$selectorFactory = _ref.selectorFactory,\n      selectorFactory = _ref$selectorFactory === void 0 ? defaultSelectorFactory : _ref$selectorFactory;\n\n  return function connect(mapStateToProps, mapDispatchToProps, mergeProps, _ref2) {\n    if (_ref2 === void 0) {\n      _ref2 = {};\n    }\n\n    var _ref3 = _ref2,\n        _ref3$pure = _ref3.pure,\n        pure = _ref3$pure === void 0 ? true : _ref3$pure,\n        _ref3$areStatesEqual = _ref3.areStatesEqual,\n        areStatesEqual = _ref3$areStatesEqual === void 0 ? strictEqual : _ref3$areStatesEqual,\n        _ref3$areOwnPropsEqua = _ref3.areOwnPropsEqual,\n        areOwnPropsEqual = _ref3$areOwnPropsEqua === void 0 ? shallowEqual : _ref3$areOwnPropsEqua,\n        _ref3$areStatePropsEq = _ref3.areStatePropsEqual,\n        areStatePropsEqual = _ref3$areStatePropsEq === void 0 ? shallowEqual : _ref3$areStatePropsEq,\n        _ref3$areMergedPropsE = _ref3.areMergedPropsEqual,\n        areMergedPropsEqual = _ref3$areMergedPropsE === void 0 ? shallowEqual : _ref3$areMergedPropsE,\n        extraOptions = _objectWithoutPropertiesLoose(_ref3, _excluded);\n\n    var initMapStateToProps = match(mapStateToProps, mapStateToPropsFactories, 'mapStateToProps');\n    var initMapDispatchToProps = match(mapDispatchToProps, mapDispatchToPropsFactories, 'mapDispatchToProps');\n    var initMergeProps = match(mergeProps, mergePropsFactories, 'mergeProps');\n    return connectHOC(selectorFactory, _extends({\n      // used in error messages\n      methodName: 'connect',\n      // used to compute Connect's displayName from the wrapped component's displayName.\n      getDisplayName: function getDisplayName(name) {\n        return \"Connect(\" + name + \")\";\n      },\n      // if mapStateToProps is falsy, the Connect component doesn't subscribe to store state changes\n      shouldHandleStateChanges: Boolean(mapStateToProps),\n      // passed through to selectorFactory\n      initMapStateToProps: initMapStateToProps,\n      initMapDispatchToProps: initMapDispatchToProps,\n      initMergeProps: initMergeProps,\n      pure: pure,\n      areStatesEqual: areStatesEqual,\n      areOwnPropsEqual: areOwnPropsEqual,\n      areStatePropsEqual: areStatePropsEqual,\n      areMergedPropsEqual: areMergedPropsEqual\n    }, extraOptions));\n  };\n}\nexport default /*#__PURE__*/createConnect();"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,IAAIC,SAAS,GAAG,CAAC,MAAM,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,oBAAoB,EAAE,qBAAqB,CAAC;AAC3G,OAAOC,eAAe,MAAM,+BAA+B;AAC3D,OAAOC,YAAY,MAAM,uBAAuB;AAChD,OAAOC,kCAAkC,MAAM,sBAAsB;AACrE,OAAOC,+BAA+B,MAAM,mBAAmB;AAC/D,OAAOC,0BAA0B,MAAM,cAAc;AACrD,OAAOC,sBAAsB,MAAM,mBAAmB;AACtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,SAASC,KAAKA,CAACC,GAAG,EAAEC,SAAS,EAAEC,IAAI,EAAE;EACnC,KAAK,IAAIC,CAAC,GAAGF,SAAS,CAACG,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;IAC9C,IAAIE,MAAM,GAAGJ,SAAS,CAACE,CAAC,CAAC,CAACH,GAAG,CAAC;IAC9B,IAAIK,MAAM,EAAE,OAAOA,MAAM;EAC3B;EAEA,OAAO,UAAUC,QAAQ,EAAEC,OAAO,EAAE;IAClC,MAAM,IAAIC,KAAK,CAAC,wBAAwB,GAAG,OAAOR,GAAG,GAAG,OAAO,GAAGE,IAAI,GAAG,sCAAsC,GAAGK,OAAO,CAACE,oBAAoB,GAAG,GAAG,CAAC;EACvJ,CAAC;AACH;AAEA,SAASC,WAAWA,CAACC,CAAC,EAAEC,CAAC,EAAE;EACzB,OAAOD,CAAC,KAAKC,CAAC;AAChB,CAAC,CAAC;AACF;;AAGA,OAAO,SAASC,aAAaA,CAACC,KAAK,EAAE;EACnC,IAAIC,IAAI,GAAGD,KAAK,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,KAAK;IACpCE,eAAe,GAAGD,IAAI,CAACE,UAAU;IACjCA,UAAU,GAAGD,eAAe,KAAK,KAAK,CAAC,GAAGvB,eAAe,GAAGuB,eAAe;IAC3EE,qBAAqB,GAAGH,IAAI,CAACI,wBAAwB;IACrDA,wBAAwB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAGtB,+BAA+B,GAAGsB,qBAAqB;IACrHE,qBAAqB,GAAGL,IAAI,CAACM,2BAA2B;IACxDA,2BAA2B,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAGzB,kCAAkC,GAAGyB,qBAAqB;IAC3HE,qBAAqB,GAAGP,IAAI,CAACQ,mBAAmB;IAChDA,mBAAmB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAGzB,0BAA0B,GAAGyB,qBAAqB;IAC3GE,oBAAoB,GAAGT,IAAI,CAACU,eAAe;IAC3CA,eAAe,GAAGD,oBAAoB,KAAK,KAAK,CAAC,GAAG1B,sBAAsB,GAAG0B,oBAAoB;EAErG,OAAO,SAASE,OAAOA,CAACC,eAAe,EAAEC,kBAAkB,EAAEC,UAAU,EAAEC,KAAK,EAAE;IAC9E,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MACpBA,KAAK,GAAG,CAAC,CAAC;IACZ;IAEA,IAAIC,KAAK,GAAGD,KAAK;MACbE,UAAU,GAAGD,KAAK,CAACE,IAAI;MACvBA,IAAI,GAAGD,UAAU,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,UAAU;MAChDE,oBAAoB,GAAGH,KAAK,CAACI,cAAc;MAC3CA,cAAc,GAAGD,oBAAoB,KAAK,KAAK,CAAC,GAAGxB,WAAW,GAAGwB,oBAAoB;MACrFE,qBAAqB,GAAGL,KAAK,CAACM,gBAAgB;MAC9CA,gBAAgB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG1C,YAAY,GAAG0C,qBAAqB;MAC1FE,qBAAqB,GAAGP,KAAK,CAACQ,kBAAkB;MAChDA,kBAAkB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG5C,YAAY,GAAG4C,qBAAqB;MAC5FE,qBAAqB,GAAGT,KAAK,CAACU,mBAAmB;MACjDA,mBAAmB,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAG9C,YAAY,GAAG8C,qBAAqB;MAC7FE,YAAY,GAAGnD,6BAA6B,CAACwC,KAAK,EAAEvC,SAAS,CAAC;IAElE,IAAImD,mBAAmB,GAAG5C,KAAK,CAAC4B,eAAe,EAAER,wBAAwB,EAAE,iBAAiB,CAAC;IAC7F,IAAIyB,sBAAsB,GAAG7C,KAAK,CAAC6B,kBAAkB,EAAEP,2BAA2B,EAAE,oBAAoB,CAAC;IACzG,IAAIwB,cAAc,GAAG9C,KAAK,CAAC8B,UAAU,EAAEN,mBAAmB,EAAE,YAAY,CAAC;IACzE,OAAON,UAAU,CAACQ,eAAe,EAAEnC,QAAQ,CAAC;MAC1C;MACAwD,UAAU,EAAE,SAAS;MACrB;MACAC,cAAc,EAAE,SAASA,cAAcA,CAAC7C,IAAI,EAAE;QAC5C,OAAO,UAAU,GAAGA,IAAI,GAAG,GAAG;MAChC,CAAC;MACD;MACA8C,wBAAwB,EAAEC,OAAO,CAACtB,eAAe,CAAC;MAClD;MACAgB,mBAAmB,EAAEA,mBAAmB;MACxCC,sBAAsB,EAAEA,sBAAsB;MAC9CC,cAAc,EAAEA,cAAc;MAC9BZ,IAAI,EAAEA,IAAI;MACVE,cAAc,EAAEA,cAAc;MAC9BE,gBAAgB,EAAEA,gBAAgB;MAClCE,kBAAkB,EAAEA,kBAAkB;MACtCE,mBAAmB,EAAEA;IACvB,CAAC,EAAEC,YAAY,CAAC,CAAC;EACnB,CAAC;AACH;AACA,eAAe,aAAa7B,aAAa,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}