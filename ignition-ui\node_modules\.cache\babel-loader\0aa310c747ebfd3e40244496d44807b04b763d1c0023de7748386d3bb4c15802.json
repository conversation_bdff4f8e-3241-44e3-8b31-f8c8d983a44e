{"ast": null, "code": "import isPlainObject from './isPlainObject';\nimport warning from './warning';\nexport default function verifyPlainObject(value, displayName, methodName) {\n  if (!isPlainObject(value)) {\n    warning(methodName + \"() in \" + displayName + \" must return a plain object. Instead received \" + value + \".\");\n  }\n}", "map": {"version": 3, "names": ["isPlainObject", "warning", "verifyPlainObject", "value", "displayName", "methodName"], "sources": ["C:/ignition/ignition-ui/node_modules/react-beautiful-dnd/node_modules/react-redux/es/utils/verifyPlainObject.js"], "sourcesContent": ["import isPlainObject from './isPlainObject';\nimport warning from './warning';\nexport default function verifyPlainObject(value, displayName, methodName) {\n  if (!isPlainObject(value)) {\n    warning(methodName + \"() in \" + displayName + \" must return a plain object. Instead received \" + value + \".\");\n  }\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,OAAO,MAAM,WAAW;AAC/B,eAAe,SAASC,iBAAiBA,CAACC,KAAK,EAAEC,WAAW,EAAEC,UAAU,EAAE;EACxE,IAAI,CAACL,aAAa,CAACG,KAAK,CAAC,EAAE;IACzBF,OAAO,CAACI,UAAU,GAAG,QAAQ,GAAGD,WAAW,GAAG,gDAAgD,GAAGD,KAAK,GAAG,GAAG,CAAC;EAC/G;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}