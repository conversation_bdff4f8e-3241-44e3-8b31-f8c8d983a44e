{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { listReducer, ListActionTypes, moveHighlight } from '../useList';\nimport { TabsListActionTypes } from './useTabsList.types';\nexport function tabsListReducer(state, action) {\n  if (action.type === TabsListActionTypes.valueChange) {\n    return _extends({}, state, {\n      highlightedValue: action.value\n    });\n  }\n  const newState = listReducer(state, action);\n  const {\n    context: {\n      selectionFollowsFocus\n    }\n  } = action;\n  if (action.type === ListActionTypes.itemsChange) {\n    if (newState.selectedValues.length > 0) {\n      return _extends({}, newState, {\n        highlightedValue: newState.selectedValues[0]\n      });\n    }\n    moveHighlight(null, 'reset', action.context);\n  }\n  if (selectionFollowsFocus && newState.highlightedValue != null) {\n    return _extends({}, newState, {\n      selectedValues: [newState.highlightedValue]\n    });\n  }\n  return newState;\n}", "map": {"version": 3, "names": ["_extends", "listReducer", "ListActionTypes", "moveHighlight", "TabsListActionTypes", "tabsListReducer", "state", "action", "type", "valueChange", "highlightedValue", "value", "newState", "context", "selectionFollowsFocus", "itemsChange", "<PERSON><PERSON><PERSON><PERSON>", "length"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/useTabsList/tabsListReducer.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { listReducer, ListActionTypes, moveHighlight } from '../useList';\nimport { TabsListActionTypes } from './useTabsList.types';\nexport function tabsListReducer(state, action) {\n  if (action.type === TabsListActionTypes.valueChange) {\n    return _extends({}, state, {\n      highlightedValue: action.value\n    });\n  }\n  const newState = listReducer(state, action);\n  const {\n    context: {\n      selectionFollowsFocus\n    }\n  } = action;\n  if (action.type === ListActionTypes.itemsChange) {\n    if (newState.selectedValues.length > 0) {\n      return _extends({}, newState, {\n        highlightedValue: newState.selectedValues[0]\n      });\n    }\n    moveHighlight(null, 'reset', action.context);\n  }\n  if (selectionFollowsFocus && newState.highlightedValue != null) {\n    return _extends({}, newState, {\n      selectedValues: [newState.highlightedValue]\n    });\n  }\n  return newState;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,WAAW,EAAEC,eAAe,EAAEC,aAAa,QAAQ,YAAY;AACxE,SAASC,mBAAmB,QAAQ,qBAAqB;AACzD,OAAO,SAASC,eAAeA,CAACC,KAAK,EAAEC,MAAM,EAAE;EAC7C,IAAIA,MAAM,CAACC,IAAI,KAAKJ,mBAAmB,CAACK,WAAW,EAAE;IACnD,OAAOT,QAAQ,CAAC,CAAC,CAAC,EAAEM,KAAK,EAAE;MACzBI,gBAAgB,EAAEH,MAAM,CAACI;IAC3B,CAAC,CAAC;EACJ;EACA,MAAMC,QAAQ,GAAGX,WAAW,CAACK,KAAK,EAAEC,MAAM,CAAC;EAC3C,MAAM;IACJM,OAAO,EAAE;MACPC;IACF;EACF,CAAC,GAAGP,MAAM;EACV,IAAIA,MAAM,CAACC,IAAI,KAAKN,eAAe,CAACa,WAAW,EAAE;IAC/C,IAAIH,QAAQ,CAACI,cAAc,CAACC,MAAM,GAAG,CAAC,EAAE;MACtC,OAAOjB,QAAQ,CAAC,CAAC,CAAC,EAAEY,QAAQ,EAAE;QAC5BF,gBAAgB,EAAEE,QAAQ,CAACI,cAAc,CAAC,CAAC;MAC7C,CAAC,CAAC;IACJ;IACAb,aAAa,CAAC,IAAI,EAAE,OAAO,EAAEI,MAAM,CAACM,OAAO,CAAC;EAC9C;EACA,IAAIC,qBAAqB,IAAIF,QAAQ,CAACF,gBAAgB,IAAI,IAAI,EAAE;IAC9D,OAAOV,QAAQ,CAAC,CAAC,CAAC,EAAEY,QAAQ,EAAE;MAC5BI,cAAc,EAAE,CAACJ,QAAQ,CAACF,gBAAgB;IAC5C,CAAC,CAAC;EACJ;EACA,OAAOE,QAAQ;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}