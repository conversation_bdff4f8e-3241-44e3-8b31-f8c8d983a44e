{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"component\", \"children\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport { useThemeProps } from '../styles';\nimport styled from '../styles/styled';\nimport stepClasses from '../Step/stepClasses';\nimport stepperClasses from '../Stepper/stepperClasses';\nimport stepButtonClasses from './stepButtonClasses';\nimport useSlot from '../utils/useSlot';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst StepButtonRoot = styled('button', {\n  name: 'JoyStepButton',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(_ref => {\n  let {\n    theme\n  } = _ref;\n  return {\n    [`.${stepClasses.indicator}:empty + &`]: {\n      '--StepIndicator-size': '0px',\n      '--Step-gap': '0px'\n    },\n    [`.${stepClasses.horizontal} &`]: {\n      '--_StepButton-alignSelf': 'stretch',\n      '--_StepButton-gap': 'var(--Step-gap)'\n    },\n    [`.${stepClasses.horizontal} &::before`]: {\n      '--_StepButton-left': 'calc(-1 * (var(--StepIndicator-size) + var(--Step-gap)))'\n    },\n    [`.${stepClasses.vertical} &::before`]: {\n      '--_StepButton-top': 'calc(-1 * (var(--StepIndicator-size) + var(--Step-gap)))'\n    },\n    [`.${stepperClasses.vertical} .${stepClasses.vertical} &`]: {\n      '--_StepButton-alignItems': 'flex-start'\n    },\n    [`.${stepperClasses.vertical} &::before`]: {\n      '--_StepButton-left': 'calc(-1 * (var(--StepIndicator-size) + var(--Step-gap)))',\n      '--_StepButton-top': '0px'\n    },\n    WebkitTapHighlightColor: 'transparent',\n    boxSizing: 'border-box',\n    border: 'none',\n    backgroundColor: 'transparent',\n    cursor: 'pointer',\n    position: 'relative',\n    padding: 0,\n    textDecoration: 'none',\n    // prevent user agent underline when used as anchor\n    font: 'inherit',\n    display: 'inline-flex',\n    flexDirection: 'inherit',\n    alignItems: 'var(--_StepButton-alignItems, inherit)',\n    alignSelf: 'var(--_StepButton-alignSelf)',\n    gap: 'var(--_StepButton-gap)',\n    [theme.focus.selector]: theme.focus.default,\n    '&::before': {\n      content: '\"\"',\n      display: 'block',\n      position: 'absolute',\n      top: 'var(--_StepButton-top, 0)',\n      right: 0,\n      bottom: 0,\n      left: 'var(--_StepButton-left, 0)'\n    }\n  };\n});\n\n/**\n *\n * Demos:\n *\n * - [Stepper](https://mui.com/joy-ui/react-stepper/)\n *\n * API:\n *\n * - [StepButton API](https://mui.com/joy-ui/api/step-button/)\n */\nconst StepButton = /*#__PURE__*/React.forwardRef(function StepButton(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyStepButton'\n  });\n  const {\n      className,\n      component = 'button',\n      children,\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    component\n  });\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    className: clsx(stepButtonClasses.root, className),\n    elementType: StepButtonRoot,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      type: 'button'\n    }\n  });\n  return /*#__PURE__*/_jsx(SlotRoot, _extends({}, rootProps, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? StepButton.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Used to render icon or text elements inside the StepButton if `src` is not set.\n   * This can be an element, or just a string.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default StepButton;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "clsx", "PropTypes", "useThemeProps", "styled", "stepClasses", "stepperClasses", "stepButtonClasses", "useSlot", "jsx", "_jsx", "StepButtonRoot", "name", "slot", "overridesResolver", "props", "styles", "root", "_ref", "theme", "indicator", "horizontal", "vertical", "WebkitTapHighlightColor", "boxSizing", "border", "backgroundColor", "cursor", "position", "padding", "textDecoration", "font", "display", "flexDirection", "alignItems", "alignSelf", "gap", "focus", "selector", "default", "content", "top", "right", "bottom", "left", "StepButton", "forwardRef", "inProps", "ref", "className", "component", "children", "slots", "slotProps", "other", "ownerState", "externalForwardedProps", "SlotRoot", "rootProps", "elementType", "additionalProps", "type", "process", "env", "NODE_ENV", "propTypes", "node", "string", "shape", "oneOfType", "func", "object", "sx", "arrayOf", "bool"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/StepButton/StepButton.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"component\", \"children\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport { useThemeProps } from '../styles';\nimport styled from '../styles/styled';\nimport stepClasses from '../Step/stepClasses';\nimport stepperClasses from '../Stepper/stepperClasses';\nimport stepButtonClasses from './stepButtonClasses';\nimport useSlot from '../utils/useSlot';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst StepButtonRoot = styled('button', {\n  name: 'JoyStepButton',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(({\n  theme\n}) => {\n  return {\n    [`.${stepClasses.indicator}:empty + &`]: {\n      '--StepIndicator-size': '0px',\n      '--Step-gap': '0px'\n    },\n    [`.${stepClasses.horizontal} &`]: {\n      '--_StepButton-alignSelf': 'stretch',\n      '--_StepButton-gap': 'var(--Step-gap)'\n    },\n    [`.${stepClasses.horizontal} &::before`]: {\n      '--_StepButton-left': 'calc(-1 * (var(--StepIndicator-size) + var(--Step-gap)))'\n    },\n    [`.${stepClasses.vertical} &::before`]: {\n      '--_StepButton-top': 'calc(-1 * (var(--StepIndicator-size) + var(--Step-gap)))'\n    },\n    [`.${stepperClasses.vertical} .${stepClasses.vertical} &`]: {\n      '--_StepButton-alignItems': 'flex-start'\n    },\n    [`.${stepperClasses.vertical} &::before`]: {\n      '--_StepButton-left': 'calc(-1 * (var(--StepIndicator-size) + var(--Step-gap)))',\n      '--_StepButton-top': '0px'\n    },\n    WebkitTapHighlightColor: 'transparent',\n    boxSizing: 'border-box',\n    border: 'none',\n    backgroundColor: 'transparent',\n    cursor: 'pointer',\n    position: 'relative',\n    padding: 0,\n    textDecoration: 'none',\n    // prevent user agent underline when used as anchor\n    font: 'inherit',\n    display: 'inline-flex',\n    flexDirection: 'inherit',\n    alignItems: 'var(--_StepButton-alignItems, inherit)',\n    alignSelf: 'var(--_StepButton-alignSelf)',\n    gap: 'var(--_StepButton-gap)',\n    [theme.focus.selector]: theme.focus.default,\n    '&::before': {\n      content: '\"\"',\n      display: 'block',\n      position: 'absolute',\n      top: 'var(--_StepButton-top, 0)',\n      right: 0,\n      bottom: 0,\n      left: 'var(--_StepButton-left, 0)'\n    }\n  };\n});\n\n/**\n *\n * Demos:\n *\n * - [Stepper](https://mui.com/joy-ui/react-stepper/)\n *\n * API:\n *\n * - [StepButton API](https://mui.com/joy-ui/api/step-button/)\n */\nconst StepButton = /*#__PURE__*/React.forwardRef(function StepButton(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyStepButton'\n  });\n  const {\n      className,\n      component = 'button',\n      children,\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    component\n  });\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    className: clsx(stepButtonClasses.root, className),\n    elementType: StepButtonRoot,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      type: 'button'\n    }\n  });\n  return /*#__PURE__*/_jsx(SlotRoot, _extends({}, rootProps, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? StepButton.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Used to render icon or text elements inside the StepButton if `src` is not set.\n   * This can be an element, or just a string.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default StepButton;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,CAAC;AAC9E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,aAAa,QAAQ,WAAW;AACzC,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,WAAW,MAAM,qBAAqB;AAC7C,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,OAAO,MAAM,kBAAkB;AACtC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,cAAc,GAAGP,MAAM,CAAC,QAAQ,EAAE;EACtCQ,IAAI,EAAE,eAAe;EACrBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACC;AAC/C,CAAC,CAAC,CAACC,IAAA,IAEG;EAAA,IAFF;IACFC;EACF,CAAC,GAAAD,IAAA;EACC,OAAO;IACL,CAAC,IAAIb,WAAW,CAACe,SAAS,YAAY,GAAG;MACvC,sBAAsB,EAAE,KAAK;MAC7B,YAAY,EAAE;IAChB,CAAC;IACD,CAAC,IAAIf,WAAW,CAACgB,UAAU,IAAI,GAAG;MAChC,yBAAyB,EAAE,SAAS;MACpC,mBAAmB,EAAE;IACvB,CAAC;IACD,CAAC,IAAIhB,WAAW,CAACgB,UAAU,YAAY,GAAG;MACxC,oBAAoB,EAAE;IACxB,CAAC;IACD,CAAC,IAAIhB,WAAW,CAACiB,QAAQ,YAAY,GAAG;MACtC,mBAAmB,EAAE;IACvB,CAAC;IACD,CAAC,IAAIhB,cAAc,CAACgB,QAAQ,KAAKjB,WAAW,CAACiB,QAAQ,IAAI,GAAG;MAC1D,0BAA0B,EAAE;IAC9B,CAAC;IACD,CAAC,IAAIhB,cAAc,CAACgB,QAAQ,YAAY,GAAG;MACzC,oBAAoB,EAAE,0DAA0D;MAChF,mBAAmB,EAAE;IACvB,CAAC;IACDC,uBAAuB,EAAE,aAAa;IACtCC,SAAS,EAAE,YAAY;IACvBC,MAAM,EAAE,MAAM;IACdC,eAAe,EAAE,aAAa;IAC9BC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE,UAAU;IACpBC,OAAO,EAAE,CAAC;IACVC,cAAc,EAAE,MAAM;IACtB;IACAC,IAAI,EAAE,SAAS;IACfC,OAAO,EAAE,aAAa;IACtBC,aAAa,EAAE,SAAS;IACxBC,UAAU,EAAE,wCAAwC;IACpDC,SAAS,EAAE,8BAA8B;IACzCC,GAAG,EAAE,wBAAwB;IAC7B,CAACjB,KAAK,CAACkB,KAAK,CAACC,QAAQ,GAAGnB,KAAK,CAACkB,KAAK,CAACE,OAAO;IAC3C,WAAW,EAAE;MACXC,OAAO,EAAE,IAAI;MACbR,OAAO,EAAE,OAAO;MAChBJ,QAAQ,EAAE,UAAU;MACpBa,GAAG,EAAE,2BAA2B;MAChCC,KAAK,EAAE,CAAC;MACRC,MAAM,EAAE,CAAC;MACTC,IAAI,EAAE;IACR;EACF,CAAC;AACH,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,GAAG,aAAa7C,KAAK,CAAC8C,UAAU,CAAC,SAASD,UAAUA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACjF,MAAMjC,KAAK,GAAGZ,aAAa,CAAC;IAC1BY,KAAK,EAAEgC,OAAO;IACdnC,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFqC,SAAS;MACTC,SAAS,GAAG,QAAQ;MACpBC,QAAQ;MACRC,KAAK,GAAG,CAAC,CAAC;MACVC,SAAS,GAAG,CAAC;IACf,CAAC,GAAGtC,KAAK;IACTuC,KAAK,GAAGxD,6BAA6B,CAACiB,KAAK,EAAEhB,SAAS,CAAC;EACzD,MAAMwD,UAAU,GAAG1D,QAAQ,CAAC,CAAC,CAAC,EAAEkB,KAAK,EAAE;IACrCmC;EACF,CAAC,CAAC;EACF,MAAMM,sBAAsB,GAAG3D,QAAQ,CAAC,CAAC,CAAC,EAAEyD,KAAK,EAAE;IACjDJ,SAAS;IACTE,KAAK;IACLC;EACF,CAAC,CAAC;EACF,MAAM,CAACI,QAAQ,EAAEC,SAAS,CAAC,GAAGlD,OAAO,CAAC,MAAM,EAAE;IAC5CwC,GAAG;IACHC,SAAS,EAAEhD,IAAI,CAACM,iBAAiB,CAACU,IAAI,EAAEgC,SAAS,CAAC;IAClDU,WAAW,EAAEhD,cAAc;IAC3B6C,sBAAsB;IACtBD,UAAU;IACVK,eAAe,EAAE;MACfC,IAAI,EAAE;IACR;EACF,CAAC,CAAC;EACF,OAAO,aAAanD,IAAI,CAAC+C,QAAQ,EAAE5D,QAAQ,CAAC,CAAC,CAAC,EAAE6D,SAAS,EAAE;IACzDP,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFW,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGnB,UAAU,CAACoB,SAAS,CAAC,yBAAyB;EACpF;EACA;EACA;EACA;EACA;AACF;AACA;AACA;EACEd,QAAQ,EAAEjD,SAAS,CAACgE,IAAI;EACxB;AACF;AACA;EACEjB,SAAS,EAAE/C,SAAS,CAACiE,MAAM;EAC3B;AACF;AACA;AACA;EACEjB,SAAS,EAAEhD,SAAS,CAACyD,WAAW;EAChC;AACF;AACA;AACA;EACEN,SAAS,EAAEnD,SAAS,CAACkE,KAAK,CAAC;IACzBnD,IAAI,EAAEf,SAAS,CAACmE,SAAS,CAAC,CAACnE,SAAS,CAACoE,IAAI,EAAEpE,SAAS,CAACqE,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEnB,KAAK,EAAElD,SAAS,CAACkE,KAAK,CAAC;IACrBnD,IAAI,EAAEf,SAAS,CAACyD;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACEa,EAAE,EAAEtE,SAAS,CAACmE,SAAS,CAAC,CAACnE,SAAS,CAACuE,OAAO,CAACvE,SAAS,CAACmE,SAAS,CAAC,CAACnE,SAAS,CAACoE,IAAI,EAAEpE,SAAS,CAACqE,MAAM,EAAErE,SAAS,CAACwE,IAAI,CAAC,CAAC,CAAC,EAAExE,SAAS,CAACoE,IAAI,EAAEpE,SAAS,CAACqE,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAe1B,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}