{"ast": null, "code": "import { useContext } from 'react';\nimport { ReactReduxContext } from '../components/Context';\n/**\r\n * A hook to access the value of the `ReactReduxContext`. This is a low-level\r\n * hook that you should usually not need to call directly.\r\n *\r\n * @returns {any} the value of the `ReactReduxContext`\r\n *\r\n * @example\r\n *\r\n * import React from 'react'\r\n * import { useReduxContext } from 'react-redux'\r\n *\r\n * export const CounterComponent = ({ value }) => {\r\n *   const { store } = useReduxContext()\r\n *   return <div>{store.getState()}</div>\r\n * }\r\n */\n\nexport function useReduxContext() {\n  var contextValue = useContext(ReactReduxContext);\n  if (process.env.NODE_ENV !== 'production' && !contextValue) {\n    throw new Error('could not find react-redux context value; please ensure the component is wrapped in a <Provider>');\n  }\n  return contextValue;\n}", "map": {"version": 3, "names": ["useContext", "ReactReduxContext", "useReduxContext", "contextValue", "process", "env", "NODE_ENV", "Error"], "sources": ["C:/ignition/ignition-ui/node_modules/react-beautiful-dnd/node_modules/react-redux/es/hooks/useReduxContext.js"], "sourcesContent": ["import { useContext } from 'react';\nimport { ReactReduxContext } from '../components/Context';\n/**\r\n * A hook to access the value of the `ReactReduxContext`. This is a low-level\r\n * hook that you should usually not need to call directly.\r\n *\r\n * @returns {any} the value of the `ReactReduxContext`\r\n *\r\n * @example\r\n *\r\n * import React from 'react'\r\n * import { useReduxContext } from 'react-redux'\r\n *\r\n * export const CounterComponent = ({ value }) => {\r\n *   const { store } = useReduxContext()\r\n *   return <div>{store.getState()}</div>\r\n * }\r\n */\n\nexport function useReduxContext() {\n  var contextValue = useContext(ReactReduxContext);\n\n  if (process.env.NODE_ENV !== 'production' && !contextValue) {\n    throw new Error('could not find react-redux context value; please ensure the component is wrapped in a <Provider>');\n  }\n\n  return contextValue;\n}"], "mappings": "AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,eAAeA,CAAA,EAAG;EAChC,IAAIC,YAAY,GAAGH,UAAU,CAACC,iBAAiB,CAAC;EAEhD,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAACH,YAAY,EAAE;IAC1D,MAAM,IAAII,KAAK,CAAC,kGAAkG,CAAC;EACrH;EAEA,OAAOJ,YAAY;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}