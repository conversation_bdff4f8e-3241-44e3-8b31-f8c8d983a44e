{"ast": null, "code": "import { wrapMapToPropsConstant, wrapMapToPropsFunc } from './wrapMapToProps';\nexport function whenMapStateToPropsIsFunction(mapStateToProps) {\n  return typeof mapStateToProps === 'function' ? wrapMapToPropsFunc(mapStateToProps, 'mapStateToProps') : undefined;\n}\nexport function whenMapStateToPropsIsMissing(mapStateToProps) {\n  return !mapStateToProps ? wrapMapToPropsConstant(function () {\n    return {};\n  }) : undefined;\n}\nexport default [whenMapStateToPropsIsFunction, whenMapStateToPropsIsMissing];", "map": {"version": 3, "names": ["wrapMapToPropsConstant", "wrapMapToPropsFunc", "whenMapStateToPropsIsFunction", "mapStateToProps", "undefined", "whenMapStateToPropsIsMissing"], "sources": ["C:/ignition/ignition-ui/node_modules/react-beautiful-dnd/node_modules/react-redux/es/connect/mapStateToProps.js"], "sourcesContent": ["import { wrapMapToPropsConstant, wrapMapToPropsFunc } from './wrapMapToProps';\nexport function whenMapStateToPropsIsFunction(mapStateToProps) {\n  return typeof mapStateToProps === 'function' ? wrapMapToPropsFunc(mapStateToProps, 'mapStateToProps') : undefined;\n}\nexport function whenMapStateToPropsIsMissing(mapStateToProps) {\n  return !mapStateToProps ? wrapMapToPropsConstant(function () {\n    return {};\n  }) : undefined;\n}\nexport default [whenMapStateToPropsIsFunction, whenMapStateToPropsIsMissing];"], "mappings": "AAAA,SAASA,sBAAsB,EAAEC,kBAAkB,QAAQ,kBAAkB;AAC7E,OAAO,SAASC,6BAA6BA,CAACC,eAAe,EAAE;EAC7D,OAAO,OAAOA,eAAe,KAAK,UAAU,GAAGF,kBAAkB,CAACE,eAAe,EAAE,iBAAiB,CAAC,GAAGC,SAAS;AACnH;AACA,OAAO,SAASC,4BAA4BA,CAACF,eAAe,EAAE;EAC5D,OAAO,CAACA,eAAe,GAAGH,sBAAsB,CAAC,YAAY;IAC3D,OAAO,CAAC,CAAC;EACX,CAAC,CAAC,GAAGI,SAAS;AAChB;AACA,eAAe,CAACF,6BAA6B,EAAEG,4BAA4B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}