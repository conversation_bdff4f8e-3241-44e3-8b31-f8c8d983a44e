{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getCircularProgressUtilityClass(slot) {\n  return generateUtilityClass('MuiCircularProgress', slot);\n}\nconst circularProgressClasses = generateUtilityClasses('MuiCircularProgress', ['root', 'determinate', 'svg', 'track', 'progress', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'sizeSm', 'sizeMd', 'sizeLg', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid']);\nexport default circularProgressClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getCircularProgressUtilityClass", "slot", "circularProgressClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/CircularProgress/circularProgressClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getCircularProgressUtilityClass(slot) {\n  return generateUtilityClass('MuiCircularProgress', slot);\n}\nconst circularProgressClasses = generateUtilityClasses('MuiCircularProgress', ['root', 'determinate', 'svg', 'track', 'progress', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'sizeSm', 'sizeMd', 'sizeLg', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid']);\nexport default circularProgressClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,+BAA+BA,CAACC,IAAI,EAAE;EACpD,OAAOH,oBAAoB,CAAC,qBAAqB,EAAEG,IAAI,CAAC;AAC1D;AACA,MAAMC,uBAAuB,GAAGH,sBAAsB,CAAC,qBAAqB,EAAE,CAAC,MAAM,EAAE,aAAa,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,cAAc,EAAE,iBAAiB,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;AACjU,eAAeG,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}