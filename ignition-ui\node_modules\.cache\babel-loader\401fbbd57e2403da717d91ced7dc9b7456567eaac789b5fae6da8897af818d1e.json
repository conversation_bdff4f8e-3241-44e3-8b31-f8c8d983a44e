{"ast": null, "code": "'use strict';\n\nvar keys = require('object-keys');\nvar hasSymbols = typeof Symbol === 'function' && typeof Symbol('foo') === 'symbol';\nvar toStr = Object.prototype.toString;\nvar concat = Array.prototype.concat;\nvar defineDataProperty = require('define-data-property');\nvar isFunction = function (fn) {\n  return typeof fn === 'function' && toStr.call(fn) === '[object Function]';\n};\nvar supportsDescriptors = require('has-property-descriptors')();\nvar defineProperty = function (object, name, value, predicate) {\n  if (name in object) {\n    if (predicate === true) {\n      if (object[name] === value) {\n        return;\n      }\n    } else if (!isFunction(predicate) || !predicate()) {\n      return;\n    }\n  }\n  if (supportsDescriptors) {\n    defineDataProperty(object, name, value, true);\n  } else {\n    defineDataProperty(object, name, value);\n  }\n};\nvar defineProperties = function (object, map) {\n  var predicates = arguments.length > 2 ? arguments[2] : {};\n  var props = keys(map);\n  if (hasSymbols) {\n    props = concat.call(props, Object.getOwnPropertySymbols(map));\n  }\n  for (var i = 0; i < props.length; i += 1) {\n    defineProperty(object, props[i], map[props[i]], predicates[props[i]]);\n  }\n};\ndefineProperties.supportsDescriptors = !!supportsDescriptors;\nmodule.exports = defineProperties;", "map": {"version": 3, "names": ["keys", "require", "hasSymbols", "Symbol", "toStr", "Object", "prototype", "toString", "concat", "Array", "defineDataProperty", "isFunction", "fn", "call", "supportsDescriptors", "defineProperty", "object", "name", "value", "predicate", "defineProperties", "map", "predicates", "arguments", "length", "props", "getOwnPropertySymbols", "i", "module", "exports"], "sources": ["C:/ignition/ignition-ui/node_modules/define-properties/index.js"], "sourcesContent": ["'use strict';\n\nvar keys = require('object-keys');\nvar hasSymbols = typeof Symbol === 'function' && typeof Symbol('foo') === 'symbol';\n\nvar toStr = Object.prototype.toString;\nvar concat = Array.prototype.concat;\nvar defineDataProperty = require('define-data-property');\n\nvar isFunction = function (fn) {\n\treturn typeof fn === 'function' && toStr.call(fn) === '[object Function]';\n};\n\nvar supportsDescriptors = require('has-property-descriptors')();\n\nvar defineProperty = function (object, name, value, predicate) {\n\tif (name in object) {\n\t\tif (predicate === true) {\n\t\t\tif (object[name] === value) {\n\t\t\t\treturn;\n\t\t\t}\n\t\t} else if (!isFunction(predicate) || !predicate()) {\n\t\t\treturn;\n\t\t}\n\t}\n\n\tif (supportsDescriptors) {\n\t\tdefineDataProperty(object, name, value, true);\n\t} else {\n\t\tdefineDataProperty(object, name, value);\n\t}\n};\n\nvar defineProperties = function (object, map) {\n\tvar predicates = arguments.length > 2 ? arguments[2] : {};\n\tvar props = keys(map);\n\tif (hasSymbols) {\n\t\tprops = concat.call(props, Object.getOwnPropertySymbols(map));\n\t}\n\tfor (var i = 0; i < props.length; i += 1) {\n\t\tdefineProperty(object, props[i], map[props[i]], predicates[props[i]]);\n\t}\n};\n\ndefineProperties.supportsDescriptors = !!supportsDescriptors;\n\nmodule.exports = defineProperties;\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,IAAI,GAAGC,OAAO,CAAC,aAAa,CAAC;AACjC,IAAIC,UAAU,GAAG,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAAC,KAAK,CAAC,KAAK,QAAQ;AAElF,IAAIC,KAAK,GAAGC,MAAM,CAACC,SAAS,CAACC,QAAQ;AACrC,IAAIC,MAAM,GAAGC,KAAK,CAACH,SAAS,CAACE,MAAM;AACnC,IAAIE,kBAAkB,GAAGT,OAAO,CAAC,sBAAsB,CAAC;AAExD,IAAIU,UAAU,GAAG,SAAAA,CAAUC,EAAE,EAAE;EAC9B,OAAO,OAAOA,EAAE,KAAK,UAAU,IAAIR,KAAK,CAACS,IAAI,CAACD,EAAE,CAAC,KAAK,mBAAmB;AAC1E,CAAC;AAED,IAAIE,mBAAmB,GAAGb,OAAO,CAAC,0BAA0B,CAAC,CAAC,CAAC;AAE/D,IAAIc,cAAc,GAAG,SAAAA,CAAUC,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAEC,SAAS,EAAE;EAC9D,IAAIF,IAAI,IAAID,MAAM,EAAE;IACnB,IAAIG,SAAS,KAAK,IAAI,EAAE;MACvB,IAAIH,MAAM,CAACC,IAAI,CAAC,KAAKC,KAAK,EAAE;QAC3B;MACD;IACD,CAAC,MAAM,IAAI,CAACP,UAAU,CAACQ,SAAS,CAAC,IAAI,CAACA,SAAS,CAAC,CAAC,EAAE;MAClD;IACD;EACD;EAEA,IAAIL,mBAAmB,EAAE;IACxBJ,kBAAkB,CAACM,MAAM,EAAEC,IAAI,EAAEC,KAAK,EAAE,IAAI,CAAC;EAC9C,CAAC,MAAM;IACNR,kBAAkB,CAACM,MAAM,EAAEC,IAAI,EAAEC,KAAK,CAAC;EACxC;AACD,CAAC;AAED,IAAIE,gBAAgB,GAAG,SAAAA,CAAUJ,MAAM,EAAEK,GAAG,EAAE;EAC7C,IAAIC,UAAU,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,GAAGD,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACzD,IAAIE,KAAK,GAAGzB,IAAI,CAACqB,GAAG,CAAC;EACrB,IAAInB,UAAU,EAAE;IACfuB,KAAK,GAAGjB,MAAM,CAACK,IAAI,CAACY,KAAK,EAAEpB,MAAM,CAACqB,qBAAqB,CAACL,GAAG,CAAC,CAAC;EAC9D;EACA,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,CAACD,MAAM,EAAEG,CAAC,IAAI,CAAC,EAAE;IACzCZ,cAAc,CAACC,MAAM,EAAES,KAAK,CAACE,CAAC,CAAC,EAAEN,GAAG,CAACI,KAAK,CAACE,CAAC,CAAC,CAAC,EAAEL,UAAU,CAACG,KAAK,CAACE,CAAC,CAAC,CAAC,CAAC;EACtE;AACD,CAAC;AAEDP,gBAAgB,CAACN,mBAAmB,GAAG,CAAC,CAACA,mBAAmB;AAE5Dc,MAAM,CAACC,OAAO,GAAGT,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}