{"ast": null, "code": "'use client';\n\nexport { NoSsr } from './NoSsr';\nexport * from './NoSsr.types';", "map": {"version": 3, "names": ["NoSsr"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/NoSsr/index.js"], "sourcesContent": ["'use client';\n\nexport { NoSsr } from './NoSsr';\nexport * from './NoSsr.types';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,KAAK,QAAQ,SAAS;AAC/B,cAAc,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}