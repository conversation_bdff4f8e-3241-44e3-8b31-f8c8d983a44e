{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getTabUtilityClass(slot) {\n  return generateUtilityClass('MuiTab', slot);\n}\nconst tabListClasses = generateUtilityClasses('MuiTab', ['root', 'disabled', 'focusVisible', 'selected', 'horizontal', 'vertical', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid']);\nexport default tabListClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getTabUtilityClass", "slot", "tabListClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Tab/tabClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getTabUtilityClass(slot) {\n  return generateUtilityClass('MuiTab', slot);\n}\nconst tabListClasses = generateUtilityClasses('MuiTab', ['root', 'disabled', 'focusVisible', 'selected', 'horizontal', 'vertical', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid']);\nexport default tabListClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,kBAAkBA,CAACC,IAAI,EAAE;EACvC,OAAOH,oBAAoB,CAAC,QAAQ,EAAEG,IAAI,CAAC;AAC7C;AACA,MAAMC,cAAc,GAAGH,sBAAsB,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,YAAY,EAAE,UAAU,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,iBAAiB,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;AACpS,eAAeG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}