{"ast": null, "code": "'use strict';\n\nvar supportsDescriptors = require('define-properties').supportsDescriptors;\nvar getPolyfill = require('./polyfill');\nvar gOPD = require('gopd');\nvar defineProperty = Object.defineProperty;\nvar $TypeError = require('es-errors');\nvar getProto = require('get-proto');\nvar regex = /a/;\nmodule.exports = function shimFlags() {\n  if (!supportsDescriptors || !getProto) {\n    throw new $TypeError('RegExp.prototype.flags requires a true ES5 environment that supports property descriptors');\n  }\n  var polyfill = getPolyfill();\n  var proto = getProto(regex);\n  var descriptor = gOPD(proto, 'flags');\n  if (!descriptor || descriptor.get !== polyfill) {\n    defineProperty(proto, 'flags', {\n      configurable: true,\n      enumerable: false,\n      get: polyfill\n    });\n  }\n  return polyfill;\n};", "map": {"version": 3, "names": ["supportsDescriptors", "require", "getPolyfill", "gOPD", "defineProperty", "Object", "$TypeError", "getProto", "regex", "module", "exports", "shim<PERSON><PERSON>s", "polyfill", "proto", "descriptor", "get", "configurable", "enumerable"], "sources": ["C:/ignition/ignition-ui/node_modules/regexp.prototype.flags/shim.js"], "sourcesContent": ["'use strict';\n\nvar supportsDescriptors = require('define-properties').supportsDescriptors;\nvar getPolyfill = require('./polyfill');\nvar gOPD = require('gopd');\nvar defineProperty = Object.defineProperty;\nvar $TypeError = require('es-errors');\nvar getProto = require('get-proto');\nvar regex = /a/;\n\nmodule.exports = function shimFlags() {\n\tif (!supportsDescriptors || !getProto) {\n\t\tthrow new $TypeError('RegExp.prototype.flags requires a true ES5 environment that supports property descriptors');\n\t}\n\tvar polyfill = getPolyfill();\n\tvar proto = getProto(regex);\n\tvar descriptor = gOPD(proto, 'flags');\n\tif (!descriptor || descriptor.get !== polyfill) {\n\t\tdefineProperty(proto, 'flags', {\n\t\t\tconfigurable: true,\n\t\t\tenumerable: false,\n\t\t\tget: polyfill\n\t\t});\n\t}\n\treturn polyfill;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,mBAAmB,GAAGC,OAAO,CAAC,mBAAmB,CAAC,CAACD,mBAAmB;AAC1E,IAAIE,WAAW,GAAGD,OAAO,CAAC,YAAY,CAAC;AACvC,IAAIE,IAAI,GAAGF,OAAO,CAAC,MAAM,CAAC;AAC1B,IAAIG,cAAc,GAAGC,MAAM,CAACD,cAAc;AAC1C,IAAIE,UAAU,GAAGL,OAAO,CAAC,WAAW,CAAC;AACrC,IAAIM,QAAQ,GAAGN,OAAO,CAAC,WAAW,CAAC;AACnC,IAAIO,KAAK,GAAG,GAAG;AAEfC,MAAM,CAACC,OAAO,GAAG,SAASC,SAASA,CAAA,EAAG;EACrC,IAAI,CAACX,mBAAmB,IAAI,CAACO,QAAQ,EAAE;IACtC,MAAM,IAAID,UAAU,CAAC,2FAA2F,CAAC;EAClH;EACA,IAAIM,QAAQ,GAAGV,WAAW,CAAC,CAAC;EAC5B,IAAIW,KAAK,GAAGN,QAAQ,CAACC,KAAK,CAAC;EAC3B,IAAIM,UAAU,GAAGX,IAAI,CAACU,KAAK,EAAE,OAAO,CAAC;EACrC,IAAI,CAACC,UAAU,IAAIA,UAAU,CAACC,GAAG,KAAKH,QAAQ,EAAE;IAC/CR,cAAc,CAACS,KAAK,EAAE,OAAO,EAAE;MAC9BG,YAAY,EAAE,IAAI;MAClBC,UAAU,EAAE,KAAK;MACjBF,GAAG,EAAEH;IACN,CAAC,CAAC;EACH;EACA,OAAOA,QAAQ;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}