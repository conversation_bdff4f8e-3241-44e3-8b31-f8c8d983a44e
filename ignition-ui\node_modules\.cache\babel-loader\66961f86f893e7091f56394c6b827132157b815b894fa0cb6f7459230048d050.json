{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getContainerUtilityClass(slot) {\n  return generateUtilityClass('MuiContainer', slot);\n}\nconst containerClasses = generateUtilityClasses('MuiContainer', ['root', 'disableGutters', 'fixed', 'maxWidthXs', 'maxWidthSm', 'maxWidthMd', 'maxWidthLg', 'maxWidthXl']);\nexport default containerClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getContainerUtilityClass", "slot", "containerClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Container/containerClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getContainerUtilityClass(slot) {\n  return generateUtilityClass('MuiContainer', slot);\n}\nconst containerClasses = generateUtilityClasses('MuiContainer', ['root', 'disableGutters', 'fixed', 'maxWidthXs', 'maxWidthSm', 'maxWidthMd', 'maxWidthLg', 'maxWidthXl']);\nexport default containerClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,wBAAwBA,CAACC,IAAI,EAAE;EAC7C,OAAOH,oBAAoB,CAAC,cAAc,EAAEG,IAAI,CAAC;AACnD;AACA,MAAMC,gBAAgB,GAAGH,sBAAsB,CAAC,cAAc,EAAE,CAAC,MAAM,EAAE,gBAAgB,EAAE,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;AAC1K,eAAeG,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}