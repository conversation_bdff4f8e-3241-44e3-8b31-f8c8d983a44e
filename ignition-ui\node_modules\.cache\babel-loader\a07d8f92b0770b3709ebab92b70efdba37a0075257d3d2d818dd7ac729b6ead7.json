{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getToggleButtonGroupUtilityClass(slot) {\n  return generateUtilityClass('MuiToggleButtonGroup', slot);\n}\nconst toggleButtonGroupClasses = generateUtilityClasses('MuiToggleButtonGroup', ['root', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid', 'sizeSm', 'sizeMd', 'sizeLg', 'horizontal', 'vertical']);\nexport default toggleButtonGroupClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getToggleButtonGroupUtilityClass", "slot", "toggleButtonGroupClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/ToggleButtonGroup/toggleButtonGroupClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getToggleButtonGroupUtilityClass(slot) {\n  return generateUtilityClass('MuiToggleButtonGroup', slot);\n}\nconst toggleButtonGroupClasses = generateUtilityClasses('MuiToggleButtonGroup', ['root', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid', 'sizeSm', 'sizeMd', 'sizeLg', 'horizontal', 'vertical']);\nexport default toggleButtonGroupClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,gCAAgCA,CAACC,IAAI,EAAE;EACrD,OAAOH,oBAAoB,CAAC,sBAAsB,EAAEG,IAAI,CAAC;AAC3D;AACA,MAAMC,wBAAwB,GAAGH,sBAAsB,CAAC,sBAAsB,EAAE,CAAC,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,iBAAiB,EAAE,aAAa,EAAE,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,EAAE,UAAU,CAAC,CAAC;AAClT,eAAeG,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}