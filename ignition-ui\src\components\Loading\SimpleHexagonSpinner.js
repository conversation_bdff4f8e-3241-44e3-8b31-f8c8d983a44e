import React, { useEffect, useRef } from 'react';
import { Box } from '@mui/material';

const SimpleHexagonSpinner = ({ size = 200 }) => {
  const canvasRef = useRef(null);
  const animationRef = useRef(null);

  useEffect(() => {
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    
    // Set canvas size
    canvas.width = size;
    canvas.height = size;
    
    const centerX = size / 2;
    const centerY = size / 2;
    const hexRadius = size * 0.35;
    
    // Ball physics
    let ballX = centerX;
    let ballY = centerY;
    let ballVelX = 2;
    let ballVelY = 0;
    const ballRadius = 8;
    const gravity = 0.3;
    const bounce = 0.8;
    
    // Hexagon rotation
    let hexRotation = 0;
    
    // Create hexagon vertices
    const getHexagonVertices = (rotation) => {
      const vertices = [];
      for (let i = 0; i < 6; i++) {
        const angle = (Math.PI / 3) * i + rotation;
        vertices.push({
          x: centerX + hexRadius * Math.cos(angle),
          y: centerY + hexRadius * Math.sin(angle)
        });
      }
      return vertices;
    };
    
    // Check if point is inside hexagon
    const isInsideHexagon = (x, y, vertices) => {
      let inside = true;
      for (let i = 0; i < 6; i++) {
        const j = (i + 1) % 6;
        const v1 = vertices[i];
        const v2 = vertices[j];
        
        // Calculate cross product to determine which side of edge the point is on
        const cross = (v2.x - v1.x) * (y - v1.y) - (v2.y - v1.y) * (x - v1.x);
        if (cross > 0) {
          inside = false;
          break;
        }
      }
      return inside;
    };
    
    // Get closest point on hexagon edge
    const getClosestPointOnEdge = (x, y, vertices) => {
      let closestPoint = { x, y };
      let minDistance = Infinity;
      
      for (let i = 0; i < 6; i++) {
        const j = (i + 1) % 6;
        const v1 = vertices[i];
        const v2 = vertices[j];
        
        // Project point onto line segment
        const A = x - v1.x;
        const B = y - v1.y;
        const C = v2.x - v1.x;
        const D = v2.y - v1.y;
        
        const dot = A * C + B * D;
        const lenSq = C * C + D * D;
        let param = -1;
        if (lenSq !== 0) param = dot / lenSq;
        
        let xx, yy;
        if (param < 0) {
          xx = v1.x;
          yy = v1.y;
        } else if (param > 1) {
          xx = v2.x;
          yy = v2.y;
        } else {
          xx = v1.x + param * C;
          yy = v1.y + param * D;
        }
        
        const distance = Math.sqrt((x - xx) * (x - xx) + (y - yy) * (y - yy));
        if (distance < minDistance) {
          minDistance = distance;
          closestPoint = { x: xx, y: yy };
        }
      }
      
      return closestPoint;
    };
    
    const animate = () => {
      // Clear canvas
      ctx.clearRect(0, 0, size, size);
      
      // Update hexagon rotation
      hexRotation += 0.02;
      
      // Get current hexagon vertices
      const vertices = getHexagonVertices(hexRotation);
      
      // Apply gravity to ball
      ballVelY += gravity;
      
      // Update ball position
      ballX += ballVelX;
      ballY += ballVelY;
      
      // Check collision with hexagon
      if (!isInsideHexagon(ballX, ballY, vertices)) {
        const closestPoint = getClosestPointOnEdge(ballX, ballY, vertices);
        
        // Calculate normal vector
        const normalX = ballX - closestPoint.x;
        const normalY = ballY - closestPoint.y;
        const normalLength = Math.sqrt(normalX * normalX + normalY * normalY);
        
        if (normalLength > 0) {
          const unitNormalX = normalX / normalLength;
          const unitNormalY = normalY / normalLength;
          
          // Move ball back inside
          ballX = closestPoint.x + unitNormalX * ballRadius;
          ballY = closestPoint.y + unitNormalY * ballRadius;
          
          // Reflect velocity
          const dotProduct = ballVelX * unitNormalX + ballVelY * unitNormalY;
          ballVelX = (ballVelX - 2 * dotProduct * unitNormalX) * bounce;
          ballVelY = (ballVelY - 2 * dotProduct * unitNormalY) * bounce;
        }
      }
      
      // Draw hexagon
      ctx.beginPath();
      ctx.moveTo(vertices[0].x, vertices[0].y);
      for (let i = 1; i < 6; i++) {
        ctx.lineTo(vertices[i].x, vertices[i].y);
      }
      ctx.closePath();
      ctx.strokeStyle = '#FFFFFF';
      ctx.lineWidth = 2;
      ctx.stroke();
      
      // Draw ball
      ctx.beginPath();
      ctx.arc(ballX, ballY, ballRadius, 0, Math.PI * 2);
      ctx.fillStyle = '#F0A500';
      ctx.fill();
      
      animationRef.current = requestAnimationFrame(animate);
    };
    
    animate();
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [size]);

  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        width: size,
        height: size,
      }}
    >
      <canvas
        ref={canvasRef}
        style={{
          backgroundColor: 'transparent',
        }}
      />
    </Box>
  );
};

export default SimpleHexagonSpinner;
