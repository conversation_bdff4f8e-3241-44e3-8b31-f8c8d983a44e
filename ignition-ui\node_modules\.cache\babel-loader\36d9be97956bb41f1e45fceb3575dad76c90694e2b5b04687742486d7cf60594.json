{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getDividerUtilityClass(slot) {\n  return generateUtilityClass('MuiDivider', slot);\n}\nconst dividerClasses = generateUtilityClasses('MuiDivider', ['root', 'horizontal', 'vertical', 'insetContext', 'insetNone']);\nexport default dividerClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getDividerUtilityClass", "slot", "dividerClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Divider/dividerClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getDividerUtilityClass(slot) {\n  return generateUtilityClass('MuiDivider', slot);\n}\nconst dividerClasses = generateUtilityClasses('MuiDivider', ['root', 'horizontal', 'vertical', 'insetContext', 'insetNone']);\nexport default dividerClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,sBAAsBA,CAACC,IAAI,EAAE;EAC3C,OAAOH,oBAAoB,CAAC,YAAY,EAAEG,IAAI,CAAC;AACjD;AACA,MAAMC,cAAc,GAAGH,sBAAsB,CAAC,YAAY,EAAE,CAAC,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,cAAc,EAAE,WAAW,CAAC,CAAC;AAC5H,eAAeG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}