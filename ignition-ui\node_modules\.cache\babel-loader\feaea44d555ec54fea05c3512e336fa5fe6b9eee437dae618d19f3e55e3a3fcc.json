{"ast": null, "code": "'use strict';\n\nexports.__esModule = true;\nvar _react = require('react');\nvar _react2 = _interopRequireDefault(_react);\nvar _implementation = require('./implementation');\nvar _implementation2 = _interopRequireDefault(_implementation);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nexports.default = _react2.default.createContext || _implementation2.default;\nmodule.exports = exports['default'];", "map": {"version": 3, "names": ["exports", "__esModule", "_react", "require", "_react2", "_interopRequireDefault", "_implementation", "_implementation2", "obj", "default", "createContext", "module"], "sources": ["C:/ignition/ignition-ui/node_modules/@hypnosphi/create-react-context/lib/index.js"], "sourcesContent": ["'use strict';\n\nexports.__esModule = true;\n\nvar _react = require('react');\n\nvar _react2 = _interopRequireDefault(_react);\n\nvar _implementation = require('./implementation');\n\nvar _implementation2 = _interopRequireDefault(_implementation);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nexports.default = _react2.default.createContext || _implementation2.default;\nmodule.exports = exports['default'];"], "mappings": "AAAA,YAAY;;AAEZA,OAAO,CAACC,UAAU,GAAG,IAAI;AAEzB,IAAIC,MAAM,GAAGC,OAAO,CAAC,OAAO,CAAC;AAE7B,IAAIC,OAAO,GAAGC,sBAAsB,CAACH,MAAM,CAAC;AAE5C,IAAII,eAAe,GAAGH,OAAO,CAAC,kBAAkB,CAAC;AAEjD,IAAII,gBAAgB,GAAGF,sBAAsB,CAACC,eAAe,CAAC;AAE9D,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACP,UAAU,GAAGO,GAAG,GAAG;IAAEC,OAAO,EAAED;EAAI,CAAC;AAAE;AAE9FR,OAAO,CAACS,OAAO,GAAGL,OAAO,CAACK,OAAO,CAACC,aAAa,IAAIH,gBAAgB,CAACE,OAAO;AAC3EE,MAAM,CAACX,OAAO,GAAGA,OAAO,CAAC,SAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}