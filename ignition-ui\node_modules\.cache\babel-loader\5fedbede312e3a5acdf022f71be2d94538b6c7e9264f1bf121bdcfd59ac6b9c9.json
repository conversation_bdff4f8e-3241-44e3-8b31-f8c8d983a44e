{"ast": null, "code": "import verifyPlainObject from '../utils/verifyPlainObject';\nexport function wrapMapToPropsConstant(getConstant) {\n  return function initConstantSelector(dispatch, options) {\n    var constant = getConstant(dispatch, options);\n    function constantSelector() {\n      return constant;\n    }\n    constantSelector.dependsOnOwnProps = false;\n    return constantSelector;\n  };\n} // dependsOnOwnProps is used by createMapToPropsProxy to determine whether to pass props as args\n// to the mapToProps function being wrapped. It is also used by makePurePropsSelector to determine\n// whether mapToProps needs to be invoked when props have changed.\n//\n// A length of one signals that mapToProps does not depend on props from the parent component.\n// A length of zero is assumed to mean mapToProps is getting args via arguments or ...args and\n// therefore not reporting its length accurately..\n\nexport function getDependsOnOwnProps(mapToProps) {\n  return mapToProps.dependsOnOwnProps !== null && mapToProps.dependsOnOwnProps !== undefined ? Boolean(mapToProps.dependsOnOwnProps) : mapToProps.length !== 1;\n} // Used by whenMapStateToPropsIsFunction and whenMapDispatchToPropsIsFunction,\n// this function wraps mapToProps in a proxy function which does several things:\n//\n//  * Detects whether the mapToProps function being called depends on props, which\n//    is used by selectorFactory to decide if it should reinvoke on props changes.\n//\n//  * On first call, handles mapToProps if returns another function, and treats that\n//    new function as the true mapToProps for subsequent calls.\n//\n//  * On first call, verifies the first result is a plain object, in order to warn\n//    the developer that their mapToProps function is not returning a valid result.\n//\n\nexport function wrapMapToPropsFunc(mapToProps, methodName) {\n  return function initProxySelector(dispatch, _ref) {\n    var displayName = _ref.displayName;\n    var proxy = function mapToPropsProxy(stateOrDispatch, ownProps) {\n      return proxy.dependsOnOwnProps ? proxy.mapToProps(stateOrDispatch, ownProps) : proxy.mapToProps(stateOrDispatch);\n    }; // allow detectFactoryAndVerify to get ownProps\n\n    proxy.dependsOnOwnProps = true;\n    proxy.mapToProps = function detectFactoryAndVerify(stateOrDispatch, ownProps) {\n      proxy.mapToProps = mapToProps;\n      proxy.dependsOnOwnProps = getDependsOnOwnProps(mapToProps);\n      var props = proxy(stateOrDispatch, ownProps);\n      if (typeof props === 'function') {\n        proxy.mapToProps = props;\n        proxy.dependsOnOwnProps = getDependsOnOwnProps(props);\n        props = proxy(stateOrDispatch, ownProps);\n      }\n      if (process.env.NODE_ENV !== 'production') verifyPlainObject(props, displayName, methodName);\n      return props;\n    };\n    return proxy;\n  };\n}", "map": {"version": 3, "names": ["verifyPlainObject", "wrapMapToPropsConstant", "getConstant", "initConstantSelector", "dispatch", "options", "constant", "constantSelector", "dependsOnOwnProps", "getDependsOnOwnProps", "mapToProps", "undefined", "Boolean", "length", "wrapMapToPropsFunc", "methodName", "initProxySelector", "_ref", "displayName", "proxy", "mapToPropsProxy", "stateOrDispatch", "ownProps", "detectFactoryAndVerify", "props", "process", "env", "NODE_ENV"], "sources": ["C:/ignition/ignition-ui/node_modules/react-beautiful-dnd/node_modules/react-redux/es/connect/wrapMapToProps.js"], "sourcesContent": ["import verifyPlainObject from '../utils/verifyPlainObject';\nexport function wrapMapToPropsConstant(getConstant) {\n  return function initConstantSelector(dispatch, options) {\n    var constant = getConstant(dispatch, options);\n\n    function constantSelector() {\n      return constant;\n    }\n\n    constantSelector.dependsOnOwnProps = false;\n    return constantSelector;\n  };\n} // dependsOnOwnProps is used by createMapToPropsProxy to determine whether to pass props as args\n// to the mapToProps function being wrapped. It is also used by makePurePropsSelector to determine\n// whether mapToProps needs to be invoked when props have changed.\n//\n// A length of one signals that mapToProps does not depend on props from the parent component.\n// A length of zero is assumed to mean mapToProps is getting args via arguments or ...args and\n// therefore not reporting its length accurately..\n\nexport function getDependsOnOwnProps(mapToProps) {\n  return mapToProps.dependsOnOwnProps !== null && mapToProps.dependsOnOwnProps !== undefined ? Boolean(mapToProps.dependsOnOwnProps) : mapToProps.length !== 1;\n} // Used by whenMapStateToPropsIsFunction and whenMapDispatchToPropsIsFunction,\n// this function wraps mapToProps in a proxy function which does several things:\n//\n//  * Detects whether the mapToProps function being called depends on props, which\n//    is used by selectorFactory to decide if it should reinvoke on props changes.\n//\n//  * On first call, handles mapToProps if returns another function, and treats that\n//    new function as the true mapToProps for subsequent calls.\n//\n//  * On first call, verifies the first result is a plain object, in order to warn\n//    the developer that their mapToProps function is not returning a valid result.\n//\n\nexport function wrapMapToPropsFunc(mapToProps, methodName) {\n  return function initProxySelector(dispatch, _ref) {\n    var displayName = _ref.displayName;\n\n    var proxy = function mapToPropsProxy(stateOrDispatch, ownProps) {\n      return proxy.dependsOnOwnProps ? proxy.mapToProps(stateOrDispatch, ownProps) : proxy.mapToProps(stateOrDispatch);\n    }; // allow detectFactoryAndVerify to get ownProps\n\n\n    proxy.dependsOnOwnProps = true;\n\n    proxy.mapToProps = function detectFactoryAndVerify(stateOrDispatch, ownProps) {\n      proxy.mapToProps = mapToProps;\n      proxy.dependsOnOwnProps = getDependsOnOwnProps(mapToProps);\n      var props = proxy(stateOrDispatch, ownProps);\n\n      if (typeof props === 'function') {\n        proxy.mapToProps = props;\n        proxy.dependsOnOwnProps = getDependsOnOwnProps(props);\n        props = proxy(stateOrDispatch, ownProps);\n      }\n\n      if (process.env.NODE_ENV !== 'production') verifyPlainObject(props, displayName, methodName);\n      return props;\n    };\n\n    return proxy;\n  };\n}"], "mappings": "AAAA,OAAOA,iBAAiB,MAAM,4BAA4B;AAC1D,OAAO,SAASC,sBAAsBA,CAACC,WAAW,EAAE;EAClD,OAAO,SAASC,oBAAoBA,CAACC,QAAQ,EAAEC,OAAO,EAAE;IACtD,IAAIC,QAAQ,GAAGJ,WAAW,CAACE,QAAQ,EAAEC,OAAO,CAAC;IAE7C,SAASE,gBAAgBA,CAAA,EAAG;MAC1B,OAAOD,QAAQ;IACjB;IAEAC,gBAAgB,CAACC,iBAAiB,GAAG,KAAK;IAC1C,OAAOD,gBAAgB;EACzB,CAAC;AACH,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASE,oBAAoBA,CAACC,UAAU,EAAE;EAC/C,OAAOA,UAAU,CAACF,iBAAiB,KAAK,IAAI,IAAIE,UAAU,CAACF,iBAAiB,KAAKG,SAAS,GAAGC,OAAO,CAACF,UAAU,CAACF,iBAAiB,CAAC,GAAGE,UAAU,CAACG,MAAM,KAAK,CAAC;AAC9J,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,SAASC,kBAAkBA,CAACJ,UAAU,EAAEK,UAAU,EAAE;EACzD,OAAO,SAASC,iBAAiBA,CAACZ,QAAQ,EAAEa,IAAI,EAAE;IAChD,IAAIC,WAAW,GAAGD,IAAI,CAACC,WAAW;IAElC,IAAIC,KAAK,GAAG,SAASC,eAAeA,CAACC,eAAe,EAAEC,QAAQ,EAAE;MAC9D,OAAOH,KAAK,CAACX,iBAAiB,GAAGW,KAAK,CAACT,UAAU,CAACW,eAAe,EAAEC,QAAQ,CAAC,GAAGH,KAAK,CAACT,UAAU,CAACW,eAAe,CAAC;IAClH,CAAC,CAAC,CAAC;;IAGHF,KAAK,CAACX,iBAAiB,GAAG,IAAI;IAE9BW,KAAK,CAACT,UAAU,GAAG,SAASa,sBAAsBA,CAACF,eAAe,EAAEC,QAAQ,EAAE;MAC5EH,KAAK,CAACT,UAAU,GAAGA,UAAU;MAC7BS,KAAK,CAACX,iBAAiB,GAAGC,oBAAoB,CAACC,UAAU,CAAC;MAC1D,IAAIc,KAAK,GAAGL,KAAK,CAACE,eAAe,EAAEC,QAAQ,CAAC;MAE5C,IAAI,OAAOE,KAAK,KAAK,UAAU,EAAE;QAC/BL,KAAK,CAACT,UAAU,GAAGc,KAAK;QACxBL,KAAK,CAACX,iBAAiB,GAAGC,oBAAoB,CAACe,KAAK,CAAC;QACrDA,KAAK,GAAGL,KAAK,CAACE,eAAe,EAAEC,QAAQ,CAAC;MAC1C;MAEA,IAAIG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE3B,iBAAiB,CAACwB,KAAK,EAAEN,WAAW,EAAEH,UAAU,CAAC;MAC5F,OAAOS,KAAK;IACd,CAAC;IAED,OAAOL,KAAK;EACd,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}