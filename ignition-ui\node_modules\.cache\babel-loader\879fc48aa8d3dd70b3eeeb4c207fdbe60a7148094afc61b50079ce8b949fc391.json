{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getCheckboxUtilityClass(slot) {\n  return generateUtilityClass('MuiCheckbox', slot);\n}\nconst checkboxClasses = generateUtilityClasses('MuiCheckbox', ['root', 'checkbox', 'action', 'input', 'label', 'checked', 'disabled', 'focusVisible', 'indeterminate', 'colorPrimary', 'colorDanger', 'colorNeutral', 'colorSuccess', 'colorWarning', 'colorContext', 'sizeSm', 'sizeMd', 'sizeLg', 'variantOutlined', 'variantSoft', 'variantSolid']);\nexport default checkboxClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getCheckboxUtilityClass", "slot", "checkboxClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Checkbox/checkboxClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getCheckboxUtilityClass(slot) {\n  return generateUtilityClass('MuiCheckbox', slot);\n}\nconst checkboxClasses = generateUtilityClasses('MuiCheckbox', ['root', 'checkbox', 'action', 'input', 'label', 'checked', 'disabled', 'focusVisible', 'indeterminate', 'colorPrimary', 'colorDanger', 'colorNeutral', 'colorSuccess', 'colorWarning', 'colorContext', 'sizeSm', 'sizeMd', 'sizeLg', 'variantOutlined', 'variantSoft', 'variantSolid']);\nexport default checkboxClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,uBAAuBA,CAACC,IAAI,EAAE;EAC5C,OAAOH,oBAAoB,CAAC,aAAa,EAAEG,IAAI,CAAC;AAClD;AACA,MAAMC,eAAe,GAAGH,sBAAsB,CAAC,aAAa,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,eAAe,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,iBAAiB,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;AACtV,eAAeG,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}