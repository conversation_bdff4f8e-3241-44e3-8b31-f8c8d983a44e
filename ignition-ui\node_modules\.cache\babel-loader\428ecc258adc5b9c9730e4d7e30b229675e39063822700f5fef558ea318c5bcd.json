{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getScopedCssBaselineUtilityClass(slot) {\n  return generateUtilityClass('MuiScopedCssBaseline', slot);\n}\nconst scopedCssBaselineClasses = generateUtilityClasses('MuiScopedCssBaseline', ['root']);\nexport default scopedCssBaselineClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getScopedCssBaselineUtilityClass", "slot", "scopedCssBaselineClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/ScopedCssBaseline/scopedCssBaselineClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getScopedCssBaselineUtilityClass(slot) {\n  return generateUtilityClass('MuiScopedCssBaseline', slot);\n}\nconst scopedCssBaselineClasses = generateUtilityClasses('MuiScopedCssBaseline', ['root']);\nexport default scopedCssBaselineClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,gCAAgCA,CAACC,IAAI,EAAE;EACrD,OAAOH,oBAAoB,CAAC,sBAAsB,EAAEG,IAAI,CAAC;AAC3D;AACA,MAAMC,wBAAwB,GAAGH,sBAAsB,CAAC,sBAAsB,EAAE,CAAC,MAAM,CAAC,CAAC;AACzF,eAAeG,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}