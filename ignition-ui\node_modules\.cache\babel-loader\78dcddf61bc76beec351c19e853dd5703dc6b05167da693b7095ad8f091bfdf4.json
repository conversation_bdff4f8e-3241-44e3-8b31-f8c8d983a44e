{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"component\", \"color\", \"children\", \"variant\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport { unstable_capitalize as capitalize } from '@mui/utils';\nimport { useThemeProps } from '../styles';\nimport styled from '../styles/styled';\nimport { getStepIndicatorUtilityClass } from './stepIndicatorClasses';\nimport useSlot from '../utils/useSlot';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    color,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', color && `color${capitalize(color)}`, variant && `variant${capitalize(variant)}`]\n  };\n  return composeClasses(slots, getStepIndicatorUtilityClass, {});\n};\nconst StepIndicatorRoot = styled('div', {\n  name: 'JoyStepIndicator',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(_ref => {\n  let {\n    theme,\n    ownerState\n  } = _ref;\n  var _theme$variants;\n  return _extends({\n    '--Icon-fontSize': 'calc(var(--StepIndicator-size, 2rem) / 2)',\n    '--Icon-color': 'currentColor',\n    boxSizing: 'border-box',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    font: 'inherit',\n    borderRadius: '50%',\n    width: 'var(--StepIndicator-size, 1.5rem)',\n    height: 'var(--StepIndicator-size, 1.5rem)'\n  }, (_theme$variants = theme.variants[ownerState.variant]) == null ? void 0 : _theme$variants[ownerState.color]);\n});\n\n/**\n *\n * Demos:\n *\n * - [Stepper](https://mui.com/joy-ui/react-stepper/)\n *\n * API:\n *\n * - [StepIndicator API](https://mui.com/joy-ui/api/step-indicator/)\n */\nconst StepIndicator = /*#__PURE__*/React.forwardRef(function StepIndicator(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyStepIndicator'\n  });\n  const {\n      className,\n      component = 'div',\n      color = 'neutral',\n      children,\n      variant = 'soft',\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    component,\n    color,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    className: clsx(classes.root, className),\n    elementType: StepIndicatorRoot,\n    externalForwardedProps,\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(SlotRoot, _extends({}, rootProps, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? StepIndicator.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Used to render icon or text elements inside the StepIndicator if `src` is not set.\n   * This can be an element, or just a string.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component. It supports those theme colors that make sense for this component.\n   * @default 'neutral'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['danger', 'neutral', 'primary', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The [global variant](https://mui.com/joy-ui/main-features/global-variants/) to use.\n   * @default 'soft'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['outlined', 'plain', 'soft', 'solid']), PropTypes.string])\n} : void 0;\nexport default StepIndicator;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "clsx", "PropTypes", "unstable_composeClasses", "composeClasses", "unstable_capitalize", "capitalize", "useThemeProps", "styled", "getStepIndicatorUtilityClass", "useSlot", "jsx", "_jsx", "useUtilityClasses", "ownerState", "color", "variant", "slots", "root", "StepIndicatorRoot", "name", "slot", "overridesResolver", "props", "styles", "_ref", "theme", "_theme$variants", "boxSizing", "display", "alignItems", "justifyContent", "font", "borderRadius", "width", "height", "variants", "StepIndicator", "forwardRef", "inProps", "ref", "className", "component", "children", "slotProps", "other", "classes", "externalForwardedProps", "SlotRoot", "rootProps", "elementType", "process", "env", "NODE_ENV", "propTypes", "node", "string", "oneOfType", "oneOf", "shape", "func", "object", "sx", "arrayOf", "bool"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/StepIndicator/StepIndicator.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"component\", \"color\", \"children\", \"variant\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport { unstable_capitalize as capitalize } from '@mui/utils';\nimport { useThemeProps } from '../styles';\nimport styled from '../styles/styled';\nimport { getStepIndicatorUtilityClass } from './stepIndicatorClasses';\nimport useSlot from '../utils/useSlot';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    color,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', color && `color${capitalize(color)}`, variant && `variant${capitalize(variant)}`]\n  };\n  return composeClasses(slots, getStepIndicatorUtilityClass, {});\n};\nconst StepIndicatorRoot = styled('div', {\n  name: 'JoyStepIndicator',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(({\n  theme,\n  ownerState\n}) => {\n  var _theme$variants;\n  return _extends({\n    '--Icon-fontSize': 'calc(var(--StepIndicator-size, 2rem) / 2)',\n    '--Icon-color': 'currentColor',\n    boxSizing: 'border-box',\n    display: 'flex',\n    alignItems: 'center',\n    justifyContent: 'center',\n    font: 'inherit',\n    borderRadius: '50%',\n    width: 'var(--StepIndicator-size, 1.5rem)',\n    height: 'var(--StepIndicator-size, 1.5rem)'\n  }, (_theme$variants = theme.variants[ownerState.variant]) == null ? void 0 : _theme$variants[ownerState.color]);\n});\n\n/**\n *\n * Demos:\n *\n * - [Stepper](https://mui.com/joy-ui/react-stepper/)\n *\n * API:\n *\n * - [StepIndicator API](https://mui.com/joy-ui/api/step-indicator/)\n */\nconst StepIndicator = /*#__PURE__*/React.forwardRef(function StepIndicator(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyStepIndicator'\n  });\n  const {\n      className,\n      component = 'div',\n      color = 'neutral',\n      children,\n      variant = 'soft',\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    component,\n    color,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    className: clsx(classes.root, className),\n    elementType: StepIndicatorRoot,\n    externalForwardedProps,\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(SlotRoot, _extends({}, rootProps, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? StepIndicator.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Used to render icon or text elements inside the StepIndicator if `src` is not set.\n   * This can be an element, or just a string.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component. It supports those theme colors that make sense for this component.\n   * @default 'neutral'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['danger', 'neutral', 'primary', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The [global variant](https://mui.com/joy-ui/main-features/global-variants/) to use.\n   * @default 'soft'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['outlined', 'plain', 'soft', 'solid']), PropTypes.string])\n} : void 0;\nexport default StepIndicator;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,WAAW,CAAC;AAClG,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,WAAW;AACrE,SAASC,mBAAmB,IAAIC,UAAU,QAAQ,YAAY;AAC9D,SAASC,aAAa,QAAQ,WAAW;AACzC,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,4BAA4B,QAAQ,wBAAwB;AACrE,OAAOC,OAAO,MAAM,kBAAkB;AACtC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,KAAK;IACLC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEH,KAAK,IAAI,QAAQT,UAAU,CAACS,KAAK,CAAC,EAAE,EAAEC,OAAO,IAAI,UAAUV,UAAU,CAACU,OAAO,CAAC,EAAE;EACjG,CAAC;EACD,OAAOZ,cAAc,CAACa,KAAK,EAAER,4BAA4B,EAAE,CAAC,CAAC,CAAC;AAChE,CAAC;AACD,MAAMU,iBAAiB,GAAGX,MAAM,CAAC,KAAK,EAAE;EACtCY,IAAI,EAAE,kBAAkB;EACxBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC/C,CAAC,CAAC,CAACO,IAAA,IAGG;EAAA,IAHF;IACFC,KAAK;IACLZ;EACF,CAAC,GAAAW,IAAA;EACC,IAAIE,eAAe;EACnB,OAAO7B,QAAQ,CAAC;IACd,iBAAiB,EAAE,2CAA2C;IAC9D,cAAc,EAAE,cAAc;IAC9B8B,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE,MAAM;IACfC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,IAAI,EAAE,SAAS;IACfC,YAAY,EAAE,KAAK;IACnBC,KAAK,EAAE,mCAAmC;IAC1CC,MAAM,EAAE;EACV,CAAC,EAAE,CAACR,eAAe,GAAGD,KAAK,CAACU,QAAQ,CAACtB,UAAU,CAACE,OAAO,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGW,eAAe,CAACb,UAAU,CAACC,KAAK,CAAC,CAAC;AACjH,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsB,aAAa,GAAG,aAAarC,KAAK,CAACsC,UAAU,CAAC,SAASD,aAAaA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACvF,MAAMjB,KAAK,GAAGhB,aAAa,CAAC;IAC1BgB,KAAK,EAAEgB,OAAO;IACdnB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFqB,SAAS;MACTC,SAAS,GAAG,KAAK;MACjB3B,KAAK,GAAG,SAAS;MACjB4B,QAAQ;MACR3B,OAAO,GAAG,MAAM;MAChBC,KAAK,GAAG,CAAC,CAAC;MACV2B,SAAS,GAAG,CAAC;IACf,CAAC,GAAGrB,KAAK;IACTsB,KAAK,GAAGhD,6BAA6B,CAAC0B,KAAK,EAAExB,SAAS,CAAC;EACzD,MAAMe,UAAU,GAAGhB,QAAQ,CAAC,CAAC,CAAC,EAAEyB,KAAK,EAAE;IACrCmB,SAAS;IACT3B,KAAK;IACLC;EACF,CAAC,CAAC;EACF,MAAM8B,OAAO,GAAGjC,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMiC,sBAAsB,GAAGjD,QAAQ,CAAC,CAAC,CAAC,EAAE+C,KAAK,EAAE;IACjDH,SAAS;IACTzB,KAAK;IACL2B;EACF,CAAC,CAAC;EACF,MAAM,CAACI,QAAQ,EAAEC,SAAS,CAAC,GAAGvC,OAAO,CAAC,MAAM,EAAE;IAC5C8B,GAAG;IACHC,SAAS,EAAExC,IAAI,CAAC6C,OAAO,CAAC5B,IAAI,EAAEuB,SAAS,CAAC;IACxCS,WAAW,EAAE/B,iBAAiB;IAC9B4B,sBAAsB;IACtBjC;EACF,CAAC,CAAC;EACF,OAAO,aAAaF,IAAI,CAACoC,QAAQ,EAAElD,QAAQ,CAAC,CAAC,CAAC,EAAEmD,SAAS,EAAE;IACzDN,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFQ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGhB,aAAa,CAACiB,SAAS,CAAC,yBAAyB;EACvF;EACA;EACA;EACA;EACA;AACF;AACA;AACA;EACEX,QAAQ,EAAEzC,SAAS,CAACqD,IAAI;EACxB;AACF;AACA;EACEd,SAAS,EAAEvC,SAAS,CAACsD,MAAM;EAC3B;AACF;AACA;AACA;EACEzC,KAAK,EAAEb,SAAS,CAAC,sCAAsCuD,SAAS,CAAC,CAACvD,SAAS,CAACwD,KAAK,CAAC,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,EAAExD,SAAS,CAACsD,MAAM,CAAC,CAAC;EAC7J;AACF;AACA;AACA;EACEd,SAAS,EAAExC,SAAS,CAACgD,WAAW;EAChC;AACF;AACA;AACA;EACEN,SAAS,EAAE1C,SAAS,CAACyD,KAAK,CAAC;IACzBzC,IAAI,EAAEhB,SAAS,CAACuD,SAAS,CAAC,CAACvD,SAAS,CAAC0D,IAAI,EAAE1D,SAAS,CAAC2D,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;EACE5C,KAAK,EAAEf,SAAS,CAACyD,KAAK,CAAC;IACrBzC,IAAI,EAAEhB,SAAS,CAACgD;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACEY,EAAE,EAAE5D,SAAS,CAACuD,SAAS,CAAC,CAACvD,SAAS,CAAC6D,OAAO,CAAC7D,SAAS,CAACuD,SAAS,CAAC,CAACvD,SAAS,CAAC0D,IAAI,EAAE1D,SAAS,CAAC2D,MAAM,EAAE3D,SAAS,CAAC8D,IAAI,CAAC,CAAC,CAAC,EAAE9D,SAAS,CAAC0D,IAAI,EAAE1D,SAAS,CAAC2D,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;EACE7C,OAAO,EAAEd,SAAS,CAAC,sCAAsCuD,SAAS,CAAC,CAACvD,SAAS,CAACwD,KAAK,CAAC,CAAC,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,EAAExD,SAAS,CAACsD,MAAM,CAAC;AAChJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAenB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}