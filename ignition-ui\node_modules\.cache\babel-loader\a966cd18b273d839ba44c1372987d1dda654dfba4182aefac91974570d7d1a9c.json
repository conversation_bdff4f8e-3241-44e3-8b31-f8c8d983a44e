{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"disabled\", \"label\", \"id\", \"slotProps\", \"slots\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { getMenuItemUtilityClass } from './menuItemClasses';\nimport { useMenuItem, useMenuItemContextStabilizer } from '../useMenuItem';\nimport { unstable_composeClasses as composeClasses } from '../composeClasses';\nimport { useSlotProps } from '../utils/useSlotProps';\nimport { useClassNamesOverride } from '../utils/ClassNameConfigurator';\nimport { ListContext } from '../useList';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction useUtilityClasses(ownerState) {\n  const {\n    disabled,\n    focusVisible\n  } = ownerState;\n  const slots = {\n    root: ['root', disabled && 'disabled', focusVisible && 'focusVisible']\n  };\n  return composeClasses(slots, useClassNamesOverride(getMenuItemUtilityClass));\n}\nconst InnerMenuItem = /*#__PURE__*/React.memo(/*#__PURE__*/React.forwardRef(function MenuItem(props, forwardedRef) {\n  var _slots$root;\n  const {\n      children,\n      disabled: disabledProp = false,\n      label,\n      id,\n      slotProps = {},\n      slots = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    getRootProps,\n    disabled,\n    focusVisible,\n    highlighted\n  } = useMenuItem({\n    id,\n    disabled: disabledProp,\n    rootRef: forwardedRef,\n    label\n  });\n  const ownerState = _extends({}, props, {\n    disabled,\n    focusVisible,\n    highlighted\n  });\n  const classes = useUtilityClasses(ownerState);\n  const Root = (_slots$root = slots.root) != null ? _slots$root : 'li';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    getSlotProps: getRootProps,\n    externalSlotProps: slotProps.root,\n    externalForwardedProps: other,\n    className: classes.root,\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(Root, _extends({}, rootProps, {\n    children: children\n  }));\n}));\n\n/**\n * An unstyled menu item to be used within a Menu.\n *\n * Demos:\n *\n * - [Menu](https://mui.com/base-ui/react-menu/)\n *\n * API:\n *\n * - [MenuItem API](https://mui.com/base-ui/react-menu/components-api/#menu-item)\n */\nconst MenuItem = /*#__PURE__*/React.forwardRef(function MenuItem(props, ref) {\n  const {\n    id: idProp\n  } = props;\n\n  // This wrapper component is used as a performance optimization.\n  // `useMenuItemContextStabilizer` ensures that the context value\n  // is stable across renders, so that the actual MenuItem re-renders\n  // only when it needs to.\n  const {\n    contextValue,\n    id\n  } = useMenuItemContextStabilizer(idProp);\n  return /*#__PURE__*/_jsx(ListContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(InnerMenuItem, _extends({}, props, {\n      id: id,\n      ref: ref\n    }))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? MenuItem.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the menu item will be disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the menu item won't receive focus when the mouse moves over it.\n   *\n   * @default false\n   */\n  disableFocusOnHover: PropTypes.bool,\n  /**\n   * A text representation of the menu item's content.\n   * Used for keyboard text navigation matching.\n   */\n  label: PropTypes.string,\n  /**\n   * @ignore\n   */\n  onClick: PropTypes.func,\n  /**\n   * The props used for each slot inside the MenuItem.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the MenuItem.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  })\n} : void 0;\nexport { MenuItem };", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "PropTypes", "getMenuItemUtilityClass", "useMenuItem", "useMenuItemContextStabilizer", "unstable_composeClasses", "composeClasses", "useSlotProps", "useClassNamesOverride", "ListContext", "jsx", "_jsx", "useUtilityClasses", "ownerState", "disabled", "focusVisible", "slots", "root", "InnerMenuItem", "memo", "forwardRef", "MenuItem", "props", "forwardedRef", "_slots$root", "children", "disabledProp", "label", "id", "slotProps", "other", "getRootProps", "highlighted", "rootRef", "classes", "Root", "rootProps", "elementType", "getSlotProps", "externalSlotProps", "externalForwardedProps", "className", "ref", "idProp", "contextValue", "Provider", "value", "process", "env", "NODE_ENV", "propTypes", "node", "string", "bool", "disableFocusOnHover", "onClick", "func", "shape", "oneOfType", "object"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/MenuItem/MenuItem.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"children\", \"disabled\", \"label\", \"id\", \"slotProps\", \"slots\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { getMenuItemUtilityClass } from './menuItemClasses';\nimport { useMenuItem, useMenuItemContextStabilizer } from '../useMenuItem';\nimport { unstable_composeClasses as composeClasses } from '../composeClasses';\nimport { useSlotProps } from '../utils/useSlotProps';\nimport { useClassNamesOverride } from '../utils/ClassNameConfigurator';\nimport { ListContext } from '../useList';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction useUtilityClasses(ownerState) {\n  const {\n    disabled,\n    focusVisible\n  } = ownerState;\n  const slots = {\n    root: ['root', disabled && 'disabled', focusVisible && 'focusVisible']\n  };\n  return composeClasses(slots, useClassNamesOverride(getMenuItemUtilityClass));\n}\nconst InnerMenuItem = /*#__PURE__*/React.memo( /*#__PURE__*/React.forwardRef(function MenuItem(props, forwardedRef) {\n  var _slots$root;\n  const {\n      children,\n      disabled: disabledProp = false,\n      label,\n      id,\n      slotProps = {},\n      slots = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    getRootProps,\n    disabled,\n    focusVisible,\n    highlighted\n  } = useMenuItem({\n    id,\n    disabled: disabledProp,\n    rootRef: forwardedRef,\n    label\n  });\n  const ownerState = _extends({}, props, {\n    disabled,\n    focusVisible,\n    highlighted\n  });\n  const classes = useUtilityClasses(ownerState);\n  const Root = (_slots$root = slots.root) != null ? _slots$root : 'li';\n  const rootProps = useSlotProps({\n    elementType: Root,\n    getSlotProps: getRootProps,\n    externalSlotProps: slotProps.root,\n    externalForwardedProps: other,\n    className: classes.root,\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(Root, _extends({}, rootProps, {\n    children: children\n  }));\n}));\n\n/**\n * An unstyled menu item to be used within a Menu.\n *\n * Demos:\n *\n * - [Menu](https://mui.com/base-ui/react-menu/)\n *\n * API:\n *\n * - [MenuItem API](https://mui.com/base-ui/react-menu/components-api/#menu-item)\n */\nconst MenuItem = /*#__PURE__*/React.forwardRef(function MenuItem(props, ref) {\n  const {\n    id: idProp\n  } = props;\n\n  // This wrapper component is used as a performance optimization.\n  // `useMenuItemContextStabilizer` ensures that the context value\n  // is stable across renders, so that the actual MenuItem re-renders\n  // only when it needs to.\n  const {\n    contextValue,\n    id\n  } = useMenuItemContextStabilizer(idProp);\n  return /*#__PURE__*/_jsx(ListContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(InnerMenuItem, _extends({}, props, {\n      id: id,\n      ref: ref\n    }))\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? MenuItem.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * If `true`, the menu item will be disabled.\n   * @default false\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the menu item won't receive focus when the mouse moves over it.\n   *\n   * @default false\n   */\n  disableFocusOnHover: PropTypes.bool,\n  /**\n   * A text representation of the menu item's content.\n   * Used for keyboard text navigation matching.\n   */\n  label: PropTypes.string,\n  /**\n   * @ignore\n   */\n  onClick: PropTypes.func,\n  /**\n   * The props used for each slot inside the MenuItem.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside the MenuItem.\n   * Either a string to use a HTML element or a component.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  })\n} : void 0;\nexport { MenuItem };"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,CAAC;AAC/E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,uBAAuB,QAAQ,mBAAmB;AAC3D,SAASC,WAAW,EAAEC,4BAA4B,QAAQ,gBAAgB;AAC1E,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,mBAAmB;AAC7E,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,qBAAqB,QAAQ,gCAAgC;AACtE,SAASC,WAAW,QAAQ,YAAY;AACxC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,iBAAiBA,CAACC,UAAU,EAAE;EACrC,MAAM;IACJC,QAAQ;IACRC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEH,QAAQ,IAAI,UAAU,EAAEC,YAAY,IAAI,cAAc;EACvE,CAAC;EACD,OAAOT,cAAc,CAACU,KAAK,EAAER,qBAAqB,CAACN,uBAAuB,CAAC,CAAC;AAC9E;AACA,MAAMgB,aAAa,GAAG,aAAalB,KAAK,CAACmB,IAAI,CAAE,aAAanB,KAAK,CAACoB,UAAU,CAAC,SAASC,QAAQA,CAACC,KAAK,EAAEC,YAAY,EAAE;EAClH,IAAIC,WAAW;EACf,MAAM;MACFC,QAAQ;MACRX,QAAQ,EAAEY,YAAY,GAAG,KAAK;MAC9BC,KAAK;MACLC,EAAE;MACFC,SAAS,GAAG,CAAC,CAAC;MACdb,KAAK,GAAG,CAAC;IACX,CAAC,GAAGM,KAAK;IACTQ,KAAK,GAAGhC,6BAA6B,CAACwB,KAAK,EAAEvB,SAAS,CAAC;EACzD,MAAM;IACJgC,YAAY;IACZjB,QAAQ;IACRC,YAAY;IACZiB;EACF,CAAC,GAAG7B,WAAW,CAAC;IACdyB,EAAE;IACFd,QAAQ,EAAEY,YAAY;IACtBO,OAAO,EAAEV,YAAY;IACrBI;EACF,CAAC,CAAC;EACF,MAAMd,UAAU,GAAGhB,QAAQ,CAAC,CAAC,CAAC,EAAEyB,KAAK,EAAE;IACrCR,QAAQ;IACRC,YAAY;IACZiB;EACF,CAAC,CAAC;EACF,MAAME,OAAO,GAAGtB,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMsB,IAAI,GAAG,CAACX,WAAW,GAAGR,KAAK,CAACC,IAAI,KAAK,IAAI,GAAGO,WAAW,GAAG,IAAI;EACpE,MAAMY,SAAS,GAAG7B,YAAY,CAAC;IAC7B8B,WAAW,EAAEF,IAAI;IACjBG,YAAY,EAAEP,YAAY;IAC1BQ,iBAAiB,EAAEV,SAAS,CAACZ,IAAI;IACjCuB,sBAAsB,EAAEV,KAAK;IAC7BW,SAAS,EAAEP,OAAO,CAACjB,IAAI;IACvBJ;EACF,CAAC,CAAC;EACF,OAAO,aAAaF,IAAI,CAACwB,IAAI,EAAEtC,QAAQ,CAAC,CAAC,CAAC,EAAEuC,SAAS,EAAE;IACrDX,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMJ,QAAQ,GAAG,aAAarB,KAAK,CAACoB,UAAU,CAAC,SAASC,QAAQA,CAACC,KAAK,EAAEoB,GAAG,EAAE;EAC3E,MAAM;IACJd,EAAE,EAAEe;EACN,CAAC,GAAGrB,KAAK;;EAET;EACA;EACA;EACA;EACA,MAAM;IACJsB,YAAY;IACZhB;EACF,CAAC,GAAGxB,4BAA4B,CAACuC,MAAM,CAAC;EACxC,OAAO,aAAahC,IAAI,CAACF,WAAW,CAACoC,QAAQ,EAAE;IAC7CC,KAAK,EAAEF,YAAY;IACnBnB,QAAQ,EAAE,aAAad,IAAI,CAACO,aAAa,EAAErB,QAAQ,CAAC,CAAC,CAAC,EAAEyB,KAAK,EAAE;MAC7DM,EAAE,EAAEA,EAAE;MACNc,GAAG,EAAEA;IACP,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC,CAAC;AACFK,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG5B,QAAQ,CAAC6B,SAAS,CAAC,yBAAyB;EAClF;EACA;EACA;EACA;EACA;AACF;AACA;EACEzB,QAAQ,EAAExB,SAAS,CAACkD,IAAI;EACxB;AACF;AACA;EACEV,SAAS,EAAExC,SAAS,CAACmD,MAAM;EAC3B;AACF;AACA;AACA;EACEtC,QAAQ,EAAEb,SAAS,CAACoD,IAAI;EACxB;AACF;AACA;AACA;AACA;EACEC,mBAAmB,EAAErD,SAAS,CAACoD,IAAI;EACnC;AACF;AACA;AACA;EACE1B,KAAK,EAAE1B,SAAS,CAACmD,MAAM;EACvB;AACF;AACA;EACEG,OAAO,EAAEtD,SAAS,CAACuD,IAAI;EACvB;AACF;AACA;AACA;EACE3B,SAAS,EAAE5B,SAAS,CAACwD,KAAK,CAAC;IACzBxC,IAAI,EAAEhB,SAAS,CAACyD,SAAS,CAAC,CAACzD,SAAS,CAACuD,IAAI,EAAEvD,SAAS,CAAC0D,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;AACA;EACE3C,KAAK,EAAEf,SAAS,CAACwD,KAAK,CAAC;IACrBxC,IAAI,EAAEhB,SAAS,CAACoC;EAClB,CAAC;AACH,CAAC,GAAG,KAAK,CAAC;AACV,SAAShB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}