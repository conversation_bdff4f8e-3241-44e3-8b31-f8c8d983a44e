{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"checked\", \"uncheckedIcon\", \"checkedIcon\", \"label\", \"defaultChecked\", \"disabled\", \"disableIcon\", \"overlay\", \"id\", \"indeterminate\", \"indeterminateIcon\", \"name\", \"onBlur\", \"onChange\", \"onFocus\", \"onFocusVisible\", \"readOnly\", \"required\", \"value\", \"color\", \"variant\", \"size\", \"component\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_useId as useId, unstable_capitalize as capitalize } from '@mui/utils';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport { useSwitch } from '@mui/base/useSwitch';\nimport { styled, useThemeProps } from '../styles';\nimport useSlot from '../utils/useSlot';\nimport checkboxClasses, { getCheckboxUtilityClass } from './checkboxClasses';\nimport CheckIcon from '../internal/svg-icons/Check';\nimport IndeterminateIcon from '../internal/svg-icons/HorizontalRule';\nimport { TypographyNestedContext } from '../Typography/Typography';\nimport FormControlContext from '../FormControl/FormControlContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    checked,\n    disabled,\n    disableIcon,\n    focusVisible,\n    color,\n    variant,\n    size,\n    indeterminate\n  } = ownerState;\n  const slots = {\n    root: ['root', checked && 'checked', disabled && 'disabled', focusVisible && 'focusVisible', variant && `variant${capitalize(variant)}`, color && `color${capitalize(color)}`, size && `size${capitalize(size)}`],\n    checkbox: ['checkbox', checked && 'checked', indeterminate && 'indeterminate', disabled && 'disabled' // disabled class is necessary for displaying global variant\n    ],\n    action: ['action', checked && 'checked', disableIcon && disabled && 'disabled',\n    // add disabled class to action element for displaying global variant\n    focusVisible && 'focusVisible'],\n    input: ['input'],\n    label: ['label']\n  };\n  return composeClasses(slots, getCheckboxUtilityClass, {});\n};\nconst CheckboxRoot = styled('span', {\n  name: 'JoyCheckbox',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(_ref3 => {\n  let {\n    ownerState,\n    theme\n  } = _ref3;\n  var _theme$variants$plain, _theme$variants, _theme$variants2;\n  return _extends({\n    '--Icon-fontSize': 'var(--Checkbox-size)'\n  }, ownerState.size === 'sm' && {\n    '--Checkbox-size': '1rem',\n    '& ~ *': {\n      '--FormHelperText-margin': '0 0 0 1.5rem'\n    },\n    fontSize: theme.vars.fontSize.sm,\n    gap: 'var(--Checkbox-gap, 0.5rem)'\n  }, ownerState.size === 'md' && {\n    '--Checkbox-size': '1.25rem',\n    '& ~ *': {\n      '--FormHelperText-margin': '0.25rem 0 0 1.875rem'\n    },\n    fontSize: theme.vars.fontSize.md,\n    gap: 'var(--Checkbox-gap, 0.625rem)'\n  }, ownerState.size === 'lg' && {\n    '--Checkbox-size': '1.5rem',\n    '& ~ *': {\n      '--FormHelperText-margin': '0.375rem 0 0 2.25rem'\n    },\n    fontSize: theme.vars.fontSize.lg,\n    gap: 'var(--Checkbox-gap, 0.75rem)'\n  }, {\n    position: ownerState.overlay ? 'initial' : 'relative',\n    display: 'inline-flex',\n    fontFamily: theme.vars.fontFamily.body,\n    lineHeight: 'var(--Checkbox-size)',\n    color: theme.vars.palette.text.primary,\n    [`&.${checkboxClasses.disabled}`]: {\n      color: (_theme$variants$plain = theme.variants.plainDisabled) == null || (_theme$variants$plain = _theme$variants$plain[ownerState.color]) == null ? void 0 : _theme$variants$plain.color\n    }\n  }, ownerState.disableIcon && {\n    color: (_theme$variants = theme.variants[ownerState.variant]) == null || (_theme$variants = _theme$variants[ownerState.color]) == null ? void 0 : _theme$variants.color,\n    [`&.${checkboxClasses.disabled}`]: {\n      color: (_theme$variants2 = theme.variants[`${ownerState.variant}Disabled`]) == null || (_theme$variants2 = _theme$variants2[ownerState.color]) == null ? void 0 : _theme$variants2.color\n    }\n  });\n});\nconst CheckboxCheckbox = styled('span', {\n  name: 'JoyCheckbox',\n  slot: 'Checkbox',\n  overridesResolver: (props, styles) => styles.checkbox\n})(_ref4 => {\n  let {\n    theme,\n    ownerState\n  } = _ref4;\n  var _theme$variants3, _variantStyle$backgro, _theme$variants4, _theme$variants5, _theme$variants6;\n  const variantStyle = (_theme$variants3 = theme.variants[`${ownerState.variant}`]) == null ? void 0 : _theme$variants3[ownerState.color];\n  return [_extends({\n    '--Icon-color': ownerState.color !== 'neutral' || ownerState.variant === 'solid' ? 'currentColor' : theme.vars.palette.text.icon,\n    boxSizing: 'border-box',\n    borderRadius: `min(${theme.vars.radius.sm}, 0.25rem)`,\n    width: 'var(--Checkbox-size)',\n    height: 'var(--Checkbox-size)',\n    display: 'inline-flex',\n    justifyContent: 'center',\n    alignItems: 'center',\n    flexShrink: 0\n  }, ownerState.disableIcon && {\n    display: 'contents'\n  }, {\n    [`&.${checkboxClasses.checked}, &.${checkboxClasses.indeterminate}`]: {\n      '--Icon-color': 'currentColor'\n    }\n  }), ...(!ownerState.disableIcon ? [_extends({}, variantStyle, {\n    backgroundColor: (_variantStyle$backgro = variantStyle == null ? void 0 : variantStyle.backgroundColor) != null ? _variantStyle$backgro : theme.vars.palette.background.surface\n  }), {\n    '&:hover': {\n      '@media (hover: hover)': (_theme$variants4 = theme.variants[`${ownerState.variant}Hover`]) == null ? void 0 : _theme$variants4[ownerState.color]\n    }\n  }, {\n    '&:active': (_theme$variants5 = theme.variants[`${ownerState.variant}Active`]) == null ? void 0 : _theme$variants5[ownerState.color]\n  }, {\n    [`&.${checkboxClasses.disabled}`]: (_theme$variants6 = theme.variants[`${ownerState.variant}Disabled`]) == null ? void 0 : _theme$variants6[ownerState.color]\n  }] : [])];\n});\nconst CheckboxAction = styled('span', {\n  name: 'JoyCheckbox',\n  slot: 'Action',\n  overridesResolver: (props, styles) => styles.action\n})(_ref5 => {\n  let {\n    theme,\n    ownerState\n  } = _ref5;\n  var _theme$variants7, _theme$variants8, _theme$variants9, _theme$variants10;\n  return [{\n    borderRadius: `var(--Checkbox-actionRadius, ${ownerState.overlay ? 'var(--unstable_actionRadius, inherit)' : 'inherit'})`,\n    textAlign: 'left',\n    // prevent text-align inheritance\n    position: 'absolute',\n    top: 'calc(-1 * var(--variant-borderWidth, 0px))',\n    // clickable on the border and focus outline does not move when checked/unchecked\n    left: 'calc(-1 * var(--variant-borderWidth, 0px))',\n    bottom: 'calc(-1 * var(--variant-borderWidth, 0px))',\n    right: 'calc(-1 * var(--variant-borderWidth, 0px))',\n    zIndex: 1,\n    // The action element usually cover the area of nearest positioned parent\n    [theme.focus.selector]: theme.focus.default\n  }, ...(ownerState.disableIcon ? [(_theme$variants7 = theme.variants[ownerState.variant]) == null ? void 0 : _theme$variants7[ownerState.color], {\n    '&:hover': (_theme$variants8 = theme.variants[`${ownerState.variant}Hover`]) == null ? void 0 : _theme$variants8[ownerState.color]\n  }, {\n    '&:active': (_theme$variants9 = theme.variants[`${ownerState.variant}Active`]) == null ? void 0 : _theme$variants9[ownerState.color]\n  }, {\n    [`&.${checkboxClasses.disabled}`]: (_theme$variants10 = theme.variants[`${ownerState.variant}Disabled`]) == null ? void 0 : _theme$variants10[ownerState.color]\n  }] : [])];\n});\nconst CheckboxInput = styled('input', {\n  name: 'JoyCheckbox',\n  slot: 'Input',\n  overridesResolver: (props, styles) => styles.input\n})(() => ({\n  margin: 0,\n  opacity: 0,\n  position: 'absolute',\n  width: '100%',\n  height: '100%',\n  cursor: 'pointer'\n}));\nconst CheckboxLabel = styled('label', {\n  name: 'JoyCheckbox',\n  slot: 'Label',\n  overridesResolver: (props, styles) => styles.label\n})(_ref6 => {\n  let {\n    ownerState\n  } = _ref6;\n  return _extends({\n    flex: 1,\n    minWidth: 0\n  }, ownerState.disableIcon && {\n    zIndex: 1,\n    // label should stay on top of the action.\n    pointerEvents: 'none' // makes hover ineffect.\n  });\n});\nconst defaultCheckedIcon = /*#__PURE__*/_jsx(CheckIcon, {});\nconst defaultIndeterminateIcon = /*#__PURE__*/_jsx(IndeterminateIcon, {});\n/**\n *\n * Demos:\n *\n * - [Checkbox](https://mui.com/joy-ui/react-checkbox/)\n *\n * API:\n *\n * - [Checkbox API](https://mui.com/joy-ui/api/checkbox/)\n */\nconst Checkbox = /*#__PURE__*/React.forwardRef(function Checkbox(inProps, ref) {\n  var _ref, _inProps$disabled, _ref2, _inProps$size, _formControl$color;\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyCheckbox'\n  });\n  const {\n      checked: checkedProp,\n      uncheckedIcon,\n      checkedIcon = defaultCheckedIcon,\n      label,\n      defaultChecked,\n      disabled: disabledExternalProp,\n      disableIcon = false,\n      overlay,\n      id: idOverride,\n      indeterminate = false,\n      indeterminateIcon = defaultIndeterminateIcon,\n      name,\n      onBlur,\n      onChange,\n      onFocus,\n      onFocusVisible,\n      readOnly,\n      required,\n      value,\n      color: colorProp,\n      variant: variantProp,\n      size: sizeProp = 'md',\n      component,\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const formControl = React.useContext(FormControlContext);\n  const disabledProp = (_ref = (_inProps$disabled = inProps.disabled) != null ? _inProps$disabled : formControl == null ? void 0 : formControl.disabled) != null ? _ref : disabledExternalProp;\n  const size = (_ref2 = (_inProps$size = inProps.size) != null ? _inProps$size : formControl == null ? void 0 : formControl.size) != null ? _ref2 : sizeProp;\n  if (process.env.NODE_ENV !== 'production') {\n    const registerEffect = formControl == null ? void 0 : formControl.registerEffect;\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (registerEffect) {\n        return registerEffect();\n      }\n      return undefined;\n    }, [registerEffect]);\n  }\n  const id = useId(idOverride != null ? idOverride : formControl == null ? void 0 : formControl.htmlFor);\n  const useCheckboxProps = {\n    checked: checkedProp,\n    defaultChecked,\n    disabled: disabledProp,\n    onBlur,\n    onChange,\n    onFocus,\n    onFocusVisible\n  };\n  const {\n    getInputProps,\n    checked,\n    disabled,\n    focusVisible\n  } = useSwitch(useCheckboxProps);\n  const isCheckboxActive = checked || indeterminate;\n  const activeVariant = variantProp || 'solid';\n  const inactiveVariant = variantProp || 'outlined';\n  const variant = isCheckboxActive ? activeVariant : inactiveVariant;\n  const color = inProps.color || (formControl != null && formControl.error ? 'danger' : (_formControl$color = formControl == null ? void 0 : formControl.color) != null ? _formControl$color : colorProp);\n  const activeColor = color || 'primary';\n  const inactiveColor = color || 'neutral';\n  const ownerState = _extends({}, props, {\n    checked,\n    disabled,\n    disableIcon,\n    overlay,\n    focusVisible,\n    color: isCheckboxActive ? activeColor : inactiveColor,\n    variant,\n    size\n  });\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    className: classes.root,\n    elementType: CheckboxRoot,\n    externalForwardedProps,\n    ownerState\n  });\n  const [SlotCheckbox, checkboxProps] = useSlot('checkbox', {\n    className: classes.checkbox,\n    elementType: CheckboxCheckbox,\n    externalForwardedProps,\n    ownerState\n  });\n  const [SlotAction, actionProps] = useSlot('action', {\n    className: classes.action,\n    elementType: CheckboxAction,\n    externalForwardedProps,\n    ownerState\n  });\n  const [SlotInput, inputProps] = useSlot('input', {\n    additionalProps: _extends({\n      id,\n      name,\n      value,\n      readOnly,\n      role: undefined,\n      required: required != null ? required : formControl == null ? void 0 : formControl.required,\n      'aria-describedby': formControl == null ? void 0 : formControl['aria-describedby']\n    }, indeterminate && {\n      // https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Attributes/aria-checked#values\n      'aria-checked': 'mixed'\n    }),\n    className: classes.input,\n    elementType: CheckboxInput,\n    externalForwardedProps,\n    getSlotProps: getInputProps,\n    ownerState\n  });\n  const [SlotLabel, labelProps] = useSlot('label', {\n    additionalProps: {\n      htmlFor: id\n    },\n    className: classes.label,\n    elementType: CheckboxLabel,\n    externalForwardedProps,\n    ownerState\n  });\n  let icon = uncheckedIcon;\n  if (disableIcon) {\n    icon = null;\n  } else if (indeterminate) {\n    icon = indeterminateIcon;\n  } else if (checked) {\n    icon = checkedIcon;\n  }\n  return /*#__PURE__*/_jsxs(SlotRoot, _extends({}, rootProps, {\n    children: [/*#__PURE__*/_jsxs(SlotCheckbox, _extends({}, checkboxProps, {\n      children: [/*#__PURE__*/_jsx(SlotAction, _extends({}, actionProps, {\n        children: /*#__PURE__*/_jsx(SlotInput, _extends({}, inputProps))\n      })), icon]\n    })), label && /*#__PURE__*/_jsx(TypographyNestedContext.Provider, {\n      value: true,\n      children: /*#__PURE__*/_jsx(SlotLabel, _extends({}, labelProps, {\n        children: label\n      }))\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Checkbox.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If `true`, the component is checked.\n   */\n  checked: PropTypes.bool,\n  /**\n   * The icon to display when the component is checked.\n   * @default <CheckIcon />\n   */\n  checkedIcon: PropTypes.node,\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * Class name applied to the root element.\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component. It supports those theme colors that make sense for this component.\n   * @default 'neutral'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['danger', 'neutral', 'primary', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The default checked state. Use when the component is not controlled.\n   */\n  defaultChecked: PropTypes.bool,\n  /**\n   * If `true`, the component is disabled.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the checked icon is removed and the selected variant is applied on the `action` element instead.\n   * @default false\n   */\n  disableIcon: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  id: PropTypes.string,\n  /**\n   * If `true`, the component appears indeterminate.\n   * This does not set the native input element to indeterminate due\n   * to inconsistent behavior across browsers.\n   * However, we set a `data-indeterminate` attribute on the `input`.\n   * @default false\n   */\n  indeterminate: PropTypes.bool,\n  /**\n   * The icon to display when the component is indeterminate.\n   * @default <IndeterminateIcon />\n   */\n  indeterminateIcon: PropTypes.node,\n  /**\n   * The label element next to the checkbox.\n   */\n  label: PropTypes.node,\n  /**\n   * The `name` attribute of the input.\n   */\n  name: PropTypes.string,\n  /**\n   * @ignore\n   */\n  onBlur: PropTypes.func,\n  /**\n   * Callback fired when the state is changed.\n   *\n   * @param {React.ChangeEvent<HTMLInputElement>} event The event source of the callback.\n   * You can pull out the new value by accessing `event.target.value` (string).\n   * You can pull out the new checked state by accessing `event.target.checked` (boolean).\n   */\n  onChange: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocus: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocusVisible: PropTypes.func,\n  /**\n   * If `true`, the root element's position is set to initial which allows the action area to fill the nearest positioned parent.\n   * This prop is useful for composing Checkbox with ListItem component.\n   * @default false\n   */\n  overlay: PropTypes.bool,\n  /**\n   * If `true`, the component is read only.\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * If `true`, the `input` element is required.\n   */\n  required: PropTypes.bool,\n  /**\n   * The size of the component.\n   * @default 'md'\n   */\n  size: PropTypes.oneOf(['sm', 'md', 'lg']),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    action: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    checkbox: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    input: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    label: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    action: PropTypes.elementType,\n    checkbox: PropTypes.elementType,\n    input: PropTypes.elementType,\n    label: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The icon when `checked` is false.\n   */\n  uncheckedIcon: PropTypes.node,\n  /**\n   * The value of the component. The DOM API casts this to a string.\n   * The browser uses \"on\" as the default value.\n   */\n  value: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.string), PropTypes.number, PropTypes.string]),\n  /**\n   * The [global variant](https://mui.com/joy-ui/main-features/global-variants/) to use.\n   * @default 'solid'\n   */\n  variant: PropTypes.oneOf(['outlined', 'plain', 'soft', 'solid'])\n} : void 0;\nexport default Checkbox;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "unstable_useId", "useId", "unstable_capitalize", "capitalize", "unstable_composeClasses", "composeClasses", "useSwitch", "styled", "useThemeProps", "useSlot", "checkboxClasses", "getCheckboxUtilityClass", "CheckIcon", "IndeterminateIcon", "TypographyNestedContext", "FormControlContext", "jsx", "_jsx", "jsxs", "_jsxs", "useUtilityClasses", "ownerState", "checked", "disabled", "disableIcon", "focusVisible", "color", "variant", "size", "indeterminate", "slots", "root", "checkbox", "action", "input", "label", "CheckboxRoot", "name", "slot", "overridesResolver", "props", "styles", "_ref3", "theme", "_theme$variants$plain", "_theme$variants", "_theme$variants2", "fontSize", "vars", "sm", "gap", "md", "lg", "position", "overlay", "display", "fontFamily", "body", "lineHeight", "palette", "text", "primary", "variants", "plainDisabled", "CheckboxCheckbox", "_ref4", "_theme$variants3", "_variantStyle$backgro", "_theme$variants4", "_theme$variants5", "_theme$variants6", "variantStyle", "icon", "boxSizing", "borderRadius", "radius", "width", "height", "justifyContent", "alignItems", "flexShrink", "backgroundColor", "background", "surface", "CheckboxAction", "_ref5", "_theme$variants7", "_theme$variants8", "_theme$variants9", "_theme$variants10", "textAlign", "top", "left", "bottom", "right", "zIndex", "focus", "selector", "default", "CheckboxInput", "margin", "opacity", "cursor", "CheckboxLabel", "_ref6", "flex", "min<PERSON><PERSON><PERSON>", "pointerEvents", "defaultCheckedIcon", "defaultIndeterminateIcon", "Checkbox", "forwardRef", "inProps", "ref", "_ref", "_inProps$disabled", "_ref2", "_inProps$size", "_formControl$color", "checkedProp", "uncheckedIcon", "checkedIcon", "defaultChecked", "disabledExternalProp", "id", "idOverride", "indeterminateIcon", "onBlur", "onChange", "onFocus", "onFocusVisible", "readOnly", "required", "value", "colorProp", "variantProp", "sizeProp", "component", "slotProps", "other", "formControl", "useContext", "disabledProp", "process", "env", "NODE_ENV", "registerEffect", "useEffect", "undefined", "htmlFor", "useCheckboxProps", "getInputProps", "isCheckboxActive", "activeVariant", "inactiveVariant", "error", "activeColor", "inactiveColor", "classes", "externalForwardedProps", "SlotRoot", "rootProps", "className", "elementType", "SlotCheckbox", "checkboxProps", "SlotAction", "actionProps", "SlotInput", "inputProps", "additionalProps", "role", "getSlotProps", "SlotLabel", "labelProps", "children", "Provider", "propTypes", "bool", "node", "string", "oneOfType", "oneOf", "func", "shape", "object", "sx", "arrayOf", "number"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Checkbox/Checkbox.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"checked\", \"uncheckedIcon\", \"checkedIcon\", \"label\", \"defaultChecked\", \"disabled\", \"disableIcon\", \"overlay\", \"id\", \"indeterminate\", \"indeterminateIcon\", \"name\", \"onBlur\", \"onChange\", \"onFocus\", \"onFocusVisible\", \"readOnly\", \"required\", \"value\", \"color\", \"variant\", \"size\", \"component\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { unstable_useId as useId, unstable_capitalize as capitalize } from '@mui/utils';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport { useSwitch } from '@mui/base/useSwitch';\nimport { styled, useThemeProps } from '../styles';\nimport useSlot from '../utils/useSlot';\nimport checkboxClasses, { getCheckboxUtilityClass } from './checkboxClasses';\nimport CheckIcon from '../internal/svg-icons/Check';\nimport IndeterminateIcon from '../internal/svg-icons/HorizontalRule';\nimport { TypographyNestedContext } from '../Typography/Typography';\nimport FormControlContext from '../FormControl/FormControlContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    checked,\n    disabled,\n    disableIcon,\n    focusVisible,\n    color,\n    variant,\n    size,\n    indeterminate\n  } = ownerState;\n  const slots = {\n    root: ['root', checked && 'checked', disabled && 'disabled', focusVisible && 'focusVisible', variant && `variant${capitalize(variant)}`, color && `color${capitalize(color)}`, size && `size${capitalize(size)}`],\n    checkbox: ['checkbox', checked && 'checked', indeterminate && 'indeterminate', disabled && 'disabled' // disabled class is necessary for displaying global variant\n    ],\n    action: ['action', checked && 'checked', disableIcon && disabled && 'disabled',\n    // add disabled class to action element for displaying global variant\n    focusVisible && 'focusVisible'],\n    input: ['input'],\n    label: ['label']\n  };\n  return composeClasses(slots, getCheckboxUtilityClass, {});\n};\nconst CheckboxRoot = styled('span', {\n  name: 'JoyCheckbox',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(({\n  ownerState,\n  theme\n}) => {\n  var _theme$variants$plain, _theme$variants, _theme$variants2;\n  return _extends({\n    '--Icon-fontSize': 'var(--Checkbox-size)'\n  }, ownerState.size === 'sm' && {\n    '--Checkbox-size': '1rem',\n    '& ~ *': {\n      '--FormHelperText-margin': '0 0 0 1.5rem'\n    },\n    fontSize: theme.vars.fontSize.sm,\n    gap: 'var(--Checkbox-gap, 0.5rem)'\n  }, ownerState.size === 'md' && {\n    '--Checkbox-size': '1.25rem',\n    '& ~ *': {\n      '--FormHelperText-margin': '0.25rem 0 0 1.875rem'\n    },\n    fontSize: theme.vars.fontSize.md,\n    gap: 'var(--Checkbox-gap, 0.625rem)'\n  }, ownerState.size === 'lg' && {\n    '--Checkbox-size': '1.5rem',\n    '& ~ *': {\n      '--FormHelperText-margin': '0.375rem 0 0 2.25rem'\n    },\n    fontSize: theme.vars.fontSize.lg,\n    gap: 'var(--Checkbox-gap, 0.75rem)'\n  }, {\n    position: ownerState.overlay ? 'initial' : 'relative',\n    display: 'inline-flex',\n    fontFamily: theme.vars.fontFamily.body,\n    lineHeight: 'var(--Checkbox-size)',\n    color: theme.vars.palette.text.primary,\n    [`&.${checkboxClasses.disabled}`]: {\n      color: (_theme$variants$plain = theme.variants.plainDisabled) == null || (_theme$variants$plain = _theme$variants$plain[ownerState.color]) == null ? void 0 : _theme$variants$plain.color\n    }\n  }, ownerState.disableIcon && {\n    color: (_theme$variants = theme.variants[ownerState.variant]) == null || (_theme$variants = _theme$variants[ownerState.color]) == null ? void 0 : _theme$variants.color,\n    [`&.${checkboxClasses.disabled}`]: {\n      color: (_theme$variants2 = theme.variants[`${ownerState.variant}Disabled`]) == null || (_theme$variants2 = _theme$variants2[ownerState.color]) == null ? void 0 : _theme$variants2.color\n    }\n  });\n});\nconst CheckboxCheckbox = styled('span', {\n  name: 'JoyCheckbox',\n  slot: 'Checkbox',\n  overridesResolver: (props, styles) => styles.checkbox\n})(({\n  theme,\n  ownerState\n}) => {\n  var _theme$variants3, _variantStyle$backgro, _theme$variants4, _theme$variants5, _theme$variants6;\n  const variantStyle = (_theme$variants3 = theme.variants[`${ownerState.variant}`]) == null ? void 0 : _theme$variants3[ownerState.color];\n  return [_extends({\n    '--Icon-color': ownerState.color !== 'neutral' || ownerState.variant === 'solid' ? 'currentColor' : theme.vars.palette.text.icon,\n    boxSizing: 'border-box',\n    borderRadius: `min(${theme.vars.radius.sm}, 0.25rem)`,\n    width: 'var(--Checkbox-size)',\n    height: 'var(--Checkbox-size)',\n    display: 'inline-flex',\n    justifyContent: 'center',\n    alignItems: 'center',\n    flexShrink: 0\n  }, ownerState.disableIcon && {\n    display: 'contents'\n  }, {\n    [`&.${checkboxClasses.checked}, &.${checkboxClasses.indeterminate}`]: {\n      '--Icon-color': 'currentColor'\n    }\n  }), ...(!ownerState.disableIcon ? [_extends({}, variantStyle, {\n    backgroundColor: (_variantStyle$backgro = variantStyle == null ? void 0 : variantStyle.backgroundColor) != null ? _variantStyle$backgro : theme.vars.palette.background.surface\n  }), {\n    '&:hover': {\n      '@media (hover: hover)': (_theme$variants4 = theme.variants[`${ownerState.variant}Hover`]) == null ? void 0 : _theme$variants4[ownerState.color]\n    }\n  }, {\n    '&:active': (_theme$variants5 = theme.variants[`${ownerState.variant}Active`]) == null ? void 0 : _theme$variants5[ownerState.color]\n  }, {\n    [`&.${checkboxClasses.disabled}`]: (_theme$variants6 = theme.variants[`${ownerState.variant}Disabled`]) == null ? void 0 : _theme$variants6[ownerState.color]\n  }] : [])];\n});\nconst CheckboxAction = styled('span', {\n  name: 'JoyCheckbox',\n  slot: 'Action',\n  overridesResolver: (props, styles) => styles.action\n})(({\n  theme,\n  ownerState\n}) => {\n  var _theme$variants7, _theme$variants8, _theme$variants9, _theme$variants10;\n  return [{\n    borderRadius: `var(--Checkbox-actionRadius, ${ownerState.overlay ? 'var(--unstable_actionRadius, inherit)' : 'inherit'})`,\n    textAlign: 'left',\n    // prevent text-align inheritance\n    position: 'absolute',\n    top: 'calc(-1 * var(--variant-borderWidth, 0px))',\n    // clickable on the border and focus outline does not move when checked/unchecked\n    left: 'calc(-1 * var(--variant-borderWidth, 0px))',\n    bottom: 'calc(-1 * var(--variant-borderWidth, 0px))',\n    right: 'calc(-1 * var(--variant-borderWidth, 0px))',\n    zIndex: 1,\n    // The action element usually cover the area of nearest positioned parent\n    [theme.focus.selector]: theme.focus.default\n  }, ...(ownerState.disableIcon ? [(_theme$variants7 = theme.variants[ownerState.variant]) == null ? void 0 : _theme$variants7[ownerState.color], {\n    '&:hover': (_theme$variants8 = theme.variants[`${ownerState.variant}Hover`]) == null ? void 0 : _theme$variants8[ownerState.color]\n  }, {\n    '&:active': (_theme$variants9 = theme.variants[`${ownerState.variant}Active`]) == null ? void 0 : _theme$variants9[ownerState.color]\n  }, {\n    [`&.${checkboxClasses.disabled}`]: (_theme$variants10 = theme.variants[`${ownerState.variant}Disabled`]) == null ? void 0 : _theme$variants10[ownerState.color]\n  }] : [])];\n});\nconst CheckboxInput = styled('input', {\n  name: 'JoyCheckbox',\n  slot: 'Input',\n  overridesResolver: (props, styles) => styles.input\n})(() => ({\n  margin: 0,\n  opacity: 0,\n  position: 'absolute',\n  width: '100%',\n  height: '100%',\n  cursor: 'pointer'\n}));\nconst CheckboxLabel = styled('label', {\n  name: 'JoyCheckbox',\n  slot: 'Label',\n  overridesResolver: (props, styles) => styles.label\n})(({\n  ownerState\n}) => _extends({\n  flex: 1,\n  minWidth: 0\n}, ownerState.disableIcon && {\n  zIndex: 1,\n  // label should stay on top of the action.\n  pointerEvents: 'none' // makes hover ineffect.\n}));\nconst defaultCheckedIcon = /*#__PURE__*/_jsx(CheckIcon, {});\nconst defaultIndeterminateIcon = /*#__PURE__*/_jsx(IndeterminateIcon, {});\n/**\n *\n * Demos:\n *\n * - [Checkbox](https://mui.com/joy-ui/react-checkbox/)\n *\n * API:\n *\n * - [Checkbox API](https://mui.com/joy-ui/api/checkbox/)\n */\nconst Checkbox = /*#__PURE__*/React.forwardRef(function Checkbox(inProps, ref) {\n  var _ref, _inProps$disabled, _ref2, _inProps$size, _formControl$color;\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyCheckbox'\n  });\n  const {\n      checked: checkedProp,\n      uncheckedIcon,\n      checkedIcon = defaultCheckedIcon,\n      label,\n      defaultChecked,\n      disabled: disabledExternalProp,\n      disableIcon = false,\n      overlay,\n      id: idOverride,\n      indeterminate = false,\n      indeterminateIcon = defaultIndeterminateIcon,\n      name,\n      onBlur,\n      onChange,\n      onFocus,\n      onFocusVisible,\n      readOnly,\n      required,\n      value,\n      color: colorProp,\n      variant: variantProp,\n      size: sizeProp = 'md',\n      component,\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const formControl = React.useContext(FormControlContext);\n  const disabledProp = (_ref = (_inProps$disabled = inProps.disabled) != null ? _inProps$disabled : formControl == null ? void 0 : formControl.disabled) != null ? _ref : disabledExternalProp;\n  const size = (_ref2 = (_inProps$size = inProps.size) != null ? _inProps$size : formControl == null ? void 0 : formControl.size) != null ? _ref2 : sizeProp;\n  if (process.env.NODE_ENV !== 'production') {\n    const registerEffect = formControl == null ? void 0 : formControl.registerEffect;\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (registerEffect) {\n        return registerEffect();\n      }\n      return undefined;\n    }, [registerEffect]);\n  }\n  const id = useId(idOverride != null ? idOverride : formControl == null ? void 0 : formControl.htmlFor);\n  const useCheckboxProps = {\n    checked: checkedProp,\n    defaultChecked,\n    disabled: disabledProp,\n    onBlur,\n    onChange,\n    onFocus,\n    onFocusVisible\n  };\n  const {\n    getInputProps,\n    checked,\n    disabled,\n    focusVisible\n  } = useSwitch(useCheckboxProps);\n  const isCheckboxActive = checked || indeterminate;\n  const activeVariant = variantProp || 'solid';\n  const inactiveVariant = variantProp || 'outlined';\n  const variant = isCheckboxActive ? activeVariant : inactiveVariant;\n  const color = inProps.color || (formControl != null && formControl.error ? 'danger' : (_formControl$color = formControl == null ? void 0 : formControl.color) != null ? _formControl$color : colorProp);\n  const activeColor = color || 'primary';\n  const inactiveColor = color || 'neutral';\n  const ownerState = _extends({}, props, {\n    checked,\n    disabled,\n    disableIcon,\n    overlay,\n    focusVisible,\n    color: isCheckboxActive ? activeColor : inactiveColor,\n    variant,\n    size\n  });\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    className: classes.root,\n    elementType: CheckboxRoot,\n    externalForwardedProps,\n    ownerState\n  });\n  const [SlotCheckbox, checkboxProps] = useSlot('checkbox', {\n    className: classes.checkbox,\n    elementType: CheckboxCheckbox,\n    externalForwardedProps,\n    ownerState\n  });\n  const [SlotAction, actionProps] = useSlot('action', {\n    className: classes.action,\n    elementType: CheckboxAction,\n    externalForwardedProps,\n    ownerState\n  });\n  const [SlotInput, inputProps] = useSlot('input', {\n    additionalProps: _extends({\n      id,\n      name,\n      value,\n      readOnly,\n      role: undefined,\n      required: required != null ? required : formControl == null ? void 0 : formControl.required,\n      'aria-describedby': formControl == null ? void 0 : formControl['aria-describedby']\n    }, indeterminate && {\n      // https://developer.mozilla.org/en-US/docs/Web/Accessibility/ARIA/Attributes/aria-checked#values\n      'aria-checked': 'mixed'\n    }),\n    className: classes.input,\n    elementType: CheckboxInput,\n    externalForwardedProps,\n    getSlotProps: getInputProps,\n    ownerState\n  });\n  const [SlotLabel, labelProps] = useSlot('label', {\n    additionalProps: {\n      htmlFor: id\n    },\n    className: classes.label,\n    elementType: CheckboxLabel,\n    externalForwardedProps,\n    ownerState\n  });\n  let icon = uncheckedIcon;\n  if (disableIcon) {\n    icon = null;\n  } else if (indeterminate) {\n    icon = indeterminateIcon;\n  } else if (checked) {\n    icon = checkedIcon;\n  }\n  return /*#__PURE__*/_jsxs(SlotRoot, _extends({}, rootProps, {\n    children: [/*#__PURE__*/_jsxs(SlotCheckbox, _extends({}, checkboxProps, {\n      children: [/*#__PURE__*/_jsx(SlotAction, _extends({}, actionProps, {\n        children: /*#__PURE__*/_jsx(SlotInput, _extends({}, inputProps))\n      })), icon]\n    })), label && /*#__PURE__*/_jsx(TypographyNestedContext.Provider, {\n      value: true,\n      children: /*#__PURE__*/_jsx(SlotLabel, _extends({}, labelProps, {\n        children: label\n      }))\n    })]\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? Checkbox.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * If `true`, the component is checked.\n   */\n  checked: PropTypes.bool,\n  /**\n   * The icon to display when the component is checked.\n   * @default <CheckIcon />\n   */\n  checkedIcon: PropTypes.node,\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * Class name applied to the root element.\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component. It supports those theme colors that make sense for this component.\n   * @default 'neutral'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['danger', 'neutral', 'primary', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The default checked state. Use when the component is not controlled.\n   */\n  defaultChecked: PropTypes.bool,\n  /**\n   * If `true`, the component is disabled.\n   */\n  disabled: PropTypes.bool,\n  /**\n   * If `true`, the checked icon is removed and the selected variant is applied on the `action` element instead.\n   * @default false\n   */\n  disableIcon: PropTypes.bool,\n  /**\n   * @ignore\n   */\n  id: PropTypes.string,\n  /**\n   * If `true`, the component appears indeterminate.\n   * This does not set the native input element to indeterminate due\n   * to inconsistent behavior across browsers.\n   * However, we set a `data-indeterminate` attribute on the `input`.\n   * @default false\n   */\n  indeterminate: PropTypes.bool,\n  /**\n   * The icon to display when the component is indeterminate.\n   * @default <IndeterminateIcon />\n   */\n  indeterminateIcon: PropTypes.node,\n  /**\n   * The label element next to the checkbox.\n   */\n  label: PropTypes.node,\n  /**\n   * The `name` attribute of the input.\n   */\n  name: PropTypes.string,\n  /**\n   * @ignore\n   */\n  onBlur: PropTypes.func,\n  /**\n   * Callback fired when the state is changed.\n   *\n   * @param {React.ChangeEvent<HTMLInputElement>} event The event source of the callback.\n   * You can pull out the new value by accessing `event.target.value` (string).\n   * You can pull out the new checked state by accessing `event.target.checked` (boolean).\n   */\n  onChange: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocus: PropTypes.func,\n  /**\n   * @ignore\n   */\n  onFocusVisible: PropTypes.func,\n  /**\n   * If `true`, the root element's position is set to initial which allows the action area to fill the nearest positioned parent.\n   * This prop is useful for composing Checkbox with ListItem component.\n   * @default false\n   */\n  overlay: PropTypes.bool,\n  /**\n   * If `true`, the component is read only.\n   */\n  readOnly: PropTypes.bool,\n  /**\n   * If `true`, the `input` element is required.\n   */\n  required: PropTypes.bool,\n  /**\n   * The size of the component.\n   * @default 'md'\n   */\n  size: PropTypes.oneOf(['sm', 'md', 'lg']),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    action: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    checkbox: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    input: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    label: PropTypes.oneOfType([PropTypes.func, PropTypes.object]),\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    action: PropTypes.elementType,\n    checkbox: PropTypes.elementType,\n    input: PropTypes.elementType,\n    label: PropTypes.elementType,\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The icon when `checked` is false.\n   */\n  uncheckedIcon: PropTypes.node,\n  /**\n   * The value of the component. The DOM API casts this to a string.\n   * The browser uses \"on\" as the default value.\n   */\n  value: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.string), PropTypes.number, PropTypes.string]),\n  /**\n   * The [global variant](https://mui.com/joy-ui/main-features/global-variants/) to use.\n   * @default 'solid'\n   */\n  variant: PropTypes.oneOf(['outlined', 'plain', 'soft', 'solid'])\n} : void 0;\nexport default Checkbox;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,SAAS,EAAE,eAAe,EAAE,aAAa,EAAE,OAAO,EAAE,gBAAgB,EAAE,UAAU,EAAE,aAAa,EAAE,SAAS,EAAE,IAAI,EAAE,eAAe,EAAE,mBAAmB,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,gBAAgB,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,WAAW,CAAC;AACrU,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,cAAc,IAAIC,KAAK,EAAEC,mBAAmB,IAAIC,UAAU,QAAQ,YAAY;AACvF,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,WAAW;AACrE,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,MAAM,EAAEC,aAAa,QAAQ,WAAW;AACjD,OAAOC,OAAO,MAAM,kBAAkB;AACtC,OAAOC,eAAe,IAAIC,uBAAuB,QAAQ,mBAAmB;AAC5E,OAAOC,SAAS,MAAM,6BAA6B;AACnD,OAAOC,iBAAiB,MAAM,sCAAsC;AACpE,SAASC,uBAAuB,QAAQ,0BAA0B;AAClE,OAAOC,kBAAkB,MAAM,mCAAmC;AAClE,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC,QAAQ;IACRC,WAAW;IACXC,YAAY;IACZC,KAAK;IACLC,OAAO;IACPC,IAAI;IACJC;EACF,CAAC,GAAGR,UAAU;EACd,MAAMS,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAET,OAAO,IAAI,SAAS,EAAEC,QAAQ,IAAI,UAAU,EAAEE,YAAY,IAAI,cAAc,EAAEE,OAAO,IAAI,UAAUxB,UAAU,CAACwB,OAAO,CAAC,EAAE,EAAED,KAAK,IAAI,QAAQvB,UAAU,CAACuB,KAAK,CAAC,EAAE,EAAEE,IAAI,IAAI,OAAOzB,UAAU,CAACyB,IAAI,CAAC,EAAE,CAAC;IACjNI,QAAQ,EAAE,CAAC,UAAU,EAAEV,OAAO,IAAI,SAAS,EAAEO,aAAa,IAAI,eAAe,EAAEN,QAAQ,IAAI,UAAU,CAAC;IAAA,CACrG;IACDU,MAAM,EAAE,CAAC,QAAQ,EAAEX,OAAO,IAAI,SAAS,EAAEE,WAAW,IAAID,QAAQ,IAAI,UAAU;IAC9E;IACAE,YAAY,IAAI,cAAc,CAAC;IAC/BS,KAAK,EAAE,CAAC,OAAO,CAAC;IAChBC,KAAK,EAAE,CAAC,OAAO;EACjB,CAAC;EACD,OAAO9B,cAAc,CAACyB,KAAK,EAAEnB,uBAAuB,EAAE,CAAC,CAAC,CAAC;AAC3D,CAAC;AACD,MAAMyB,YAAY,GAAG7B,MAAM,CAAC,MAAM,EAAE;EAClC8B,IAAI,EAAE,aAAa;EACnBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACV;AAC/C,CAAC,CAAC,CAACW,KAAA,IAGG;EAAA,IAHF;IACFrB,UAAU;IACVsB;EACF,CAAC,GAAAD,KAAA;EACC,IAAIE,qBAAqB,EAAEC,eAAe,EAAEC,gBAAgB;EAC5D,OAAOlD,QAAQ,CAAC;IACd,iBAAiB,EAAE;EACrB,CAAC,EAAEyB,UAAU,CAACO,IAAI,KAAK,IAAI,IAAI;IAC7B,iBAAiB,EAAE,MAAM;IACzB,OAAO,EAAE;MACP,yBAAyB,EAAE;IAC7B,CAAC;IACDmB,QAAQ,EAAEJ,KAAK,CAACK,IAAI,CAACD,QAAQ,CAACE,EAAE;IAChCC,GAAG,EAAE;EACP,CAAC,EAAE7B,UAAU,CAACO,IAAI,KAAK,IAAI,IAAI;IAC7B,iBAAiB,EAAE,SAAS;IAC5B,OAAO,EAAE;MACP,yBAAyB,EAAE;IAC7B,CAAC;IACDmB,QAAQ,EAAEJ,KAAK,CAACK,IAAI,CAACD,QAAQ,CAACI,EAAE;IAChCD,GAAG,EAAE;EACP,CAAC,EAAE7B,UAAU,CAACO,IAAI,KAAK,IAAI,IAAI;IAC7B,iBAAiB,EAAE,QAAQ;IAC3B,OAAO,EAAE;MACP,yBAAyB,EAAE;IAC7B,CAAC;IACDmB,QAAQ,EAAEJ,KAAK,CAACK,IAAI,CAACD,QAAQ,CAACK,EAAE;IAChCF,GAAG,EAAE;EACP,CAAC,EAAE;IACDG,QAAQ,EAAEhC,UAAU,CAACiC,OAAO,GAAG,SAAS,GAAG,UAAU;IACrDC,OAAO,EAAE,aAAa;IACtBC,UAAU,EAAEb,KAAK,CAACK,IAAI,CAACQ,UAAU,CAACC,IAAI;IACtCC,UAAU,EAAE,sBAAsB;IAClChC,KAAK,EAAEiB,KAAK,CAACK,IAAI,CAACW,OAAO,CAACC,IAAI,CAACC,OAAO;IACtC,CAAC,KAAKnD,eAAe,CAACa,QAAQ,EAAE,GAAG;MACjCG,KAAK,EAAE,CAACkB,qBAAqB,GAAGD,KAAK,CAACmB,QAAQ,CAACC,aAAa,KAAK,IAAI,IAAI,CAACnB,qBAAqB,GAAGA,qBAAqB,CAACvB,UAAU,CAACK,KAAK,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGkB,qBAAqB,CAAClB;IACtL;EACF,CAAC,EAAEL,UAAU,CAACG,WAAW,IAAI;IAC3BE,KAAK,EAAE,CAACmB,eAAe,GAAGF,KAAK,CAACmB,QAAQ,CAACzC,UAAU,CAACM,OAAO,CAAC,KAAK,IAAI,IAAI,CAACkB,eAAe,GAAGA,eAAe,CAACxB,UAAU,CAACK,KAAK,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGmB,eAAe,CAACnB,KAAK;IACvK,CAAC,KAAKhB,eAAe,CAACa,QAAQ,EAAE,GAAG;MACjCG,KAAK,EAAE,CAACoB,gBAAgB,GAAGH,KAAK,CAACmB,QAAQ,CAAC,GAAGzC,UAAU,CAACM,OAAO,UAAU,CAAC,KAAK,IAAI,IAAI,CAACmB,gBAAgB,GAAGA,gBAAgB,CAACzB,UAAU,CAACK,KAAK,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGoB,gBAAgB,CAACpB;IACrL;EACF,CAAC,CAAC;AACJ,CAAC,CAAC;AACF,MAAMsC,gBAAgB,GAAGzD,MAAM,CAAC,MAAM,EAAE;EACtC8B,IAAI,EAAE,aAAa;EACnBC,IAAI,EAAE,UAAU;EAChBC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACT;AAC/C,CAAC,CAAC,CAACiC,KAAA,IAGG;EAAA,IAHF;IACFtB,KAAK;IACLtB;EACF,CAAC,GAAA4C,KAAA;EACC,IAAIC,gBAAgB,EAAEC,qBAAqB,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,gBAAgB;EACjG,MAAMC,YAAY,GAAG,CAACL,gBAAgB,GAAGvB,KAAK,CAACmB,QAAQ,CAAC,GAAGzC,UAAU,CAACM,OAAO,EAAE,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGuC,gBAAgB,CAAC7C,UAAU,CAACK,KAAK,CAAC;EACvI,OAAO,CAAC9B,QAAQ,CAAC;IACf,cAAc,EAAEyB,UAAU,CAACK,KAAK,KAAK,SAAS,IAAIL,UAAU,CAACM,OAAO,KAAK,OAAO,GAAG,cAAc,GAAGgB,KAAK,CAACK,IAAI,CAACW,OAAO,CAACC,IAAI,CAACY,IAAI;IAChIC,SAAS,EAAE,YAAY;IACvBC,YAAY,EAAE,OAAO/B,KAAK,CAACK,IAAI,CAAC2B,MAAM,CAAC1B,EAAE,YAAY;IACrD2B,KAAK,EAAE,sBAAsB;IAC7BC,MAAM,EAAE,sBAAsB;IAC9BtB,OAAO,EAAE,aAAa;IACtBuB,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,QAAQ;IACpBC,UAAU,EAAE;EACd,CAAC,EAAE3D,UAAU,CAACG,WAAW,IAAI;IAC3B+B,OAAO,EAAE;EACX,CAAC,EAAE;IACD,CAAC,KAAK7C,eAAe,CAACY,OAAO,OAAOZ,eAAe,CAACmB,aAAa,EAAE,GAAG;MACpE,cAAc,EAAE;IAClB;EACF,CAAC,CAAC,EAAE,IAAI,CAACR,UAAU,CAACG,WAAW,GAAG,CAAC5B,QAAQ,CAAC,CAAC,CAAC,EAAE2E,YAAY,EAAE;IAC5DU,eAAe,EAAE,CAACd,qBAAqB,GAAGI,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACU,eAAe,KAAK,IAAI,GAAGd,qBAAqB,GAAGxB,KAAK,CAACK,IAAI,CAACW,OAAO,CAACuB,UAAU,CAACC;EAC1K,CAAC,CAAC,EAAE;IACF,SAAS,EAAE;MACT,uBAAuB,EAAE,CAACf,gBAAgB,GAAGzB,KAAK,CAACmB,QAAQ,CAAC,GAAGzC,UAAU,CAACM,OAAO,OAAO,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGyC,gBAAgB,CAAC/C,UAAU,CAACK,KAAK;IACjJ;EACF,CAAC,EAAE;IACD,UAAU,EAAE,CAAC2C,gBAAgB,GAAG1B,KAAK,CAACmB,QAAQ,CAAC,GAAGzC,UAAU,CAACM,OAAO,QAAQ,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG0C,gBAAgB,CAAChD,UAAU,CAACK,KAAK;EACrI,CAAC,EAAE;IACD,CAAC,KAAKhB,eAAe,CAACa,QAAQ,EAAE,GAAG,CAAC+C,gBAAgB,GAAG3B,KAAK,CAACmB,QAAQ,CAAC,GAAGzC,UAAU,CAACM,OAAO,UAAU,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG2C,gBAAgB,CAACjD,UAAU,CAACK,KAAK;EAC9J,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AACX,CAAC,CAAC;AACF,MAAM0D,cAAc,GAAG7E,MAAM,CAAC,MAAM,EAAE;EACpC8B,IAAI,EAAE,aAAa;EACnBC,IAAI,EAAE,QAAQ;EACdC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACR;AAC/C,CAAC,CAAC,CAACoD,KAAA,IAGG;EAAA,IAHF;IACF1C,KAAK;IACLtB;EACF,CAAC,GAAAgE,KAAA;EACC,IAAIC,gBAAgB,EAAEC,gBAAgB,EAAEC,gBAAgB,EAAEC,iBAAiB;EAC3E,OAAO,CAAC;IACNf,YAAY,EAAE,gCAAgCrD,UAAU,CAACiC,OAAO,GAAG,uCAAuC,GAAG,SAAS,GAAG;IACzHoC,SAAS,EAAE,MAAM;IACjB;IACArC,QAAQ,EAAE,UAAU;IACpBsC,GAAG,EAAE,4CAA4C;IACjD;IACAC,IAAI,EAAE,4CAA4C;IAClDC,MAAM,EAAE,4CAA4C;IACpDC,KAAK,EAAE,4CAA4C;IACnDC,MAAM,EAAE,CAAC;IACT;IACA,CAACpD,KAAK,CAACqD,KAAK,CAACC,QAAQ,GAAGtD,KAAK,CAACqD,KAAK,CAACE;EACtC,CAAC,EAAE,IAAI7E,UAAU,CAACG,WAAW,GAAG,CAAC,CAAC8D,gBAAgB,GAAG3C,KAAK,CAACmB,QAAQ,CAACzC,UAAU,CAACM,OAAO,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG2D,gBAAgB,CAACjE,UAAU,CAACK,KAAK,CAAC,EAAE;IAC9I,SAAS,EAAE,CAAC6D,gBAAgB,GAAG5C,KAAK,CAACmB,QAAQ,CAAC,GAAGzC,UAAU,CAACM,OAAO,OAAO,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG4D,gBAAgB,CAAClE,UAAU,CAACK,KAAK;EACnI,CAAC,EAAE;IACD,UAAU,EAAE,CAAC8D,gBAAgB,GAAG7C,KAAK,CAACmB,QAAQ,CAAC,GAAGzC,UAAU,CAACM,OAAO,QAAQ,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG6D,gBAAgB,CAACnE,UAAU,CAACK,KAAK;EACrI,CAAC,EAAE;IACD,CAAC,KAAKhB,eAAe,CAACa,QAAQ,EAAE,GAAG,CAACkE,iBAAiB,GAAG9C,KAAK,CAACmB,QAAQ,CAAC,GAAGzC,UAAU,CAACM,OAAO,UAAU,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG8D,iBAAiB,CAACpE,UAAU,CAACK,KAAK;EAChK,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC;AACX,CAAC,CAAC;AACF,MAAMyE,aAAa,GAAG5F,MAAM,CAAC,OAAO,EAAE;EACpC8B,IAAI,EAAE,aAAa;EACnBC,IAAI,EAAE,OAAO;EACbC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACP;AAC/C,CAAC,CAAC,CAAC,OAAO;EACRkE,MAAM,EAAE,CAAC;EACTC,OAAO,EAAE,CAAC;EACVhD,QAAQ,EAAE,UAAU;EACpBuB,KAAK,EAAE,MAAM;EACbC,MAAM,EAAE,MAAM;EACdyB,MAAM,EAAE;AACV,CAAC,CAAC,CAAC;AACH,MAAMC,aAAa,GAAGhG,MAAM,CAAC,OAAO,EAAE;EACpC8B,IAAI,EAAE,aAAa;EACnBC,IAAI,EAAE,OAAO;EACbC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACN;AAC/C,CAAC,CAAC,CAACqE,KAAA;EAAA,IAAC;IACFnF;EACF,CAAC,GAAAmF,KAAA;EAAA,OAAK5G,QAAQ,CAAC;IACb6G,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ,CAAC,EAAErF,UAAU,CAACG,WAAW,IAAI;IAC3BuE,MAAM,EAAE,CAAC;IACT;IACAY,aAAa,EAAE,MAAM,CAAC;EACxB,CAAC,CAAC;AAAA,EAAC;AACH,MAAMC,kBAAkB,GAAG,aAAa3F,IAAI,CAACL,SAAS,EAAE,CAAC,CAAC,CAAC;AAC3D,MAAMiG,wBAAwB,GAAG,aAAa5F,IAAI,CAACJ,iBAAiB,EAAE,CAAC,CAAC,CAAC;AACzE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiG,QAAQ,GAAG,aAAahH,KAAK,CAACiH,UAAU,CAAC,SAASD,QAAQA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC7E,IAAIC,IAAI,EAAEC,iBAAiB,EAAEC,KAAK,EAAEC,aAAa,EAAEC,kBAAkB;EACrE,MAAM9E,KAAK,GAAGhC,aAAa,CAAC;IAC1BgC,KAAK,EAAEwE,OAAO;IACd3E,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFf,OAAO,EAAEiG,WAAW;MACpBC,aAAa;MACbC,WAAW,GAAGb,kBAAkB;MAChCzE,KAAK;MACLuF,cAAc;MACdnG,QAAQ,EAAEoG,oBAAoB;MAC9BnG,WAAW,GAAG,KAAK;MACnB8B,OAAO;MACPsE,EAAE,EAAEC,UAAU;MACdhG,aAAa,GAAG,KAAK;MACrBiG,iBAAiB,GAAGjB,wBAAwB;MAC5CxE,IAAI;MACJ0F,MAAM;MACNC,QAAQ;MACRC,OAAO;MACPC,cAAc;MACdC,QAAQ;MACRC,QAAQ;MACRC,KAAK;MACL3G,KAAK,EAAE4G,SAAS;MAChB3G,OAAO,EAAE4G,WAAW;MACpB3G,IAAI,EAAE4G,QAAQ,GAAG,IAAI;MACrBC,SAAS;MACT3G,KAAK,GAAG,CAAC,CAAC;MACV4G,SAAS,GAAG,CAAC;IACf,CAAC,GAAGlG,KAAK;IACTmG,KAAK,GAAGhJ,6BAA6B,CAAC6C,KAAK,EAAE3C,SAAS,CAAC;EACzD,MAAM+I,WAAW,GAAG9I,KAAK,CAAC+I,UAAU,CAAC9H,kBAAkB,CAAC;EACxD,MAAM+H,YAAY,GAAG,CAAC5B,IAAI,GAAG,CAACC,iBAAiB,GAAGH,OAAO,CAACzF,QAAQ,KAAK,IAAI,GAAG4F,iBAAiB,GAAGyB,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACrH,QAAQ,KAAK,IAAI,GAAG2F,IAAI,GAAGS,oBAAoB;EAC5L,MAAM/F,IAAI,GAAG,CAACwF,KAAK,GAAG,CAACC,aAAa,GAAGL,OAAO,CAACpF,IAAI,KAAK,IAAI,GAAGyF,aAAa,GAAGuB,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAChH,IAAI,KAAK,IAAI,GAAGwF,KAAK,GAAGoB,QAAQ;EAC1J,IAAIO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,MAAMC,cAAc,GAAGN,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACM,cAAc;IAChF;IACApJ,KAAK,CAACqJ,SAAS,CAAC,MAAM;MACpB,IAAID,cAAc,EAAE;QAClB,OAAOA,cAAc,CAAC,CAAC;MACzB;MACA,OAAOE,SAAS;IAClB,CAAC,EAAE,CAACF,cAAc,CAAC,CAAC;EACtB;EACA,MAAMtB,EAAE,GAAG3H,KAAK,CAAC4H,UAAU,IAAI,IAAI,GAAGA,UAAU,GAAGe,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACS,OAAO,CAAC;EACtG,MAAMC,gBAAgB,GAAG;IACvBhI,OAAO,EAAEiG,WAAW;IACpBG,cAAc;IACdnG,QAAQ,EAAEuH,YAAY;IACtBf,MAAM;IACNC,QAAQ;IACRC,OAAO;IACPC;EACF,CAAC;EACD,MAAM;IACJqB,aAAa;IACbjI,OAAO;IACPC,QAAQ;IACRE;EACF,CAAC,GAAGnB,SAAS,CAACgJ,gBAAgB,CAAC;EAC/B,MAAME,gBAAgB,GAAGlI,OAAO,IAAIO,aAAa;EACjD,MAAM4H,aAAa,GAAGlB,WAAW,IAAI,OAAO;EAC5C,MAAMmB,eAAe,GAAGnB,WAAW,IAAI,UAAU;EACjD,MAAM5G,OAAO,GAAG6H,gBAAgB,GAAGC,aAAa,GAAGC,eAAe;EAClE,MAAMhI,KAAK,GAAGsF,OAAO,CAACtF,KAAK,KAAKkH,WAAW,IAAI,IAAI,IAAIA,WAAW,CAACe,KAAK,GAAG,QAAQ,GAAG,CAACrC,kBAAkB,GAAGsB,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAClH,KAAK,KAAK,IAAI,GAAG4F,kBAAkB,GAAGgB,SAAS,CAAC;EACvM,MAAMsB,WAAW,GAAGlI,KAAK,IAAI,SAAS;EACtC,MAAMmI,aAAa,GAAGnI,KAAK,IAAI,SAAS;EACxC,MAAML,UAAU,GAAGzB,QAAQ,CAAC,CAAC,CAAC,EAAE4C,KAAK,EAAE;IACrClB,OAAO;IACPC,QAAQ;IACRC,WAAW;IACX8B,OAAO;IACP7B,YAAY;IACZC,KAAK,EAAE8H,gBAAgB,GAAGI,WAAW,GAAGC,aAAa;IACrDlI,OAAO;IACPC;EACF,CAAC,CAAC;EACF,MAAMkI,OAAO,GAAG1I,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAM0I,sBAAsB,GAAGnK,QAAQ,CAAC,CAAC,CAAC,EAAE+I,KAAK,EAAE;IACjDF,SAAS;IACT3G,KAAK;IACL4G;EACF,CAAC,CAAC;EACF,MAAM,CAACsB,QAAQ,EAAEC,SAAS,CAAC,GAAGxJ,OAAO,CAAC,MAAM,EAAE;IAC5CwG,GAAG;IACHiD,SAAS,EAAEJ,OAAO,CAAC/H,IAAI;IACvBoI,WAAW,EAAE/H,YAAY;IACzB2H,sBAAsB;IACtB1I;EACF,CAAC,CAAC;EACF,MAAM,CAAC+I,YAAY,EAAEC,aAAa,CAAC,GAAG5J,OAAO,CAAC,UAAU,EAAE;IACxDyJ,SAAS,EAAEJ,OAAO,CAAC9H,QAAQ;IAC3BmI,WAAW,EAAEnG,gBAAgB;IAC7B+F,sBAAsB;IACtB1I;EACF,CAAC,CAAC;EACF,MAAM,CAACiJ,UAAU,EAAEC,WAAW,CAAC,GAAG9J,OAAO,CAAC,QAAQ,EAAE;IAClDyJ,SAAS,EAAEJ,OAAO,CAAC7H,MAAM;IACzBkI,WAAW,EAAE/E,cAAc;IAC3B2E,sBAAsB;IACtB1I;EACF,CAAC,CAAC;EACF,MAAM,CAACmJ,SAAS,EAAEC,UAAU,CAAC,GAAGhK,OAAO,CAAC,OAAO,EAAE;IAC/CiK,eAAe,EAAE9K,QAAQ,CAAC;MACxBgI,EAAE;MACFvF,IAAI;MACJgG,KAAK;MACLF,QAAQ;MACRwC,IAAI,EAAEvB,SAAS;MACfhB,QAAQ,EAAEA,QAAQ,IAAI,IAAI,GAAGA,QAAQ,GAAGQ,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACR,QAAQ;MAC3F,kBAAkB,EAAEQ,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC,kBAAkB;IACnF,CAAC,EAAE/G,aAAa,IAAI;MAClB;MACA,cAAc,EAAE;IAClB,CAAC,CAAC;IACFqI,SAAS,EAAEJ,OAAO,CAAC5H,KAAK;IACxBiI,WAAW,EAAEhE,aAAa;IAC1B4D,sBAAsB;IACtBa,YAAY,EAAErB,aAAa;IAC3BlI;EACF,CAAC,CAAC;EACF,MAAM,CAACwJ,SAAS,EAAEC,UAAU,CAAC,GAAGrK,OAAO,CAAC,OAAO,EAAE;IAC/CiK,eAAe,EAAE;MACfrB,OAAO,EAAEzB;IACX,CAAC;IACDsC,SAAS,EAAEJ,OAAO,CAAC3H,KAAK;IACxBgI,WAAW,EAAE5D,aAAa;IAC1BwD,sBAAsB;IACtB1I;EACF,CAAC,CAAC;EACF,IAAImD,IAAI,GAAGgD,aAAa;EACxB,IAAIhG,WAAW,EAAE;IACfgD,IAAI,GAAG,IAAI;EACb,CAAC,MAAM,IAAI3C,aAAa,EAAE;IACxB2C,IAAI,GAAGsD,iBAAiB;EAC1B,CAAC,MAAM,IAAIxG,OAAO,EAAE;IAClBkD,IAAI,GAAGiD,WAAW;EACpB;EACA,OAAO,aAAatG,KAAK,CAAC6I,QAAQ,EAAEpK,QAAQ,CAAC,CAAC,CAAC,EAAEqK,SAAS,EAAE;IAC1Dc,QAAQ,EAAE,CAAC,aAAa5J,KAAK,CAACiJ,YAAY,EAAExK,QAAQ,CAAC,CAAC,CAAC,EAAEyK,aAAa,EAAE;MACtEU,QAAQ,EAAE,CAAC,aAAa9J,IAAI,CAACqJ,UAAU,EAAE1K,QAAQ,CAAC,CAAC,CAAC,EAAE2K,WAAW,EAAE;QACjEQ,QAAQ,EAAE,aAAa9J,IAAI,CAACuJ,SAAS,EAAE5K,QAAQ,CAAC,CAAC,CAAC,EAAE6K,UAAU,CAAC;MACjE,CAAC,CAAC,CAAC,EAAEjG,IAAI;IACX,CAAC,CAAC,CAAC,EAAErC,KAAK,IAAI,aAAalB,IAAI,CAACH,uBAAuB,CAACkK,QAAQ,EAAE;MAChE3C,KAAK,EAAE,IAAI;MACX0C,QAAQ,EAAE,aAAa9J,IAAI,CAAC4J,SAAS,EAAEjL,QAAQ,CAAC,CAAC,CAAC,EAAEkL,UAAU,EAAE;QAC9DC,QAAQ,EAAE5I;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF4G,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGnC,QAAQ,CAACmE,SAAS,CAAC,yBAAyB;EAClF;EACA;EACA;EACA;EACA;AACF;AACA;EACE3J,OAAO,EAAEvB,SAAS,CAACmL,IAAI;EACvB;AACF;AACA;AACA;EACEzD,WAAW,EAAE1H,SAAS,CAACoL,IAAI;EAC3B;AACF;AACA;EACEJ,QAAQ,EAAEhL,SAAS,CAACoL,IAAI;EACxB;AACF;AACA;EACEjB,SAAS,EAAEnK,SAAS,CAACqL,MAAM;EAC3B;AACF;AACA;AACA;EACE1J,KAAK,EAAE3B,SAAS,CAAC,sCAAsCsL,SAAS,CAAC,CAACtL,SAAS,CAACuL,KAAK,CAAC,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,EAAEvL,SAAS,CAACqL,MAAM,CAAC,CAAC;EAC7J;AACF;AACA;AACA;EACE3C,SAAS,EAAE1I,SAAS,CAACoK,WAAW;EAChC;AACF;AACA;EACEzC,cAAc,EAAE3H,SAAS,CAACmL,IAAI;EAC9B;AACF;AACA;EACE3J,QAAQ,EAAExB,SAAS,CAACmL,IAAI;EACxB;AACF;AACA;AACA;EACE1J,WAAW,EAAEzB,SAAS,CAACmL,IAAI;EAC3B;AACF;AACA;EACEtD,EAAE,EAAE7H,SAAS,CAACqL,MAAM;EACpB;AACF;AACA;AACA;AACA;AACA;AACA;EACEvJ,aAAa,EAAE9B,SAAS,CAACmL,IAAI;EAC7B;AACF;AACA;AACA;EACEpD,iBAAiB,EAAE/H,SAAS,CAACoL,IAAI;EACjC;AACF;AACA;EACEhJ,KAAK,EAAEpC,SAAS,CAACoL,IAAI;EACrB;AACF;AACA;EACE9I,IAAI,EAAEtC,SAAS,CAACqL,MAAM;EACtB;AACF;AACA;EACErD,MAAM,EAAEhI,SAAS,CAACwL,IAAI;EACtB;AACF;AACA;AACA;AACA;AACA;AACA;EACEvD,QAAQ,EAAEjI,SAAS,CAACwL,IAAI;EACxB;AACF;AACA;EACEtD,OAAO,EAAElI,SAAS,CAACwL,IAAI;EACvB;AACF;AACA;EACErD,cAAc,EAAEnI,SAAS,CAACwL,IAAI;EAC9B;AACF;AACA;AACA;AACA;EACEjI,OAAO,EAAEvD,SAAS,CAACmL,IAAI;EACvB;AACF;AACA;EACE/C,QAAQ,EAAEpI,SAAS,CAACmL,IAAI;EACxB;AACF;AACA;EACE9C,QAAQ,EAAErI,SAAS,CAACmL,IAAI;EACxB;AACF;AACA;AACA;EACEtJ,IAAI,EAAE7B,SAAS,CAACuL,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC;EACzC;AACF;AACA;AACA;EACE5C,SAAS,EAAE3I,SAAS,CAACyL,KAAK,CAAC;IACzBvJ,MAAM,EAAElC,SAAS,CAACsL,SAAS,CAAC,CAACtL,SAAS,CAACwL,IAAI,EAAExL,SAAS,CAAC0L,MAAM,CAAC,CAAC;IAC/DzJ,QAAQ,EAAEjC,SAAS,CAACsL,SAAS,CAAC,CAACtL,SAAS,CAACwL,IAAI,EAAExL,SAAS,CAAC0L,MAAM,CAAC,CAAC;IACjEvJ,KAAK,EAAEnC,SAAS,CAACsL,SAAS,CAAC,CAACtL,SAAS,CAACwL,IAAI,EAAExL,SAAS,CAAC0L,MAAM,CAAC,CAAC;IAC9DtJ,KAAK,EAAEpC,SAAS,CAACsL,SAAS,CAAC,CAACtL,SAAS,CAACwL,IAAI,EAAExL,SAAS,CAAC0L,MAAM,CAAC,CAAC;IAC9D1J,IAAI,EAAEhC,SAAS,CAACsL,SAAS,CAAC,CAACtL,SAAS,CAACwL,IAAI,EAAExL,SAAS,CAAC0L,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;EACE3J,KAAK,EAAE/B,SAAS,CAACyL,KAAK,CAAC;IACrBvJ,MAAM,EAAElC,SAAS,CAACoK,WAAW;IAC7BnI,QAAQ,EAAEjC,SAAS,CAACoK,WAAW;IAC/BjI,KAAK,EAAEnC,SAAS,CAACoK,WAAW;IAC5BhI,KAAK,EAAEpC,SAAS,CAACoK,WAAW;IAC5BpI,IAAI,EAAEhC,SAAS,CAACoK;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACEuB,EAAE,EAAE3L,SAAS,CAACsL,SAAS,CAAC,CAACtL,SAAS,CAAC4L,OAAO,CAAC5L,SAAS,CAACsL,SAAS,CAAC,CAACtL,SAAS,CAACwL,IAAI,EAAExL,SAAS,CAAC0L,MAAM,EAAE1L,SAAS,CAACmL,IAAI,CAAC,CAAC,CAAC,EAAEnL,SAAS,CAACwL,IAAI,EAAExL,SAAS,CAAC0L,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;EACEjE,aAAa,EAAEzH,SAAS,CAACoL,IAAI;EAC7B;AACF;AACA;AACA;EACE9C,KAAK,EAAEtI,SAAS,CAACsL,SAAS,CAAC,CAACtL,SAAS,CAAC4L,OAAO,CAAC5L,SAAS,CAACqL,MAAM,CAAC,EAAErL,SAAS,CAAC6L,MAAM,EAAE7L,SAAS,CAACqL,MAAM,CAAC,CAAC;EACrG;AACF;AACA;AACA;EACEzJ,OAAO,EAAE5B,SAAS,CAACuL,KAAK,CAAC,CAAC,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC;AACjE,CAAC,GAAG,KAAK,CAAC;AACV,eAAexE,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}