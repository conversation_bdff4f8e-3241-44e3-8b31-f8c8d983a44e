{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"aria-describedby\", \"aria-label\", \"aria-labelledby\", \"autoComplete\", \"autoFocus\", \"className\", \"defaultValue\", \"disabled\", \"disabledInProp\", \"error\", \"id\", \"name\", \"onClick\", \"onChange\", \"onKeyDown\", \"onKeyUp\", \"onFocus\", \"onBlur\", \"placeholder\", \"readOnly\", \"required\", \"type\", \"value\"];\nimport * as React from 'react';\nimport { useInput } from '@mui/base/useInput';\nimport FormControlContext from '../FormControl/FormControlContext';\nexport default function useForwardedInput(props, classes) {\n  var _ref;\n  const formControl = React.useContext(FormControlContext);\n  const {\n      'aria-describedby': ariaDescribedby,\n      'aria-label': ariaLabel,\n      'aria-labelledby': ariaLabelledby,\n      autoComplete,\n      autoFocus,\n      className,\n      defaultValue,\n      disabled: disabledProp,\n      disabledInProp,\n      error: errorProp,\n      id,\n      name,\n      onClick,\n      onChange,\n      onKeyDown,\n      onKeyUp,\n      onFocus,\n      onBlur,\n      placeholder,\n      readOnly,\n      required,\n      type,\n      value\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    getRootProps,\n    getInputProps,\n    focused,\n    error,\n    disabled\n  } = useInput({\n    disabled: (_ref = disabledInProp != null ? disabledInProp : formControl == null ? void 0 : formControl.disabled) != null ? _ref : disabledProp,\n    defaultValue,\n    error: errorProp,\n    onBlur,\n    onClick,\n    onChange,\n    onFocus,\n    required: required != null ? required : formControl == null ? void 0 : formControl.required,\n    value\n  });\n  const rootStateClasses = {\n    [classes.disabled]: disabled,\n    [classes.error]: error,\n    [classes.focused]: focused,\n    [classes.formControl]: Boolean(formControl),\n    [className]: className\n  };\n  const inputStateClasses = {\n    [classes.disabled]: disabled\n  };\n  const propsToForward = {\n    'aria-describedby': ariaDescribedby,\n    'aria-label': ariaLabel,\n    'aria-labelledby': ariaLabelledby,\n    autoComplete,\n    autoFocus,\n    disabled,\n    id,\n    onKeyDown,\n    onKeyUp,\n    name,\n    placeholder,\n    readOnly,\n    type\n  };\n  return _extends({\n    formControl,\n    propsToForward,\n    rootStateClasses,\n    inputStateClasses,\n    getRootProps,\n    getInputProps,\n    focused,\n    error,\n    disabled\n  }, other);\n}", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "useInput", "FormControlContext", "useForwardedInput", "props", "classes", "_ref", "formControl", "useContext", "aria<PERSON><PERSON><PERSON><PERSON>", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "autoComplete", "autoFocus", "className", "defaultValue", "disabled", "disabledProp", "disabledInProp", "error", "errorProp", "id", "name", "onClick", "onChange", "onKeyDown", "onKeyUp", "onFocus", "onBlur", "placeholder", "readOnly", "required", "type", "value", "other", "getRootProps", "getInputProps", "focused", "rootStateClasses", "Boolean", "inputStateClasses", "propsToForward"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Input/useForwardedInput.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"aria-describedby\", \"aria-label\", \"aria-labelledby\", \"autoComplete\", \"autoFocus\", \"className\", \"defaultValue\", \"disabled\", \"disabledInProp\", \"error\", \"id\", \"name\", \"onClick\", \"onChange\", \"onKeyDown\", \"onKeyUp\", \"onFocus\", \"onBlur\", \"placeholder\", \"readOnly\", \"required\", \"type\", \"value\"];\nimport * as React from 'react';\nimport { useInput } from '@mui/base/useInput';\nimport FormControlContext from '../FormControl/FormControlContext';\nexport default function useForwardedInput(props, classes) {\n  var _ref;\n  const formControl = React.useContext(FormControlContext);\n  const {\n      'aria-describedby': ariaDescribedby,\n      'aria-label': ariaLabel,\n      'aria-labelledby': ariaLabelledby,\n      autoComplete,\n      autoFocus,\n      className,\n      defaultValue,\n      disabled: disabledProp,\n      disabledInProp,\n      error: errorProp,\n      id,\n      name,\n      onClick,\n      onChange,\n      onKeyDown,\n      onKeyUp,\n      onFocus,\n      onBlur,\n      placeholder,\n      readOnly,\n      required,\n      type,\n      value\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const {\n    getRootProps,\n    getInputProps,\n    focused,\n    error,\n    disabled\n  } = useInput({\n    disabled: (_ref = disabledInProp != null ? disabledInProp : formControl == null ? void 0 : formControl.disabled) != null ? _ref : disabledProp,\n    defaultValue,\n    error: errorProp,\n    onBlur,\n    onClick,\n    onChange,\n    onFocus,\n    required: required != null ? required : formControl == null ? void 0 : formControl.required,\n    value\n  });\n  const rootStateClasses = {\n    [classes.disabled]: disabled,\n    [classes.error]: error,\n    [classes.focused]: focused,\n    [classes.formControl]: Boolean(formControl),\n    [className]: className\n  };\n  const inputStateClasses = {\n    [classes.disabled]: disabled\n  };\n  const propsToForward = {\n    'aria-describedby': ariaDescribedby,\n    'aria-label': ariaLabel,\n    'aria-labelledby': ariaLabelledby,\n    autoComplete,\n    autoFocus,\n    disabled,\n    id,\n    onKeyDown,\n    onKeyUp,\n    name,\n    placeholder,\n    readOnly,\n    type\n  };\n  return _extends({\n    formControl,\n    propsToForward,\n    rootStateClasses,\n    inputStateClasses,\n    getRootProps,\n    getInputProps,\n    focused,\n    error,\n    disabled\n  }, other);\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,kBAAkB,EAAE,YAAY,EAAE,iBAAiB,EAAE,cAAc,EAAE,WAAW,EAAE,WAAW,EAAE,cAAc,EAAE,UAAU,EAAE,gBAAgB,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ,EAAE,aAAa,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC;AAClT,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,OAAOC,kBAAkB,MAAM,mCAAmC;AAClE,eAAe,SAASC,iBAAiBA,CAACC,KAAK,EAAEC,OAAO,EAAE;EACxD,IAAIC,IAAI;EACR,MAAMC,WAAW,GAAGP,KAAK,CAACQ,UAAU,CAACN,kBAAkB,CAAC;EACxD,MAAM;MACF,kBAAkB,EAAEO,eAAe;MACnC,YAAY,EAAEC,SAAS;MACvB,iBAAiB,EAAEC,cAAc;MACjCC,YAAY;MACZC,SAAS;MACTC,SAAS;MACTC,YAAY;MACZC,QAAQ,EAAEC,YAAY;MACtBC,cAAc;MACdC,KAAK,EAAEC,SAAS;MAChBC,EAAE;MACFC,IAAI;MACJC,OAAO;MACPC,QAAQ;MACRC,SAAS;MACTC,OAAO;MACPC,OAAO;MACPC,MAAM;MACNC,WAAW;MACXC,QAAQ;MACRC,QAAQ;MACRC,IAAI;MACJC;IACF,CAAC,GAAG7B,KAAK;IACT8B,KAAK,GAAGpC,6BAA6B,CAACM,KAAK,EAAEL,SAAS,CAAC;EACzD,MAAM;IACJoC,YAAY;IACZC,aAAa;IACbC,OAAO;IACPlB,KAAK;IACLH;EACF,CAAC,GAAGf,QAAQ,CAAC;IACXe,QAAQ,EAAE,CAACV,IAAI,GAAGY,cAAc,IAAI,IAAI,GAAGA,cAAc,GAAGX,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACS,QAAQ,KAAK,IAAI,GAAGV,IAAI,GAAGW,YAAY;IAC9IF,YAAY;IACZI,KAAK,EAAEC,SAAS;IAChBQ,MAAM;IACNL,OAAO;IACPC,QAAQ;IACRG,OAAO;IACPI,QAAQ,EAAEA,QAAQ,IAAI,IAAI,GAAGA,QAAQ,GAAGxB,WAAW,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACwB,QAAQ;IAC3FE;EACF,CAAC,CAAC;EACF,MAAMK,gBAAgB,GAAG;IACvB,CAACjC,OAAO,CAACW,QAAQ,GAAGA,QAAQ;IAC5B,CAACX,OAAO,CAACc,KAAK,GAAGA,KAAK;IACtB,CAACd,OAAO,CAACgC,OAAO,GAAGA,OAAO;IAC1B,CAAChC,OAAO,CAACE,WAAW,GAAGgC,OAAO,CAAChC,WAAW,CAAC;IAC3C,CAACO,SAAS,GAAGA;EACf,CAAC;EACD,MAAM0B,iBAAiB,GAAG;IACxB,CAACnC,OAAO,CAACW,QAAQ,GAAGA;EACtB,CAAC;EACD,MAAMyB,cAAc,GAAG;IACrB,kBAAkB,EAAEhC,eAAe;IACnC,YAAY,EAAEC,SAAS;IACvB,iBAAiB,EAAEC,cAAc;IACjCC,YAAY;IACZC,SAAS;IACTG,QAAQ;IACRK,EAAE;IACFI,SAAS;IACTC,OAAO;IACPJ,IAAI;IACJO,WAAW;IACXC,QAAQ;IACRE;EACF,CAAC;EACD,OAAOnC,QAAQ,CAAC;IACdU,WAAW;IACXkC,cAAc;IACdH,gBAAgB;IAChBE,iBAAiB;IACjBL,YAAY;IACZC,aAAa;IACbC,OAAO;IACPlB,KAAK;IACLH;EACF,CAAC,EAAEkB,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}