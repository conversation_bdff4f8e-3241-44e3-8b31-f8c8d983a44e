{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"component\", \"children\", \"orientation\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport { useThemeProps } from '../styles';\nimport styled from '../styles/styled';\nimport { getCardContentUtilityClass } from './cardContentClasses';\nimport cardOverflowClasses from '../CardOverflow/cardOverflowClasses';\nimport useSlot from '../utils/useSlot';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = () => {\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getCardContentUtilityClass, {});\n};\nexport const StyledCardContentRoot = styled('div')(_ref => {\n  let {\n    ownerState\n  } = _ref;\n  return {\n    display: 'flex',\n    flexDirection: ownerState.orientation === 'horizontal' ? 'row' : 'column',\n    flex: 9999,\n    // fill the available space in the Card and also shrink if needed\n    zIndex: 1,\n    columnGap: 'var(--Card-padding)',\n    rowGap: 'max(2px, calc(0.1875 * var(--Card-padding)))',\n    padding: 'var(--unstable_padding)',\n    [`.${cardOverflowClasses.root} > &`]: {\n      '--unstable_padding': 'calc(var(--Card-padding) * 0.75) 0px'\n    }\n  };\n});\nconst CardContentRoot = styled(StyledCardContentRoot, {\n  name: 'JoyCardContent',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({});\n/**\n *\n * Demos:\n *\n * - [Card](https://mui.com/joy-ui/react-card/)\n *\n * API:\n *\n * - [CardContent API](https://mui.com/joy-ui/api/card-content/)\n */\nconst CardContent = /*#__PURE__*/React.forwardRef(function CardContent(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyCardContent'\n  });\n  const {\n      className,\n      component = 'div',\n      children,\n      orientation = 'vertical',\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const ownerState = _extends({}, props, {\n    component,\n    orientation\n  });\n  const classes = useUtilityClasses();\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    className: clsx(classes.root, className),\n    elementType: CardContentRoot,\n    externalForwardedProps,\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(SlotRoot, _extends({}, rootProps, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? CardContent.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Used to render icon or text elements inside the CardContent if `src` is not set.\n   * This can be an element, or just a string.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The component orientation.\n   * @default 'vertical'\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default CardContent;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "clsx", "PropTypes", "unstable_composeClasses", "composeClasses", "useThemeProps", "styled", "getCardContentUtilityClass", "cardOverflowClasses", "useSlot", "jsx", "_jsx", "useUtilityClasses", "slots", "root", "StyledCardContentRoot", "_ref", "ownerState", "display", "flexDirection", "orientation", "flex", "zIndex", "columnGap", "rowGap", "padding", "CardContentRoot", "name", "slot", "overridesResolver", "props", "styles", "<PERSON><PERSON><PERSON><PERSON>", "forwardRef", "inProps", "ref", "className", "component", "children", "slotProps", "other", "externalForwardedProps", "classes", "SlotRoot", "rootProps", "elementType", "process", "env", "NODE_ENV", "propTypes", "node", "string", "oneOf", "shape", "oneOfType", "func", "object", "sx", "arrayOf", "bool"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/CardContent/CardContent.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"className\", \"component\", \"children\", \"orientation\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport { useThemeProps } from '../styles';\nimport styled from '../styles/styled';\nimport { getCardContentUtilityClass } from './cardContentClasses';\nimport cardOverflowClasses from '../CardOverflow/cardOverflowClasses';\nimport useSlot from '../utils/useSlot';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = () => {\n  const slots = {\n    root: ['root']\n  };\n  return composeClasses(slots, getCardContentUtilityClass, {});\n};\nexport const StyledCardContentRoot = styled('div')(({\n  ownerState\n}) => ({\n  display: 'flex',\n  flexDirection: ownerState.orientation === 'horizontal' ? 'row' : 'column',\n  flex: 9999,\n  // fill the available space in the Card and also shrink if needed\n  zIndex: 1,\n  columnGap: 'var(--Card-padding)',\n  rowGap: 'max(2px, calc(0.1875 * var(--Card-padding)))',\n  padding: 'var(--unstable_padding)',\n  [`.${cardOverflowClasses.root} > &`]: {\n    '--unstable_padding': 'calc(var(--Card-padding) * 0.75) 0px'\n  }\n}));\nconst CardContentRoot = styled(StyledCardContentRoot, {\n  name: 'JoyCardContent',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})({});\n/**\n *\n * Demos:\n *\n * - [Card](https://mui.com/joy-ui/react-card/)\n *\n * API:\n *\n * - [CardContent API](https://mui.com/joy-ui/api/card-content/)\n */\nconst CardContent = /*#__PURE__*/React.forwardRef(function CardContent(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyCardContent'\n  });\n  const {\n      className,\n      component = 'div',\n      children,\n      orientation = 'vertical',\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const ownerState = _extends({}, props, {\n    component,\n    orientation\n  });\n  const classes = useUtilityClasses();\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    className: clsx(classes.root, className),\n    elementType: CardContentRoot,\n    externalForwardedProps,\n    ownerState\n  });\n  return /*#__PURE__*/_jsx(SlotRoot, _extends({}, rootProps, {\n    children: children\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? CardContent.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * Used to render icon or text elements inside the CardContent if `src` is not set.\n   * This can be an element, or just a string.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The component orientation.\n   * @default 'vertical'\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default CardContent;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,aAAa,EAAE,OAAO,EAAE,WAAW,CAAC;AAC7F,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,WAAW;AACrE,SAASC,aAAa,QAAQ,WAAW;AACzC,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,0BAA0B,QAAQ,sBAAsB;AACjE,OAAOC,mBAAmB,MAAM,qCAAqC;AACrE,OAAOC,OAAO,MAAM,kBAAkB;AACtC,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAC9B,MAAMC,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM;EACf,CAAC;EACD,OAAOV,cAAc,CAACS,KAAK,EAAEN,0BAA0B,EAAE,CAAC,CAAC,CAAC;AAC9D,CAAC;AACD,OAAO,MAAMQ,qBAAqB,GAAGT,MAAM,CAAC,KAAK,CAAC,CAACU,IAAA;EAAA,IAAC;IAClDC;EACF,CAAC,GAAAD,IAAA;EAAA,OAAM;IACLE,OAAO,EAAE,MAAM;IACfC,aAAa,EAAEF,UAAU,CAACG,WAAW,KAAK,YAAY,GAAG,KAAK,GAAG,QAAQ;IACzEC,IAAI,EAAE,IAAI;IACV;IACAC,MAAM,EAAE,CAAC;IACTC,SAAS,EAAE,qBAAqB;IAChCC,MAAM,EAAE,8CAA8C;IACtDC,OAAO,EAAE,yBAAyB;IAClC,CAAC,IAAIjB,mBAAmB,CAACM,IAAI,MAAM,GAAG;MACpC,oBAAoB,EAAE;IACxB;EACF,CAAC;AAAA,CAAC,CAAC;AACH,MAAMY,eAAe,GAAGpB,MAAM,CAACS,qBAAqB,EAAE;EACpDY,IAAI,EAAE,gBAAgB;EACtBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACjB;AAC/C,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkB,WAAW,GAAG,aAAahC,KAAK,CAACiC,UAAU,CAAC,SAASD,WAAWA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACnF,MAAML,KAAK,GAAGzB,aAAa,CAAC;IAC1ByB,KAAK,EAAEI,OAAO;IACdP,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFS,SAAS;MACTC,SAAS,GAAG,KAAK;MACjBC,QAAQ;MACRlB,WAAW,GAAG,UAAU;MACxBP,KAAK,GAAG,CAAC,CAAC;MACV0B,SAAS,GAAG,CAAC;IACf,CAAC,GAAGT,KAAK;IACTU,KAAK,GAAG1C,6BAA6B,CAACgC,KAAK,EAAE/B,SAAS,CAAC;EACzD,MAAM0C,sBAAsB,GAAG5C,QAAQ,CAAC,CAAC,CAAC,EAAE2C,KAAK,EAAE;IACjDH,SAAS;IACTxB,KAAK;IACL0B;EACF,CAAC,CAAC;EACF,MAAMtB,UAAU,GAAGpB,QAAQ,CAAC,CAAC,CAAC,EAAEiC,KAAK,EAAE;IACrCO,SAAS;IACTjB;EACF,CAAC,CAAC;EACF,MAAMsB,OAAO,GAAG9B,iBAAiB,CAAC,CAAC;EACnC,MAAM,CAAC+B,QAAQ,EAAEC,SAAS,CAAC,GAAGnC,OAAO,CAAC,MAAM,EAAE;IAC5C0B,GAAG;IACHC,SAAS,EAAEnC,IAAI,CAACyC,OAAO,CAAC5B,IAAI,EAAEsB,SAAS,CAAC;IACxCS,WAAW,EAAEnB,eAAe;IAC5Be,sBAAsB;IACtBxB;EACF,CAAC,CAAC;EACF,OAAO,aAAaN,IAAI,CAACgC,QAAQ,EAAE9C,QAAQ,CAAC,CAAC,CAAC,EAAE+C,SAAS,EAAE;IACzDN,QAAQ,EAAEA;EACZ,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFQ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGhB,WAAW,CAACiB,SAAS,CAAC,yBAAyB;EACrF;EACA;EACA;EACA;EACA;AACF;AACA;AACA;EACEX,QAAQ,EAAEpC,SAAS,CAACgD,IAAI;EACxB;AACF;AACA;EACEd,SAAS,EAAElC,SAAS,CAACiD,MAAM;EAC3B;AACF;AACA;AACA;EACEd,SAAS,EAAEnC,SAAS,CAAC2C,WAAW;EAChC;AACF;AACA;AACA;EACEzB,WAAW,EAAElB,SAAS,CAACkD,KAAK,CAAC,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;EACxD;AACF;AACA;AACA;EACEb,SAAS,EAAErC,SAAS,CAACmD,KAAK,CAAC;IACzBvC,IAAI,EAAEZ,SAAS,CAACoD,SAAS,CAAC,CAACpD,SAAS,CAACqD,IAAI,EAAErD,SAAS,CAACsD,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;EACE3C,KAAK,EAAEX,SAAS,CAACmD,KAAK,CAAC;IACrBvC,IAAI,EAAEZ,SAAS,CAAC2C;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACEY,EAAE,EAAEvD,SAAS,CAACoD,SAAS,CAAC,CAACpD,SAAS,CAACwD,OAAO,CAACxD,SAAS,CAACoD,SAAS,CAAC,CAACpD,SAAS,CAACqD,IAAI,EAAErD,SAAS,CAACsD,MAAM,EAAEtD,SAAS,CAACyD,IAAI,CAAC,CAAC,CAAC,EAAEzD,SAAS,CAACqD,IAAI,EAAErD,SAAS,CAACsD,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAexB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}