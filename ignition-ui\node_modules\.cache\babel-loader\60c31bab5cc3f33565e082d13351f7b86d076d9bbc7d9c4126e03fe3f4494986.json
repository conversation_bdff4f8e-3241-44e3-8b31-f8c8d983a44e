{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getCardActionsUtilityClass(slot) {\n  return generateUtilityClass('MuiCardActions', slot);\n}\nconst cardActionsClasses = generateUtilityClasses('MuiCardActions', ['root']);\nexport default cardActionsClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getCardActionsUtilityClass", "slot", "cardActionsClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/CardActions/cardActionsClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getCardActionsUtilityClass(slot) {\n  return generateUtilityClass('MuiCardActions', slot);\n}\nconst cardActionsClasses = generateUtilityClasses('MuiCardActions', ['root']);\nexport default cardActionsClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,0BAA0BA,CAACC,IAAI,EAAE;EAC/C,OAAOH,oBAAoB,CAAC,gBAAgB,EAAEG,IAAI,CAAC;AACrD;AACA,MAAMC,kBAAkB,GAAGH,sBAAsB,CAAC,gBAAgB,EAAE,CAAC,MAAM,CAAC,CAAC;AAC7E,eAAeG,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}