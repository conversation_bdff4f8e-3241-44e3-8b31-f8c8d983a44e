{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"children\", \"invertedColors\", \"orientation\", \"color\", \"component\", \"variant\", \"size\", \"layout\", \"maxWidth\", \"minWidth\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport { unstable_capitalize as capitalize, unstable_isMuiElement as isMuiElement, unstable_useId as useId } from '@mui/utils';\nimport { styled, useThemeProps } from '../styles';\nimport { getModalDialogUtilityClass } from './modalDialogClasses';\nimport ModalDialogSizeContext from './ModalDialogSizeContext';\nimport ModalDialogVariantColorContext from './ModalDialogVariantColorContext';\nimport useSlot from '../utils/useSlot';\nimport { StyledCardRoot } from '../Card/Card';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    variant,\n    color,\n    size,\n    layout\n  } = ownerState;\n  const slots = {\n    root: ['root', variant && `variant${capitalize(variant)}`, color && `color${capitalize(color)}`, size && `size${capitalize(size)}`, layout && `layout${capitalize(layout)}`]\n  };\n  return composeClasses(slots, getModalDialogUtilityClass, {});\n};\nfunction getBreakpointValue(theme, breakpoint) {\n  var _theme$breakpoints, _theme$breakpoints2;\n  return breakpoint && (_theme$breakpoints = theme.breakpoints) != null && _theme$breakpoints.values[breakpoint] ? `${(_theme$breakpoints2 = theme.breakpoints) == null ? void 0 : _theme$breakpoints2.values[breakpoint]}px` : breakpoint;\n}\nconst ModalDialogRoot = styled(StyledCardRoot, {\n  name: 'JoyModalDialog',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(_ref => {\n  let {\n    theme,\n    ownerState\n  } = _ref;\n  return _extends({\n    '--ModalDialog-minWidth': typeof ownerState.minWidth === 'number' ? `${ownerState.minWidth}px` : getBreakpointValue(theme, ownerState.minWidth),\n    '--ModalDialog-maxWidth': typeof ownerState.maxWidth === 'number' ? `${ownerState.maxWidth}px` : getBreakpointValue(theme, ownerState.maxWidth),\n    '--ModalClose-radius': 'max((var(--Card-radius) - var(--variant-borderWidth, 0px)) - var(--ModalClose-inset), min(var(--ModalClose-inset) / 2, (var(--Card-radius) - var(--variant-borderWidth, 0px)) / 2))'\n  }, ownerState.variant === 'solid' && {\n    '--DialogContent-color': 'currentColor'\n  }, ownerState.size === 'sm' && {\n    '--Card-padding': '1rem',\n    '--ModalDialog-titleOffset': theme.spacing(0.25),\n    '--ModalDialog-descriptionOffset': theme.spacing(0.25),\n    '--ModalClose-inset': '0.375rem'\n  }, ownerState.size === 'md' && {\n    '--Card-padding': '1.25rem',\n    '--ModalDialog-titleOffset': theme.spacing(0.25),\n    '--ModalDialog-descriptionOffset': theme.spacing(0.75),\n    '--ModalClose-inset': '0.5rem'\n  }, ownerState.size === 'lg' && {\n    '--Card-padding': '1.5rem',\n    '--ModalDialog-titleOffset': theme.spacing(0.5),\n    '--ModalDialog-descriptionOffset': theme.spacing(1),\n    '--ModalClose-inset': '0.625rem'\n  }, {\n    boxSizing: 'border-box',\n    boxShadow: theme.shadow.md,\n    minWidth: 'min(calc(100vw - 2 * var(--Card-padding)), var(--ModalDialog-minWidth, 300px))',\n    outline: 0,\n    position: 'absolute'\n  }, ownerState.layout === 'fullscreen' && {\n    top: 0,\n    left: 0,\n    right: 0,\n    bottom: 0,\n    border: 0,\n    borderRadius: 0\n  }, ownerState.layout === 'center' && {\n    top: '50%',\n    left: '50%',\n    transform: 'translate(-50%, -50%)',\n    maxWidth: 'min(calc(100vw - 2 * var(--Card-padding)), var(--ModalDialog-maxWidth, 100vw))',\n    maxHeight: 'calc(100% - 2 * var(--Card-padding))'\n  }, {\n    [`& [id=\"${ownerState['aria-labelledby']}\"]`]: {\n      '--Typography-margin': 'calc(-1 * var(--ModalDialog-titleOffset)) 0 var(--ModalDialog-gap) 0',\n      '--Typography-fontSize': '1.125em',\n      [`& + [id=\"${ownerState['aria-describedby']}\"]`]: {\n        '--unstable_ModalDialog-descriptionOffset': 'calc(-1 * var(--ModalDialog-descriptionOffset))'\n      }\n    },\n    [`& [id=\"${ownerState['aria-describedby']}\"]`]: {\n      '--Typography-fontSize': '1em',\n      '--Typography-margin': 'var(--unstable_ModalDialog-descriptionOffset, var(--ModalDialog-gap)) 0 0 0',\n      '&:not(:last-child)': {\n        // create spacing between description and the next element.\n        '--Typography-margin': 'var(--unstable_ModalDialog-descriptionOffset, var(--ModalDialog-gap)) 0 var(--ModalDialog-gap) 0'\n      }\n    }\n  });\n});\n/**\n *\n * Demos:\n *\n * - [Modal](https://mui.com/joy-ui/react-modal/)\n *\n * API:\n *\n * - [ModalDialog API](https://mui.com/joy-ui/api/modal-dialog/)\n */\nconst ModalDialog = /*#__PURE__*/React.forwardRef(function ModalDialog(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyModalDialog'\n  });\n  const {\n      className,\n      children,\n      invertedColors = false,\n      orientation = 'vertical',\n      color = 'neutral',\n      component = 'div',\n      variant = 'outlined',\n      size = 'md',\n      layout = 'center',\n      maxWidth,\n      minWidth,\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    color,\n    component,\n    maxWidth,\n    minWidth,\n    layout,\n    size,\n    variant,\n    invertedColors\n  });\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const labelledBy = useId();\n  const describedBy = useId();\n  const contextValue = React.useMemo(() => ({\n    variant,\n    color,\n    labelledBy,\n    describedBy\n  }), [color, variant, labelledBy, describedBy]);\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    className: clsx(classes.root, className),\n    elementType: ModalDialogRoot,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      as: component,\n      role: 'dialog',\n      'aria-modal': 'true',\n      'aria-labelledby': labelledBy,\n      'aria-describedby': describedBy\n    }\n  });\n  return /*#__PURE__*/_jsx(ModalDialogSizeContext.Provider, {\n    value: size,\n    children: /*#__PURE__*/_jsx(ModalDialogVariantColorContext.Provider, {\n      value: contextValue,\n      children: /*#__PURE__*/_jsx(SlotRoot, _extends({}, rootProps, {\n        children: React.Children.map(children, (child, index) => {\n          if (! /*#__PURE__*/React.isValidElement(child)) {\n            return child;\n          }\n          const extraProps = {};\n          if (isMuiElement(child, ['Divider'])) {\n            var _childProps$inset, _childProps$orientati;\n            const childProps = child.props;\n            extraProps.inset = (_childProps$inset = childProps == null ? void 0 : childProps.inset) != null ? _childProps$inset : 'context';\n            const dividerOrientation = orientation === 'vertical' ? 'horizontal' : 'vertical';\n            extraProps.orientation = (_childProps$orientati = childProps == null ? void 0 : childProps.orientation) != null ? _childProps$orientati : dividerOrientation;\n          }\n          if (index === 0) {\n            extraProps['data-first-child'] = '';\n          }\n          if (index === React.Children.count(children) - 1) {\n            extraProps['data-last-child'] = '';\n          }\n          return /*#__PURE__*/React.cloneElement(child, extraProps);\n        })\n      }))\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? ModalDialog.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component. It supports those theme colors that make sense for this component.\n   * @default 'neutral'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['danger', 'neutral', 'primary', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the children with an implicit color prop invert their colors to match the component's variant and color.\n   * @default false\n   */\n  invertedColors: PropTypes.bool,\n  /**\n   * The layout of the dialog\n   * @default 'center'\n   */\n  layout: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['center', 'fullscreen']), PropTypes.string]),\n  /**\n   * The maximum width of the component.\n   * @example 'md' will use the theme's `md` breakpoint value\n   * @example 360 is the number of pixels\n   * @example '60ch' can be any valid CSS max-width unit\n   */\n  maxWidth: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  /**\n   * The minimum width of the component.\n   * @example 'md' will use the theme's `md` breakpoint value\n   * @example 360 is the number of pixels\n   * @example '60ch' can be any valid CSS min-width unit\n   */\n  minWidth: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  /**\n   * The component orientation.\n   * @default 'vertical'\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']),\n  /**\n   * The size of the component.\n   * @default 'md'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['sm', 'md', 'lg']), PropTypes.string]),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The [global variant](https://mui.com/joy-ui/main-features/global-variants/) to use.\n   * @default 'outlined'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['outlined', 'plain', 'soft', 'solid']), PropTypes.string])\n} : void 0;\nexport default ModalDialog;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "unstable_composeClasses", "composeClasses", "unstable_capitalize", "capitalize", "unstable_isMuiElement", "isMuiElement", "unstable_useId", "useId", "styled", "useThemeProps", "getModalDialogUtilityClass", "ModalDialogSizeContext", "ModalDialogVariantColorContext", "useSlot", "StyledCardRoot", "jsx", "_jsx", "useUtilityClasses", "ownerState", "variant", "color", "size", "layout", "slots", "root", "getBreakpointValue", "theme", "breakpoint", "_theme$breakpoints", "_theme$breakpoints2", "breakpoints", "values", "ModalDialogRoot", "name", "slot", "overridesResolver", "props", "styles", "_ref", "min<PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON>", "spacing", "boxSizing", "boxShadow", "shadow", "md", "outline", "position", "top", "left", "right", "bottom", "border", "borderRadius", "transform", "maxHeight", "ModalDialog", "forwardRef", "inProps", "ref", "className", "children", "invertedColors", "orientation", "component", "slotProps", "other", "classes", "externalForwardedProps", "labelledBy", "describedBy", "contextValue", "useMemo", "SlotRoot", "rootProps", "elementType", "additionalProps", "as", "role", "Provider", "value", "Children", "map", "child", "index", "isValidElement", "extraProps", "_childProps$inset", "_childProps$orientati", "childProps", "inset", "dividerOrientation", "count", "cloneElement", "process", "env", "NODE_ENV", "propTypes", "node", "string", "oneOfType", "oneOf", "bool", "number", "shape", "func", "object", "sx", "arrayOf"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/ModalDialog/ModalDialog.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"children\", \"invertedColors\", \"orientation\", \"color\", \"component\", \"variant\", \"size\", \"layout\", \"maxWidth\", \"minWidth\", \"slots\", \"slotProps\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport { unstable_composeClasses as composeClasses } from '@mui/base';\nimport { unstable_capitalize as capitalize, unstable_isMuiElement as isMuiElement, unstable_useId as useId } from '@mui/utils';\nimport { styled, useThemeProps } from '../styles';\nimport { getModalDialogUtilityClass } from './modalDialogClasses';\nimport ModalDialogSizeContext from './ModalDialogSizeContext';\nimport ModalDialogVariantColorContext from './ModalDialogVariantColorContext';\nimport useSlot from '../utils/useSlot';\nimport { StyledCardRoot } from '../Card/Card';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    variant,\n    color,\n    size,\n    layout\n  } = ownerState;\n  const slots = {\n    root: ['root', variant && `variant${capitalize(variant)}`, color && `color${capitalize(color)}`, size && `size${capitalize(size)}`, layout && `layout${capitalize(layout)}`]\n  };\n  return composeClasses(slots, getModalDialogUtilityClass, {});\n};\nfunction getBreakpointValue(theme, breakpoint) {\n  var _theme$breakpoints, _theme$breakpoints2;\n  return breakpoint && (_theme$breakpoints = theme.breakpoints) != null && _theme$breakpoints.values[breakpoint] ? `${(_theme$breakpoints2 = theme.breakpoints) == null ? void 0 : _theme$breakpoints2.values[breakpoint]}px` : breakpoint;\n}\nconst ModalDialogRoot = styled(StyledCardRoot, {\n  name: 'JoyModalDialog',\n  slot: 'Root',\n  overridesResolver: (props, styles) => styles.root\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  '--ModalDialog-minWidth': typeof ownerState.minWidth === 'number' ? `${ownerState.minWidth}px` : getBreakpointValue(theme, ownerState.minWidth),\n  '--ModalDialog-maxWidth': typeof ownerState.maxWidth === 'number' ? `${ownerState.maxWidth}px` : getBreakpointValue(theme, ownerState.maxWidth),\n  '--ModalClose-radius': 'max((var(--Card-radius) - var(--variant-borderWidth, 0px)) - var(--ModalClose-inset), min(var(--ModalClose-inset) / 2, (var(--Card-radius) - var(--variant-borderWidth, 0px)) / 2))'\n}, ownerState.variant === 'solid' && {\n  '--DialogContent-color': 'currentColor'\n}, ownerState.size === 'sm' && {\n  '--Card-padding': '1rem',\n  '--ModalDialog-titleOffset': theme.spacing(0.25),\n  '--ModalDialog-descriptionOffset': theme.spacing(0.25),\n  '--ModalClose-inset': '0.375rem'\n}, ownerState.size === 'md' && {\n  '--Card-padding': '1.25rem',\n  '--ModalDialog-titleOffset': theme.spacing(0.25),\n  '--ModalDialog-descriptionOffset': theme.spacing(0.75),\n  '--ModalClose-inset': '0.5rem'\n}, ownerState.size === 'lg' && {\n  '--Card-padding': '1.5rem',\n  '--ModalDialog-titleOffset': theme.spacing(0.5),\n  '--ModalDialog-descriptionOffset': theme.spacing(1),\n  '--ModalClose-inset': '0.625rem'\n}, {\n  boxSizing: 'border-box',\n  boxShadow: theme.shadow.md,\n  minWidth: 'min(calc(100vw - 2 * var(--Card-padding)), var(--ModalDialog-minWidth, 300px))',\n  outline: 0,\n  position: 'absolute'\n}, ownerState.layout === 'fullscreen' && {\n  top: 0,\n  left: 0,\n  right: 0,\n  bottom: 0,\n  border: 0,\n  borderRadius: 0\n}, ownerState.layout === 'center' && {\n  top: '50%',\n  left: '50%',\n  transform: 'translate(-50%, -50%)',\n  maxWidth: 'min(calc(100vw - 2 * var(--Card-padding)), var(--ModalDialog-maxWidth, 100vw))',\n  maxHeight: 'calc(100% - 2 * var(--Card-padding))'\n}, {\n  [`& [id=\"${ownerState['aria-labelledby']}\"]`]: {\n    '--Typography-margin': 'calc(-1 * var(--ModalDialog-titleOffset)) 0 var(--ModalDialog-gap) 0',\n    '--Typography-fontSize': '1.125em',\n    [`& + [id=\"${ownerState['aria-describedby']}\"]`]: {\n      '--unstable_ModalDialog-descriptionOffset': 'calc(-1 * var(--ModalDialog-descriptionOffset))'\n    }\n  },\n  [`& [id=\"${ownerState['aria-describedby']}\"]`]: {\n    '--Typography-fontSize': '1em',\n    '--Typography-margin': 'var(--unstable_ModalDialog-descriptionOffset, var(--ModalDialog-gap)) 0 0 0',\n    '&:not(:last-child)': {\n      // create spacing between description and the next element.\n      '--Typography-margin': 'var(--unstable_ModalDialog-descriptionOffset, var(--ModalDialog-gap)) 0 var(--ModalDialog-gap) 0'\n    }\n  }\n}));\n/**\n *\n * Demos:\n *\n * - [Modal](https://mui.com/joy-ui/react-modal/)\n *\n * API:\n *\n * - [ModalDialog API](https://mui.com/joy-ui/api/modal-dialog/)\n */\nconst ModalDialog = /*#__PURE__*/React.forwardRef(function ModalDialog(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'JoyModalDialog'\n  });\n  const {\n      className,\n      children,\n      invertedColors = false,\n      orientation = 'vertical',\n      color = 'neutral',\n      component = 'div',\n      variant = 'outlined',\n      size = 'md',\n      layout = 'center',\n      maxWidth,\n      minWidth,\n      slots = {},\n      slotProps = {}\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    color,\n    component,\n    maxWidth,\n    minWidth,\n    layout,\n    size,\n    variant,\n    invertedColors\n  });\n  const classes = useUtilityClasses(ownerState);\n  const externalForwardedProps = _extends({}, other, {\n    component,\n    slots,\n    slotProps\n  });\n  const labelledBy = useId();\n  const describedBy = useId();\n  const contextValue = React.useMemo(() => ({\n    variant,\n    color,\n    labelledBy,\n    describedBy\n  }), [color, variant, labelledBy, describedBy]);\n  const [SlotRoot, rootProps] = useSlot('root', {\n    ref,\n    className: clsx(classes.root, className),\n    elementType: ModalDialogRoot,\n    externalForwardedProps,\n    ownerState,\n    additionalProps: {\n      as: component,\n      role: 'dialog',\n      'aria-modal': 'true',\n      'aria-labelledby': labelledBy,\n      'aria-describedby': describedBy\n    }\n  });\n  return /*#__PURE__*/_jsx(ModalDialogSizeContext.Provider, {\n    value: size,\n    children: /*#__PURE__*/_jsx(ModalDialogVariantColorContext.Provider, {\n      value: contextValue,\n      children: /*#__PURE__*/_jsx(SlotRoot, _extends({}, rootProps, {\n        children: React.Children.map(children, (child, index) => {\n          if (! /*#__PURE__*/React.isValidElement(child)) {\n            return child;\n          }\n          const extraProps = {};\n          if (isMuiElement(child, ['Divider'])) {\n            var _childProps$inset, _childProps$orientati;\n            const childProps = child.props;\n            extraProps.inset = (_childProps$inset = childProps == null ? void 0 : childProps.inset) != null ? _childProps$inset : 'context';\n            const dividerOrientation = orientation === 'vertical' ? 'horizontal' : 'vertical';\n            extraProps.orientation = (_childProps$orientati = childProps == null ? void 0 : childProps.orientation) != null ? _childProps$orientati : dividerOrientation;\n          }\n          if (index === 0) {\n            extraProps['data-first-child'] = '';\n          }\n          if (index === React.Children.count(children) - 1) {\n            extraProps['data-last-child'] = '';\n          }\n          return /*#__PURE__*/React.cloneElement(child, extraProps);\n        })\n      }))\n    })\n  });\n});\nprocess.env.NODE_ENV !== \"production\" ? ModalDialog.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The color of the component. It supports those theme colors that make sense for this component.\n   * @default 'neutral'\n   */\n  color: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['danger', 'neutral', 'primary', 'success', 'warning']), PropTypes.string]),\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the children with an implicit color prop invert their colors to match the component's variant and color.\n   * @default false\n   */\n  invertedColors: PropTypes.bool,\n  /**\n   * The layout of the dialog\n   * @default 'center'\n   */\n  layout: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['center', 'fullscreen']), PropTypes.string]),\n  /**\n   * The maximum width of the component.\n   * @example 'md' will use the theme's `md` breakpoint value\n   * @example 360 is the number of pixels\n   * @example '60ch' can be any valid CSS max-width unit\n   */\n  maxWidth: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  /**\n   * The minimum width of the component.\n   * @example 'md' will use the theme's `md` breakpoint value\n   * @example 360 is the number of pixels\n   * @example '60ch' can be any valid CSS min-width unit\n   */\n  minWidth: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),\n  /**\n   * The component orientation.\n   * @default 'vertical'\n   */\n  orientation: PropTypes.oneOf(['horizontal', 'vertical']),\n  /**\n   * The size of the component.\n   * @default 'md'\n   */\n  size: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['sm', 'md', 'lg']), PropTypes.string]),\n  /**\n   * The props used for each slot inside.\n   * @default {}\n   */\n  slotProps: PropTypes.shape({\n    root: PropTypes.oneOfType([PropTypes.func, PropTypes.object])\n  }),\n  /**\n   * The components used for each slot inside.\n   * @default {}\n   */\n  slots: PropTypes.shape({\n    root: PropTypes.elementType\n  }),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The [global variant](https://mui.com/joy-ui/main-features/global-variants/) to use.\n   * @default 'outlined'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['outlined', 'plain', 'soft', 'solid']), PropTypes.string])\n} : void 0;\nexport default ModalDialog;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,gBAAgB,EAAE,aAAa,EAAE,OAAO,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,WAAW,CAAC;AAC7K,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,SAASC,uBAAuB,IAAIC,cAAc,QAAQ,WAAW;AACrE,SAASC,mBAAmB,IAAIC,UAAU,EAAEC,qBAAqB,IAAIC,YAAY,EAAEC,cAAc,IAAIC,KAAK,QAAQ,YAAY;AAC9H,SAASC,MAAM,EAAEC,aAAa,QAAQ,WAAW;AACjD,SAASC,0BAA0B,QAAQ,sBAAsB;AACjE,OAAOC,sBAAsB,MAAM,0BAA0B;AAC7D,OAAOC,8BAA8B,MAAM,kCAAkC;AAC7E,OAAOC,OAAO,MAAM,kBAAkB;AACtC,SAASC,cAAc,QAAQ,cAAc;AAC7C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC,KAAK;IACLC,IAAI;IACJC;EACF,CAAC,GAAGJ,UAAU;EACd,MAAMK,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEL,OAAO,IAAI,UAAUhB,UAAU,CAACgB,OAAO,CAAC,EAAE,EAAEC,KAAK,IAAI,QAAQjB,UAAU,CAACiB,KAAK,CAAC,EAAE,EAAEC,IAAI,IAAI,OAAOlB,UAAU,CAACkB,IAAI,CAAC,EAAE,EAAEC,MAAM,IAAI,SAASnB,UAAU,CAACmB,MAAM,CAAC,EAAE;EAC7K,CAAC;EACD,OAAOrB,cAAc,CAACsB,KAAK,EAAEb,0BAA0B,EAAE,CAAC,CAAC,CAAC;AAC9D,CAAC;AACD,SAASe,kBAAkBA,CAACC,KAAK,EAAEC,UAAU,EAAE;EAC7C,IAAIC,kBAAkB,EAAEC,mBAAmB;EAC3C,OAAOF,UAAU,IAAI,CAACC,kBAAkB,GAAGF,KAAK,CAACI,WAAW,KAAK,IAAI,IAAIF,kBAAkB,CAACG,MAAM,CAACJ,UAAU,CAAC,GAAG,GAAG,CAACE,mBAAmB,GAAGH,KAAK,CAACI,WAAW,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,mBAAmB,CAACE,MAAM,CAACJ,UAAU,CAAC,IAAI,GAAGA,UAAU;AAC1O;AACA,MAAMK,eAAe,GAAGxB,MAAM,CAACM,cAAc,EAAE;EAC7CmB,IAAI,EAAE,gBAAgB;EACtBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACb;AAC/C,CAAC,CAAC,CAACc,IAAA;EAAA,IAAC;IACFZ,KAAK;IACLR;EACF,CAAC,GAAAoB,IAAA;EAAA,OAAK3C,QAAQ,CAAC;IACb,wBAAwB,EAAE,OAAOuB,UAAU,CAACqB,QAAQ,KAAK,QAAQ,GAAG,GAAGrB,UAAU,CAACqB,QAAQ,IAAI,GAAGd,kBAAkB,CAACC,KAAK,EAAER,UAAU,CAACqB,QAAQ,CAAC;IAC/I,wBAAwB,EAAE,OAAOrB,UAAU,CAACsB,QAAQ,KAAK,QAAQ,GAAG,GAAGtB,UAAU,CAACsB,QAAQ,IAAI,GAAGf,kBAAkB,CAACC,KAAK,EAAER,UAAU,CAACsB,QAAQ,CAAC;IAC/I,qBAAqB,EAAE;EACzB,CAAC,EAAEtB,UAAU,CAACC,OAAO,KAAK,OAAO,IAAI;IACnC,uBAAuB,EAAE;EAC3B,CAAC,EAAED,UAAU,CAACG,IAAI,KAAK,IAAI,IAAI;IAC7B,gBAAgB,EAAE,MAAM;IACxB,2BAA2B,EAAEK,KAAK,CAACe,OAAO,CAAC,IAAI,CAAC;IAChD,iCAAiC,EAAEf,KAAK,CAACe,OAAO,CAAC,IAAI,CAAC;IACtD,oBAAoB,EAAE;EACxB,CAAC,EAAEvB,UAAU,CAACG,IAAI,KAAK,IAAI,IAAI;IAC7B,gBAAgB,EAAE,SAAS;IAC3B,2BAA2B,EAAEK,KAAK,CAACe,OAAO,CAAC,IAAI,CAAC;IAChD,iCAAiC,EAAEf,KAAK,CAACe,OAAO,CAAC,IAAI,CAAC;IACtD,oBAAoB,EAAE;EACxB,CAAC,EAAEvB,UAAU,CAACG,IAAI,KAAK,IAAI,IAAI;IAC7B,gBAAgB,EAAE,QAAQ;IAC1B,2BAA2B,EAAEK,KAAK,CAACe,OAAO,CAAC,GAAG,CAAC;IAC/C,iCAAiC,EAAEf,KAAK,CAACe,OAAO,CAAC,CAAC,CAAC;IACnD,oBAAoB,EAAE;EACxB,CAAC,EAAE;IACDC,SAAS,EAAE,YAAY;IACvBC,SAAS,EAAEjB,KAAK,CAACkB,MAAM,CAACC,EAAE;IAC1BN,QAAQ,EAAE,gFAAgF;IAC1FO,OAAO,EAAE,CAAC;IACVC,QAAQ,EAAE;EACZ,CAAC,EAAE7B,UAAU,CAACI,MAAM,KAAK,YAAY,IAAI;IACvC0B,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE,CAAC;IACTC,YAAY,EAAE;EAChB,CAAC,EAAEnC,UAAU,CAACI,MAAM,KAAK,QAAQ,IAAI;IACnC0B,GAAG,EAAE,KAAK;IACVC,IAAI,EAAE,KAAK;IACXK,SAAS,EAAE,uBAAuB;IAClCd,QAAQ,EAAE,gFAAgF;IAC1Fe,SAAS,EAAE;EACb,CAAC,EAAE;IACD,CAAC,UAAUrC,UAAU,CAAC,iBAAiB,CAAC,IAAI,GAAG;MAC7C,qBAAqB,EAAE,sEAAsE;MAC7F,uBAAuB,EAAE,SAAS;MAClC,CAAC,YAAYA,UAAU,CAAC,kBAAkB,CAAC,IAAI,GAAG;QAChD,0CAA0C,EAAE;MAC9C;IACF,CAAC;IACD,CAAC,UAAUA,UAAU,CAAC,kBAAkB,CAAC,IAAI,GAAG;MAC9C,uBAAuB,EAAE,KAAK;MAC9B,qBAAqB,EAAE,6EAA6E;MACpG,oBAAoB,EAAE;QACpB;QACA,qBAAqB,EAAE;MACzB;IACF;EACF,CAAC,CAAC;AAAA,EAAC;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsC,WAAW,GAAG,aAAa3D,KAAK,CAAC4D,UAAU,CAAC,SAASD,WAAWA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACnF,MAAMvB,KAAK,GAAG3B,aAAa,CAAC;IAC1B2B,KAAK,EAAEsB,OAAO;IACdzB,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACF2B,SAAS;MACTC,QAAQ;MACRC,cAAc,GAAG,KAAK;MACtBC,WAAW,GAAG,UAAU;MACxB3C,KAAK,GAAG,SAAS;MACjB4C,SAAS,GAAG,KAAK;MACjB7C,OAAO,GAAG,UAAU;MACpBE,IAAI,GAAG,IAAI;MACXC,MAAM,GAAG,QAAQ;MACjBkB,QAAQ;MACRD,QAAQ;MACRhB,KAAK,GAAG,CAAC,CAAC;MACV0C,SAAS,GAAG,CAAC;IACf,CAAC,GAAG7B,KAAK;IACT8B,KAAK,GAAGxE,6BAA6B,CAAC0C,KAAK,EAAExC,SAAS,CAAC;EACzD,MAAMsB,UAAU,GAAGvB,QAAQ,CAAC,CAAC,CAAC,EAAEyC,KAAK,EAAE;IACrChB,KAAK;IACL4C,SAAS;IACTxB,QAAQ;IACRD,QAAQ;IACRjB,MAAM;IACND,IAAI;IACJF,OAAO;IACP2C;EACF,CAAC,CAAC;EACF,MAAMK,OAAO,GAAGlD,iBAAiB,CAACC,UAAU,CAAC;EAC7C,MAAMkD,sBAAsB,GAAGzE,QAAQ,CAAC,CAAC,CAAC,EAAEuE,KAAK,EAAE;IACjDF,SAAS;IACTzC,KAAK;IACL0C;EACF,CAAC,CAAC;EACF,MAAMI,UAAU,GAAG9D,KAAK,CAAC,CAAC;EAC1B,MAAM+D,WAAW,GAAG/D,KAAK,CAAC,CAAC;EAC3B,MAAMgE,YAAY,GAAG1E,KAAK,CAAC2E,OAAO,CAAC,OAAO;IACxCrD,OAAO;IACPC,KAAK;IACLiD,UAAU;IACVC;EACF,CAAC,CAAC,EAAE,CAAClD,KAAK,EAAED,OAAO,EAAEkD,UAAU,EAAEC,WAAW,CAAC,CAAC;EAC9C,MAAM,CAACG,QAAQ,EAAEC,SAAS,CAAC,GAAG7D,OAAO,CAAC,MAAM,EAAE;IAC5C8C,GAAG;IACHC,SAAS,EAAE7D,IAAI,CAACoE,OAAO,CAAC3C,IAAI,EAAEoC,SAAS,CAAC;IACxCe,WAAW,EAAE3C,eAAe;IAC5BoC,sBAAsB;IACtBlD,UAAU;IACV0D,eAAe,EAAE;MACfC,EAAE,EAAEb,SAAS;MACbc,IAAI,EAAE,QAAQ;MACd,YAAY,EAAE,MAAM;MACpB,iBAAiB,EAAET,UAAU;MAC7B,kBAAkB,EAAEC;IACtB;EACF,CAAC,CAAC;EACF,OAAO,aAAatD,IAAI,CAACL,sBAAsB,CAACoE,QAAQ,EAAE;IACxDC,KAAK,EAAE3D,IAAI;IACXwC,QAAQ,EAAE,aAAa7C,IAAI,CAACJ,8BAA8B,CAACmE,QAAQ,EAAE;MACnEC,KAAK,EAAET,YAAY;MACnBV,QAAQ,EAAE,aAAa7C,IAAI,CAACyD,QAAQ,EAAE9E,QAAQ,CAAC,CAAC,CAAC,EAAE+E,SAAS,EAAE;QAC5Db,QAAQ,EAAEhE,KAAK,CAACoF,QAAQ,CAACC,GAAG,CAACrB,QAAQ,EAAE,CAACsB,KAAK,EAAEC,KAAK,KAAK;UACvD,IAAI,EAAE,aAAavF,KAAK,CAACwF,cAAc,CAACF,KAAK,CAAC,EAAE;YAC9C,OAAOA,KAAK;UACd;UACA,MAAMG,UAAU,GAAG,CAAC,CAAC;UACrB,IAAIjF,YAAY,CAAC8E,KAAK,EAAE,CAAC,SAAS,CAAC,CAAC,EAAE;YACpC,IAAII,iBAAiB,EAAEC,qBAAqB;YAC5C,MAAMC,UAAU,GAAGN,KAAK,CAAC/C,KAAK;YAC9BkD,UAAU,CAACI,KAAK,GAAG,CAACH,iBAAiB,GAAGE,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACC,KAAK,KAAK,IAAI,GAAGH,iBAAiB,GAAG,SAAS;YAC/H,MAAMI,kBAAkB,GAAG5B,WAAW,KAAK,UAAU,GAAG,YAAY,GAAG,UAAU;YACjFuB,UAAU,CAACvB,WAAW,GAAG,CAACyB,qBAAqB,GAAGC,UAAU,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC1B,WAAW,KAAK,IAAI,GAAGyB,qBAAqB,GAAGG,kBAAkB;UAC9J;UACA,IAAIP,KAAK,KAAK,CAAC,EAAE;YACfE,UAAU,CAAC,kBAAkB,CAAC,GAAG,EAAE;UACrC;UACA,IAAIF,KAAK,KAAKvF,KAAK,CAACoF,QAAQ,CAACW,KAAK,CAAC/B,QAAQ,CAAC,GAAG,CAAC,EAAE;YAChDyB,UAAU,CAAC,iBAAiB,CAAC,GAAG,EAAE;UACpC;UACA,OAAO,aAAazF,KAAK,CAACgG,YAAY,CAACV,KAAK,EAAEG,UAAU,CAAC;QAC3D,CAAC;MACH,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,CAAC;AACJ,CAAC,CAAC;AACFQ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGxC,WAAW,CAACyC,SAAS,CAAC,yBAAyB;EACrF;EACA;EACA;EACA;EACA;AACF;AACA;EACEpC,QAAQ,EAAE/D,SAAS,CAACoG,IAAI;EACxB;AACF;AACA;EACEtC,SAAS,EAAE9D,SAAS,CAACqG,MAAM;EAC3B;AACF;AACA;AACA;EACE/E,KAAK,EAAEtB,SAAS,CAAC,sCAAsCsG,SAAS,CAAC,CAACtG,SAAS,CAACuG,KAAK,CAAC,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,EAAEvG,SAAS,CAACqG,MAAM,CAAC,CAAC;EAC7J;AACF;AACA;AACA;EACEnC,SAAS,EAAElE,SAAS,CAAC6E,WAAW;EAChC;AACF;AACA;AACA;EACEb,cAAc,EAAEhE,SAAS,CAACwG,IAAI;EAC9B;AACF;AACA;AACA;EACEhF,MAAM,EAAExB,SAAS,CAAC,sCAAsCsG,SAAS,CAAC,CAACtG,SAAS,CAACuG,KAAK,CAAC,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC,EAAEvG,SAAS,CAACqG,MAAM,CAAC,CAAC;EAChI;AACF;AACA;AACA;AACA;AACA;EACE3D,QAAQ,EAAE1C,SAAS,CAACsG,SAAS,CAAC,CAACtG,SAAS,CAACyG,MAAM,EAAEzG,SAAS,CAACqG,MAAM,CAAC,CAAC;EACnE;AACF;AACA;AACA;AACA;AACA;EACE5D,QAAQ,EAAEzC,SAAS,CAACsG,SAAS,CAAC,CAACtG,SAAS,CAACyG,MAAM,EAAEzG,SAAS,CAACqG,MAAM,CAAC,CAAC;EACnE;AACF;AACA;AACA;EACEpC,WAAW,EAAEjE,SAAS,CAACuG,KAAK,CAAC,CAAC,YAAY,EAAE,UAAU,CAAC,CAAC;EACxD;AACF;AACA;AACA;EACEhF,IAAI,EAAEvB,SAAS,CAAC,sCAAsCsG,SAAS,CAAC,CAACtG,SAAS,CAACuG,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,EAAEvG,SAAS,CAACqG,MAAM,CAAC,CAAC;EACxH;AACF;AACA;AACA;EACElC,SAAS,EAAEnE,SAAS,CAAC0G,KAAK,CAAC;IACzBhF,IAAI,EAAE1B,SAAS,CAACsG,SAAS,CAAC,CAACtG,SAAS,CAAC2G,IAAI,EAAE3G,SAAS,CAAC4G,MAAM,CAAC;EAC9D,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEnF,KAAK,EAAEzB,SAAS,CAAC0G,KAAK,CAAC;IACrBhF,IAAI,EAAE1B,SAAS,CAAC6E;EAClB,CAAC,CAAC;EACF;AACF;AACA;EACEgC,EAAE,EAAE7G,SAAS,CAACsG,SAAS,CAAC,CAACtG,SAAS,CAAC8G,OAAO,CAAC9G,SAAS,CAACsG,SAAS,CAAC,CAACtG,SAAS,CAAC2G,IAAI,EAAE3G,SAAS,CAAC4G,MAAM,EAAE5G,SAAS,CAACwG,IAAI,CAAC,CAAC,CAAC,EAAExG,SAAS,CAAC2G,IAAI,EAAE3G,SAAS,CAAC4G,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;EACEvF,OAAO,EAAErB,SAAS,CAAC,sCAAsCsG,SAAS,CAAC,CAACtG,SAAS,CAACuG,KAAK,CAAC,CAAC,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,EAAEvG,SAAS,CAACqG,MAAM,CAAC;AAChJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAe3C,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}