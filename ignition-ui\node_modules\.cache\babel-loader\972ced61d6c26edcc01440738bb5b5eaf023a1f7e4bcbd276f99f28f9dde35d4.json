{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getIconButtonUtilityClass(slot) {\n  return generateUtilityClass('MuiIconButton', slot);\n}\nconst iconButtonClasses = generateUtilityClasses('MuiIconButton', ['root', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid', 'focusVisible', 'disabled', 'sizeSm', 'sizeMd', 'sizeLg', 'loading', 'loadingIndicator']);\nexport default iconButtonClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getIconButtonUtilityClass", "slot", "iconButtonClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/IconButton/iconButtonClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getIconButtonUtilityClass(slot) {\n  return generateUtilityClass('MuiIconButton', slot);\n}\nconst iconButtonClasses = generateUtilityClasses('MuiIconButton', ['root', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid', 'focusVisible', 'disabled', 'sizeSm', 'sizeMd', 'sizeLg', 'loading', 'loadingIndicator']);\nexport default iconButtonClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,yBAAyBA,CAACC,IAAI,EAAE;EAC9C,OAAOH,oBAAoB,CAAC,eAAe,EAAEG,IAAI,CAAC;AACpD;AACA,MAAMC,iBAAiB,GAAGH,sBAAsB,CAAC,eAAe,EAAE,CAAC,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,iBAAiB,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,kBAAkB,CAAC,CAAC;AACrU,eAAeG,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}