{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getMenuUtilityClass(slot) {\n  return generateUtilityClass('MuiMenu', slot);\n}\nconst menuClasses = generateUtilityClasses('MuiMenu', ['root', 'listbox', 'expanded', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid', 'sizeSm', 'sizeMd', 'sizeLg']);\nexport default menuClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getMenuUtilityClass", "slot", "menuClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Menu/menuClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getMenuUtilityClass(slot) {\n  return generateUtilityClass('MuiMenu', slot);\n}\nconst menuClasses = generateUtilityClasses('MuiMenu', ['root', 'listbox', 'expanded', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'colorContext', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid', 'sizeSm', 'sizeMd', 'sizeLg']);\nexport default menuClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,mBAAmBA,CAACC,IAAI,EAAE;EACxC,OAAOH,oBAAoB,CAAC,SAAS,EAAEG,IAAI,CAAC;AAC9C;AACA,MAAMC,WAAW,GAAGH,sBAAsB,CAAC,SAAS,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,iBAAiB,EAAE,aAAa,EAAE,cAAc,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;AACrR,eAAeG,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}