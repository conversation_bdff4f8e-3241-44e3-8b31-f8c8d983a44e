{"ast": null, "code": "'use strict';\n\nvar setFunctionName = require('set-function-name');\nvar $TypeError = require('es-errors/type');\nvar $Object = Object;\nmodule.exports = setFunctionName(function flags() {\n  if (this == null || this !== $Object(this)) {\n    throw new $TypeError('RegExp.prototype.flags getter called on non-object');\n  }\n  var result = '';\n  if (this.hasIndices) {\n    result += 'd';\n  }\n  if (this.global) {\n    result += 'g';\n  }\n  if (this.ignoreCase) {\n    result += 'i';\n  }\n  if (this.multiline) {\n    result += 'm';\n  }\n  if (this.dotAll) {\n    result += 's';\n  }\n  if (this.unicode) {\n    result += 'u';\n  }\n  if (this.unicodeSets) {\n    result += 'v';\n  }\n  if (this.sticky) {\n    result += 'y';\n  }\n  return result;\n}, 'get flags', true);", "map": {"version": 3, "names": ["setFunctionName", "require", "$TypeError", "$Object", "Object", "module", "exports", "flags", "result", "hasIndices", "global", "ignoreCase", "multiline", "dotAll", "unicode", "unicodeSets", "sticky"], "sources": ["C:/ignition/ignition-ui/node_modules/regexp.prototype.flags/implementation.js"], "sourcesContent": ["'use strict';\n\nvar setFunctionName = require('set-function-name');\nvar $TypeError = require('es-errors/type');\n\nvar $Object = Object;\n\nmodule.exports = setFunctionName(function flags() {\n\tif (this == null || this !== $Object(this)) {\n\t\tthrow new $TypeError('RegExp.prototype.flags getter called on non-object');\n\t}\n\tvar result = '';\n\tif (this.hasIndices) {\n\t\tresult += 'd';\n\t}\n\tif (this.global) {\n\t\tresult += 'g';\n\t}\n\tif (this.ignoreCase) {\n\t\tresult += 'i';\n\t}\n\tif (this.multiline) {\n\t\tresult += 'm';\n\t}\n\tif (this.dotAll) {\n\t\tresult += 's';\n\t}\n\tif (this.unicode) {\n\t\tresult += 'u';\n\t}\n\tif (this.unicodeSets) {\n\t\tresult += 'v';\n\t}\n\tif (this.sticky) {\n\t\tresult += 'y';\n\t}\n\treturn result;\n}, 'get flags', true);\n\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,eAAe,GAAGC,OAAO,CAAC,mBAAmB,CAAC;AAClD,IAAIC,UAAU,GAAGD,OAAO,CAAC,gBAAgB,CAAC;AAE1C,IAAIE,OAAO,GAAGC,MAAM;AAEpBC,MAAM,CAACC,OAAO,GAAGN,eAAe,CAAC,SAASO,KAAKA,CAAA,EAAG;EACjD,IAAI,IAAI,IAAI,IAAI,IAAI,IAAI,KAAKJ,OAAO,CAAC,IAAI,CAAC,EAAE;IAC3C,MAAM,IAAID,UAAU,CAAC,oDAAoD,CAAC;EAC3E;EACA,IAAIM,MAAM,GAAG,EAAE;EACf,IAAI,IAAI,CAACC,UAAU,EAAE;IACpBD,MAAM,IAAI,GAAG;EACd;EACA,IAAI,IAAI,CAACE,MAAM,EAAE;IAChBF,MAAM,IAAI,GAAG;EACd;EACA,IAAI,IAAI,CAACG,UAAU,EAAE;IACpBH,MAAM,IAAI,GAAG;EACd;EACA,IAAI,IAAI,CAACI,SAAS,EAAE;IACnBJ,MAAM,IAAI,GAAG;EACd;EACA,IAAI,IAAI,CAACK,MAAM,EAAE;IAChBL,MAAM,IAAI,GAAG;EACd;EACA,IAAI,IAAI,CAACM,OAAO,EAAE;IACjBN,MAAM,IAAI,GAAG;EACd;EACA,IAAI,IAAI,CAACO,WAAW,EAAE;IACrBP,MAAM,IAAI,GAAG;EACd;EACA,IAAI,IAAI,CAACQ,MAAM,EAAE;IAChBR,MAAM,IAAI,GAAG;EACd;EACA,OAAOA,MAAM;AACd,CAAC,EAAE,WAAW,EAAE,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}