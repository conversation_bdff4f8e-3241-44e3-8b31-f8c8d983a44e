import os
import logging
from starlette import status

logger = logging.getLogger(__name__)
from drf_yasg import openapi
from drf_yasg.utils import swagger_auto_schema
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.pagination import PageNumberPagination
from plans.serializers import PlanViewSerializer, SubtaskUpdateSerializer, \
    TaskUpdateSerializer, UserSerializer, TaskAssignSerializer, SubtaskSerializer, \
    TaskSerializer, MilestoneSerializer, CheckUserExistsSerializer, \
    AddInvitationSerializer, InvitationSerializer, InvitationUpdateSerializer, \
    CheckInvitationSerializer, PlanAccessSerializer
from plans.models import Plan, Subtask, Task, Milestone, Invitation, PlanAccess
from plans.permissions import IsPlanOwner, canAccessPlan, IsSubtaskPlanOwner, CanUpdatePlanMilestone, \
    IsTaskPlanOwner, canAccessTask, Is<PERSON>wner<PERSON>nly, IsHeadOwnerOnly, IsOwnerOrEditor
from users.models import User
from django.db.models import Q
from django.core import signing
from django.utils import timezone
from django.db import transaction
from django.core.mail import send_mail
from django.shortcuts import get_object_or_404
from django.core.signing import BadSignature, SignatureExpired
from utils.email import send_email_with_name, send_task_assignment_sms
from dotenv import load_dotenv
load_dotenv()


class PlanDetailInfoView(APIView):
    permission_classes = [IsAuthenticated, canAccessPlan]

    def get(self, request, pk):
        if not pk:
            return Response({
                "message": "Invalid plan_id",
            }, status=status.HTTP_400_BAD_REQUEST)

        plan = get_object_or_404(Plan, pk=pk)
        serializer = PlanViewSerializer(plan, context={'request': request})

        return Response({
            "data": serializer.data,
        }, status=status.HTTP_200_OK)


class PlanDetailInformationBySlugView(APIView):
    permission_classes = [IsAuthenticated, canAccessPlan]

    def get(self, request, slug):
        if not slug:
            return Response({
                "message": "Invalid plan_slug_id",
            }, status=status.HTTP_400_BAD_REQUEST)

        plan = get_object_or_404(Plan, slug=slug)
        serializer = PlanViewSerializer(plan, context={'request': request})

        return Response({
            "data": serializer.data,
        }, status=status.HTTP_200_OK)


class UpdateSubTaskView(APIView):
    permission_classes = [IsAuthenticated, IsSubtaskPlanOwner]

    @swagger_auto_schema(request_body=SubtaskUpdateSerializer, responses={200: 'Updated successfully!'})
    def put(self, request, slug):
        subtask = get_object_or_404(Subtask, slug=slug)
        serializer = SubtaskUpdateSerializer(subtask, data=request.data)
        if serializer.is_valid():
            serializer.save()
            return Response(status=status.HTTP_200_OK)
        else:
            return Response({
                "message": serializer.errors,
            }, status=status.HTTP_400_BAD_REQUEST)


class UpdateTaskView(APIView):
    permission_classes = [IsAuthenticated, canAccessTask]

    @swagger_auto_schema(request_body=TaskUpdateSerializer, responses={200: 'Login success'})
    def put(self, request, slug):
        task = get_object_or_404(Task, slug=slug)

        data = request.data.copy()
        serializer = TaskUpdateSerializer(task, data=data)
        if serializer.is_valid():
            if 'start_date' not in data:
                task.start_date = None
            if 'end_date' not in data:
                task.end_date = None
            serializer.save()
            task.save()
            return Response(status=status.HTTP_200_OK)
        else:
            return Response({
                "message": serializer.errors,
            }, status=status.HTTP_400_BAD_REQUEST)


class getPlanUsersView(APIView):
    permission_classes = [IsAuthenticated, canAccessPlan]

    def get(self, request, slug):
        plan = get_object_or_404(Plan, slug=slug)
        assignees = User.objects.filter(is_active=True)
        serializer = UserSerializer(assignees, many=True)
        return Response({
            "data": serializer.data,
            "status": status.HTTP_200_OK
        }, status=status.HTTP_200_OK)


class AssignTaskView(APIView):
    permission_classes = [IsAuthenticated, IsTaskPlanOwner]

    @swagger_auto_schema(
        request_body=TaskAssignSerializer,
        responses={
            200: openapi.Response(description="Assignees updated successfully"),
            400: openapi.Response(description="Invalid input")
        }
    )
    def put(self, request, slug):
        task = get_object_or_404(Task, slug=slug)
        serializer = TaskAssignSerializer(data=request.data)
        if not serializer.is_valid():
            return Response({"message": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)

        assignees_data = serializer.validated_data.get('assignees', [])
        current_assignees = set(task.assignees.values_list('id', flat=True))

        valid_assignees = []
        invalid_assignees = []
        for user_id in assignees_data:
            try:
                user = User.objects.get(id=user_id.id)
                if user_id.id not in current_assignees:
                    valid_assignees.append(user)
            except User.DoesNotExist:
                invalid_assignees.append(user_id.id)

        if invalid_assignees:
            return Response({
                "message": f"Invalid user IDs: {invalid_assignees}"
            }, status=status.HTTP_400_BAD_REQUEST)

        task.assignees.add(*valid_assignees)

        # Send SMS notifications to newly assigned users
        sms_results = []
        print(f"📋 Task Assignment: {len(valid_assignees)} new assignees for task '{task.name}' (slug: {task.slug})")

        for assignee in valid_assignees:
            print(f"📱 Processing SMS for user: {assignee.email} (phone: {assignee.phone_number})")
            try:
                sms_result = send_task_assignment_sms(assignee, task, request.user)

                if sms_result.get("success"):
                    print(f"✅ SMS sent successfully to {assignee.email} - Message ID: {sms_result.get('message_id')}")
                else:
                    print(f"❌ SMS failed for {assignee.email}: {sms_result.get('error')}")

                sms_results.append({
                    "user": assignee.email,
                    "sms_sent": sms_result.get("success", False),
                    "sms_error": sms_result.get("error") if not sms_result.get("success") else None,
                    "message_id": sms_result.get("message_id") if sms_result.get("success") else None
                })
            except Exception as e:
                print(f"❌ SMS exception for {assignee.email}: {str(e)}")
                sms_results.append({
                    "user": assignee.email,
                    "sms_sent": False,
                    "sms_error": str(e)
                })

        print(f"📊 SMS Summary: {sum(1 for r in sms_results if r['sms_sent'])} sent, {sum(1 for r in sms_results if not r['sms_sent'])} failed")

        return Response({
            "message": "Assignees updated successfully",
            "invalid_assignees": invalid_assignees,
            "sms_notifications": sms_results
        }, status=status.HTTP_200_OK)


class retrievePlanThroughUserAccountView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        paginator = PageNumberPagination()
        paginator.page_size = 5

        user = request.user
        name = request.query_params.get('name', None)
        invite_user = request.query_params.get('invite_user', None)

        user_plans = Plan.objects.filter(user=user)
        user_email = user.email
        invited_plans = Plan.objects.filter(invitations__email=user_email, invitations__accepted=True).distinct()
        combined_plans = list(user_plans) + list(invited_plans)

        if name:
            combined_plans = [plan for plan in combined_plans if name.lower() in plan.name.lower()]

        combined_plans = sorted(list({plan.id: plan for plan in combined_plans}.values()), key=lambda plan: plan.id, reverse=True)
        result_page = paginator.paginate_queryset(combined_plans, request)
        serializer = PlanViewSerializer(result_page, many=True)
        paginated_response = paginator.get_paginated_response(serializer.data)
        paginated_response.data['status'] = status.HTTP_200_OK
        return paginated_response


class AddSubtaskView(APIView):
    permission_classes = [IsAuthenticated, IsTaskPlanOwner]

    @swagger_auto_schema(
        request_body=SubtaskSerializer,
        responses={
            200: openapi.Response(description="Subtask created successfully"),
            400: openapi.Response(description="Invalid input")
        }
    )
    def post(self, request, slug):
        task = get_object_or_404(Task, slug=slug)
        data = request.data.copy()
        data['task'] = task.id

        serializer = SubtaskSerializer(data=data)
        if serializer.is_valid():
            serializer.save()
            return Response({
                "message": "Subtask created successfully",
                "data": serializer.data
            }, status=status.HTTP_201_CREATED)
        else:
            return Response({
                "message": serializer.errors,
            }, status=status.HTTP_400_BAD_REQUEST)


class DeleteSubtaskView(APIView):
    permission_classes = [IsAuthenticated, IsSubtaskPlanOwner]

    def delete(self, request, slug):
        subtask = get_object_or_404(Subtask, slug=slug)
        self.check_object_permissions(request, subtask)
        subtask.delete()

        return Response({
            "message": "Subtask deleted successfully"
        }, status=status.HTTP_204_NO_CONTENT)


class UpdateSubtaskOrderView(APIView):
    permission_classes = [IsAuthenticated, IsTaskPlanOwner]

    @swagger_auto_schema(
        request_body=openapi.Schema(
            type=openapi.TYPE_ARRAY,
            items=openapi.Items(type=openapi.TYPE_OBJECT, properties={
                'id': openapi.Schema(type=openapi.TYPE_INTEGER, description='Subtask ID'),
                'order': openapi.Schema(type=openapi.TYPE_INTEGER, description='New order index'),
            }),
        ),
        responses={
            200: openapi.Response(description="Order updated successfully"),
            400: openapi.Response(description="Invalid input"),
        }
    )
    def put(self, request, slug):
        task = get_object_or_404(Task, slug=slug)
        subtasks_data = request.data

        for subtask_data in subtasks_data:
            subtask_id = subtask_data.get('id')
            order = subtask_data.get('order')
            if subtask_id is not None and order is not None:
                try:
                    subtask = Subtask.objects.get(id=subtask_id, task=task)
                    subtask.order = order
                    subtask.save()
                except Subtask.DoesNotExist:
                    return Response({"message": f"Subtask with ID {subtask_id} not found"}, status=status.HTTP_400_BAD_REQUEST)

        return Response({"message": "Order updated successfully"}, status=status.HTTP_200_OK)


class UpdateTaskOrderView(APIView):
    permission_classes = [IsAuthenticated]

    def put(self, request, id):
        milestone = get_object_or_404(Milestone, id=id)
        tasks_data = request.data

        try:
            with transaction.atomic():
                for task_data in tasks_data:
                    task_id = task_data.get('id')
                    order = task_data.get('order')
                    if task_id is not None and order is not None:
                        task = Task.objects.get(id=task_id, milestone=milestone)
                        task.order = order
                        task.save()

            return Response({"message": "Task order updated successfully"}, status=status.HTTP_200_OK)
        except Task.DoesNotExist:
            return Response({"message": "Task not found"}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({"message": str(e)}, status=status.HTTP_400_BAD_REQUEST)


class AddTaskToMilestoneView(APIView):
    permission_classes = [IsAuthenticated]

    @swagger_auto_schema(request_body=TaskSerializer, responses={201: 'Task created successfully'})
    def post(self, request, id):
        milestone = get_object_or_404(Milestone, id=id)
        data = request.data.copy()
        data['milestone'] = milestone.id

        serializer = TaskUpdateSerializer(data=data)
        if serializer.is_valid():
            task = serializer.save()

            if 'order' not in data:
                task.order = Task.objects.filter(milestone=milestone).count()
                task.save()

            return Response(serializer.data, status=status.HTTP_201_CREATED)
        else:
            return Response({"message": serializer.errors}, status=status.HTTP_400_BAD_REQUEST)


class DeleteTaskView(APIView):
    permission_classes = [IsAuthenticated, IsTaskPlanOwner]

    def delete(self, request, slug):
        task = get_object_or_404(Task, slug=slug)
        task.delete()

        return Response({"message": "Task and its subtasks deleted successfully"}, status=status.HTTP_204_NO_CONTENT)


class UpdateMilestoneNameView(APIView):
    permission_classes = [IsAuthenticated, CanUpdatePlanMilestone]

    def put(self, request, id):
        milestone = get_object_or_404(Milestone, id=id)
        serializer = MilestoneSerializer(milestone, data=request.data, partial=True)

        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_200_OK)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class CheckUserExistsView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request):
        serializer = CheckUserExistsSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data['email']
            user_exists = User.objects.filter(email=email).exists()

            if user_exists:
                return Response({"exists": True, "message": "User exists in the system"}, status=status.HTTP_200_OK)
            else:
                return Response({"exists": False, "message": "User does not exist in the system"}, status=status.HTTP_200_OK)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class SendInvitationView(APIView):
    permission_classes = [IsAuthenticated]

    def post(self, request, slug):
        plan = get_object_or_404(Plan, slug=slug)
        serializer = AddInvitationSerializer(data=request.data)

        if serializer.is_valid():
            email = serializer.validated_data['email']

            existing_invitation = Invitation.objects.filter(email=email, plan=plan).first()
            if existing_invitation:
                return Response({
                    "message": "This email has already been invited to this plan."
                }, status=status.HTTP_400_BAD_REQUEST)
            if email == plan.user.email:
                return Response({
                    "message": "You cannot invite yourself to your own plan."
                }, status=status.HTTP_400_BAD_REQUEST)

            invitation = serializer.save(invited_by=request.user, plan=plan)

            print(f"📧 Plan Invitation: Sending invitation to {email} for plan '{plan.name}'")

            # Send Email Invitation
            subject = "You've been invited to collaborate"
            url_frontend = os.getenv('URL_FRONTEND')
            signed_id = signing.dumps(invitation.id)
            accept_url = f'{url_frontend}/p/accept-invitation/{signed_id}'
            lines = (
                "Dear User,\n\n"
                "You've been invited to collaborate on the plan: {name}.\n"
                "This invitation will expire in 1 hour.\n"
                "Please click the link below to accept:\n\n"
                'Invitation Link: {url}\n\n'
                'Best regards,\n'
                'The Ignition Team'
            )
            email_message = lines.format(name=plan.name, url=accept_url)
            recipient_list = [email]

            # Send SMS Notification (if user exists and has phone number)
            sms_result = None
            try:
                invited_user = User.objects.get(email=email)
                print(f"📱 Plan Invitation: Found user {email} with phone {invited_user.phone_number}")

                if invited_user.phone_number:
                    from utils.email import send_sms

                    sms_message = (
                        f"Hi {invited_user.first_name}! You've been invited to collaborate on:\n\n"
                        f"Plan: {plan.name}\n"
                        f"Invited by: {request.user.first_name} {request.user.last_name}\n\n"
                        f"Check your email for the invitation link!"
                    )

                    print(f"📱 Plan Invitation: Sending SMS to {invited_user.phone_number}")
                    sms_result = send_sms(invited_user.phone_number, sms_message)

                    if sms_result.get("success"):
                        print(f"✅ Plan Invitation SMS sent successfully - Message ID: {sms_result.get('message_id')}")
                    else:
                        print(f"❌ Plan Invitation SMS failed: {sms_result.get('error')}")
                else:
                    print(f"📱 Plan Invitation: User {email} has no phone number, skipping SMS")

            except User.DoesNotExist:
                print(f"📱 Plan Invitation: User {email} not found in system, skipping SMS")

            try:
                send_email_with_name(subject, email_message, recipient_list, fail_silently=False)
                print(f"✅ Plan Invitation email sent successfully to {email}")
            except Exception as e:
                print(f"❌ Plan Invitation email failed: {str(e)}")
                return Response({
                    'message': f'Failure sendmail: {str(e)}',
                    'status': status.HTTP_500_INTERNAL_SERVER_ERROR
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

            response_data = {"message": "Invitation sent successfully"}
            if sms_result:
                response_data["sms_notification"] = {
                    "sms_sent": sms_result.get("success", False),
                    "sms_error": sms_result.get("error") if not sms_result.get("success") else None,
                    "message_id": sms_result.get("message_id") if sms_result.get("success") else None
                }

            return Response(response_data, status=status.HTTP_201_CREATED)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class CheckInvitationView(APIView):

    def post(self, request):
        signed_id = request.data.get('signed_id')
        if not signed_id:
            return Response({"message": "Signed ID is required"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            invitation_id = signing.loads(signed_id, max_age=3600)
        except SignatureExpired:
            return Response({"message": "The invitation link has expired."}, status=status.HTTP_400_BAD_REQUEST)
        except BadSignature:
            return Response({"message": "Invalid invitation link."}, status=status.HTTP_400_BAD_REQUEST)

        invitation = get_object_or_404(Invitation, id=invitation_id)

        find_user = User.objects.filter(email=invitation.email).exists()
        if not find_user:
            return Response({
                'is_not_registered': 1,
                "invitation_email": invitation.email,
                "message": "This email is not registered in the system."
            }, status=status.HTTP_404_NOT_FOUND)

        if request.user.is_authenticated and invitation.email != request.user.email:
            return Response({
                "message": "This invitation is not for you."
            }, status=status.HTTP_403_FORBIDDEN)

        if not request.user.is_authenticated:
            return Response({
                "message": "You need to be logged in to access this invitation."
            }, status=status.HTTP_401_UNAUTHORIZED)

        if invitation.accepted:
            return Response({"message": "This invitation has already been accepted."}, status=status.HTTP_400_BAD_REQUEST)

        serializer = CheckInvitationSerializer(invitation)
        return Response({
            "message": "Invitation has been accepted.",
            "invitation": serializer.data,
        }, status=status.HTTP_200_OK)


class UpdateInvitationStatusView(APIView):
    permission_classes = [IsAuthenticated]
    def post(self, request, id):
        invitation = get_object_or_404(Invitation, id=id)
        if invitation.email!= request.user.email:
            return Response({
                "message": "This invitation is not for you."
            }, status=status.HTTP_403_FORBIDDEN)

        serializer = InvitationUpdateSerializer(invitation, data=request.data, partial=True)

        if serializer.is_valid():
            serializer.save()
            if serializer.validated_data['accepted'] == Invitation.ACCEPTED:
                invitation.accepted_at = timezone.now()
            else:
                invitation.accepted_at = None
            invitation.save()
            return Response({
                "message": "Invitation status updated successfully.",
                "accepted_at": invitation.accepted_at
            }, status=status.HTTP_200_OK)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ListInvitedUsersView(APIView):
    permission_classes = [IsAuthenticated, canAccessPlan]

    def get(self, request, slug):
        plan = get_object_or_404(Plan, slug=slug)
        invitations = Invitation.objects.filter(plan=plan)
        serializer = InvitationSerializer(invitations, many=True)

        return Response(serializer.data, status=status.HTTP_200_OK)


class DeletePlanView(APIView):
    permission_classes = [IsAuthenticated, IsPlanOwner]

    def delete(self, request, slug):
        try:
            plan = get_object_or_404(Plan, slug=slug)

            for milestone in plan.milestone_set.all():
                for task in milestone.task_set.all():
                    task.assignees.clear()

            plan.delete()
            return Response({"message": "Plan and related data deleted successfully"}, status=status.HTTP_204_NO_CONTENT)
        except Plan.DoesNotExist:
            return Response({"error": "Plan not found or you do not have permission to delete this plan"}, status=status.HTTP_404_NOT_FOUND)


class OptOutPlanView(APIView):
    permission_classes = [IsAuthenticated]

    def delete(self, request, slug):
        try:
            plan = get_object_or_404(Plan, slug=slug)

            # Check if user is the plan owner
            if plan.user == request.user:
                return Response({
                    "error": "Plan owners cannot opt out of their own plans. Use delete instead."
                }, status=status.HTTP_400_BAD_REQUEST)

            # Find and delete the user's invitation
            invitation = get_object_or_404(Invitation, plan=plan, email=request.user.email, accepted=Invitation.ACCEPTED)
            invitation.delete()

            return Response({
                "message": "Successfully opted out of the plan"
            }, status=status.HTTP_204_NO_CONTENT)

        except Invitation.DoesNotExist:
            return Response({
                "error": "You are not a member of this plan or invitation not found"
            }, status=status.HTTP_404_NOT_FOUND)
        except Plan.DoesNotExist:
            return Response({
                "error": "Plan not found"
            }, status=status.HTTP_404_NOT_FOUND)


class PlanAccessManagementView(APIView):
    """Manage access levels for a plan"""
    permission_classes = [IsAuthenticated, IsOwnerOnly]

    def get(self, request, slug):
        """Get all access levels for a plan"""
        plan = get_object_or_404(Plan, slug=slug)
        access_levels = PlanAccess.objects.filter(plan=plan)
        serializer = PlanAccessSerializer(access_levels, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def post(self, request, slug):
        """Add a new access level"""
        plan = get_object_or_404(Plan, slug=slug)

        email = request.data.get('email')
        access_level = request.data.get('access_level', PlanAccess.VIEWER)

        if not email:
            return Response({
                "error": "Email is required"
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = User.objects.get(email=email)
        except User.DoesNotExist:
            return Response({
                "error": "User not found"
            }, status=status.HTTP_404_NOT_FOUND)

        # Check if user already has access
        if PlanAccess.objects.filter(plan=plan, user=user).exists():
            return Response({
                "error": "User already has access to this plan"
            }, status=status.HTTP_400_BAD_REQUEST)

        # Create access level
        access = PlanAccess.objects.create(
            user=user,
            plan=plan,
            access_level=access_level,
            granted_by=request.user
        )

        serializer = PlanAccessSerializer(access)
        return Response(serializer.data, status=status.HTTP_201_CREATED)


class PlanAccessDetailView(APIView):
    """Manage individual access level"""
    permission_classes = [IsAuthenticated]

    def get_permissions(self):
        if self.request.method == 'DELETE':
            # Only head owner can remove owners, owners can remove editors/viewers
            return [IsAuthenticated(), IsOwnerOnly()]
        elif self.request.method == 'PUT':
            # Only head owner can change access levels
            return [IsAuthenticated(), IsHeadOwnerOnly()]
        return [IsAuthenticated(), IsOwnerOnly()]

    def put(self, request, slug, access_id):
        """Update access level"""
        plan = get_object_or_404(Plan, slug=slug)
        access = get_object_or_404(PlanAccess, id=access_id, plan=plan)

        new_access_level = request.data.get('access_level')
        is_head_owner = request.data.get('is_head_owner', False)

        if not new_access_level:
            return Response({
                "error": "Access level is required"
            }, status=status.HTTP_400_BAD_REQUEST)

        # Handle head owner transfer
        if is_head_owner and new_access_level == PlanAccess.OWNER:
            # Remove head owner from current user
            current_head = PlanAccess.objects.get(plan=plan, is_head_owner=True)
            current_head.is_head_owner = False
            current_head.save()

            # Set new head owner
            access.is_head_owner = True

        access.access_level = new_access_level
        access.save()

        serializer = PlanAccessSerializer(access)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def delete(self, request, slug, access_id):
        """Remove access level"""
        plan = get_object_or_404(Plan, slug=slug)
        access = get_object_or_404(PlanAccess, id=access_id, plan=plan)

        # Cannot remove head owner
        if access.is_head_owner:
            return Response({
                "error": "Cannot remove head owner. Transfer ownership first."
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get current user's access level
        user_access = PlanAccess.objects.get(plan=plan, user=request.user)

        # Only head owner can remove owners
        if access.access_level == PlanAccess.OWNER and not user_access.is_head_owner:
            return Response({
                "error": "Only head owner can remove other owners"
            }, status=status.HTTP_403_FORBIDDEN)

        access.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)
