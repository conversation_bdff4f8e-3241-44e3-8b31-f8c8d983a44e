{"ast": null, "code": "import * as React from 'react';\nconst ModalDialogVariantColorContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== 'production') {\n  ModalDialogVariantColorContext.displayName = 'ModalDialogVariantColorContext';\n}\nexport default ModalDialogVariantColorContext;", "map": {"version": 3, "names": ["React", "ModalDialogVariantColorContext", "createContext", "undefined", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/ModalDialog/ModalDialogVariantColorContext.js"], "sourcesContent": ["import * as React from 'react';\nconst ModalDialogVariantColorContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== 'production') {\n  ModalDialogVariantColorContext.displayName = 'ModalDialogVariantColorContext';\n}\nexport default ModalDialogVariantColorContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,8BAA8B,GAAG,aAAaD,KAAK,CAACE,aAAa,CAACC,SAAS,CAAC;AAClF,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCL,8BAA8B,CAACM,WAAW,GAAG,gCAAgC;AAC/E;AACA,eAAeN,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}