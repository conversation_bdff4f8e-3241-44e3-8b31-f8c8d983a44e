{"ast": null, "code": "'use client';\n\nexport { useTab } from './useTab';\nexport * from './useTab.types';", "map": {"version": 3, "names": ["useTab"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/useTab/index.js"], "sourcesContent": ["'use client';\n\nexport { useTab } from './useTab';\nexport * from './useTab.types';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,MAAM,QAAQ,UAAU;AACjC,cAAc,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}