import openai
from .models import Assistant, Thread, Message, Run
from .utils import convert_to_json
import os
import requests
import json
from dotenv import load_dotenv
load_dotenv()


class OpenRouterConfig:
    def __init__(self):
        self.api_key = os.getenv("OPENROUTER_API_KEY")
        self.base_url = "https://openrouter.ai/api/v1"
        self.model = os.getenv("OPENROUTER_DEFAULT_MODEL", "deepseek/deepseek-r1-0528:free")
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "http://localhost:3000",  # Your site URL
            "X-Title": "Ignition Project Manager"  # Your app name
        }

    def chat_completion(self, messages, temperature=0.7, max_tokens=8000):
        """
        Create a chat completion using OpenRouter API
        """
        data = {
            "model": self.model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens
        }

        response = requests.post(
            f"{self.base_url}/chat/completions",
            headers=self.headers,
            json=data
        )

        if response.status_code == 200:
            return response.json()
        else:
            raise Exception(f"OpenRouter API error: {response.status_code} - {response.text}")

    def create_thread(self):
        # For OpenRouter, we'll simulate thread creation since it doesn't have threads
        import uuid
        thread_id = str(uuid.uuid4())

        # Create a dummy assistant if needed
        assistant, created = Assistant.objects.get_or_create(
            value="openrouter-assistant")
        new_thread = Thread(value=thread_id, assistant=assistant)
        new_thread.save()

        return {"id": thread_id}

    def add_message(self, threadid, message):
        # For OpenRouter, we'll simulate message creation
        import uuid
        message_id = str(uuid.uuid4())

        thread, created = Thread.objects.get_or_create(value=threadid)
        new_message = Message(
            message_id=message_id,
            value=message,
            thread=thread
        )
        new_message.save()

        return {
            "id": message_id,
            "thread_id": threadid,
            "content": [{"text": {"value": message}}]
        }

    def create_run_assistant(self, thread_id, assistant_id):
        # For OpenRouter, we'll simulate run creation
        import uuid
        run_id = str(uuid.uuid4())

        thread, created = Thread.objects.get_or_create(value=thread_id)
        new_run = Run(
            value=run_id,
            status="completed",  # OpenRouter responses are immediate
            thread=thread,
        )
        new_run.save()

        return {
            "id": run_id,
            "status": "completed"
        }

    def retrieve_run_assistant(self, thread_id, run_id):
        # For OpenRouter, runs are completed immediately
        return {
            "id": run_id,
            "status": "completed"
        }

    def list_messages_assistant(self, thread_id):
        # For OpenRouter, we'll return messages from our database
        try:
            thread = Thread.objects.get(value=thread_id)
            messages = Message.objects.filter(thread=thread).order_by('-id')

            message_data = []
            for message in messages:
                message_data.append({
                    "id": message.message_id,
                    "content": [{"text": {"value": message.value}}],
                    "role": "assistant"
                })

            return {
                "data": message_data,
                "first_id": message_data[0]["id"] if message_data else None,
                "last_id": message_data[-1]["id"] if message_data else None
            }
        except Thread.DoesNotExist:
            return {"data": [], "first_id": None, "last_id": None}

class OpenAIConfig:
    def __init__(self):
        self.openai_client = openai.OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

    def create_thread(self):
        response = self.openai_client.beta.threads.create()

        converted_data = {item[0]: item[1] for item in response}
        assistant, created = Assistant.objects.get_or_create(
            value=os.getenv("ASSISTANT_ID"))
        new_thread = Thread(value=converted_data["id"], assistant=assistant)
        new_thread.save()

        return converted_data

    def add_message(self, threadid, message):
        thread_message = self.openai_client.beta.threads.messages.create(
            threadid,
            role="user",
            content=message,
        )
        converted_data = {item[0]: item[1] for item in thread_message}
        if 'content' in converted_data:
            content_list = converted_data['content']
            if isinstance(content_list, list) and len(content_list) > 0:
                content_data = []
                for content_item in content_list:
                    content_dict = {}
                    for pair in content_item:
                        key, value = pair
                        if key == "text":
                            # Chuyển đổi 'text' từ danh sách các cặp sang từ điển
                            text_data = {sub_item[0]: sub_item[1]
                                         for sub_item in value}
                            content_dict[key] = text_data
                        else:
                            content_dict[key] = value
                    content_data.append(content_dict)
                converted_data['content'] = content_data

        message_id = converted_data["id"]
        content = converted_data["content"][0]["text"]["value"]
        thread_id = converted_data["thread_id"]
        thread, created = Thread.objects.get_or_create(value=thread_id)
        new_message = Message(
            message_id=message_id,
            value=content,
            thread=thread
        )
        new_message.save()
        return converted_data

    def create_run_assistant(self, thread_id, assistant_id):
        run = self.openai_client.beta.threads.runs.create(
            thread_id=thread_id,
            assistant_id=assistant_id
        )

        converted_data = {item[0]: item[1] for item in run}
        thread, created = Thread.objects.get_or_create(value=thread_id)
        new_run = Run(
            value=converted_data['id'],
            status=converted_data['status'],
            thread=thread,
        )
        new_run.save()
        return converted_data

    def retrieve_run_assistant(self, thread_id, run_id):
        run = self.openai_client.beta.threads.runs.retrieve(
            thread_id=thread_id,
            run_id=run_id
        )
        converted_data = {item[0]: item[1] for item in run}
        return converted_data

    def list_messages_assistant(self, thread_id):
        thread_messages = self.openai_client.beta.threads.messages.list(thread_id)
        converted_data = convert_to_json(thread_messages)

        return converted_data


# Keep the old class name for backward compatibility
class OpenAIConfig(OpenRouterConfig):
    pass
