{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getSvgIconUtilityClass(slot) {\n  return generateUtilityClass('MuiSvgIcon', slot);\n}\nconst svgIconClasses = generateUtilityClasses('MuiSvgIcon', ['root', 'colorInherit', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'fontSizeInherit', 'fontSizeXs', 'fontSizeSm', 'fontSizeMd', 'fontSizeLg', 'fontSizeXl', 'fontSizeXl2', 'fontSizeXl3', 'fontSizeXl4', 'sizeSm', 'sizeMd', 'sizeLg']);\nexport default svgIconClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getSvgIconUtilityClass", "slot", "svgIconClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/SvgIcon/svgIconClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getSvgIconUtilityClass(slot) {\n  return generateUtilityClass('MuiSvgIcon', slot);\n}\nconst svgIconClasses = generateUtilityClasses('MuiSvgIcon', ['root', 'colorInherit', 'colorPrimary', 'colorNeutral', 'colorDanger', 'colorSuccess', 'colorWarning', 'fontSizeInherit', 'fontSizeXs', 'fontSizeSm', 'fontSizeMd', 'fontSizeLg', 'fontSizeXl', 'fontSizeXl2', 'fontSizeXl3', 'fontSizeXl4', 'sizeSm', 'sizeMd', 'sizeLg']);\nexport default svgIconClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,sBAAsBA,CAACC,IAAI,EAAE;EAC3C,OAAOH,oBAAoB,CAAC,YAAY,EAAEG,IAAI,CAAC;AACjD;AACA,MAAMC,cAAc,GAAGH,sBAAsB,CAAC,YAAY,EAAE,CAAC,MAAM,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,iBAAiB,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,aAAa,EAAE,aAAa,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,CAAC,CAAC;AACxU,eAAeG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}