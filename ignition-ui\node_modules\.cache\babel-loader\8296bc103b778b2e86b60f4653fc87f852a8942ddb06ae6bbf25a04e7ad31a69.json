{"ast": null, "code": "'use strict';\n\nvar setFunctionLength = require('set-function-length');\nvar $defineProperty = require('es-define-property');\nvar callBindBasic = require('call-bind-apply-helpers');\nvar applyBind = require('call-bind-apply-helpers/applyBind');\nmodule.exports = function callBind(originalFunction) {\n  var func = callBindBasic(arguments);\n  var adjustedLength = originalFunction.length - (arguments.length - 1);\n  return setFunctionLength(func, 1 + (adjustedLength > 0 ? adjustedLength : 0), true);\n};\nif ($defineProperty) {\n  $defineProperty(module.exports, 'apply', {\n    value: applyBind\n  });\n} else {\n  module.exports.apply = applyBind;\n}", "map": {"version": 3, "names": ["setFunctionLength", "require", "$defineProperty", "callBindBasic", "applyBind", "module", "exports", "callBind", "originalFunction", "func", "arguments", "<PERSON><PERSON><PERSON>th", "length", "value", "apply"], "sources": ["C:/ignition/ignition-ui/node_modules/call-bind/index.js"], "sourcesContent": ["'use strict';\n\nvar setFunctionLength = require('set-function-length');\n\nvar $defineProperty = require('es-define-property');\n\nvar callBindBasic = require('call-bind-apply-helpers');\nvar applyBind = require('call-bind-apply-helpers/applyBind');\n\nmodule.exports = function callBind(originalFunction) {\n\tvar func = callBindBasic(arguments);\n\tvar adjustedLength = originalFunction.length - (arguments.length - 1);\n\treturn setFunctionLength(\n\t\tfunc,\n\t\t1 + (adjustedLength > 0 ? adjustedLength : 0),\n\t\ttrue\n\t);\n};\n\nif ($defineProperty) {\n\t$defineProperty(module.exports, 'apply', { value: applyBind });\n} else {\n\tmodule.exports.apply = applyBind;\n}\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,iBAAiB,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AAEtD,IAAIC,eAAe,GAAGD,OAAO,CAAC,oBAAoB,CAAC;AAEnD,IAAIE,aAAa,GAAGF,OAAO,CAAC,yBAAyB,CAAC;AACtD,IAAIG,SAAS,GAAGH,OAAO,CAAC,mCAAmC,CAAC;AAE5DI,MAAM,CAACC,OAAO,GAAG,SAASC,QAAQA,CAACC,gBAAgB,EAAE;EACpD,IAAIC,IAAI,GAAGN,aAAa,CAACO,SAAS,CAAC;EACnC,IAAIC,cAAc,GAAGH,gBAAgB,CAACI,MAAM,IAAIF,SAAS,CAACE,MAAM,GAAG,CAAC,CAAC;EACrE,OAAOZ,iBAAiB,CACvBS,IAAI,EACJ,CAAC,IAAIE,cAAc,GAAG,CAAC,GAAGA,cAAc,GAAG,CAAC,CAAC,EAC7C,IACD,CAAC;AACF,CAAC;AAED,IAAIT,eAAe,EAAE;EACpBA,eAAe,CAACG,MAAM,CAACC,OAAO,EAAE,OAAO,EAAE;IAAEO,KAAK,EAAET;EAAU,CAAC,CAAC;AAC/D,CAAC,MAAM;EACNC,MAAM,CAACC,OAAO,CAACQ,KAAK,GAAGV,SAAS;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}