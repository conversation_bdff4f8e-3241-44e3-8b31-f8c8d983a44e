import os
import time
import json
from celery import shared_task
from urllib.error import H<PERSON><PERSON><PERSON><PERSON><PERSON>, URLError
from django.db import transaction
from assistant.assistant_functions import OpenRouterConfig
from plans.models import Plan, Milestone, Task, Subtask, User
from .utils import create_prompt, convert_json_text

@shared_task
def call_assistant_api(prompt, language, role, user_id):
    try:
        openrouter_config = OpenRouterConfig()
        content = create_prompt(prompt=prompt, language=language, role=role)

        # Use OpenRouter chat completion
        messages = [
            {"role": "system", "content": "You are an expert project planning assistant. You MUST follow ALL requirements exactly as specified. You MUST create detailed, specific subtasks that are 50+ words each with exact tools, deliverables, and steps. You MUST NOT create vague or short subtasks. You MUST create exactly 7-8 milestones. Failure to follow these requirements is unacceptable."},
            {"role": "user", "content": content}
        ]

        chat_completion = openrouter_config.chat_completion(
            messages=messages,
            temperature=0.7,
            max_tokens=4000
        )

        if chat_completion.get('choices') and chat_completion['choices'][0].get('message'):
            created_plan_data = chat_completion['choices'][0]['message']['content']
            converted_data = convert_json_text(created_plan_data)
            plan_data_dict = json.loads(converted_data, strict=False)

            # Save the plan data to the database
            return save_plan_to_db(plan_data_dict, user_id)
        else:
            return {"error": "Invalid response from OpenRouter API"}

    except HTTPError as e:
        print(f"HTTP Error occurred using Assistant API: {e}")
        return {"error": f"HTTP Error occurred using Assistant API: {e}"}
    except URLError as e:
        print(f"URL Error occurred using Assistant API: {e}")
        return {"error": f"URL Error occurred using Assistant API: {e}"}
    except Exception as e:
        print(f"An Error occurred: {e}")
        return {"error": f"An error occurred: {e}"}

def save_plan_to_db(plan_data_dict, user_id):
    with transaction.atomic():
        plan = Plan.objects.create(
            name=plan_data_dict['name'],
            description=plan_data_dict['description'],
            user=User.objects.get(id=user_id)
        )
        plan_data_dict['slug'] = plan.slug

        for milestone_data in plan_data_dict['milestones']:
            milestone = Milestone.objects.create(
                name=milestone_data['name'],
                plan=plan
            )

            for task_data in milestone_data['tasks']:
                task = Task.objects.create(
                    name=task_data['name'],
                    milestone=milestone
                )
                
                for subtask_data in task_data['sub-tasks']:
                    Subtask.objects.create(
                        name=subtask_data['name'],
                        task=task
                    )
    return plan_data_dict