import os
import time
import json
from celery import shared_task
from urllib.error import H<PERSON><PERSON><PERSON><PERSON><PERSON>, URLError
from openai import OpenAI
from plans.models import Plan, Milestone, Task, Subtask, User
from .utils import create_prompt, convert_json_text

@shared_task
def call_assistant_api(prompt, language, role, user_id):
    try:
        client = OpenAI(api_key=os.environ['OPENAI_API_KEY'])
        thread = client.beta.threads.create()
        content = create_prompt(prompt=prompt, language=language, role=role)

        message = client.beta.threads.messages.create(
            thread_id=thread.id,
            role='user',
            content=content
        )

        run = client.beta.threads.runs.create(
            thread_id=thread.id,
            assistant_id=os.environ['ASSISTANT_ID'],
        )

        while True:
            run_status = client.beta.threads.runs.retrieve(
                thread_id=thread.id,
                run_id=run.id
            )
            if run_status.status == 'completed':
                result_message = client.beta.threads.messages.list(thread_id=thread.id)
                for message in result_message.data:
                    created_plan_data = message.content[0].text.value
                    converted_data = convert_json_text(created_plan_data)
                    plan_data_dict = json.loads(converted_data, strict=False)
                    
                    # Save the plan data to the database
                    return save_plan_to_db(plan_data_dict, user_id)
            time.sleep(5)

    except HTTPError as e:
        print(f"HTTP Error occurred using Assistant API: {e}")
        return {"error": f"HTTP Error occurred using Assistant API: {e}"}
    except URLError as e:
        print(f"URL Error occurred using Assistant API: {e}")
        return {"error": f"URL Error occurred using Assistant API: {e}"}
    except Exception as e:
        print(f"An Error occurred: {e}")
        return {"error": f"An error occurred: {e}"}

def save_plan_to_db(plan_data_dict, user_id):
    with transaction.atomic():
        plan = Plan.objects.create(
            name=plan_data_dict['name'],
            description=plan_data_dict['description'],
            user=User.objects.get(id=user_id)
        )
        plan_data_dict['slug'] = plan.slug

        for milestone_data in plan_data_dict['milestones']:
            milestone = Milestone.objects.create(
                name=milestone_data['name'],
                plan=plan
            )

            for task_data in milestone_data['tasks']:
                task = Task.objects.create(
                    name=task_data['name'],
                    milestone=milestone
                )
                
                for subtask_data in task_data['sub-tasks']:
                    Subtask.objects.create(
                        name=subtask_data['name'],
                        task=task
                    )
    return plan_data_dict