{"ast": null, "code": "import { generateUtilityClasses } from '../generateUtilityClasses';\nimport { generateUtilityClass } from '../generateUtilityClass';\nconst COMPONENT_NAME = 'Badge';\nexport function getBadgeUtilityClass(slot) {\n  return generateUtilityClass(COMPONENT_NAME, slot);\n}\nexport const badgeClasses = generateUtilityClasses(COMPONENT_NAME, ['root', 'badge', 'invisible']);", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "COMPONENT_NAME", "getBadgeUtilityClass", "slot", "badgeClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/Badge/badgeClasses.js"], "sourcesContent": ["import { generateUtilityClasses } from '../generateUtilityClasses';\nimport { generateUtilityClass } from '../generateUtilityClass';\nconst COMPONENT_NAME = 'Badge';\nexport function getBadgeUtilityClass(slot) {\n  return generateUtilityClass(COMPONENT_NAME, slot);\n}\nexport const badgeClasses = generateUtilityClasses(COMPONENT_NAME, ['root', 'badge', 'invisible']);"], "mappings": "AAAA,SAASA,sBAAsB,QAAQ,2BAA2B;AAClE,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,MAAMC,cAAc,GAAG,OAAO;AAC9B,OAAO,SAASC,oBAAoBA,CAACC,IAAI,EAAE;EACzC,OAAOH,oBAAoB,CAACC,cAAc,EAAEE,IAAI,CAAC;AACnD;AACA,OAAO,MAAMC,YAAY,GAAGL,sBAAsB,CAACE,cAAc,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,WAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}