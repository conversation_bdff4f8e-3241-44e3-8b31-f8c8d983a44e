{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getStackUtilityClass(slot) {\n  return generateUtilityClass('MuiStack', slot);\n}\nconst stackClasses = generateUtilityClasses('MuiStack', ['root']);\nexport default stackClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getStackUtilityClass", "slot", "stackClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Stack/stackClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getStackUtilityClass(slot) {\n  return generateUtilityClass('MuiStack', slot);\n}\nconst stackClasses = generateUtilityClasses('MuiStack', ['root']);\nexport default stackClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,oBAAoBA,CAACC,IAAI,EAAE;EACzC,OAAOH,oBAAoB,CAAC,UAAU,EAAEG,IAAI,CAAC;AAC/C;AACA,MAAMC,YAAY,GAAGH,sBAAsB,CAAC,UAAU,EAAE,CAAC,MAAM,CAAC,CAAC;AACjE,eAAeG,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}