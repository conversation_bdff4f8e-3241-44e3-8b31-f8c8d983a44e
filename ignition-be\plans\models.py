from django.db import models
from users.models import User
import uuid
from django.utils import timezone


STATUS_CHOICES = (
    (1, 'Todo'),
    (2, 'In Progress'),
    (3, 'Done'),
)

PRIORITY_CHOICES = (
    (1, 'Low'),
    (2, 'Medium'),
    (3, 'High'),
    (4, 'Critical'),
)

TODO_STATUS = 1
INPROGRESS_STATUS = 2
DONE_STATUS = 3


class Plan(models.Model):
    name = models.CharField(null=True, blank=True, max_length=255)
    description = models.TextField(null=True, blank=True)
    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    priority = models.IntegerField(choices=PRIORITY_CHOICES, default=1)
    slug = models.SlugField(max_length=255, unique=True, blank=True)
    assignees = models.ManyToManyField(User, related_name='assigned_plan', blank=True)
    created_at = models.DateTimeField(default=timezone.now)

    # Thêm trường status
    STATUS_CHOICES = (
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    )
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')

    class Meta:
        db_table = 'plans'

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = uuid.uuid4()
        is_new = self.pk is None
        super(Plan, self).save(*args, **kwargs)

        # Create head owner access level for the plan creator
        if is_new and self.user:
            # We'll create the PlanAccess after the model is defined
            # This will be handled by a signal instead to avoid circular import
            pass


class Milestone(models.Model):
    name = models.CharField(null=True, blank=True, max_length=255)
    description = models.TextField(null=True, blank=True)
    plan = models.ForeignKey(Plan, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    estimated_duration = models.CharField(max_length=100, null=True, blank=True)
    success_criteria = models.TextField(null=True, blank=True)

    class Meta:
        db_table = 'milestones'


class Risk(models.Model):
    risk = models.TextField(null=True, blank=True)
    mitigation = models.TextField(null=True, blank=True)
    milestone = models.ForeignKey(Milestone, on_delete=models.CASCADE, related_name='risks')
    created_at = models.DateTimeField(default=timezone.now)

    class Meta:
        db_table = 'risks'


class Task(models.Model):
    name = models.CharField(null=True, blank=True, max_length=255)
    description = models.TextField(null=True, blank=True)
    milestone = models.ForeignKey(Milestone, on_delete=models.SET_NULL, null=True, blank=True)
    slug = models.SlugField(max_length=255, unique=True, blank=True)
    status = models.IntegerField(choices=STATUS_CHOICES, default=1)
    priority = models.IntegerField(choices=PRIORITY_CHOICES, default=2)
    start_date = models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    progress = models.IntegerField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    assignees = models.ManyToManyField(User, related_name='assigned_task', blank=True, null=True)
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    order = models.IntegerField(null=True, blank=True)
    estimated_duration = models.CharField(max_length=100, null=True, blank=True)

    def __str__(self):
        return self.name

    class Meta:
        db_table = 'tasks'
        ordering = ['order']

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = uuid.uuid4()
        super(Task, self).save(*args, **kwargs)


class Subtask(models.Model):
    name = models.CharField(null=True, blank=True, max_length=255)
    description = models.TextField(null=True, blank=True)
    task = models.ForeignKey(Task, on_delete=models.CASCADE, null=True, blank=True)
    slug = models.SlugField(max_length=255, unique=True, blank=True)
    status = models.IntegerField(choices=STATUS_CHOICES, default=1)
    start_date = models.DateField(null=True, blank=True)
    end_date = models.DateField(null=True, blank=True)
    progress = models.IntegerField(null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)
    order = models.IntegerField(null=True, blank=True)

    class Meta:
        db_table = 'subtasks'
        ordering = ['order']

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = uuid.uuid4()

        if self.task and self.status != self.task.status:
            self.status = self.task.status

        super(Subtask, self).save(*args, **kwargs)


class PlanAccess(models.Model):
    OWNER = 'owner'
    EDITOR = 'editor'
    VIEWER = 'viewer'

    ACCESS_LEVEL_CHOICES = [
        (OWNER, 'Owner'),
        (EDITOR, 'Editor'),
        (VIEWER, 'Viewer'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    plan = models.ForeignKey(Plan, on_delete=models.CASCADE, related_name="access_levels")
    access_level = models.CharField(max_length=10, choices=ACCESS_LEVEL_CHOICES)
    is_head_owner = models.BooleanField(default=False)
    granted_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name="granted_access", null=True, blank=True)
    created_at = models.DateTimeField(default=timezone.now)

    class Meta:
        db_table = 'plan_access'
        unique_together = ('user', 'plan')

    def __str__(self):
        head_indicator = " (Head)" if self.is_head_owner else ""
        return f"{self.user.email} - {self.access_level}{head_indicator} on {self.plan.name}"


class Invitation(models.Model):
    PENDING = 0
    ACCEPTED = 1
    REJECTED = -1
    ACCEPTANCE_CHOICES = [
        (PENDING, 'Pending'),
        (ACCEPTED, 'Accepted'),
        (REJECTED, 'Rejected'),
    ]

    email = models.EmailField()
    plan = models.ForeignKey(Plan, on_delete=models.CASCADE, related_name="invitations")
    invited_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name="sent_invitations")
    access_level = models.CharField(max_length=10, choices=PlanAccess.ACCESS_LEVEL_CHOICES, default=PlanAccess.VIEWER)
    accepted = models.IntegerField(choices=ACCEPTANCE_CHOICES, default=PENDING)
    created_at = models.DateTimeField(default=timezone.now)
    accepted_at = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"Invitation to {self.email} for {self.plan.name} as {self.access_level}"

    def save(self, *args, **kwargs):
        if self.accepted and not self.accepted_at:
            self.accepted_at = timezone.now()
        super().save(*args, **kwargs)
