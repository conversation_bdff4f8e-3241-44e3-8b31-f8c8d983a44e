{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useId as useId, unstable_useForkRef as useForkRef } from '@mui/utils';\nimport { useTabsContext } from '../Tabs';\nimport { useCompoundItem } from '../useCompound';\nfunction tabPanelValueGenerator(otherTabPanelValues) {\n  return otherTabPanelValues.size;\n}\n\n/**\n *\n * Demos:\n *\n * - [Tabs](https://mui.com/base-ui/react-tabs/#hooks)\n *\n * API:\n *\n * - [useTabPanel API](https://mui.com/base-ui/react-tabs/hooks-api/#use-tab-panel)\n */\nfunction useTabPanel(parameters) {\n  const {\n    value: valueParam,\n    id: idParam,\n    rootRef: externalRef\n  } = parameters;\n  const context = useTabsContext();\n  if (context === null) {\n    throw new Error('No TabContext provided');\n  }\n  const {\n    value: selectedTabValue,\n    getTabId\n  } = context;\n  const id = useId(idParam);\n  const ref = React.useRef(null);\n  const handleRef = useForkRef(ref, externalRef);\n  const metadata = React.useMemo(() => ({\n    id,\n    ref\n  }), [id]);\n  const {\n    id: value\n  } = useCompoundItem(valueParam != null ? valueParam : tabPanelValueGenerator, metadata);\n  const hidden = value !== selectedTabValue;\n  const correspondingTabId = value !== undefined ? getTabId(value) : undefined;\n  const getRootProps = function () {\n    let externalProps = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n    return _extends({\n      'aria-labelledby': correspondingTabId != null ? correspondingTabId : undefined,\n      hidden,\n      id: id != null ? id : undefined\n    }, externalProps, {\n      ref: handleRef\n    });\n  };\n  return {\n    hidden,\n    getRootProps,\n    rootRef: handleRef\n  };\n}\nexport { useTabPanel };", "map": {"version": 3, "names": ["_extends", "React", "unstable_useId", "useId", "unstable_useForkRef", "useForkRef", "useTabsContext", "useCompoundItem", "tabPanelValueGenerator", "otherTabPanelValues", "size", "useTabPanel", "parameters", "value", "valueParam", "id", "idParam", "rootRef", "externalRef", "context", "Error", "selectedTabValue", "getTabId", "ref", "useRef", "handleRef", "metadata", "useMemo", "hidden", "correspondingTabId", "undefined", "getRootProps", "externalProps", "arguments", "length"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/base/useTabPanel/useTabPanel.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { unstable_useId as useId, unstable_useForkRef as useForkRef } from '@mui/utils';\nimport { useTabsContext } from '../Tabs';\nimport { useCompoundItem } from '../useCompound';\nfunction tabPanelValueGenerator(otherTabPanelValues) {\n  return otherTabPanelValues.size;\n}\n\n/**\n *\n * Demos:\n *\n * - [Tabs](https://mui.com/base-ui/react-tabs/#hooks)\n *\n * API:\n *\n * - [useTabPanel API](https://mui.com/base-ui/react-tabs/hooks-api/#use-tab-panel)\n */\nfunction useTabPanel(parameters) {\n  const {\n    value: valueParam,\n    id: idParam,\n    rootRef: externalRef\n  } = parameters;\n  const context = useTabsContext();\n  if (context === null) {\n    throw new Error('No TabContext provided');\n  }\n  const {\n    value: selectedTabValue,\n    getTabId\n  } = context;\n  const id = useId(idParam);\n  const ref = React.useRef(null);\n  const handleRef = useForkRef(ref, externalRef);\n  const metadata = React.useMemo(() => ({\n    id,\n    ref\n  }), [id]);\n  const {\n    id: value\n  } = useCompoundItem(valueParam != null ? valueParam : tabPanelValueGenerator, metadata);\n  const hidden = value !== selectedTabValue;\n  const correspondingTabId = value !== undefined ? getTabId(value) : undefined;\n  const getRootProps = (externalProps = {}) => {\n    return _extends({\n      'aria-labelledby': correspondingTabId != null ? correspondingTabId : undefined,\n      hidden,\n      id: id != null ? id : undefined\n    }, externalProps, {\n      ref: handleRef\n    });\n  };\n  return {\n    hidden,\n    getRootProps,\n    rootRef: handleRef\n  };\n}\nexport { useTabPanel };"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,IAAIC,KAAK,EAAEC,mBAAmB,IAAIC,UAAU,QAAQ,YAAY;AACvF,SAASC,cAAc,QAAQ,SAAS;AACxC,SAASC,eAAe,QAAQ,gBAAgB;AAChD,SAASC,sBAAsBA,CAACC,mBAAmB,EAAE;EACnD,OAAOA,mBAAmB,CAACC,IAAI;AACjC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,WAAWA,CAACC,UAAU,EAAE;EAC/B,MAAM;IACJC,KAAK,EAAEC,UAAU;IACjBC,EAAE,EAAEC,OAAO;IACXC,OAAO,EAAEC;EACX,CAAC,GAAGN,UAAU;EACd,MAAMO,OAAO,GAAGb,cAAc,CAAC,CAAC;EAChC,IAAIa,OAAO,KAAK,IAAI,EAAE;IACpB,MAAM,IAAIC,KAAK,CAAC,wBAAwB,CAAC;EAC3C;EACA,MAAM;IACJP,KAAK,EAAEQ,gBAAgB;IACvBC;EACF,CAAC,GAAGH,OAAO;EACX,MAAMJ,EAAE,GAAGZ,KAAK,CAACa,OAAO,CAAC;EACzB,MAAMO,GAAG,GAAGtB,KAAK,CAACuB,MAAM,CAAC,IAAI,CAAC;EAC9B,MAAMC,SAAS,GAAGpB,UAAU,CAACkB,GAAG,EAAEL,WAAW,CAAC;EAC9C,MAAMQ,QAAQ,GAAGzB,KAAK,CAAC0B,OAAO,CAAC,OAAO;IACpCZ,EAAE;IACFQ;EACF,CAAC,CAAC,EAAE,CAACR,EAAE,CAAC,CAAC;EACT,MAAM;IACJA,EAAE,EAAEF;EACN,CAAC,GAAGN,eAAe,CAACO,UAAU,IAAI,IAAI,GAAGA,UAAU,GAAGN,sBAAsB,EAAEkB,QAAQ,CAAC;EACvF,MAAME,MAAM,GAAGf,KAAK,KAAKQ,gBAAgB;EACzC,MAAMQ,kBAAkB,GAAGhB,KAAK,KAAKiB,SAAS,GAAGR,QAAQ,CAACT,KAAK,CAAC,GAAGiB,SAAS;EAC5E,MAAMC,YAAY,GAAG,SAAAA,CAAA,EAAwB;IAAA,IAAvBC,aAAa,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAH,SAAA,GAAAG,SAAA,MAAG,CAAC,CAAC;IACtC,OAAOjC,QAAQ,CAAC;MACd,iBAAiB,EAAE6B,kBAAkB,IAAI,IAAI,GAAGA,kBAAkB,GAAGC,SAAS;MAC9EF,MAAM;MACNb,EAAE,EAAEA,EAAE,IAAI,IAAI,GAAGA,EAAE,GAAGe;IACxB,CAAC,EAAEE,aAAa,EAAE;MAChBT,GAAG,EAAEE;IACP,CAAC,CAAC;EACJ,CAAC;EACD,OAAO;IACLG,MAAM;IACNG,YAAY;IACZd,OAAO,EAAEQ;EACX,CAAC;AACH;AACA,SAASd,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}