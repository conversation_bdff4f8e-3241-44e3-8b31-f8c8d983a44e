{"ast": null, "code": "import * as React from 'react';\nconst ListItemButtonOrientationContext = /*#__PURE__*/React.createContext('horizontal');\nif (process.env.NODE_ENV !== 'production') {\n  ListItemButtonOrientationContext.displayName = 'ListItemButtonOrientationContext';\n}\nexport default ListItemButtonOrientationContext;", "map": {"version": 3, "names": ["React", "ListItemButtonOrientationContext", "createContext", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/ListItemButton/ListItemButtonOrientationContext.js"], "sourcesContent": ["import * as React from 'react';\nconst ListItemButtonOrientationContext = /*#__PURE__*/React.createContext('horizontal');\nif (process.env.NODE_ENV !== 'production') {\n  ListItemButtonOrientationContext.displayName = 'ListItemButtonOrientationContext';\n}\nexport default ListItemButtonOrientationContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,gCAAgC,GAAG,aAAaD,KAAK,CAACE,aAAa,CAAC,YAAY,CAAC;AACvF,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,gCAAgC,CAACK,WAAW,GAAG,kCAAkC;AACnF;AACA,eAAeL,gCAAgC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}