{"ast": null, "code": "import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getBadgeUtilityClass(slot) {\n  return generateUtilityClass('MuiBadge', slot);\n}\nconst badgeClasses = generateUtilityClasses('MuiBadge', ['root', 'badge', 'anchorOriginTopRight', 'anchorOriginBottomRight', 'anchorOriginTopLeft', 'anchorOriginBottomLeft', 'colorPrimary', 'colorDanger', 'colorNeutral', 'colorSuccess', 'colorWarning', 'colorContext', 'invisible', 'locationInside', 'locationOutside', 'sizeSm', 'sizeMd', 'sizeLg', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid']);\nexport default badgeClasses;", "map": {"version": 3, "names": ["generateUtilityClass", "generateUtilityClasses", "getBadgeUtilityClass", "slot", "badgeClasses"], "sources": ["C:/ignition/ignition-ui/node_modules/@mui/joy/Badge/badgeClasses.js"], "sourcesContent": ["import { generateUtilityClass, generateUtilityClasses } from '../className';\nexport function getBadgeUtilityClass(slot) {\n  return generateUtilityClass('MuiBadge', slot);\n}\nconst badgeClasses = generateUtilityClasses('MuiBadge', ['root', 'badge', 'anchorOriginTopRight', 'anchorOriginBottomRight', 'anchorOriginTopLeft', 'anchorOriginBottomLeft', 'colorPrimary', 'colorDanger', 'colorNeutral', 'colorSuccess', 'colorWarning', 'colorContext', 'invisible', 'locationInside', 'locationOutside', 'sizeSm', 'sizeMd', 'sizeLg', 'variantPlain', 'variantOutlined', 'variantSoft', 'variantSolid']);\nexport default badgeClasses;"], "mappings": "AAAA,SAASA,oBAAoB,EAAEC,sBAAsB,QAAQ,cAAc;AAC3E,OAAO,SAASC,oBAAoBA,CAACC,IAAI,EAAE;EACzC,OAAOH,oBAAoB,CAAC,UAAU,EAAEG,IAAI,CAAC;AAC/C;AACA,MAAMC,YAAY,GAAGH,sBAAsB,CAAC,UAAU,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,sBAAsB,EAAE,yBAAyB,EAAE,qBAAqB,EAAE,wBAAwB,EAAE,cAAc,EAAE,aAAa,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,WAAW,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,cAAc,EAAE,iBAAiB,EAAE,aAAa,EAAE,cAAc,CAAC,CAAC;AAC/Z,eAAeG,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}